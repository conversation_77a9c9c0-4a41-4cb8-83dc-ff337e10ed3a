{"ExtendedData": {"inputs": ["https://weixin.cqlandmark.com/services/service.svc"], "collectionTypes": ["System.Array", "System.Collections.Generic.Dictionary`2"], "namespaceMappings": ["*, LandmarkServiceReference"], "references": ["Aliyun.OSS.Core, {Aliyun.OSS.SDK.NetCore, 2.13.0}", "aliyun-net-sdk-core, {aliyun-net-sdk-core, 1.5.10}", "aliyun-net-sdk-nlp-automl, {aliyun-net-sdk-nlp-automl, 0.0.9}", "AutoMapper, {AutoMapper, 10.0.0}", "BouncyCastle.Crypto, {Portable.BouncyCastle, 1.8.6}", "Caching.CSRedis, {Caching.CSRedis, 3.6.5}", "Castle.Core, {Castle.Core, 4.4.1}", "Castle.Core.AsyncInterceptor, {Castle.Core.AsyncInterceptor, 1.7.0}", "CSRedisCore, {CSRedisCore, 3.6.5}", "<PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON>, **********}", "EFCore.<PERSON>, {E<PERSON><PERSON><PERSON>, *******}", "EFCore.Sharding.SqlServer, {EFCore.Sharding.SqlServer, *******}", "Elasticsearch.Net, {Elasticsearch.Net, 7.8.1}", "ICSharpCode.SharpZipLib, {SharpZipLib, 1.2.0}", "<PERSON><PERSON><PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON><PERSON><PERSON>, 1.4.1}", "Id<PERSON><PERSON><PERSON>.Zookeeper, {Id<PERSON><PERSON><PERSON>.Zookeeper, 1.5.1}", "LinqKit.Microsoft.EntityFrameworkCore, {LinqKit.Microsoft.EntityFrameworkCore, 3.0.0}", "Microsoft.AspNetCore, {Microsoft.AspNetCore, 2.2.0}", "Microsoft.AspNetCore.Authentication.Abstractions, {Microsoft.AspNetCore.Authentication.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Authentication.Core, {Microsoft.AspNetCore.Authentication.Core, 2.2.0}", "Microsoft.AspNetCore.Authorization, {Microsoft.AspNetCore.Authorization, 2.2.0}", "Microsoft.AspNetCore.Authorization.Policy, {Microsoft.AspNetCore.Authorization.Policy, 2.2.0}", "Microsoft.AspNetCore.Connections.Abstractions, {Microsoft.AspNetCore.Connections.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Diagnostics, {Microsoft.AspNetCore.Diagnostics, 2.2.0}", "Microsoft.AspNetCore.Diagnostics.Abstractions, {Microsoft.AspNetCore.Diagnostics.Abstractions, 2.2.0}", "Microsoft.AspNetCore.HostFiltering, {Microsoft.AspNetCore.HostFiltering, 2.2.0}", "Microsoft.AspNetCore.Hosting, {Microsoft.AspNetCore.Hosting, 2.2.0}", "Microsoft.AspNetCore.Hosting.Abstractions, {Microsoft.AspNetCore.Hosting.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Hosting.Server.Abstractions, {Microsoft.AspNetCore.Hosting.Server.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Http, {Microsoft.AspNetCore.Http, 2.2.0}", "Microsoft.AspNetCore.Http.Abstractions, {Microsoft.AspNetCore.Http.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Http.Extensions, {Microsoft.AspNetCore.Http.Extensions, 2.2.0}", "Microsoft.AspNetCore.Http.Features, {Microsoft.AspNetCore.Http.Features, 2.2.0}", "Microsoft.AspNetCore.HttpOverrides, {Microsoft.AspNetCore.HttpOverrides, 2.2.0}", "Microsoft.AspNetCore.Mvc.Abstractions, {Microsoft.AspNetCore.Mvc.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Mvc.Core, {Microsoft.AspNetCore.Mvc.Core, 2.2.5}", "Microsoft.AspNetCore.ResponseCaching.Abstractions, {Microsoft.AspNetCore.ResponseCaching.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Routing, {Microsoft.AspNetCore.Routing, 2.2.0}", "Microsoft.AspNetCore.Routing.Abstractions, {Microsoft.AspNetCore.Routing.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Server.IIS, {Microsoft.AspNetCore.Server.IIS, 2.2.0}", "Microsoft.AspNetCore.Server.IISIntegration, {Microsoft.AspNetCore.Server.IISIntegration, 2.2.0}", "Microsoft.AspNetCore.Server.Kestrel, {Microsoft.AspNetCore.Server.Kestrel, 2.2.0}", "Microsoft.AspNetCore.Server.Kestrel.Core, {Microsoft.AspNetCore.Server.Kestrel.Core, 2.2.0}", "Microsoft.AspNetCore.Server.Kestrel.Https, {Microsoft.AspNetCore.Server.Kestrel.Https, 2.2.0}", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions, {Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets, {Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets, 2.2.0}", "Microsoft.AspNetCore.WebUtilities, {Microsoft.AspNetCore.WebUtilities, 2.2.0}", "Microsoft.Bcl.AsyncInterfaces, {Microsoft.Bcl.AsyncInterfaces, 1.1.1}", "Microsoft.Bcl.HashCode, {Microsoft.Bcl.HashCode, 1.1.0}", "Microsoft.CSharp, {Microsoft.CSharp, 4.7.0}", "Microsoft.Data.SqlClient, {Microsoft.Data.SqlClient, 2.0.0}", "Microsoft.DotNet.PlatformAbstractions, {Microsoft.DotNet.PlatformAbstractions, 2.1.0}", "Microsoft.EntityFrameworkCore, {Microsoft.EntityFrameworkCore, 3.1.8}", "Microsoft.EntityFrameworkCore.Abstractions, {Microsoft.EntityFrameworkCore.Abstractions, 3.1.8}", "Microsoft.EntityFrameworkCore.Relational, {Microsoft.EntityFrameworkCore.Relational, 3.1.8}", "Microsoft.EntityFrameworkCore.SqlServer, {Microsoft.EntityFrameworkCore.SqlServer, 3.1.8}", "Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite, {Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite, 3.1.8}", "Microsoft.Extensions.Caching.Memory, {Microsoft.Extensions.Caching.Memory, 3.1.8}", "Microsoft.Extensions.Configuration, {Microsoft.Extensions.Configuration, 3.1.8}", "Microsoft.Extensions.Configuration.Abstractions, {Microsoft.Extensions.Configuration.Abstractions, 3.1.8}", "Microsoft.Extensions.Configuration.Binder, {Microsoft.Extensions.Configuration.Binder, 3.1.8}", "Microsoft.Extensions.Configuration.CommandLine, {Microsoft.Extensions.Configuration.CommandLine, 2.2.0}", "Microsoft.Extensions.Configuration.EnvironmentVariables, {Microsoft.Extensions.Configuration.EnvironmentVariables, 2.2.0}", "Microsoft.Extensions.Configuration.FileExtensions, {Microsoft.Extensions.Configuration.FileExtensions, 2.2.0}", "Microsoft.Extensions.Configuration.Json, {Microsoft.Extensions.Configuration.Json, 2.2.0}", "Microsoft.Extensions.Configuration.UserSecrets, {Microsoft.Extensions.Configuration.UserSecrets, 2.2.0}", "Microsoft.Extensions.DependencyInjection, {Microsoft.Extensions.DependencyInjection, 3.1.8}", "Microsoft.Extensions.DependencyInjection.Abstractions, {Microsoft.Extensions.DependencyInjection.Abstractions, 3.1.8}", "Microsoft.Extensions.DependencyModel, {Microsoft.Extensions.DependencyModel, 2.1.0}", "Microsoft.Extensions.FileProviders.Abstractions, {Microsoft.Extensions.FileProviders.Abstractions, 3.1.8}", "Microsoft.Extensions.FileProviders.Physical, {Microsoft.Extensions.FileProviders.Physical, 2.2.0}", "Microsoft.Extensions.FileSystemGlobbing, {Microsoft.Extensions.FileSystemGlobbing, 2.2.0}", "Microsoft.Extensions.Hosting.Abstractions, {Microsoft.Extensions.Hosting.Abstractions, 3.1.8}", "Microsoft.Extensions.Http, {Microsoft.Extensions.Http, 3.1.8}", "Microsoft.Extensions.Logging, {Microsoft.Extensions.Logging, 3.1.8}", "Microsoft.Extensions.Logging.Abstractions, {Microsoft.Extensions.Logging.Abstractions, 3.1.8}", "Microsoft.Extensions.Logging.Configuration, {Microsoft.Extensions.Logging.Configuration, 2.2.0}", "Microsoft.Extensions.Logging.Console, {Microsoft.Extensions.Logging.Console, 2.2.0}", "Microsoft.Extensions.Logging.Debug, {Microsoft.Extensions.Logging.Debug, 2.2.0}", "Microsoft.Extensions.Logging.EventSource, {Microsoft.Extensions.Logging.EventSource, 2.2.0}", "Microsoft.Extensions.ObjectPool, {Microsoft.Extensions.ObjectPool, 2.2.0}", "Microsoft.Extensions.Options, {Microsoft.Extensions.Options, 3.1.8}", "Microsoft.Extensions.Options.ConfigurationExtensions, {Microsoft.Extensions.Options.ConfigurationExtensions, 2.2.0}", "Microsoft.Extensions.Primitives, {Microsoft.Extensions.Primitives, 3.1.8}", "Microsoft.IdentityModel.JsonWebTokens, {Microsoft.IdentityModel.JsonWebTokens, 5.6.0}", "Microsoft.IdentityModel.Logging, {Microsoft.IdentityModel.Logging, 5.6.0}", "Microsoft.IdentityModel.Protocols, {Microsoft.IdentityModel.Protocols, 5.6.0}", "Microsoft.IdentityModel.Protocols.OpenIdConnect, {Microsoft.IdentityModel.Protocols.OpenIdConnect, 5.6.0}", "Microsoft.IdentityModel.Tokens, {Microsoft.IdentityModel.Tokens, 5.6.0}", "Microsoft.Net.Http.Headers, {Microsoft.Net.Http.Headers, 2.2.0}", "MySqlConnector, {MySqlConnector, 0.69.9}", "NetTopologySuite, {NetTopologySuite, 2.0.0}", "NetTopologySuite.IO.SqlServerBytes, {NetTopologySuite.IO.SqlServerBytes, 2.0.0}", "Newtonsoft.Json, {Newtonsoft<PERSON>J<PERSON>, 12.0.3}", "NodaTime, {NodaTime, 3.0.0}", "Npgsql, {Npgsql, 4.1.4}", "NPOI, {NPOI, 2.5.1}", "NPOI.OOXML, {NPOI, 2.5.1}", "NPOI.OpenXml4Net, {NPOI, 2.5.1}", "NPOI.OpenXmlFormats, {NPOI, 2.5.1}", "Oracle.ManagedDataAccess, {Oracle.ManagedDataAccess.Core, 2.19.90}", "<PERSON><PERSON><PERSON><PERSON>, {Q<PERSON><PERSON><PERSON>, 1.3.9}", "<PERSON><PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON><PERSON>, 3.1.0}", "<PERSON><PERSON><PERSON>, {Serilog, 2.9.0}", "Serilog.AspNetCore, {Serilog.AspNetCore, 3.4.0}", "Serilog.Extensions.Hosting, {Serilog.Extensions.Hosting, 3.1.0}", "Serilog.Extensions.Logging, {Serilog.Extensions.Logging, 3.0.1}", "Serilog.Formatting.Compact, {Serilog.Formatting.Compact, 1.1.0}", "Serilog.Formatting.Elasticsearch, {Serilog.Formatting.Elasticsearch, 8.4.1}", "Serilog.Settings.Configuration, {Serilog.Settings.Configuration, 3.1.0}", "Serilog.<PERSON><PERSON><PERSON>, {Serilog.<PERSON>ks.Console, 3.1.1}", "Serilog.Sinks.Debug, {Serilog.Sinks.Debug, 1.0.1}", "Serilog.Sinks.Elasticsearch, {Serilog.Sinks.Elasticsearch, 8.4.1}", "Serilog.Sinks.File, {Serilog.Sinks.File, 4.1.0}", "Serilog.Sinks.PeriodicBatching, {Serilog.Sinks.PeriodicBatching, 2.1.1}", "System.AppContext, {System.AppContext, 4.1.0}", "<PERSON><PERSON>Buff<PERSON>, {<PERSON><PERSON>Buffers, 4.5.1}", "System.Collections, {System.Collections, 4.3.0}", "System.Collections.Concurrent, {System.Collections.Concurrent, 4.3.0}", "System.Collections.Immutable, {System.Collections.Immutable, 1.7.1}", "System.Collections.NonGeneric, {System.Collections.NonGeneric, 4.3.0}", "System.Collections.Specialized, {System.Collections.Specialized, 4.3.0}", "System.ComponentModel, {System.ComponentModel, 4.3.0}", "System.ComponentModel.Annotations, {System.ComponentModel.Annotations, 4.7.0}", "System.ComponentModel.Primitives, {System.ComponentModel.Primitives, 4.3.0}", "System.ComponentModel.TypeConverter, {System.ComponentModel.TypeConverter, 4.3.0}", "System.Configuration.ConfigurationManager, {System.Configuration.ConfigurationManager, 4.7.0}", "<PERSON><PERSON>Console, {<PERSON><PERSON>Console, 4.3.0}", "System.Data.SqlClient, {System.Data.SqlClient, 4.8.2}", "System.Diagnostics.Debug, {System.Diagnostics.Debug, 4.3.0}", "System.Diagnostics.DiagnosticSource, {System.Diagnostics.DiagnosticSource, 4.7.1}", "System.Diagnostics.TraceSource, {System.Diagnostics.TraceSource, 4.3.0}", "System.Drawing.Common, {System.Drawing.Common, 4.7.0}", "System.Dynamic.Runtime, {System.Dynamic.Runtime, 4.3.0}", "System.Globalization, {System.Globalization, 4.3.0}", "System.IdentityModel.Tokens.Jwt, {System.IdentityModel.Tokens.Jwt, 5.6.0}", "System.IO, {System.IO, 4.3.0}", "System.IO.FileSystem, {System.IO.FileSystem, 4.3.0}", "System.IO.FileSystem.Primitives, {System.IO.FileSystem.Primitives, 4.3.0}", "System.IO.Pipelines, {System.IO.Pipelines, 4.5.2}", "<PERSON><PERSON>, {System.Linq, 4.3.0}", "System.Linq.Dynamic.Core, {System.Linq.Dynamic.Core, 1.2.2}", "System.Linq.Expressions, {System.Linq.Expressions, 4.3.0}", "System.Memory, {System.Memory, 4.5.4}", "System.Numerics.Vectors, {System.Numerics.Vectors, 4.5.0}", "System.ObjectModel, {System.ObjectModel, 4.3.0}", "System.Reflection, {System.Reflection, 4.3.0}", "System.Reflection.DispatchProxy, {System.Reflection.DispatchProxy, 4.5.0}", "System.Reflection.Emit, {System.Reflection.Emit, 4.7.0}", "System.Reflection.Emit.ILGeneration, {System.Reflection.Emit.ILGeneration, 4.7.0}", "System.Reflection.Emit.Lightweight, {System.Reflection.Emit.Lightweight, 4.3.0}", "System.Reflection.Metadata, {System.Reflection.Metadata, 1.6.0}", "System.Reflection.Primitives, {System.Reflection.Primitives, 4.3.0}", "System.Reflection.TypeExtensions, {System.Reflection.TypeExtensions, 4.3.0}", "System.Runtime, {System.Runtime, 4.3.0}", "System.Runtime.CompilerServices.Unsafe, {System.Runtime.CompilerServices.Unsafe, 4.7.1}", "System.Runtime.Extensions, {System.Runtime.Extensions, 4.3.0}", "System.Runtime.Handles, {System.Runtime.Handles, 4.3.0}", "System.Runtime.InteropServices, {System.Runtime.InteropServices, 4.3.0}", "System.Runtime.InteropServices.RuntimeInformation, {System.Runtime.InteropServices.RuntimeInformation, 4.0.0}", "System.Security.AccessControl, {System.Security.AccessControl, 4.7.0}", "System.Security.Cryptography.Cng, {System.Security.Cryptography.Cng, 4.5.0}", "System.Security.Cryptography.Xml, {System.Security.Cryptography.Xml, 4.5.0}", "System.Security.Permissions, {System.Security.Permissions, 4.7.0}", "System.Security.Principal.Windows, {System.Security.Principal.Windows, 4.7.0}", "System.ServiceModel, {System.ServiceModel.Primitives, 4.7.0}", "System.ServiceModel.Duplex, {System.ServiceModel.Duplex, 4.4.4}", "System.ServiceModel.Http, {System.ServiceModel.Http, 4.4.4}", "System.ServiceModel.NetTcp, {System.ServiceModel.NetTcp, 4.4.4}", "System.ServiceModel.Primitives, {System.ServiceModel.Primitives, 4.7.0}", "System.ServiceModel.Security, {System.ServiceModel.Security, 4.4.4}", "System.Text.Encoding, {System.Text.Encoding, 4.3.0}", "System.Text.Encoding.CodePages, {System.Text.Encoding.CodePages, 4.7.1}", "System.Text.Encoding.Extensions, {System.Text.Encoding.Extensions, 4.3.0}", "System.Text.Encodings.Web, {System.Text.Encodings.Web, 4.6.0}", "System.<PERSON>.<PERSON>, {System.Text.<PERSON>, 4.6.0}", "System.Threading.Tasks, {System.Threading.Tasks, 4.3.0}", "System.Threading.Tasks.Extensions, {System.Threading.Tasks.Extensions, 4.5.4}", "System.Threading.Timer, {System.Threading.Timer, 4.0.1}", "System.Xml.ReaderWriter, {System.Xml.ReaderWriter, 4.3.0}", "System.Xml.XmlDocument, {System.Xml.XmlDocument, 4.3.0}", "ZooKeeperNetEx, {ZooKeeperNetEx, 3.4.12.4}"], "targetFramework": "netstandard2.0", "typeReuseMode": "All"}}