﻿<template>
  <a-modal
    :title="title"
    width="40%"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="()=>{this.visible=false}"
  >
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="entity" :rules="rules" v-bind="layout">
        <a-form-model-item label="F_Name" prop="F_Name">
          <a-input v-model="entity.F_Name" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_Describe" prop="F_Describe">
          <a-input v-model="entity.F_Describe" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_Icon" prop="F_Icon">
          <a-input v-model="entity.F_Icon" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_Isable" prop="F_Isable">
          <a-input v-model="entity.F_Isable" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_Leader" prop="F_Leader">
          <a-input v-model="entity.F_Leader" autocomplete="off" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      layout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 18 }
      },
      visible: false,
      loading: false,
      entity: {},
      rules: {},
      title: ''
    }
  },
  methods: {
    init() {
      this.visible = true
      this.entity = {}
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    openForm(id, title) {
      this.init()

      if (id) {
        this.loading = true
        this.$http.post('/Wechat_Go/Go_Team/GetTheData', { id: id }).then(resJson => {
          this.loading = false

          this.entity = resJson.Data
        })
      }
    },
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.loading = true
        this.$http.post('/Wechat_Go/Go_Team/SaveData', this.entity).then(resJson => {
          this.loading = false

          if (resJson.Success) {
            this.$message.success('操作成功!')
            this.visible = false

            this.parentObj.getDataList()
          } else {
            this.$message.error(resJson.Msg)
          }
        })
      })
    }
  }
}
</script>
