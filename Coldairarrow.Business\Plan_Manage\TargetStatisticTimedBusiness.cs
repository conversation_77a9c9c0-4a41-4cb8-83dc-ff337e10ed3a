﻿using Coldairarrow.Entity.Plan_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Util.DataAccess;
using Newtonsoft.Json;
using CSRedis;
using System;
using Coldairarrow.Util.Helper;
using System.Text;

namespace Coldairarrow.Business.Plan_Manage
{
    public class TargetStatisticTimedBusiness : BaseBusiness<TargetStatisticTimed>, ITargetStatisticTimedBusiness, ITransientDependency
    {
        public TargetStatisticTimedBusiness(IERPDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<TargetStatisticTimed>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<TargetStatisticTimed>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<TargetStatisticTimed, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<TargetStatisticTimed> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(TargetStatisticTimed data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(TargetStatisticTimed data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<TargetStatisticTimed>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<TargetStatisticTimed, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<A01_TargetStatisticTimed, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        #endregion

        #region 营销数据
        public async Task<TargetStatisticTimedDTO> GetTargetStatistic(DateTime? date)
        {
            date = date ?? DateTime.Now;
            var dateStr = date.Value.ToString("yyyy-MM-dd");
            TargetStatisticTimedDTO targetStatisticTimedDTO = new TargetStatisticTimedDTO();
            //获取各项目得数据
            targetStatisticTimedDTO.projectDatas = await this.Db.GetListBySqlAsync<TargetStatisticTimed>(@$"SELECT TOP 3 *
  FROM [Mysoft_ERP25].[dbo].[A01_TargetStatisticTimed]
WHERE ID IN (
 SELECT * FROM ( SELECT TOP 1 ID 
  FROM [Mysoft_ERP25].[dbo].[A01_TargetStatisticTimed]
WHERE TeamProjGUID =N'8baf9a96-1647-43cd-9bcd-067448bd10c9' 
  and CONVERT(varchar,[CreateDate],23)='{dateStr}'
ORDER BY ID DESC) t1
  UNION ALL 
 SELECT * FROM  ( SELECT TOP 1 ID 
  FROM [Mysoft_ERP25].[dbo].[A01_TargetStatisticTimed] 
WHERE TeamProjGUID =N'1980EA50-A241-4096-8057-18A97E605DA9' 
  and CONVERT(varchar,[CreateDate],23)='{dateStr}'
ORDER BY ID DESC) t2
    UNION ALL
  SELECT * FROM  (  SELECT TOP 1 ID 
  FROM [Mysoft_ERP25].[dbo].[A01_TargetStatisticTimed] 
WHERE TeamProjGUID =N'DACC9453-86DA-4D09-BA92-621B0EC33967'
  and CONVERT(varchar,[CreateDate],23)='{dateStr}'
ORDER BY ID DESC) t3)");
            //获取全项目得数据
            var targets = await this.Db.GetListBySqlAsync<TargetStatisticTimed>(@$"SELECT  max(CreateDate) CreateDate,SUM([今日认购套数]) AS [今日认购套数],
      SUM([本月认购套数]) AS [本月认购套数],
      SUM([本季认购套数]) AS [本季认购套数],
      SUM([本年认购套数]) AS [本年认购套数],
      SUM([今日签约套数]) AS [今日签约套数],
      SUM([本月签约套数]) AS [本月签约套数],
      SUM([本季签约套数]) AS [本季签约套数],
      SUM([本年签约套数]) AS [本年签约套数],
      SUM([认购未签约套数]) AS [认购未签约套数],
      SUM([认购未签约金额]) AS [认购未签约金额],
      SUM([今日认购金额]) AS [今日认购金额],
      SUM([本月认购金额]) AS [本月认购金额],
      SUM([本季认购金额]) AS [本季认购金额],
      SUM([本年认购金额]) AS [本年认购金额],
      SUM([今日签约金额]) AS [今日签约金额],
      SUM([本月签约金额]) AS [本月签约金额],
      SUM([本季签约金额]) AS [本季签约金额],
      SUM([本年签约金额]) AS [本年签约金额],
      SUM([今日回款]) AS [今日回款],
      SUM([本月回款]) AS [本月回款],
      SUM([本年回款]) AS [本年回款],
      SUM([剩余应收款]) AS [剩余应收款],
   
     SUM([本年已竣未售签约金额车位]) AS [本年已竣未售签约金额车位],
      SUM([本年已竣未售签约金额住宅]) AS[本年已竣未售签约金额住宅],
     SUM([本年已竣未售签约金额商业]) AS[本年已竣未售签约金额商业],
      SUM([今日已竣未售签约金额车位]) AS[今日已竣未售签约金额车位],
     SUM([今日已竣未售签约金额住宅]) AS[今日已竣未售签约金额住宅],
      SUM([今日已竣未售签约金额商业]) AS[今日已竣未售签约金额商业],
      SUM([已竣未售目标金额车位]) AS[已竣未售目标金额车位],
      SUM([已竣未售目标金额住宅]) AS[已竣未售目标金额住宅],
     SUM([已竣未售目标金额商业]) AS[已竣未售目标金额商业],
       SUM([年度销售目标]) AS [年度销售目标],
       SUM([年度回款目标]) AS [年度回款目标],
     SUM([计划供货]) AS [计划供货],
       SUM([实际供货]) AS [实际供货],
     SUM([总货值]) AS [总货值],
     SUM([在售货值]) AS [在售货值],
     SUM([总面积]) AS [总面积],
     SUM([在售面积]) AS [在售面积]
  FROM [Mysoft_ERP25].[dbo].[A01_TargetStatisticTimed]
WHERE ID IN (
 SELECT * FROM ( SELECT TOP 1 ID 
  FROM [Mysoft_ERP25].[dbo].[A01_TargetStatisticTimed] 
WHERE TeamProjGUID =N'8baf9a96-1647-43cd-9bcd-067448bd10c9' 
  and CONVERT(varchar,[CreateDate],23)='{dateStr}'
ORDER BY ID DESC) t1
  UNION ALL 
 SELECT * FROM  ( SELECT TOP 1 ID 
  FROM [Mysoft_ERP25].[dbo].[A01_TargetStatisticTimed] 
WHERE TeamProjGUID =N'1980EA50-A241-4096-8057-18A97E605DA9' 
  and CONVERT(varchar,[CreateDate],23)='{dateStr}'
ORDER BY ID DESC) t2
    UNION ALL
  SELECT * FROM  (  SELECT TOP 1 ID 
  FROM [Mysoft_ERP25].[dbo].[A01_TargetStatisticTimed] 
WHERE TeamProjGUID =N'DACC9453-86DA-4D09-BA92-621B0EC33967'
  and CONVERT(varchar,[CreateDate],23)='{dateStr}'
ORDER BY ID DESC) t3)");
            targetStatisticTimedDTO.allProjectData = targets.FirstOrDefault();
            return targetStatisticTimedDTO;
        }
        /// <summary>
        /// 获取今天项目数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<ProjectMarketTodayDTO>> GetProjectMarketToday(DateTime? date)
        {
            date = date ?? DateTime.Now;
            var dateStr = date.Value.ToString("yyyy-MM-dd");
            List<ProjectMarketTodayDTO> projectMarkets = new List<ProjectMarketTodayDTO>();
            var redisStr = await RedisHelper.GetAsync("ERP_PROJECTMARKETTODAY_"+dateStr);
            if (!string.IsNullOrWhiteSpace(redisStr))
                projectMarkets = JsonConvert.DeserializeObject<List<ProjectMarketTodayDTO>>(redisStr);
            else
            {
                projectMarkets = await this.Db.GetListBySqlAsync<ProjectMarketTodayDTO>(@$"SELECT * FROM (  SELECT  REPLACE(REPLACE(REPLACE(skToday.SKProjname,'重庆长嘉汇【长嘉汇地块】-', ''),'重庆公园大道【空港地块】-', ''),'重庆嘉景湾【礼嘉地块】-', '嘉景湾') AS '项目名称',
skToday.OrderCount - isnull(skYesterday.OrderCount,0) AS '今日认购数量',skToday.OrderArea - isnull(skYesterday.OrderArea,0)  AS '今日认购面积', 
skToday.OrderAmount - isnull(skYesterday.OrderAmount,0)  AS '今日认购金额', skToday.ContractCount-isnull(skYesterday.ContractCount,0)  AS '今日签约数量',  
skToday.ContractArea - isnull(skYesterday.ContractArea,0) AS '今日签约面积', skToday.ContractAmount - isnull(skYesterday.ContractAmount,0) AS '今日签约金额',
skToday.ReturnFund - isnull(skYesterday.ReturnFund,0) AS '今日回款金额', skToday.RecordDate AS '记录时间'  
FROM A01_ShowSaleData skToday left JOIN A01_ShowSaleData skYesterday ON skToday.skguid = skYesterday.skguid  
AND  skYesterday.RecordDate =  (SELECT TOP 1 A01_ShowSaleData.RecordDate FROM A01_ShowSaleData 
WHERE CONVERT(VARCHAR(50),A01_ShowSaleData.RecordDate,23) =  CONVERT(VARCHAR(50) ,CAST('{dateStr}' as datetime)-1,23)  
GROUP BY A01_ShowSaleData.RecordDate ORDER BY A01_ShowSaleData.RecordDate desc)
WHERE skToday.RecordDate =  (SELECT TOP 1 A01_ShowSaleData.RecordDate FROM A01_ShowSaleData
WHERE CONVERT(VARCHAR(50),A01_ShowSaleData.RecordDate,23) =  CONVERT(VARCHAR(50),CAST('{dateStr}' as datetime),23) 
GROUP BY A01_ShowSaleData.RecordDate ORDER BY A01_ShowSaleData.RecordDate DESC)  )ab  
WHERE (今日认购数量 <>0 OR 今日认购面积  <>0 OR 今日认购金额  <>0 OR  今日签约数量  <>0 OR  今日签约面积  <>0 OR 今日签约金额 <>0 OR 今日回款金额  <>0 )
");
                //注入缓存
                await RedisHelper.SetAsync("ERP_PROJECTMARKETTODAY_"+ redisStr, JsonConvert.SerializeObject(projectMarkets), TimeSpan.FromMinutes(30));

            }
            return projectMarkets;

        }
        /// <summary>
        /// 获取回款趋势
        /// </summary>
        /// <returns></returns>
        public async Task<List<TargetStatisticTimed>> GetTargetTrend(YunYingInput Input)
        {
            List<TargetStatisticTimed> projectMarkets = new List<TargetStatisticTimed>();
            var queryParam = Input.queryJson.ToJObject();
            //判断时间类型(day,week,month)
            var timeType = queryParam["timeType"]?.ToString();
            if (!timeType.IsNullOrEmpty())
            {
                var redisStr = await RedisHelper.GetAsync("ERP_TargetTrend_" + timeType + "_" + Input.project + DateTime.Now.ToString("yyyy-MM-dd"));
               //redisStr = "";
                if (!string.IsNullOrWhiteSpace(redisStr))
                    projectMarkets = JsonConvert.DeserializeObject<List<TargetStatisticTimed>>(redisStr);
                else
                {
                    var strSql = new StringBuilder();
                    strSql.Append(@$"SELECT  max(CreateDate) CreateDate,SUM([今日认购套数]) AS [今日认购套数],
      SUM([本月认购套数]) AS [本月认购套数],
      SUM([本季认购套数]) AS [本季认购套数],
      SUM([本年认购套数]) AS [本年认购套数],
      SUM([今日签约套数]) AS [今日签约套数],
      SUM([本月签约套数]) AS [本月签约套数],
      SUM([本季签约套数]) AS [本季签约套数],
      SUM([本年签约套数]) AS [本年签约套数],
      SUM([认购未签约套数]) AS [认购未签约套数],
      SUM([认购未签约金额]) AS [认购未签约金额],
      SUM([今日认购金额]) AS [今日认购金额],
      SUM([本月认购金额]) AS [本月认购金额],
      SUM([本季认购金额]) AS [本季认购金额],
      SUM([本年认购金额]) AS [本年认购金额],
      SUM([今日签约金额]) AS [今日签约金额],
      SUM([本月签约金额]) AS [本月签约金额],
      SUM([本季签约金额]) AS [本季签约金额],
      SUM([本年签约金额]) AS [本年签约金额],
      SUM([今日回款]) AS [今日回款],
      SUM([本月回款]) AS [本月回款],
      SUM([本年回款]) AS [本年回款],
      SUM([剩余应收款]) AS [剩余应收款],
     SUM([本年已竣未售签约金额车位]) AS [本年已竣未售签约金额车位],
      SUM([本年已竣未售签约金额住宅]) AS[本年已竣未售签约金额住宅],
     SUM([本年已竣未售签约金额商业]) AS[本年已竣未售签约金额商业],
      SUM([今日已竣未售签约金额车位]) AS[今日已竣未售签约金额车位],
     SUM([今日已竣未售签约金额住宅]) AS[今日已竣未售签约金额住宅],
      SUM([今日已竣未售签约金额商业]) AS[今日已竣未售签约金额商业],
      SUM([已竣未售目标金额车位]) AS[已竣未售目标金额车位],
      SUM([已竣未售目标金额住宅]) AS[已竣未售目标金额住宅],
     SUM([已竣未售目标金额商业]) AS[已竣未售目标金额商业],
       SUM([年度销售目标]) AS [年度销售目标],
       SUM([年度回款目标]) AS [年度回款目标],
     SUM([计划供货]) AS [计划供货],
       SUM([实际供货]) AS [实际供货],
     SUM([总货值]) AS [总货值],
     SUM([在售货值]) AS [在售货值],
     SUM([总面积]) AS [总面积],
     SUM([在售面积]) AS [在售面积]
  FROM [Mysoft_ERP25].[dbo].[A01_TargetStatisticTimed]
");
                    strSql.Append(" WHERE ID IN( ");
                    strSql.Append(@" SELECT * FROM(SELECT TOP 1 ID
                     FROM[Mysoft_ERP25].[dbo].[A01_TargetStatisticTimed] WHERE CONVERT(VARCHAR(10), CreateDate, 23) = @time and TeamProjGUID = N'8baf9a96-1647-43cd-9bcd-067448bd10c9' ");
                    if (!Input.project.IsNullOrEmpty())
                    {
                        strSql.Append(" and [TeamProject]='"+Input.project+"' ");
                    }
                    strSql.Append(" ORDER BY ID DESC) t1  ");
                    strSql.Append(" UNION ALL  ");
                    strSql.Append(@" SELECT * FROM(SELECT TOP 1 ID
                     FROM[Mysoft_ERP25].[dbo].[A01_TargetStatisticTimed] WHERE CONVERT(VARCHAR(10), CreateDate, 23) = @time and TeamProjGUID = N'1980EA50-A241-4096-8057-18A97E605DA9' ");
                    if (!Input.project.IsNullOrEmpty())
                    {
                        strSql.Append(" and [TeamProject]='" + Input.project + "' ");
                    }
                    strSql.Append(" ORDER BY ID DESC) t2  ");
                    strSql.Append(" UNION ALL  ");
                    strSql.Append(@" SELECT * FROM(SELECT TOP 1 ID
                     FROM[Mysoft_ERP25].[dbo].[A01_TargetStatisticTimed] WHERE CONVERT(VARCHAR(10), CreateDate, 23) = @time and TeamProjGUID = N'DACC9453-86DA-4D09-BA92-621B0EC33967' ");
                    if (!Input.project.IsNullOrEmpty())
                    {
                        strSql.Append(" and [TeamProject]='" + Input.project + "' ");
                    }
                    strSql.Append(" ORDER BY ID DESC) t3)  ");
                    var threndTime = GetTrendTime(timeType);
                    threndTime.ForEach((threndtime) =>
                   {
                       //获取全项目得数据
                       var targets = this.Db.GetListBySql<TargetStatisticTimed>(strSql.ToString(), ("time", threndtime.time));
                       TargetStatisticTimed model = targets?.FirstOrDefault();
                       //周末求和
                       if (threndtime.times.Count > 0)
                       {
                           var weekTarget = new List<TargetStatisticTimed>();
                           threndtime.times.ForEach((weekData) =>
                           {
                               //获取全项目得数据
                               var targets = this.Db.GetListBySql<TargetStatisticTimed>(strSql.ToString(), ("time", weekData)).FirstOrDefault();
                               weekTarget.Add(targets);
                           });
                           if (weekTarget.Count > 0)
                           {
                               model.本周回款 = weekTarget.Sum(x => x.今日回款);
                               model.本周签约套数 = weekTarget.Sum(x => x.今日签约套数);
                               model.本周签约金额 = weekTarget.Sum(x => x.今日签约金额);
                               model.本周认购套数 = weekTarget.Sum(x => x.今日认购套数);
                               model.本周认购金额 = weekTarget.Sum(x => x.今日认购金额);
                           }
                       }

                       model.timeStr = threndtime.timeStr;
                       projectMarkets.Add(model);
                   });
                }
                //注入缓存
                await RedisHelper.SetAsync("ERP_TargetTrend_" + timeType + "_" + Input.project + DateTime.Now.ToString("yyyy-MM-dd"),
                                            JsonConvert.SerializeObject(projectMarkets), TimeSpan.FromMinutes(30));
            }
            return projectMarkets;
        }
        public List<TrendTime> GetTrendTime(string timeType)
        {
            List<TrendTime> timeStrList = new List<TrendTime>();
            var index = 0;
            var Upper = new List<string> { "一", "二", "三", "四", "五", "六", "七" };
            switch (timeType)
            {
                ///每天(过去15天)
                case "day":
                    for (var day = 0; day > -15; day--)
                    {
                        TrendTime trendTime = new TrendTime()
                        {
                            time = DateTime.Now.AddDays(day).ToString("yyyy-MM-dd"),
                            timeStr = DateTime.Now.AddDays(day).ToString("MM.dd")
                        };
                        timeStrList.Add(trendTime);
                    }
                    break;
                //每周(过去7周的最后一天)
                case "week":
                    for (var day = 0; day >= -6; day--)
                    {
                        var current = index == 0 ? DateTime.Now : TimeStampsHelper.GetWeekLastDaySun(timeStrList[index - 1].time.ToDateTime()).AddDays(-7);
                        TrendTime trendTime = new TrendTime()
                        {
                            time = index == 0 ? DateTime.Now.ToString("yyyy-MM-dd") : TimeStampsHelper.GetWeekLastDaySun(current).ToString("yyyy-MM-dd"),
                            timeStr = index == 0 ? "本周" : index == 1 ? "上周" : $"前{Upper[index]}周"
                        };

                        for (var weekDay = -1; weekDay >= -6; weekDay--)
                        {
                            trendTime.times.Add(trendTime.time.ToDateTime().AddDays(weekDay).ToString("yyyy-MM-dd"));
                        }
                        timeStrList.Add(trendTime);
                        ++index;
                    }
                    break;
                //每月
                case "month":
                    for (var day = 0; day >= -11; day--)
                    {
                        var current = index == 0 ? DateTime.Now : timeStrList[index - 1].time.ToDateTime().AddMonths(-1);
                        TrendTime trendTime = new TrendTime()
                        {
                            time = index == 0 ? DateTime.Now.ToString("yyyy-MM-dd") : current.AddMonths(1).AddDays(-Convert.ToInt32(current.Day)).ToString("yyyy-MM-dd"),
                        };
                        trendTime.timeStr = trendTime.time.ToDateTime().ToString("yyyy.MM");
                        timeStrList.Add(trendTime);
                        ++index;
                    }
                    break;
            }
            return timeStrList.OrderBy(x => x.time).ToList();
        }
        #endregion
    }
    public class TrendTime
    {
        public string time { get; set; }
        public List<string> times { get; set; } = new List<string>();
        public string timeStr { get; set; }
    }
}