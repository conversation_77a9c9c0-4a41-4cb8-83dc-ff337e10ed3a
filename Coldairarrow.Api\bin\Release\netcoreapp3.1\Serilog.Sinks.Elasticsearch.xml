<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Serilog.Sinks.Elasticsearch</name>
    </assembly>
    <members>
        <member name="T:Serilog.LoggerConfigurationElasticsearchExtensions">
            <summary>
            Adds the WriteTo.Elasticsearch() extension method to <see cref="T:Serilog.LoggerConfiguration"/>.
            </summary>
        </member>
        <member name="M:Serilog.LoggerConfigurationElasticsearchExtensions.Elasticsearch(Serilog.Configuration.LoggerSinkConfiguration,Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions)">
            <summary>
            Adds a sink that writes log events as documents to an Elasticsearch index.
            This works great with the Kibana web interface when using the default settings.
            
            By passing in the BufferBaseFilename, you make this into a durable sink. 
            Meaning it will log to disk first and tries to deliver to the Elasticsearch server in the background.
            </summary>
            <remarks>
            Make sure to have a sensible mapping in your Elasticsearch indexes. 
            You can automatically create one by specifying this in the options.
            </remarks>
            <param name="loggerSinkConfiguration">Options for the sink.</param>
            <param name="options">Provides options specific to the Elasticsearch sink</param>
            <returns>LoggerConfiguration object</returns>
        </member>
        <member name="M:Serilog.LoggerConfigurationElasticsearchExtensions.Elasticsearch(Serilog.Configuration.LoggerSinkConfiguration,System.String,System.String,System.String,System.String,System.Int32,System.Int32,System.Boolean,Serilog.Events.LogEventLevel,System.String,System.Nullable{System.Int64},System.Int64,System.String,Serilog.Core.LoggingLevelSwitch)">
            <summary>
            Overload to allow basic configuration through AppSettings.
            </summary>
            <param name="loggerSinkConfiguration">Options for the sink.</param>
            <param name="nodeUris">A comma or semi column separated list of URIs for Elasticsearch nodes.</param>
            <param name="indexFormat"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.IndexFormat"/></param>
            <param name="templateName"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.TemplateName"/></param>
            <param name="typeName"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.TypeName"/></param>
            <param name="batchPostingLimit"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BatchPostingLimit"/></param>
            <param name="period"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.Period"/></param>
            <param name="inlineFields"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.InlineFields"/></param>
            <param name="restrictedToMinimumLevel">The minimum log event level required in order to write an event to the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level to be changed at runtime.</param>
            <param name="bufferBaseFilename"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BufferBaseFilename"/></param>
            <param name="bufferFileSizeLimitBytes"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BufferFileSizeLimitBytes"/></param>
            <param name="bufferLogShippingInterval"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BufferLogShippingInterval"/></param>
            <param name="connectionGlobalHeaders">A comma or semi column separated list of key value pairs of headers to be added to each elastic http request</param>
        </member>
        <member name="M:Serilog.LoggerConfigurationElasticsearchExtensions.Elasticsearch(Serilog.Configuration.LoggerSinkConfiguration,System.String,System.String,System.String,System.String,System.Int32,System.Int32,System.Boolean,Serilog.Events.LogEventLevel,System.String,System.Nullable{System.Int64},System.Int64,System.String,Serilog.Core.LoggingLevelSwitch,System.Int32,Serilog.Sinks.Elasticsearch.EmitEventFailureHandling,System.Int32,System.String,System.Boolean,Serilog.Sinks.Elasticsearch.AutoRegisterTemplateVersion,System.Boolean,Serilog.Sinks.Elasticsearch.RegisterTemplateRecovery,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.IFormatProvider,Elasticsearch.Net.IConnection,Elasticsearch.Net.IElasticsearchSerializer,Elasticsearch.Net.IConnectionPool,Serilog.Formatting.ITextFormatter,Serilog.Formatting.ITextFormatter,Serilog.Core.ILogEventSink,System.Nullable{System.Int64},System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.String},Serilog.Sinks.Elasticsearch.ElasticOpType)">
            <summary>
            Overload to allow basic configuration through AppSettings.
            </summary>
            <param name="loggerSinkConfiguration">Options for the sink.</param>
            <param name="nodeUris">A comma or semi column separated list of URIs for Elasticsearch nodes.</param>
            <param name="indexFormat"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.IndexFormat"/></param>
            <param name="templateName"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.TemplateName"/></param>
            <param name="typeName"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.TypeName"/></param>
            <param name="batchPostingLimit"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BatchPostingLimit"/></param>
            <param name="period"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.Period"/></param>
            <param name="inlineFields"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.InlineFields"/></param>
            <param name="restrictedToMinimumLevel">The minimum log event level required in order to write an event to the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level to be changed at runtime.</param>
            <param name="bufferBaseFilename"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BufferBaseFilename"/></param>
            <param name="bufferFileSizeLimitBytes"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BufferFileSizeLimitBytes"/></param>
            <param name="bufferFileCountLimit"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BufferFileCountLimit"/></param>        
            <param name="bufferLogShippingInterval"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BufferLogShippingInterval"/></param>
            <param name="connectionGlobalHeaders">A comma or semi column separated list of key value pairs of headers to be added to each elastic http request</param>   
            <param name="connectionTimeout"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.ConnectionTimeout"/>The connection timeout (in seconds) when sending bulk operations to elasticsearch (defaults to 5).</param>   
            <param name="emitEventFailure"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.EmitEventFailure"/>Specifies how failing emits should be handled.</param>  
            <param name="queueSizeLimit"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.QueueSizeLimit"/>The maximum number of events that will be held in-memory while waiting to ship them to Elasticsearch. Beyond this limit, events will be dropped. The default is 100,000. Has no effect on durable log shipping.</param>   
            <param name="pipelineName"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.PipelineName"/>Name the Pipeline where log events are sent to sink. Please note that the Pipeline should be existing before the usage starts.</param>   
            <param name="autoRegisterTemplate"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.AutoRegisterTemplate"/>When set to true the sink will register an index template for the logs in elasticsearch.</param>   
            <param name="autoRegisterTemplateVersion"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.AutoRegisterTemplateVersion"/>When using the AutoRegisterTemplate feature, this allows to set the Elasticsearch version. Depending on the version, a template will be selected. Defaults to pre 5.0.</param>  
            <param name="overwriteTemplate"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.OverwriteTemplate"/>When using the AutoRegisterTemplate feature, this allows you to overwrite the template in Elasticsearch if it already exists. Defaults to false</param>   
            <param name="registerTemplateFailure"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.RegisterTemplateFailure"/>Specifies the option on how to handle failures when writing the template to Elasticsearch. This is only applicable when using the AutoRegisterTemplate option.</param>  
            <param name="deadLetterIndexName"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.DeadLetterIndexName"/>Optionally set this value to the name of the index that should be used when the template cannot be written to ES.</param>  
            <param name="numberOfShards"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.NumberOfShards"/>The default number of shards.</param>   
            <param name="numberOfReplicas"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.NumberOfReplicas"/>The default number of replicas.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
            <param name="connection">Allows you to override the connection used to communicate with elasticsearch.</param>
            <param name="serializer">When passing a serializer unknown object will be serialized to object instead of relying on their ToString representation</param>
            <param name="connectionPool">The connectionpool describing the cluster to write event to</param>
            <param name="customFormatter">Customizes the formatter used when converting log events into ElasticSearch documents. Please note that the formatter output must be valid JSON :)</param>
            <param name="customDurableFormatter">Customizes the formatter used when converting log events into the durable sink. Please note that the formatter output must be valid JSON :)</param>
            <param name="failureSink">Sink to use when Elasticsearch is unable to accept the events. This is optionally and depends on the EmitEventFailure setting.</param>   
            <param name="singleEventSizePostingLimit"><see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.SingleEventSizePostingLimit"/>The maximum length of an event allowed to be posted to Elasticsearch.default null</param>
            <param name="templateCustomSettings">Add custom elasticsearch settings to the template</param>
            <param name="batchAction">Configures the OpType being used when inserting document in batch. Must be set to create for data streams.</param>
            <returns>LoggerConfiguration object</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="nodeUris"/> is <see langword="null" />.</exception>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.Durable.APayloadReader`1">
            <summary>
            Abstract payload reader
            Generic version of https://github.com/serilog/serilog-sinks-seq/blob/v4.0.0/src/Serilog.Sinks.Seq/Sinks/Seq/Durable/PayloadReader.cs
            </summary>
            <typeparam name="TPayload"></typeparam>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.APayloadReader`1.GetNoPayload">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.APayloadReader`1.ReadPayload(System.Int32,System.Nullable{System.Int64},Serilog.Sinks.Elasticsearch.Durable.FileSetPosition@,System.Int32@,System.String)">
            <summary>
            
            </summary>
            <param name="batchPostingLimit"></param>
            <param name="eventBodyLimitBytes"></param>
            <param name="position"></param>
            <param name="count"></param>
            <param name="fileName"></param>
            <returns></returns>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.APayloadReader`1.InitPayLoad(System.String)">
            <summary>
            
            </summary>
            <param name="fileName"></param>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.APayloadReader`1.FinishPayLoad">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.APayloadReader`1.AddToPayLoad(System.String)">
            <summary>
            
            </summary>
            <param name="nextLine"></param>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.Durable.BookmarkFile">
            <summary>
            https://github.com/serilog/serilog-sinks-seq/blob/v4.0.0/src/Serilog.Sinks.Seq/Sinks/Seq/Durable/BookmarkFile.cs
            </summary>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.Durable.ControlledLevelSwitch">
            <summary>
            Instances of this type are single-threaded, generally only updated on a background
            timer thread. An exception is <see cref="M:Serilog.Sinks.Elasticsearch.Durable.ControlledLevelSwitch.IsIncluded(Serilog.Events.LogEvent)"/>, which may be called
            concurrently but performs no synchronization.
            https://github.com/serilog/serilog-sinks-seq/blob/v4.0.0/src/Serilog.Sinks.Seq/Sinks/Seq/ControlledLevelSwitch.cs
            </summary>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.Durable.ElasticsearchLogClient">
            <summary>
            
            </summary>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.ElasticsearchLogClient.#ctor(Elasticsearch.Net.IElasticLowLevelClient,System.Func{System.String,System.Nullable{System.Int64},System.String,System.String},Serilog.Sinks.Elasticsearch.ElasticOpType)">
            <summary>
            
            </summary>
            <param name="elasticLowLevelClient"></param>
            <param name="cleanPayload"></param>
            <param name="elasticOpType"></param>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.Durable.ElasticsearchLogShipper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.ElasticsearchLogShipper.#ctor(System.String,System.Int32,System.TimeSpan,System.Nullable{System.Int64},Serilog.Core.LoggingLevelSwitch,Serilog.Sinks.Elasticsearch.Durable.ILogClient{System.Collections.Generic.List{System.String}},Serilog.Sinks.Elasticsearch.Durable.IPayloadReader{System.Collections.Generic.List{System.String}},System.Nullable{System.Int64},System.Nullable{System.Int64},System.Action)">
            <summary>
            
            </summary>
            <param name="bufferBaseFilename"></param>
            <param name="batchPostingLimit"></param>
            <param name="period"></param>
            <param name="eventBodyLimitBytes"></param>
            <param name="levelControlSwitch"></param>
            <param name="logClient"></param>
            <param name="payloadReader"></param>
            <param name="retainedInvalidPayloadsLimitBytes"></param>
            <param name="bufferSizeLimitBytes"></param>
            <param name="registerTemplateIfNeeded"></param>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.ElasticsearchLogShipper.OnTick">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.Durable.ElasticsearchPayloadReader">
            <summary>
            
            </summary>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.ElasticsearchPayloadReader.#ctor(System.String,System.String,System.Func{System.Object,System.String},System.Func{System.String,System.DateTime,System.String},Serilog.Sinks.Elasticsearch.ElasticOpType)">
            <summary>
            
            </summary>
            <param name="pipelineName"></param>
            <param name="typeName"></param>
            <param name="serialize"></param>
            <param name="getIndexForEvent"></param>
            <param name="elasticOpType"></param>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.ElasticsearchPayloadReader.GetNoPayload">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.ElasticsearchPayloadReader.InitPayLoad(System.String)">
            <summary>
            
            </summary>
            <param name="filename"></param>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.ElasticsearchPayloadReader.FinishPayLoad">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.ElasticsearchPayloadReader.AddToPayLoad(System.String)">
            <summary>
            
            </summary>
            <param name="nextLine"></param>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.Durable.ExponentialBackoffConnectionSchedule">
            <summary>
            Based on the BatchedConnectionStatus class from <see cref="T:Serilog.Sinks.PeriodicBatching.PeriodicBatchingSink"/>.
            https://github.com/serilog/serilog-sinks-seq/blob/v4.0.0/src/Serilog.Sinks.Seq/Sinks/Seq/ExponentialBackoffConnectionSchedule.cs
            </summary>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.Durable.FileSet">
            <summary>
            https://github.com/serilog/serilog-sinks-seq/blob/v4.0.0/src/Serilog.Sinks.Seq/Sinks/Seq/Durable/FileSet.cs
            </summary>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.Durable.FileSetPosition">
            <summary>
            https://github.com/serilog/serilog-sinks-seq/blob/v4.0.0/src/Serilog.Sinks.Seq/Sinks/Seq/Durable/FileSetPosition.cs
            </summary>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.Durable.ILogClient`1">
            <summary>
            A wrapper client which talk to the log server
            </summary>
            <typeparam name="TPayload"></typeparam>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.Durable.IPayloadReader`1">
            <summary>
            Reads logs from logfiles and formats it for logserver
            </summary>
            <typeparam name="TPayload"></typeparam>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.Durable.LogShipper`1">
            <summary>
            Reads and sends logdata to log server
            Generic version of  https://github.com/serilog/serilog-sinks-seq/blob/v4.0.0/src/Serilog.Sinks.Seq/Sinks/Seq/Durable/HttpLogShipper.cs
            </summary>
            <typeparam name="TPayload"></typeparam>
        </member>
        <member name="F:Serilog.Sinks.Elasticsearch.Durable.LogShipper`1._connectionSchedule">
            <summary>
            
            </summary>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.LogShipper`1.#ctor(System.String,System.Int32,System.TimeSpan,System.Nullable{System.Int64},Serilog.Core.LoggingLevelSwitch,Serilog.Sinks.Elasticsearch.Durable.ILogClient{`0},Serilog.Sinks.Elasticsearch.Durable.IPayloadReader{`0},System.Nullable{System.Int64},System.Nullable{System.Int64})">
            <summary>
            
            </summary>
            <param name="bufferBaseFilename"></param>
            <param name="batchPostingLimit"></param>
            <param name="period"></param>
            <param name="eventBodyLimitBytes"></param>
            <param name="levelControlSwitch"></param>
            <param name="logClient"></param>
            <param name="payloadReader"></param>
            <param name="retainedInvalidPayloadsLimitBytes"></param>
            <param name="bufferSizeLimitBytes"></param>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.LogShipper`1.IsIncluded(Serilog.Events.LogEvent)">
            <summary>
            
            </summary>
            <param name="logEvent"></param>
            <returns></returns>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.LogShipper`1.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.Durable.LogShipper`1.OnTick">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.Durable.PortableTimer">
            <summary>
            https://github.com/serilog/serilog-sinks-seq/blob/v4.0.0/src/Serilog.Sinks.Seq/Sinks/Seq/PortableTimer.cs
            </summary>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.ElasticsearchSink">
            <summary>
            Writes log events as documents to ElasticSearch.
            </summary>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.ElasticsearchSink.#ctor(Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions)">
            <summary>
            Creates a new ElasticsearchSink instance with the provided options
            </summary>
            <param name="options">Options configuring how the sink behaves, may NOT be null</param>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.ElasticsearchSink.EmitBatchAsync(System.Collections.Generic.IEnumerable{Serilog.Events.LogEvent})">
            <summary>
            Emit a batch of log events, running to completion synchronously.
            </summary>
            <param name="events">The events to emit.</param>
            <remarks>
            Override either <see cref="M:Serilog.Sinks.PeriodicBatching.PeriodicBatchingSink.EmitBatch(System.Collections.Generic.IEnumerable{Serilog.Events.LogEvent})" />
             or <see cref="M:Serilog.Sinks.PeriodicBatching.PeriodicBatchingSink.EmitBatchAsync(System.Collections.Generic.IEnumerable{Serilog.Events.LogEvent})" />,
            not both.
            </remarks>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.ElasticsearchSink.EmitBatchCheckedAsync``1(System.Collections.Generic.IEnumerable{Serilog.Events.LogEvent})">
            <summary>
            Emit a batch of log events, running to completion synchronously.
            </summary>
            <param name="events">The events to emit.</param>
            <returns>Response from Elasticsearch</returns>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.ElasticsearchSink.EmitBatchChecked``1(System.Collections.Generic.IEnumerable{Serilog.Events.LogEvent})">
            <summary>
            Emit a batch of log events, running to completion synchronously.
            </summary>
            <param name="events">The events to emit.</param>
            <returns>Response from Elasticsearch</returns>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.ElasticsearchSink.HandleException(System.Exception,System.Collections.Generic.IEnumerable{Serilog.Events.LogEvent})">
            <summary>
            Handles the exceptions.
            </summary>
            <param name="ex"></param>
            <param name="events"></param>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions">
            <summary>
            Provides ElasticsearchSink with configurable options
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.AutoRegisterTemplate">
            <summary>
            When set to true the sink will register an index template for the logs in elasticsearch.
            This template is optimized to deal with serilog events
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.AutoRegisterTemplateVersion">
            <summary>
            When using the <see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.AutoRegisterTemplate"/> feature, this allows to set the Elasticsearch version. Depending on the
            version, a template will be selected. Defaults to pre 5.0.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.RegisterTemplateFailure">
            <summary>
            Specifies the option on how to handle failures when writing the template to Elasticsearch. This is only applicable when using the AutoRegisterTemplate option.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.TemplateName">
            <summary>
             When using the <see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.AutoRegisterTemplate"/> feature this allows you to override the default template name.
             Defaults to: serilog-events-template
             </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.GetTemplateContent">
            <summary>
            When using the <see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.AutoRegisterTemplate"/> feature, this allows you to override the default template content.
            If not provided, a default template that is optimized to deal with Serilog events is used.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.TemplateCustomSettings">
            <summary>
            When using the <see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.AutoRegisterTemplate"/> feature, this allows you to override the default template content.
            If not provided, a default template that is optimized to deal with Serilog events is used.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.OverwriteTemplate">
            <summary>
            When using the <see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.AutoRegisterTemplate"/> feature, this allows you to overwrite the template in Elasticsearch if it already exists.
            Defaults to: false
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.NumberOfShards">
            <summary>
            When using the <see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.AutoRegisterTemplate"/> feature, this allows you to override the default number of shards.
            If not provided, this will default to the default number_of_shards configured in Elasticsearch.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.NumberOfReplicas">
            <summary>
            When using the <see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.AutoRegisterTemplate"/> feature, this allows you to override the default number of replicas.
            If not provided, this will default to the default number_of_replicas configured in Elasticsearch.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.IndexAliases">
            <summary>
            Index aliases. Sets alias/aliases to an index in elasticsearch.
            Tested and works with ElasticSearch 7.x
            When using the <see cref="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.AutoRegisterTemplate"/> feature, this allows you to set index aliases.
            If not provided, index aliases will be blank.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.ModifyConnectionSettings">
            <summary>
             Connection configuration to use for connecting to the cluster.
             </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.IndexFormat">
            <summary>
             The index name formatter. A string.Format using the DateTimeOffset of the event is run over this string.
             defaults to "logstash-{0:yyyy.MM.dd}"
             Needs to be lowercased.
             </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.DeadLetterIndexName">
            <summary>
            Optionally set this value to the name of the index that should be used when the template cannot be written to ES.
            defaults to "deadletter-{0:yyyy.MM.dd}"
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.TypeName">
            <summary>
             The default elasticsearch type name to use for the log events. Defaults to: logevent.
             </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BatchAction">
            <summary>
            Configures the <see cref="T:Elasticsearch.Net.OpType"/> being used when bulk indexing documents.
            In order to use data streams, this needs to be set to OpType.Create. 
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.PipelineNameDecider">
            <summary>
            Function to decide which Pipeline to use for the LogEvent
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.PipelineName">
            <summary>
            Name the Pipeline where log events are sent to sink. Please note that the Pipeline should be existing before the usage starts.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BatchPostingLimit">
            <summary>
             The maximum number of events to post in a single batch. Defaults to: 50.
             </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.SingleEventSizePostingLimit">
            <summary>
             The maximum length of a an event record to be sent. Defaults to: null (No Limit) only used in file buffer mode
             </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.Period">
            <summary>
             The time to wait between checking for event batches. Defaults to 2 seconds.
             </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.FormatProvider">
            <summary>
             Supplies culture-specific formatting information, or null.
             </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.Connection">
            <summary>
             Allows you to override the connection used to communicate with elasticsearch.
             </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.ConnectionTimeout">
            <summary>
            The connection timeout (in milliseconds) when sending bulk operations to elasticsearch (defaults to 5000).
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.InlineFields">
            <summary>
            When true fields will be written at the root of the json document.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.MinimumLogEventLevel">
            <summary>
            The minimum log event level required in order to write an event to the sink. Ignored when LoggingLevelSwitch is specified.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.LevelSwitch">
            <summary>
            A switch allowing the pass-through minimum level to be changed at runtime.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.Serializer">
            <summary>
             When passing a serializer unknown object will be serialized to object instead of relying on their ToString representation
             </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.ConnectionPool">
            <summary>
            The connection pool describing the cluster to write event to
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.IndexDecider">
            <summary>
            Function to decide which index to write the LogEvent to, when using file see: BufferIndexDecider
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BufferIndexDecider">
            <summary>
            Function to decide which index to write the LogEvent to when using file buffer
            Arguments is: logRow, DateTime of logfile
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BufferBaseFilename">
            <summary>
            Optional path to directory that can be used as a log shipping buffer for increasing the reliability of the log forwarding.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BufferFileSizeLimitBytes">
            <summary>
            The maximum size, in bytes, to which the buffer log file for a specific date will be allowed to grow. By default 100L * 1024 * 1024 will be applied.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BufferLogShippingInterval">
            <summary>
            The interval between checking the buffer files.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BufferCleanPayload">
            <summary>
            An action to do when log row was denied by the elasticsearch because of the data (payload).
            The arguments is: The log row, status code from server, error message
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BufferRetainedInvalidPayloadsLimitBytes">
            <summary>
            A soft limit for the number of bytes to use for storing failed requests.  
            The limit is soft in that it can be exceeded by any single error payload, but in that case only that single error
            payload will be retained.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.CustomFormatter">
            <summary>
            Customizes the formatter used when converting log events into ElasticSearch documents. Please note that the formatter output must be valid JSON :)
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.CustomDurableFormatter">
            <summary>
            Customizes the formatter used when converting log events into the durable sink. Please note that the formatter output must be valid JSON :)
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.EmitEventFailure">
            <summary>
            Specifies how failing emits should be handled.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.FailureSink">
            <summary>
            Sink to use when Elasticsearch is unable to accept the events. This is optional and depends on the EmitEventFailure setting.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.FailureCallback">
            <summary>
            A callback which can be used to handle logevents which are not submitted to Elasticsearch
            like when it is unable to accept the events. This is optional and depends on the EmitEventFailure setting.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.QueueSizeLimit">
            <summary>
            The maximum number of events that will be held in-memory while waiting to ship them to
            Elasticsearch. Beyond this limit, events will be dropped. The default is 100,000. Has no effect on
            durable log shipping.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.BufferFileCountLimit">
            <summary>
            The maximum number of log files that will be retained,
            including the current log file. For unlimited retention, pass null. The default is 31.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.FormatStackTraceAsArray">
            <summary>
            When set to true splits the StackTrace by new line and writes it as a an array of strings.
            </summary>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.#ctor">
            <summary>
            Configures the elasticsearch sink defaults
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.DefaultTypeName">
            <summary>
            The default Elasticsearch type name used for Elasticsearch versions prior to 7.
            <para>As of <c>Elasticsearch 7</c> and up <c>_type</c> has been removed.</para>
            </summary>
        </member>
        <member name="P:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.DetectElasticsearchVersion">
             <summary>
             Instructs the sink to auto detect the running Elasticsearch version.
            
             <para>
             This information is used to attempt to register an older or newer template
             </para>
             <para></para>
            
             <para>
             Currently supports:
             </para>
             <para></para>
             
             <para>
             Currently supports:
             - using <see cref="F:Serilog.Sinks.Elasticsearch.AutoRegisterTemplateVersion.ESv7"/> against <c> Elasticsearch 6.x </c>
             - using <see cref="F:Serilog.Sinks.Elasticsearch.AutoRegisterTemplateVersion.ESv6"/> against <c> Elasticsearch 7.x </c>
             </para>
             </summary>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.#ctor(Elasticsearch.Net.IConnectionPool)">
            <summary>
            Configures the elasticsearch sink
            </summary>
            <param name="connectionPool">The connectionpool to use to write events to</param>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.#ctor(System.Collections.Generic.IEnumerable{System.Uri})">
            <summary>
            Configures the elasticsearch sink
            </summary>
            <param name="nodes">The nodes to write to</param>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions.#ctor(System.Uri)">
            <summary>
            Configures the elasticsearch sink
            </summary>
            <param name="node">The node to write to</param>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.EmitEventFailureHandling">
            <summary>
            Sepecifies options for handling failures when emitting the events to Elasticsearch. Can be a combination of options.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.Elasticsearch.EmitEventFailureHandling.WriteToSelfLog">
            <summary>
            Send the error to the SelfLog
            </summary>
        </member>
        <member name="F:Serilog.Sinks.Elasticsearch.EmitEventFailureHandling.WriteToFailureSink">
            <summary>
            Write the events to another sink. Make sure to configure this one.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.Elasticsearch.EmitEventFailureHandling.ThrowException">
            <summary>
            Throw the exception to the caller.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.Elasticsearch.EmitEventFailureHandling.RaiseCallback">
            <summary>
            The failure callback function will be called when the event cannot be submitted to Elasticsearch.
            </summary>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.RegisterTemplateRecovery">
            <summary>
            Specifies what to do when the template could not be created. This can mean that your data is not correctly indexed, so you might want to handle this failure.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.Elasticsearch.RegisterTemplateRecovery.IndexAnyway">
            <summary>
            Ignore the issue and keep indexing. This is the default option.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.Elasticsearch.RegisterTemplateRecovery.IndexToDeadletterIndex">
            <summary>
            When the template cannot be registered, move the events to the deadletter index instead.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.Elasticsearch.RegisterTemplateRecovery.FailSink">
            <summary>
            When the template cannot be registered, throw an exception and fail the sink.
            </summary>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.ElasticOpType">
             <summary>
             Collection of op_type:s that can be used when indexing documents
             ‹https://www.elastic.co/guide/en/elasticsearch/reference/current/docs-index_.html
            
             This is the same as the internal <seealso cref="T:Elasticsearch.Net.OpType"/> but we don't want to
             expose it in the API.
             </summary>
        </member>
        <member name="F:Serilog.Sinks.Elasticsearch.ElasticOpType.Index">
            <summary>
            Default option, creates or updates a document.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.Elasticsearch.ElasticOpType.Create">
            <summary>
            Set to create to only index the document if it does not already exist (put if absent).
            - If a document with the specified _id already exists, the indexing operation will fail.
            - If the request targets a data stream, an op_type of create is required
            </summary>
        </member>
        <member name="M:Serilog.Sinks.Elasticsearch.ElasticsearchSinkState.RegisterTemplateIfNeeded">
            <summary>
            Register the elasticsearch index template if the provided options mandate it.
            </summary>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.AutoRegisterTemplateVersion">
             <summary>
            
             </summary>
        </member>
        <member name="F:Serilog.Sinks.Elasticsearch.AutoRegisterTemplateVersion.ESv2">
            <summary>
            Elasticsearch version &lt;= 2.4
            </summary>
        </member>
        <member name="F:Serilog.Sinks.Elasticsearch.AutoRegisterTemplateVersion.ESv5">
            <summary>
            Elasticsearch version &lt;= version 5.6
            </summary>
        </member>
        <member name="F:Serilog.Sinks.Elasticsearch.AutoRegisterTemplateVersion.ESv6">
            <summary>
            Elasticsearch version &gt;= version 6.0
            </summary>
        </member>
        <member name="F:Serilog.Sinks.Elasticsearch.AutoRegisterTemplateVersion.ESv7">
            <summary>
            Elasticsearch version &gt;= version 7.0
            </summary>
        </member>
        <member name="T:Serilog.Sinks.Elasticsearch.ElasticsearchTemplateProvider">
             <summary>
            
             </summary>
        </member>
    </members>
</doc>
