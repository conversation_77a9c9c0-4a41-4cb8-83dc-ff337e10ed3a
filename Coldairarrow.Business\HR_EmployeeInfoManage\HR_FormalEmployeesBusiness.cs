﻿using AutoMapper;
using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HR_DataDictionaryManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage.Extensions;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NPOI.POIFS.FileSystem;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HR_EmployeeInfoManage
{
    public class HR_FormalEmployeesBusiness : BaseBusiness<HR_FormalEmployees>
                                            , IHR_FormalEmployeesBusiness
                                            , ITransientDependency
    {
        readonly IMapper _mapper;
        private IBase_PostBusiness _base_PostBusiness;
        private IBase_UserBusiness _base_UserBusiness;
        private IBase_CompanyBusiness _base_CompanyBus;
        IOperator _operator;
        IBase_DepartmentBusiness _base_DepartmentBusiness;
        public HR_FormalEmployeesBusiness(IDbAccessor db, IMapper mapper, IBase_PostBusiness base_PostBusiness,
                                        IBase_UserBusiness base_UserBusiness, IOperator @operator, IBase_DepartmentBusiness base_DepartmentBusiness, IBase_CompanyBusiness base_CompanyBus)
            : base(db)
        {
            _mapper = mapper;
            _base_PostBusiness = base_PostBusiness;
            _base_UserBusiness = base_UserBusiness;
            _operator = @operator;
            _base_DepartmentBusiness = base_DepartmentBusiness;
            _base_CompanyBus = base_CompanyBus;
        }
        #region 外部接口
        public IQueryable<HR_FormalEmployeesDTO> GetHR_PersonnelChangeDetails(PageInput<ConditionDTO> input)
        {
            //Expression<Func<HR_FormalEmployees, Base_Department, Base_Post, Base_Company, HR_Departure, HR_FormalEmployeesDTO>> select = (e, d, p, c, f) => new HR_FormalEmployeesDTO
            //{
            //    DepartmentName = d.Name,
            //    PostName = p.F_Name,
            //    CompanyName = c.F_FullName,
            //    F_TrueDepartureDate = f.F_TrueDepartureDate

            //};
            //select = select.BuildExtendSelectExpre();
            //var q = from e in this.Db.GetIQueryable<HR_FormalEmployees>().Where(i => i.F_BusState == (int)ASKBusState.正常).AsExpandable()
            //        join d in this.Db.GetIQueryable<Base_Department>() on e.F_DepartmentId equals d.Id into dept
            //        from d in dept.DefaultIfEmpty()
            //        join p in this.Db.GetIQueryable<Base_Post>() on e.F_PositionId equals p.F_Id into post
            //        from p in post.DefaultIfEmpty()
            //        join c in this.Db.GetIQueryable<Base_Company>() on p.F_CompanyId equals c.F_Id into com
            //        from c in com.DefaultIfEmpty()
            //        join f in this.Db.GetIQueryable<HR_Departure>() on e.F_Id equals f.F_UserId into dep
            //        from depa in dep.DefaultIfEmpty()
            //        select @select.Invoke(e, d, p, c, depa);

            var sqlStr = @"SELECT
                    f.F_Id,
	                f.EmployeesCode,
	                f.NameUser,
                    f.ND,
                    f.F_PositionId,
	                f.IdCardNumber,
	                f.DirthDate,f.Sex,
	                f.EmployRelStatus,
	                dep.Name as DepartmentName,
	                po.F_Name as PostName,
	                co.F_FullName as CompanyName,
	               case when f.EmployRelStatus like '%离职%' or f.EmployRelStatus like '%终止%' then  d.F_TrueDepartureDate else null end  as F_TrueDepartureDate,
                    f.F_CreateDate,
                    f.F_InTime F_InTime
                FROM
                    HR_FormalEmployees f
                LEFT JOIN HR_Departure d ON d.F_TrueDepartureDate = (SELECT MAX(F_TrueDepartureDate) FROM HR_Departure WHERE F_Id = d.F_Id ) 
	            AND f.F_Id = d.F_UserId 
                LEFT JOIN Base_Department dep on f.F_DepartmentId = dep.Id 
                LEFT JOIN Base_Post po on f.F_PositionId = po.F_Id 
                LEFT JOIN Base_Company co on f.F_CompanyId = co.F_Id 
                where 1=1 
               ";
            if (input.PostId?.Count > 0)
            {
                var depList = string.Join("','", input.PostId.ToArray());
                sqlStr += " and f.F_DepartmentId in ('" + depList + "')";
            }
            if (input.IsInterview?.Count > 0)
            {
                var IntList = string.Join("','", input.IsInterview.ToArray());
                sqlStr += " and f.F_CompanyId in ('" + IntList + "')";
            }
            if (input.RoundInterview?.Count > 0)
            {
                var rouList = string.Join("','", input.RoundInterview.ToArray());
                sqlStr += " and f.EmployRelStatus in ('" + rouList + "')";
            }
            var q = this.Db.GetListBySql<HR_FormalEmployeesDTO>(sqlStr).AsQueryable();
            var where = LinqHelper.True<HR_FormalEmployeesDTO>();
            var search = input.Search;
            if (!search.EmployRelStatus.IsNullOrEmpty() && search.EmployRelStatus != "所有员工")
            {
                q = q.Where(i => search.EmployRelStatus != null && i.EmployRelStatus.Contains(search.EmployRelStatus));
            }
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_FormalEmployeesDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return q.Where(where);
        }
        public async Task<PageResult<HR_FormalEmployeesDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var hR_FormalEmployees = GetHR_PersonnelChangeDetails(input);
            var count = hR_FormalEmployees.Count();
            var list = hR_FormalEmployees
           .OrderBy($@"{input.SortField} {input.SortType}")
           .Skip((input.PageIndex - 1) * input.PageRows)
           .Take(input.PageRows)
           .ToList();
            if (list.Count > 0)
            {
                list.ForEach(item =>
                {
                    item.OrgInfo = _base_PostBusiness.GetOrgName(item.F_PositionId);
                });
            }
            return new PageResult<HR_FormalEmployeesDTO> { Data = list, Total = count };
            //return await GetHR_PersonnelChangeDetails(input).GetPageResultAsync(input);
        }
        /// <summary>
        /// 获取我的团队数据
        /// </summary>
        /// <param name="input"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<PageResult<HR_FormalEmployeesDTO>> GetMyTeamsList(PageInput<ConditionDTO> input, string userId)
        {
            if (userId.IsNullOrEmpty())
            {
                throw new BusException("参数不能为空");
            }
            //得到我的部门下人员ID
            var deptIds = from e in Db.GetIQueryable<HR_FormalEmployees>()
                          where e.BaseUserId == userId
                          select e.F_DepartmentId;
            input.PostId = deptIds.ToList();
            //判断是否有岗位,如果有则查询,否则返回
            if (input.PostId != null && input.PostId.Count > 0)
            {
                return await GetDataListAsync(input);
            }
            else
            {
                return new PageResult<HR_FormalEmployeesDTO> { Data = new List<HR_FormalEmployeesDTO>(), Total = 0, IsSuccuess = 0 };
            }

        }
        /// <summary>
        /// 根据员工Id获取的员工和部门职位的左连接
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<IEnumerable<HR_FormalEmployeesDTO>> GetConDataListAsync(string id)
        {
            var FormEmployess = !string.IsNullOrEmpty(id) ?
                                this.Db.GetIQueryable<HR_FormalEmployees>().Where(i => i.F_Id == id && i.F_BusState == (int)ASKBusState.正常)
                                : this.Db.GetIQueryable<HR_FormalEmployees>();
            var dd = from e in FormEmployess
                     join d in this.Db.GetIQueryable<Base_Department>() on e.F_DepartmentId equals d.Id into dept
                     from d in dept.DefaultIfEmpty()
                     join p in this.Db.GetIQueryable<Base_Post>().DefaultIfEmpty() on e.F_PositionId equals p.F_Id into post
                     from p in post.DefaultIfEmpty()
                     select new HR_FormalEmployeesDTO
                     {
                         DepartmentName = d.Name,
                         PostName = p.F_Name,
                         EmployeesCode = e.EmployeesCode,
                         NameUser = e.NameUser,
                         F_DepartmentId = e.F_DepartmentId,
                         F_PositionId = e.F_PositionId,
                         EmployRelStatus = e.EmployRelStatus,
                         IdCardNumber = e.IdCardNumberStr,
                         Sex = e.Sex,
                         MobilePhone = e.MobilePhoneStr
                     };
            return dd.ToList();
        }
        public async Task<HR_FormalEmployees> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        public HR_FormalEmployees GetTheData(string id)
        {
            return this.Db.GetEntity<HR_FormalEmployees>(id);
        }
        /// <summary>
        /// 通过用户id去获取用户信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public HR_FormalEmployees GetTheDataByUserId(string userId)
        {
            return this.Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(i => i.BaseUserId == userId);
        }
        /// <summary>
        /// 根据员工的id获取对应的流程项目部门
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ProjectDept GetProjectDept(string id)
        {
            ProjectDept projectDept = new ProjectDept();
            if (!id.IsNullOrEmpty())
            {
                var formalEmployees = this.GetEntity(id);
                if (formalEmployees != null)
                {
                    projectDept.project = !formalEmployees.ND.IsNullOrEmpty() ?
                  string.Join(",", this.Db.GetIQueryable<HR_DataDictionaryDetails>()
                    .Where(i => formalEmployees.ND.Split(',').Contains(i.F_ItemValue)).Where(i => !string.IsNullOrWhiteSpace(i.F_OAValue)).Select(x => x.F_OAValue.Trim())) : "";
                    projectDept.company = !formalEmployees.F_CompanyId.IsNullOrEmpty() ? _base_CompanyBus.GetTheData(formalEmployees.F_CompanyId)?.F_FullName : "";
                    if (!string.IsNullOrWhiteSpace(projectDept.company) && projectDept.company.Trim() == "重庆怡置商业管理有限公司" || projectDept.company.Trim() == "重庆外商服务有限公司-商业")
                    {
                        projectDept.dept = !formalEmployees.F_DepartmentId.IsNullOrEmpty() ? _base_DepartmentBusiness.GetTheData(formalEmployees.F_DepartmentId)?.Name : "";
                    }
                    else
                    {
                        projectDept.dept = !formalEmployees.F_DepartmentId.IsNullOrEmpty() ? _base_DepartmentBusiness.GetRootDept(formalEmployees.F_DepartmentId)?.Name : "";
                    }
                }
            }
            return projectDept;
        }
        /// <summary>
        /// 根据用户名称获取员工信息
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public HR_FormalEmployeesDTO GetByName(string name)
        {
            HR_FormalEmployeesDTO ret = null;
            var emp = this.Db.GetIQueryable<HR_FormalEmployees>().Where(i => i.F_BusState == (int)ASKBusState.正常).FirstOrDefault(x => x.NameUser == name);
            if (emp != null)
            {
                var json = JsonConvert.SerializeObject(emp);
                ret = JsonConvert.DeserializeObject<HR_FormalEmployeesDTO>(json);
                var pos = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == emp.F_PositionId);
                ret.PostName = pos?.F_Name;
                var dept = Db.GetIQueryable<Base_Department>().FirstOrDefault(x => x.Id == emp.F_DepartmentId);
                ret.DepartmentName = dept?.Name;
            }

            return ret;
        }
        /// <summary>
        /// 获取表单数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<HR_FormalEmployeesTheDTO> GetFormDataAsync(string id)
        {
            var Hr_Form = await GetEntityAsync(id);
            var from = JsonConvert.SerializeObject(Hr_Form);
            var hR_FormalEmployeesTheDTO = JsonConvert.DeserializeObject<HR_FormalEmployeesTheDTO>(from);
            if (hR_FormalEmployeesTheDTO != null && !string.IsNullOrWhiteSpace(hR_FormalEmployeesTheDTO.F_CompanyId))
            {
                var companyEntity = this.Db.GetEntity<Base_Company>(hR_FormalEmployeesTheDTO.F_CompanyId);
                if (companyEntity != null && !string.IsNullOrWhiteSpace(companyEntity.F_FullName))
                {
                    hR_FormalEmployeesTheDTO.CompanyName = companyEntity.F_FullName;
                }
            }
            if (hR_FormalEmployeesTheDTO != null && !string.IsNullOrWhiteSpace(hR_FormalEmployeesTheDTO.F_Id))
            {
                //过滤sql注入
                hR_FormalEmployeesTheDTO.F_PositionId.FilterSql();
                var positive = this.Db.GetListBySql<Base_Post>("SELECT * FROM Base_Post t WHERE t.F_Id= '" + hR_FormalEmployeesTheDTO.F_PositionId + "' ").FirstOrDefault();
                if (positive != null)
                {
                    var postDTO = await _base_PostBusiness.GetThenIdAsync(positive.F_Id);
                    hR_FormalEmployeesTheDTO.PositionName = positive?.F_Name;
                    hR_FormalEmployeesTheDTO.PositionOrg = postDTO?.CompanyName + " | " +
                                                           postDTO?.DepartmentName + " | " +
                                                           postDTO?.F_Name;
                    hR_FormalEmployeesTheDTO.F_DepartmentId = postDTO?.F_DepartmentId;
                    //hR_FormalEmployeesTheDTO.Rank = positive.F_PositiveRand;
                }
                //过滤sql注入
                hR_FormalEmployeesTheDTO.F_Id.FilterSql();
                var induction = this.Db.GetListBySql<HR_Induction>("SELECT * FROM HR_Induction t WHERE t.F_UserId= '" + hR_FormalEmployeesTheDTO.F_Id + "' ").FirstOrDefault();
                hR_FormalEmployeesTheDTO.F_InductionDate = induction?.F_InductionDate;
                hR_FormalEmployeesTheDTO.F_ProbationPeriod = induction?.F_ProbationPeriod;
                hR_FormalEmployeesTheDTO.F_InductionSource = induction?.F_InductionSource;
                var positive1 = this.Db.GetListBySql<HR_Positive>("SELECT * FROM HR_Positive t WHERE t.F_UserId= '" + hR_FormalEmployeesTheDTO.F_Id + "' ").FirstOrDefault();
                hR_FormalEmployeesTheDTO.F_PositiveDate = positive1?.F_PositiveDate;
                var departure = this.Db.GetListBySql<HR_Departure>("SELECT * FROM HR_Departure t WHERE t.F_UserId= '" + hR_FormalEmployeesTheDTO.F_Id + "' ").FirstOrDefault();
                hR_FormalEmployeesTheDTO.F_DepartureReason = departure?.F_DepartureReason;
                hR_FormalEmployeesTheDTO.F_TrueDepartureDate = departure?.F_TrueDepartureDate;
                hR_FormalEmployeesTheDTO.hR_SocialWorkExps = this.Db.GetListBySql<HR_SocialWorkExp>("SELECT * FROM HR_SocialWorkExp t WHERE t.F_UserId= '" + hR_FormalEmployeesTheDTO.F_Id + "' ").ToList();
                //hR_FormalEmployeesTheDTO.HeadPortraitText = this.Db.GetListBySql<HR_PersonPhoto>("SELECT * FROM HR_PersonPhoto t WHERE t.F_UserId= '" + hR_FormalEmployeesTheDTO.F_Id + "' ").FirstOrDefault().f_;
            }
            if (hR_FormalEmployeesTheDTO != null && !string.IsNullOrWhiteSpace(hR_FormalEmployeesTheDTO.NameUser))
            {
                //过滤sql注入
                hR_FormalEmployeesTheDTO.NameUser.FilterSql();
                var user = this.Db.GetListBySql<Base_User>("SELECT * FROM Base_User t WHERE t.RealName= '" + hR_FormalEmployeesTheDTO.NameUser + "' ").FirstOrDefault();
                if (user != null)
                {
                    hR_FormalEmployeesTheDTO.BaseUserId = user.Id;
                }
            }
            if (hR_FormalEmployeesTheDTO != null)
            {
                hR_FormalEmployeesTheDTO = await DecryptFormalByDto(hR_FormalEmployeesTheDTO);
            }
            return hR_FormalEmployeesTheDTO;
        }
        /// <summary>
        /// 解密数据
        /// </summary>
        /// <param name="hR_Formal"></param>
        /// <returns></returns>
        public async Task<HR_FormalEmployeesTheDTO> DecryptFormalByDto(HR_FormalEmployeesTheDTO hR_Formal)
        {
            hR_Formal.MobilePhone = await AESHelper.DecryptStringAsync(hR_Formal.MobilePhone, AESHelper.AesKey);
            hR_Formal.IdCardAddress = await AESHelper.DecryptStringAsync(hR_Formal.IdCardAddress, AESHelper.AesKey);
            hR_Formal.IdCardNumber = await AESHelper.DecryptStringAsync(hR_Formal.IdCardNumber, AESHelper.AesKey);
            hR_Formal.RegisteredResidence = await AESHelper.DecryptStringAsync(hR_Formal.RegisteredResidence, AESHelper.AesKey);
            hR_Formal.LicenseInfo = await AESHelper.DecryptStringAsync(hR_Formal.LicenseInfo, AESHelper.AesKey);
            hR_Formal.CurrentBankCard = await AESHelper.DecryptStringAsync(hR_Formal.CurrentBankCard, AESHelper.AesKey);
            hR_Formal.OldBankCard = await AESHelper.DecryptStringAsync(hR_Formal.OldBankCard, AESHelper.AesKey);
            hR_Formal.HomeAddress = await AESHelper.DecryptStringAsync(hR_Formal.HomeAddress, AESHelper.AesKey);
            hR_Formal.EmergencyContact = await AESHelper.DecryptStringAsync(hR_Formal.EmergencyContact, AESHelper.AesKey);
            hR_Formal.EmergencyContactNumber = await AESHelper.DecryptStringAsync(hR_Formal.EmergencyContactNumber, AESHelper.AesKey);
            return hR_Formal;
        }
        /// <summary>
        /// 加密数据
        /// </summary>
        /// <param name="hR_Formal"></param>
        /// <returns></returns>
        public HR_FormalEmployeesTheDTO EncryptFormByDto(HR_FormalEmployeesTheDTO hR_Formal)
        {
            hR_Formal.MobilePhone = AESHelper.EncryptString(hR_Formal.MobilePhone, AESHelper.AesKey);
            hR_Formal.IdCardAddress = AESHelper.EncryptString(hR_Formal.IdCardAddress, AESHelper.AesKey);
            hR_Formal.IdCardNumber = AESHelper.EncryptString(hR_Formal.IdCardNumber, AESHelper.AesKey);
            hR_Formal.RegisteredResidence = AESHelper.EncryptString(hR_Formal.RegisteredResidence, AESHelper.AesKey);
            hR_Formal.LicenseInfo = AESHelper.EncryptString(hR_Formal.LicenseInfo, AESHelper.AesKey);
            hR_Formal.CurrentBankCard = AESHelper.EncryptString(hR_Formal.CurrentBankCard, AESHelper.AesKey);
            hR_Formal.OldBankCard = AESHelper.EncryptString(hR_Formal.OldBankCard, AESHelper.AesKey);
            hR_Formal.HomeAddress = AESHelper.EncryptString(hR_Formal.HomeAddress, AESHelper.AesKey);
            hR_Formal.EmergencyContact = AESHelper.EncryptString(hR_Formal.EmergencyContact, AESHelper.AesKey);
            hR_Formal.EmergencyContactNumber = AESHelper.EncryptString(hR_Formal.EmergencyContactNumber, AESHelper.AesKey);
            return hR_Formal;
        }
        /// <summary>
        /// 解密数据
        /// </summary>
        /// <param name="hR_Formal"></param>
        /// <returns></returns>
        public async Task<HR_FormalEmployees> DecryptFormalByDto(HR_FormalEmployees hR_Formal)
        {
            hR_Formal.MobilePhone = await AESHelper.DecryptStringAsync(hR_Formal.MobilePhone, AESHelper.AesKey);
            hR_Formal.IdCardAddress = await AESHelper.DecryptStringAsync(hR_Formal.IdCardAddress, AESHelper.AesKey);
            hR_Formal.IdCardNumber = await AESHelper.DecryptStringAsync(hR_Formal.IdCardNumber, AESHelper.AesKey);
            hR_Formal.RegisteredResidence = await AESHelper.DecryptStringAsync(hR_Formal.RegisteredResidence, AESHelper.AesKey);
            hR_Formal.LicenseInfo = await AESHelper.DecryptStringAsync(hR_Formal.LicenseInfo, AESHelper.AesKey);
            hR_Formal.CurrentBankCard = await AESHelper.DecryptStringAsync(hR_Formal.CurrentBankCard, AESHelper.AesKey);
            hR_Formal.OldBankCard = await AESHelper.DecryptStringAsync(hR_Formal.OldBankCard, AESHelper.AesKey);
            hR_Formal.HomeAddress = await AESHelper.DecryptStringAsync(hR_Formal.HomeAddress, AESHelper.AesKey);
            hR_Formal.EmergencyContact = await AESHelper.DecryptStringAsync(hR_Formal.EmergencyContact, AESHelper.AesKey);
            hR_Formal.EmergencyContactNumber = await AESHelper.DecryptStringAsync(hR_Formal.EmergencyContactNumber, AESHelper.AesKey);
            return hR_Formal;
        }
        /// <summary>
        /// 加密数据
        /// </summary>
        /// <param name="hR_Formal"></param>
        /// <returns></returns>
        public HR_FormalEmployees EncryptFormByDto(HR_FormalEmployees hR_Formal)
        {
            hR_Formal.MobilePhone = AESHelper.EncryptString(hR_Formal.MobilePhone, AESHelper.AesKey);
            hR_Formal.IdCardAddress = AESHelper.EncryptString(hR_Formal.IdCardAddress, AESHelper.AesKey);
            hR_Formal.IdCardNumber = AESHelper.EncryptString(hR_Formal.IdCardNumber, AESHelper.AesKey);
            hR_Formal.RegisteredResidence = AESHelper.EncryptString(hR_Formal.RegisteredResidence, AESHelper.AesKey);
            hR_Formal.LicenseInfo = AESHelper.EncryptString(hR_Formal.LicenseInfo, AESHelper.AesKey);
            hR_Formal.CurrentBankCard = AESHelper.EncryptString(hR_Formal.CurrentBankCard, AESHelper.AesKey);
            hR_Formal.OldBankCard = AESHelper.EncryptString(hR_Formal.OldBankCard, AESHelper.AesKey);
            hR_Formal.HomeAddress = AESHelper.EncryptString(hR_Formal.HomeAddress, AESHelper.AesKey);
            hR_Formal.EmergencyContact = AESHelper.EncryptString(hR_Formal.EmergencyContact, AESHelper.AesKey);
            hR_Formal.EmergencyContactNumber = AESHelper.EncryptString(hR_Formal.EmergencyContactNumber, AESHelper.AesKey);
            return hR_Formal;
        }
        /// <summary>
        /// 获取详情数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<HR_FormalEmployeesTheDTO> GetInfoDataAsync(string id)
        {
            var Hr_Form = await GetEntityAsync(id);
            var json = JsonConvert.SerializeObject(Hr_Form);
            var hR_FormalEmployeesTheDTO = JsonConvert.DeserializeObject<HR_FormalEmployeesTheDTO>(json);
            if (hR_FormalEmployeesTheDTO != null && !string.IsNullOrWhiteSpace(hR_FormalEmployeesTheDTO.F_CompanyId))
            {
                var companyEntity = this.Db.GetEntity<Base_Company>(hR_FormalEmployeesTheDTO.F_CompanyId);
                if (companyEntity != null && !string.IsNullOrWhiteSpace(companyEntity.F_FullName))
                {
                    hR_FormalEmployeesTheDTO.CompanyName = companyEntity.F_FullName;
                }
            }
            //过滤sql注入
            hR_FormalEmployeesTheDTO.F_PositionId.FilterSql();
            hR_FormalEmployeesTheDTO.F_Id.FilterSql();
            if (!string.IsNullOrWhiteSpace(hR_FormalEmployeesTheDTO.F_Id))
            {
                var positive = this.Db.GetListBySql<Base_Post>("SELECT * FROM Base_Post t WHERE t.F_Id= '" + hR_FormalEmployeesTheDTO.F_PositionId + "' ").FirstOrDefault();
                if (positive != null)
                {
                    var postDTO = await _base_PostBusiness.GetThenIdAsync(positive.F_Id);
                    hR_FormalEmployeesTheDTO.PositionName = positive?.F_Name;
                    hR_FormalEmployeesTheDTO.PositionOrg = postDTO?.CompanyName + " | " +
                                                           postDTO?.DepartmentName + " | " +
                                                           postDTO?.F_Name;
                    hR_FormalEmployeesTheDTO.F_DepartmentId = postDTO?.F_DepartmentId;
                    //hR_FormalEmployeesTheDTO.Rank = positive.F_PositiveRand;
                }
                var induction = this.Db.GetListBySql<HR_Induction>("SELECT * FROM HR_Induction t WHERE t.F_UserId= '" + hR_FormalEmployeesTheDTO.F_Id + "' ").FirstOrDefault();
                hR_FormalEmployeesTheDTO.F_InductionDate = induction?.F_InductionDate;
                hR_FormalEmployeesTheDTO.F_ProbationPeriod = induction?.F_ProbationPeriod;
                hR_FormalEmployeesTheDTO.F_InductionSource = induction?.F_InductionSource;
                var positive1 = this.Db.GetListBySql<HR_Positive>("SELECT * FROM HR_Positive t WHERE t.F_UserId= '" + hR_FormalEmployeesTheDTO.F_Id + "' ").FirstOrDefault();
                hR_FormalEmployeesTheDTO.F_PositiveDate = positive1?.F_PositiveDate;
                var departure = this.Db.GetListBySql<HR_Departure>("SELECT * FROM HR_Departure t WHERE t.F_UserId= '" + hR_FormalEmployeesTheDTO.F_Id + "' ").FirstOrDefault();
                hR_FormalEmployeesTheDTO.F_DepartureReason = departure?.F_DepartureReason;
                hR_FormalEmployeesTheDTO.F_TrueDepartureDate = departure?.F_TrueDepartureDate;
                //var = this.Db.GetListBySql<HR_LaborContractInfo>("SELECT * FROM HR_LaborContractInfo t WHERE t.UserId= '" + hR_FormalEmployeesTheDTO.F_Id + "' ").OrderByDescending(i=>).FirstOrDefault();
            }
            if (!string.IsNullOrWhiteSpace(hR_FormalEmployeesTheDTO.NameUser))
            {
                var user = this.Db.GetListBySql<Base_User>("SELECT * FROM Base_User t WHERE t.RealName= '" + hR_FormalEmployeesTheDTO.NameUser + "' ").FirstOrDefault();
                if (user != null)
                {
                    hR_FormalEmployeesTheDTO.BaseUserId = user.Id;
                }
            }
            return hR_FormalEmployeesTheDTO;
        }
        /// <summary>
        /// 取最大的整数编码
        /// </summary>
        /// <returns></returns>
        public string GetMaxCode()
        {
            var emids = this.GetIQueryable().Select(i => i.EmployeesCode).ToList();
            var max = 1;
            if (emids.Count > 0)
            {
                emids.ForEach((item) =>
                {
                    string pattern = @"^\d*$";
                    if (Regex.IsMatch(item, pattern))
                    {
                        max = Math.Max(max, Convert.ToInt32(item));
                    };
                });
            }
            ++max;
            return max.ToString();
        }
        /// <summary>
        /// 根据用户ID查询员工信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public HR_FormalEmployeesDTO GetDataByUserId(string userId)
        {
            HR_FormalEmployeesDTO model = null;
            var entity = this.GetIQueryable().FirstOrDefault(x => x.BaseUserId == userId);
            if (entity != null)
            {
                var json = JsonConvert.SerializeObject(entity);
                model = JsonConvert.DeserializeObject<HR_FormalEmployeesDTO>(json);
                model.DecryptFormal();
                var company = Db.GetIQueryable<Base_Company>().FirstOrDefault(x => x.F_Id == entity.F_CompanyId);
                var dept = Db.GetIQueryable<Base_Department>().FirstOrDefault(x => x.Id == entity.F_DepartmentId);
                var post = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == entity.F_PositionId);
                var induction = Db.GetIQueryable<HR_Induction>().FirstOrDefault(x => x.F_UserId == entity.F_Id);
                List<int> state = new List<int>() {
                (int)WFStates.草稿,
                 (int)WFStates.取消流程,
                 //(int)WFStates.退回,
                };
                var positive = Db.GetIQueryable<HR_Positive>().FirstOrDefault(x => x.F_UserId == entity.F_Id);
                model.CompanyName = company?.F_FullName;
                model.DepartmentName = dept?.Name;
                model.PostName = post?.F_Name;
                model.F_InductionDate = induction?.F_InductionDate;
                model.F_InductionEmployRelStatus = induction?.F_InductionEmployRelStatus;
                model.F_ProbationPeriod = induction?.F_ProbationPeriod;
                model.positiveId = positive?.F_Id;
                model.F_WFState = positive?.F_WFState;
                model.OrgInfo = _base_PostBusiness.GetOrgName(entity.F_PositionId);
                model.IsPosive = positive != null ? (positive.F_WFState.IsNullOrEmpty() || state.Contains((Int32)positive.F_WFState)) ? false : true : false;
                var sqlstr = @"select ro.RoleName as RoleId from [dbo].[Base_UserRole] ur
                                left join Base_Role ro on ur.RoleId = ro.Id
                                where ur.UserId = '" + userId + "'";
                var RoleEntity = Db.GetListBySql<Base_UserRole>(sqlstr).ToList();
                if (RoleEntity.Count > 0)
                {
                    model.RoleStr = String.Join(",", RoleEntity.Select(i => i.RoleId).ToList());
                }
            }
            return model;
        }
        public HR_FormalEmployeesDTO GetDataByFId(string userId)
        {
            HR_FormalEmployeesDTO model = null;
            var entity = this.GetIQueryable().FirstOrDefault(x => x.F_Id == userId);
            if (entity != null)
            {
                var json = JsonConvert.SerializeObject(entity);
                model = JsonConvert.DeserializeObject<HR_FormalEmployeesDTO>(json);
                var company = Db.GetIQueryable<Base_Company>().FirstOrDefault(x => x.F_Id == entity.F_CompanyId);
                var dept = Db.GetIQueryable<Base_Department>().FirstOrDefault(x => x.Id == entity.F_DepartmentId);
                var post = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == entity.F_PositionId);
                var induction = Db.GetIQueryable<HR_Induction>().FirstOrDefault(x => x.F_UserId == entity.F_Id);
                List<int> state = new List<int>() {
                (int)WFStates.草稿,
                 (int)WFStates.取消流程,
                 //(int)WFStates.退回,
                };
                var positive = Db.GetIQueryable<HR_Positive>().FirstOrDefault(x => x.F_UserId == entity.F_Id);
                model.CompanyName = company?.F_FullName;
                model.DepartmentName = dept?.Name;
                model.PostName = post?.F_Name;
                model.F_InductionDate = induction?.F_InductionDate;
                model.F_InductionEmployRelStatus = induction?.F_InductionEmployRelStatus;
                model.F_ProbationPeriod = induction?.F_ProbationPeriod;
                model.positiveId = positive?.F_Id;
                model.F_WFState = positive?.F_WFState;
                model.OrgInfo = _base_PostBusiness.GetOrgName(entity.F_PositionId);
                model.IsPosive = positive != null ? (positive.F_WFState.IsNullOrEmpty() || state.Contains((Int32)positive.F_WFState)) ? false : true : false;
                var sqlstr = @"select ro.RoleName as RoleId from [dbo].[Base_UserRole] ur
                                left join Base_Role ro on ur.RoleId = ro.Id
                                where ur.UserId = '" + userId + "'";
                var RoleEntity = Db.GetListBySql<Base_UserRole>(sqlstr).ToList();
                if (RoleEntity.Count > 0)
                {
                    model.RoleStr = String.Join(",", RoleEntity.Select(i => i.RoleId).ToList());
                }
            }
            return model;
        }
        /// <summary>
        /// 通过手机号
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public HR_FormalEmployeesDTO GetDataByMobile(string mobile)
        {
            HR_FormalEmployeesDTO model = null;
            var entity = this.GetIQueryable().ToList().Where(x => x.MobilePhoneStr == mobile).FirstOrDefault();
            if (entity != null)
            {
                var json = JsonConvert.SerializeObject(entity);
                model = JsonConvert.DeserializeObject<HR_FormalEmployeesDTO>(json);
                var company = Db.GetIQueryable<Base_Company>().FirstOrDefault(x => x.F_Id == entity.F_CompanyId);
                var dept = Db.GetIQueryable<Base_Department>().FirstOrDefault(x => x.Id == entity.F_DepartmentId);
                var post = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == entity.F_PositionId);
                var induction = Db.GetIQueryable<HR_Induction>().FirstOrDefault(x => x.F_UserId == entity.F_Id);
                List<int> state = new List<int>() {
                (int)WFStates.草稿,
                 (int)WFStates.取消流程,
                 //(int)WFStates.退回,
                };
                var positive = Db.GetIQueryable<HR_Positive>().FirstOrDefault(x => x.F_UserId == entity.F_Id);
                model.CompanyName = company?.F_FullName;
                model.DepartmentName = dept?.Name;
                model.PostName = post?.F_Name;
                model.F_InductionDate = induction?.F_InductionDate;
                model.F_InductionEmployRelStatus = induction?.F_InductionEmployRelStatus;
                model.F_ProbationPeriod = induction?.F_ProbationPeriod;
                model.positiveId = positive?.F_Id;
                model.F_WFState = positive?.F_WFState;
                model.OrgInfo = _base_PostBusiness.GetOrgName(entity.F_PositionId);
                model.IsPosive = positive != null ? (positive.F_WFState.IsNullOrEmpty() || state.Contains((Int32)positive.F_WFState)) ? false : true : false;
                var sqlstr = @"select ro.RoleName as RoleId from [dbo].[Base_UserRole] ur
                                left join Base_Role ro on ur.RoleId = ro.Id
                                where ur.UserId = '" + entity.BaseUserId + "'";
                var RoleEntity = Db.GetListBySql<Base_UserRole>(sqlstr).ToList();
                if (RoleEntity.Count > 0)
                {
                    model.RoleStr = String.Join(",", RoleEntity.Select(i => i.RoleId).ToList());
                }
            }
            return model;
        }
        /// <summary>
        /// 数据保存
        /// </summary>
        /// <param name="data"></param>
        //[Transactional]
        public async void SaveData(HR_FormalEmployeesTheDTO data)
        {
            if (!string.IsNullOrWhiteSpace(data.BaseUserId))
            {
                var base_User = _base_UserBusiness.GetTheData(data.BaseUserId);
                if (base_User != null)
                {
                    //if(!string.IsNullOrWhiteSpace(base_User.IsBinding)&&)
                    //改变User表的状态
                    base_User.IsBinding = 1;
                    _base_UserBusiness.UpdateData(base_User);
                }
            }
            //if (!data.HeadPortrait.IsNullOrEmpty())
            //{
            //    //截取
            //    var index = data.HeadPortrait.IndexOf("/Upload");
            //    var headpo = data.HeadPortrait.Substring(index);
            //    data.HeadPortrait = headpo;
            //}
            //加密数据
            data = EncryptFormByDto(data);
            if (data.F_Id.IsNullOrEmpty())
            {

                data.EmployRelStatus = "正式员工";
                data.F_InTime = DateTime.Now;
                InitEntity(data, _operator);
                data.F_BusState = (int)ASKBusState.正常;
                AddData(data);
                //新增员工转正表
                HR_Positive hR_Positive = new HR_Positive()
                {
                    F_UserId = data.F_Id,
                    F_PositiveDate = DateTime.Now,
                    F_ProbationPeriod = 0,
                    F_PositivePosition = data.PositionName,
                    F_PositiveOrg = data.PositionOrg,
                    F_PositiveRand = data.F_Rank,
                    F_InspectionResults = 0,
                    F_WorkConclusion = "",
                    F_ChangesOperating = "转正",
                    F_ChangesType = "转正",
                    F_BusState = (int)ASKBusState.正常,
                    F_WFState = (int)WFStates.草稿,
                };
                InitEntity(hR_Positive, _operator);
                this.Db.Insert(hR_Positive);
                //_hR_PositiveBus.AddData(hR_Positive);
                //新增员工转正表
                HR_Induction hR_Induction = new HR_Induction()
                {
                    F_UserId = data.F_Id,
                    F_InductionDate = DateTime.Now,
                    F_ProbationPeriod = 0,
                    F_InductionPosition = data.PositionName,
                    F_InductionOrg = data.PositionOrg,
                    F_InductionRank = data.F_Rank,
                    F_ChangesOperating = "",
                    F_ChangesType = "雇佣入职",
                    F_ChangesReason = "派驻",
                    F_ChangesDate = DateTime.Now,
                    F_InductionEmployRelStatus = "正式员工",
                    F_BusState = (int)ASKBusState.正常,
                    F_WFState = (int)WFStates.草稿,
                };
                InitEntity(hR_Induction, _operator);
                this.Db.Insert(hR_Induction);
                //_hR_InductionBus.AddData(hR_Induction);

            }
            else
            {
                UpdateEntity(data, _operator);
                UpdateData(data);
                if (!string.IsNullOrWhiteSpace(data.F_Id))
                {
                    //修改转正和入职的职称与职级
                    var positive = this.Db.GetIQueryable<HR_Positive>().Where(i => i.F_UserId == data.F_Id).FirstOrDefault();
                    if (positive != null)
                    {
                        positive.F_PositivePosition = data.PositionName;
                        positive.F_PositiveOrg = data.PositionOrg;
                        positive.F_PositiveRand = data.F_Rank;
                        UpdateEntity(positive, _operator);
                        this.Db.Update(positive);
                    }
                    var induction = this.Db.GetIQueryable<HR_Induction>().Where(i => i.F_UserId == data.F_Id).FirstOrDefault();
                    if (induction != null)
                    {
                        induction.F_InductionPosition = data.PositionName;
                        induction.F_InductionOrg = data.PositionOrg;
                        induction.F_InductionRank = data.F_Rank;
                        UpdateEntity(induction, _operator);
                        this.Db.Update(induction);
                    }
                }
            }
            ///保存头像
            if (!data.HeardId.IsNullOrEmpty())
            {
                //先查询该用户有没有相关头像
                var oldpersonPhoto = this.Db.GetIQueryable<HR_PersonPhoto>().FirstOrDefault(i => i.F_UserId == data.F_Id);
                var newpersonPhoto = this.Db.GetIQueryable<HR_PersonPhoto>().FirstOrDefault(i => i.FID == data.HeardId);
                if (oldpersonPhoto != null)
                {
                    oldpersonPhoto.FImageData = newpersonPhoto.FImageData;
                    oldpersonPhoto.FImageDataSource = newpersonPhoto.FImageDataSource;
                    oldpersonPhoto.FimageContentType = newpersonPhoto.FimageContentType;
                    this.Db.Update(oldpersonPhoto);
                }
                else
                {
                    newpersonPhoto.F_UserId = data.F_Id;
                    this.Db.Update(newpersonPhoto);
                }
            }
        }
        public async Task<PageResult<HR_FormalEmployeesDetailsDTO>> GetDetailsFormDataAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_FormalEmployees, Base_Department, Base_Post, Base_Company, HR_Induction, HR_Positive, HR_FormalEmployeesDetailsDTO>> select = (e, d, p, c, h, j) => new HR_FormalEmployeesDetailsDTO
            {
                DepartmentName = d.Name,
                PostName = p.F_Name,
                CompanyName = c.F_Id,
                F_InductionOrg = h.F_InductionOrg,
                F_PositionId = e.F_PositionId,
                F_InductionPosition = h.F_InductionPosition,
                F_InductionRank = h.F_InductionRank,
                PositiWFState = j.F_WFState,
                F_InductionDate = h.F_InductionDate,
                F_InductionEmployRelStatus = h.F_InductionEmployRelStatus
            };
            select = select.BuildExtendSelectExpre();
            var q = from e in this.Db.GetIQueryable<HR_FormalEmployees>().AsExpandable()
                    join h in this.Db.GetIQueryable<HR_Induction>() on e.F_Id equals h.F_UserId into indu
                    from h in indu.DefaultIfEmpty()
                    join d in this.Db.GetIQueryable<Base_Department>() on e.F_DepartmentId equals d.Id into dept
                    from d in dept.DefaultIfEmpty()
                    join p in this.Db.GetIQueryable<Base_Post>() on e.F_PositionId equals p.F_Id into post
                    from p in post.DefaultIfEmpty()
                    join c in this.Db.GetIQueryable<Base_Company>() on p.F_CompanyId equals c.F_Id into com
                    from c in com.DefaultIfEmpty()
                    join po in this.Db.GetIQueryable<HR_Positive>() on e.F_Id equals po.F_UserId into pos
                    from j in pos.DefaultIfEmpty()
                    select @select.Invoke(e, d, p, c, h, j);
            var where = LinqHelper.True<HR_FormalEmployeesDetailsDTO>();
            var search = input.Search;
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_FormalEmployeesDetailsDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            List<string> relStatus = new List<string>();
            //取试用员工的数据
            if (search.IsProbation != null && search.IsProbation == 1)
            {
                where = where.And(i => !i.PositiWFState.HasValue || (i.PositiWFState.HasValue && i.PositiWFState.Value == (int)WFStates.草稿));
                relStatus.AddRange(new List<string>() {
                    "试用员工",
                    "试用员工（延期转正）",
            });
            }
            else
            {
                relStatus.AddRange(new List<string>() {
                    "派驻",
                    "正式员工",
                    "试用员工",
                    "第三方用工",
                    "第三方员工",
                    "试用员工（延期转正）",
                    "正式员工（校招）",
                    "正式员工（销售）",
                    "顾问"
                });
            }
            where = where.And(x => relStatus.Contains(x.EmployRelStatus));

            var data = await q.Where(where).GetPageResultAsync(input);
            if (data.Data.Count > 0)
            {
                foreach (var item in data.Data)
                {
                    item.DecryptFormal();
                    item.F_InductionOrg = _base_PostBusiness.GetOrgName(item.F_PositionId);
                }
            }
            return data;
        }
        public async Task AddDataAsync(HR_FormalEmployees data)
        {
            await InsertAsync(data);
        }
        public void AddData(HR_FormalEmployees data)
        {
            this.Insert(data);
        }
        public async Task UpdateDataAsync(HR_FormalEmployees data)
        {
            await UpdateAsync(data);
        }
        public void UpdateData(HR_FormalEmployees data)
        {
            this.Db.Update(data);
        }
        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }
        /// <summary>
        /// 业务删除正式员工
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task BusDeleteDataAsync(List<string> ids)
        {
            var formalEmployees = GetList().Where(i => ids.Contains(i.F_Id));
            if (formalEmployees.Count() > 0)
            {
                formalEmployees.ForEach(i => i.F_BusState = (int)ASKBusState.删除);
                await UpdateAsync(formalEmployees.ToList());
            }
        }
        /// <summary>
        /// 员工解绑
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task UnBindUsers(string id)
        {
            var Hr_Form = await GetEntityAsync(id);
            if (Hr_Form != null)
            {
                if (Hr_Form.BaseUserId != null)
                {
                    var user = await this.Db.GetEntityAsync<Base_User>(Hr_Form.BaseUserId);
                    if (user != null)
                    {
                        //改变用户表的绑定状态
                        user.IsBinding = 0;
                        await this.Db.UpdateAsync(user);
                    }
                }
                //修改员工表
                Hr_Form.BaseUserId = "";
                await this.UpdateAsync(Hr_Form);
            }
        }
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            return GetHR_PersonnelChangeDetails(input).ToDataTable();
        }
        /// <summary>
        /// 导出(最新职级)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public DataTable GetPositionExcelListAsync(PageInput<ConditionDTO> input)
        {
            var sqlStr = @"select 
                f.NameUser as NameUser,CONVERT(varchar(100),co.F_StartTime,111)  as ND,
                (case when CONVERT(varchar(100),co.F_EndTime, 111)  is  NULL then '至今' else  CONVERT(varchar(100),co.F_EndTime, 111) end )
                as ND2
                ,co.F_PositionInfo  as PostName,
                co.F_OrganizeInfo as OrgInfo,f.EmployRelStatus as EmployRelStatus,
                co.F_Rank as F_Rank
                from 
                [dbo].[HR_FormalEmployees] f 
                left join [dbo].[HR_CompanyEmploy] co on f.F_Id=co.F_UserId 
                and co.F_StartTime = ( select MAX(F_StartTime) from  
                [dbo].[HR_CompanyEmploy] where F_UserId=f.F_Id and F_EndTime is null 
                or F_EndTime=(select MAX(F_EndTime) from [HR_CompanyEmploy] where F_UserId=f.F_Id  )) where 1=1
               ";
            if (input.PostId?.Count > 0)
            {
                var depList = string.Join("','", input.PostId.ToArray());
                sqlStr += " and f.F_DepartmentId in ('" + depList + "')";
            }
            if (input.IsInterview?.Count > 0)
            {
                var IntList = string.Join("','", input.IsInterview.ToArray());
                sqlStr += " and f.F_CompanyId in ('" + IntList + "')";
            }
            if (input.RoundInterview?.Count > 0)
            {
                var rouList = string.Join("','", input.RoundInterview.ToArray());
                sqlStr += " and f.EmployRelStatus in ('" + rouList + "')";
            }
            var q = this.Db.GetListBySql<HR_FormalEmployeesDTO>(sqlStr).AsQueryable();
            var where = LinqHelper.True<HR_FormalEmployeesDTO>();
            var search = input.Search;
            if (!search.EmployRelStatus.IsNullOrEmpty() && search.EmployRelStatus != "所有员工")
            {
                q = q.Where(i => search.EmployRelStatus != null && i.EmployRelStatus.Contains(search.EmployRelStatus));
            }
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_FormalEmployeesDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return q.Where(where).ToDataTable();
        }

        public HomeInfo GetHomeInfo(string UserId)
        {
            DateTime dataNow = DateTime.Now;
            var str = " select a.F_Id, a.F_InTime,a.F_PositionId,b.F_IsDirectReports, a.F_Rank as RankInfo, e.displayName as Name,a.HeadPortrait as Url,b.F_FullName as CompanyName,b.Name as DepName,b.F_Name as PostName, " +
                " a.MobilePhone as Phone ,a.OfficePhone as TepPhone," +
                " a.Email as EMail , c.F_InductionDate as InductionTime from[dbo].[HR_FormalEmployees] a " +
                " left join(select ps.F_Id,ps.F_IsDirectReports, cmp.F_FullName, dep.Name, ps.F_Name from[dbo].[Base_Company] cmp,[dbo].[Base_Department] dep,[dbo].[Base_Post] ps " +
                " where cmp.F_Id = dep.F_CompanyId and dep.Id = ps.F_DepartmentId) b on a.F_PositionId = b.F_Id " +
                " left join(select* from HR_Induction where F_BusState= 1) c on c.F_UserId = a.F_Id " +
                " left join[dbo].[Base_User] e on a.BaseUserId = e.Id " +
                " where a.BaseUserId = '" + UserId + "'";
            var tmep = Db.GetListBySql<HomeInfo>(str).FirstOrDefault();
            if (tmep == null)
            {
                return new HomeInfo();
            }
            tmep.Phone = AESHelper.DecryptString(tmep.Phone, AESHelper.AesKey);
            if (tmep.F_InTime.HasValue)
            {
                tmep.RankInfoNum = (int)Math.Ceiling(((dataNow - tmep.F_InTime.Value).TotalDays) / 30);

            }
            var companyEmploy = Db.GetIQueryable<HR_CompanyEmploy>().OrderByDescending(i => i.F_StartTime).FirstOrDefault(i => i.F_UserId == tmep.F_Id);
            tmep.PostNameNum = 0;
            if (companyEmploy != null)
            {
                tmep.PostNameNum = (int)Math.Ceiling(((dataNow - companyEmploy.F_StartTime.Value).TotalDays) / 30);
            }

            var entity = Db.GetIQueryable<HR_LaborContractInfo>().Where(i => i.ContractSigningType == "生效" && i.UserId == tmep.F_Id).OrderByDescending(i => i.ContractEffectDate).FirstOrDefault();
            if (entity != null)
            {
                tmep.ContractType = entity.ContractType;
                tmep.ContractTypeNum = (int)Math.Ceiling(((dataNow - entity.ContractEffectDate.Value).TotalDays) / 30);
            }

            if (tmep.InductionTime.HasValue)
            {

                TimeSpan ts = dataNow.Subtract(tmep.InductionTime.Value);
                //tmep.InductionYear = ts.Days / 365;
                //tmep.InductionMonth = (ts.Days % 365) / 30;
                //tmep.InductionDay = (ts.Days % 365) % 30;
                int month = 0;
                int day = 0;
                tmep.InductionYear = ts.Days / 365;
                DateTime mi = tmep.InductionTime.Value.AddYears(tmep.InductionYear);
                while ((mi = mi.AddMonths(1)) <= dataNow)
                {
                    month++;
                }
                tmep.InductionMonth = month;

                mi = mi.AddMonths(-1);
                while ((mi = mi.AddDays(1)) <= dataNow)
                {
                    day++;
                }
                tmep.InductionDay = day;
            }
            return tmep;
        }
        /// <summary>
        /// 上传用户信息导入
        /// </summary>
        /// <param name="list"></param>
        public async Task UploadFileByUserInfo(List<HR_FormalEmployeeImportDTO> list)
        {
            var employees = await this.GetIQueryable().Where(x => list.Select(x => x.userName).Contains(x.NameUser)).AsNoTracking().ToListAsync();
            var inductions = await this.Db.GetIQueryable<HR_Induction>().Where(x => employees.Select(x => x.F_Id).Contains(x.F_UserId)).AsNoTracking().ToListAsync();
            if (employees.Count > 0)
            {
                var companies = await this.Db.GetIQueryable<Base_Company>().AsNoTracking().ToListAsync();
                var departments = await this.Db.GetIQueryable<Base_Department>().AsNoTracking().ToListAsync();
                var posts = await this.Db.GetIQueryable<Base_Post>().AsNoTracking().ToListAsync();
                var updateForm = new List<HR_FormalEmployees>();
                var updateInduction = new List<HR_Induction>();
                list.ForEach(item =>
                {
                    if (!string.IsNullOrWhiteSpace(item.userName))
                    {
                        if (item.userName == "袁媛")
                        {
                            Console.WriteLine("111");
                        }
                        var user = employees.Find(x => x.NameUser == item.userName);
                        if (user != null)
                        {
                            //var induction = inductions.Find(x => x.F_UserId == user.F_Id);
                            var company = companies.FirstOrDefault(x => x.F_FullName.Equals(item.companyName.Trim()));
                            if (company != null)
                            {
                                user.F_CompanyId = company.F_Id;
                            }
                            var companyDeps = departments.Where(x => x.F_CompanyId == user.F_CompanyId);
                            if (!string.IsNullOrWhiteSpace(item.seconDepName) && item.seconDepName != "-")
                            {
                                //查询二级部门
                                var firstDepartment = companyDeps.FirstOrDefault(x => x.Name == item.firstDepName.Trim());
                                if (firstDepartment != null)
                                {
                                    var department = companyDeps.FirstOrDefault(x => x.ParentId == firstDepartment.Id && x.Name == item.seconDepName.Trim());
                                    if (department != null)
                                    {
                                        user.F_DepartmentId = department.Id;
                                    }
                                }

                            }
                            else
                            {
                                //只取一级部门
                                var department = companyDeps.FirstOrDefault(x => x.Name == item.firstDepName.Trim());
                                if (department != null)
                                {
                                    user.F_DepartmentId = department.Id;
                                }
                            }
                            user.Sex = item.sex == "女" ? 0 : 1;
                            var post = posts.FirstOrDefault(x => x.F_CompanyId == user.F_CompanyId && x.F_DepartmentId == user.F_DepartmentId && x.F_Name == item.postName);
                            if (post != null)
                            {
                                user.F_PositionId = post.F_Id;
                                //induction.F_InductionPosition = post.F_Id;
                            }
                            user.F_Rank = item.rankName;
                            updateForm.Add(user);
                        }
                    }
                });
                if (updateForm.Count > 0)
                {
                    await this.Db.UpdateAsync(updateForm);
                }
            }
        }
        /// <summary>
        /// 筛选信息
        /// </summary>
        /// <returns></returns> 
        public ScreeningInfoModel GetScreeningInfo(DataInputDTO data)
        {
            ScreeningInfoModel screeningInfo = new ScreeningInfoModel();
            var strSql = "select distinct a.F_DepartmentId as Value ,b.Name as Name,c.Name as PName  from [dbo].[HR_FormalEmployees]  a  left join[dbo].[Base_Department] b " +
                  "on a.F_DepartmentId = b.Id left join[dbo].[Base_Department] c on b.ParentId = c.Id ";
            if (data.Data == "正式员工")
            {
                strSql += " where a.EmployRelStatus like '%正式员工%'";
            }
            screeningInfo.Department = Db.GetListBySql<ScreeningInfo>(strSql);
            strSql = "select distinct a.EmployRelStatus   as Value, a.EmployRelStatus as Name  from [dbo].[HR_FormalEmployees]  a  ";
            if (data.Data == "正式员工")
            {
                strSql += "where a.EmployRelStatus like '%正式员工%'";
            }
            screeningInfo.WorkingState = Db.GetListBySql<ScreeningInfo>(strSql);
            strSql = " select  distinct F_Id as Value, F_ProjectName as Name from  [dbo].[Base_Company] where F_ProjectName is not null";
            screeningInfo.ProjectList = Db.GetListBySql<ScreeningInfo>(strSql);
            return screeningInfo;
        }
        #endregion

        #region 外部接口
        /// <summary>
        /// 员工关怀接口，推送短信
        /// </summary>
        /// <returns></returns>
        public async Task GetUserPush()
        {
            List<string> relStatus = new List<string>()
            {
                    "派驻",
                    "正式员工",
                    "试用员工",
                    "第三方用工",
                    "第三方员工",
                    "试用员工（延期转正）",
                    "正式员工（校招）",
                    "正式员工（销售）",
                    "顾问"
            };
            var now = DateTime.Now.ToString("MM-dd");
            var hR_FormalEmployees = this.GetIQueryable().ToList().Where(x => relStatus.Contains(x.EmployRelStatus) &&
            ((x.F_InTime.HasValue && x.F_InTime.Value.ToString("MM-dd") == now) ||
            (x.DirthDate.HasValue && x.DirthDate.Value.ToString("MM-dd") == now))).ToList();
            var base_UserLogs = new List<Base_UserLog>();
            var base_Users = await this.Db.GetIQueryable<Base_User>().ToListAsync();
            var laborContractInfos = await this.Db.GetIQueryable<HR_LaborContractInfo>().ToListAsync();
            var companies = await this.Db.GetIQueryable<Base_Company>().ToListAsync();
            if (hR_FormalEmployees.Count > 0)
            {
                hR_FormalEmployees.ForEach(async item =>
                {
                    var user = base_Users.Where(x => x.RealName == item.NameUser && !string.IsNullOrWhiteSpace(x.MobileStr))?.OrderByDescending(x => x.CreateTime).FirstOrDefault();
                    var phone = user != null ? user.MobileStr : item.MobilePhoneStr;
                    var hR_LaborContractInfo = laborContractInfos.Where(x => x.UserId == item.F_Id && x.IsSigningContract.HasValue && x.IsSigningContract.Value == 1).OrderByDescending(x => x.F_CreateDate).FirstOrDefault();
                    var company = hR_LaborContractInfo != null ? hR_LaborContractInfo.SubjectLaborContract : "重庆招商置地开发有限公司";
                    var F_ShortName = companies.Find(x => x.F_FullName == company)?.F_ShortName;
                    //判断入职时间
                    if (item.F_InTime.HasValue && item.F_InTime.Value.ToString("MM-dd") == now && !string.IsNullOrWhiteSpace(phone))
                    {
                        var timeSpan = DateTime.Now.Year - item.F_InTime.Value.Year;
                        if (timeSpan > 0)
                        {
                            var msg = $"【入职纪念日】\n亲爱的{item.NameUser}:\n今天是你入职{F_ShortName}『{timeSpan}周年』的日子\n感恩遇见与你携手并进\n未来继续我们一路同行\n愿常怀热爱\n愿初心如磐\n愿事事顺利\n";
                            //=
                            await Task.Run(() => { SendMessageHelper.SendSMSMsg(phone, msg, "员工关怀短信"); });

                            Base_UserLog base_UserLog = new Base_UserLog()
                            {
                                Id = IdHelper.GetId(),
                                CreateTime = DateTime.Now,
                                CreatorId = "admin",
                                CreatorRealName = "超级管理员",
                                LogContent = $"发送{item.NameUser}{timeSpan}周年关怀短信",
                                LogType = UserLogType.关怀短信.ToString(),
                                JsonContent = msg,
                            };
                        }
                    }
                    //判断生日
                    if (item.DirthDate.HasValue && item.DirthDate.Value.ToString("MM-dd") == now && !string.IsNullOrWhiteSpace(phone))
                    {
                        var msg = $"【生日祝福】\n亲爱的{item.NameUser}: \n在这个特别的日子，{F_ShortName}祝愿你\n生日快乐!\nhappy birthday!\n岁岁平安 顺风顺水 愿望成真 快乐万岁\n一路同行，感恩有你\n";
                        //=
                        await Task.Run(() => { SendMessageHelper.SendSMSMsg(phone, msg, "员工关怀短信"); });

                        Base_UserLog base_UserLog = new Base_UserLog()
                        {
                            Id = IdHelper.GetId(),
                            CreateTime = DateTime.Now,
                            CreatorId = "admin",
                            CreatorRealName = "超级管理员",
                            LogContent = $"发送{item.NameUser}生日关怀短信",
                            LogType = UserLogType.关怀短信.ToString(),
                            JsonContent = msg,
                        };
                    }
                });
            }
        }
        #endregion 

        #region 打卡小程序
        /// <summary>
        /// 获取打卡审批流程者
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<HR_FormalEmployees>> GetPunchCarFlowUsers()
        {
            return await this.Db.GetIQueryable<HR_FormalEmployees>().Where(i => (i.IsPunchCardType.HasValue && i.IsPunchCardType != 0)).ToListAsync();
        }
        #endregion
        #region 私有成员

        #endregion
    }
}