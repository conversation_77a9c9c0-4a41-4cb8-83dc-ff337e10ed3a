<template>
  <span class="count-down-container">
     {{hours}} 小时 {{min}} 分钟 {{sec}} 秒
  </span>
</template>

<script>
  export default{
    name:"CountDown",
    data(){
      return{
        hours:0,
        min:0,
        sec:0,
        time:""
      };
    },
    methods:{
      //单位均为分钟
      init(min){
        if(min>=60){
          //大于60分钟，去除60分钟以上的
          this.min = min%60;
          if(this.min==0){
            //刚好为整数小时，不含分钟数
            this.hours = (parseInt(min/60)-1);
            this.min=59;
          }else{
            this.hours = parseInt(min/60);
            this.min = this.min-1;
          }
        }
        if(min<60){
          //小于60分钟无需换算,最开始计算减1分钟
          this.min=parseInt(min-1);
        }
        //从60秒开始倒计时
        this.sec=59;
        this.countTime();
      },
      countTime(){
        let _this=this;
        setInterval(function(){
          _this.sec--;
          if(_this.hours==0&&_this.min==0&&_this.sec==0){
            //倒计时结束 停止计时
          }
          if(_this.sec==0){
            //倒计时秒数为0 ，重置为60秒
            if(_this.min==0){
              //倒计时分钟为0
              if(_this.hours==0){
                //倒计时小时为0，终止循环
                 clearInterval(countTime());
              }
              if(_this.hours>0){
                //分钟设置为60分钟，秒数设置为59秒
                _this.hours--;
                _this.min = 59;
                _this.sec = 59;
              }
            }
            if(_this.min>0){
              //分钟大于0，分钟数减1，秒数重置为60秒
              _this.min--;
              _this.sec = 59;
            }
          }
        },1000)
      }
    },
    mounted() {
    }
  }
  
</script>

<style>
</style>
