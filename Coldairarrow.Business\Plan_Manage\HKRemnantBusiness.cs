﻿using Coldairarrow.Entity.Plan_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System;
using Coldairarrow.Util.DataAccess;

namespace Coldairarrow.Business.Plan_Manage
{
    public class HKRemnantBusiness : BaseBusiness<HKRemnant>, IHKRemnantBusiness, ITransientDependency
    {
        public HKRemnantBusiness(IERPDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HKRemnant>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HKRemnant>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HKRemnant, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HKRemnant> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HKRemnant data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HKRemnant data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HKRemnant>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HKRemnant, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<A01_HKRemnant, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 运营驾驶舱
        public async Task<HKRemnantDTO> GetHKRemnantData(DateTime? date)
        {
            date = date ?? DateTime.Now;
            var dateStr = date.Value.ToString("yyyy-MM-dd");
            HKRemnantDTO hKRemnantDTO = new HKRemnantDTO();
            hKRemnantDTO.ytDatas = await this.Db.GetListBySqlAsync<HKRemnant>(@$"SELECT TOP (100) *
  FROM [Mysoft_ERP25].[dbo].[A01_HKRemnant]
  where createdate in (select max(createdate) from [Mysoft_ERP25].[dbo].[A01_HKRemnant] where 1=1  and CONVERT(varchar,[CreateDate],23)='{dateStr}') ");
            if (hKRemnantDTO.ytDatas.Count > 0)
            {
                //获取各项目得数据
                hKRemnantDTO.projectDatas = hKRemnantDTO.ytDatas.GroupBy(x => x.projName).Select(item => new HKRemnant()
                {
                    projName = item.Key,
                    dayhk = item.Sum(x => x.dayhk),
                    weekhk = item.Sum(x => x.weekhk),
                    monthhk = item.Sum(x => x.monthhk),
                    quarterhk = item.Sum(x => x.quarterhk),
                    yearhk = item.Sum(x => x.yearhk)
                }).ToList();
                //获取全项目得数据
                hKRemnantDTO.allProjectData = new HKRemnant()
                {
                    dayhk = hKRemnantDTO.ytDatas.Sum(x => x.dayhk),
                    weekhk = hKRemnantDTO.ytDatas.Sum(x => x.weekhk),
                    monthhk = hKRemnantDTO.ytDatas.Sum(x => x.monthhk),
                    quarterhk = hKRemnantDTO.ytDatas.Sum(x => x.quarterhk),
                    yearhk = hKRemnantDTO.ytDatas.Sum(x => x.yearhk)
                };
            }
            return hKRemnantDTO;
        }
        #endregion


        #region 私有成员

        #endregion
    }
}