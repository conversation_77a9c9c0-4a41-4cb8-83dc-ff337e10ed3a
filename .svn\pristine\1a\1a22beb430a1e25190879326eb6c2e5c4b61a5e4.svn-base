﻿<template>
  <a-modal
    :title="title"
    width="40%"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="()=>{this.visible=false}"
  >
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="entity" :rules="rules" v-bind="layout">
        <a-form-model-item label="F_Id" prop="F_Id">
          <a-input v-model="entity.F_Id" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="创建时间" prop="F_CreateDate">
          <a-input v-model="entity.F_CreateDate" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="创建人" prop="F_CreateUserId">
          <a-input v-model="entity.F_CreateUserId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="创建人名" prop="F_CreateUserName">
          <a-input v-model="entity.F_CreateUserName" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="修改时间" prop="F_ModifyDate">
          <a-input v-model="entity.F_ModifyDate" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="修改人" prop="F_ModifyUserId">
          <a-input v-model="entity.F_ModifyUserId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="修改人名" prop="F_ModifyUserName">
          <a-input v-model="entity.F_ModifyUserName" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="业务状态" prop="F_BusState">
          <a-input v-model="entity.F_BusState" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="流程Guid" prop="F_WFId">
          <a-input v-model="entity.F_WFId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="流程状态" prop="F_WFState">
          <a-input v-model="entity.F_WFState" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="创建时间" prop="F_StartTime">
          <a-input v-model="entity.F_StartTime" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="审核完成时间" prop="F_EndTime">
          <a-input v-model="entity.F_EndTime" autocomplete="off" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      layout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 18 }
      },
      visible: false,
      loading: false,
      entity: {},
      rules: {},
      title: ''
    }
  },
  methods: {
    init() {
      this.visible = true
      this.entity = {}
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    openForm(id, title) {
      this.init()

      if (id) {
        this.loading = true
        this.$http.post('/PunchCard_Manage/PunchCard_Flow_List/GetTheData', { id: id }).then(resJson => {
          this.loading = false

          this.entity = resJson.Data
        })
      }
    },
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.loading = true
        this.$http.post('/PunchCard_Manage/PunchCard_Flow_List/SaveData', this.entity).then(resJson => {
          this.loading = false

          if (resJson.Success) {
            this.$message.success('操作成功!')
            this.visible = false

            this.parentObj.getDataList()
          } else {
            this.$message.error(resJson.Msg)
          }
        })
      })
    }
  }
}
</script>
