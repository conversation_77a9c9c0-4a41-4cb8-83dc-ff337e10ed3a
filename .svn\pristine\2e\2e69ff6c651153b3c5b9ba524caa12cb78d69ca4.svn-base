﻿<template>
  <!-- 内容 -->
  <div class="container" style="margin-top: 20px;">
    <div class="row indextop">
      <div class="col-sm-12 col-lg-8">
        <div id="carouselExampleIndicators" class="carousel">

          <a-carousel autoplay>
            <div>
              <div class="carousel-item active">
                <div class="des">招置学堂迎新上线，知识分享如此简单 | 探索观阅</div>
                <img src="@/assets/school/img/firstSlide.png" class="d-block w-100">
              </div>
            </div>
            <div>
              <div class="carousel-item active">
                <div class="des">有了这些有趣小物，谁都知道我是读书人 | 看看下面</div>
                <img src="@/assets/school/img/seSlide.jpg" class="d-block w-100">
              </div>
            </div>

          </a-carousel>

        </div>
      </div>
      <div class="col-sm-6 col-md-2 col-lg-2 " style="flex:none;">
        <div class="row cards" @click="goinfo(content.F_Id,'/School/Course/Content')"
          v-for="content in contentList.slice(0,2)" :key="content.F_Id">
          <img class="cimag" :src="content.F_CoverImage" width="100%"
            onerror="this.src='https://hrapi.cqlandmark.com/uploadfile/img/bg2.jpg'" />
          <div class="card-box" :style="'left:0'">
            <div class="card-tit">{{content.F_Title}}</div>
          </div>
        </div>
      </div>

    </div>

    <!-- 分享推荐 -->
    <div>
      <div class="share">
        <div class="share-title">分享推荐</div>
        <div>
          <nav aria-label="Page navigation" class="page-nation">
            <a-pagination show-size-changer :default-current="Recompagination.current"
              :defaultPageSize="Recompagination.pageSize" :total="Recompagination.total"
              @showSizeChange="onShowSizeChange" @change="onChangeCurrent" style="margin: 5px 0;text-align: center;" />
          </nav>
        </div>
      </div>
      <div class="row">
        <div class="col-sm-6 col-md-4 col-lg-3 cards" v-for="content in RecommendList" :key="content.F_Id"
          @click="goinfo(content.F_Id,'/School/Course/Content')">
          <img class="cimag" :src="content.F_CoverImage" width="100%"
            onerror="this.src='https://hrapi.cqlandmark.com/uploadfile/img/bg1.jpg'" />
          <div class="card-box">
            <div class="card-tit">{{content.F_Title}}</div>
            <div class="flex-start">
              <div class="flex-scan mr">
                <img src="@/assets/school/img/<EMAIL>" width="17px" height="11px" />
                <div>{{content.F_Hits}}</div>
              </div>
              <div class="flex-scan mr">
                <img src="@/assets/school/img/<EMAIL>" width="15px" height="13px" />
                <div>{{content.F_IsDiscuss}}</div>
              </div>
              <div class="flex-scan">
                <img src="@/assets/school/img/<EMAIL>" width="14px" height="12px" />
                <div>{{content.F_IsHot}}</div>
              </div>
            </div>
          </div>
          <div class="card-bottom flex-scan">
            <img :src="$rootUrl+'HR_Manage/HR_PersonPhoto/GetFile?userid='+content.F_CreateUserId"
              :alt="content.F_CreateUserName"
              onerror="this.src='https://hrapi.cqlandmark.com/uploadfile/img/head.png'" />
            <span>{{content.F_CreateUserName}}<span
                style="color: #B4B4B4;">（{{content.F_CreateDate  | dayjs('YYYY-MM-DD')}}）</span></span>
          </div>
        </div>
      </div>

      <!-- 版块推荐 -->
      <div>
        <div class="share">
          <div class="share-title">版块推荐</div>
        </div>
        <div class="row part-box">
          <template v-if="DictionList.length>0">
            <div class=" part-card cards" v-for="(ditem,dindex) in DictionList" :key="ditem.F_ItemValue"
              @click="goinfo(ditem.F_ItemValue,'/School/Course/List')">
              <div class="des" :style="{ 'display':  'block' }"></div>
              <div class="txt">{{ditem.F_ItemName}}</div>
              <img :src='`https://hrapi.cqlandmark.com/uploadfile/img/bg${dindex+1}.jpg`' width="100%" height="100%" />
            </div>
          </template>
          <template v-else>
            <div class="des">没有相关板块推荐</div>
          </template>

        </div>
      </div>

      <!-- 系列推荐、近期热门 -->
      <div class="row">
        <!-- 系列推荐 -->
        <div class="col-lg-6">
          <div class="hot-title share-title">系列推荐</div>
          <div class="recomend" style="box-shadow: 3px 3px 6px #ededed;">
            <div class="row media-box" v-for="classify in classifyList" :key="classify.F_Id"
              @click="goinfo(classify.F_Id,'/School/Series/Detail')">
              <div class="col-sm-4 col-md-3">
                <img :src="classify.F_CoverImage" width="100%"
                  onerror="this.src='https://hrapi.cqlandmark.com/uploadfile/img/bg1.jpg'" />
              </div>
              <div class="col-sm-8 col-md-9">
                <h4 class="media-title out-over">{{classify.ClassifyName}}</h4>
                <div class="media-cont out-over">{{classify.Describe}}</div>
                <!-- 浏览记录 -->
                <div class="media-cont mt20">
                  <div class="flex-between">
                    <div class="flex-start">
                      <div class="flex-scan mr">
                        <img src="@/assets/school/img/<EMAIL>" width="17px" height="11px" />
                        <div>{{classify.F_Hits}}</div>
                      </div>
                      <div class="flex-scan mr">
                        <img src="@/assets/school/img/<EMAIL>" width="15px" height="13px" />
                        <div>{{classify.F_IsDiscuss}}</div>
                      </div>
                      <div class="flex-scan">
                        <img src="@/assets/school/img/<EMAIL>" width="14px" height="12px" />
                        <div>{{classify.F_IsHot}}</div>
                      </div>
                    </div>
                    <!-- 日期 -->
                    <div class="fon14">{{classify.F_CreateDate  | dayjs('YYYY-MM-DD')}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 近期热门 -->
        <div class="col-lg-6">
          <div class="hot-title share-title">近期热门</div>
          <div class="hot-card" style="box-shadow: 3px 3px 6px #ededed;">
            <div v-for="content in HotList" :key="content.F_Id" @click="goinfo(content.F_Id,'/School/Course/Content')">
              <div class="hot-one">
                <div class="hot-left">{{content.F_Title}} | {{content.F_Abstract}}</div>
                <div class="colo9">{{content.F_CreateDate  | dayjs('YYYY-MM-DD')}}</div>
              </div>
              <div class="line-gray"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import '@/assets/school/css/bootstrap.min.css';

import '@/assets/school/css/base.css';
export default {
  mounted () {
    this.init()
  },
  data () {
    return {

      loading: false,
      confirmLoading: false,
      DictionList: [],
      contentList: [],
      classifyList: [],
      RecommendList: [],//推荐的内容
      HotList: [],//近期热门的
      queryParam: { condition: "" },
      Recompagination: {
        current: 1,
        pageSize: 10,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      //左边
      pagination: {
        current: 1,
        pageSize: 12,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      // fxpagination: {
      //   current: 1,
      //   pageSize: 5,
      //   showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      // },
      //系列分页
      classifypagination: {
        current: 1,
        pageSize: 3,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
    };
  },
  methods: {
    init (id) {
      this.getContentList();
      // document.getElementsByClassName('ant-tabs-extra-content')
      this.getUserList();
      //获取分类数据字典
      this.GetDictionaries();
    },
    //获取数据字典
    GetDictionaries () {
      this.commonApi.dictTypeInfo("招置学堂类别").then(res => {
        this.DictionList = res
        // console.log(this.DictionList)
      })
    },
    onShowSizeChange (current, pageSize) {
      this.Recompagination.current = current
      this.Recompagination.pageSize = pageSize
      this.getContentList()
    },
    onChangeCurrent (current, pageSize) {
      this.Recompagination.current = current
      this.getContentList()
    },
    //跳转
    goinfo (id, addr) {
      this.$router.push({
        path: addr,
        query: {
          id: id
        }
      })
    },
    getContentList () {
      this.$http.post('/S_School/S_ContentPage/GetDataList', {
        Search: this.queryParam, IsHot: 1, PageIndex: this.Recompagination.current,
        PageRows: this.Recompagination.pageSize
      }).then(resJson => {
        this.RecommendList = resJson.Data;
        const Recompagination = { ...this.Recompagination }

        this.Recompagination = Recompagination
        this.Recompagination.total = resJson.Total
      });


    },
    //获取课程系列
    getUserList () {
      this.$http.post('/S_School/S_ContentPage/GetDataList', {
        Search: this.queryParam, PageIndex: this.pagination.current,
        PageRows: this.pagination.pageSize,
      }).then(resJson => {
        this.contentList = resJson.Data;

      });

      this.queryParam.ContentId = "1";
      this.$http.post('/S_School/S_Classify/GetDataList', {
        Search: this.queryParam, PageIndex: this.classifypagination.current,
        PageRows: this.classifypagination.pageSize
      }).then(resJson => {
        console.log(resJson.Data)
        this.classifyList = resJson.Data;
      });
      //获取热门
      this.$http.post('/S_School/S_ContentPage/GetHotContent', { Search: {} }).then(resJson => {
        this.HotList = resJson.Data;
      });
    },
  },
};
</script>

<style lang="less" scoped>
@charset "utf-8";
@media (min-width: 576px) {
  .col-sm-6 {
    max-width: 48%;
  }
  .col-sm-12 {
    max-width: 98%;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 95%;
  }
  .col-sm-6 {
    max-width: 48%;
  }
}
@media (min-width: 992px) {
  .container {
    max-width: 90%;
  }
  .col-md-2 {
    max-width: 31.333%;
  }
  .col-md-4 {
    max-width: 31.333%;
  }
  .col-md-8 {
    max-width: 58%;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 90%;
  }
  .col-lg-2 {
    max-width: 28%;
  }
  .col-lg-3 {
    max-width: 18%;
  }
  .col-lg-8 {
    max-width: 72%;
  }
}
.part-box {
  padding-top: 20px;
  .part-card {
    max-width: 14.633%;
    position: relative;
    text-align: center;
  }
  .des {
    position: absolute;
    top: 0;
    display: none;
    text-align: center;
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0.35;
    color: #fff;
  }
  .txt {
    position: absolute;
    top: 40%;
    text-align: center;
    width: 100%;
    color: #fff;
    cursor: pointer;
  }
  .part-card:hover .des {
    opacity: 0.6;
    cursor: pointer;
  }
}
.cards {
  background: #ffffff;
  padding-left: 0;
  padding-right: 0;
  margin-left: 1%;
  margin-right: 1%;
  flex: none;
  margin-bottom: 20px;
}
.card-box {
  padding: 0 15px;
}
.card-tit {
  font-size: 16px;
  font-weight: 600;
  color: #282828;
  padding: 10px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: #999999;
  font-size: 12px;
  width: 80%;
}
.mr {
  margin-right: 10%;
}
.flex-scan {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-scan img {
  margin-right: 5px;
}
.card-bottom {
  border-top: 1px solid #f4f4f4;
  font-size: 12px;
  margin-top: 10px;
  padding: 15px;
}
.card-bottom img {
  width: 24px;
  height: 24px;
  border-radius: 100%;
}
.share {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  padding-left: 30px;
}
.share-title {
  font-size: 24px;
  font-weight: 800;
  color: #282828;
}

.media-title {
  margin-top: 10px;
  margin-bottom: 5px;
  font-size: 16px;
  font-weight: 600;
  color: #282828;
}
.media-cont {
  font-size: 14px;
  color: #969696;
}

.media-box {
  min-height: 120px;
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}
.recomend {
  padding-top: 15px;
  background: #ffffff;
}
.hot-card {
  padding: 20px 15px 30px;
  background: #fff;
  font-size: 14px;
}
.hot-title {
  margin-top: 20px;
  margin-bottom: 15px;
  margin-left: 20px;
}
.hot-one {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
}
.hot-left {
  width: 75%;
  font-size: 16px;
  font-weight: 600;
  color: #282828;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.indextop {
  .cards {
    -webkit-box-shadow: none;
    box-shadow: none;
    cursor: pointer;
    position: relative;
    margin-bottom: 41px;
  }
  .card-box {
    position: absolute;
    bottom: -22px;
    left: 0;
    background: #fff;
    width: 100%;
    padding: 0;
    text-indent: 18px;
  }
}
.carousel-item {
  text-align: center;
  position: relative;

  .des {
    position: absolute;
    top: 40%;
    text-align: center;
    width: 100%;
    font-size: 30px;
    font-weight: bold;
    color: #fff;
  }
}
.page-nation {
  margin: 0;
  margin-bottom: 20px;
}
</style>