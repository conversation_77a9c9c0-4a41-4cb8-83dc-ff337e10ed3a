﻿<template>
  <a-card :bordered="false" :hoverable="true">
    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="hanldleAdd()">新建</a-button>
      <a-button
        type="primary"
        icon="minus"
        @click="handleDelete(selectedRowKeys)"
        :disabled="!hasSelected()"
        :loading="loading"
        >删除</a-button
      >
      <a-button type="primary" icon="redo" @click="exportExcel()">导出Excel</a-button>
      <a-button type="primary" icon="redo" @click="getDataList()">刷新</a-button>
    </div>

    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="10">
          <a-col :md="4" :sm="24">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="queryParam.condition">
                <a-select-option key="F_Id">F_Id</a-select-option>
                <a-select-option key="F_CreateUserId">创建人</a-select-option>
                <a-select-option key="F_CreateUserName">创建人名</a-select-option>
                <a-select-option key="F_ModifyUserId">修改人</a-select-option>
                <a-select-option key="F_ModifyUserName">修改人名</a-select-option>
                <a-select-option key="F_WFId">流程Guid</a-select-option>
                <a-select-option key="F_Remark">备注</a-select-option>
                <a-select-option key="F_EmployeeRelations">请注明员工姓名关系</a-select-option>
                <a-select-option key="F_Signature">申请人签字</a-select-option>
                <a-select-option key="F_Other">其他</a-select-option>
                <a-select-option key="F_Interviewer">初面面试官</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item>
              <a-input v-model="queryParam.keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-button
              type="primary"
              @click="
                () => {
                  this.pagination.current = 1
                  this.getDataList()
                }
              "
              >查询</a-button
            >
            <a-button style="margin-left: 8px" @click="() => (queryParam = {})">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table
      ref="table"
      :columns="columns"
      :rowKey="row => row.F_Id"
      :dataSource="data"
      :pagination="false"
      :loading="loading"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      :bordered="true"
      size="small"
    >
      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record.F_Id)">编辑</a>
          <a-divider type="vertical" />
          <a @click="handleDelete([record.F_Id])">删除</a>
        </template>
      </span>
    </a-table>
    <a-pagination
      show-size-changer
      :default-current="pagination.current"
      :defaultPageSize="pagination.pageSize"
      :showTotal="pagination.showTotal"
      :total="pagination.total"
      @showSizeChange="onShowSizeChange"
      @change="onChangeCurrent"
      style="margin: 5px 0;text-align: center;"
    />

    <edit-form ref="editForm" :parentObj="this"></edit-form>
  </a-card>
</template>

<script>
import EditForm from './EditForm'

const columns = [
  { title: 'F_Id', dataIndex: 'F_Id', width: '10%' },
  { title: '创建时间', dataIndex: 'F_CreateDate', width: '10%' },
  { title: '创建人', dataIndex: 'F_CreateUserId', width: '10%' },
  { title: '创建人名', dataIndex: 'F_CreateUserName', width: '10%' },
  { title: '修改时间', dataIndex: 'F_ModifyDate', width: '10%' },
  { title: '修改人', dataIndex: 'F_ModifyUserId', width: '10%' },
  { title: '修改人名', dataIndex: 'F_ModifyUserName', width: '10%' },
  { title: '流程Guid', dataIndex: 'F_WFId', width: '10%' },
  { title: '业务状态', dataIndex: 'F_BusState', width: '10%' },
  { title: '流程状态', dataIndex: 'F_WFState', width: '10%' },
  { title: '备注', dataIndex: 'F_Remark', width: '10%' },
  { title: '是否认识本公司同事', dataIndex: 'F_IsKnowledgeE', width: '10%' },
  { title: '请注明员工姓名关系', dataIndex: 'F_EmployeeRelations', width: '10%' },
  { title: '申请人签字', dataIndex: 'F_Signature', width: '10%' },
  { title: '其他', dataIndex: 'F_Other', width: '10%' },
  { title: '预计可上班时间', dataIndex: 'F_WorkTime', width: '10%' },
  { title: '日期', dataIndex: 'F_Date', width: '10%' },
  { title: '初面面试官', dataIndex: 'F_Interviewer', width: '10%' },
  { title: '面试级别', dataIndex: 'F_InterviewLevel', width: '10%' },
  { title: '操作', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
]

export default {
  components: {
    EditForm
  },
  mounted() {
    this.getDataList()
  },
  data() {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 15,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      filters: {},
      sorter: { field: 'F_Id', order: 'asc' },
      loading: false,
      columns,
      queryParam: {},
      selectedRowKeys: []
    }
  },
  methods: {
    onChangeCurrent(current, pageSize) {
      this.pagination.current = current
      this.getDataList()
    },
    onShowSizeChange(current, pageSize) {
      this.pagination.current = current
      this.pagination.pageSize = pageSize
      this.getDataList()
    },
    handleTableChange(pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.filters = { ...filters }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    exportExcel() {},
    getDataList() {
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/HR_Manage/HR_InterviewRecord/GetDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'F_Id',
          SortType: this.sorter.order,
          Search: this.queryParam,
          ...this.filters
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    hasSelected() {
      return this.selectedRowKeys.length > 0
    },
    hanldleAdd() {
      this.$refs.editForm.openForm()
    },
    handleEdit(id) {
      this.$refs.editForm.openForm(id)
    },
    handleDelete(ids) {
      var thisObj = this
      this.$confirm({
        title: '确认删除吗?',
        onOk() {
          return new Promise((resolve, reject) => {
            thisObj.$http.post('/HR_Manage/HR_InterviewRecord/DeleteData', ids).then(resJson => {
              resolve()

              if (resJson.Success) {
                thisObj.$message.success('操作成功!')

                thisObj.getDataList()
              } else {
                thisObj.$message.error(resJson.Msg)
              }
            })
          })
        }
      })
    }
  }
}
</script>
