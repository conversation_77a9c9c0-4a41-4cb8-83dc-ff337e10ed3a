﻿using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.IO;
using System.Text;

namespace Coldairarrow.Api
{
    /// <summary>
    /// 基控制器
    /// </summary>
    [FormatResponse]
    public class BaseController : ControllerBase
    {
        /// <summary>
        /// 获取操作者
        /// </summary>
        /// <returns></returns>
        protected IOperator GetOperator()
        {
            return HttpContext.RequestServices.GetService<IOperator>();
        }
        protected void InitEntity(object obj)
        {
            var op = HttpContext.RequestServices.GetService<IOperator>();
            if (obj.ContainsProperty("Id"))
                obj.SetPropertyValue("Id", Guid.NewGuid().ToString("N"));
            if (obj.ContainsProperty("CreateTime"))
                obj.SetPropertyValue("CreateTime", DateTime.Now);
            if (obj.ContainsProperty("CreatorId"))
                obj.SetPropertyValue("CreatorId", op?.UserId);
            if (obj.ContainsProperty("CreatorRealName"))
                obj.SetPropertyValue("CreatorRealName", op?.Property?.RealName);
            if (obj.ContainsProperty("F_Id"))
                obj.SetPropertyValue("F_Id", Guid.NewGuid().ToString("N"));
            if (obj.ContainsProperty("F_CreateDate"))
                obj.SetPropertyValue("F_CreateDate", DateTime.Now);
            if (obj.ContainsProperty("F_CreateUserId"))
                obj.SetPropertyValue("F_CreateUserId", op?.UserId);
            if (obj.ContainsProperty("F_CreateUserName"))
                obj.SetPropertyValue("F_CreateUserName", op?.Property?.RealName);
        }
        protected void UpdateEntity(object obj)
        {
            var op = HttpContext.RequestServices.GetService<IOperator>();
            if (obj.ContainsProperty("F_ModifyDate"))
                obj.SetPropertyValue("F_ModifyDate", DateTime.Now);
            if (obj.ContainsProperty("F_ModifyUserId"))
                obj.SetPropertyValue("F_ModifyUserId", op?.UserId);
            if (obj.ContainsProperty("F_ModifyUserName"))
                obj.SetPropertyValue("F_ModifyUserName", op?.Property?.RealName);
        }

        protected string GetAbsolutePath(string virtualPath)
        {
            string path = virtualPath.Replace(Path.AltDirectorySeparatorChar, Path.DirectorySeparatorChar);
            if (path[0] == '~')
                path = path.Remove(0, 2);
            string rootPath = HttpContext.RequestServices.GetService<IWebHostEnvironment>().WebRootPath;
            if (rootPath.IsNullOrEmpty())
            {
                rootPath = Path.Combine(HttpContext.RequestServices.GetService<IWebHostEnvironment>().ContentRootPath, "wwwroot");
            }

            return Path.Combine(rootPath, path);
        }

        /// <summary>
        /// 返回JSON
        /// </summary>
        /// <param name="jsonStr">json字符串</param>
        /// <returns></returns>
        protected ContentResult JsonContent(string jsonStr)
        {
            return base.Content(jsonStr, "application/json", Encoding.UTF8);
        }

        /// <summary>
        /// 返回html
        /// </summary>
        /// <param name="body">html内容</param>
        /// <returns></returns>
        protected ContentResult HtmlContent(string body)
        {
            return base.Content(body);
        }

        /// <summary>
        /// 返回成功
        /// </summary>
        /// <returns></returns>
        protected AjaxResult Success()
        {
            AjaxResult res = new AjaxResult
            {
                Success = true,
                Msg = "请求成功！",
            };

            return res;
        }

        /// <summary>
        /// 返回成功
        /// </summary>
        /// <param name="data">返回数据</param>
        /// <returns></returns>
        protected AjaxResult<T> Success<T>(T data)
        {
            AjaxResult<T> res = new AjaxResult<T>
            {
                Success = true,
                Msg = "操作成功",
                Data = data
            };

            return res;
        }

        /// <summary>
        /// 返回成功
        /// </summary>
        /// <param name="data">返回数据</param>
        /// <param name="msg">返回消息</param>
        /// <returns></returns>
        protected AjaxResult<T> Success<T>(T data, string msg)
        {
            AjaxResult<T> res = new AjaxResult<T>
            {
                Success = true,
                Msg = msg,
                Data = data
            };

            return res;
        }

        /// <summary>
        /// 返回错误
        /// </summary>
        /// <returns></returns>
        protected AjaxResult Error()
        {
            AjaxResult res = new AjaxResult
            {
                Success = false,
                Msg = "请求失败！",
            };

            return res;
        }

        /// <summary>
        /// 返回错误
        /// </summary>
        /// <param name="msg">错误提示</param>
        /// <returns></returns>
        protected AjaxResult Error(string msg)
        {
            AjaxResult res = new AjaxResult
            {
                Success = false,
                Msg = msg,
            };

            return res;
        }
    }
}