<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.ResponseCaching</name>
    </assembly>
    <members>
        <member name="M:Microsoft.AspNetCore.ResponseCaching.IResponseCache.Get(System.String)">
            <summary>
            Gets the cached response for the given key, if it exists.
            If no cached response exists for the given key, <c>null</c> is returned.
            </summary>
            <param name="key">The cache key to look up.</param>
            <returns>The response cache entry if it exists; otherwise <c>null</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.ResponseCaching.IResponseCache.GetAsync(System.String)">
            <summary>
            Gets the cached response for the given key, if it exists.
            If no cached response exists for the given key, <c>null</c> is returned.
            </summary>
            <param name="key">The cache key to look up.</param>
            <returns>The response cache entry if it exists; otherwise <c>null</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.ResponseCaching.IResponseCache.Set(System.String,Microsoft.AspNetCore.ResponseCaching.IResponseCacheEntry,System.TimeSpan)">
            <summary>
            Stores the given response in the response cache.
            </summary>
            <param name="key">The cache key to store the response under.</param>
            <param name="entry">The response cache entry to store.</param>
            <param name="validFor">The amount of time the entry will be kept in the cache before expiring, relative to now.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.ResponseCaching.IResponseCache.SetAsync(System.String,Microsoft.AspNetCore.ResponseCaching.IResponseCacheEntry,System.TimeSpan)">
            <summary>
            Stores the given response in the response cache.
            </summary>
            <param name="key">The cache key to store the response under.</param>
            <param name="entry">The response cache entry to store.</param>
            <param name="validFor">The amount of time the entry will be kept in the cache before expiring, relative to now.</param>
            <returns>No result is returned.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.ResponseCaching.IResponseCachingKeyProvider.CreateBaseKey(Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext)">
            <summary>
            Create a base key for a response cache entry.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext"/>.</param>
            <returns>The created base key.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.ResponseCaching.IResponseCachingKeyProvider.CreateStorageVaryByKey(Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext)">
            <summary>
            Create a vary key for storing cached responses.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext"/>.</param>
            <returns>The created vary key.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.ResponseCaching.IResponseCachingKeyProvider.CreateLookupVaryByKeys(Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext)">
            <summary>
            Create one or more vary keys for looking up cached responses.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext"/>.</param>
            <returns>An ordered <see cref="T:System.Collections.Generic.IEnumerable`1"/> containing the vary keys to try when looking up items.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.ResponseCaching.IResponseCachingPolicyProvider.AttemptResponseCaching(Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext)">
            <summary>
            Determine whether the response caching logic should be attempted for the incoming HTTP request.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext"/>.</param>
            <returns><c>true</c> if response caching logic should be attempted; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.ResponseCaching.IResponseCachingPolicyProvider.AllowCacheLookup(Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext)">
            <summary>
            Determine whether a cache lookup is allowed for the incoming HTTP request.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext"/>.</param>
            <returns><c>true</c> if cache lookup for this request is allowed; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.ResponseCaching.IResponseCachingPolicyProvider.AllowCacheStorage(Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext)">
            <summary>
            Determine whether storage of the response is allowed for the incoming HTTP request.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext"/>.</param>
            <returns><c>true</c> if storage of the response for this request is allowed; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.ResponseCaching.IResponseCachingPolicyProvider.IsResponseCacheable(Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext)">
            <summary>
            Determine whether the response received by the middleware can be cached for future requests.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext"/>.</param>
            <returns><c>true</c> if the response is cacheable; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.ResponseCaching.IResponseCachingPolicyProvider.IsCachedEntryFresh(Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext)">
            <summary>
            Determine whether the response retrieved from the response cache is fresh and can be served.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext"/>.</param>
            <returns><c>true</c> if the cached entry is fresh; otherwise <c>false</c>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.ResponseCaching.ISystemClock">
            <summary>
            Abstracts the system clock to facilitate testing.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.ResponseCaching.ISystemClock.UtcNow">
            <summary>
            Retrieves the current system time in UTC.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.ResponseCaching.LoggerExtensions">
            <summary>
            Defines *all* the logger messages produced by response caching
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.OnFinalizeCacheHeaders(Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext)">
            <summary>
            Finalize cache headers.
            </summary>
            <param name="context"></param>
            <returns><c>true</c> if a vary by entry needs to be stored in the cache; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.ResponseCaching.ResponseCachingMiddleware.OnStartResponse(Microsoft.AspNetCore.ResponseCaching.ResponseCachingContext)">
            <summary>
            Mark the response as started and set the response time if no reponse was started yet.
            </summary>
            <param name="context"></param>
            <returns><c>true</c> if the response was not started before this call; otherwise <c>false</c>.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.ResponseCaching.ResponseCachingOptions.SizeLimit">
            <summary>
            The size limit for the response cache middleware in bytes. The default is set to 100 MB.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.ResponseCaching.ResponseCachingOptions.MaximumBodySize">
            <summary>
            The largest cacheable size for the response body in bytes. The default is set to 64 MB.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.ResponseCaching.ResponseCachingOptions.UseCaseSensitivePaths">
            <summary>
            <c>true</c> if request paths are case-sensitive; otherwise <c>false</c>. The default is to treat paths as case-insensitive.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.ResponseCaching.ResponseCachingOptions.SystemClock">
            <summary>
            For testing purposes only.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.ResponseCaching.StreamUtilities.BodySegmentSize">
            <summary>
            The segment size for buffering the response body in bytes. The default is set to 80 KB (81920 Bytes) to avoid allocations on the LOH.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.ResponseCaching.SystemClock">
            <summary>
            Provides access to the normal system clock.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.ResponseCaching.SystemClock.UtcNow">
            <summary>
            Retrieves the current system time in UTC.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.ResponseCachingServicesExtensions">
            <summary>
            Extension methods for the ResponseCaching middleware.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.ResponseCachingServicesExtensions.AddResponseCaching(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Add response caching services.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> for adding services.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.ResponseCachingServicesExtensions.AddResponseCaching(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Microsoft.AspNetCore.ResponseCaching.ResponseCachingOptions})">
            <summary>
            Add response caching services and configure the related options.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> for adding services.</param>
            <param name="configureOptions">A delegate to configure the <see cref="T:Microsoft.AspNetCore.ResponseCaching.ResponseCachingOptions"/>.</param>
            <returns></returns>
        </member>
    </members>
</doc>
