﻿using Coldairarrow.Entity.S_School;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.S_School
{
    public interface IS_VideoBusiness
    {
        Task<PageResult<S_Video>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<S_Video> GetTheDataAsync(string id);
        Task AddDataAsync(S_Video data);
        Task UpdateDataAsync(S_Video data);
        Task DeleteDataAsync(List<string> ids);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
    }
}