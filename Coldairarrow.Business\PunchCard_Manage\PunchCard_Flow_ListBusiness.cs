﻿using Coldairarrow.Entity.PunchCard_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System;

namespace Coldairarrow.Business.PunchCard_Manage
{
    public class PunchCard_Flow_ListBusiness : BaseBusiness<PunchCard_Flow_List>, IPunchCard_Flow_ListBusiness, ITransientDependency
    {
        public PunchCard_Flow_ListBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<PunchCard_Flow_ListDTO>> GetDataListAsync(PageInput<PunchCardModel> input)
        {
            var sqlStr = @"
                select P.F_Id as  F_WFId ,P.*,F.NameUser as UserName,P2.F_Type ,P2.F_TimeType,P.F_StartTime as F_Time,P2.F_TimeShort
                from PunchCard_Flow_List P
                LEFT JOIN[dbo].[PunchCard_Info] P2 ON P2.F_WFId = P.F_Id
                LEFT JOIN[dbo].[HR_FormalEmployees] F ON P.F_UserId = F.F_Id
                where 1=1 
               ";
            var q = this.Db.GetListBySql<PunchCard_Flow_ListDTO>(sqlStr).AsQueryable();
            var where = LinqHelper.True<PunchCard_Flow_ListDTO>();
            var search = input.Search;
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<PunchCard_Flow_ListDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            if (input.Search.F_Time.HasValue)
            {
                q = q.Where(x => x.F_StartTime.Value.ToString("yyyy-MM-dd") == input.Search.F_Time.Value.ToString("yyyy-MM-dd"));
            }
            if (!search.UserId.IsNullOrEmpty())
            {
                q = q.Where(x => x.F_UserId == search.UserId);
            }
            //获取本月的数据
            if (search.isMonth.HasValue && search.isMonth == YesOrNo.Yes)
            {
                q = q.Where(x => x.F_StartTime.HasValue && x.F_StartTime.Value.ToString("yyyy-MM") == DateTime.Now.ToString("yyyy-MM"));
            }
            var data = q.Where(where);
            var list = data
              .OrderBy($@"{input.SortField} {input.SortType}")
              .Skip((input.PageIndex - 1) * input.PageRows)
              .Take(input.PageRows)
              .ToList();
            return new PageResult<PunchCard_Flow_ListDTO> { Data = list, Total = data.Count() };
        }

        public async Task<PunchCard_Flow_List> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(PunchCard_Flow_List data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(PunchCard_Flow_List data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<PunchCard_Flow_List>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<PunchCard_Flow_List, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<PunchCard_Flow_List, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}