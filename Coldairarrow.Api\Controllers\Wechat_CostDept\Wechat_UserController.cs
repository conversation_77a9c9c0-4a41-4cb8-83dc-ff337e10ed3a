﻿using Coldairarrow.Business.Wechat_CostDept;
using Coldairarrow.Entity.Wechat_CostDept;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Wechat_CostDept
{
    [Route("/Wechat_CostDept/[controller]/[action]")]
    public class Wechat_UserController : BaseApiController
    {
        #region DI

        public Wechat_UserController(IWechat_UserBusiness wechat_UserBus)
        {
            _wechat_UserBus = wechat_UserBus;
        }

        IWechat_UserBusiness _wechat_UserBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Wechat_User>> GetDataList(PageInput<UserInputDTO> input)
        {
            return await _wechat_UserBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Wechat_User> GetTheData(IdInputDTO input)
        {
            return await _wechat_UserBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Wechat_User data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _wechat_UserBus.AddDataAsync(data);
            }
            else
            {
                await _wechat_UserBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _wechat_UserBus.DeleteDataAsync(ids);
        }

        #endregion
        #region 二次开发
        //获取openId
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetOpenid(string code)
        {
            var a = Request.Headers;
            string appid = "wx896f32ce29d837df";
            string secret = "d0e13eaa27c8819f122c050e0b1f255b";
            string grant_type = "authorization_code";
            using (var httpClient = new HttpClient())
            {
                //post
                var url = new System.Uri("https://api.weixin.qq.com/sns/jscode2session");
                var body = new FormUrlEncodedContent(new Dictionary<string, string>
                {
                    { "appid", appid},
                    { "secret", secret },
                    { "js_code", code},
                    { "grant_type",grant_type}

                });
                // response
                var response = httpClient.PostAsync(url, body).Result;
                var data = response.Content.ReadAsStringAsync().Result;
                JObject jo = (JObject)JsonConvert.DeserializeObject(data);
                var pm = new
                {
                    openid = jo["openid"].ToString(),
                    session_key = jo["session_key"].ToString()
                };
                return Success(pm);
            }
        }

        [NoCheckJWT]
        [HttpPost]
        public AjaxResult UpdataUserInfo()
        {
            try
            {
                string id = HttpContext.Request.Form["id"].ToString();
                string nickName = HttpContext.Request.Form["nickName"].ToString();
                string city = HttpContext.Request.Form["city"].ToString();
                string province = HttpContext.Request.Form["province"].ToString();
                string country = HttpContext.Request.Form["country"].ToString();
                string avatarUrl = HttpContext.Request.Form["avatarUrl"].ToString();
                int gender = HttpContext.Request.Form["gender"].ToString().ToInt();
                var user = _wechat_UserBus.GetTheDataAsync(id).Result;
                if (user != null)
                {
                    user.W_Name = nickName;
                    user.U_City = city;
                    user.U_Province = province;
                    user.U_Country = country;
                    user.U_Gender = gender;
                    user.U_Icon = avatarUrl;
                    _wechat_UserBus.UpdateDataAsync(user).Wait();
                    return Success(user);
                }
                else
                {
                    return Error("无此用户");
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        //根据openId查询用户信息
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetUserInfoById(string id)
        {
            try
            {
                var user = _wechat_UserBus.GetTheDataByOpenId(id);
                if (user != null)
                {
                    return Success(user);
                }
                else
                {
                    var newUser = new Wechat_User();
                    newUser.W_OpenId = id;
                    newUser.W_UserType = 0;
                    _wechat_UserBus.AddDataAsync(newUser).Wait();
                    return Success(newUser);
                }
                
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        //登录注册接口
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult registerUser(string openId,int type, string name, string mobile, string post,string company,int place,string contractName,string contractGuid,string contractNumber)
        {
            try
            {
                var user = _wechat_UserBus.GetTheDataByOpenId(openId);
                user.W_UserType = type;
                user.W_Name = name;
                user.W_Phone = mobile;
                user.W_Post = post;
                user.W_Company = company;
                user.W_Place = place;
                user.W_CompanyName = contractName;
                user.W_CompanyId = contractGuid;
                user.W_ContractNum = contractNumber;
                _wechat_UserBus.UpdateDataAsync(user).Wait();
                return Success(user);

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        //获取合同信息接口
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult getProviderName(string index)
        {
            try
            {
                var result = _wechat_UserBus.GetProviderName(index);
                JObject jo1 = (JObject)JsonConvert.DeserializeObject(result);//转换为json对象
                var company = jo1["RetData"].FirstOrDefault();
                var res = new {
                    contractName = "",
                    yfprovidername = "",
                    contractGuid = "",
                    type = 0,
                    projectNameList = ""
                };
                if (company!=null)
                {
                    //为空则非合作
                    //JObject jo2 = (JObject)JsonConvert.DeserializeObject(company.ToString());
                    var contractName = company["ContractName"].ToString();
                    var yfprovidername = company["YfProviderName"].ToString();
                    var contractGuid = company["ContractGUID"].ToString();
                    var projectNameList = company["ProjectNameList"].ToString();
                    res = new
                    {
                        contractName = contractName,
                        yfprovidername = yfprovidername,
                        contractGuid = contractGuid,
                        type = 1,
                        projectNameList = projectNameList
                    };
                }
                else
                {
                    //不为空在合作
                     res = new
                    {
                        contractName = "",
                        yfprovidername = "",
                        contractGuid = "",
                        type = 2,
                        projectNameList=""
                     };
                }
                return Success(res);

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 解析微信手机号接口
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult AESDecrypt()
        {
            try
            {
                string text = HttpContext.Request.Form["text"].ToString();
                string sessionKey = HttpContext.Request.Form["sessionKey"].ToString();
                string iv = HttpContext.Request.Form["iv"].ToString();
                if (sessionKey.IsNullOrEmpty())
                {
                    return Error("登录状态失效，请点击右上角重新进入");
                }
                //16进制数据转换成byte
                byte[] encryptedData = Convert.FromBase64String(text);  // strToToHexByte(text);
                RijndaelManaged rijndaelCipher = new RijndaelManaged();
                rijndaelCipher.Key = Convert.FromBase64String(sessionKey); // Encoding.UTF8.GetBytes(AesKey);
                rijndaelCipher.IV = Convert.FromBase64String(iv);// Encoding.UTF8.GetBytes(AesIV);
                rijndaelCipher.Mode = CipherMode.CBC;
                rijndaelCipher.Padding = PaddingMode.PKCS7;
                ICryptoTransform transform = rijndaelCipher.CreateDecryptor();
                byte[] plainText = transform.TransformFinalBlock(encryptedData, 0, encryptedData.Length);
                string result = Encoding.Default.GetString(plainText);
                return Success(result);
            }
            catch (Exception ex)
            {
                return Error("解码失败，请右上角重新进入小程序");
            }
        }
        #endregion
    }
}