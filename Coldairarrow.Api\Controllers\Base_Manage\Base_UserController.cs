﻿using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Base_Manage
{
    [Route("/Base_Manage/[controller]/[action]")]
    public class Base_UserController : BaseApiController
    {
        #region DI

        public Base_UserController(IBase_UserBusiness userBus)
        {
            _userBus = userBus;
        }

        IBase_UserBusiness _userBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Base_UserDTO>> GetDataList(PageInput<Base_UsersInputDTO> input)
        {
            return await _userBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Base_UserDTO> GetTheData(IdInputDTO input)
        {
            return await _userBus.GetTheDataAsync(input.id) ?? new Base_UserDTO();
        }

        [HttpPost]
        public async Task<List<SelectOption>> GetOptionList(OptionListInputDTO input)
        {
            return await _userBus.GetOptionListAsync(input);
        }

        [HttpPost]
        [NoCheckJWT]
        public async Task HandlerAdUsers()
        {
            var op = GetOperator();

            await _userBus.HandlerAdUsers(op.UserId?? "Admin");
        }
        [HttpPost]
        public AjaxResult GetLoginUser()
        {
            var op = GetOperator();
            return Success(op);
        }

        #endregion

        #region 外部接口
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetUserList()
        {
            var list = _userBus.GetUserList();
            return Success(list);
        }
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult IsExistsPhoneUser()
        {
            string phone = HttpContext.Request.Form["phone"].ToString();
            var base_User = _userBus.GetTheDataByPhone(phone);
            return Success(base_User == null ? false : true);
        }
        [NoCheckJWT]
        [HttpPost]
        ///加密数据
        public async Task<AjaxResult>  EncryptUser()
        {
            await _userBus.EncryptUser();
            return Success();
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(UserEditInputDTO input)
        {
            if (!input.newPwd.IsNullOrEmpty())
                input.Password = input.newPwd.ToMD5String();
            if (!input.Mobile.IsNullOrEmpty()) {
                var mobile = input.Mobile;
                input.Mobile = AESHelper.EncryptString(mobile, AESHelper.AesKey); 
                input.MobileSecurity = mobile.ToMD5String();
            }
            if (input.Id.IsNullOrEmpty())
            {
                InitEntity(input);

                await _userBus.AddDataAsync(input);
            }
            else
            {
                await _userBus.UpdateDataAsync(input);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _userBus.DeleteDataAsync(ids);
        }

        [NoCheckJWT]
        [HttpGet]
        public AjaxResult sendMessage(string mobile,string str)
        {
            var gethtml = SendMessageHelper.SendSMSMsg(mobile, str);
            return Success(gethtml);
        }
        #endregion
    }
}