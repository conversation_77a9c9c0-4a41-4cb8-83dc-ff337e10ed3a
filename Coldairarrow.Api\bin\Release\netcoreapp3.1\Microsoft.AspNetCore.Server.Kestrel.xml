<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Server.Kestrel</name>
    </assembly>
    <members>
        <member name="M:Microsoft.AspNetCore.Hosting.WebHostBuilderKestrelExtensions.UseKestrel(Microsoft.AspNetCore.Hosting.IWebHostBuilder)">
            <summary>
            Specify <PERSON><PERSON><PERSON> as the server to be used by the web host.
            </summary>
            <param name="hostBuilder">
            The Microsoft.AspNetCore.Hosting.IWebHostBuilder to configure.
            </param>
            <returns>
            The Microsoft.AspNetCore.Hosting.IWebHostBuilder.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.WebHostBuilderKestrelExtensions.UseKestrel(Microsoft.AspNetCore.Hosting.IWebHostBuilder,System.Action{Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerOptions})">
            <summary>
            Specify <PERSON><PERSON><PERSON> as the server to be used by the web host.
            </summary>
            <param name="hostBuilder">
            The Microsoft.AspNetCore.Hosting.IWebHostBuilder to configure.
            </param>
            <param name="options">
            A callback to configure Kestrel options.
            </param>
            <returns>
            The Microsoft.AspNetCore.Hosting.IWebHostBuilder.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.WebHostBuilderKestrelExtensions.ConfigureKestrel(Microsoft.AspNetCore.Hosting.IWebHostBuilder,System.Action{Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerOptions})">
            <summary>
            Configures Kestrel options but does not register an IServer. See <see cref="M:Microsoft.AspNetCore.Hosting.WebHostBuilderKestrelExtensions.UseKestrel(Microsoft.AspNetCore.Hosting.IWebHostBuilder)"/>.
            </summary>
            <param name="hostBuilder">
            The Microsoft.AspNetCore.Hosting.IWebHostBuilder to configure.
            </param>
            <param name="options">
            A callback to configure Kestrel options.
            </param>
            <returns>
            The Microsoft.AspNetCore.Hosting.IWebHostBuilder.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.WebHostBuilderKestrelExtensions.UseKestrel(Microsoft.AspNetCore.Hosting.IWebHostBuilder,System.Action{Microsoft.AspNetCore.Hosting.WebHostBuilderContext,Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerOptions})">
            <summary>
            Specify Kestrel as the server to be used by the web host.
            </summary>
            <param name="hostBuilder">
            The Microsoft.AspNetCore.Hosting.IWebHostBuilder to configure.
            </param>
            <param name="configureOptions">A callback to configure Kestrel options.</param>
            <returns>
            The Microsoft.AspNetCore.Hosting.IWebHostBuilder.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.WebHostBuilderKestrelExtensions.ConfigureKestrel(Microsoft.AspNetCore.Hosting.IWebHostBuilder,System.Action{Microsoft.AspNetCore.Hosting.WebHostBuilderContext,Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerOptions})">
            <summary>
            Configures Kestrel options but does not register an IServer. See <see cref="M:Microsoft.AspNetCore.Hosting.WebHostBuilderKestrelExtensions.UseKestrel(Microsoft.AspNetCore.Hosting.IWebHostBuilder)"/>.
            </summary>
            <param name="hostBuilder">
            The Microsoft.AspNetCore.Hosting.IWebHostBuilder to configure.
            </param>
            <param name="configureOptions">A callback to configure Kestrel options.</param>
            <returns>
            The Microsoft.AspNetCore.Hosting.IWebHostBuilder.
            </returns>
        </member>
    </members>
</doc>
