﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.IBusiness;
using System.Collections;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using System.Security.Principal;
using Coldairarrow.Util.UEditor;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_FeeUserInfoBusiness : BaseBusiness<HR_FeeUserInfo>, IHR_FeeUserInfoBusiness, ITransientDependency
    {
        public HR_FeeUserInfoBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_FeeUserInfo>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_FeeUserInfo>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_FeeUserInfo, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            var result = await q.Where(where).GetPageResultAsync(input);
           var users = this.Db.GetIQueryable<Base_User>().ToList();
            result.Data.ForEach((item) =>
            {
                item.F_IdCard = item.F_IdCardStr;
                item.F_BankCard = item.F_BankCardStr;
                item.phone=users.FirstOrDefault(x => item.F_UserName == x.RealName && !x.MobileStr.IsNullOrWhiteSpace())?.MobileStr;
            });
            return result;
        }

        public async Task<HR_FeeUserInfo> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        public async Task<HR_FeeUserInfo> GetTheDataByUserAsync(string userId)
        {
            return await GetIQueryable().FirstOrDefaultAsync(x => x.F_UserId == userId);
        }
        public async Task AddDataAsync(HR_FeeUserInfo data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_FeeUserInfo data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_FeeUserInfo>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_FeeUserInfo, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_FeeUserInfo, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        /// <summary>
        /// 导入并保存数据
        /// </summary>
        /// <param name="physicPath">物理路径</param>
        /// <returns></returns>
        [Transactional]
        public async Task<AjaxResult<DataTable>> ImportSaveData(string physicPath, IOperator op)
        {
            AjaxResult<DataTable> ajaxResult = new AjaxResult<DataTable>();
            Hashtable ht = new Hashtable();
            ht["F_Company"] = "所属公司";
            ht["F_UserName"] = "姓名";
            ht["F_DeptName"] = "部门";
            var importList = new ExcelHelper<HR_FeeUserInfo>().ExcelImport(ht, physicPath);
            if (importList == null || importList.Count == 0)
            {
                ajaxResult.Success = false;
                ajaxResult.Msg = "上传数据错误或不能为空";
            }
            else
            {
                var userEntity = await Db.GetIQueryable<HR_FormalEmployees>().ToListAsync();
                var base_Users = await Db.GetIQueryable<Base_User>().ToListAsync();
                var feeUserInfos = await GetIQueryable().ToListAsync();
                var addData = new List<HR_FeeUserInfo>();
                var updateData = new List<HR_FeeUserInfo>();
                var updateFData = new List<HR_FormalEmployees>();
                importList = importList.Where(x => !x.F_UserName.IsNullOrWhiteSpace()).ToList();
                importList.ForEach(item =>
                {
                    if (item.F_Company.Contains("招商置地"))
                    {
                        item.F_Company = "重庆招商置地开发有限公司";
                    }
                    else if (item.F_Company.Contains("怡置招商"))
                    {
                        item.F_Company = "重庆怡置招商房地产开发有限公司";
                    }
                    else if (item.F_Company.Contains("瀚置招商"))
                    {
                        item.F_Company = "重庆瀚置招商房地产开发有限公司";
                    }
                    else if (item.F_Company.Contains("怡置商业"))
                    {
                        item.F_Company = "重庆怡置商业管理有限公司";
                    }
                    //查找相关id
                    var base_User = base_Users.Where(x => x.RealName == item.F_UserName.Trim()).OrderBy(x => x.CreateTime).FirstOrDefault();
                    if (base_User != null)
                    {
                        item.F_UserCode = !string.IsNullOrWhiteSpace(base_User.UserName) ? base_User.UserName.Replace("@cqlandmark.com", "") : "";
                        item.F_UserId = base_User.Id?.Trim();
                        //var hR_Formal = userEntity.Find(x => x.NameUser == item.F_UserName.Trim());
                        //if (hR_Formal != null)
                        //{
                        //    item.F_IdCard = hR_Formal.IdCardNumberStr;
                        //    item.F_BankCard = AESHelper.EncryptString(item.F_BankCard, AESHelper.AesKey);
                        //    hR_Formal.CurrentBankCard = item.F_BankCard;
                        //    UpdateEntity(hR_Formal, op);
                        //    updateFData.Add(hR_Formal);
                        //}
                    }
                    var feeUserInfo = feeUserInfos.Find(x => x.F_UserName == item.F_UserName);
                    if (feeUserInfo != null)
                    {
                        feeUserInfo.F_Company = item.F_Company;
                        feeUserInfo.F_UserName = item.F_UserName;
                        feeUserInfo.F_DeptName = item.F_DeptName;
                        feeUserInfo.F_UserCode = item.F_UserCode;
                        feeUserInfo.F_UserId = item.F_UserId;
                        UpdateEntity(feeUserInfo, op);
                        updateData.Add(feeUserInfo);
                    }
                    else
                    {
                        InitEntity(item, op);
                        addData.Add(item);
                    }

                });
                //if (updateFData.Count > 0)
                //{
                //    await this.Db.UpdateAsync(updateFData);
                //}
                if (updateData.Count > 0)
                {
                     this.Db.Update(updateData);
                }
                //if (addData.Count > 0)
                //{
                //    this.Db.BulkInsert(addData);
                //}
            }
            return ajaxResult;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}