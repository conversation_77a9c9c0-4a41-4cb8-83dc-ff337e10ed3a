﻿using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.Tasks;

namespace Coldairarrow.Business.AOP
{
    public class FlowCallBackLogAttribute : FlowWriteDataLogAttribute
    {
        public FlowCallBackLogAttribute(UserLogType logType, string dataName)
       : base(logType, dataName)
        {
        }
        public override async Task After(IAOPContext context)
        {
            var op = context.ServiceProvider.GetService<IOperator>();
            var obj = context.Arguments[0];
                
            op.WriteUserLog(_logType, $"添加{_dataName}:{obj.GetPropertyValue("id")?.ToString()}修改状态{obj.GetPropertyValue("status")?.ToString()}");

            await Task.CompletedTask;
        }
    }
}
