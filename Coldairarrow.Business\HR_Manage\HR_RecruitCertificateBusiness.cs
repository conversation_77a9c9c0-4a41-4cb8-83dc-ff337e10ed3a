﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_RecruitCertificateBusiness : BaseBusiness<HR_RecruitCertificate>, IHR_RecruitCertificateBusiness, ITransientDependency
    {
        public HR_RecruitCertificateBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_RecruitCertificate>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RecruitCertificate>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RecruitCertificate, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_RecruitCertificate> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_RecruitCertificate data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_RecruitCertificate data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RecruitCertificate>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RecruitCertificate, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_RecruitCertificate, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        public int AddData(HR_RecruitCertificate data)
        {
            return Insert(data);
        }

        public int UpdateData(HR_RecruitCertificate data)
        {
            return Update(data);
        }

        public int DeleteData(HR_RecruitCertificate data)
        {
            return Delete(data);
        }

        public int UpdateListData(List<HR_RecruitCertificate> data)
        {
            return Update(data);
        }

        public void AddListData(List<HR_RecruitCertificate> data)
        {
             BulkInsert(data);
        }
        #endregion

        #region 私有成员

        #endregion
    }
}