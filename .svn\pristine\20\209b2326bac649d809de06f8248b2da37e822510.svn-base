﻿using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;

namespace Coldairarrow.Api.Controllers.HR_Manage
{
    [Route("/HR_Manage/[controller]/[action]")]
    public class HR_ResumeController : BaseApiController
    {
        #region DI
        private static IHostingEnvironment _hostingEnvironment;
             private static IConfiguration _configuration;
        public HR_ResumeController(IHR_ResumeBusiness hR_ResumeBus, IConfiguration configuration, IHostingEnvironment hostingEnvironment)
        {
            _hR_ResumeBus = hR_ResumeBus;
            _configuration = configuration;
            _hostingEnvironment = hostingEnvironment;
        }

        IHR_ResumeBusiness _hR_ResumeBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_Resume>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_ResumeBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_Resume> GetTheData(IdInputDTO input)
        {
            return await _hR_ResumeBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_Resume data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_ResumeBus.AddDataAsync(data);
            }
            else
            {
                await _hR_ResumeBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_ResumeBus.DeleteDataAsync(ids);
        }

        #endregion
        

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_ResumeBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<HR_Resume>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _hR_ResumeBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion

        #region 二次开发
        /// <summary>
        /// 微信小程序上传图片
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult SingleFileUpload()
        {
           var formFile = Request.Form.Files["file"];//获取请求发送过来的文件

            var userId = HttpContext.Request.Form["user"].ToString();
            if (string.IsNullOrEmpty(userId))
            {
                userId = Guid.NewGuid().ToString("N");
            }
            var currentDate = DateTime.Now;
            var webRootPath = _hostingEnvironment.WebRootPath;//>>>相当于HttpContext.Current.Server.MapPath("") 

            try
            {
                var filePath = $"/UploadFile/{currentDate:yyyyMMdd}/";

                //创建每日存储文件夹
                if (!Directory.Exists(webRootPath + filePath))
                {
                    Directory.CreateDirectory(webRootPath + filePath);
                }

                if (formFile != null)
                {
                    //文件后缀
                    var fileExtension = Path.GetExtension(formFile.FileName);//获取文件格式，拓展名
                    if (fileExtension != ".pdf" && fileExtension != ".jpg" && fileExtension != ".png" && fileExtension != ".doc" && fileExtension != ".docx")
                    {
                        return Error("只支持上传pdf、word、图片文件");
                    }
                    //判断文件大小
                    var fileSize = formFile.Length;

                    if (fileSize > 1024 * 1024 * 10) //10M TODO:(1mb=1024X1024b)
                    {
                        //return new JsonResult(new { isSuccess = false, resultMsg = "上传的文件不能大于10M" });
                        return Error("上传的文件不能大于10M");
                    }

                    //保存的文件名称(以名称和保存时间命名)
                    var saveName = userId + "_" + currentDate.ToString("HHmmss") + fileExtension;

                    //文件保存
                    using (var fs = System.IO.File.Create(webRootPath + filePath + saveName))
                    {
                        formFile.CopyTo(fs);
                        fs.Flush();
                    }

                    //完整的文件路径
                    var completeFilePath = Path.Combine(filePath, saveName);

                    //return new JsonResult(new { isSuccess = true, returnMsg = "上传成功", completeFilePath = completeFilePath });
                    return Success(completeFilePath);
                }
                else
                {
                    //return new JsonResult(new { isSuccess = false, resultMsg = "上传失败，未检测上传的文件信息~" });
                    return Error("上传失败，未检测上传的文件信息~");
                }

            }
            catch (Exception ex)
            {
                //return new JsonResult(new { isSuccess = false, resultMsg = "文件保存失败，异常信息为：" + ex.Message });
                return Error("文件保存失败，异常信息为：" + ex.Message);
            }
        }


        /// <summary>
        /// 存储提交的简历
        /// </summary>
        /// <param name="openId"></param>
        /// <param name="recruitId"></param>
        /// <param name="fileName"></param>
        /// <param name="fileUrl"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult UploadFile(string openId, string recruitId, string fileName, string fileUrl)
        {
            try
            {
                string url = $"{_configuration["WebRootUrl"]}{fileUrl}";
                HR_Resume data = new HR_Resume
                {
                    F_OpenId = openId,
                    F_RecruitId = recruitId,
                    F_Id = IdHelper.GetId(),
                    F_CreateDate = DateTime.Now,
                    F_Url = url,
                    F_Name = fileName

                };
                _hR_ResumeBus.AddDataAsync(data).Wait();
                return Success(data.F_Id);
            }
            catch (Exception ex)
            {
                return Error("失败:" + ex.Message);
            }
        }

        #endregion
    }
}