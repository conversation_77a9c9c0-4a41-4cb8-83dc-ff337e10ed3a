﻿<template>
  <a-card :bordered="false">
    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="hanldleAdd()">新建</a-button>
      <a-button type="primary" icon="minus" @click="handleDelete(selectedRowKeys)" :disabled="!hasSelected()"
        :loading="loading">删除</a-button>
      <a-button type="primary" icon="import" @click="ImportExcel()">导入Excel</a-button>
      <a-button type="primary" icon="redo" @click="exportExcel()">导出Excel</a-button>
      <a-button type="primary" icon="redo" @click="getDataList()">刷新</a-button>
    </div>

    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="10">
          <a-col :md="4" :sm="24">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="queryParam.condition">
                <a-select-option key="F_UserName">人员名称</a-select-option>
                <a-select-option key="F_Company">所属公司</a-select-option>
                <a-select-option key="F_BankCard">银行卡号</a-select-option>
                <a-select-option key="F_IdCard">身份证</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item>
              <a-input v-model="queryParam.keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-button type="primary" @click="() => {this.pagination.current = 1; this.getDataList()}">查询</a-button>
            <a-button style="margin-left: 8px" @click="() => (queryParam = {})">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table" :columns="columns" :rowKey="row => row.F_Id" :dataSource="data" :pagination="pagination"
      :loading="loading" @change="handleTableChange"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true" size="small">
      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record.F_Id)">编辑</a>
          <a-divider type="vertical" />
          <a @click="handleDelete([record.F_Id])">删除</a>
        </template>
      </span>
    </a-table>

    <edit-form ref="editForm" :parentObj="this"></edit-form>
    <upload ref="tempUpload" :parentObj="this" @handleUpload="handleUpload"></upload>
  </a-card>
</template>

<script>
import EditForm from './EditForm'
import upload from '@/components/TemplateUpload/geUpload'
import { downLoadFile } from '@/utils/plugin/axios-plugin.js'
import { operateFile } from '@/utils/tools.js'
const columns = [
  { title: '员工编号', dataIndex: 'F_UserCode', width: '10%' },
  { title: '人员名称', dataIndex: 'F_UserName', width: '10%' },
  { title: '所属公司', dataIndex: 'F_Company', width: '15%' },
  { title: '银行卡号', dataIndex: 'F_BankCard', width: '15%' },
  { title: '身份证', dataIndex: 'F_IdCard', width: '15%' },
  { title: '操作', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
]

export default {
  components: {
    EditForm,
    upload
  },
  mounted () {
    this.getDataList()
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 10,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      filters: {},
      sorter: { field: 'F_Id', order: 'asc' },
      loading: false,
      columns,
      queryParam: {},
      selectedRowKeys: []
    }
  },
  methods: {
    handleTableChange (pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.filters = { ...filters }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    exportExcel () {
      var that = this
      this.loading = true
      const data = {
        PageIndex: this.pagination.current,
        PageRows: this.pagination.pageSize,
        SortField: this.sorter.field || 'F_CreateDate',
        SortType: this.sorter.order || 'desc',
        Search: this.queryParam,
        ...this.filters
      }
      const url = '/HR_Manage/HR_FeeUserInfo/ExcelDownload'
      downLoadFile(
        url,
        data,
        function (res) {
          console.log(res)
          that.loading = false
          if (res) {
            operateFile(res, '员工工资卡信息明细')
          } else {
            console.log('失败')
          }
        },
        function (err) {
          console.log(err)
          that.loading = false
        }
      )

    },
    handleUpload (res) {
      console.log(res)
      if (res.Success) {
        this.getDataList()
      } else {
        this.$message.info(res.Msg)
      }
    },
    ImportExcel () {
      const downloadUrl = '/HR_Manage/HR_FeeUserInfo/DownloadTemplate'
      const uploadUrl = '/HR_Manage/HR_FeeUserInfo/UploadFileByForm'
      this.$refs.tempUpload.openForm('', '通讯费员工工资卡号导入', downloadUrl, uploadUrl)
    },
    getDataList () {
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/HR_Manage/HR_FeeUserInfo/GetDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'F_Id',
          SortType: this.sorter.order,
          Search: this.queryParam,
          ...this.filters
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange (selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    hasSelected () {
      return this.selectedRowKeys.length > 0
    },
    hanldleAdd () {
      this.$refs.editForm.openForm()
    },
    handleEdit (id) {
      this.$refs.editForm.openForm(id)
    },
    handleDelete (ids) {
      var thisObj = this
      this.$confirm({
        title: '确认删除吗?',
        onOk () {
          return new Promise((resolve, reject) => {
            thisObj.$http.post('/HR_Manage/HR_FeeUserInfo/DeleteData', ids).then(resJson => {
              resolve()

              if (resJson.Success) {
                thisObj.$message.success('操作成功!')

                thisObj.getDataList()
              } else {
                thisObj.$message.error(resJson.Msg)
              }
            })
          })
        }
      })
    }
  }
}
</script>