﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using System.Drawing;
using System.Text.RegularExpressions;
using System.Reflection;
using Coldairarrow.Entity.ERP_CRM;
using Dynamitey.DynamicObjects;

namespace Coldairarrow.Api.Word
{

    public class s_ContractContainer
    {
        public s_ContractContainer()
        {
        }

        /// <summary>
        /// 生成草签合同
        /// </summary>
        /// <param name="list">List<InitiateContract></param>
        /// <param name="templatePath">模板地址</param>
        /// <param name="filePath">生成文件的地址</param>
        /// <param name="targetPath">生成文件的地址</param>
        /// <returns>生成的地址</returns>
        public string AddInitiateContract(List<InitiateContract> list, string templatePath)
        {
            //1、把系统的信息读取出来；
            var obj = list.FirstOrDefault();
            if (obj == null)
            {
                return "";
            }
            //2、将上面的模板文件读取出来（使用Microsoft.Office.Interop.Word），再将这些信息放置在文件的对应的【标签】中，另存为新的文件；
            #region 
            Dictionary<string, string> books = new Dictionary<string, string>();
            books.Add("disAgree", obj.disAgree);
            books.Add("ApplyDay", obj.ApplyDay);
            books.Add("PaymentTerm", obj.PaymentTerm);
            books.Add("Penalty", obj.Penalty);
            books.Add("Submission", obj.Submission);
            books.Add("Address", obj.Address);
            books.Add("BusinessNo", obj.BusinessNo);
            books.Add("CardID", obj.CardID);
            books.Add("CardType", obj.CardType);
            books.Add("CertificateNo", obj.CertificateNo);
            books.Add("ContactNumber", obj.ContactNumber);
            books.Add("contractNo", obj.contractNo);
            books.Add("cstname", obj.cstname);
            books.Add("EstateCertificate", obj.EstateCertificate);
            books.Add("HandoverDate", obj.HandoverDate);
            books.Add("HtTnPrice", obj.HtTnPrice + "");
            books.Add("HtTotal", obj.HtTotal + "");
            books.Add("LandArea", obj.LandArea + "");
            books.Add("Located", obj.Located);
            books.Add("ManyTime", obj.ManyTime + "");
            books.Add("ManyTimeUp", obj.ManyTimeUp);
            books.Add("mortgage", obj.mortgage + "");
            books.Add("mortgageUp", obj.mortgageUp);
            books.Add("nonRenovationBldCjPrice", obj.nonRenovationBldCjPrice + "");
            books.Add("nonRenovationTnCjPrice", obj.nonRenovationTnCjPrice + "");
            books.Add("nonRenovationTotal", obj.nonRenovationTotal + "");
            books.Add("nonRenovationTotalUp", obj.nonRenovationTotalUp);
            books.Add("NonresidentialLife", obj.NonresidentialLife);
            books.Add("Once", obj.Once + "");
            books.Add("OnceUp", obj.OnceUp);
            books.Add("OwnershipType", obj.OwnershipType);
            books.Add("PageHolder", obj.PageHolder);
            books.Add("PageNum", obj.PageNum + "");
            books.Add("payformType", obj.payformType + "");
            books.Add("PresalePermit", obj.PresalePermit);
            books.Add("ProjCompany", obj.ProjCompany);
            if (list.Count > 1)
            {
                books.Add("PropertyRate", string.Join(";", list.Select(p => p.PropertyRate).ToArray()));
            }
            else
            {
                books.Add("PropertyRate", obj.PropertyRate);
            }
            books.Add("Provisionalname", obj.Provisionalname);
            books.Add("Purpose", obj.Purpose);
            books.Add("QualificationNo", obj.QualificationNo);
            books.Add("RegisteredAddress", obj.RegisteredAddress);
            books.Add("RenovationBldCjPrice", obj.RenovationBldCjPrice + "");
            books.Add("RenovationTnCjPrice", obj.RenovationTnCjPrice + "");
            books.Add("RenovationTotal", obj.RenovationTotal + "");
            books.Add("RenovationTotalUp", obj.RenovationTotalUp);
            books.Add("Representative", obj.Representative);
            books.Add("ResidentialLife", obj.ResidentialLife);
            books.Add("SupervisionBank", obj.SupervisionBank);
            books.Add("Zipcode", obj.Zipcode);
            //********新增
            books.Add("Renovation", obj.Renovation);
            books.Add("WaterSupplyDate", obj.WaterSupplyDate);
            books.Add("PowerSupplyDate", obj.PowerSupplyDate);
            books.Add("OnceText", obj.OnceText);
            books.Add("ManyTimeText", obj.ManyTimeText);
            books.Add("mortgageText", obj.mortgageText);
            books.Add("bldarea", obj.bldarea + "");
            books.Add("TnArea", obj.TnArea + "");
            books.Add("Sharearea", obj.Sharearea+"");
            books.Add("PeriodDate", obj.PeriodDate);
            //2021-07-26新增
            books.Add("PeriodNowRoom", obj.PeriodNowRoom);
            books.Add("MobileTel", obj.MobileTel);
            if (obj.PeriodNowRoom == "1")
            {
                books.Add("预售机关", "X");
            }
            else
            {
                books.Add("预售机关", "重庆市住房和城乡建设委员会");
            }
            return WordBase.WordHandle(templatePath, books, null);
            //循环书签，判断书签名称，并为其赋值
            #endregion
        }

        /// <summary>
        /// 生成按揭催办函
        /// </summary>
        /// <param name="obj">MortgageUrgingt</param>
        /// <param name="templatePath">模板地址</param>
        /// <param name="filePath">生成文件的地址</param>
        /// <param name="targetPath">生成文件的地址</param>
        /// <returns>生成的地址</returns>
        public string AddMortgageUrging(MortgageUrgingt obj, string templatePath)
        {
            //1、把系统的信息读取出来；

            //2、将上面的模板文件读取出来（使用Microsoft.Office.Interop.Word），再将这些信息放置在文件的对应的【标签】中，另存为新的文件；
            #region 

            Dictionary<string, string> books = new Dictionary<string, string>();
            books.Add("bgday", obj.bgday); 
            books.Add("bgmonth", obj.bgmonth);
            books.Add("bgyear", obj.bgyear);
            books.Add("Cstname", obj.Cstname);
            books.Add("curday", obj.curday);
            books.Add("curmonth", obj.curmonth);
            books.Add("curyear", obj.curyear);
            books.Add("endday", obj.endday);
            books.Add("endmonth", obj.endmonth);
            books.Add("ProjectCompany", obj.ProjectCompany);
            books.Add("ProjName", obj.ProjName);
            books.Add("ContractNo", obj.ContractNo);
            books.Add("Room_Address", obj.Room_Address);
            books.Add("dueyear", obj.dueyear);
            books.Add("duemonth", obj.duemonth);
            books.Add("dueday", obj.dueday);
            books.Add("limityear", obj.limityear);
            books.Add("limitmonth", obj.limitmonth);
            books.Add("limitday", obj.limitday);
            books.Add("Situation", obj.Situation);
            return WordBase.WordHandle(templatePath, books, null);
            ////循环书签，判断书签名称，并为其赋值
       

            #endregion
            //return filePath;

        }


    }
}
