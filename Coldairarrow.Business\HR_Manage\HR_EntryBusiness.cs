﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System.Linq.Expressions;
using System;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Aliyun.Base.xiaoxizn;
using Newtonsoft.Json;
using System.IO;
using System.Drawing;
using Coldairarrow.Entity.Base_Manage;
using System.Text;
using System.Data.Common;
using Npgsql;
using System.Data.SqlClient;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity.Enum;
using Coldairarrow.Entity;
using Coldairarrow.Entity.HR_Manage.Extensions;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_EntryBusiness : BaseBusiness<HR_Entry>, IHR_EntryBusiness, ITransientDependency
    {
        IHR_PrepareRecruitsBusiness _iHR_Prepare;
        IHR_ContentOperationLogBusiness _ContentOperationLogBusiness;
        public HR_EntryBusiness(IDbAccessor db, IHR_PrepareRecruitsBusiness iHR_Prepare, IHR_ContentOperationLogBusiness ContentOperationLogBusiness)
            : base(db)
        {
            _iHR_Prepare = iHR_Prepare;
            _ContentOperationLogBusiness = ContentOperationLogBusiness;
        }

        #region 外部接口

        public async Task<PageResult<HR_Entry>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_Entry>();
            var search = input.Search;
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere1 = DynamicExpressionParser.ParseLambda<HR_Entry, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere1);
            }
            //选取未入职的数据
            if (!search.BusState.IsNullOrEmpty())
            {
                var newWhere1 = DynamicExpressionParser.ParseLambda<HR_Entry, bool>(
               ParsingConfig.Default, false, $@"F_BusState=@0", 1);
                where = where.And(newWhere1);
            }
            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_Entry> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public HR_InterviewRecordFromDTO GetEntry(string id, string phone)
        {
            var interviewModel = new HR_InterviewRecordFromDTO();
            var temp = Db.GetIQueryable<HR_Entry>().Select(p => new HR_Entry()
            {
                F_Id = p.F_Id,
                F_CreateDate = p.F_CreateDate,
                F_CreateUserId = p.F_CreateUserId,
                F_CreateUserName = p.F_CreateUserName,
                F_ModifyDate = p.F_ModifyDate,
                F_ModifyUserId = p.F_ModifyUserId,
                F_ModifyUserName = p.F_ModifyUserName,
                F_WFId = p.F_WFId,
                F_BusState = p.F_BusState,
                F_WFState = p.F_WFState,
                Remark = p.Remark,
                NameUser = p.NameUser,
                HeadPortrait = p.HeadPortrait,
                EmployeesCode = p.EmployeesCode,
                IdCardNumber = p.IdCardNumber,
                PassportNo = p.PassportNo,
                DirthDate = p.DirthDate,
                EmployRelStatus = p.EmployRelStatus,
                Sex = p.Sex,
                IdCardAddress = p.IdCardAddress,
                ND = p.ND,
                ND2 = p.ND2,
                ND3 = p.ND3,
                ND4 = p.ND4,
                ProfessionalCategory = p.ProfessionalCategory,
                Nationality = p.Nationality,
                NativePlace = p.NativePlace,
                NationalInfo = p.NationalInfo,
                AccountType = p.AccountType,
                RegisteredResidence = p.RegisteredResidence,
                MaritalStatus = p.MaritalStatus,
                PoliticalLandscape = p.PoliticalLandscape,
                FertilityStatus = p.FertilityStatus,
                IsWhetherCar = p.IsWhetherCar,
                LicenseInfo = p.LicenseInfo,
                CurrentBankCard = p.CurrentBankCard,
                OldBankCard = p.OldBankCard,
                EffectiveDate = p.EffectiveDate,
                MobilePhone = p.MobilePhone,
                Email = p.Email,
                OfficePhone = p.OfficePhone,
                HomePhone = p.HomePhone,
                HomeAddress = p.HomeAddress,
                EmergencyContact = p.EmergencyContact,
                EmergencyContactNumber = p.EmergencyContactNumber,
                BaseUserId = p.BaseUserId,
                F_PositionId = p.F_PositionId,
                F_DepartmentId = p.F_DepartmentId,
                F_Rank = p.F_Rank,
                F_CompanyId = p.F_CompanyId,
                F_RecordFormalschooling = p.F_RecordFormalschooling,
                F_Stature = p.F_Stature,
                F_BloodType = p.F_BloodType,
                F_HealthCondition = p.F_HealthCondition,
                F_ForeignLevel = p.F_ForeignLevel,
                F_ProfessionalQualification = p.F_ProfessionalQualification,
                EmergencyContact2 = p.EmergencyContact2,
                EmergencyContactNumber2 = p.EmergencyContactNumber2,
                F_CompanyName = p.F_CompanyName,
                F_WeChatUserId = p.F_WeChatUserId,
                F_Introduce = p.F_Introduce,
                F_FolderId = p.F_FolderId,
                F_Interview = p.F_Interview,
                F_Sources = p.F_Sources,
                F_SourcesPersonnel = p.F_SourcesPersonnel,
                F_InterestsHobbies = p.F_InterestsHobbies,
                F_Weight = p.F_Weight,
                F_FileId = p.F_FileId,
                F_IsFileModel = p.F_IsFileModel,
                //F_FileBase64Img = p.F_FileBase64Img
                F_FileBase64Img = !string.IsNullOrEmpty(p.HeadPortrait) ? "" : ImgHelper.SaveBase64Image(p.F_FileBase64Img, p.F_Id)
            }).FirstOrDefault(i => i.F_WeChatUserId == id);
            if (temp == null)
            {
                //在判断是否有手机号已有简历
                if (!string.IsNullOrEmpty(phone))
                {
                    temp = Db.GetIQueryable<HR_Entry>().FirstOrDefault(i => i.MobilePhoneStr == phone);
                }
                if (temp == null)
                {
                    //还是空则新建
                    var model = new HR_Entry()
                    {
                        F_Id = Guid.NewGuid().ToString("N"),
                        MobilePhone =AESHelper.EncryptString(phone,AESHelper.AesKey),
                        F_WeChatUserId = id,
                        F_CreateDate = DateTime.Now,
                        F_CreateUserId = "小程序",
                        F_CreateUserName = "小程序",
                        F_SeveralInterview = 0
                    };
                    Db.Insert(model);
                    interviewModel.hR_Entry = model;
                    return interviewModel;
                }
                else
                {
                    //否则进行绑定
                    temp.F_WeChatUserId = id;
                    UpdateAsync(temp).Wait();
                }
            }
            else
            {
                //如果头像为空
                if (string.IsNullOrEmpty(temp.HeadPortrait))
                {
                    //则保存并重新赋值
                    temp.HeadPortrait = ImgHelper.SaveBase64Image(temp.F_FileBase64Img, temp.F_Id);
                    Db.Update<HR_Entry>(u => u.F_Id == temp.F_Id, u => u.HeadPortrait = temp.HeadPortrait);
                }
            }

            interviewModel.hR_Entry = temp;

            ////不返回这两个字段 太长了 
            //interviewModel.hR_Entry.F_FileModel = "";
            //interviewModel.hR_Entry.F_FileBase64Img = "";
            interviewModel.hR_RecruitCertificates = Db.GetIQueryable<HR_RecruitCertificate>().Where(i => i.F_UserId == temp.F_Id).ToList();
            interviewModel.hR_RecruitEduBacks = Db.GetIQueryable<HR_RecruitEduBack>().Where(i => i.UserId == temp.F_Id).ToList();
            interviewModel.hR_RecruitWorkExpes = Db.GetIQueryable<HR_RecruitWorkExpe>().Where(i => i.F_UserId == temp.F_Id).ToList();
            Task.WaitAll();
            return interviewModel;
        }


        /// <summary>
        /// 通过应聘者Id获取
        /// </summary>
        /// <param name="id"> 应聘者ID</param>
        /// <returns></returns>
        public PersonModel GetFileModel(string id)
        {
            PersonModel personModel = new PersonModel();
            var hR_Entry = Db.GetIQueryable<HR_Entry>().Where(i => i.F_Id == id).Select(p => p.F_FileModel).FirstOrDefault();
            if (hR_Entry != null)
            {
                //var model = hR_Entry.F_FileModel;
                personModel = !string.IsNullOrEmpty(hR_Entry) ? JsonConvert.DeserializeObject<PersonModel>(hR_Entry) : new PersonModel();
            }
            return personModel;
        }
        /// <summary>
        /// 不带创建的纯查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public HR_InterviewRecordFromDTO GetEntryById(string id)
        {
            var interviewModel = new HR_InterviewRecordFromDTO();
            var temp = Db.GetIQueryable<HR_Entry>().Select(p => new HR_Entry()
            {
                F_Id = p.F_Id,
                F_CreateDate = p.F_CreateDate,
                F_CreateUserId = p.F_CreateUserId,
                F_CreateUserName = p.F_CreateUserName,
                F_ModifyDate = p.F_ModifyDate,
                F_ModifyUserId = p.F_ModifyUserId,
                F_ModifyUserName = p.F_ModifyUserName,
                F_WFId = p.F_WFId,
                F_BusState = p.F_BusState,
                F_WFState = p.F_WFState,
                Remark = p.Remark,
                NameUser = p.NameUser,
                HeadPortrait = p.HeadPortrait,
                EmployeesCode = p.EmployeesCode,
                IdCardNumber = p.IdCardNumber,
                PassportNo = p.PassportNo,
                DirthDate = p.DirthDate,
                EmployRelStatus = p.EmployRelStatus,
                Sex = p.Sex,
                IdCardAddress = p.IdCardAddress,
                ProfessionalCategory = p.ProfessionalCategory,
                Nationality = p.Nationality,
                NativePlace = p.NativePlace,
                NationalInfo = p.NationalInfo,
                AccountType = p.AccountType,
                RegisteredResidence = p.RegisteredResidence,
                MaritalStatus = p.MaritalStatus,
                PoliticalLandscape = p.PoliticalLandscape,
                FertilityStatus = p.FertilityStatus,
                IsWhetherCar = p.IsWhetherCar,
                LicenseInfo = p.LicenseInfo,
                CurrentBankCard = p.CurrentBankCard,
                OldBankCard = p.OldBankCard,
                EffectiveDate = p.EffectiveDate,
                MobilePhone = p.MobilePhone,
                Email = p.Email,
                OfficePhone = p.OfficePhone,
                HomePhone = p.HomePhone,
                HomeAddress = p.HomeAddress,
                EmergencyContact = p.EmergencyContact,
                EmergencyContactNumber = p.EmergencyContactNumber,
                BaseUserId = p.BaseUserId,
                F_PositionId = p.F_PositionId,
                F_DepartmentId = p.F_DepartmentId,
                F_Rank = p.F_Rank,
                F_CompanyId = p.F_CompanyId,
                F_RecordFormalschooling = p.F_RecordFormalschooling,
                F_Stature = p.F_Stature,
                F_BloodType = p.F_BloodType,
                F_HealthCondition = p.F_HealthCondition,
                F_ForeignLevel = p.F_ForeignLevel,
                F_ProfessionalQualification = p.F_ProfessionalQualification,
                EmergencyContact2 = p.EmergencyContact2,
                EmergencyContactNumber2 = p.EmergencyContactNumber2,
                F_CompanyName = p.F_CompanyName,
                F_WeChatUserId = p.F_WeChatUserId,
                F_Introduce = p.F_Introduce,
                F_FolderId = p.F_FolderId,
                F_Interview = p.F_Interview,
                F_Sources = p.F_Sources,
                F_SourcesPersonnel = p.F_SourcesPersonnel,
                F_InterestsHobbies = p.F_InterestsHobbies,
                F_Weight = p.F_Weight,
                F_FileId = p.F_FileId,
                F_IsFileModel = p.F_IsFileModel,
            }).FirstOrDefault(i => i.F_Id == id);
            //var temp = Db.GetIQueryable<HR_Entry>().AsNoTracking().ToList()
            //            .Where(i => i.F_Id == id).FirstOrDefault();
            if (temp == null)
            {
                interviewModel.hR_Entry = temp;
                return interviewModel;
            }
            temp.DecryptFormal();
            interviewModel.hR_Entry = temp;
            interviewModel.hR_RecruitCertificates = Db.GetIQueryable<HR_RecruitCertificate>().Where(i => i.F_UserId == temp.F_Id).OrderByDescending(p => p.F_Time).ToList();
            interviewModel.hR_RecruitEduBacks = Db.GetIQueryable<HR_RecruitEduBack>().Where(i => i.UserId == temp.F_Id).OrderByDescending(p => p.F_StartDate).ToList();
            interviewModel.hR_RecruitWorkExpes = Db.GetIQueryable<HR_RecruitWorkExpe>().Where(i => i.F_UserId == temp.F_Id).OrderByDescending(p => p.F_StartTime).ToList();
            interviewModel.personModel = !string.IsNullOrEmpty(temp.F_FileModel) ? JsonConvert.DeserializeObject<PersonModel>(temp.F_FileModel) : new PersonModel();
            interviewModel.personModel.avatar_data = "";
            interviewModel.hR_Entry.F_FileModel = "";

            //工作年限及曾就职信息
            var workList = interviewModel.hR_RecruitWorkExpes.Where(i => i.F_UserId == id).ToList();
            if (workList.Count > 0)
            {
                var zdate = interviewModel.hR_RecruitWorkExpes.Min(i => i.F_StartTime);
                if (zdate.HasValue)
                {
                    interviewModel.WorkYear = DateTime.Now.Year - zdate.Value.Year;
                }
                var workD = workList.Select(i => i.F_CompanyName).ToList();
                interviewModel.WorkCompany = string.Join(",", workD.ToArray());
            }
            return interviewModel;
        }
        /// <summary>
        /// 通过手机号判断是否面试员（用于小程序）
        /// </summary>
        /// <param name="phone"></param>
        /// <returns></returns>
        public bool IsInterviewers(string phone)
        {
            var temp = Db.GetIQueryable<HR_FormalEmployees>().Where(o => o.MobilePhoneStr == phone).FirstOrDefault();
            if (temp != null)
            {
                return true;
            }
            return false;
        }
        /// <summary>
        /// 通过手机号判断是否面试员（用于小程序）
        /// </summary>
        /// <param name="phone"></param>
        /// <returns></returns>
        public HR_FormalEmployees GetInterviewers(string phone)
        {
            var temp = Db.GetIQueryable<HR_FormalEmployees>().Where(o => o.MobilePhoneStr == phone).FirstOrDefault();
            return temp;
        }

        public async Task AddDataAsync(HR_Entry data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_Entry data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_Entry>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_Entry, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_Entry, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        public int AddData(HR_Entry data)
        {
            return Insert(data);
        }

        public int UpdateData(HR_Entry data)
        {
            return Update(data);
        }

        public int DeleteData(HR_Entry data)
        {
            return Delete(data);
        }

        /// <summary>
        /// 获取面试记录
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public List<ApplicationInfoDTO> GetApplicationInfo(string userId, string postId)
        {
            List<ApplicationInfoDTO> applicationInfos = new List<ApplicationInfoDTO>();
            var entity = Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => i.F_UserId == userId).OrderBy(i => i.F_CreateDate).ToList();
            if (!string.IsNullOrEmpty(postId))
            {
                entity = (from a in entity
                          join b in Db.GetIQueryable<HR_Recruit>() on a.F_RecruitId equals b.F_Id
                          where b.F_Role == postId
                          select a).ToList();
            }
            if (entity.Count > 0)
            {
                var recList = entity.Select(s => s.F_RecruitId).ToList();
                var ids = entity.Select(s => s.F_Id).ToList();
                var zpInfo = Db.GetIQueryable<HR_Recruit>().Where(i => recList.Contains(i.F_Id)).ToList();
                var MSAllModel = Db.GetIQueryable<HR_InterviewPlan>().Where(i => ids.Contains(i.F_RecCanId)).ToList();
                //获取面试信息
                foreach (var item in entity)
                {
                    var recModel = zpInfo.FirstOrDefault(i => i.F_Id == item.F_RecruitId);
                    ApplicationInfoDTO applicationInfo = new ApplicationInfoDTO()
                    {
                        PostName = recModel.F_RecruitName,
                        PostId = recModel.F_Id
                    };
                    List<ApplicationInfo> applications = new List<ApplicationInfo>();
                    //投简历
                    ApplicationInfo TJ = new ApplicationInfo()
                    {
                        Name = "投递简历",
                        State = 1,
                        StartTime = item.F_CreateDate.ToString("yyyy-MM-dd HH:mm:ss"),
                        Describe = "投递成功",
                    };
                    applications.Add(TJ);
                    if (item.F_BusState > 0)
                    {
                        //招聘专员筛选
                        ApplicationInfo SX = new ApplicationInfo()
                        {
                            Name = "招聘专员筛选",
                            StartTime = item.F_ModifyDate.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                            Describe = "通过",
                        };
                        if (item.F_BusState == 1)
                        {
                            SX.State = 1;
                            applications.Add(SX);
                            //开始面试
                            var msModel = MSAllModel.Where(i => i.F_RecCanId == item.F_Id).OrderBy(i => i.F_RoundInterview).ToArray();
                            var planId = msModel.Select(s => s.F_Id).ToList();
                            var mod = Db.GetIQueryable<HR_InterviewEvaluation>().Where(i => planId.Contains(i.F_PlanId) && i.F_ApplicantId == userId && i.F_IsDelete == 0).ToList();
                            var userIds = mod.Select(i => i.F_InterviewerIdAll).Distinct().ToList();
                            List<string> userIdList = new List<string>();
                            foreach (var userInfo in userIds)
                            {
                                var userids = userInfo.Split(new Char[] { ',', '，' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                                userIdList.AddRange(userids);
                            }
                            var userEntity = Db.GetIQueryable<HR_FormalEmployees>().Where(i => userIdList.Contains(i.F_Id)).ToList();
                            foreach (var msitem in msModel)
                            {
                                //todo有问题F_PassingTime
                                if (msitem.F_InterTime == null)
                                {
                                    msitem.F_InterTime = DateTime.Now;
                                }

                                var interModel = mod.Where(i => i.F_PlanId == msitem.F_Id).OrderBy(i => i.F_CreateDate).ToList();
                                foreach (var inter in interModel)
                                {
                                    ApplicationInfo ms = new ApplicationInfo()
                                    {
                                        Name = "第 " + msitem.F_RoundInterview + " 轮面试",
                                        Describe = "通过",
                                        //StartTime = inter.F_ModifyDate.HasValue? inter.F_ModifyDate.Value.ToString("yyyy-MM-dd HH:mm:ss"):"",
                                        StartTime = msitem.F_InterTime.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                                        Evaluation = inter.F_Evaluation,
                                        F_IsGiveUpInterview = item.F_IsGiveUpInterview
                                    };
                                    var userListId = inter.F_InterviewerIdAll.Split(new Char[] { ',', '，' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                                    var listName = userEntity.Where(i => userListId.Contains(i.F_Id)).Select(s => s.NameUser).ToList();
                                    ms.Interviewer = string.Join(",", listName.ToArray());
                                    switch (msitem.F_IsPassingInter)
                                    {
                                        case 0:
                                            if (inter.F_IsThrough == 0)
                                            {
                                                ms.State = 2;
                                                ms.Describe = "未评价";
                                            }
                                            if (inter.F_IsThrough == 1)
                                            {
                                                ms.State = 1;
                                                ms.Describe = "通过";
                                                ms.StartTime = msitem.F_PassingTime.HasValue ? msitem.F_PassingTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : null;
                                            }
                                            if (inter.F_IsThrough == 2)
                                            {
                                                ms.State = 0;
                                                ms.Describe = "未通过";
                                                ms.StartTime = msitem.F_PassingTime.HasValue ? msitem.F_PassingTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : null;
                                            }
                                            break;
                                        case 1:
                                            ms.State = 1;
                                            ms.StartTime = msitem.F_PassingTime.HasValue ? msitem.F_PassingTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : null;
                                            break;
                                        case 2:
                                            if (inter.F_IsThrough == 0)
                                            {
                                                ms.State = 2;
                                                ms.Describe = "未评价";
                                            }
                                            if (inter.F_IsThrough == 1)
                                            {
                                                ms.State = 1;
                                                ms.Describe = "通过";
                                                ms.StartTime = msitem.F_PassingTime.HasValue ? msitem.F_PassingTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : null;
                                            }
                                            if (inter.F_IsThrough == 2)
                                            {
                                                ms.State = 0;
                                                ms.Describe = "未通过";
                                                ms.StartTime = msitem.F_PassingTime.HasValue ? msitem.F_PassingTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : null;
                                            }
                                            break;
                                        case 3:
                                            ms.State = 1;
                                            break;
                                        default:
                                            break;
                                    }

                                    applications.Add(ms);
                                }
                            }

                            //面试是否通过
                            //ApplicationInfo Th = new ApplicationInfo()
                            //{
                            //    Name = "面试通过",
                            //    StartTime = item.F_ThroughTime.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                            //    Describe = "通过",
                            //};
                            //if (item.F_Through == 1)//通过
                            //{
                            //    Th.State = 1;
                            //    Th.Describe = "通过";
                            //}
                            //else if (item.F_Through == 2)//未通过
                            //{
                            //    Th.State = 0;
                            //    Th.Describe = "为通过";
                            //}
                            //applications.Add(Th);
                            if (item.F_HiringTime.HasValue)
                            {
                                ApplicationInfo rz = new ApplicationInfo()
                                {
                                    Name = "聘用信息",
                                    State = 1,
                                    StartTime = item.F_HiringTime.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                                    Describe = "恭喜您,面试成功！",
                                    F_IsGiveUpInterview = item.F_IsGiveUpInterview
                                };
                                applications.Add(rz);
                            }
                            if (item.F_InvitationTime.HasValue)
                            {
                                ApplicationInfo rz = new ApplicationInfo()
                                {
                                    Name = "邀请入职",
                                    State = 1,
                                    StartTime = item.F_InvitationTime.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                                    Describe = "恭喜您,面试成功！",
                                    F_IsInvitation = item.F_IsInvitation,
                                    F_IsGiveUpInterview = item.F_IsGiveUpInterview
                                };
                                applications.Add(rz);
                            }
                        }
                        else
                        {
                            SX.State = 0;
                            SX.Describe = "未通过";
                            SX.F_IsGiveUpInterview = item.F_IsGiveUpInterview;
                            applications.Add(SX);
                        }
                    }
                    applicationInfo.List = applications;
                    applicationInfo.RecruitId = item.F_Id;
                    applicationInfos.Add(applicationInfo);
                }
            }
            return applicationInfos;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="postId"></param>
        /// <returns></returns>
        [DataEditJsonLog(UserLogType.招聘管理, "F_Id", "确认入职")]
        public bool ConfirmEntry(string id, string postId)
        {
            try
            {
                var IsPass = false;
                if (!string.IsNullOrEmpty(id))
                {
                    HR_PrepareRecruits hR_PrepareRecruits = new HR_PrepareRecruits();
                    var strSql = new StringBuilder();
                    strSql.Append(@$" select a.*,a.F_Id as  RecruitId, b.F_ProjectName as F_Project,c.NameUser as F_Name,c.Sex as F_Sex,
            c.IdCardNumber as F_IdNumber,c.MobilePhone as F_Contact,a.F_InvitationTime as F_WorkTime,e.F_Name as F_Jobs,e1.F_Name as F_ReportSuperior,
            d.Name as F_Department,
            e.F_Seat,e.F_Supplies,e.F_ComputerPurch from  [dbo].[HR_RecruitmentCandidates] a
            left join [dbo].[Base_Company] b on a.F_CompanyId =b.F_Id
            left join [dbo].[HR_Entry] c on a.F_UserId=c.F_Id
            left join [dbo].[Base_Post] e on a.F_PostId=e.F_Id
            left join [dbo].[Base_Post] e1 on e.F_ParentId=e1.F_Id
            left join [dbo].[Base_Department] d on e.F_DepartmentId=d.Id 
            where a.F_RecruitId = 
            (select top 1 F_Id  from  [dbo].[HR_Recruit]  where F_Id='{postId}')
            and a.F_UserId='{id}' and a.F_InvitationTime is not null");
                    var hR_Recruitments = this.Db.GetListBySql<HR_PrepareRecruits>(strSql.ToString());
                    if (hR_Recruitments.Count > 0)
                    {
                        var hR_PrepareRecruits1 = hR_Recruitments.FirstOrDefault(i => i.F_WorkTime.HasValue);
                        //查询新员工入职有没有绑定入职
                        var prepareRecruits = this.Db.GetIQueryable<HR_PrepareRecruits>()
                                .Where(i => i.F_UserId == hR_PrepareRecruits1.F_UserId)
                                .FirstOrDefault();
                        if (prepareRecruits == null)
                        {
                            hR_PrepareRecruits1.F_UserId = id;
                            //自动发起新员工入职流程
                            IsPass = _iHR_Prepare.SaveInitiFlow(hR_PrepareRecruits1);
                        }
                        else
                        {
                            IsPass = true;
                        }
                        if (IsPass)
                        {
                            //修改应聘表的状态
                            if (!string.IsNullOrEmpty(id))
                            {
                                var recruitmentCandidates = this.Db.GetIQueryable<HR_RecruitmentCandidates>()
                                                        .FirstOrDefault(i => i.F_Id == hR_PrepareRecruits1.RecruitId);
                                if (recruitmentCandidates != null)
                                {
                                    recruitmentCandidates.F_IsInvitation = 1;
                                    //recruitmentCandidates.F_Label += EnumHelper.GetEnumDescription(RecruitType.TagType.应聘者确认入职);
                                    //recruitmentCandidates.F_LabelOperationTime = DateTime.Now;
                                    ////保存标签
                                    //HR_ContentOperationLog hR_ContentOperation = new HR_ContentOperationLog()
                                    //{
                                    //    F_BusState = (int)ASKBusState.正常,
                                    //    F_UserId = recruitmentCandidates.F_UserId,
                                    //    F_RelationalID = recruitmentCandidates.F_Id,
                                    //    F_RelationalType = (int)RecruitType.RelationalType.正常,
                                    //    F_TagContent = EnumHelper.GetEnumDescription(RecruitType.TagType.应聘者确认入职),
                                    //    F_TagType = (int)RecruitType.TagType.应聘者确认入职
                                    //};
                                    //Task.Run(() =>
                                    //{
                                    //    _ContentOperationLogBusiness.SaveDataAsync(new List<HR_ContentOperationLog> { hR_ContentOperation }).GetAwaiter();
                                    //});
                                    this.Db.Update(recruitmentCandidates);
                                }
                            }
                        }
                    }
                }
                return IsPass;
            }
            catch (Exception ex)
            {
                throw new Exception("发起失败！");
            }

        }

        /// <summary>
        /// 放弃入职
        /// <param name="id">应聘者关系表主键</param>
        /// </summary>
        /// <returns></returns>
        public async Task AbandonEntry(List<string> ids)
        {
            if (ids.Count > 0)
            {
                foreach (var id in ids)
                {
                    var recruitmentEntity = await this.Db.GetEntityAsync<HR_RecruitmentCandidates>(id);
                    if (recruitmentEntity != null)
                    {
                        recruitmentEntity.F_IsGiveUpInterview = (int)RecruitType.IsGiveUpInterviewType.GUcareer;
                        recruitmentEntity.F_Label += EnumHelper.GetEnumDescription(RecruitType.TagType.应聘者放弃入职);
                        recruitmentEntity.F_LabelOperationTime = DateTime.Now;
                        await this.Db.UpdateAsync(recruitmentEntity);
                        //保存标签
                        HR_ContentOperationLog hR_ContentOperation = new HR_ContentOperationLog()
                        {
                            F_BusState = (int)ASKBusState.正常,
                            F_UserId = recruitmentEntity.F_UserId,
                            F_RelationalID = recruitmentEntity.F_Id,
                            F_RelationalType = (int)RecruitType.RelationalType.正常,
                            F_TagContent = EnumHelper.GetEnumDescription(RecruitType.TagType.应聘者放弃入职),
                            F_TagType = (int)RecruitType.TagType.应聘者放弃入职
                        };
                        await _ContentOperationLogBusiness.SaveDataAsync(new List<HR_ContentOperationLog> { hR_ContentOperation });
                    }
                }
            }
        }
        #endregion

        #region 私有成员

        #endregion
    }

}