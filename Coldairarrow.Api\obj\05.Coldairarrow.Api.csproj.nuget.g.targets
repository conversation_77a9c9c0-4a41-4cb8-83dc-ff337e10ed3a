﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.apidescription.server\3.0.0\build\Microsoft.Extensions.ApiDescription.Server.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.apidescription.server\3.0.0\build\Microsoft.Extensions.ApiDescription.Server.targets')" />
    <Import Project="$(NuGetPackageRoot)nswag.aspnetcore\13.7.4\build\NSwag.AspNetCore.targets" Condition="Exists('$(NuGetPackageRoot)nswag.aspnetcore\13.7.4\build\NSwag.AspNetCore.targets')" />
    <Import Project="$(NuGetPackageRoot)ikvm\8.2.0\buildTransitive\netstandard2.0\IKVM.targets" Condition="Exists('$(NuGetPackageRoot)ikvm\8.2.0\buildTransitive\netstandard2.0\IKVM.targets')" />
  </ImportGroup>
</Project>