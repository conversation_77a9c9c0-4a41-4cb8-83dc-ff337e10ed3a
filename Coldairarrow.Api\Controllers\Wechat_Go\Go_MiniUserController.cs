﻿using Coldairarrow.Business.Wechat_Go;
using Coldairarrow.Entity.Wechat_Go;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using System.Net.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Coldairarrow.Business.HR_Manage;

namespace Coldairarrow.Api.Controllers.Wechat_Go
{
    [Route("/Wechat_Go/[controller]/[action]")]
    public class Go_MiniUserController : BaseApiController
    {
        #region DI

        public Go_MiniUserController(IGo_MiniUserBusiness go_MiniUserBus,IHR_EntryBusiness hR_EntryBus)
        {
            _go_MiniUserBus = go_MiniUserBus;
            _hR_EntryBus = hR_EntryBus;
        }

        IGo_MiniUserBusiness _go_MiniUserBus { get; }
        IHR_EntryBusiness _hR_EntryBus { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Go_MiniUser>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _go_MiniUserBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Go_MiniUser> GetTheData(IdInputDTO input)
        {
            return await _go_MiniUserBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Go_MiniUser data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _go_MiniUserBus.AddDataAsync(data);
            }
            else
            {
                await _go_MiniUserBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _go_MiniUserBus.DeleteDataAsync(ids);
        }

        #endregion
        

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _go_MiniUserBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Go_MiniUser>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _go_MiniUserBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion

        #region 二次开发
        //获取openId
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetOpenid()
        {
            try
            {
                string code = HttpContext.Request.Form["code"].ToString();
                if (code.IsNullOrEmpty())
                {
                    return Error("微信服务器波动");
                }
                string appid = "wxec7724d0a3facb8d";
                string secret = "41dd74cac38a77b3ebb2368ee59faba3";
                string grant_type = "authorization_code";
                using (var httpClient = new HttpClient())
                {
                    //post
                    var url = new System.Uri("https://api.weixin.qq.com/sns/jscode2session");
                    var body = new FormUrlEncodedContent(new Dictionary<string, string>
                {
                    { "appid", appid},
                    { "secret", secret },
                    { "js_code", code},
                    { "grant_type",grant_type}

                });
                    // response
                    var response = httpClient.PostAsync(url, body).Result;
                    var data = response.Content.ReadAsStringAsync().Result;
                    JObject jo = (JObject)JsonConvert.DeserializeObject(data);
                    var pm = new
                    {
                        openid = jo["openid"].ToString(),
                        session_key = jo["session_key"].ToString(),
                        unionid = jo["unionid"].ToString()
                    };
                    return Success(pm);
                }
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }

        }


        //根据openId查询用户信息
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetUserInfoById()
        {
            try
            {
                string id = HttpContext.Request.Form["openId"].ToString();
                string unionId = HttpContext.Request.Form["unionId"].ToString();
                var user = _go_MiniUserBus.GetTheDataAsync(id).Result;
                if (user != null)
                {
                    var result = _go_MiniUserBus.GetRecentlyRecord(id);
                    user.U_LastLoginTime = DateTime.Now;
                    _go_MiniUserBus.UpdateDataAsync(user).Wait();
                    if (result)
                    {
                        user.CreatorId =  "123";
                    }
                    return Success(user);
                }
                else
                {
                    var newUser = new Go_MiniUser();
                    newUser.F_Id = id;
                    newUser.U_UnionId = unionId;
                    newUser.U_IsAble = 1;
                    newUser.U_IsRegister = 0;
                    newUser.U_UserType = 0;
                    newUser.U_LastLoginTime = DateTime.Now;
                    newUser.CreateTime = DateTime.Now;
                    _go_MiniUserBus.AddDataAsync(newUser).Wait();
                    return Success(newUser);
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        //通过手机号获取员工信息
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetCompanyUser()
        {
            try
            {
                string mobile = HttpContext.Request.Form["mobile"].ToString();
                var result = _hR_EntryBus.GetInterviewers(mobile);
                return Success(result);

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        //获取登录信息
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetAllUser()
        {
            try
            {
                var result = _go_MiniUserBus.GetAllUser();
                return Success(result);

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }


        //根据openId更新用户手机号
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult UpdataUserPhone()
        {
            try
            {
                string id = HttpContext.Request.Form["id"].ToString();
                string mobile = HttpContext.Request.Form["mobile"].ToString();
                string name = HttpContext.Request.Form["name"].ToString();
                int userType = int.Parse(HttpContext.Request.Form["userType"].ToString());
                var user = _go_MiniUserBus.GetTheDataAsync(id).Result;
                if (user != null)
                {
                    //同时判断身份,
                    //是公司员工
                    user.U_IsRegister = 1;
                    user.U_Mobile = mobile;
                    user.U_RealName = name;
                    user.U_UserType = userType;
                    _go_MiniUserBus.UpdateDataAsync(user).Wait();
                    return Success(user);
                }
                else
                {
                    return Error("无此用户");
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        //根据openId更新用户信息
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult UpdataUserInfo()
        {
            try
            {
                string id = HttpContext.Request.Form["id"].ToString();
                string nickName = HttpContext.Request.Form["nickName"].ToString();
                string language = HttpContext.Request.Form["language"].ToString();
                string city = HttpContext.Request.Form["city"].ToString();
                string province = HttpContext.Request.Form["province"].ToString();
                string country = HttpContext.Request.Form["country"].ToString();
                string avatarUrl = HttpContext.Request.Form["avatarUrl"].ToString();
                int gender = HttpContext.Request.Form["gender"].ToString().ToInt();
                var user = _go_MiniUserBus.GetTheDataAsync(id).Result;
                if (user != null)
                {
                    user.U_NickName = nickName;
                    user.U_Language = language;
                    user.U_City = city;
                    user.U_Province = province;
                    user.U_Country = country;
                    user.U_Gender = gender;
                    user.U_HeadIcon = avatarUrl;
                    _go_MiniUserBus.UpdateDataAsync(user).Wait();
                    return Success(user);
                }
                else
                {
                    return Error("无此用户");
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        #endregion
    }
}