<template>
  <a-select style="width: 210px;" @select="selectFun" v-model="currValue" :disabled="isEdit" :labelCol="{ span: 8 }"
    :wrapperCol="{ span: 14 }">
    <a-select-option v-for="Diction in DictionList" :value="Diction.F_ItemValue" :key="Diction.F_ItemValue">
      {{ Diction.F_ItemName }}
    </a-select-option>
  </a-select>
</template>

<script>
export default {
  props: {
    Name: String,
    Value: String,
    Disabled: Boolean
  },
  data () {
    return {
      DictionList: [],
      currValue: this.value,
      isEdit: this.Disabled
    }
  },
  created () {
    //console.log(this.Disabled)
    this.currValue = this.value
    this.GetDictionaries()
  },
  watch: {
    Value: function (val) {
      this.currValue = val
    },
    currValue: function (val) {
      //console.log(val)
      this.$emit('Value', val)
    },
    Disabled: function (val) {
      this.isEdit = val
    },
  },
  methods: {
    init () {
      this.visible = true
      this.selectUser = []
    },
    //获取数据字典
    GetDictionaries () {
      this.commonApi.dictTypeInfo(this.Name).then(res => {
        this.DictionList = res
        // console.log(this.DictionList)
      })
    },
    openForm (title) {
      this.GetDictionaries()
    },
    selectFun (value) {
      this.$emit('selectedvalue', value)
    }
  }
}
</script>
