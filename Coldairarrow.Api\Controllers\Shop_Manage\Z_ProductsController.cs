﻿using Coldairarrow.Business.Shop_Manage;
using Coldairarrow.Entity.Shop_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Quartz.Util;
using Coldairarrow.Entity.Shop_Manage.Model;
using Coldairarrow.Entity.Base_Manage;
using DocumentFormat.OpenXml.Bibliography;
using Newtonsoft.Json;
using Coldairarrow.IBusiness;
using Coldairarrow.Business.HR_DataDictionaryManage;
using DocumentFormat.OpenXml.Drawing;
using Coldairarrow.Business.Base_Manage;

namespace Coldairarrow.Api.Controllers.Shop_Manage
{
    [Route("/Shop_Manage/[controller]/[action]")]
    public class Z_ProductsController : BaseApiController
    {
        #region DI

        public Z_ProductsController(IZ_ProductsBusiness z_ProductsBus, IOperator @operator, IHR_DataDictionaryDetailsBusiness hR_DataDictionaryDetailsBus, IBase_UserBusiness userBus)
        {
            _z_ProductsBus = z_ProductsBus;
            _operator = @operator;
            _hR_DataDictionaryDetailsBus = hR_DataDictionaryDetailsBus;
            _userBus = userBus;
        }
        IOperator _operator;
        IZ_ProductsBusiness _z_ProductsBus { get; }
        IHR_DataDictionaryDetailsBusiness _hR_DataDictionaryDetailsBus { get; }
        IBase_UserBusiness _userBus { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Z_Products>> GetDataList(PageInput<ProductConditinDto> input)
        {
            return await _z_ProductsBus.GetDataListAsync(input);
        }


        /// <summary>
        /// 商城是否开放
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public bool GetIsOpen(IdInputDTO input)
        {
            var data = _hR_DataDictionaryDetailsBus.PostDetailList("商场开放日期");
            if (data != null && data.Count > 0)
            {
                var now = DateTime.Now;
                var startTime = data.Find(x => x.F_ItemName == "开始日期").F_ItemValue.ToDateTime();
                var endTime = data.Find(x => x.F_ItemName == "结束日期").F_ItemValue.ToDateTime().AddDays(1).AddMinutes(-1);
                var isOpen = endTime >= now && now >= startTime;
                if (!isOpen && !string.IsNullOrWhiteSpace(input.userId))
                {
                    //查询此用户是否开放
                    var user = _userBus.GetBase_User(input.userId);
                    if (user.IsShopUser.HasValue && user.IsShopUser.Value == 1)
                    {
                        if (user.shopEndTime.HasValue && now <= user.shopEndTime.Value.AddDays(1).AddMinutes(-1))
                        {
                            return true;
                        }
                        else
                        {
                            user.IsShopUser = 0;
                            _userBus.UpdateData(user);
                        }

                    }
                }
                return endTime >= now && now >= startTime;
            }
            return false;
        }

        [HttpPost]
        public async Task<Z_Products> GetTheData(IdInputDTO input)
        {
            return await _z_ProductsBus.GetTheDataAsync(input.id);
        }
        /// <summary>
        /// 通过京东链接获取京东商品的详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        [NoCheckJWT]
        public async Task<JD_Result_ZProducts> GetJDProductInfoByUrl(OtherInputDTO input)
        {
            if (!string.IsNullOrWhiteSpace(input.url) && input.url.Contains("item.jd.com"))
            {
                return await _z_ProductsBus.GetJDProductInfoByUrl(input.url);
            }
            else
            {
                throw new Exception("输入链接有误,请重新输入！");
            }
        }

        /// <summary>
        /// 通过京东链接获取京东商品的详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        [NoCheckJWT]
        public async Task JDProductInfoCallBack(JdCallBackDTO input)
        {
            //写入日志
            var log = new Base_UserLog
            {
                Id = IdHelper.GetId(),
                CreateTime = DateTime.Now,
                CreatorId = "admin",
                CreatorRealName = "超级管理员",
                LogContent = $"获取京东链接商品详情-" + input.goodsId,
                JsonContent = JsonConvert.SerializeObject(input),
            };
            if (!input.goodsId.IsNullOrWhiteSpace())
            {
                //保存商品数据
                await _z_ProductsBus.SaveJdProductInfo(input);
            }
            _operator.WriteUserLog(log);
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Z_Products data)
        {
            if (!data.F_JDUrl.IsNullOrEmpty() && data.F_JDId.IsNullOrWhiteSpace())
            {
                //解析链接获取商品id 
                data.F_JDId = System.Text.RegularExpressions.Regex.Replace(data.F_JDUrl, @"[^0-9]+", "");
            }
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _z_ProductsBus.AddDataAsync(data);
            }
            else
            {
                await _z_ProductsBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task SaveData2(Z_Products data)
        {

            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _z_ProductsBus.AddDataAsync2(data);
            }
            else
            {
                await _z_ProductsBus.UpdateDataAsync2(data);
            }
        }

        [HttpPost]
        public async Task UpdateData(Z_Products data)
        {
            await _z_ProductsBus.UpdateDataAsyncById(data);
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _z_ProductsBus.DeleteDataAsync(ids);
        }

        [HttpPost]
        public async Task SaveOpenTime()
        {

        }
        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public async Task<FileContentResult> ExcelDownload(PageInput<ProductConditinDto> input)
        {
            try
            {
                input.PageRows = 10000;
                var pageResult = await _z_ProductsBus.GetDataListAsync(input);
                if (pageResult.Data.Count > 0)
                {
                    pageResult.Data.ForEach((item) =>
                    {
                        if (item.F_Sales != 0)
                        {
                            item.F_SalesPrice = item.F_Sales * item.F_Price;
                        }
                    });

                }
                //取出数据源
                DataTable exportTable = pageResult.Data.ToDataTable();
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                //excelconfig.Title = "人员异动详情";
                //excelconfig.TitleFont = "微软雅黑";
                //excelconfig.TitlePoint = 20;
                excelconfig.FileName = "商品清单明细.xls";
                excelconfig.IsAllSizeColumn = true;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                var Sort = 1;
                excelconfig.ColumnEntity = new List<ColumnModel>
                {
                    new ColumnModel() { Column = "f_name", ExcelColumn = "商品名称", Sort = Sort++ },
                    new ColumnModel() { Column = "f_price", ExcelColumn = "商品单价", Sort = Sort++ },
                    new ColumnModel() { Column = "f_code", ExcelColumn = "商品编码", Sort = Sort++ },
                    new ColumnModel() { Column = "f_typename", ExcelColumn = "商品类型",Sort = Sort++ },
                    new ColumnModel() { Column = "f_sales", ExcelColumn = "商品销量", Sort = Sort++ },
                    new ColumnModel() { Column = "f_salesprice", ExcelColumn = "商品销量价格", Sort = Sort++ },
                };
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = System.IO.Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Z_Products>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _z_ProductsBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}