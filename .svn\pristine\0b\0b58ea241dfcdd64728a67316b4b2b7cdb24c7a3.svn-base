<template>
  <a-modal :title="title" width="60%" :visible="visible" :confirmLoading="loading" :maskClosable="false"
    @ok="handleSubmit" @cancel="
      () => {
        this.visible = false
      }
    ">
    <template slot="footer">
      <a-button key="save" :loading="loading" @click="handleSubmit" icon="edit">
        保存
      </a-button>
      <a-button key="cancel" :loading="loading" @click="
          () => {
            this.visible = false
          }
        " icon="close">
        取消
      </a-button>
    </template>
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="10">
          <a-col :md="6" :sm="24">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="queryParam.condition">
                <a-select-option key="F_Name">姓名</a-select-option>
                <a-select-option key="F_Sex">性别</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item>
              <a-input v-model="queryParam.keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-button type="primary" icon="search" @click="
                () => {
                  this.pagination.current = 1
                  this.getEmpList()
                }
              ">查询</a-button>
            <a-button style="margin-left: 8px" icon="sync" @click="() => (queryParam = {})">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <a-spin :spinning="loading">
      <a-table ref="table" :columns="columns" :rowKey="row => row.F_Id" :dataSource="data" :pagination="pagination"
        :loading="loading" @change="handleTableChange"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true">
      </a-table>
    </a-spin>
  </a-modal>
</template>

<script>
const columns = [
  {
    title: '姓名', dataIndex: 'F_Name', width: 80,
    align: 'center'
  },
  {
    title: '性别', dataIndex: 'F_Sex', width: 80,
    align: 'center'
  },
  {
    title: '项目', dataIndex: 'F_Project', width: 110
  },
  { title: '身份证号', dataIndex: 'F_IdNumber', width: 170 },
  { title: '联系方式', dataIndex: 'F_Contact', width: 130 },
  {
    title: '办公座位', dataIndex: 'F_SeatText', width: 90,
    align: 'center'
  },
  {
    title: '办公用品', dataIndex: 'F_SuppliesText', width: 90,
    align: 'center'
  },
  {
    title: '电脑采购', dataIndex: 'F_ComputerPurchText', width: 90,
    align: 'center'
  },
  {
    title: '账号', dataIndex: 'F_Account', width: 70,
    align: 'center'
  },
]
export default {
  props: {
    //回调方法，返回选中的员工
    callBack: Function,
    //是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    queryParam: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 10,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      sorter: { field: 'F_CreateDate', order: 'desc' },
      columns,
      Param: {},
      visible: false,
      loading: false,
      selectedRowKeys: [],
      selectedUsers: [], //选择用户
      entity: {},
      rules: {},
      title: ''
    }
  },
  created () {
    this.Param = this.queryParam
  },
  methods: {
    init () {
      this.visible = true
      this.selectUser = []
    },
    openForm (title) {
      this.title = title
      this.init()
      //查询员工信息
      this.getEmpList()
    },
    handleTableChange (pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.sorter = { ...sorter }
      this.getEmpList()
    },
    //查询员工信息
    getEmpList () {
      this.selectedRowKeys = []

      this.loading = true
      this.Param.wfState = 0;
      this.$http
        .post('/HR_EmployeeInfoManage/HR_PrepareRecruits/GetDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'F_CreateDate',
          SortType: this.sorter.order,
          Search: this.Param
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          let pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      console.log(selectedRows)
      this.selectedRowKeys = selectedRowKeys
      this.selectedUsers = selectedRows
    },
    handleSubmit () {
      if (this.selectedUsers.length == 0) {
        this.$message.warning('必须选中一行')
        return
      }
      if (!this.multiple && this.selectedUsers.length > 1) {
        this.$message.warning('只能选择一条数据')
        return
      }
      if (this.callBack) {
        this.visible = false
        this.callBack(this.selectedUsers)
      }
    }
  }
}
</script>
