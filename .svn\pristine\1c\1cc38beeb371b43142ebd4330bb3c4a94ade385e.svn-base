﻿using Coldairarrow.Entity.S_School;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.S_School
{
    public class S_CatchPageBusiness : BaseBusiness<S_CatchPage>, IS_CatchPageBusiness, ITransientDependency
    {
        public S_CatchPageBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<S_CatchPage>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<S_CatchPage>();
            var search = input.Search;

            input.SortField = "F_CreateDate";
            input.SortType = "desc";
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<S_CatchPage, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            if (!search.F_CreateUserId.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<S_CatchPage, bool>(
                    ParsingConfig.Default, false, $@"F_CreateUserId = @0", search.F_CreateUserId);
                where = where.And(newWhere);
            }
            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<S_CatchPage> GetTheDataAsync(string id)
        {
            var obj = GetEntity(id);
            obj.F_Hits++;
            var q = from a in this.Db.GetIQueryable<S_CatchPageEx>()
                    where a.F_Id == id
                    select new S_CatchPage
                    {
                        F_Id = obj.F_Id,
                        F_CreateDate = obj.F_CreateDate,
                        F_CreateUserId = obj.F_CreateUserId,
                        F_CreateUserName = obj.F_CreateUserName,
                        F_ModifyDate = obj.F_ModifyDate,
                        F_ModifyUserId = obj.F_ModifyUserId,
                        F_ModifyUserName = obj.F_ModifyUserName,
                        F_WFId = obj.F_WFId,
                        F_BusState = obj.F_BusState,
                        F_WFState = obj.F_WFState,
                        Remark = obj.Remark,
                        F_Title = obj.F_Title,
                        F_ShortTitle = obj.F_ShortTitle,
                        F_Tag = obj.F_Tag,
                        F_Abstract = obj.F_Abstract,
                        F_Classify = obj.F_Classify,
                        F_Category = obj.F_Category,
                        F_Source = obj.F_Source,
                        F_SourceUrl = obj.F_SourceUrl,
                        F_Author = obj.F_Author,
                        F_IsIndex = obj.F_IsIndex,
                        F_IsDelete = obj.F_IsDelete,
                        F_IsAuditing = obj.F_IsAuditing,
                        F_Hits = obj.F_Hits,
                        F_CoverImage = obj.F_CoverImage,
                        F_IsDiscuss = obj.F_IsDiscuss,
                        F_IsHot = obj.F_IsHot,
                        F_FileId = obj.F_FileId,
                        F_FilePath = obj.F_FilePath,
                        F_Content = a.F_Content,
                    };
            await UpdateAsync(obj);
            return await q.FirstAsync();
        }

        public async Task AddDataAsync(S_CatchPage data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(S_CatchPage data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
             this.Db.Delete<S_CatchPageEx>(ids);
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<S_CatchPage>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<S_CatchPage, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<S_CatchPage, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 操作


        public async Task Comment(string id)
        {
            var obj = GetEntity(id);
            obj.F_IsDiscuss++;
            await UpdateAsync(obj);
        }
        public async Task GiveUp(string id)
        {
            var obj = GetEntity(id);
            obj.F_IsHot++;
            await UpdateAsync(obj);
        }


        public async Task Forward(string id,string actiontxt= "Forward")
        {
            var obj = GetEntity(id);
            obj.F_IsHot++;
            await UpdateAsync(obj);
        }


        #endregion

        #region 私有成员

        #endregion
    }
}