﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Memory</name>
  </assembly>
  <members>
    <member name="T:System.Buffers.ArrayBufferWriter`1">
      <summary>Represents a heap-based, array-backed output sink into which <typeparam name="T" /> data can be written.</summary>
      <typeparam name="T">The type of the items in this <see cref="T:System.Buffers.ArrayBufferWriter`1" /> instance.</typeparam>
    </member>
    <member name="M:System.Buffers.ArrayBufferWriter`1.#ctor">
      <summary>Creates an instance of an <see cref="T:System.Buffers.ArrayBufferWriter`1" /> to which data can be written, with the default initial capacity.</summary>
    </member>
    <member name="M:System.Buffers.ArrayBufferWriter`1.#ctor(System.Int32)">
      <summary>Creates an instance of an <see cref="T:System.Buffers.ArrayBufferWriter`1" /> to which data can be written, with a specified initial capacity.</summary>
      <param name="initialCapacity">The minimum capacity with which to initialize the underlying buffer.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="initialCapacity" /> is less than or equal to 0.</exception>
    </member>
    <member name="M:System.Buffers.ArrayBufferWriter`1.Advance(System.Int32)">
      <summary>Notifies the <see cref="T:System.Buffers.IBufferWriter`1" /> that <paramref name="count" /> items were written to the output <see cref="T:System.Span`1" />/<see cref="T:System.Memory`1" /></summary>
      <param name="count">The number of items written.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.InvalidOperationException">The method call attempts to advance past the end of the underlying buffer.</exception>
    </member>
    <member name="P:System.Buffers.ArrayBufferWriter`1.Capacity">
      <summary>Gets the total amount of space within the underlying buffer.</summary>
      <returns>The total capacity of the underlying buffer.</returns>
    </member>
    <member name="M:System.Buffers.ArrayBufferWriter`1.Clear">
      <summary>Clears the data written to the underlying buffer.</summary>
    </member>
    <member name="P:System.Buffers.ArrayBufferWriter`1.FreeCapacity">
      <summary>Gets the amount of available space that can be written to without forcing the underlying buffer to grow.</summary>
      <returns>The space available for writing without forcing the underlying buffer to grow.</returns>
    </member>
    <member name="M:System.Buffers.ArrayBufferWriter`1.GetMemory(System.Int32)">
      <summary>Returns a <see cref="T:System.Memory`1" /> to write to that is at least the length specified by <paramref name="sizeHint" />.</summary>
      <param name="sizeHint">The minimum requested length of the <see cref="T:System.Memory`1" />.</param>
      <returns>A <see cref="T:System.Memory`1" /> whose length is at least <paramref name="sizeHint" />.  If <paramref name="sizeHint" /> is not provided or is equal to 0, some non-empty buffer is returned.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="sizeHint" /> is negative.</exception>
    </member>
    <member name="M:System.Buffers.ArrayBufferWriter`1.GetSpan(System.Int32)">
      <summary>Returns a <see cref="T:System.Span`1" /> to write to that is at least a specified length.</summary>
      <param name="sizeHint">The minimum requested length of the <see cref="T:System.Span`1" />.</param>
      <returns>A span of at least <paramref name="sizeHint" /> in length. If <paramref name="sizeHint" /> is not provided or is equal to 0, some non-empty buffer is returned.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="sizeHint" /> is negative.</exception>
    </member>
    <member name="P:System.Buffers.ArrayBufferWriter`1.WrittenCount">
      <summary>Gets the amount of data written to the underlying buffer.</summary>
      <returns>The amount of data written to the underlying buffer.</returns>
    </member>
    <member name="P:System.Buffers.ArrayBufferWriter`1.WrittenMemory">
      <summary>Gets a <see cref="T:System.ReadOnlyMemory`1" /> that contains the data written to the underlying buffer so far.</summary>
      <returns>The data written to the underlying buffer.</returns>
    </member>
    <member name="P:System.Buffers.ArrayBufferWriter`1.WrittenSpan">
      <summary>Gets a <see cref="T:System.ReadOnlySpan`1" /> that contains the data written to the underlying buffer so far.</summary>
      <returns>The data written to the underlying buffer.</returns>
    </member>
    <member name="T:System.Buffers.Binary.BinaryPrimitives">
      <summary>Reads bytes as primitives with specific endianness.</summary>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadInt16BigEndian(System.ReadOnlySpan{System.Byte})">
      <summary>Reads an <see cref="T:System.Int16" /> from the beginning of a read-only span of bytes, as big endian.</summary>
      <param name="source">The read-only span to read.</param>
      <returns>The big endian value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="source" /> is too small to contain an <see cref="T:System.Int16" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadInt16LittleEndian(System.ReadOnlySpan{System.Byte})">
      <summary>Reads an <see cref="T:System.Int16" /> from the beginning of a read-only span of bytes, as little endian.</summary>
      <param name="source">The read-only span to read.</param>
      <returns>The little endian value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="source" /> is too small to contain an <see cref="T:System.Int16" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadInt32BigEndian(System.ReadOnlySpan{System.Byte})">
      <summary>Reads an <see cref="T:System.Int32" /> from the beginning of a read-only span of bytes, as big endian.</summary>
      <param name="source">The read-only span to read.</param>
      <returns>The big endian value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="source" /> is too small to contain an <see cref="T:System.Int32" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadInt32LittleEndian(System.ReadOnlySpan{System.Byte})">
      <summary>Reads an <see cref="T:System.Int32" /> from the beginning of a read-only span of bytes, as little endian.</summary>
      <param name="source">The read-only span to read.</param>
      <returns>The little endian value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="source" /> is too small to contain an <see cref="T:System.Int32" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadInt64BigEndian(System.ReadOnlySpan{System.Byte})">
      <summary>Reads an <see cref="T:System.Int64" /> from the beginning of a read-only span of bytes, as big endian.</summary>
      <param name="source">The read-only span to read.</param>
      <returns>The big endian value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="source" /> is too small to contain an <see cref="T:System.Int64" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadInt64LittleEndian(System.ReadOnlySpan{System.Byte})">
      <summary>Reads an <see cref="T:System.Int64" /> from the beginning of a read-only span of bytes, as little endian.</summary>
      <param name="source">The read-only span to read.</param>
      <returns>The little endian value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="source" /> is too small to contain an <see cref="T:System.Int64" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadUInt16BigEndian(System.ReadOnlySpan{System.Byte})">
      <summary>Reads a <see cref="T:System.UInt16" /> from the beginning of a read-only span of bytes, as big endian.</summary>
      <param name="source">The read-only span to read.</param>
      <returns>The big endian value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="source" /> is too small to contain a <see cref="T:System.UInt16" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadUInt16LittleEndian(System.ReadOnlySpan{System.Byte})">
      <summary>Reads a <see cref="T:System.UInt16" /> from the beginning of a read-only span of bytes, as little endian.</summary>
      <param name="source">The read-only span to read.</param>
      <returns>The little endian value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="source" /> is too small to contain a <see cref="T:System.UInt16" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadUInt32BigEndian(System.ReadOnlySpan{System.Byte})">
      <summary>Reads a <see cref="T:System.UInt32" /> from the beginning of a read-only span of bytes, as big endian.</summary>
      <param name="source">The read-only span to read.</param>
      <returns>The big endian value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="source" /> is too small to contain a <see cref="T:System.UInt32" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadUInt32LittleEndian(System.ReadOnlySpan{System.Byte})">
      <summary>Reads a <see cref="T:System.UInt32" /> from the beginning of a read-only span of bytes, as little endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <returns>The little endian value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="source" /> is too small to contain a <see cref="T:System.UInt32" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadUInt64BigEndian(System.ReadOnlySpan{System.Byte})">
      <summary>Reads a <see cref="T:System.UInt64" /> from the beginning of a read-only span of bytes, as big endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <returns>The big endian value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="source" /> is too small to contain a <see cref="T:System.UInt64" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReadUInt64LittleEndian(System.ReadOnlySpan{System.Byte})">
      <summary>Reads a <see cref="T:System.UInt64" /> from the beginning of a read-only span of bytes, as little endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <returns>The little endian value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="source" /> is too small to contain a <see cref="T:System.UInt64" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.Byte)">
      <summary>Reverses a primitive value by performing an endianness swap of the specified <see cref="T:System.Byte" /> value, which effectively does nothing for a <see cref="T:System.Byte" />.</summary>
      <param name="value">The value to reverse.</param>
      <returns>The passed-in value, unmodified.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.Int16)">
      <summary>Reverses a primitive value by performing an endianness swap of the specified <see cref="T:System.Int16" /> value.</summary>
      <param name="value">The value to reverse.</param>
      <returns>The reversed value.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.Int32)">
      <summary>Reverses a primitive value by performing an endianness swap of the specified <see cref="T:System.Int32" /> value.</summary>
      <param name="value">The value to reverse.</param>
      <returns>The reversed value.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.Int64)">
      <summary>Reverses a primitive value by performing an endianness swap of the specified <see cref="T:System.Int64" /> value.</summary>
      <param name="value">The value to reverse.</param>
      <returns>The reversed value.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.SByte)">
      <summary>Reverses a primitive value by performing an endianness swap of the specified <see cref="T:System.SByte" /> value, which effectively does nothing for an <see cref="T:System.SByte" />.</summary>
      <param name="value">The value to reverse.</param>
      <returns>The passed-in value, unmodified.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.UInt16)">
      <summary>Reverses a primitive value by performing an endianness swap of the specified <see cref="T:System.UInt16" /> value.</summary>
      <param name="value">The value to reverse.</param>
      <returns>The reversed value.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.UInt32)">
      <summary>Reverses a primitive value by performing an endianness swap of the specified <see cref="T:System.UInt32" /> value.</summary>
      <param name="value">The value to reverse.</param>
      <returns>The reversed value.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.ReverseEndianness(System.UInt64)">
      <summary>Reverses a primitive value by performing an endianness swap of the specified <see cref="T:System.UInt64" /> value.</summary>
      <param name="value">The value to reverse.</param>
      <returns>The reversed value.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadInt16BigEndian(System.ReadOnlySpan{System.Byte},System.Int16@)">
      <summary>Reads an <see cref="T:System.Int16" /> from the beginning of a read-only span of bytes, as big endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <param name="value">When this method returns, the value read out of the read-only span of bytes, as big endian.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain an <see cref="T:System.Int16" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadInt16LittleEndian(System.ReadOnlySpan{System.Byte},System.Int16@)">
      <summary>Reads an <see cref="T:System.Int16" /> from the beginning of a read-only span of bytes, as little endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <param name="value">When this method returns, the value read out of the read-only span of bytes, as little endian.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain an <see cref="T:System.Int16" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadInt32BigEndian(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Reads an <see cref="T:System.Int32" /> from the beginning of a read-only span of bytes, as big endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <param name="value">When this method returns, the value read out of the read-only span of bytes, as big endian.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain an <see cref="T:System.Int32" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadInt32LittleEndian(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Reads an <see cref="T:System.Int32" /> from the beginning of a read-only span of bytes, as little endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <param name="value">When this method returns, the value read out of the read-only span of bytes, as little endian.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain an <see cref="T:System.Int32" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadInt64BigEndian(System.ReadOnlySpan{System.Byte},System.Int64@)">
      <summary>Reads an <see cref="T:System.Int64" /> from the beginning of a read-only span of bytes, as big endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <param name="value">When this method returns, the value read out of the read-only span of bytes, as big endian.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain an <see cref="T:System.Int64" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadInt64LittleEndian(System.ReadOnlySpan{System.Byte},System.Int64@)">
      <summary>Reads an <see cref="T:System.Int64" /> from the beginning of a read-only span of bytes, as little endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <param name="value">When this method returns, the value read out of the read-only span of bytes, as little endian.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain an <see cref="T:System.Int64" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadUInt16BigEndian(System.ReadOnlySpan{System.Byte},System.UInt16@)">
      <summary>Reads a <see cref="T:System.UInt16" /> from the beginning of a read-only span of bytes, as big endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <param name="value">When this method returns, the value read out of the read-only span of bytes, as big endian.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain a <see cref="T:System.UInt16" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadUInt16LittleEndian(System.ReadOnlySpan{System.Byte},System.UInt16@)">
      <summary>Reads a <see cref="T:System.UInt16" /> from the beginning of a read-only span of bytes, as little endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <param name="value">When this method returns, the value read out of the read-only span of bytes, as little endian.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain a <see cref="T:System.UInt16" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadUInt32BigEndian(System.ReadOnlySpan{System.Byte},System.UInt32@)">
      <summary>Reads a <see cref="T:System.UInt32" /> from the beginning of a read-only span of bytes, as big endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <param name="value">When this method returns, the value read out of the read-only span of bytes, as big endian.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain a <see cref="T:System.UInt32" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadUInt32LittleEndian(System.ReadOnlySpan{System.Byte},System.UInt32@)">
      <summary>Reads a <see cref="T:System.UInt32" /> from the beginning of a read-only span of bytes, as little endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <param name="value">When this method returns, the value read out of the read-only span of bytes, as little endian.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain a <see cref="T:System.UInt32" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadUInt64BigEndian(System.ReadOnlySpan{System.Byte},System.UInt64@)">
      <summary>Reads a <see cref="T:System.UInt64" /> from the beginning of a read-only span of bytes, as big endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <param name="value">When this method returns, the value read out of the read-only span of bytes, as big endian.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain a <see cref="T:System.UInt64" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryReadUInt64LittleEndian(System.ReadOnlySpan{System.Byte},System.UInt64@)">
      <summary>Reads a <see cref="T:System.UInt64" /> from the beginning of a read-only span of bytes, as little endian.</summary>
      <param name="source">The read-only span of bytes to read.</param>
      <param name="value">When this method returns, the value read out of the read-only span of bytes, as little endian.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain a <see cref="T:System.UInt64" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteInt16BigEndian(System.Span{System.Byte},System.Int16)">
      <summary>Writes an <see cref="T:System.Int16" /> into a span of bytes, as big endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as big endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain an <see cref="T:System.Int16" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteInt16LittleEndian(System.Span{System.Byte},System.Int16)">
      <summary>Writes an <see cref="T:System.Int16" /> into a span of bytes, as little endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as little endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain an <see cref="T:System.Int16" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteInt32BigEndian(System.Span{System.Byte},System.Int32)">
      <summary>Writes an <see cref="T:System.Int32" /> into a span of bytes, as big endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as big endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain an <see cref="T:System.Int32" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteInt32LittleEndian(System.Span{System.Byte},System.Int32)">
      <summary>Writes an <see cref="T:System.Int32" /> into a span of bytes, as little endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as little endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain an <see cref="T:System.Int32" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteInt64BigEndian(System.Span{System.Byte},System.Int64)">
      <summary>Writes an <see cref="T:System.Int64" /> into a span of bytes, as big endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as big endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain an <see cref="T:System.Int64" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteInt64LittleEndian(System.Span{System.Byte},System.Int64)">
      <summary>Writes an <see cref="T:System.Int64" /> into a span of bytes, as little endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as little endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain an <see cref="T:System.Int64" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteUInt16BigEndian(System.Span{System.Byte},System.UInt16)">
      <summary>Writes a <see cref="T:System.UInt16" /> into a span of bytes, as big endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as big endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain a <see cref="T:System.UInt16" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteUInt16LittleEndian(System.Span{System.Byte},System.UInt16)">
      <summary>Writes a <see cref="T:System.UInt16" /> into a span of bytes, as little endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as little endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain a <see cref="T:System.UInt16" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteUInt32BigEndian(System.Span{System.Byte},System.UInt32)">
      <summary>Writes a <see cref="T:System.UInt32" /> into a span of bytes, as big endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as big endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain a <see cref="T:System.UInt32" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteUInt32LittleEndian(System.Span{System.Byte},System.UInt32)">
      <summary>Writes a <see cref="T:System.UInt32" /> into a span of bytes, as little endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as little endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain a <see cref="T:System.UInt32" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteUInt64BigEndian(System.Span{System.Byte},System.UInt64)">
      <summary>Writes a <see cref="T:System.UInt64" /> into a span of bytes, as big endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as big endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain a <see cref="T:System.UInt64" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.TryWriteUInt64LittleEndian(System.Span{System.Byte},System.UInt64)">
      <summary>Writes a <see cref="T:System.UInt64" /> into a span of bytes, as little endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as little endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <returns>
        <see langword="true" /> if the span is large enough to contain a <see cref="T:System.UInt64" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteInt16BigEndian(System.Span{System.Byte},System.Int16)">
      <summary>Writes an <see cref="T:System.Int16" /> into a span of bytes, as big endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as big endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="destination" /> is too small to contain an <see cref="T:System.Int16" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteInt16LittleEndian(System.Span{System.Byte},System.Int16)">
      <summary>Writes an <see cref="T:System.Int16" /> into a span of bytes, as little endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as little endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="destination" /> is too small to contain an <see cref="T:System.Int16" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteInt32BigEndian(System.Span{System.Byte},System.Int32)">
      <summary>Writes an <see cref="T:System.Int32" /> into a span of bytes, as big endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as big endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="destination" /> is too small to contain an <see cref="T:System.Int32" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteInt32LittleEndian(System.Span{System.Byte},System.Int32)">
      <summary>Writes an <see cref="T:System.Int32" /> into a span of bytes, as little endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as little endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="destination" /> is too small to contain an <see cref="T:System.Int32" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteInt64BigEndian(System.Span{System.Byte},System.Int64)">
      <summary>Writes an <see cref="T:System.Int64" /> into a span of bytes, as big endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as big endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="destination" /> is too small to contain an <see cref="T:System.Int64" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteInt64LittleEndian(System.Span{System.Byte},System.Int64)">
      <summary>Writes an <see cref="T:System.Int64" /> into a span of bytes, as little endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as little endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="destination" /> is too small to contain an <see cref="T:System.Int64" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteUInt16BigEndian(System.Span{System.Byte},System.UInt16)">
      <summary>Writes a <see cref="T:System.UInt16" /> into a span of bytes, as big endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as big endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="destination" /> is too small to contain a <see cref="T:System.UInt16" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteUInt16LittleEndian(System.Span{System.Byte},System.UInt16)">
      <summary>Writes a <see cref="T:System.UInt16" /> into a span of bytes, as little endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as little endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="destination" /> is too small to contain a <see cref="T:System.UInt16" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteUInt32BigEndian(System.Span{System.Byte},System.UInt32)">
      <summary>Writes a <see cref="T:System.UInt32" /> into a span of bytes, as big endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as big endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="destination" /> is too small to contain a <see cref="T:System.UInt32" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteUInt32LittleEndian(System.Span{System.Byte},System.UInt32)">
      <summary>Writes a <see cref="T:System.UInt32" /> into a span of bytes, as little endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as little endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="destination" /> is too small to contain a <see cref="T:System.UInt32" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteUInt64BigEndian(System.Span{System.Byte},System.UInt64)">
      <summary>Writes a <see cref="T:System.UInt64" /> into a span of bytes, as big endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as big endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="destination" /> is too small to contain a <see cref="T:System.UInt64" />.</exception>
    </member>
    <member name="M:System.Buffers.Binary.BinaryPrimitives.WriteUInt64LittleEndian(System.Span{System.Byte},System.UInt64)">
      <summary>Writes a <see cref="T:System.UInt64" /> into a span of bytes, as little endian.</summary>
      <param name="destination">The span of bytes where the value is to be written, as little endian.</param>
      <param name="value">The value to write into the span of bytes.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="destination" /> is too small to contain a <see cref="T:System.UInt64" />.</exception>
    </member>
    <member name="T:System.Buffers.BuffersExtensions">
      <summary>Provides extension methods for <see cref="T:System.Buffers.ReadOnlySequence`1" />.</summary>
    </member>
    <member name="M:System.Buffers.BuffersExtensions.CopyTo``1(System.Buffers.ReadOnlySequence{``0},System.Span{``0})">
      <summary>Copies the <see cref="T:System.Buffers.ReadOnlySequence`1" /> to the specified <see cref="T:System.Span`1" />.</summary>
      <param name="source">The source <see cref="T:System.Buffers.ReadOnlySequence`1" />.</param>
      <param name="destination">The destination <see cref="T:System.Span`1" />.</param>
      <typeparam name="T">The type of the items in the <see cref="T:System.Buffers.ReadOnlySequence`1" />.</typeparam>
    </member>
    <member name="M:System.Buffers.BuffersExtensions.PositionOf``1(System.Buffers.ReadOnlySequence{``0},``0)">
      <summary>Returns the position of the first occurrence of <paramref name="item" /> in the <see cref="T:System.Buffers.ReadOnlySequence`1" />.</summary>
      <param name="source">The source <see cref="T:System.Buffers.ReadOnlySequence`1" />.</param>
      <param name="value">The item to find in the <see cref="T:System.Buffers.ReadOnlySequence`1" />.</param>
      <typeparam name="T">The type of the items in the <see cref="T:System.Buffers.ReadOnlySequence`1" />.</typeparam>
      <returns>An object whose <see cref="M:System.SequencePosition.GetInteger" /> method returns the position of the first occurrence of <paramref name="item" />, or an object whose <see langword="Nullable&lt;SequencePosition&gt;.HasValue" /> property is <see langword="false" /> .</returns>
    </member>
    <member name="M:System.Buffers.BuffersExtensions.ToArray``1(System.Buffers.ReadOnlySequence{``0})">
      <summary>Converts the <see cref="T:System.Buffers.ReadOnlySequence`1" /> to an array.</summary>
      <param name="sequence">The read-only sequence to convert to an array.</param>
      <typeparam name="T">The type of the items in the <see cref="T:System.Buffers.ReadOnlySequence`1" />.</typeparam>
      <returns>An array containing the data in the current read-only sequence.</returns>
    </member>
    <member name="M:System.Buffers.BuffersExtensions.Write``1(System.Buffers.IBufferWriter{``0},System.ReadOnlySpan{``0})">
      <summary>Writes the contents of <paramref name="value" /> to <paramref name="writer" />.</summary>
      <param name="writer">The buffer writer to which to write <paramref name="value" />.</param>
      <param name="value">The read-only span to be written to <paramref name="writer" />.</param>
      <typeparam name="T">The type of the items in the <see cref="T:System.ReadOnlySpan`1" />.</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="writer" /> is shorter than <paramref name="value" />.</exception>
    </member>
    <member name="T:System.Buffers.IBufferWriter`1">
      <summary>Represents an output sink into which <typeparamref name="T" /> data can be written.</summary>
      <typeparam name="T">The type of the items in the <see cref="T:System.Buffers.IBufferWriter`1" />.</typeparam>
    </member>
    <member name="M:System.Buffers.IBufferWriter`1.Advance(System.Int32)">
      <summary>Notifies the <see cref="T:System.Buffers.IBufferWriter`1" /> that <paramref name="count" /> data items were written to the output <see cref="T:System.Span`1" /> or <see cref="T:System.Memory`1" />.</summary>
      <param name="count">The number of data items written to the <see cref="T:System.Span`1" /> or <see cref="T:System.Memory`1" />.</param>
    </member>
    <member name="M:System.Buffers.IBufferWriter`1.GetMemory(System.Int32)">
      <summary>Returns a <see cref="T:System.Memory`1" /> to write to that is at least the requested size (specified by <paramref name="sizeHint" />).</summary>
      <param name="sizeHint">The minimum length of the returned <see cref="T:System.Memory`1" />. If 0, a non-empty buffer is returned.</param>
      <returns>A <see cref="T:System.Memory`1" /> of at least the size <paramref name="sizeHint" />. If <paramref name="sizeHint" /> is 0, returns a non-empty buffer.</returns>
      <exception cref="T:System.OutOfMemoryException">The requested buffer size is not available.</exception>
    </member>
    <member name="M:System.Buffers.IBufferWriter`1.GetSpan(System.Int32)">
      <summary>Returns a <see cref="T:System.Span`1" /> to write to that is at least the requested size (specified by <paramref name="sizeHint" />).</summary>
      <param name="sizeHint">The minimum length of the returned <see cref="T:System.Span`1" />. If 0, a non-empty buffer is returned.</param>
      <returns>A <see cref="T:System.Span`1" /> of at least the size <paramref name="sizeHint" />. If <paramref name="sizeHint" /> is 0, returns a non-empty buffer.</returns>
    </member>
    <member name="T:System.Buffers.MemoryPool`1">
      <summary>Represents a pool of memory blocks.</summary>
      <typeparam name="T">The type of the items in the memory pool.</typeparam>
    </member>
    <member name="M:System.Buffers.MemoryPool`1.#ctor">
      <summary>Constructs a new instance of a memory pool.</summary>
    </member>
    <member name="M:System.Buffers.MemoryPool`1.Dispose">
      <summary>Frees all resources used by the memory pool.</summary>
    </member>
    <member name="M:System.Buffers.MemoryPool`1.Dispose(System.Boolean)">
      <summary>Frees the unmanaged resources used by the memory pool and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources;  to release only unmanaged resources.</param>
    </member>
    <member name="P:System.Buffers.MemoryPool`1.MaxBufferSize">
      <summary>Gets the maximum buffer size supported by this pool.</summary>
      <returns>The maximum buffer size supported by this pool.</returns>
    </member>
    <member name="M:System.Buffers.MemoryPool`1.Rent(System.Int32)">
      <summary>Returns a memory block capable of holding at least <paramref name="minBufferSize" /> elements of T.</summary>
      <param name="minBufferSize">The minimum number of elements of <typeparamref name="T" /> that the memory pool can hold. A value of -1 returns a memory pool set to the default size for the pool.</param>
      <returns>A memory block capable of holding at least <paramref name="minBufferSize" /> elements of T.</returns>
    </member>
    <member name="P:System.Buffers.MemoryPool`1.Shared">
      <summary>Gets a singleton instance of a memory pool based on arrays.</summary>
      <returns>A singleton instance of a memory pool.</returns>
    </member>
    <member name="T:System.Buffers.OperationStatus">
      <summary>Defines the values that can be returned from span-based operations that support processing of input contained in multiple discontiguous buffers.</summary>
    </member>
    <member name="F:System.Buffers.OperationStatus.DestinationTooSmall">
      <summary>The input is partially processed, up to what could fit into the destination buffer. The caller can enlarge the destination buffer, slice the buffers appropriately, and retry.</summary>
    </member>
    <member name="F:System.Buffers.OperationStatus.Done">
      <summary>The entire input buffer has been processed and the operation is complete.</summary>
    </member>
    <member name="F:System.Buffers.OperationStatus.InvalidData">
      <summary>The input contained invalid bytes which could not be processed. If the input is partially processed, the destination contains the partial result. This guarantees that no additional data appended to the input will make the invalid sequence valid.</summary>
    </member>
    <member name="F:System.Buffers.OperationStatus.NeedMoreData">
      <summary>The input is partially processed, up to the last valid chunk of the input that could be consumed. The caller can stitch the remaining unprocessed input with more data, slice the buffers appropriately, and retry.</summary>
    </member>
    <member name="T:System.Buffers.ReadOnlySequence`1">
      <summary>Represents a sequence that can read a sequential series of <typeparamref name="T" />.</summary>
      <typeparam name="T" />
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.#ctor(`0[])">
      <summary>Creates an instance of <see cref="T:System.Buffers.ReadOnlySequence`1" /> from the <paramref name="array" />.</summary>
      <param name="array">The array from which to create a read-only sequence.</param>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.#ctor(`0[],System.Int32,System.Int32)">
      <summary>Creates an instance of a <see cref="T:System.Buffers.ReadOnlySequence`1" /> from a section of an array.</summary>
      <param name="array">The array from which to create the read-only sequence.</param>
      <param name="start">The zero-based index of the first element in the array to include in the read-only sequence.</param>
      <param name="length">The number of elements to include in the read-only sequence.</param>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.#ctor(System.Buffers.ReadOnlySequenceSegment{`0},System.Int32,System.Buffers.ReadOnlySequenceSegment{`0},System.Int32)">
      <summary>Creates an instance of a <see cref="T:System.Buffers.ReadOnlySequence`1" /> from a linked memory list represented by start and end segments and the corresponding indexes in them.</summary>
      <param name="startSegment" />
      <param name="startIndex" />
      <param name="endSegment" />
      <param name="endIndex" />
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.#ctor(System.ReadOnlyMemory{`0})">
      <summary>Creates an instance of <see cref="T:System.Buffers.ReadOnlySequence`1" /> from a <see cref="T:System.ReadOnlyMemory`1" />.</summary>
      <param name="memory" />
    </member>
    <member name="F:System.Buffers.ReadOnlySequence`1.Empty">
      <summary>Returns an empty <see cref="T:System.Buffers.ReadOnlySequence`1" /></summary>
    </member>
    <member name="P:System.Buffers.ReadOnlySequence`1.End">
      <summary>Gets the position at the end of the <see cref="T:System.Buffers.ReadOnlySequence`1" />.</summary>
    </member>
    <member name="T:System.Buffers.ReadOnlySequence`1.Enumerator">
      <summary>Represents an enumerator over a <see cref="T:System.Buffers.ReadOnlySequence`1" />.</summary>
      <typeparam name="T" />
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.Enumerator.#ctor(System.Buffers.ReadOnlySequence{`0}@)">
      <summary>Initializes the enumerator.</summary>
      <param name="sequence">The <see cref="T:System.Buffers.ReadOnlySequence`1" /> to enumerate.</param>
    </member>
    <member name="P:System.Buffers.ReadOnlySequence`1.Enumerator.Current">
      <summary>Gets the current <see cref="T:System.ReadOnlyMemory`1" />.</summary>
      <returns>The current <see cref="T:System.ReadOnlyMemory`1" />.</returns>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:System.ReadOnlyMemory`1" /> in the <see cref="T:System.Buffers.ReadOnlySequence`1" />.</summary>
      <returns>
        <see langword="true" /> if the enumerator successfully advanced to the next item; <see langword="false" /> if the end of the sequence has been reached.</returns>
    </member>
    <member name="P:System.Buffers.ReadOnlySequence`1.First">
      <summary>Gets the <see cref="T:System.ReadOnlyMemory`1" /> from the first segment.</summary>
    </member>
    <member name="P:System.Buffers.ReadOnlySequence`1.FirstSpan">
      <summary>Gets the <see cref="T:System.ReadOnlySpan`1" /> from the first segment.</summary>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.GetEnumerator">
      <summary>Returns an enumerator over the <see cref="T:System.Buffers.ReadOnlySequence`1" />.</summary>
      <returns>Returns an enumerator over the <see cref="T:System.Buffers.ReadOnlySequence`1" />.</returns>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.GetPosition(System.Int64)">
      <summary>Returns a new <see cref="T:System.SequencePosition" /> at an <paramref name="offset" /> from the start of the sequence.</summary>
      <param name="offset" />
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.GetPosition(System.Int64,System.SequencePosition)">
      <summary>Returns a new <see cref="T:System.SequencePosition" /> at an <paramref name="offset" /> from the <paramref name="origin" />.</summary>
      <param name="offset" />
      <param name="origin" />
      <returns>A new object that starts at the <paramref name="offset" /> position of the original object.</returns>
    </member>
    <member name="P:System.Buffers.ReadOnlySequence`1.IsEmpty">
      <summary>Gets a value that indicates whether the <see cref="T:System.Buffers.ReadOnlySequence`1" /> is empty.</summary>
    </member>
    <member name="P:System.Buffers.ReadOnlySequence`1.IsSingleSegment">
      <summary>Gets a value that indicates whether the <see cref="T:System.Buffers.ReadOnlySequence`1" /> contains a single <see cref="T:System.ReadOnlyMemory`1" /> segment.</summary>
      <returns>
        <see langword="true" /> if the read-only sequence is empty; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Buffers.ReadOnlySequence`1.Length">
      <summary>Gets the length of the <see cref="T:System.Buffers.ReadOnlySequence`1" />.</summary>
      <returns>The length of the read-only sequence.</returns>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.Int32,System.Int32)">
      <summary>Forms a slice out of the current <see cref="T:System.Buffers.ReadOnlySequence`1" />, beginning at <paramref name="start" />, with <paramref name="length" /> items.</summary>
      <param name="start">The index at which to begin this slice.</param>
      <param name="length">The length of the slice.</param>
      <returns>A slice that consists of <paramref name="length" /> elements from the current instance starting at index <paramref name="start" />.</returns>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.Int32,System.SequencePosition)">
      <summary>Forms a slice out of the current <see cref="T:System.Buffers.ReadOnlySequence`1" />, beginning at <paramref name="start" /> and ending at <paramref name="end" /> (exclusive).</summary>
      <param name="start">The index at which to begin this slice.</param>
      <param name="end">The end (exclusive) <see cref="T:System.SequencePosition" /> of the slice.</param>
      <returns>A slice that consists of items from the <paramref name="start" /> index to, but not including, the <paramref name="end" /> sequence position in the current read-only sequence.</returns>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.Int64)">
      <summary>Forms a slice out of the current <see cref="T:System.Buffers.ReadOnlySequence`1" />, beginning at a specified index and continuing to the end of the read-only sequence.</summary>
      <param name="start">The start index at which to begin this slice.</param>
      <returns>A slice starting at index <paramref name="start" /> and continuing to the end of the current read-only sequence.</returns>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.Int64,System.Int64)">
      <summary>Forms a slice out of the given <see cref="T:System.Buffers.ReadOnlySequence`1" />, beginning at <paramref name="start" />, with <paramref name="length" /> items.</summary>
      <param name="start">The index at which to begin this slice.</param>
      <param name="length">The length of the slice.</param>
      <returns>A slice that consists of <paramref name="length" /> elements from the current instance starting at index <paramref name="start" />.</returns>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.Int64,System.SequencePosition)">
      <summary>Forms a slice out of the current <see cref="T:System.Buffers.ReadOnlySequence`1" />, beginning at <paramref name="start" /> and ending at <paramref name="end" /> (exclusive).</summary>
      <param name="start">The index at which to begin this slice.</param>
      <param name="end">The end (exclusive) of the slice.</param>
      <returns>A slice that consists of items from the <paramref name="start" /> index to, but not including, the <paramref name="end" /> sequence position in the current read-only sequence.</returns>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.SequencePosition)">
      <summary>Forms a slice out of the current <see cref="T:System.Buffers.ReadOnlySequence`1" />, beginning at a specified sequence position and continuing to the end of the read-only sequence.</summary>
      <param name="start">The starting (inclusive) <see cref="T:System.SequencePosition" /> at which to begin this slice.</param>
      <returns>A slice starting at sequence position <paramref name="start" /> and continuing to the end of the current read-only sequence.</returns>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.SequencePosition,System.Int32)">
      <summary>Forms a slice out of the current <see cref="T:System.Buffers.ReadOnlySequence`1" />, beginning at <paramref name="start" />, with <paramref name="length" /> items.</summary>
      <param name="start">The starting (inclusive) <see cref="T:System.SequencePosition" /> at which to begin this slice.</param>
      <param name="length">The length of the slice.</param>
      <returns>A slice that consists of <paramref name="length" /> elements from the current instance starting at sequence position <paramref name="start" />.</returns>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.SequencePosition,System.Int64)">
      <summary>Forms a slice out of the current <see cref="T:System.Buffers.ReadOnlySequence`1" />, beginning at <paramref name="start" />, with <paramref name="length" /> items.</summary>
      <param name="start">The starting (inclusive) <see cref="T:System.SequencePosition" /> at which to begin this slice.</param>
      <param name="length">The length of the slice.</param>
      <returns>A slice that consists of <paramref name="length" /> elements from the current instance starting at sequence position <paramref name="start" />.</returns>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.Slice(System.SequencePosition,System.SequencePosition)">
      <summary>Forms a slice out of the current <see cref="T:System.Buffers.ReadOnlySequence`1" />, beginning at <paramref name="start" /> and ending at <paramref name="end" /> (exclusive).</summary>
      <param name="start">The starting (inclusive) <see cref="T:System.SequencePosition" /> at which to begin this slice.</param>
      <param name="end">The ending (exclusive) <see cref="T:System.SequencePosition" /> of the slice.</param>
      <returns>A slice that consists of items from the <paramref name="start" /> sequence position to, but not including, the <paramref name="end" /> sequence position in the current read-only sequence.</returns>
    </member>
    <member name="P:System.Buffers.ReadOnlySequence`1.Start">
      <summary>Gets the position to the start of the <see cref="T:System.Buffers.ReadOnlySequence`1" />.</summary>
    </member>
    <member name="M:System.Buffers.ReadOnlySequence`1.ToString" />
    <member name="M:System.Buffers.ReadOnlySequence`1.TryGet(System.SequencePosition@,System.ReadOnlyMemory{`0}@,System.Boolean)">
      <summary>Tries to retrieve the next segment after <paramref name="position" /> and returns a value that indicates whether the operation succeeded.</summary>
      <param name="position">The current sequence position.</param>
      <param name="memory">A read-only memory span that contains the next segment after <paramref name="position" />.</param>
      <param name="advance">
        <see langword="true" /> if <paramref name="position" /> should to the beginning of next segment; otherwise, <see langword="false" />.</param>
      <returns>Returns <see langword="true" /> if the method returned the next segment, or <see langword="false" /> if the end of the read-only sequence was reached.</returns>
    </member>
    <member name="T:System.Buffers.ReadOnlySequenceSegment`1">
      <summary>Represents a linked list of <see cref="T:System.ReadOnlyMemory`1" /> nodes.</summary>
      <typeparam name="T" />
    </member>
    <member name="M:System.Buffers.ReadOnlySequenceSegment`1.#ctor" />
    <member name="P:System.Buffers.ReadOnlySequenceSegment`1.Memory">
      <summary>Gets or sets a <see cref="T:System.ReadOnlyMemory`1" /> value for the current node.</summary>
      <returns>A <see cref="T:System.ReadOnlyMemory`1" /> value for the current node.</returns>
    </member>
    <member name="P:System.Buffers.ReadOnlySequenceSegment`1.Next">
      <summary>Gets or sets the next node.</summary>
      <returns>The next node.</returns>
    </member>
    <member name="P:System.Buffers.ReadOnlySequenceSegment`1.RunningIndex">
      <summary>Gets or sets the sum of node lengths before the current node.</summary>
      <returns>The sum of node lengths before the current node.</returns>
    </member>
    <member name="T:System.Buffers.SequenceReader`1">
      <summary>Provides methods for reading binary and text data out of a <see cref="T:System.Buffers.ReadOnlySequence`1" /> with a focus on performance and minimal or zero heap allocations.</summary>
      <typeparam name="T">The type of the read-only sequence.</typeparam>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.#ctor(System.Buffers.ReadOnlySequence{`0})">
      <summary>Creates a <see cref="T:System.Buffers.SequenceReader`1" /> over a given <see cref="T:System.Buffers.ReadOnlySequence`1" />.</summary>
      <param name="sequence">The read-only sequence over which to create the <see cref="T:System.Buffers.SequenceReader`1" />.</param>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.Advance(System.Int64)">
      <summary>Moves the reader ahead a specified number of items.</summary>
      <param name="count">The number of items to advance.</param>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.AdvancePast(`0)">
      <summary>Advances past consecutive instances of the given <paramref name="value" />.</summary>
      <param name="value">The value past which the reader is to advance.</param>
      <returns>The number of positions the reader has advanced.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.AdvancePastAny(`0,`0)">
      <summary>Advances past consecutive instances of either of two specified values.</summary>
      <param name="value0">The first value to skip.</param>
      <param name="value1">The second value to skip.</param>
      <returns>The number of positions the reader has advanced.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.AdvancePastAny(`0,`0,`0)">
      <summary>Advances past consecutive instances of any of three specified values.</summary>
      <param name="value0">The first value to skip.</param>
      <param name="value1">The second value to skip.</param>
      <param name="value2">The third value to skip.</param>
      <returns>The number of positions the reader has advanced.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.AdvancePastAny(`0,`0,`0,`0)">
      <summary>Advances past consecutive instances of any of four specified values.</summary>
      <param name="value0">The first value to skip.</param>
      <param name="value1">The second value to skip.</param>
      <param name="value2">The third value to skip.</param>
      <param name="value3">The fourth value to skip.</param>
      <returns>The number of positions the reader has advanced.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.AdvancePastAny(System.ReadOnlySpan{`0})">
      <summary>Skips consecutive instances of any of the specified <paramref name="values" />.</summary>
      <param name="values">The values to be skipped.</param>
      <returns>The number of positions the reader has advanced.</returns>
    </member>
    <member name="P:System.Buffers.SequenceReader`1.Consumed">
      <summary>Gets the total number of <typeparamref name="T" /> values processed by the reader.</summary>
      <returns>The total number of values processed by the reader.</returns>
    </member>
    <member name="P:System.Buffers.SequenceReader`1.CurrentSpan">
      <summary>Gets a <see cref="T:System.Span`1" /> that contains the current segment in the <see cref="P:System.Buffers.SequenceReader`1.Sequence" />.</summary>
      <returns>A span that contains the current segment in the sequence.</returns>
    </member>
    <member name="P:System.Buffers.SequenceReader`1.CurrentSpanIndex">
      <summary>Gets the index in the <see cref="P:System.Buffers.SequenceReader`1.CurrentSpan" />.</summary>
      <returns>The index in the <see cref="P:System.Buffers.SequenceReader`1.CurrentSpan" />.</returns>
    </member>
    <member name="P:System.Buffers.SequenceReader`1.End">
      <summary>Gets a value that indicates whether there is no more data in the <see cref="P:System.Buffers.SequenceReader`1.Sequence" />.</summary>
      <returns>
        <see langword="true" /> when there is no more data in the <see cref="P:System.Buffers.SequenceReader`1.Sequence" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.IsNext(`0,System.Boolean)">
      <summary>Checks whether a specified value is next in the sequence.</summary>
      <param name="next">The value to compare the next items to.</param>
      <param name="advancePast">
        <see langword="true" /> to move past the <paramref name="next" /> values if they are found; otherwise, <see langword="false" />.</param>
      <returns>
        <see langword="true" /> if the values are next in sequence; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.IsNext(System.ReadOnlySpan{`0},System.Boolean)">
      <summary>Checks whether the values specified in a read-only span are next in the sequence.</summary>
      <param name="next">The span to compare the next items to.</param>
      <param name="advancePast">
        <see langword="true" /> to move past the <paramref name="next" /> values if they are found; otherwise, <see langword="false" />.</param>
      <returns>
        <see langword="true" /> if the values are next in sequence; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Buffers.SequenceReader`1.Length">
      <summary>Gets the count of items in the reader's <see cref="P:System.Buffers.SequenceReader`1.Sequence" />.</summary>
      <returns>The count of items in the reader's <see cref="P:System.Buffers.SequenceReader`1.Sequence" />.</returns>
    </member>
    <member name="P:System.Buffers.SequenceReader`1.Position">
      <summary>Gets the current position in the <see cref="P:System.Buffers.SequenceReader`1.Sequence" />.</summary>
      <returns>The current position in the <see cref="P:System.Buffers.SequenceReader`1.Sequence" />.</returns>
    </member>
    <member name="P:System.Buffers.SequenceReader`1.Remaining">
      <summary>Gets  the remaining items in the reader's <see cref="P:System.Buffers.SequenceReader`1.Sequence" />.</summary>
      <returns>The remaining items in the reader's <see cref="P:System.Buffers.SequenceReader`1.Sequence" /></returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.Rewind(System.Int64)">
      <summary>Moves the reader back the specified number of items.</summary>
      <param name="count">The number of items.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is negative or greater than <see cref="P:System.Buffers.SequenceReader`1.Consumed" />.</exception>
    </member>
    <member name="P:System.Buffers.SequenceReader`1.Sequence">
      <summary>Gets the underlying <see cref="T:System.Buffers.ReadOnlySequence`1" /> for the reader.</summary>
      <returns>The underlying read-only sequence for the reader.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.TryAdvanceTo(`0,System.Boolean)">
      <summary>Searches for a specified delimiter and optionally advances past it if it is found.</summary>
      <param name="delimiter">The delimiter to search for.</param>
      <param name="advancePastDelimiter">
        <see langword="true" /> to move past the <paramref name="delimiter" /> if it is found; otherwise, <see langword="false" />.</param>
      <returns>
        <see langword="true" /> if the given <paramref name="delimiter" /> was found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.TryAdvanceToAny(System.ReadOnlySpan{`0},System.Boolean)">
      <summary>Searches for any of a number of specified delimiters and optionally advances past the first one to be found.</summary>
      <param name="delimiters">The delimiters to search for.</param>
      <param name="advancePastDelimiter">
        <see langword="true" /> to move past the first instance of any of the given <paramref name="delimiters" />;  to not move past the delimiter.</param>
      <returns>
        <see langword="true" /> if any of the given <paramref name="delimiters" /> was found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.TryCopyTo(System.Span{`0})">
      <summary>Copies data from the current position to the given destination span if there is enough data to fill it.</summary>
      <param name="destination">Destination span to copy to.</param>
      <returns>
        <see langword="true" /> if there is enough data to completely fill the <paramref name="destination" /> span; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.TryPeek(`0@)">
      <summary>Peeks at the next value without advancing the reader.</summary>
      <param name="value">The next value, or the default value if at the end of the reader.</param>
      <returns>
        <see langword="true" /> if the reader is not at its end and the peek operation succeeded; <see langword="false" /> if at the end of the reader.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.TryRead(`0@)">
      <summary>Reads the next value and advance the reader.</summary>
      <param name="value">The next value, or the default value if at the end of the reader.</param>
      <returns>
        <see langword="true" /> if the reader is not at its end and the read operation succeeded; <see langword="false" /> if at the end of the reader.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.TryReadTo(System.Buffers.ReadOnlySequence{`0}@,`0,`0,System.Boolean)">
      <summary>Tries to read everything up to the given <paramref name="delimiter" />, ignoring delimiters that are preceded by <paramref name="delimiterEscape" />.</summary>
      <param name="sequence">When the method returns, contains the data read, if any.</param>
      <param name="delimiter">The delimiter to look for.</param>
      <param name="delimiterEscape">A value that indicates that an immediately following <paramref name="delimiter" /> is to be skipped.</param>
      <param name="advancePastDelimiter">
        <see langword="true" /> to move past the <paramref name="delimiter" /> if found; otherwise, <see langword="false" />.</param>
      <returns>
        <see langword="true" /> if the <paramref name="delimiter" /> was found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.TryReadTo(System.Buffers.ReadOnlySequence{`0}@,`0,System.Boolean)">
      <summary>Tries to read everything up to the given <paramref name="delimiter" />.</summary>
      <param name="sequence">When the method returns, contains the data read, if any.</param>
      <param name="delimiter">The delimiter to look for.</param>
      <param name="advancePastDelimiter">
        <see langword="true" /> to move past the <paramref name="delimiter" /> if found; otherwise, <see langword="false" />.</param>
      <returns>
        <see langword="true" /> if the <paramref name="delimiter" /> was found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.TryReadTo(System.Buffers.ReadOnlySequence{`0}@,System.ReadOnlySpan{`0},System.Boolean)">
      <summary>Tries to read data until the entire delimiter specified as a read-only span matches.</summary>
      <param name="sequence">When the method returns, contains the data read, if any.</param>
      <param name="delimiter">A read-only span that contains one or more delimiters.</param>
      <param name="advancePastDelimiter">
        <see langword="true" /> to move past the <paramref name="delimiter" /> if found; otherwise, <see langword="false" />.</param>
      <returns>
        <see langword="true" /> if the <paramref name="delimiter" /> was found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.TryReadTo(System.ReadOnlySpan{`0}@,`0,`0,System.Boolean)">
      <summary>Tries to read everything up to the given <paramref name="delimiter" />, ignoring delimiters that are preceded by <paramref name="delimiterEscape" />.</summary>
      <param name="span">When the method returns, contains the data read, if any.</param>
      <param name="delimiter">The delimiter to look for.</param>
      <param name="delimiterEscape">A value that indicates that an immediately following <paramref name="delimiter" /> is to be skipped.</param>
      <param name="advancePastDelimiter">
        <see langword="true" /> to move past the <paramref name="delimiter" /> if found; otherwise, <see langword="false" />.</param>
      <returns>
        <see langword="true" /> if the <paramref name="delimiter" /> was found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.TryReadTo(System.ReadOnlySpan{`0}@,`0,System.Boolean)">
      <summary>Tries to read everything up to the given <paramref name="delimiter" />.</summary>
      <param name="span">When the method returns, contains the data read, if any.</param>
      <param name="delimiter">The delimiter to look for.</param>
      <param name="advancePastDelimiter">
        <see langword="true" /> to move past the <paramref name="delimiter" /> if found; otherwise, <see langword="false" />.</param>
      <returns>
        <see langword="true" /> if the <paramref name="delimiter" /> was found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.TryReadToAny(System.Buffers.ReadOnlySequence{`0}@,System.ReadOnlySpan{`0},System.Boolean)">
      <summary>Tries to read everything up to any of the specified <paramref name="delimiters" />.</summary>
      <param name="sequence">When the method returns, contains the data read, if any.</param>
      <param name="delimiters">The delimiters to look for.</param>
      <param name="advancePastDelimiter">
        <see langword="true" /> to move past the first found instance of any of the given <paramref name="delimiters" />; otherwise, <see langword="false" />.</param>
      <returns>
        <see langword="true" /> if any of the <paramref name="delimiters" /> were found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReader`1.TryReadToAny(System.ReadOnlySpan{`0}@,System.ReadOnlySpan{`0},System.Boolean)">
      <summary>Tries to read everything up to any of the specified <paramref name="delimiters" />.</summary>
      <param name="span">When the method returns, contains the data read, if any.</param>
      <param name="delimiters">The delimiters to look for.</param>
      <param name="advancePastDelimiter">
        <see langword="true" /> to move past the first found instance of any of the given <paramref name="delimiters" />; otherwise, <see langword="false" />.</param>
      <returns>
        <see langword="true" /> if any of the <paramref name="delimiters" /> were found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Buffers.SequenceReader`1.UnreadSpan">
      <summary>Gets the unread portion of the <see cref="P:System.Buffers.SequenceReader`1.CurrentSpan" />.</summary>
      <returns>The unread portion of the <see cref="P:System.Buffers.SequenceReader`1.CurrentSpan" />.</returns>
    </member>
    <member name="T:System.Buffers.SequenceReaderExtensions">
      <summary>Provides extended functionality for the <see cref="T:System.Buffers.SequenceReader`1" /> class that allows reading of endian specific numeric values from binary data.</summary>
    </member>
    <member name="M:System.Buffers.SequenceReaderExtensions.TryReadBigEndian(System.Buffers.SequenceReader{System.Byte},System.Int16@)">
      <summary>Tries to read an <see cref="T:System.Int16" /> as big endian.</summary>
      <param name="reader">The byte sequence reader instance from which the value is to be read.</param>
      <param name="value">When the method returns, the value read out of the byte sequence reader, as big endian.</param>
      <returns>
        <see langword="true" /> if the read operation is successful; <see langword="false" /> if there isn't enough data for an <see cref="T:System.Int16" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReaderExtensions.TryReadBigEndian(System.Buffers.SequenceReader{System.Byte},System.Int32@)">
      <summary>Tries to read an <see cref="T:System.Int32" /> as big endian.</summary>
      <param name="reader">The byte sequence reader from which to read the value.</param>
      <param name="value">When the method returns, the value read out of the byte sequence reader, as big endian.</param>
      <returns>
        <see langword="true" /> if the read operation is successful; <see langword="false" /> if there isn't enough data for an <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReaderExtensions.TryReadBigEndian(System.Buffers.SequenceReader{System.Byte},System.Int64@)">
      <summary>Tries to read an <see cref="T:System.Int64" /> as big endian.</summary>
      <param name="reader">The byte sequence reader instance from which the value is to be read.</param>
      <param name="value">When the method returns, the value read out of the byte sequence reader, as big endian.</param>
      <returns>
        <see langword="true" /> if the read operation is successful; <see langword="false" /> if there isn't enough data for an <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReaderExtensions.TryReadLittleEndian(System.Buffers.SequenceReader{System.Byte},System.Int16@)">
      <summary>Tries to read an <see cref="T:System.Int16" /> as little endian.</summary>
      <param name="reader">The byte sequence reader instance from which the value is to be read.</param>
      <param name="value">When the method returns, the value read out of the byte sequence reader, as little endian.</param>
      <returns>
        <see langword="true" /> if the read operation is successful; <see langword="false" /> if there isn't enough data for an <see cref="T:System.Int16" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReaderExtensions.TryReadLittleEndian(System.Buffers.SequenceReader{System.Byte},System.Int32@)">
      <summary>Tries to read an <see cref="T:System.Int32" /> as little endian.</summary>
      <param name="reader">The byte sequence reader instance from which the value is to be read.</param>
      <param name="value">When the method returns, the value read out of the byte sequence reader, as little endian.</param>
      <returns>
        <see langword="true" /> if the read operation is successful; <see langword="false" /> if there isn't enough data for an <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:System.Buffers.SequenceReaderExtensions.TryReadLittleEndian(System.Buffers.SequenceReader{System.Byte},System.Int64@)">
      <summary>Tries to read an <see cref="T:System.Int64" /> as little endian.</summary>
      <param name="reader">The byte sequence reader instance from which the value is to be read.</param>
      <param name="value">When the method returns, the value read out of the byte sequence reader, as little endian.</param>
      <returns>
        <see langword="true" /> if the read operation is successful; <see langword="false" /> if there isn't enough data for an <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="T:System.Buffers.StandardFormat">
      <summary>Represents a standard format string without using an actual string.</summary>
    </member>
    <member name="M:System.Buffers.StandardFormat.#ctor(System.Char,System.Byte)">
      <summary>Initializes a new instance of the <see cref="T:System.Buffers.StandardFormat" /> structure.</summary>
      <param name="symbol">A type-specific format specifier, such as 'G', 'D', or 'X'.</param>
      <param name="precision">An optional precision ranging from 0 to 99, or the special value <see cref="F:System.Buffers.StandardFormat.NoPrecision" /> (the default).</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="symbol" /> is not <see cref="F:System.Buffers.StandardFormat.NoPrecision" />, and its value is greater than <see cref="F:System.Buffers.StandardFormat.MaxPrecision" />.
-or-
<paramref name="symbol" /> cannot be converted to a <see cref="T:System.Byte" />.</exception>
    </member>
    <member name="M:System.Buffers.StandardFormat.Equals(System.Buffers.StandardFormat)">
      <summary>Returns a value that indicates whether the specified <see cref="T:System.Buffers.StandardFormat" /> is equal to the current instance.</summary>
      <param name="other">The format to compare to the current instance.</param>
      <returns>
        <see langword="true" /> if the two instances are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.StandardFormat.Equals(System.Object)">
      <summary>Returns a value that indicates whether the specified object is a <see cref="T:System.Buffers.StandardFormat" /> object that is equal to the current instance.</summary>
      <param name="obj">An object to compare to the current instance.</param>
      <returns>
        <see langword="true" /> if the two instances are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.StandardFormat.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>The hash code for this instance.</returns>
    </member>
    <member name="P:System.Buffers.StandardFormat.HasPrecision">
      <summary>Gets a value that indicates whether a format has a defined precision.</summary>
      <returns>
        <see langword="true" /> if the format has a precision other than <see cref="F:System.Buffers.StandardFormat.NoPrecision" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Buffers.StandardFormat.IsDefault">
      <summary>Gets a value that indicates whether the current instance is a default format.</summary>
      <returns>
        <see langword="true" /> if the current instance is a default format; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="F:System.Buffers.StandardFormat.MaxPrecision">
      <summary>Defines the maximum valid precision value.</summary>
    </member>
    <member name="F:System.Buffers.StandardFormat.NoPrecision">
      <summary>Indicates that a format doesn't use a precision or that the precision is unspecified.</summary>
    </member>
    <member name="M:System.Buffers.StandardFormat.op_Equality(System.Buffers.StandardFormat,System.Buffers.StandardFormat)">
      <summary>Returns a value that indicates whether two <see cref="T:System.Buffers.StandardFormat" /> instances are equal.</summary>
      <param name="left">The first format to compare.</param>
      <param name="right">The second format to compare.</param>
      <returns>
        <see langword="true" /> if the two instances are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.StandardFormat.op_Implicit(System.Char)~System.Buffers.StandardFormat">
      <summary>Converts a character to a <see cref="T:System.Buffers.StandardFormat" /> instance using <see cref="F:System.Buffers.StandardFormat.NoPrecision" /> precision.</summary>
      <param name="symbol">The character to convert to a <see cref="T:System.Buffers.StandardFormat" /> value.</param>
      <returns>A format with a <see cref="P:System.Buffers.StandardFormat.Symbol" /> property equal to <paramref name="symbol" /> and a <see cref="P:System.Buffers.StandardFormat.Precision" /> property equal to <see cref="F:System.Buffers.StandardFormat.NoPrecision" />.</returns>
    </member>
    <member name="M:System.Buffers.StandardFormat.op_Inequality(System.Buffers.StandardFormat,System.Buffers.StandardFormat)">
      <summary>Determines whether two <see cref="T:System.Buffers.StandardFormat" /> instances are unequal.</summary>
      <param name="left">The first format to compare.</param>
      <param name="right">The second format to compare.</param>
      <returns>
        <see langword="true" /> if the two formats are unequal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Buffers.StandardFormat.Parse(System.ReadOnlySpan{System.Char})">
      <summary>Converts a <see langword="ReadOnlySpan&lt;System.Char&gt;" /> into a <see cref="T:System.Buffers.StandardFormat" /> instance using <see cref="F:System.Buffers.StandardFormat.NoPrecision" /> precision.</summary>
      <param name="format">A read-only span that contains the character to parse.</param>
      <returns>A value whose <see cref="P:System.Buffers.StandardFormat.Symbol" /> property value is the character in <paramref name="format" /> and whose <see cref="P:System.Buffers.StandardFormat.Precision" /> property value is <see cref="F:System.Buffers.StandardFormat.NoPrecision" />.</returns>
    </member>
    <member name="M:System.Buffers.StandardFormat.Parse(System.String)">
      <summary>Converts a classic .NET standard format string to a <see cref="T:System.Buffers.StandardFormat" /> instance.</summary>
      <param name="format">A classic .NET standard format string.</param>
      <returns>A format.</returns>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> is not a valid standard format string.</exception>
    </member>
    <member name="P:System.Buffers.StandardFormat.Precision">
      <summary>Gets the precision component of the format.</summary>
      <returns>The precision component, which can be <see cref="F:System.Buffers.StandardFormat.NoPrecision" />, or can range from 0 to 9.</returns>
    </member>
    <member name="P:System.Buffers.StandardFormat.Symbol">
      <summary>Gets the character component of the format.</summary>
      <returns>The character component of the format.</returns>
    </member>
    <member name="M:System.Buffers.StandardFormat.ToString">
      <summary>Returns the string representation of this format.</summary>
      <returns>The string representation of this format.</returns>
    </member>
    <member name="M:System.Buffers.StandardFormat.TryParse(System.ReadOnlySpan{System.Char},System.Buffers.StandardFormat@)">
      <summary>Attempts to convert a <see langword="ReadOnlySpan&lt;Char&gt;" /> to a <see cref="T:System.Buffers.StandardFormat" /> instance and returns a value that indicates whether the parsing operation succeeded.</summary>
      <param name="format">A read-only span that contains the character to convert.</param>
      <param name="result">When the method returns, contains the parsed <see cref="T:System.Buffers.StandardFormat" /> instance if the operation was successful.</param>
      <returns>
        <see langword="true" /> if the parsing operation was successful; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Buffers.Text.Base64">
      <summary>Converts between binary data and UTF-8 encoded text that is represented in base 64.</summary>
    </member>
    <member name="M:System.Buffers.Text.Base64.DecodeFromUtf8(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@,System.Int32@,System.Boolean)">
      <summary>Decodes the span of UTF-8 encoded text represented as base 64 into binary data. If the input is not a multiple of 4, it will decode as much as it can, to the closest multiple of 4.</summary>
      <param name="utf8">The input span that contains UTF-8 encoded text in base 64 that needs to be decoded.</param>
      <param name="bytes">The output span that contains the result of the operation, that is, the decoded binary data.</param>
      <param name="bytesConsumed">The number of input bytes consumed during the operation. This can be used to slice the input for subsequent calls, if necessary.</param>
      <param name="bytesWritten">The number of bytes written into the output span. This can be used to slice the output for subsequent calls, if necessary.</param>
      <param name="isFinalBlock">
        <see langword="true" /> (default) if the input span contains the entire data to decode. <see langword="false" /> if the input span contains partial data with more data to follow.</param>
      <returns>One of the enumeration values that indicates the status of the decoding operation.</returns>
    </member>
    <member name="M:System.Buffers.Text.Base64.DecodeFromUtf8InPlace(System.Span{System.Byte},System.Int32@)">
      <summary>Decodes the span of UTF-8 encoded text in base 64 (in-place) into binary data. The decoded binary output is smaller than the text data contained in the input (the operation deflates the data). If the input is not a multiple of 4, the method will not decode any data.</summary>
      <param name="buffer">The input span that contains the base-64 text data that needs to be decoded.</param>
      <param name="bytesWritten">The number of bytes written into the buffer.</param>
      <returns>One of the enumeration values that indicates the status of the decoding operation.</returns>
    </member>
    <member name="M:System.Buffers.Text.Base64.EncodeToUtf8(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@,System.Int32@,System.Boolean)">
      <summary>Encodes the span of binary data into UTF-8 encoded text represented as base 64.</summary>
      <param name="bytes">The input span that contains binary data that needs to be encoded.</param>
      <param name="utf8">The output span that contains the result of the operation, that is, the UTF-8 encoded text in base 64.</param>
      <param name="bytesConsumed">The number of input bytes consumed during the operation. This can be used to slice the input for subsequent calls, if necessary.</param>
      <param name="bytesWritten">The number of bytes written into the output span. This can be used to slice the output for subsequent calls, if necessary.</param>
      <param name="isFinalBlock">
        <see langword="true" /> (the default) if the input span contains the entire data to encode. <see langword="false" /> if the input span contains partial data with more data to follow.</param>
      <returns>One of the enumeration values that indicates the status of the encoding operation.</returns>
    </member>
    <member name="M:System.Buffers.Text.Base64.EncodeToUtf8InPlace(System.Span{System.Byte},System.Int32,System.Int32@)">
      <summary>Encodes the span of binary data (in-place) into UTF-8 encoded text represented as base 64. The encoded text output is larger than the binary data contained in the input (the operation inflates the data).</summary>
      <param name="buffer">The input span that contains binary data that needs to be encoded. Because the method performs an in-place conversion, it needs to be large enough to store the result of the operation.</param>
      <param name="dataLength">The number of bytes of binary data contained within the buffer that needs to be encoded. This value must be smaller than the buffer length.</param>
      <param name="bytesWritten">The number of bytes written into the buffer.</param>
      <returns>One of the enumeration values that indicates the status of the encoding operation.</returns>
    </member>
    <member name="M:System.Buffers.Text.Base64.GetMaxDecodedFromUtf8Length(System.Int32)">
      <summary>Returns the maximum length (in bytes) of the result if you were to decode base-64 encoded text within a byte span with the specified length.</summary>
      <param name="length">The size of the byte span.</param>
      <returns>The maximum length (in bytes) of the result.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than 0.</exception>
    </member>
    <member name="M:System.Buffers.Text.Base64.GetMaxEncodedToUtf8Length(System.Int32)">
      <summary>Returns the maximum length (in bytes) of the result if you were to encode binary data within a byte span with the specified length.</summary>
      <param name="length">The size of the byte span.</param>
      <returns>The maximum length (in bytes) of the result.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than 0 or larger than 1610612733 (since encode inflates the data by 4/3).</exception>
    </member>
    <member name="T:System.Buffers.Text.Utf8Formatter">
      <summary>Provides static mthods to format common data types as Utf8 strings.</summary>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Boolean,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats a <see cref="T:System.Boolean" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Byte,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats a <see cref="T:System.Byte" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.DateTime,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats a <see cref="T:System.DateTime" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.DateTimeOffset,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats a <see cref="T:System.DateTimeOffset" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Decimal,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats a <see cref="T:System.Decimal" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Double,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats a <see cref="T:System.Double" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Guid,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats a <see cref="T:System.Guid" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Int16,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats an <see cref="T:System.Int16" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Int32,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats an <see cref="T:System.Int32" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Int64,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats an <see cref="T:System.Int64" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.SByte,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats an <see cref="T:System.SByte" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.Single,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats a <see cref="T:System.Single" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.TimeSpan,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats a <see cref="T:System.TimeSpan" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.UInt16,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats a <see cref="T:System.UInt16" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.UInt32,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats a <see cref="T:System.UInt32" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Formatter.TryFormat(System.UInt64,System.Span{System.Byte},System.Int32@,System.Buffers.StandardFormat)">
      <summary>Formats a <see cref="T:System.UInt64" /> as a UTF8 string.</summary>
      <param name="value">The value to format.</param>
      <param name="destination">The buffer to write the UTF8-formatted value to.</param>
      <param name="bytesWritten">When the method returns, contains the length of the formatted text in bytes.</param>
      <param name="format">The standard format to use.</param>
      <returns>
        <see langword="true" /> if the formatting operation succeeds; <see langword="false" /> if <paramref name="buffer" /> is too small.</returns>
    </member>
    <member name="T:System.Buffers.Text.Utf8Parser">
      <summary>Provides static methods to parse Utf8 strings to common data types.</summary>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Boolean@,System.Int32@,System.Char)">
      <summary>Parses a <see cref="T:System.Boolean" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Byte@,System.Int32@,System.Char)">
      <summary>Parses a <see cref="T:System.Byte" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.DateTime@,System.Int32@,System.Char)">
      <summary>Parses a <see cref="T:System.DateTime" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.DateTimeOffset@,System.Int32@,System.Char)">
      <summary>Parses a <see cref="T:System.DateTimeOffset" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Decimal@,System.Int32@,System.Char)">
      <summary>Parses a <see cref="T:System.Decimal" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Double@,System.Int32@,System.Char)">
      <summary>Parses a <see cref="T:System.Double" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Guid@,System.Int32@,System.Char)">
      <summary>Parses a <see cref="T:System.Guid" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Int16@,System.Int32@,System.Char)">
      <summary>Parses an <see cref="T:System.Int16" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Int32@,System.Int32@,System.Char)">
      <summary>Parses an <see cref="T:System.Int32" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Int64@,System.Int32@,System.Char)">
      <summary>Parses an <see cref="T:System.Int64" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.SByte@,System.Int32@,System.Char)">
      <summary>Parses an <see cref="T:System.SByte" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.Single@,System.Int32@,System.Char)">
      <summary>Parses a <see cref="T:System.Single" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.TimeSpan@,System.Int32@,System.Char)">
      <summary>Parses a <see cref="T:System.TimeSpan" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.UInt16@,System.Int32@,System.Char)">
      <summary>Parses a <see cref="T:System.UInt16" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.UInt32@,System.Int32@,System.Char)">
      <summary>Parses a <see cref="T:System.UInt32" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="M:System.Buffers.Text.Utf8Parser.TryParse(System.ReadOnlySpan{System.Byte},System.UInt64@,System.Int32@,System.Char)">
      <summary>Parses a <see cref="T:System.UInt64" /> at the start of a Utf8 string.</summary>
      <param name="source">The Utf8 string to parse.</param>
      <param name="value">When the method returns, contains the value parsed from <paramref name="source" />, if the parsing operation succeeded.</param>
      <param name="bytesConsumed">If the parsing operation was successful, contains the length in bytes of the parsed substring when the method returns. If the method fails, <paramref name="bytesConsumed" /> is set to 0.</param>
      <param name="standardFormat">The expected format of the Utf8 string.</param>
      <returns>
        <see langword="true" /> for success; <see langword="false" /> if the string was not syntactically valid or an overflow or underflow occurred.</returns>
    </member>
    <member name="T:System.MemoryExtensions">
      <summary>Provides extension methods for for the memory- and span-related types, such as <see cref="T:System.Memory`1" />, <see cref="T:System.ReadOnlyMemory`1" />, <see cref="T:System.Span`1" />, and <see cref="T:System.ReadOnlySpan`1" />.</summary>
    </member>
    <member name="M:System.MemoryExtensions.AsMemory(System.String)">
      <summary>Creates a new <see langword="ReadOnlyMemory&lt;Char&gt;" /> over the portion of the target string.</summary>
      <param name="text">The target string.</param>
      <returns>The read-only character memory representation of the string, or <see langword="default" /> if <paramref name="text" /> is <see langword="null" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.AsMemory(System.String,System.Index)">
      <summary>Creates a new <see langword="ReadOnlyMemory&lt;Char&gt;" /> over a portion of the target string starting at a specified index.</summary>
      <param name="text">The target string.</param>
      <param name="startIndex">The index at which to begin this slice.</param>
      <returns>The read-only character memory representation of the string.</returns>
    </member>
    <member name="M:System.MemoryExtensions.AsMemory(System.String,System.Int32)">
      <summary>Creates a new <see langword="ReadOnlyMemory&lt;Char&gt;" /> over a portion of the target string starting at a specified character position.</summary>
      <param name="text">The target string.</param>
      <param name="start">The index at which to begin this slice.</param>
      <returns>The read-only character memory representation of the string, or <see langword="default" /> if <paramref name="text" /> is <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="start" /> is not in the range of <paramref name="text" /> (<paramref name="start" /> is &lt; 0 or &gt; <c>text.Length</c>).</exception>
    </member>
    <member name="M:System.MemoryExtensions.AsMemory(System.String,System.Int32,System.Int32)">
      <summary>Creates a new <see langword="ReadOnlyMemory&lt;Char&gt;" /> over a portion of the target string beginning at a specified position with a length.</summary>
      <param name="text">The target string.</param>
      <param name="start">The index at which to begin this slice.</param>
      <param name="length">The desired length for the slice.</param>
      <returns>The read-only character memory representation of the string, or <see langword="default" /> if <paramref name="text" /> is <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="start" />, <paramref name="length" />, or <paramref name="start" /> + <paramref name="length" /> is not in the range of <paramref name="text" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.AsMemory(System.String,System.Range)">
      <summary>Creates a new <see langword="ReadOnlyMemory&lt;Char&gt;" /> over a specified range of the target string.</summary>
      <param name="text">The target string.</param>
      <param name="range">The range that indicates the start and length of the sliced string.</param>
      <returns>The read-only character memory representation of the string.</returns>
    </member>
    <member name="M:System.MemoryExtensions.AsMemory``1(``0[])">
      <summary>Creates a new memory region over the target array.</summary>
      <param name="array">The array to convert.</param>
      <typeparam name="T">The type of the array.</typeparam>
      <returns>The memory representation of the whole or part of the array.</returns>
    </member>
    <member name="M:System.MemoryExtensions.AsMemory``1(``0[],System.Index)">
      <summary>Creates a new memory region over the portion of the target array starting at a specified index to the end of the array.</summary>
      <param name="array">The array to convert.</param>
      <param name="startIndex">The first position of the array.</param>
      <typeparam name="T">The type of the array.</typeparam>
      <returns>The memory representation of the whole or part of the array.</returns>
    </member>
    <member name="M:System.MemoryExtensions.AsMemory``1(``0[],System.Int32)">
      <summary>Creates a new memory region over the portion of the target array starting at a specified position to the end of the array.</summary>
      <param name="array">The target array.</param>
      <param name="start">The index at which to begin the memory.</param>
      <typeparam name="T">The type of the array.</typeparam>
      <returns>The memory representation of the whole or part of the array.</returns>
      <exception cref="T:System.ArrayTypeMismatchException">
        <paramref name="array" /> is covariant, and the array's type is not exactly <see langword="T[]" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="start" /> index less than 0 or greater than <see langword="array.Length" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.AsMemory``1(``0[],System.Int32,System.Int32)">
      <summary>Creates a new memory region over the portion of the target array beginning at a specified position with a specified length.</summary>
      <param name="array">The target array.</param>
      <param name="start">The index at which to begin the memory region.</param>
      <param name="length">The number of items in the memory region.</param>
      <typeparam name="T">The type of the array.</typeparam>
      <returns>The memory representation of the whole or part of the array.</returns>
      <exception cref="T:System.ArrayTypeMismatchException">
        <paramref name="array" /> is covariant, and the array's type is not exactly <see langword="T[]" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="start" />, <paramref name="length" />, or <paramref name="start" /> + <paramref name="length" /> is not in the range of <paramref name="array" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.AsMemory``1(``0[],System.Range)">
      <summary>Creates a new memory region over the portion of the target array beginning at inclusive start index of the range and ending at the exclusive end index of the range.</summary>
      <param name="array">The array to convert.</param>
      <param name="range">The range to convert from the array.</param>
      <typeparam name="T">The type of the array.</typeparam>
      <returns>The memory representation of the whole or part of the array.</returns>
    </member>
    <member name="M:System.MemoryExtensions.AsMemory``1(System.ArraySegment{``0})">
      <summary>Creates a new memory region over the portion of the target array segment.</summary>
      <param name="segment">The segment to convert.</param>
      <typeparam name="T">The type of the segment.</typeparam>
      <returns>The memory representation of the segment.</returns>
    </member>
    <member name="M:System.MemoryExtensions.AsMemory``1(System.ArraySegment{``0},System.Int32)">
      <summary>Creates a new memory region over the portion of the target array segment starting at a specified position to the end of the segment.</summary>
      <param name="segment">The target array segment.</param>
      <param name="start">The index at which to begin the memory.</param>
      <typeparam name="T">The type of the array.</typeparam>
      <returns>The memory representation of the whole or part of the array.</returns>
      <exception cref="T:System.ArrayTypeMismatchException">
        <paramref name="segment" /> is covariant, and the type of <paramref name="segment" /> is not exactly <see langword="T[]" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="start" /> is less than 0 or greater than <see langword="segment.Count" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.AsMemory``1(System.ArraySegment{``0},System.Int32,System.Int32)">
      <summary>Creates a new memory region over the portion of the target array segment beginning at a specified position with a specified length.</summary>
      <param name="segment">The target array segment.</param>
      <param name="start">The index at which to begin the memory.</param>
      <param name="length">The number of items in the memory.</param>
      <typeparam name="T">The type of the array.</typeparam>
      <returns>The memory representation of the whole or part of the array.</returns>
      <exception cref="T:System.ArrayTypeMismatchException">
        <paramref name="segment" /> is covariant, and the array's type is not exactly <see langword="T[]" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="start" />, <paramref name="length" />, or <paramref name="start" /> + <paramref name="length" /> is not in the range of <paramref name="segment" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.AsSpan(System.String)">
      <summary>Creates a new read-only span over a portion of the target string from a specified position for a specified number of characters.</summary>
      <param name="text">The target string.</param>
      <returns>The read-only span representation of the string.</returns>
    </member>
    <member name="M:System.MemoryExtensions.AsSpan(System.String,System.Int32)">
      <summary>Creates a new read-only span over a portion of the target string from a specified position to the end of the string.</summary>
      <param name="text">The target string.</param>
      <param name="start">The index at which to begin this slice.</param>
      <returns>The read-only span representation of the string.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="text" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="start" /> is less than 0 or greater than <see langword="text.Length" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.AsSpan(System.String,System.Int32,System.Int32)">
      <summary>Creates a new read-only span over a string.</summary>
      <param name="text">The target string.</param>
      <param name="start">The index at which to begin this slice.</param>
      <param name="length">The desired length for the slice.</param>
      <returns>The read-only span representation of the string.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="start" />, <paramref name="length" />, or <paramref name="start" /> + <paramref name="length" /> is not in the range of <paramref name="text" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.AsSpan``1(``0[])">
      <summary>Creates a new span over a target array.</summary>
      <param name="array">The array to convert.</param>
      <typeparam name="T">The type of the array.</typeparam>
      <returns>The span representation of the array.</returns>
    </member>
    <member name="M:System.MemoryExtensions.AsSpan``1(``0[],System.Index)">
      <summary>Creates a new span over the portion of the target array defined by an <see cref="T:System.Index" /> value.</summary>
      <param name="array">The array to convert.</param>
      <param name="startIndex">The starting index.</param>
      <typeparam name="T">The array type.</typeparam>
      <returns>The span representation of the array.</returns>
    </member>
    <member name="M:System.MemoryExtensions.AsSpan``1(``0[],System.Int32)">
      <summary>Creates a new span over a portion of the target array starting at a specified position to the end of the array.</summary>
      <param name="array">The array to convert.</param>
      <param name="start">The initial index from which the array will be converted.</param>
      <typeparam name="T">The type of the array.</typeparam>
      <returns>The span representation of the array.</returns>
    </member>
    <member name="M:System.MemoryExtensions.AsSpan``1(``0[],System.Int32,System.Int32)">
      <summary>Creates a new span over the portion of the target array beginning at a specified position for a specified length.</summary>
      <param name="array">The target array.</param>
      <param name="start">The index at which to begin the span.</param>
      <param name="length">The number of items in the span.</param>
      <typeparam name="T">The type of the array.</typeparam>
      <returns>The span representation of the array.</returns>
      <exception cref="T:System.ArrayTypeMismatchException">
        <paramref name="array" /> is covariant, and the array's type is not exactly <see langword="T[]" />".</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="start" />, <paramref name="length" />, or <paramref name="start" /> + <paramref name="length" /> is not in the range of <paramref name="text" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.AsSpan``1(``0[],System.Range)">
      <summary>Creates a new span over a portion of a target array defined by a <see cref="T:System.Range" /> value.</summary>
      <param name="array">The array to convert.</param>
      <param name="range">The range of the array to convert.</param>
      <typeparam name="T">The type of the array.</typeparam>
      <returns>The span representation of the array.</returns>
    </member>
    <member name="M:System.MemoryExtensions.AsSpan``1(System.ArraySegment{``0})">
      <summary>Creates a new span over a target array segment.</summary>
      <param name="segment">The array segment to convert.</param>
      <typeparam name="T">The type of the array segment.</typeparam>
      <returns>The span representation of the array segment.</returns>
    </member>
    <member name="M:System.MemoryExtensions.AsSpan``1(System.ArraySegment{``0},System.Index)">
      <summary>Creates a new span over a portion of the target array segment beginning at a specified index and ending at the end of the segment.</summary>
      <param name="segment">The target array segment.</param>
      <param name="startIndex">The index at which to begin the Span.</param>
      <typeparam name="T">The type of the array segment.</typeparam>
      <returns>The span representation of the array segment.</returns>
    </member>
    <member name="M:System.MemoryExtensions.AsSpan``1(System.ArraySegment{``0},System.Int32)">
      <summary>Creates a new span over a portion of a target array segment from a specified position to the end of the segment.</summary>
      <param name="segment">The target array segment.</param>
      <param name="start">The index at which to begin the san.</param>
      <typeparam name="T">The type of the array segment.</typeparam>
      <returns>The span representation of the array segment.</returns>
      <exception cref="T:System.ArrayTypeMismatchException">
        <paramref name="segment" /> is covariant, and the array's type is not exactly <see langword="T[]" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="start" /> is less than 0 or greater than <see langword="segment.Count" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.AsSpan``1(System.ArraySegment{``0},System.Int32,System.Int32)">
      <summary>Creates a new span over a portion of a target array segment from a specified position for a specified length.</summary>
      <param name="segment">The target array segment.</param>
      <param name="start">The index at which to begin the span.</param>
      <param name="length">The number of items in the span.</param>
      <typeparam name="T">The type of the array segment.</typeparam>
      <returns>The span representation of the array.</returns>
      <exception cref="T:System.ArrayTypeMismatchException">
        <paramref name="segment" /> is covariant, and the array's type is not exactly <see langword="T[]" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="start" />, <paramref name="length" />, or <paramref name="start" /> + <paramref name="length" /> is not in the range of <paramref name="segment" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.AsSpan``1(System.ArraySegment{``0},System.Range)">
      <summary>Creates a new span over a portion of a target array segment using the range start and end indexes.</summary>
      <param name="segment">The target array segment.</param>
      <param name="range">The range which has start and end indexes to use for slicing the array.</param>
      <typeparam name="T">The type of the array segment.</typeparam>
      <returns>The span representation of the array segment.</returns>
    </member>
    <member name="M:System.MemoryExtensions.BinarySearch``1(System.ReadOnlySpan{``0},System.IComparable{``0})">
      <summary>Searches an entire sorted <see cref="T:System.ReadOnlySpan`1" /> for a value using the specified <see cref="T:System.IComparable`1" /> generic interface.</summary>
      <param name="span">The sorted <see cref="T:System.ReadOnlySpan`1" /> to search.</param>
      <param name="comparable">The <see cref="T:System.IComparable`1" /> to use when comparing.</param>
      <typeparam name="T">The element type of the span.</typeparam>
      <returns>The zero-based index of <paramref name="comparable" /> in the sorted <paramref name="span" />, if <paramref name="comparable" /> is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="comparable" /> or, if there is no larger element, the bitwise complement of <see cref="P:System.ReadOnlySpan`1.Length" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparable" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.BinarySearch``1(System.Span{``0},System.IComparable{``0})">
      <summary>Searches an entire sorted <see cref="T:System.Span`1" /> for a value using the specified <see cref="T:System.IComparable`1" /> generic interface.</summary>
      <param name="span">The sorted <see cref="T:System.Span`1" /> to search.</param>
      <param name="comparable">The <see cref="T:System.IComparable`1" /> to use when comparing.</param>
      <typeparam name="T">The element type of the span.</typeparam>
      <returns>The zero-based index of <paramref name="comparable" /> in the sorted <paramref name="span" />, if <paramref name="comparable" /> is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="comparable" /> or, if there is no larger element, the bitwise complement of <see cref="P:System.Span`1.Length" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparable" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.BinarySearch``2(System.ReadOnlySpan{``0},``0,``1)">
      <summary>Searches an entire sorted <see cref="T:System.ReadOnlySpan`1" /> for a specified value using the specified <typeparamref name="TComparer" /> generic type.</summary>
      <param name="span">The sorted <see cref="T:System.ReadOnlySpan`1" /> to search.</param>
      <param name="value">The object to locate. The value can be <see langword="null" /> for reference types.</param>
      <param name="comparer">The <typeparamref name="TComparer" /> to use when comparing.</param>
      <typeparam name="T">The element type of the span.</typeparam>
      <typeparam name="TComparer">The specific type of <see cref="T:System.Collections.Generic.IComparer`1" />.</typeparam>
      <returns>The zero-based index of <paramref name="value" /> in the sorted <paramref name="span" />, if <paramref name="value" /> is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="value" /> or, if there is no larger element, the bitwise complement of <see cref="P:System.ReadOnlySpan`1.Length" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.BinarySearch``2(System.ReadOnlySpan{``0},``1)">
      <summary>Searches an entire sorted <see cref="T:System.ReadOnlySpan`1" /> for a value using the specified <typeparamref name="TComparable" /> generic type.</summary>
      <param name="span">The sorted <see cref="T:System.ReadOnlySpan`1" /> to search.</param>
      <param name="comparable">The <typeparamref name="TComparable" /> to use when comparing.</param>
      <typeparam name="T">The element type of the span.</typeparam>
      <typeparam name="TComparable">The specific type of <see cref="T:System.IComparable`1" />.</typeparam>
      <returns>The zero-based index of <paramref name="comparable" /> in the sorted <paramref name="span" />, if <paramref name="comparable" /> is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="comparable" /> or, if there is no larger element, the bitwise complement of <see cref="P:System.ReadOnlySpan`1.Length" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparable" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.BinarySearch``2(System.Span{``0},``0,``1)">
      <summary>Searches an entire sorted <see cref="T:System.Span`1" /> for a specified value using the specified <typeparamref name="TComparer" /> generic type.</summary>
      <param name="span">The sorted <see cref="T:System.Span`1" /> to search.</param>
      <param name="value">The object to locate. The value can be <see langword="null" /> for reference types.</param>
      <param name="comparer">The <typeparamref name="TComparer" /> to use when comparing.</param>
      <typeparam name="T">The element type of the span.</typeparam>
      <typeparam name="TComparer">The specific type of <see cref="T:System.Collections.Generic.IComparer`1" />.</typeparam>
      <returns>The zero-based index of <paramref name="value" /> in the sorted <paramref name="span" />, if <paramref name="value" /> is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="value" /> or, if there is no larger element, the bitwise complement of <see cref="P:System.Span`1.Length" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.BinarySearch``2(System.Span{``0},``1)">
      <summary>Searches an entire sorted <see cref="T:System.Span`1" /> for a value using the specified <typeparamref name="TComparable" /> generic type.</summary>
      <param name="span">The sorted <see cref="T:System.Span`1" /> to search.</param>
      <param name="comparable">The <typeparamref name="TComparable" /> to use when comparing.</param>
      <typeparam name="T">The element type of the span.</typeparam>
      <typeparam name="TComparable">The specific type of <see cref="T:System.IComparable`1" />.</typeparam>
      <returns>The zero-based index of <paramref name="comparable" /> in the sorted <paramref name="span" />, if <paramref name="comparable" /> is found; otherwise, a negative number that is the bitwise complement of the index of the next element that is larger than <paramref name="comparable" /> or, if there is no larger element, the bitwise complement of <see cref="P:System.Span`1.Length" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparable" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.MemoryExtensions.CompareTo(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.StringComparison)">
      <summary>Compares one character span with another using a specified string comparison, and returns an integer that indicates their relative position in the sort order.</summary>
      <param name="span">The source span.</param>
      <param name="other">The value to compare with the source span.</param>
      <param name="comparisonType">An enumeration value that determines how <paramref name="span" /> and <paramref name="other" /> are compared.</param>
      <returns>A signed integer that indicates the relative order of <paramref name="span" /> and <paramref name="other" />:   - If less than 0, <paramref name="span" /> precedes than <paramref name="other" />.   - If 0, <paramref name="span" /> equals <paramref name="other" />.   - If greater than 0, <paramref name="span" /> follows <paramref name="other" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Contains(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.StringComparison)">
      <summary>Indicates whether a specified value occurs within a read-only character span.</summary>
      <param name="span">The source span.</param>
      <param name="value">The value to seek within the source span.</param>
      <param name="comparisonType">An enumeration value that determines how the characters in <paramref name="span" /> and <paramref name="value" /> are compared.</param>
      <returns>
        <see langword="true" /> if <paramref name="value" /> occurs within the span, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Contains``1(System.ReadOnlySpan{``0},``0)">
      <summary>Indicates whether a specified value is found in a read-only span. Values are compared using IEquatable{T}.Equals(T).</summary>
      <param name="span">The span to search.</param>
      <param name="value">The value to search for.</param>
      <typeparam name="T">The type of the span.</typeparam>
      <returns>
        <see langword="true" /> if found, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Contains``1(System.Span{``0},``0)">
      <summary>Indicates whether a specified value is found in a span. Values are compared using IEquatable{T}.Equals(T).</summary>
      <param name="span">The span to search.</param>
      <param name="value">The value to search for.</param>
      <typeparam name="T">The type of the elements in the span.</typeparam>
      <returns>
        <see langword="true" /> if found, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.MemoryExtensions.CopyTo``1(``0[],System.Memory{``0})">
      <summary>Copies the contents of the array into a memory region.</summary>
      <param name="source">The array to copy items from.</param>
      <param name="destination">The memory to copy items into.</param>
      <typeparam name="T">The type of the array.</typeparam>
      <exception cref="T:System.ArgumentException">The destination is shorter than the source array.</exception>
    </member>
    <member name="M:System.MemoryExtensions.CopyTo``1(``0[],System.Span{``0})">
      <summary>Copies the contents of the array into the span.</summary>
      <param name="source">The array to copy items from.</param>
      <param name="destination">The span to copy items into.</param>
      <typeparam name="T">The type of the array.</typeparam>
      <exception cref="T:System.ArgumentException">The destination Span is shorter than the source array.</exception>
    </member>
    <member name="M:System.MemoryExtensions.EndsWith(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.StringComparison)">
      <summary>Determines whether the end of the <paramref name="span" /> matches the specified <paramref name="value" /> when compared using the specified <paramref name="comparisonType" /> option.</summary>
      <param name="span">The source span.</param>
      <param name="value">The sequence to compare to the end of the source span.</param>
      <param name="comparisonType">An enumeration value that determines how <paramref name="span" /> and <paramref name="value" /> are compared.</param>
      <returns>
        <see langword="true" /> if <paramref name="value" /> matches the end of <paramref name="span" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.EndsWith``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <summary>Determines whether the specified sequence appears at the end of a read-only span.</summary>
      <param name="span">The source span.</param>
      <param name="value">The sequence to compare to the end of the source span.</param>
      <typeparam name="T">The type of the span.</typeparam>
      <returns>
        <see langword="true" /> if <paramref name="value" /> matches the end of <paramref name="span" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.EndsWith``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <summary>Determines whether the specified sequence appears at the end of a span.</summary>
      <param name="span">The source span.</param>
      <param name="value">The sequence to compare to the end of the source span.</param>
      <typeparam name="T">The type of the span.</typeparam>
      <returns>
        <see langword="true" /> if <paramref name="value" /> matches the end of <paramref name="span" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.EnumerateRunes(System.ReadOnlySpan{System.Char})">
      <summary>Returns an enumeration of <see cref="T:System.Text.Rune" /> from the provided read-only span.</summary>
      <param name="span">The source span.</param>
      <returns>A rune enumerator.</returns>
    </member>
    <member name="M:System.MemoryExtensions.EnumerateRunes(System.Span{System.Char})">
      <summary>Returns an enumeration of <see cref="T:System.Text.Rune" /> from the provided span.</summary>
      <param name="span">The source span.</param>
      <returns>A rune enumerator.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Equals(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.StringComparison)">
      <summary>Determines whether this <paramref name="span" /> and the specified <paramref name="other" /> span have the same characters when compared using the specified <paramref name="comparisonType" /> option.</summary>
      <param name="span">The source span.</param>
      <param name="other">The value to compare with the source span.</param>
      <param name="comparisonType">An enumeration value that determines how <paramref name="span" /> and <paramref name="other" /> are compared.</param>
      <returns>
        <see langword="true" /> if equal, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.MemoryExtensions.IndexOf(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.StringComparison)">
      <summary>Reports the zero-based index of the first occurrence of the specified <paramref name="value" /> in the current <paramref name="span" />.</summary>
      <param name="span">The source span.</param>
      <param name="value">The value to seek within the source span.</param>
      <param name="comparisonType">An enumeration value that determines how <paramref name="span" /> and <paramref name="value" /> are compared.</param>
      <returns>The index of the occurrence of the value in the span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.IndexOf``1(System.ReadOnlySpan{``0},``0)">
      <summary>Searches for the specified value and returns the index of its first occurrence. Values are compared using IEquatable{T}.Equals(T).</summary>
      <param name="span">The span to search.</param>
      <param name="value">The value to search for.</param>
      <typeparam name="T">The type of the span and value.</typeparam>
      <returns>The index of the occurrence of the value in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.IndexOf``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <summary>Searches for the specified sequence and returns the index of its first occurrence. Values are compared using IEquatable{T}.Equals(T).</summary>
      <param name="span">The span to search.</param>
      <param name="value">The sequence to search for.</param>
      <typeparam name="T">The type of the span and value.</typeparam>
      <returns>The index of the occurrence of the value in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.IndexOf``1(System.Span{``0},``0)">
      <summary>Searches for the specified value and returns the index of its first occurrence. Values are compared using IEquatable{T}.Equals(T).</summary>
      <param name="span">The span to search.</param>
      <param name="value">The value to search for.</param>
      <typeparam name="T">The type of the span and value.</typeparam>
      <returns>The index of the occurrence of the value in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.IndexOf``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <summary>Searches for the specified sequence and returns the index of its first occurrence. Values are compared using IEquatable{T}.Equals(T).</summary>
      <param name="span">The span to search.</param>
      <param name="value">The sequence to search for.</param>
      <typeparam name="T">The type of the span and value..</typeparam>
      <returns>The index of the occurrence of the value in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.IndexOfAny``1(System.ReadOnlySpan{``0},``0,``0)">
      <summary>Searches for the first index of any of the specified values similar to calling IndexOf several times with the logical OR operator.</summary>
      <param name="span">The span to search.</param>
      <param name="value0">One of the values to search for.</param>
      <param name="value1">One of the values to search for.</param>
      <typeparam name="T">The type of the span and values.</typeparam>
      <returns>The first index of the occurrence of any of the values in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.IndexOfAny``1(System.ReadOnlySpan{``0},``0,``0,``0)">
      <summary>Searches for the first index of any of the specified values similar to calling IndexOf several times with the logical OR operator.</summary>
      <param name="span">The span to search.</param>
      <param name="value0">One of the values to search for.</param>
      <param name="value1">One of the values to search for.</param>
      <param name="value2">One of the values to search for.</param>
      <typeparam name="T">The type of the span and values.</typeparam>
      <returns>The first index of the occurrence of any of the values in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.IndexOfAny``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <summary>Searches for the first index of any of the specified values similar to calling IndexOf several times with the logical OR operator.</summary>
      <param name="span">The span to search.</param>
      <param name="values">The set of values to search for.</param>
      <typeparam name="T">The type of the span and values.</typeparam>
      <returns>The first index of the occurrence of any of the values in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.IndexOfAny``1(System.Span{``0},``0,``0)">
      <summary>Searches for the first index of any of the specified values similar to calling IndexOf several times with the logical OR operator.</summary>
      <param name="span">The span to search.</param>
      <param name="value0">One of the values to search for.</param>
      <param name="value1">One of the values to search for.</param>
      <typeparam name="T">The type of the span and values.</typeparam>
      <returns>The first index of the occurrence of any of the values in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.IndexOfAny``1(System.Span{``0},``0,``0,``0)">
      <summary>Searches for the first index of any of the specified values similar to calling IndexOf several times with the logical OR operator.</summary>
      <param name="span">The span to search.</param>
      <param name="value0">One of the values to search for.</param>
      <param name="value1">One of the values to search for.</param>
      <param name="value2">One of the values to search for.</param>
      <typeparam name="T">The type of the span and values.</typeparam>
      <returns>The first index of the occurrence of any of the values in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.IndexOfAny``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <summary>Searches for the first index of any of the specified values similar to calling IndexOf several times with the logical OR operator.</summary>
      <param name="span">The span to search.</param>
      <param name="values">The set of values to search for.</param>
      <typeparam name="T">The type of the span and values.</typeparam>
      <returns>The first index of the occurrence of any of the values in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.IsWhiteSpace(System.ReadOnlySpan{System.Char})">
      <summary>Indicates whether the specified span contains only whitespace characters.</summary>
      <param name="span">The source span.</param>
      <returns>
        <see langword="true" /> if the span contains only whitespace characters, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.MemoryExtensions.LastIndexOf(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.StringComparison)">
      <summary>Reports the zero-based index of the last occurrence of the specified <paramref name="value" /> in the current <paramref name="span" />.</summary>
      <param name="span">The source span.</param>
      <param name="value">The value to seek within the source span.</param>
      <param name="comparisonType">An enumeration value that determines how <paramref name="span" /> and <paramref name="value" /> are compared.</param>
      <returns>The index of the last occurrence of the value in the span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.LastIndexOf``1(System.ReadOnlySpan{``0},``0)">
      <summary>Searches for the specified value and returns the index of its last occurrence. Values are compared using IEquatable{T}.Equals(T).</summary>
      <param name="span">The span to search.</param>
      <param name="value">The value to search for.</param>
      <typeparam name="T">The type of the span and value.</typeparam>
      <returns>The index of the last occurrence of the value in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.LastIndexOf``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <summary>Searches for the specified sequence and returns the index of its last occurrence. Values are compared using IEquatable{T}.Equals(T).</summary>
      <param name="span">The span to search.</param>
      <param name="value">The sequence to search for.</param>
      <typeparam name="T">The type of the span and value.</typeparam>
      <returns>The index of the last occurrence of the value in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.LastIndexOf``1(System.Span{``0},``0)">
      <summary>Searches for the specified value and returns the index of its last occurrence. Values are compared using IEquatable{T}.Equals(T).</summary>
      <param name="span">The span to search.</param>
      <param name="value">The value to search for.</param>
      <typeparam name="T">The type of the span and value.</typeparam>
      <returns>The index of the last occurrence of the value in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.LastIndexOf``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <summary>Searches for the specified sequence and returns the index of its last occurrence. Values are compared using IEquatable{T}.Equals(T).</summary>
      <param name="span">The span to search.</param>
      <param name="value">The sequence to search for.</param>
      <typeparam name="T">The type of the span and value.</typeparam>
      <returns>The index of the last occurrence of the value in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.LastIndexOfAny``1(System.ReadOnlySpan{``0},``0,``0)">
      <summary>Searches for the last index of any of the specified values similar to calling LastIndexOf several times with the logical OR operator.</summary>
      <param name="span">The span to search.</param>
      <param name="value0">One of the values to search for.</param>
      <param name="value1">One of the values to search for.</param>
      <typeparam name="T">The type of the span and values.</typeparam>
      <returns>The index of the last occurrence of any of the values in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.LastIndexOfAny``1(System.ReadOnlySpan{``0},``0,``0,``0)">
      <summary>Searches for the last index of any of the specified values similar to calling LastIndexOf several times with the logical OR operator.</summary>
      <param name="span">The span to search.</param>
      <param name="value0">One of the values to search for.</param>
      <param name="value1">One of the values to search for.</param>
      <param name="value2">One of the values to search for.</param>
      <typeparam name="T">The type of the span and values.</typeparam>
      <returns>The index of the last occurrence of any of the values in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.LastIndexOfAny``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <summary>Searches for the last index of any of the specified values similar to calling LastIndexOf several times with the logical OR operator.</summary>
      <param name="span">The span to search.</param>
      <param name="values">The set of values to search for.</param>
      <typeparam name="T">The type of the span and values.</typeparam>
      <returns>The index of the last occurrence of any of the values in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.LastIndexOfAny``1(System.Span{``0},``0,``0)">
      <summary>Searches for the last index of any of the specified values similar to calling LastIndexOf several times with the logical OR operator.</summary>
      <param name="span">The span to search.</param>
      <param name="value0">One of the values to search for.</param>
      <param name="value1">One of the values to search for.</param>
      <typeparam name="T">The type of the span and values.</typeparam>
      <returns>The index of the last occurrence of any of the values in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.LastIndexOfAny``1(System.Span{``0},``0,``0,``0)">
      <summary>Searches for the last index of any of the specified values similar to calling LastIndexOf several times with the logical OR operator.</summary>
      <param name="span">The span to search.</param>
      <param name="value0">One of the values to search for.</param>
      <param name="value1">One of the values to search for.</param>
      <param name="value2">One of the values to search for.</param>
      <typeparam name="T">The type of the span and values.</typeparam>
      <returns>The index of the last occurrence of any of the values in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.LastIndexOfAny``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <summary>Searches for the last index of any of the specified values similar to calling LastIndexOf several times with the logical OR operator.</summary>
      <param name="span">The span to search.</param>
      <param name="values">The set of values to search for.</param>
      <typeparam name="T">The type of the span and values.</typeparam>
      <returns>The index of the last occurrence of any of the values in the span. If not found, returns -1.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Overlaps``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <summary>Determines whether two read-only sequences overlap in memory.</summary>
      <param name="span">The first sequence.</param>
      <param name="other">The second sequence.</param>
      <typeparam name="T">The type of elmeents in the read-only sequence.</typeparam>
      <returns>
        <see langword="true" /> if the two sequences overlap; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Overlaps``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0},System.Int32@)">
      <summary>Determines whether two read-only sequences overlap in memory and outputs the element offset.</summary>
      <param name="span">The first sequence.</param>
      <param name="other">The second sequence.</param>
      <param name="elementOffset" />
      <typeparam name="T">The type of elements in the span.</typeparam>
      <returns>
        <see langword="true" /> if the two sequences overlap; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Overlaps``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <summary>Determines whether a span and a read-only span overlap in memory.</summary>
      <param name="span">The span to compare.</param>
      <param name="other">The read-only span to compare.</param>
      <typeparam name="T">The type of elements in the span.</typeparam>
      <returns>
        <see langword="true" /> if the two sequences overlap; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Overlaps``1(System.Span{``0},System.ReadOnlySpan{``0},System.Int32@)">
      <summary>Determines whether a span and a read-only span overlap in memory and outputs the element offset.</summary>
      <param name="span">The first sequence to compare.</param>
      <param name="other">The second sequence to compare.</param>
      <param name="elementOffset">When the method returns, contains the offset between <paramref name="span" /> and <paramref name="other" />.</param>
      <typeparam name="T">The type of elements in the span.</typeparam>
      <returns>
        <see langword="true" /> if the two sequences overlap; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Reverse``1(System.Span{``0})">
      <summary>Reverses the sequence of the elements in the entire span.</summary>
      <param name="span">The span to reverse.</param>
      <typeparam name="T">The type of elements in the span.</typeparam>
    </member>
    <member name="M:System.MemoryExtensions.SequenceCompareTo``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <summary>Determines the relative order of two read-only sequences by comparing their elements using IComparable{T}.CompareTo(T).</summary>
      <param name="span">The first sequence to compare.</param>
      <param name="other">The second sequence to compare.</param>
      <typeparam name="T">The type of elements in the sequence.</typeparam>
      <returns>A signed integer that indicates the relative order of <paramref name="span" /> and <paramref name="other" />:   - If less than 0, <paramref name="span" /> precedes than <paramref name="other" />.   - If 0, <paramref name="span" /> equals <paramref name="other" />.   - If greater than 0, <paramref name="span" /> follows <paramref name="other" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.SequenceCompareTo``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <summary>Determines the relative order of a span and a read-only span by comparing the elements using IComparable{T}.CompareTo(T).</summary>
      <param name="span">The span to compare.</param>
      <param name="other">The read-only span to compare.</param>
      <typeparam name="T">The type of elements in the span.</typeparam>
      <returns>A signed integer that indicates the relative order of <paramref name="span" /> and <paramref name="other" />:   - If less than 0, <paramref name="span" /> precedes than <paramref name="other" />.   - If 0, <paramref name="span" /> equals <paramref name="other" />.   - If greater than 0, <paramref name="span" /> follows <paramref name="other" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.SequenceEqual``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <summary>Determines whether two read-only sequences are equal by comparing the elements using IEquatable{T}.Equals(T).</summary>
      <param name="span">The first sequence to compare.</param>
      <param name="other">The second sequence to compare.</param>
      <typeparam name="T">The type of elements in the sequence.</typeparam>
      <returns>
        <see langword="true" /> if the two sequences are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.SequenceEqual``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <summary>Determines whether a span and a read-only span are equal by comparing the elements using IEquatable{T}.Equals(T).</summary>
      <param name="span">The span to compare.</param>
      <param name="other">The read-only span to compare.</param>
      <typeparam name="T">The type of elements in the sequence.</typeparam>
      <returns>
        <see langword="true" /> if the two sequences are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.StartsWith(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.StringComparison)">
      <summary>Determines whether a read-only character span begins with a specified value when compared using a specified <see cref="T:System.StringComparison" /> value.</summary>
      <param name="span">The source span.</param>
      <param name="value">The sequence to compare to the beginning of the source span.</param>
      <param name="comparisonType">An enumeration value that determines how <paramref name="span" /> and <paramref name="value" /> are compared.</param>
      <returns>
        <see langword="true" /> if <paramref name="value" /> matches the beginning of <paramref name="span" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.StartsWith``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <summary>Determines whether a specified sequence appears at the start of a read-only span.</summary>
      <param name="span">The read-only character span to search.</param>
      <param name="value">A sequence to search for at the start of <paramref name="span" />.</param>
      <typeparam name="T">The type of elements in the span.</typeparam>
      <returns>
        <see langword="true" /> if <paramref name="value" /> matches the beginning of <paramref name="span" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.StartsWith``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <summary>Determines whether a specified sequence appears at the start of a span.</summary>
      <param name="span">The span to search.</param>
      <param name="value">A sequence to search for at the start of <paramref name="span" />.</param>
      <typeparam name="T">The type of the elements in the span.</typeparam>
      <returns>
        <see langword="true" /> if <paramref name="value" /> matches the beginning of <paramref name="span" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.MemoryExtensions.ToLower(System.ReadOnlySpan{System.Char},System.Span{System.Char},System.Globalization.CultureInfo)">
      <summary>Copies the characters from the source span into the destination, converting each character to lowercase, using the casing rules of the specified culture.</summary>
      <param name="source">The source span.</param>
      <param name="destination">The destination span which contains the transformed characters.</param>
      <param name="culture">An object that supplies culture-specific casing rules.</param>
      <returns>The number of characters written into the destination span. If the destination is too small, returns -1.</returns>
      <exception cref="T:System.InvalidOperationException">The source and destination buffers overlap.</exception>
    </member>
    <member name="M:System.MemoryExtensions.ToLowerInvariant(System.ReadOnlySpan{System.Char},System.Span{System.Char})">
      <summary>Copies the characters from the source span into the destination, converting each character to lowercase, using the casing rules of the invariant culture.</summary>
      <param name="source">The source span.</param>
      <param name="destination">The destination span which contains the transformed characters.</param>
      <returns>The number of characters written into the destination span. If the destination is too small, returns -1.</returns>
      <exception cref="T:System.InvalidOperationException">The source and destination buffers overlap.</exception>
    </member>
    <member name="M:System.MemoryExtensions.ToUpper(System.ReadOnlySpan{System.Char},System.Span{System.Char},System.Globalization.CultureInfo)">
      <summary>Copies the characters from the source span into the destination, converting each character to uppercase, using the casing rules of the specified culture.</summary>
      <param name="source">The source span.</param>
      <param name="destination">The destination span which contains the transformed characters.</param>
      <param name="culture">An object that supplies culture-specific casing rules.</param>
      <returns>The number of characters written into the destination span. If the destination is too small, returns -1.</returns>
      <exception cref="T:System.InvalidOperationException">The source and destination buffers overlap.</exception>
    </member>
    <member name="M:System.MemoryExtensions.ToUpperInvariant(System.ReadOnlySpan{System.Char},System.Span{System.Char})">
      <summary>Copies the characters from the source span into the destination, converting each character to uppercase using the casing rules of the invariant culture.</summary>
      <param name="source">The source span.</param>
      <param name="destination">The destination span which contains the transformed characters.</param>
      <returns>The number of characters written into the destination span. If the destination is too small, returns -1.</returns>
      <exception cref="T:System.InvalidOperationException">The source and destination buffers overlap.</exception>
    </member>
    <member name="M:System.MemoryExtensions.Trim(System.Memory{System.Char})">
      <summary>Removes all leading and trailing whitespace characters from a character memory region.</summary>
      <param name="memory">The source memory from which the characters are removed.</param>
      <returns>The trimmed character memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Trim(System.ReadOnlyMemory{System.Char})">
      <summary>Removes all leading and trailing whitespace characters from a read-only character memory region.</summary>
      <param name="memory">The source memory from which the characters are removed.</param>
      <returns>The trimmed character memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Trim(System.ReadOnlySpan{System.Char})">
      <summary>Removes all leading and trailing whitespace characters from a read-only character span.</summary>
      <param name="span">The source span from which the characters are removed.</param>
      <returns>The trimmed read-only character span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Trim(System.ReadOnlySpan{System.Char},System.Char)">
      <summary>Removes all leading and trailing occurrences of a specified character from a read-only character span.</summary>
      <param name="span">The source span from which the character is removed.</param>
      <param name="trimChar">The specified character to look for and remove.</param>
      <returns>The trimmed read-only character span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Trim(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char})">
      <summary>Removes all leading and trailing occurrences of a set of characters specified in a read-only span from a read-only character span.</summary>
      <param name="span">The source span from which the characters are removed.</param>
      <param name="trimChars">The span which contains the set of characters to remove.</param>
      <returns>The trimmed read-only character span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Trim(System.Span{System.Char})">
      <summary>Removes all leading and trailing whitespace characters from a character span.</summary>
      <param name="span">The source span from which the characters are removed.</param>
      <returns>The trimmed character span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Trim``1(System.Memory{``0},``0)">
      <summary>Removes all leading and trailing occurrences of a specified element from a memory region.</summary>
      <param name="memory">The source memory from which the element is removed.</param>
      <param name="trimElement">The specified element to look for and remove.</param>
      <typeparam name="T">The type of the elements in the memory region.</typeparam>
      <returns>The trimmed memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Trim``1(System.Memory{``0},System.ReadOnlySpan{``0})">
      <summary>Removes all leading and trailing occurrences of a set of elements specified in a read-only span from a memory region.</summary>
      <param name="memory">The source memory from which the elements are removed.</param>
      <param name="trimElements">The span which contains the set of elements to remove.</param>
      <typeparam name="T">The type of the elements in the memory region.</typeparam>
      <returns>The trimmed memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Trim``1(System.ReadOnlyMemory{``0},``0)">
      <summary>Removes all leading and trailing occurrences of a specified element from a read-only memory region.</summary>
      <param name="memory">The source memory from which the element is removed.</param>
      <param name="trimElement">The specified element to look for and remove.</param>
      <typeparam name="T">The type of the elements in the read-only memory region.</typeparam>
      <returns>The trimmed read-only memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Trim``1(System.ReadOnlyMemory{``0},System.ReadOnlySpan{``0})">
      <summary>Removes all leading and trailing occurrences of a set of elements specified in a read-only span from a read-only memory region.</summary>
      <param name="memory">The source memory from which the elements are removed.</param>
      <param name="trimElements">The span which contains the set of elements to remove.</param>
      <typeparam name="T">The type of the elements in the read-only memory region.</typeparam>
      <returns>The trimmed read-only memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Trim``1(System.ReadOnlySpan{``0},``0)">
      <summary>Removes all leading and trailing occurrences of a specified element from a read-only span.</summary>
      <param name="span">The source span from which the element is removed.</param>
      <param name="trimElement">The specified element to look for and remove.</param>
      <typeparam name="T">The type of the elements in the read-only span.</typeparam>
      <returns>The trimmed read-only span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Trim``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <summary>Removes all leading and trailing occurrences of a set of elements specified in a read-only span from a read-only span.</summary>
      <param name="span">The source span from which the elements are removed.</param>
      <param name="trimElements">The span which contains the set of elements to remove.</param>
      <typeparam name="T">The type of the elements in the read-only span.</typeparam>
      <returns>The trimmed read-only span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Trim``1(System.Span{``0},``0)">
      <summary>Removes all leading and trailing occurrences of a specified element from a span.</summary>
      <param name="span">The source span from which the element is removed.</param>
      <param name="trimElement">The specified element to look for and remove.</param>
      <typeparam name="T">The type of the elements in the span.</typeparam>
      <returns>The trimmed span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.Trim``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <summary>Removes all leading and trailing occurrences of a set of elements specified in a read-only span from a span.</summary>
      <param name="span">The source span from which the elements are removed.</param>
      <param name="trimElements">The span which contains the set of elements to remove.</param>
      <typeparam name="T">The type of the elements in the span.</typeparam>
      <returns>The trimmed span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimEnd(System.Memory{System.Char})">
      <summary>Removes all trailing whitespace characters from a character memory region.</summary>
      <param name="memory">The source memory from which the characters are removed.</param>
      <returns>The trimmed character memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimEnd(System.ReadOnlyMemory{System.Char})">
      <summary>Removes all trailing whitespace characters from a read-only character memory region.</summary>
      <param name="memory">The source memory from which the characters are removed.</param>
      <returns>The trimmed read-only character span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimEnd(System.ReadOnlySpan{System.Char})">
      <summary>Removes all trailing whitespace characters from a read-only character span.</summary>
      <param name="span">The source span from which the characters are removed.</param>
      <returns>The trimmed read-only character span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimEnd(System.ReadOnlySpan{System.Char},System.Char)">
      <summary>Removes all trailing occurrences of a specified character from a read-only span.</summary>
      <param name="span">The source span from which the character is removed.</param>
      <param name="trimChar">The specified character to look for and remove.</param>
      <returns>The trimmed read-only character span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimEnd(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char})">
      <summary>Removes all trailing occurrences of a set of characters specified in a read-only span from a read-only character span.</summary>
      <param name="span">The source span from which the characters are removed.</param>
      <param name="trimChars">The span which contains the set of characters to remove.</param>
      <returns>The trimmed read-only character span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimEnd(System.Span{System.Char})">
      <summary>Removes all trailing whitespace characters from a character span.</summary>
      <param name="span">The source span from which the characters are removed.</param>
      <returns>The trimmed character span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimEnd``1(System.Memory{``0},``0)">
      <summary>Removes all trailing occurrences of a specified element from a character memory region.</summary>
      <param name="memory">The source memory from which the element is removed.</param>
      <param name="trimElement">The specified element to look for and remove.</param>
      <typeparam name="T">The type of the elements in the memory region.</typeparam>
      <returns>The trimmed memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimEnd``1(System.Memory{``0},System.ReadOnlySpan{``0})">
      <summary>Removes all trailing occurrences of a set of elements specified in a read-only span from a memory region.</summary>
      <param name="memory">The source memory from which the elements are removed.</param>
      <param name="trimElements">The span which contains the set of elements to remove.</param>
      <typeparam name="T">The type of the elements in the memory region.</typeparam>
      <returns>The trimmed memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimEnd``1(System.ReadOnlyMemory{``0},``0)">
      <summary>Removes all trailing occurrences of a specified element from a read-only memory region.</summary>
      <param name="memory">The source memory from which the element is removed.</param>
      <param name="trimElement">The specified element to look for and remove.</param>
      <typeparam name="T">The type of the elements in the read-only memory region.</typeparam>
      <returns>The trimmed read-only memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimEnd``1(System.ReadOnlyMemory{``0},System.ReadOnlySpan{``0})">
      <summary>Removes all trailing occurrences of a set of elements specified in a read-only span from a read-only memory region.</summary>
      <param name="memory">The source memory from which the elements are removed.</param>
      <param name="trimElements">The span which contains the set of elements to remove.</param>
      <typeparam name="T">The type of the elements in the read-only memory region.</typeparam>
      <returns>The trimmed read-only memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimEnd``1(System.ReadOnlySpan{``0},``0)">
      <summary>Removes all trailing occurrences of a specified element from a read-only span.</summary>
      <param name="span">The source span from which the element is removed.</param>
      <param name="trimElement">The specified element to look for and remove.</param>
      <typeparam name="T">The type of the elements in the read-only span.</typeparam>
      <returns>The trimmed read-only span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimEnd``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <summary>Removes all trailing occurrences of a set of elements specified in a read-only span from a read-only span.</summary>
      <param name="span">The source span from which the elements are removed.</param>
      <param name="trimElements">The span which contains the set of elements to remove.</param>
      <typeparam name="T">The type of the elements in the read-only span.</typeparam>
      <returns>The trimmed read-only span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimEnd``1(System.Span{``0},``0)">
      <summary>Removes all trailing occurrences of a specified element from a span.</summary>
      <param name="span">The source span from which the element is removed.</param>
      <param name="trimElement">The specified element to look for and remove.</param>
      <typeparam name="T">The type of the elements in the span.</typeparam>
      <returns>The trimmed span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimEnd``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <summary>Removes all trailing occurrences of a set of elements specified in a read-only span from a span.</summary>
      <param name="span">The source span from which the elements are removed.</param>
      <param name="trimElements">The span which contains the set of elements to remove.</param>
      <typeparam name="T">The type of the elements in the span.</typeparam>
      <returns>The trimmed span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimStart(System.Memory{System.Char})">
      <summary>Removes all leading whitespace characters from a memory region.</summary>
      <param name="memory">The source memory from which the characters are removed.</param>
      <returns>The trimmed character memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimStart(System.ReadOnlyMemory{System.Char})">
      <summary>Removes all leading whitespace characters from a read-only memory region.</summary>
      <param name="memory">The source memory from which the characters are removed.</param>
      <returns>The trimmed read-only character memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimStart(System.ReadOnlySpan{System.Char})">
      <summary>Removes all leading whitespace characters from a read-only span.</summary>
      <param name="span">The source span from which the characters are removed.</param>
      <returns>The trimmed read-only character span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimStart(System.ReadOnlySpan{System.Char},System.Char)">
      <summary>Removes all leading occurrences of a specified character from the span.</summary>
      <param name="span">The source span from which the character is removed.</param>
      <param name="trimChar">The specified character to look for and remove.</param>
      <returns>The trimmed read-only character span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimStart(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char})">
      <summary>Removes all leading occurrences of a set of characters specified in a read-only span from the span.</summary>
      <param name="span">The source span from which the characters are removed.</param>
      <param name="trimChars">The span which contains the set of characters to remove.</param>
      <returns>The trimmed read-only character span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimStart(System.Span{System.Char})">
      <summary>Removes all leading whitespace characters from a span.</summary>
      <param name="span">The source span from which the characters are removed.</param>
      <returns>The trimmed character span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimStart``1(System.Memory{``0},``0)">
      <summary>Removes all leading occurrences of a specified element from a memory region.</summary>
      <param name="memory">The source memory region from which the element is removed.</param>
      <param name="trimElement">The specified element to look for and remove.</param>
      <typeparam name="T">The type of the elements in the memory region.</typeparam>
      <returns>The trimmed memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimStart``1(System.Memory{``0},System.ReadOnlySpan{``0})">
      <summary>Removes all leading occurrences of a set of elements specified in a read-only span from a memory region.</summary>
      <param name="memory">The source memory from which the elements are removed.</param>
      <param name="trimElements">The span which contains the set of elements to remove.</param>
      <typeparam name="T">The type of the elements in the memory region.</typeparam>
      <returns>The trimmed memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimStart``1(System.ReadOnlyMemory{``0},``0)">
      <summary>Removes all leading occurrences of a specified element from a memory region.</summary>
      <param name="memory">The source memory from which the element is removed.</param>
      <param name="trimElement">The specified element to look for and remove.</param>
      <typeparam name="T">The type of the elements in the read-only memory region.</typeparam>
      <returns>The trimmed read-only memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimStart``1(System.ReadOnlyMemory{``0},System.ReadOnlySpan{``0})">
      <summary>Removes all leading occurrences of a set of elements specified in a read-only span from a memory region.</summary>
      <param name="memory">The source memory from which the elements are removed.</param>
      <param name="trimElements">The span which contains the set of elements to remove.</param>
      <typeparam name="T">The type of the elements in the read-only memory region.</typeparam>
      <returns>The trimmed read-only memory region.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimStart``1(System.ReadOnlySpan{``0},``0)">
      <summary>Removes all leading occurrences of a specified element from the span.</summary>
      <param name="span">The source span from which the element is removed.</param>
      <param name="trimElement">The specified element to look for and remove.</param>
      <typeparam name="T">The type of the elements in the read-only span.</typeparam>
      <returns>The trimmed read-only span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimStart``1(System.ReadOnlySpan{``0},System.ReadOnlySpan{``0})">
      <summary>Removes all leading occurrences of a set of elements specified in a read-only span from the span.</summary>
      <param name="span">The source span from which the elements are removed.</param>
      <param name="trimElements">The span which contains the set of elements to remove.</param>
      <typeparam name="T">The type of the elements in the read-only span.</typeparam>
      <returns>The trimmed read-only span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimStart``1(System.Span{``0},``0)">
      <summary>Removes all leading occurrences of a specified element from the span.</summary>
      <param name="span">The source span from which the element is removed.</param>
      <param name="trimElement">The specified element to look for and remove.</param>
      <typeparam name="T">The type of the elements in the span.</typeparam>
      <returns>The trimmed span.</returns>
    </member>
    <member name="M:System.MemoryExtensions.TrimStart``1(System.Span{``0},System.ReadOnlySpan{``0})">
      <summary>Removes all leading occurrences of a set of elements specified in a read-only span from the span.</summary>
      <param name="span">The source span from which the elements are removed.</param>
      <param name="trimElements">The span which contains the set of elements to remove.</param>
      <typeparam name="T">The type of the elements in the span.</typeparam>
      <returns>The trimmed span.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.MemoryMarshal">
      <summary>Provides methods to interoperate with <see cref="T:System.Memory`1" />, <see cref="T:System.ReadOnlyMemory`1" />,  <see cref="T:System.Span`1" />, and  <see cref="T:System.ReadOnlySpan`1" />.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.AsBytes``1(System.ReadOnlySpan{``0})">
      <summary>Casts a <see cref="T:System.ReadOnlySpan`1" /> of one primitive type, <paramref name="T" />, to a <see langword="ReadOnlySpan&lt;Byte&gt;" />.</summary>
      <param name="span">The source slice to convert.</param>
      <typeparam name="T">The type of items in the read-only span.</typeparam>
      <returns>A read-only span of type <see cref="T:System.Byte" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> contains references or pointers.</exception>
      <exception cref="T:System.OverflowException">The <see cref="P:System.ReadOnlySpan`1.Length" /> property of the new <see cref="T:System.ReadOnlySpan`1" /> would exceed <see cref="F:System.Int32.MaxValue" /></exception>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.AsBytes``1(System.Span{``0})">
      <summary>Casts a <see cref="T:System.Span`1" /> of one primitive type, <paramref name="T" />, to a <see langword="Span&lt;Byte&gt;" />.</summary>
      <param name="span">The source slice to convert.</param>
      <typeparam name="T">The type of items in the span.</typeparam>
      <returns>A span of type <see cref="T:System.Byte" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> contains references or pointers.</exception>
      <exception cref="T:System.OverflowException">The <see cref="P:System.Span`1.Length" /> property of the new <see cref="T:System.Span`1" /> would exceed <see cref="F:System.Int32.MaxValue" /></exception>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.AsMemory``1(System.ReadOnlyMemory{``0})">
      <summary>Creates a <see cref="T:System.Memory`1" /> instance from a <see cref="T:System.ReadOnlyMemory`1" />.</summary>
      <param name="memory">The read-only memory buffer.</param>
      <typeparam name="T">The type of items in the read-only memory buffer.</typeparam>
      <returns>A memory block that represents the same memory as the <see cref="T:System.ReadOnlyMemory`1" />.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.AsRef``1(System.ReadOnlySpan{System.Byte})">
      <summary>Reinterprets a read-only span of bytes as a read-only reference to the structure of type <paramref name="T" />.</summary>
      <param name="span">The read-only span to reinterpret.</param>
      <typeparam name="T">The type of the returned reference.</typeparam>
      <returns>The read-only reference to the structure of type <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.AsRef``1(System.Span{System.Byte})">
      <summary>Reinterprets a span of bytes as a reference to the structure of type <paramref name="T" />.</summary>
      <param name="span">The span to reinterpret.</param>
      <typeparam name="T">The type of the returned reference.</typeparam>
      <returns>The reference to the structure of type <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.Cast``2(System.ReadOnlySpan{``0})">
      <summary>Casts a read-only span of one primitive type to a read-only span of another primitive type.</summary>
      <param name="span">The source slice to convert.</param>
      <typeparam name="TFrom">The type of the source span.</typeparam>
      <typeparam name="TTo">The type of the target span.</typeparam>
      <returns>The converted read-only span.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="TFrom" /> or <paramref name="TTo" /> contains references or pointers.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.Cast``2(System.Span{``0})">
      <summary>Casts a span of one primitive type to a span of another primitive type.</summary>
      <param name="span">The source slice to convert.</param>
      <typeparam name="TFrom">The type of the source span.</typeparam>
      <typeparam name="TTo">The type of the target span.</typeparam>
      <returns>The converted span.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="TFrom" /> or <paramref name="TTo" /> contains references or pointers.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.CreateFromPinnedArray``1(``0[],System.Int32,System.Int32)">
      <summary>Creates a new memory buffer over the portion of the pre-pinned target array beginning at the <paramref name="start" /> index and consisting of <paramref name="length" /> items.</summary>
      <param name="array">The pre-pinned source array.</param>
      <param name="start">The index of <paramref name="array" /> at which to begin the memory block.</param>
      <param name="length">The number of items to include in the memory block.</param>
      <typeparam name="T">The type of the array.</typeparam>
      <returns>A block of memory over the specified elements of <paramref name="array" />. If <paramref name="array" /> is <see langword="null" />, or if <paramref name="start" /> and <paramref name="length" /> are 0, the method returns a <see cref="T:System.Memory`1" /> instance of <see cref="P:System.Memory`1.Length" /> zero.</returns>
      <exception cref="T:System.ArrayTypeMismatchException">
        <paramref name="array" /> is covariant, and the type of <paramref name="array" /> is not exactly <paramref name="T[]" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="start" /> or the end index is not in the range of 0 to <see cref="P:System.Array.Length" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.CreateReadOnlySpan``1(``0@,System.Int32)">
      <summary>Creates a new read-only span over a portion of a regular managed object.</summary>
      <param name="reference">A reference to data.</param>
      <param name="length">The number of <paramref name="T" /> elements that <paramref name="reference" /> contains.</param>
      <typeparam name="T">The type of the data items.</typeparam>
      <returns>A read-only span.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.CreateSpan``1(``0@,System.Int32)">
      <summary>Creates a new span over a portion of a regular managed object.</summary>
      <param name="reference">A reference to data.</param>
      <param name="length">The number of <paramref name="T" /> elements that <paramref name="reference" /> contains.</param>
      <typeparam name="T">The type of the data items.</typeparam>
      <returns>A span.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.GetReference``1(System.ReadOnlySpan{``0})">
      <summary>Returns a reference to the element of the read-only span at index 0.</summary>
      <param name="span">The read-only span from which the reference is retrieved.</param>
      <typeparam name="T">The type of items in the span.</typeparam>
      <returns>A reference to the element at index 0.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.GetReference``1(System.Span{``0})">
      <summary>Returns a reference to the element of the span at index 0.</summary>
      <param name="span">The span from which the reference is retrieved.</param>
      <typeparam name="T">The type of items in the span.</typeparam>
      <returns>A reference to the element at index 0.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.Read``1(System.ReadOnlySpan{System.Byte})">
      <summary>Reads a structure of type <param name="T" /> out of a read-only span of bytes.</summary>
      <param name="source">A read-only span.</param>
      <typeparam name="T">The type of the item to retrieve from the read-only span.</typeparam>
      <returns>The structure retrieved from the read-only span.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> contains references or pointers.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="source" /> is smaller than <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.ToEnumerable``1(System.ReadOnlyMemory{``0})">
      <summary>Creates an <see cref="T:System.Collections.Generic.IEnumerable`1" /> view of the given read-only memory buffer.</summary>
      <param name="memory">A read-only memory buffer.</param>
      <typeparam name="T">The type of the items in the read-only memory buffer.</typeparam>
      <returns>An enumerable view of <paramref name="memory" />.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.TryGetArray``1(System.ReadOnlyMemory{``0},System.ArraySegment{``0}@)">
      <summary>Tries to get an array segment from the underlying memory buffer. The return value indicates the success of the operation.</summary>
      <param name="memory">A read-only memory buffer.</param>
      <param name="segment">When this method returns, contains the array segment retrieved from the underlying read-only memory buffer. If the method fails, the method returns a default array segment.</param>
      <typeparam name="T">The type of items in the read-only memory buffer.</typeparam>
      <returns>
        <see langword="true" /> if the method call succeeds; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.TryGetMemoryManager``2(System.ReadOnlyMemory{``0},``1@)">
      <summary>Tries to retrieve a <see cref="T:System.Buffers.MemoryManager`1" /> from the underlying read-only memory buffer.</summary>
      <param name="memory">The read-only memory buffer for which to get the memory manager.</param>
      <param name="manager">When the method returns, the manager of <paramref name="memory" />.</param>
      <typeparam name="T">The type of the items in the read-only memory buffer.</typeparam>
      <typeparam name="TManager">The type of the <see cref="T:System.Buffers.MemoryManager`1" /> to retrieve.</typeparam>
      <returns>
        <see langword="true" /> if the method retrieved the memory manager; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.TryGetMemoryManager``2(System.ReadOnlyMemory{``0},``1@,System.Int32@,System.Int32@)">
      <summary>Tries to retrieve a <see cref="T:System.Buffers.MemoryManager`1" />, start index, and length from the underlying read-only memory buffer.</summary>
      <param name="memory">The read-only memory buffer for which to get the memory manager.</param>
      <param name="manager">When the method returns, the manager of <paramref name="memory" />.</param>
      <param name="start">When the method returns, the offset from the start of the <paramref name="manager" /> that the <paramref name="memory" /> represents.</param>
      <param name="length">When the method returns, the length of the <paramref name="manager" /> that the <paramref name="memory" /> represents.</param>
      <typeparam name="T">The type of the items in the read-only memory buffer.</typeparam>
      <typeparam name="TManager">The type of the <see cref="T:System.Buffers.MemoryManager`1" /> to retrieve.</typeparam>
      <returns>
        <see langword="true" /> if the method succeeded; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.TryGetString(System.ReadOnlyMemory{System.Char},System.String@,System.Int32@,System.Int32@)">
      <summary>Tries to get the underlying string from a <see langword="System.ReadOnlyMemory&lt;Char&gt;" />.</summary>
      <param name="memory">Read-only memory containing a block of characters.</param>
      <param name="text">When the method returns, the string contained in the memory buffer.</param>
      <param name="start">The starting location in <paramref name="text" />.</param>
      <param name="length">The number of characters in <paramref name="text" />.</param>
      <returns>
        <see langword="true" /> if the method successfully retrieves the underlying string; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.TryRead``1(System.ReadOnlySpan{System.Byte},``0@)">
      <summary>Tries to read a structure of type <paramref name="T" /> from a read-only span of bytes.</summary>
      <param name="source">A read-only span of bytes.</param>
      <param name="value">When the method returns, an instance of <paramref name="T" />.</param>
      <typeparam name="T">The type of the structure to retrieve.</typeparam>
      <returns>
        <see langword="true" /> if the method succeeds in retrieving an instance of the structure; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> contains references or pointers.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.TryWrite``1(System.Span{System.Byte},``0@)">
      <summary>Tries to write a structure of type <paramref name="T" /> into a span of bytes.</summary>
      <param name="destination">The span of bytes to contain the structure.</param>
      <param name="value">The structure to be written to the span.</param>
      <typeparam name="T">The type of the structure.</typeparam>
      <returns>
        <see langword="true" /> if the write operation succeeded; otherwise, <see langword="false" />. The method returns <see langword="false" /> if the span is too small to contain <paramref name="T" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> contains references or pointers.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.MemoryMarshal.Write``1(System.Span{System.Byte},``0@)">
      <summary>Writes a structure of type <paramref name="T" /> into a span of bytes.</summary>
      <param name="destination">The span of bytes to contain the structure.</param>
      <param name="value">The structure to be written to the span.</param>
      <typeparam name="T">The type of the structure.</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> contains references or pointers.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="destination" /> is too small to contain <paramref name="value" />.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.SequenceMarshal">
      <summary>Provides a collection of methods for interoperating with <see cref="T:System.Buffers.ReadOnlySequence`1" />.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SequenceMarshal.TryGetArray``1(System.Buffers.ReadOnlySequence{``0},System.ArraySegment{``0}@)">
      <summary>Gets an array segment from the underlying read-only sequence.</summary>
      <param name="sequence">The read-only sequence from which the array segment will be retrieved.</param>
      <param name="segment">The returned array segment.</param>
      <typeparam name="T">The type of the read-only sequence.</typeparam>
      <returns>
        <see langword="true" /> if it's possible to retrieve the array segment; otherwise, <see langword="false" /> and a default array segment is returned.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.SequenceMarshal.TryGetReadOnlyMemory``1(System.Buffers.ReadOnlySequence{``0},System.ReadOnlyMemory{``0}@)">
      <summary>Attempts to retrieve a read-only memory from the specified read-only sequence.</summary>
      <param name="sequence">The read-only sequence from which the memory will be retrieved.</param>
      <param name="memory">The returned read-only memory of type T.</param>
      <typeparam name="T">The type of the read-only sequence.</typeparam>
      <returns>
        <see langword="true" /> if the read-only memory can be retrieved; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.SequenceMarshal.TryGetReadOnlySequenceSegment``1(System.Buffers.ReadOnlySequence{``0},System.Buffers.ReadOnlySequenceSegment{``0}@,System.Int32@,System.Buffers.ReadOnlySequenceSegment{``0}@,System.Int32@)">
      <summary>Attempts to retrieve a read-only sequence segment from the specified read-only sequence.</summary>
      <param name="sequence">The read-only sequence from which the read-only sequence segment will be retrieved.</param>
      <param name="startSegment">The beginning read-only sequence segment.</param>
      <param name="startIndex">The initial position.</param>
      <param name="endSegment">The ending read-only sequence segment.</param>
      <param name="endIndex">The final position.</param>
      <typeparam name="T">The type of the read-only sequence.</typeparam>
      <returns>
        <see langword="true" /> if the read-only sequence segment can be retrieved; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.SequenceMarshal.TryRead``1(System.Buffers.SequenceReader{System.Byte}@,``0@)">
      <summary>Attempts to read the specified type out of the buffer. It's dangerous to use this method with arbitrary structs - see remarks for more information.</summary>
      <param name="reader">A reference to the sequence reader.</param>
      <param name="value">The returned value if the read was successful. <paramref name="value" /> will be <see langword="default" /> if failed (due to lack of space).</param>
      <typeparam name="T">The type of the value.</typeparam>
      <returns>
        <see langword="true" /> if the read attempt was successful, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="T:System.SequencePosition">
      <summary>Represents a position in a non-contiguous set of memory. Properties of this type should not be interpreted by anything but the type that created it.</summary>
    </member>
    <member name="M:System.SequencePosition.#ctor(System.Object,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.SequencePosition" /> struct.</summary>
      <param name="object">A non-contiguous set of memory.</param>
      <param name="integer">The position in <paramref name="object" />.</param>
    </member>
    <member name="M:System.SequencePosition.Equals(System.Object)">
      <summary>Returns a value that indicates whether the current instance is equal to another object.</summary>
      <param name="obj">The object to compare with the current instance.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> is of type <see cref="T:System.SequencePosition" /> and is equal to the current instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.SequencePosition.Equals(System.SequencePosition)">
      <summary>Indicates whether the current instance is equal to another <see cref="T:System.SequencePosition" />.</summary>
      <param name="other">The sequence position to compare with the current instance.</param>
      <returns>
        <see langword="true" /> if the two instances are equal; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.SequencePosition.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>The hash code for this instance.</returns>
    </member>
    <member name="M:System.SequencePosition.GetInteger">
      <summary>Returns the integer part of this <see cref="T:System.SequencePosition" />.</summary>
      <returns>The integer part of this sequence position.</returns>
    </member>
    <member name="M:System.SequencePosition.GetObject">
      <summary>Returns the object part of this <see cref="T:System.SequencePosition" />.</summary>
      <returns>The object part of this sequence position.</returns>
    </member>
    <member name="T:System.Text.SpanRuneEnumerator">
      <summary>Provides an enumerator for the <see cref="T:System.Text.Rune" /> values represented by a span containing UTF-16 text.</summary>
    </member>
    <member name="P:System.Text.SpanRuneEnumerator.Current">
      <summary>Gets the <see cref="T:System.Text.Rune" /> at the current position of the enumerator.</summary>
      <returns>The <see cref="T:System.Text.Rune" /> at the current position of the enumerator.</returns>
    </member>
    <member name="M:System.Text.SpanRuneEnumerator.GetEnumerator">
      <summary>Returns the current enumerator instance.</summary>
      <returns>The current enumerator instance.</returns>
    </member>
    <member name="M:System.Text.SpanRuneEnumerator.MoveNext">
      <summary>Advances the enumerator to the next <see cref="T:System.Text.Rune" /> of the span.</summary>
      <returns>
        <see langword="true" /> if the enumerator successfully advanced to the next item; <see langword="false" /> if the end of the span has been reached.</returns>
    </member>
  </members>
</doc>