<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Hosting</name>
    </assembly>
    <members>
        <member name="P:Microsoft.Extensions.Hosting.ConsoleLifetimeOptions.SuppressStatusMessages">
            <summary>
            Indicates if host lifetime status messages should be supressed such as on startup.
            The default is false.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Hosting.Host">
            <summary>
            Provides convenience methods for creating instances of <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> with pre-configured defaults.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Extensions.Hosting.HostBuilder"/> class with pre-configured defaults.
            </summary>
            <remarks>
              The following defaults are applied to the returned <see cref="T:Microsoft.Extensions.Hosting.HostBuilder"/>:
              <list type="bullet">
                <item><description>set the <see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.ContentRootPath"/> to the result of <see cref="M:System.IO.Directory.GetCurrentDirectory"/></description></item>
                <item><description>load host <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from "DOTNET_" prefixed environment variables</description></item>
                <item><description>load app <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from 'appsettings.json' and 'appsettings.[<see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.EnvironmentName"/>].json'</description></item>
                <item><description>load app <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from User Secrets when <see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.EnvironmentName"/> is 'Development' using the entry assembly</description></item>
                <item><description>load app <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from environment variables</description></item>
                <item><description>configure the <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> to log to the console, debug, and event source output</description></item>
                <item><description>enables scope validation on the dependency injection container when <see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.EnvironmentName"/> is 'Development'</description></item>
              </list>
            </remarks>
            <returns>The initialized <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder(System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Extensions.Hosting.HostBuilder"/> class with pre-configured defaults.
            </summary>
            <remarks>
              The following defaults are applied to the returned <see cref="T:Microsoft.Extensions.Hosting.HostBuilder"/>:
              <list type="bullet">
                <item><description>set the <see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.ContentRootPath"/> to the result of <see cref="M:System.IO.Directory.GetCurrentDirectory"/></description></item>
                <item><description>load host <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from "DOTNET_" prefixed environment variables</description></item>
                <item><description>load host <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from supplied command line args</description></item>
                <item><description>load app <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from 'appsettings.json' and 'appsettings.[<see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.EnvironmentName"/>].json'</description></item>
                <item><description>load app <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from User Secrets when <see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.EnvironmentName"/> is 'Development' using the entry assembly</description></item>
                <item><description>load app <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from environment variables</description></item>
                <item><description>load app <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from supplied command line args</description></item>
                <item><description>configure the <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> to log to the console, debug, and event source output</description></item>
                <item><description>enables scope validation on the dependency injection container when <see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.EnvironmentName"/> is 'Development'</description></item>
              </list>
            </remarks>
            <param name="args">The command line args.</param>
            <returns>The initialized <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/>.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Hosting.HostBuilder">
            <summary>
            A program initialization utility.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Hosting.HostBuilder.Properties">
            <summary>
            A central location for sharing state between components during the host building process.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostBuilder.ConfigureHostConfiguration(System.Action{Microsoft.Extensions.Configuration.IConfigurationBuilder})">
            <summary>
            Set up the configuration for the builder itself. This will be used to initialize the <see cref="T:Microsoft.Extensions.Hosting.IHostEnvironment"/>
            for use later in the build process. This can be called multiple times and the results will be additive.
            </summary>
            <param name="configureDelegate">The delegate for configuring the <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> that will be used
            to construct the <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> for the host.</param>
            <returns>The same instance of the <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> for chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostBuilder.ConfigureAppConfiguration(System.Action{Microsoft.Extensions.Hosting.HostBuilderContext,Microsoft.Extensions.Configuration.IConfigurationBuilder})">
            <summary>
            Sets up the configuration for the remainder of the build process and application. This can be called multiple times and
            the results will be additive. The results will be available at <see cref="P:Microsoft.Extensions.Hosting.HostBuilderContext.Configuration"/> for
            subsequent operations, as well as in <see cref="P:Microsoft.Extensions.Hosting.IHost.Services"/>.
            </summary>
            <param name="configureDelegate">The delegate for configuring the <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> that will be used
            to construct the <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> for the host.</param>
            <returns>The same instance of the <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> for chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostBuilder.ConfigureServices(System.Action{Microsoft.Extensions.Hosting.HostBuilderContext,Microsoft.Extensions.DependencyInjection.IServiceCollection})">
            <summary>
            Adds services to the container. This can be called multiple times and the results will be additive.
            </summary>
            <param name="configureDelegate">The delegate for configuring the <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> that will be used
            to construct the <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> for the host.</param>
            <returns>The same instance of the <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> for chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostBuilder.UseServiceProviderFactory``1(Microsoft.Extensions.DependencyInjection.IServiceProviderFactory{``0})">
            <summary>
            Overrides the factory used to create the service provider.
            </summary>
            <typeparam name="TContainerBuilder">The type of the builder to create.</typeparam>
            <param name="factory">A factory used for creating service providers.</param>
            <returns>The same instance of the <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> for chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostBuilder.UseServiceProviderFactory``1(System.Func{Microsoft.Extensions.Hosting.HostBuilderContext,Microsoft.Extensions.DependencyInjection.IServiceProviderFactory{``0}})">
            <summary>
            Overrides the factory used to create the service provider.
            </summary>
            <param name="factory">A factory used for creating service providers.</param>
            <typeparam name="TContainerBuilder">The type of the builder to create.</typeparam>
            <returns>The same instance of the <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> for chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostBuilder.ConfigureContainer``1(System.Action{Microsoft.Extensions.Hosting.HostBuilderContext,``0})">
            <summary>
            Enables configuring the instantiated dependency container. This can be called multiple times and
            the results will be additive.
            </summary>
            <typeparam name="TContainerBuilder">The type of the builder to create.</typeparam>
            <param name="configureDelegate">The delegate for configuring the <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> that will be used
            to construct the <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> for the host.</param>
            <returns>The same instance of the <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> for chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostBuilder.Build">
            <summary>
            Run the given actions to initialize the host. This can only be called once.
            </summary>
            <returns>An initialized <see cref="T:Microsoft.Extensions.Hosting.IHost"/></returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostingHostBuilderExtensions.UseEnvironment(Microsoft.Extensions.Hosting.IHostBuilder,System.String)">
            <summary>
            Specify the environment to be used by the host.
            </summary>
            <param name="hostBuilder">The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> to configure.</param>
            <param name="environment">The environment to host the application in.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostingHostBuilderExtensions.UseContentRoot(Microsoft.Extensions.Hosting.IHostBuilder,System.String)">
            <summary>
            Specify the content root directory to be used by the host.
            </summary>
            <param name="hostBuilder">The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> to configure.</param>
            <param name="contentRoot">Path to root directory of the application.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostingHostBuilderExtensions.UseDefaultServiceProvider(Microsoft.Extensions.Hosting.IHostBuilder,System.Action{Microsoft.Extensions.DependencyInjection.ServiceProviderOptions})">
            <summary>
            Specify the <see cref="T:System.IServiceProvider"/> to be the default one.
            </summary>
            <param name="hostBuilder">The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> to configure.</param>
            <param name="configure"></param>
            <returns>The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostingHostBuilderExtensions.UseDefaultServiceProvider(Microsoft.Extensions.Hosting.IHostBuilder,System.Action{Microsoft.Extensions.Hosting.HostBuilderContext,Microsoft.Extensions.DependencyInjection.ServiceProviderOptions})">
            <summary>
            Specify the <see cref="T:System.IServiceProvider"/> to be the default one.
            </summary>
            <param name="hostBuilder">The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> to configure.</param>
            <param name="configure">The delegate that configures the <see cref="T:System.IServiceProvider"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostingHostBuilderExtensions.ConfigureLogging(Microsoft.Extensions.Hosting.IHostBuilder,System.Action{Microsoft.Extensions.Hosting.HostBuilderContext,Microsoft.Extensions.Logging.ILoggingBuilder})">
            <summary>
            Adds a delegate for configuring the provided <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/>. This may be called multiple times.
            </summary>
            <param name="hostBuilder">The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder" /> to configure.</param>
            <param name="configureLogging">The delegate that configures the <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/>.</param>
            <returns>The same instance of the <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> for chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostingHostBuilderExtensions.ConfigureLogging(Microsoft.Extensions.Hosting.IHostBuilder,System.Action{Microsoft.Extensions.Logging.ILoggingBuilder})">
            <summary>
            Adds a delegate for configuring the provided <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/>. This may be called multiple times.
            </summary>
            <param name="hostBuilder">The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder" /> to configure.</param>
            <param name="configureLogging">The delegate that configures the <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/>.</param>
            <returns>The same instance of the <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> for chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostingHostBuilderExtensions.ConfigureAppConfiguration(Microsoft.Extensions.Hosting.IHostBuilder,System.Action{Microsoft.Extensions.Configuration.IConfigurationBuilder})">
            <summary>
            Sets up the configuration for the remainder of the build process and application. This can be called multiple times and
            the results will be additive. The results will be available at <see cref="P:Microsoft.Extensions.Hosting.HostBuilderContext.Configuration"/> for
            subsequent operations, as well as in <see cref="P:Microsoft.Extensions.Hosting.IHost.Services"/>.
            </summary>
            <param name="hostBuilder">The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder" /> to configure.</param>
            <param name="configureDelegate">The delegate for configuring the <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> that will be used
            to construct the <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> for the host.</param>
            <returns>The same instance of the <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> for chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostingHostBuilderExtensions.ConfigureServices(Microsoft.Extensions.Hosting.IHostBuilder,System.Action{Microsoft.Extensions.DependencyInjection.IServiceCollection})">
            <summary>
            Adds services to the container. This can be called multiple times and the results will be additive.
            </summary>
            <param name="hostBuilder">The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder" /> to configure.</param>
            <param name="configureDelegate">The delegate for configuring the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <returns>The same instance of the <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> for chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostingHostBuilderExtensions.ConfigureContainer``1(Microsoft.Extensions.Hosting.IHostBuilder,System.Action{``0})">
            <summary>
            Enables configuring the instantiated dependency container. This can be called multiple times and
            the results will be additive.
            </summary>
            <typeparam name="TContainerBuilder"></typeparam>
            <param name="hostBuilder">The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder" /> to configure.</param>
            <param name="configureDelegate">The delegate for configuring the <typeparamref name="TContainerBuilder"/>.</param>
            <returns>The same instance of the <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> for chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostingHostBuilderExtensions.UseConsoleLifetime(Microsoft.Extensions.Hosting.IHostBuilder)">
            <summary>
            Listens for Ctrl+C or SIGTERM and calls <see cref="M:Microsoft.Extensions.Hosting.IHostApplicationLifetime.StopApplication"/> to start the shutdown process.
            This will unblock extensions like RunAsync and WaitForShutdownAsync.
            </summary>
            <param name="hostBuilder">The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder" /> to configure.</param>
            <returns>The same instance of the <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> for chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostingHostBuilderExtensions.UseConsoleLifetime(Microsoft.Extensions.Hosting.IHostBuilder,System.Action{Microsoft.Extensions.Hosting.ConsoleLifetimeOptions})">
            <summary>
            Listens for Ctrl+C or SIGTERM and calls <see cref="M:Microsoft.Extensions.Hosting.IHostApplicationLifetime.StopApplication"/> to start the shutdown process.
            This will unblock extensions like RunAsync and WaitForShutdownAsync.
            </summary>
            <param name="hostBuilder">The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder" /> to configure.</param>
            <param name="configureOptions">The delegate for configuring the <see cref="T:Microsoft.Extensions.Hosting.Internal.ConsoleLifetime"/>.</param>
            <returns>The same instance of the <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> for chaining.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostingHostBuilderExtensions.RunConsoleAsync(Microsoft.Extensions.Hosting.IHostBuilder,System.Threading.CancellationToken)">
            <summary>
            Enables console support, builds and starts the host, and waits for Ctrl+C or SIGTERM to shut down.
            </summary>
            <param name="hostBuilder">The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder" /> to configure.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> that can be used to cancel the console.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that only completes when the token is triggeredor shutdown is triggered.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.HostingHostBuilderExtensions.RunConsoleAsync(Microsoft.Extensions.Hosting.IHostBuilder,System.Action{Microsoft.Extensions.Hosting.ConsoleLifetimeOptions},System.Threading.CancellationToken)">
            <summary>
            Enables console support, builds and starts the host, and waits for Ctrl+C or SIGTERM to shut down.
            </summary>
            <param name="hostBuilder">The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder" /> to configure.</param>
            <param name="configureOptions">The delegate for configuring the <see cref="T:Microsoft.Extensions.Hosting.Internal.ConsoleLifetime"/>.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> that can be used to cancel the console.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that only completes when the token is triggeredor shutdown is triggered.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Hosting.HostOptions">
            <summary>
            Options for <see cref="T:Microsoft.Extensions.Hosting.IHost"/>
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Hosting.HostOptions.ShutdownTimeout">
            <summary>
            The default timeout for <see cref="M:Microsoft.Extensions.Hosting.IHost.StopAsync(System.Threading.CancellationToken)"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Hosting.Internal.ApplicationLifetime">
            <summary>
            Allows consumers to perform cleanup during a graceful shutdown.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Hosting.Internal.ApplicationLifetime.ApplicationStarted">
            <summary>
            Triggered when the application host has fully started and is about to wait
            for a graceful shutdown.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Hosting.Internal.ApplicationLifetime.ApplicationStopping">
            <summary>
            Triggered when the application host is performing a graceful shutdown.
            Request may still be in flight. Shutdown will block until this event completes.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Hosting.Internal.ApplicationLifetime.ApplicationStopped">
            <summary>
            Triggered when the application host is performing a graceful shutdown.
            All requests should be complete at this point. Shutdown will block
            until this event completes.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.Internal.ApplicationLifetime.StopApplication">
            <summary>
            Signals the ApplicationStopping event and blocks until it completes.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.Internal.ApplicationLifetime.NotifyStarted">
            <summary>
            Signals the ApplicationStarted event and blocks until it completes.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.Internal.ApplicationLifetime.NotifyStopped">
            <summary>
            Signals the ApplicationStopped event and blocks until it completes.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Hosting.Internal.ConsoleLifetime">
            <summary>
            Listens for Ctrl+C or SIGTERM and initiates shutdown.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Hosting.Internal.HostingEnvironment">
            <summary>
            This API supports infrastructure and is not intended to be used
            directly from your code. This API may change or be removed in future releases.
            </summary>
        </member>
    </members>
</doc>
