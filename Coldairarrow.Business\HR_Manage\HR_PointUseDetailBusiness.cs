﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.IBusiness;
using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity;
using System;
using System.Collections;
using Coldairarrow.Util.UEditor;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.Shop_Manage.Enum;
using static Coldairarrow.Entity.Shop_Manage.Enum.ShopEnum;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_PointUseDetailBusiness : BaseBusiness<HR_PointUseDetail>, IHR_PointUseDetailBusiness, ITransientDependency
    {
        public HR_PointUseDetailBusiness(IDbAccessor db, IOperator @operator)
            : base(db)
        {
            _operator = @operator;
        }
        private readonly IOperator _operator;
        #region 外部接口

        public async Task<PageResult<HR_PointUseDetail>> GetDataListAsync(PageInput<ProductConditinDto> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_PointUseDetail>();
            var search = input.Search;
            input.SortField = "F_CreateDate";
            input.SortType = "desc";
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_PointUseDetail, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            if (!search.userId.IsNullOrWhiteSpace())
            {
                where = where.And(x => x.F_UserId == search.userId);
            }
            if (!search.userName.IsNullOrWhiteSpace())
            {
                where = where.And(x => x.F_UserName.Contains(search.userName));
            }
            if (search.orderType.HasValue)
            {
                where = where.And(x => x.F_OrderType == (ShopEnum.ShopType)search.orderType);
            }
            if (search.resType.HasValue)
            {
                where = where.And(x => x.F_ResType == (ShopEnum.PointBuyType)search.resType);
            }
            if (search.resType.HasValue)
            {
                where = where.And(x => x.F_ResType == (ShopEnum.PointBuyType)search.resType);
            }
            if (search.startTime.HasValue)
            {
                where = where.And(x => x.F_CreateDate >= search.startTime.Value);
            }
            if (search.endTime.HasValue)
            {
                where = where.And(x => x.F_CreateDate < search.endTime.Value.AddDays(1));
            }
            if (search.isMonth.HasValue)
            {
                //查询近3个月的数据
                where = where.And(x => x.F_CreateDate >= DateTime.Now.AddMonths(-3));
            }
            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_PointUseDetail> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_PointUseDetail data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_PointUseDetail data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        /// <summary>
        /// 更改员工总积分表
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task ChangeUserPoint(HR_PointUseDetail data)
        {
            //if (!data.F_UserId.IsNullOrWhiteSpace())
            //{
            //    //查询总积分表
            //    var date = (data.F_Month + "-01").ToDateTime();
            //    var pointList = await Db.GetIQueryable<HR_PointEntity>().FirstOrDefaultAsync(i => i.F_Iyear == date.Year && i.F_UserId == data.F_UserId);
            //    if (pointList != null)
            //    {
            //        pointList.F_ResNumber=
            //    }
            //}
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_PointUseDetail>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_PointUseDetail, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_PointUseDetail, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }


        public List<HR_PointUseDetail> GetThePoint(string id)
        {
            var entity = GetIQueryable().Where(x => x.F_UserId == id).OrderByDescending(x => x.F_CreateDate).ToList();
            return entity;
        }
        //[Transactional]
        public async Task AddUserPonitData(HR_PointUseDetail data)
        {
            List<HR_PointUseDetail> records = new List<HR_PointUseDetail>();
            var userEntity = Db.GetIQueryable<HR_FormalEmployees>().ToList();
            var base_Users = Db.GetIQueryable<Base_User>().ToList();
            //获取当前年的员工积分总额度
            var _PointEntity = await Db.GetIQueryable<HR_PointEntity>().FirstOrDefaultAsync(i => i.F_Iyear == DateTime.Now.Year && i.F_UserName == data.F_UserName);
            var updatePointList = new List<HR_PointEntity>();
            var addPointList = new List<HR_PointEntity>();
            //查找相关id
            var base_User = base_Users.Where(x => x.RealName == data.F_UserName.Trim()).OrderBy(x => x.CreateTime).FirstOrDefault();
            if (base_User != null)
            {
                //var detail = pointUseDetails.Find(x => x.F_UserName == import.F_UserName);

                HR_PointUseDetail hR_PointUseDetail = new HR_PointUseDetail();
                hR_PointUseDetail.F_UserName = data.F_UserName?.Trim();
                hR_PointUseDetail.F_UserId = base_User.Id?.Trim();
                hR_PointUseDetail.F_UserCode = !string.IsNullOrWhiteSpace(base_User.UserName) ? base_User.UserName.Replace("@cqlandmark.com", "") : "";
                hR_PointUseDetail.F_UseNumber = data.F_UseNumber;
                hR_PointUseDetail.F_Describe = "自动充值积分";
                hR_PointUseDetail.F_OrderType = data.F_OrderType;
                hR_PointUseDetail.F_ResType = data.F_ResType;
                hR_PointUseDetail.F_Month = DateTime.Now.ToString("yyyy-MM");
                //F_ResNumer
                if (_PointEntity != null)
                {
                    hR_PointUseDetail.F_ResNumer = _PointEntity.F_ResNumber;
                    hR_PointUseDetail.F_BackNumer = data.F_ResType == PointBuyType.增加 ? _PointEntity.F_ResNumber + data.F_UseNumber : _PointEntity.F_ResNumber - data.F_UseNumber;
                    _PointEntity.F_ResNumber = hR_PointUseDetail.F_BackNumer;
                    _PointEntity.F_StandardNumber += data.F_UseNumber;
                    this.UpdateEntity(_PointEntity, _operator);
                    updatePointList.Add(_PointEntity);
                }
                else
                {
                    hR_PointUseDetail.F_ResNumer = 0;
                    hR_PointUseDetail.F_BackNumer = data.F_UseNumber;
                    //新增积分总额度表
                    HR_PointEntity hR_Point = new HR_PointEntity()
                    {
                        F_UserName = data.F_UserName?.Trim(),
                        F_UserId = base_User.Id?.Trim(),
                        F_UserCode = hR_PointUseDetail.F_UserCode,
                        F_Iyear = DateTime.Now.Year,
                        F_ResNumber = data.F_UseNumber,
                        F_StandardNumber = data.F_UseNumber,
                        F_LncDecNumber = 0,
                        F_UsedNumber = 0
                    };
                    this.InitEntity(hR_Point, _operator);
                    addPointList.Add(hR_Point);
                }
                this.InitEntity(hR_PointUseDetail, _operator);
                records.Add(hR_PointUseDetail);



                if (updatePointList.Count > 0)
                {
                    await this.Db.UpdateAsync(updatePointList);
                }

                if (addPointList.Count > 0)
                {
                    this.Db.BulkInsert(addPointList);
                }
                if (records.Count > 0)
                {
                    this.Db.BulkInsert(records);
                }
            }
            else
            {
                throw new Exception("未找到相关用户！");
            }
        }

        /// <summary>
        /// 导入并保存数据
        /// </summary>
        /// <param name="physicPath">物理路径</param>
        /// <returns></returns>
        //[Transactional]
        public async Task<AjaxResult<DataTable>> ImportSaveData(string physicPath, IOperator op, string month)
        {
            AjaxResult<DataTable> ajaxResult = new AjaxResult<DataTable>();
            Hashtable ht = new Hashtable();
            ht["F_UserName"] = "员工姓名";
            ht["F_UseNumber"] = "积分";

            var importList = new ExcelHelper<HR_PointUseDetail>().ExcelImport(ht, physicPath);
            if (importList == null || importList.Count == 0)
            {
                ajaxResult.Success = false;
                ajaxResult.Msg = "上传数据错误或不能为空";
            }
            else
            {
                List<HR_PointUseDetail> records = new List<HR_PointUseDetail>();
                var userEntity = Db.GetIQueryable<HR_FormalEmployees>().ToList();
                var base_Users = Db.GetIQueryable<Base_User>().ToList();
                var date = (month + "-01").ToDateTime();
                //获取当前年的员工积分总额度
                var pointList = await Db.GetIQueryable<HR_PointEntity>().Where(i => i.F_Iyear == date.Year).ToListAsync();
                //获取当前月的员工积分总额度
                var pointUseDetails = await Db.GetIQueryable<HR_PointUseDetail>().Where(i => i.F_Month == month && i.F_ResType == PointBuyType.增加).ToListAsync();
                var updatePointList = new List<HR_PointEntity>();
                var addPointList = new List<HR_PointEntity>();
                var noImpUsers = new List<string>();
                importList.ForEach(import =>
                {
                    var detail = pointUseDetails.Find(x => x.F_UserName == import.F_UserName);
                    if (!import.F_UserName.IsNullOrWhiteSpace())
                    {
                        HR_PointUseDetail hR_PointUseDetail = new HR_PointUseDetail();
                        //查询员工得总额度
                        var pointEntity = pointList.Find(x => x.F_UserName == import.F_UserName.Trim());
                        //查找相关id
                        var base_User = base_Users.Where(x => x.RealName == import.F_UserName.Trim()).OrderBy(x => x.CreateTime).FirstOrDefault();
                        if (base_User != null)
                        {
                            hR_PointUseDetail.F_UserName = import.F_UserName?.Trim();
                            hR_PointUseDetail.F_UserId = base_User.Id?.Trim();
                            hR_PointUseDetail.F_UserCode = !string.IsNullOrWhiteSpace(base_User.UserName) ? base_User.UserName.Replace("@cqlandmark.com", "") : "";
                            hR_PointUseDetail.F_UseNumber = import.F_UseNumber;
                            hR_PointUseDetail.F_Describe = month.ToString() + "月度积分导入";
                            hR_PointUseDetail.F_OrderType = ShopEnum.ShopType.自动充值;
                            hR_PointUseDetail.F_ResType = ShopEnum.PointBuyType.增加;
                            hR_PointUseDetail.F_Month = month;
                            //F_ResNumer
                            if (pointEntity != null)
                            {
                                hR_PointUseDetail.F_ResNumer = pointEntity.F_ResNumber;
                                hR_PointUseDetail.F_BackNumer = pointEntity.F_ResNumber + import.F_UseNumber;
                                pointEntity.F_ResNumber = hR_PointUseDetail.F_BackNumer;
                                pointEntity.F_StandardNumber += import.F_UseNumber;
                                this.UpdateEntity(pointEntity, _operator);
                                updatePointList.Add(pointEntity);
                            }
                            else
                            {
                                hR_PointUseDetail.F_ResNumer = 0;
                                hR_PointUseDetail.F_BackNumer = import.F_UseNumber;
                                //新增积分总额度表
                                HR_PointEntity hR_Point = new HR_PointEntity()
                                {
                                    F_UserName = import.F_UserName?.Trim(),
                                    F_UserId = base_User.Id?.Trim(),
                                    F_UserCode = hR_PointUseDetail.F_UserCode,
                                    F_Iyear = date.Year,
                                    F_ResNumber = import.F_UseNumber,
                                    F_StandardNumber = import.F_UseNumber,
                                    F_LncDecNumber = 0,
                                    F_UsedNumber = 0
                                };
                                this.InitEntity(hR_Point, _operator);
                                addPointList.Add(hR_Point);
                            }
                            this.InitEntity(hR_PointUseDetail, _operator);
                            records.Add(hR_PointUseDetail);
                        }
                        else
                        {
                            noImpUsers.Add(import.F_UserName);
                        }
                    }
                });
                if (updatePointList.Count > 0)
                {
                    await this.Db.UpdateAsync(updatePointList);
                }

                if (addPointList.Count > 0)
                {
                    this.Db.BulkInsert(addPointList);
                }
                if (records.Count > 0)
                {
                    this.Db.BulkInsert(records);
                }
                if (noImpUsers.Count > 0)
                {
                    ajaxResult.Success = false;
                    ajaxResult.Msg = $"{records.Count}位员工导入成功," + string.Join(",", noImpUsers)+"导入失败,失败原因：未找到相关员工";
                    ajaxResult.ErrorCode = 0;
                }else {
                    ajaxResult.Success = true;
                    ajaxResult.Msg = $"{records.Count}位员工导入成功";
                    ajaxResult.ErrorCode = 0;
                }
          
            }
            return ajaxResult;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}