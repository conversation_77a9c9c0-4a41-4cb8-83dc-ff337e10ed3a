﻿<template>
  <a-modal
    :title="title"
    width="40%"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="()=>{this.visible=false}"
  >
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="entity" :rules="rules" v-bind="layout">
        <a-form-model-item label="F_Id" prop="F_Id">
          <a-input v-model="entity.F_Id" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="创建时间" prop="F_CreateDate">
          <a-input v-model="entity.F_CreateDate" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="创建人" prop="F_CreateUserId">
          <a-input v-model="entity.F_CreateUserId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="创建人名" prop="F_CreateUserName">
          <a-input v-model="entity.F_CreateUserName" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="修改时间" prop="F_ModifyDate">
          <a-input v-model="entity.F_ModifyDate" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="修改人" prop="F_ModifyUserId">
          <a-input v-model="entity.F_ModifyUserId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="修改人名" prop="F_ModifyUserName">
          <a-input v-model="entity.F_ModifyUserName" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="流程Guid" prop="F_WFId">
          <a-input v-model="entity.F_WFId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="业务状态" prop="F_BusState">
          <a-input v-model="entity.F_BusState" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="流程状态" prop="F_WFState">
          <a-input v-model="entity.F_WFState" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="备注" prop="F_Remark">
          <a-input v-model="entity.F_Remark" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="获取证书时间" prop="F_Time">
          <a-input v-model="entity.F_Time" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="证书名称" prop="F_Name">
          <a-input v-model="entity.F_Name" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="入职员工ID" prop="F_UserId">
          <a-input v-model="entity.F_UserId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="职称" prop="F_Title">
          <a-input v-model="entity.F_Title" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="职称级别" prop="F_TitleLevel">
          <a-input v-model="entity.F_TitleLevel" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="职称类别" prop="F_TitleCategory">
          <a-input v-model="entity.F_TitleCategory" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="职称授予时间" prop="F_TitleAwarded">
          <a-input v-model="entity.F_TitleAwarded" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="证书编号" prop="F_CertificateNumr">
          <a-input v-model="entity.F_CertificateNumr" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="职称内容" prop="F_TitleContent">
          <a-input v-model="entity.F_TitleContent" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="最高职称" prop="F_HighestTitle">
          <a-input v-model="entity.F_HighestTitle" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="评定单位" prop="F_EvaluateUnit">
          <a-input v-model="entity.F_EvaluateUnit" autocomplete="off" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      layout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 18 }
      },
      visible: false,
      loading: false,
      entity: {},
      rules: {},
      title: ''
    }
  },
  methods: {
    init() {
      this.visible = true
      this.entity = {}
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    openForm(id, title) {
      this.init()

      if (id) {
        this.loading = true
        this.$http.post('/HR_RegistrManage/HR_RegistrCertificate/GetTheData', { id: id }).then(resJson => {
          this.loading = false

          this.entity = resJson.Data
        })
      }
    },
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.loading = true
        this.$http.post('/HR_RegistrManage/HR_RegistrCertificate/SaveData', this.entity).then(resJson => {
          this.loading = false

          if (resJson.Success) {
            this.$message.success('操作成功!')
            this.visible = false

            this.parentObj.getDataList()
          } else {
            this.$message.error(resJson.Msg)
          }
        })
      })
    }
  }
}
</script>
