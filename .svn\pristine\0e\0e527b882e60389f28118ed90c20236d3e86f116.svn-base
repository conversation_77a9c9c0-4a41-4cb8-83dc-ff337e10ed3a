<template>
  <a-modal :title="title" width="60%" :visible="visible" :confirmLoading="loading" @ok="handleSubmit" @cancel="
      () => {
        this.visible = false
      }
    ">
    <a-spin :spinning="loading">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="10">
            <a-col :md="4" :sm="24">
              <a-form-item label="查询类别">
                <a-select allowClear v-model="queryParam.condition">
                  <a-select-option key="F_InterviewName">面试名称</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="4" :sm="24">
              <a-form-item>
                <a-input v-model="queryParam.keyword" placeholder="关键字" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-button type="primary" icon="search" @click="
                  () => {
                    this.pagination.current = 1
                    this.getDataList()
                  }
                ">查询
              </a-button>
              <a-button style="margin-left: 8px" icon="sync" @click="() => (queryParam = {})">重置</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <a-table ref="table" :columns="columns" :rowKey="row => row.F_Id" :dataSource="data" :pagination="pagination"
        :loading="loading" @change="handleTableChange"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true">
        <span slot="F_Start" slot-scope="text">
          <template>
            <span>{{ text | dataFilter() }}</span>
          </template>
        </span>
        <span slot="F_End" slot-scope="text">
          <template>
            <span>{{ text | dataFilter() }}</span>
          </template>
        </span>
        <span slot="F_IsInterview" slot-scope="text">
          <template>
            <span>{{ text == 1 ? '是' : '否' }}</span>
          </template>
        </span>
      </a-table>
    </a-spin>
  </a-modal>
</template>

<script>
import { parseTime } from '@/utils/util.js'

const columns = [
  { title: '面试名称', dataIndex: 'F_InterviewName', width: 120, ellipsis: true },
  { title: '应聘者', dataIndex: 'ApplicantName', width: 100, ellipsis: true },
  { title: '开始时间', dataIndex: 'F_StartTime', width: 120, scopedSlots: { customRender: 'F_Start' } },
  { title: '结束时间', dataIndex: 'F_EndTime', width: 120, scopedSlots: { customRender: 'F_Start' } },
  { title: '是否参加面试', dataIndex: 'F_IsInterview', width: 150, scopedSlots: { customRender: 'F_IsInterview' } },
  { title: '原因', dataIndex: 'F_Reason', width: 200, ellipsis: true }
]
export default {
  props: {
    //回调方法，返回选中的员工
    callBack: Function,
    //是否多选
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 10,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      sorter: { field: 'F_CreateDate', order: 'desc' },
      columns,
      queryParam: { condition: "F_InterviewName" },
      visible: false,
      loading: false,
      selectedRowKeys: [],
      selectedRows: [], //选择行
      entity: {},
      rules: {},
      title: '面试信息'
    }
  },
  methods: {
    init () {
      this.visible = true
      this.selectedRows = []
      this.getList()
    },
    openForm () {
      this.init()
      //查询信息
      //this.getList();
    },
    handleTableChange (pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.sorter = { ...sorter }
      this.getList()
    },
    //查询信息
    getList () {
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/HR_Manage/HR_InterviewPlan/GetDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'F_Id',
          SortType: this.sorter.order,
          Search: this.queryParam
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      console.log(selectedRows)
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    handleSubmit () {
      if (this.selectedRows.length == 0) {
        this.$message.warning('必须选中一行')
        return
      }
      if (!this.multiple && this.selectedRows.length > 1) {
        this.$message.warning('只能选择一条数据')
        return
      }
      if (this.callBack) {
        this.visible = false
        this.callBack(this.selectedRows)
      }
    }
  }
}
</script>
