﻿using Coldairarrow.Business.Wechat_Shekou;
using Coldairarrow.Entity.Wechat_Shekou;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Newtonsoft.Json;

namespace Coldairarrow.Api.Controllers.Wechat_Shekou
{
    [Route("/Wechat_Shekou/[controller]/[action]")]
    public class Shekou_ContractController : BaseApiController
    {
        #region DI

        public Shekou_ContractController(IShekou_ContractBusiness shekou_ContractBus)
        {
            _shekou_ContractBus = shekou_ContractBus;
        }

        IShekou_ContractBusiness _shekou_ContractBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Shekou_Contract>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _shekou_ContractBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Shekou_Contract> GetTheData(IdInputDTO input)
        {
            return await _shekou_ContractBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交
        /// <summary>
        /// 小程序查询
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetMiniData()
        {
            string name = HttpContext.Request.Form["name"].ToString();
            string openId = HttpContext.Request.Form["openId"].ToString();
            if (openId.IsNullOrEmpty())
            {
                return Error("未获取到信息，请右上角重新进入小程序");
            }
            var data =_shekou_ContractBus.getDataByName(name);
            if (data.IsNullOrEmpty())
            {
                var newData = new Shekou_Contract() {
                    F_Id = Guid.NewGuid().ToString("N"),
                   F_Name = name,
                   F_CreateTime = DateTime.Now,
                    F_CreateUser = openId
                 };
                _shekou_ContractBus.AddDataAsync(newData).Wait();
                return Success(newData);
            }
            else
            {
               return Success(data);
            }
        }
        /// <summary>
        /// 小程序更新
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult UpdateMiniData()
        {
            string input = HttpContext.Request.Form["data"].ToString();
            var data = JsonConvert.DeserializeObject<Shekou_Contract>(input);
            if (data.F_UpdateUser.IsNullOrEmpty())
            {
                return Error("未获取到信息，请右上角重新进入小程序");
            }
            data.F_UpdateTime = DateTime.Now;
            _shekou_ContractBus.UpdateDataAsync(data).Wait();
            return Success(data);
        }

        /// <summary>
        /// 查询某个供应商
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult ChaContract(string name)
        {

            var data = _shekou_ContractBus.getDataByName(name);
            object result= new
            {
                data.F_User,
                data.F_Mobile
            };
            return Success(result);
        }

        [HttpPost]
        public async Task SaveData(Shekou_Contract data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _shekou_ContractBus.AddDataAsync(data);
            }
            else
            {
                await _shekou_ContractBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _shekou_ContractBus.DeleteDataAsync(ids);
        }

        #endregion
        

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _shekou_ContractBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Shekou_Contract>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _shekou_ContractBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}