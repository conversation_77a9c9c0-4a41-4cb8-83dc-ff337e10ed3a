﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.FileSystem.AccessControl</name>
  </assembly>
  <members>
    <member name="T:System.IO.FileSystemAclExtensions">
      <summary>Provides Windows-specific static extension methods for manipulating Access Control List (ACL) security attributes for files and directories.</summary>
    </member>
    <member name="M:System.IO.FileSystemAclExtensions.Create(System.IO.DirectoryInfo,System.Security.AccessControl.DirectorySecurity)">
      <summary>Creates a new directory, ensuring it is created with the specified directory security. If the directory already exists, nothing is done.</summary>
      <param name="directoryInfo">A directory that does not exist yet that will be created by the method.</param>
      <param name="directorySecurity">The access control and audit security for the directory.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="directoryInfo" /> or <paramref name="directorySecurity" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Could not find a part of the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to the path is denied.</exception>
    </member>
    <member name="M:System.IO.FileSystemAclExtensions.Create(System.IO.FileInfo,System.IO.FileMode,System.Security.AccessControl.FileSystemRights,System.IO.FileShare,System.Int32,System.IO.FileOptions,System.Security.AccessControl.FileSecurity)">
      <summary>Creates a new file stream, ensuring it is created with the specified properties and security settings.</summary>
      <param name="fileInfo">A file that does not exist yet that will be created by the method.</param>
      <param name="mode">One of the enumeration values that specifies how the operating system should open a file.</param>
      <param name="rights">One of the enumeration values that defines the access rights to use when creating access and audit rules.</param>
      <param name="share">One of the enumeration values for controlling the kind of access other file stream objects can have to the same file.</param>
      <param name="bufferSize">The number of bytes buffered for reads and writes to the file.</param>
      <param name="options">One of the enumeration values that describes how to create or overwrite the file.</param>
      <param name="fileSecurity">An object that determines the access control and audit security for the file.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="rights" /> and <paramref name="mode" /> combination is invalid.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileInfo" /> or <paramref name="fileSecurity" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> or <paramref name="share" /> are out of their legal enum range.

-or-

<paramref name="bufferSize" /> is not a positive number.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Could not find a part of the path.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to the path is denied.</exception>
      <returns>A file stream for the newly created file.</returns>
    </member>
    <member name="M:System.IO.FileSystemAclExtensions.CreateDirectory(System.Security.AccessControl.DirectorySecurity,System.String)">
      <summary>Creates a directory and returns it, ensuring it is created with the specified directory security. If the directory already exists, the existing directory is returned.</summary>
      <param name="directorySecurity">An object that determines the access control and audit security for the directory.</param>
      <param name="path">The path of the directory to create.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="directorySecurity" /> or <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is empty.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Could not find a part of the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to the path is denied.</exception>
      <returns>A directory information object representing either a created directory with the provided security properties, or the existing directory.</returns>
    </member>
    <member name="M:System.IO.FileSystemAclExtensions.GetAccessControl(System.IO.DirectoryInfo)">
      <summary>Returns the security information of a directory.</summary>
      <param name="directoryInfo">The existing directory from which to obtain the security information.</param>
      <returns>The security descriptors of all the access control sections of the directory.</returns>
    </member>
    <member name="M:System.IO.FileSystemAclExtensions.GetAccessControl(System.IO.DirectoryInfo,System.Security.AccessControl.AccessControlSections)">
      <summary>Returns the security information of a directory.</summary>
      <param name="directoryInfo">An existing directory from which to obtain the security information.</param>
      <param name="includeSections">The desired access control sections to retrieve.</param>
      <returns>The security descriptors of the specified access control sections of the directory.</returns>
    </member>
    <member name="M:System.IO.FileSystemAclExtensions.GetAccessControl(System.IO.FileInfo)">
      <summary>Returns the security information of a file.</summary>
      <param name="fileInfo">The file from which to obtain the security information.</param>
      <returns>The security descriptors of all the access control sections of the file.</returns>
    </member>
    <member name="M:System.IO.FileSystemAclExtensions.GetAccessControl(System.IO.FileInfo,System.Security.AccessControl.AccessControlSections)">
      <summary>Returns the security information of a file.</summary>
      <param name="fileInfo">An existing file from which to obtain the security information.</param>
      <param name="includeSections">The desired access control sections to retrieve from the file.</param>
      <returns>The security descriptors of the specified access control sections of the file.</returns>
    </member>
    <member name="M:System.IO.FileSystemAclExtensions.GetAccessControl(System.IO.FileStream)">
      <summary>Returns the security information of a file.</summary>
      <param name="fileStream">An existing file from which to obtain the security information.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileStream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The file stream is closed.</exception>
      <returns>The security descriptors of all the access control sections of the file.</returns>
    </member>
    <member name="M:System.IO.FileSystemAclExtensions.SetAccessControl(System.IO.DirectoryInfo,System.Security.AccessControl.DirectorySecurity)">
      <summary>Changes the security attributes of an existing directory.</summary>
      <param name="directoryInfo">An existing directory.</param>
      <param name="directorySecurity">The security information to apply to the directory.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="directorySecurity" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.IO.FileSystemAclExtensions.SetAccessControl(System.IO.FileInfo,System.Security.AccessControl.FileSecurity)">
      <summary>Changes the security attributes of an existing file.</summary>
      <param name="fileInfo">An existing file.</param>
      <param name="fileSecurity">The security information to apply to the file.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileSecurity" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.IO.FileSystemAclExtensions.SetAccessControl(System.IO.FileStream,System.Security.AccessControl.FileSecurity)">
      <summary>Changes the security attributes of an existing file.</summary>
      <param name="fileStream">An existing file.</param>
      <param name="fileSecurity">The security information to apply to the file.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileStream" /> or <paramref name="fileSecurity" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The file stream is closed.</exception>
    </member>
    <member name="T:System.Security.AccessControl.DirectoryObjectSecurity">
      <summary>Provides the ability to control access to directory objects without direct manipulation of Access Control Lists (ACLs).</summary>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> class.</summary>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.#ctor(System.Security.AccessControl.CommonSecurityDescriptor)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> class with the specified security descriptor.</summary>
      <param name="securityDescriptor">The security descriptor to be associated with the new <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> object.</param>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType,System.Guid,System.Guid)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.AccessRule" /> class with the specified values.</summary>
      <param name="identityReference">The identity to which the access rule applies.  It must be an object that can be cast as a <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">The access mask of this rule. The access mask is a 32-bit collection of anonymous bits, the meaning of which is defined by the individual integrators.</param>
      <param name="isInherited">true if this rule is inherited from a parent container.</param>
      <param name="inheritanceFlags">Specifies the inheritance properties of the access rule.</param>
      <param name="propagationFlags">Specifies whether inherited access rules are automatically propagated. The propagation flags are ignored if <paramref name="inheritanceFlags" /> is set to <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">Specifies the valid access control type.</param>
      <param name="objectType">The identity of the class of objects to which the new access rule applies.</param>
      <param name="inheritedObjectType">The identity of the class of child objects which can inherit the new access rule.</param>
      <returns>The <see cref="T:System.Security.AccessControl.AccessRule" /> object that this method creates.</returns>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.AddAccessRule(System.Security.AccessControl.ObjectAccessRule)">
      <summary>Adds the specified access rule to the Discretionary Access Control List (DACL) associated with this <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> object.</summary>
      <param name="rule">The access rule to add.</param>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.AddAuditRule(System.Security.AccessControl.ObjectAuditRule)">
      <summary>Adds the specified audit rule to the System Access Control List (SACL) associated with this <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> object.</summary>
      <param name="rule">The audit rule to add.</param>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags,System.Guid,System.Guid)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.AuditRule" /> class with the specified values.</summary>
      <param name="identityReference">The identity to which the audit rule applies.  It must be an object that can be cast as a <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="accessMask">The access mask of this rule. The access mask is a 32-bit collection of anonymous bits, the meaning of which is defined by the individual integrators.</param>
      <param name="isInherited">
        <see langword="true" /> if this rule is inherited from a parent container.</param>
      <param name="inheritanceFlags">Specifies the inheritance properties of the audit rule.</param>
      <param name="propagationFlags">Specifies whether inherited audit rules are automatically propagated. The propagation flags are ignored if <paramref name="inheritanceFlags" /> is set to <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="flags">Specifies the conditions for which the rule is audited.</param>
      <param name="objectType">The identity of the class of objects to which the new audit rule applies.</param>
      <param name="inheritedObjectType">The identity of the class of child objects which can inherit the new audit rule.</param>
      <returns>The <see cref="T:System.Security.AccessControl.AuditRule" /> object that this method creates.</returns>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.GetAccessRules(System.Boolean,System.Boolean,System.Type)">
      <summary>Gets a collection of the access rules associated with the specified security identifier.</summary>
      <param name="includeExplicit">
        <see langword="true" /> to include access rules explicitly set for the object.</param>
      <param name="includeInherited">
        <see langword="true" /> to include inherited access rules.</param>
      <param name="targetType">The security identifier for which to retrieve access rules. This must be an object that can be cast as a <see cref="T:System.Security.Principal.SecurityIdentifier" /> object.</param>
      <returns>The collection of access rules associated with the specified <see cref="T:System.Security.Principal.SecurityIdentifier" /> object.</returns>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.GetAuditRules(System.Boolean,System.Boolean,System.Type)">
      <summary>Gets a collection of the audit rules associated with the specified security identifier.</summary>
      <param name="includeExplicit">
        <see langword="true" /> to include audit rules explicitly set for the object.</param>
      <param name="includeInherited">
        <see langword="true" /> to include inherited audit rules.</param>
      <param name="targetType">The security identifier for which to retrieve audit rules. This must be an object that can be cast as a <see cref="T:System.Security.Principal.SecurityIdentifier" /> object.</param>
      <returns>The collection of audit rules associated with the specified <see cref="T:System.Security.Principal.SecurityIdentifier" /> object.</returns>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
      <summary>Applies the specified modification to the Discretionary Access Control List (DACL) associated with this <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> object.</summary>
      <param name="modification">The modification to apply to the DACL.</param>
      <param name="rule">The access rule to modify.</param>
      <param name="modified">
        <see langword="true" /> if the DACL is successfully modified; otherwise, <see langword="false" />.</param>
      <returns>
        <see langword="true" /> if the DACL is successfully modified; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
      <summary>Applies the specified modification to the System Access Control List (SACL) associated with this <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> object.</summary>
      <param name="modification">The modification to apply to the SACL.</param>
      <param name="rule">The audit rule to modify.</param>
      <param name="modified">
        <see langword="true" /> if the SACL is successfully modified; otherwise, <see langword="false" />.</param>
      <returns>
        <see langword="true" /> if the SACL is successfully modified; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.RemoveAccessRule(System.Security.AccessControl.ObjectAccessRule)">
      <summary>Removes access rules that contain the same security identifier and access mask as the specified access rule from the Discretionary Access Control List (DACL) associated with this <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> object.</summary>
      <param name="rule">The access rule to remove.</param>
      <returns>
        <see langword="true" /> if the access rule was successfully removed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.RemoveAccessRuleAll(System.Security.AccessControl.ObjectAccessRule)">
      <summary>Removes all access rules that have the same security identifier as the specified access rule from the Discretionary Access Control List (DACL) associated with this <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> object.</summary>
      <param name="rule">The access rule to remove.</param>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.RemoveAccessRuleSpecific(System.Security.AccessControl.ObjectAccessRule)">
      <summary>Removes all access rules that exactly match the specified access rule from the Discretionary Access Control List (DACL) associated with this <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> object.</summary>
      <param name="rule">The access rule to remove.</param>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.RemoveAuditRule(System.Security.AccessControl.ObjectAuditRule)">
      <summary>Removes audit rules that contain the same security identifier and access mask as the specified audit rule from the System Access Control List (SACL) associated with this <see cref="T:System.Security.AccessControl.CommonObjectSecurity" /> object.</summary>
      <param name="rule">The audit rule to remove.</param>
      <returns>
        <see langword="true" /> if the audit rule was successfully removed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.RemoveAuditRuleAll(System.Security.AccessControl.ObjectAuditRule)">
      <summary>Removes all audit rules that have the same security identifier as the specified audit rule from the System Access Control List (SACL) associated with this <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> object.</summary>
      <param name="rule">The audit rule to remove.</param>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.RemoveAuditRuleSpecific(System.Security.AccessControl.ObjectAuditRule)">
      <summary>Removes all audit rules that exactly match the specified audit rule from the System Access Control List (SACL) associated with this <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> object.</summary>
      <param name="rule">The audit rule to remove.</param>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.ResetAccessRule(System.Security.AccessControl.ObjectAccessRule)">
      <summary>Removes all access rules in the Discretionary Access Control List (DACL) associated with this <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> object and then adds the specified access rule.</summary>
      <param name="rule">The access rule to reset.</param>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.SetAccessRule(System.Security.AccessControl.ObjectAccessRule)">
      <summary>Removes all access rules that contain the same security identifier and qualifier as the specified access rule in the Discretionary Access Control List (DACL) associated with this <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> object and then adds the specified access rule.</summary>
      <param name="rule">The access rule to set.</param>
    </member>
    <member name="M:System.Security.AccessControl.DirectoryObjectSecurity.SetAuditRule(System.Security.AccessControl.ObjectAuditRule)">
      <summary>Removes all audit rules that contain the same security identifier and qualifier as the specified audit rule in the System Access Control List (SACL) associated with this <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity" /> object and then adds the specified audit rule.</summary>
      <param name="rule">The audit rule to set.</param>
    </member>
    <member name="T:System.Security.AccessControl.DirectorySecurity">
      <summary>Represents the access control and audit security for a directory. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.AccessControl.DirectorySecurity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.DirectorySecurity" /> class.</summary>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Microsoft Windows 2000 or later.</exception>
    </member>
    <member name="M:System.Security.AccessControl.DirectorySecurity.#ctor(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.DirectorySecurity" /> class from a specified directory using the specified values of the <see cref="T:System.Security.AccessControl.AccessControlSections" /> enumeration.</summary>
      <param name="name">The location of a directory to create a <see cref="T:System.Security.AccessControl.DirectorySecurity" /> object from.</param>
      <param name="includeSections">One of the <see cref="T:System.Security.AccessControl.AccessControlSections" /> values that specifies the type of access control list (ACL) information to retrieve.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="name" /> parameter is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="name" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in the <paramref name="name" /> parameter was not found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the directory.</exception>
      <exception cref="T:System.NotSupportedException">The <paramref name="name" /> parameter is in an invalid format.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Microsoft Windows 2000 or later.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.AccessControl.PrivilegeNotHeldException">The current system account does not have administrative privileges.</exception>
      <exception cref="T:System.SystemException">The directory could not be found.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <paramref name="name" /> parameter specified a directory that is read-only.  
  
 -or-  
  
 This operation is not supported on the current platform.  
  
 -or-  
  
 The caller does not have the required permission.</exception>
    </member>
    <member name="T:System.Security.AccessControl.FileSecurity">
      <summary>Represents the access control and audit security for a file. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.AccessControl.FileSecurity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.FileSecurity" /> class.</summary>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Microsoft Windows 2000 or later.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSecurity.#ctor(System.String,System.Security.AccessControl.AccessControlSections)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.FileSecurity" /> class from a specified file using the specified values of the <see cref="T:System.Security.AccessControl.AccessControlSections" /> enumeration.</summary>
      <param name="fileName">The location of a file to create a <see cref="T:System.Security.AccessControl.FileSecurity" /> object from.</param>
      <param name="includeSections">One of the <see cref="T:System.Security.AccessControl.AccessControlSections" /> values that specifies the type of access control list (ACL) information to retrieve.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="fileName" /> parameter is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in the <paramref name="fileName" /> parameter was not found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Runtime.InteropServices.SEHException">The <paramref name="fileName" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Microsoft Windows 2000 or later.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.AccessControl.PrivilegeNotHeldException">The current system account does not have administrative privileges.</exception>
      <exception cref="T:System.SystemException">The file could not be found.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <paramref name="fileName" /> parameter specified a file that is read-only.  
  
 -or-  
  
 This operation is not supported on the current platform.  
  
 -or-  
  
 The <paramref name="fileName" /> parameter specified a directory.  
  
 -or-  
  
 The caller does not have the required permission.</exception>
    </member>
    <member name="T:System.Security.AccessControl.FileSystemAccessRule">
      <summary>Represents an abstraction of an access control entry (ACE) that defines an access rule for a file or directory. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemAccessRule.#ctor(System.Security.Principal.IdentityReference,System.Security.AccessControl.FileSystemRights,System.Security.AccessControl.AccessControlType)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> class using a reference to a user account, a value that specifies the type of operation associated with the access rule, and a value that specifies whether to allow or deny the operation.</summary>
      <param name="identity">An <see cref="T:System.Security.Principal.IdentityReference" /> object that encapsulates a reference to a user account.</param>
      <param name="fileSystemRights">One of the <see cref="T:System.Security.AccessControl.FileSystemRights" /> values that specifies the type of operation associated with the access rule.</param>
      <param name="type">One of the <see cref="T:System.Security.AccessControl.AccessControlType" /> values that specifies whether to allow or deny the operation.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="identity" /> parameter is not an <see cref="T:System.Security.Principal.IdentityReference" /> object.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="identity" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An incorrect enumeration was passed to the <paramref name="type" /> parameter.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemAccessRule.#ctor(System.Security.Principal.IdentityReference,System.Security.AccessControl.FileSystemRights,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> class using a reference to a user account, a value that specifies the type of operation associated with the access rule, a value that determines how rights are inherited, a value that determines how rights are propagated, and a value that specifies whether to allow or deny the operation.</summary>
      <param name="identity">An <see cref="T:System.Security.Principal.IdentityReference" /> object that encapsulates a reference to a user account.</param>
      <param name="fileSystemRights">One of the <see cref="T:System.Security.AccessControl.FileSystemRights" /> values that specifies the type of operation associated with the access rule.</param>
      <param name="inheritanceFlags">One of the <see cref="T:System.Security.AccessControl.InheritanceFlags" /> values that specifies how access masks are propagated to child objects.</param>
      <param name="propagationFlags">One of the <see cref="T:System.Security.AccessControl.PropagationFlags" /> values that specifies how Access Control Entries (ACEs) are propagated to child objects.</param>
      <param name="type">One of the <see cref="T:System.Security.AccessControl.AccessControlType" /> values that specifies whether to allow or deny the operation.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="identity" /> parameter is not an <see cref="T:System.Security.Principal.IdentityReference" /> object.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="identity" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An incorrect enumeration was passed to the <paramref name="type" /> parameter.  
  
 -or-  
  
 An incorrect enumeration was passed to the <paramref name="inheritanceFlags" /> parameter.  
  
 -or-  
  
 An incorrect enumeration was passed to the <paramref name="propagationFlags" /> parameter.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemAccessRule.#ctor(System.String,System.Security.AccessControl.FileSystemRights,System.Security.AccessControl.AccessControlType)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> class using the name of a user account, a value that specifies the type of operation associated with the access rule, and a value that describes whether to allow or deny the operation.</summary>
      <param name="identity">The name of a user account.</param>
      <param name="fileSystemRights">One of the <see cref="T:System.Security.AccessControl.FileSystemRights" /> values that specifies the type of operation associated with the access rule.</param>
      <param name="type">One of the <see cref="T:System.Security.AccessControl.AccessControlType" /> values that specifies whether to allow or deny the operation.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="identity" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An incorrect enumeration was passed to the <paramref name="type" /> parameter.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemAccessRule.#ctor(System.String,System.Security.AccessControl.FileSystemRights,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> class using the name of a user account, a value that specifies the type of operation associated with the access rule, a value that determines how rights are inherited, a value that determines how rights are propagated, and a value that specifies whether to allow or deny the operation.</summary>
      <param name="identity">The name of a user account.</param>
      <param name="fileSystemRights">One of the <see cref="T:System.Security.AccessControl.FileSystemRights" /> values that specifies the type of operation associated with the access rule.</param>
      <param name="inheritanceFlags">One of the <see cref="T:System.Security.AccessControl.InheritanceFlags" /> values that specifies how access masks are propagated to child objects.</param>
      <param name="propagationFlags">One of the <see cref="T:System.Security.AccessControl.PropagationFlags" /> values that specifies how Access Control Entries (ACEs) are propagated to child objects.</param>
      <param name="type">One of the <see cref="T:System.Security.AccessControl.AccessControlType" /> values that specifies whether to allow or deny the operation.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="identity" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An incorrect enumeration was passed to the <paramref name="type" /> parameter.  
  
 -or-  
  
 An incorrect enumeration was passed to the <paramref name="inheritanceFlags" /> parameter.  
  
 -or-  
  
 An incorrect enumeration was passed to the <paramref name="propagationFlags" /> parameter.</exception>
    </member>
    <member name="P:System.Security.AccessControl.FileSystemAccessRule.FileSystemRights">
      <summary>Gets the <see cref="T:System.Security.AccessControl.FileSystemRights" /> flags associated with the current <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> object.</summary>
      <returns>The <see cref="T:System.Security.AccessControl.FileSystemRights" /> flags associated with the current <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> object.</returns>
    </member>
    <member name="T:System.Security.AccessControl.FileSystemAuditRule">
      <summary>Represents an abstraction of an access control entry (ACE) that defines an audit rule for a file or directory. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemAuditRule.#ctor(System.Security.Principal.IdentityReference,System.Security.AccessControl.FileSystemRights,System.Security.AccessControl.AuditFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.FileSystemAuditRule" /> class using a reference to a user account, a value that specifies the type of operation associated with the audit rule, and a value that specifies when to perform auditing.</summary>
      <param name="identity">An <see cref="T:System.Security.Principal.IdentityReference" /> object that encapsulates a reference to a user account.</param>
      <param name="fileSystemRights">One of the <see cref="T:System.Security.AccessControl.FileSystemRights" /> values that specifies the type of operation associated with the audit rule.</param>
      <param name="flags">One of the <see cref="T:System.Security.AccessControl.AuditFlags" /> values that specifies when to perform auditing.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="identity" /> parameter is not an <see cref="T:System.Security.Principal.IdentityReference" /> object.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="identity" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An incorrect enumeration was passed to the <paramref name="flags" /> parameter.  
  
 -or-  
  
 The <see cref="F:System.Security.AccessControl.AuditFlags.None" /> value was passed to the <paramref name="flags" /> parameter.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemAuditRule.#ctor(System.Security.Principal.IdentityReference,System.Security.AccessControl.FileSystemRights,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.FileSystemAuditRule" /> class using the name of a reference to a user account, a value that specifies the type of operation associated with the audit rule, a value that determines how rights are inherited, a value that determines how rights are propagated, and a value that specifies when to perform auditing.</summary>
      <param name="identity">An <see cref="T:System.Security.Principal.IdentityReference" /> object that encapsulates a reference to a user account.</param>
      <param name="fileSystemRights">One of the <see cref="T:System.Security.AccessControl.FileSystemRights" /> values that specifies the type of operation associated with the audit rule.</param>
      <param name="inheritanceFlags">One of the <see cref="T:System.Security.AccessControl.InheritanceFlags" /> values that specifies how access masks are propagated to child objects.</param>
      <param name="propagationFlags">One of the <see cref="T:System.Security.AccessControl.PropagationFlags" /> values that specifies how Access Control Entries (ACEs) are propagated to child objects.</param>
      <param name="flags">One of the <see cref="T:System.Security.AccessControl.AuditFlags" /> values that specifies when to perform auditing.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="identity" /> parameter is not an <see cref="T:System.Security.Principal.IdentityReference" /> object.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="identity" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An incorrect enumeration was passed to the <paramref name="flags" /> parameter.  
  
 -or-  
  
 The <see cref="F:System.Security.AccessControl.AuditFlags.None" /> value was passed to the <paramref name="flags" /> parameter.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemAuditRule.#ctor(System.String,System.Security.AccessControl.FileSystemRights,System.Security.AccessControl.AuditFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.FileSystemAuditRule" /> class using a user account name, a value that specifies the type of operation associated with the audit rule, and a value that specifies when to perform auditing.</summary>
      <param name="identity">The name of a user account.</param>
      <param name="fileSystemRights">One of the <see cref="T:System.Security.AccessControl.FileSystemRights" /> values that specifies the type of operation associated with the audit rule.</param>
      <param name="flags">One of the <see cref="T:System.Security.AccessControl.AuditFlags" /> values that specifies when to perform auditing.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">An incorrect enumeration was passed to the <paramref name="flags" /> parameter.  
  
 -or-  
  
 The <see cref="F:System.Security.AccessControl.AuditFlags.None" /> value was passed to the <paramref name="flags" /> parameter.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemAuditRule.#ctor(System.String,System.Security.AccessControl.FileSystemRights,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.FileSystemAuditRule" /> class using the name of a user account, a value that specifies the type of operation associated with the audit rule, a value that determines how rights are inherited, a value that determines how rights are propagated, and a value that specifies when to perform auditing.</summary>
      <param name="identity">The name of a user account.</param>
      <param name="fileSystemRights">One of the <see cref="T:System.Security.AccessControl.FileSystemRights" /> values that specifies the type of operation associated with the audit rule.</param>
      <param name="inheritanceFlags">One of the <see cref="T:System.Security.AccessControl.InheritanceFlags" /> values that specifies how access masks are propagated to child objects.</param>
      <param name="propagationFlags">One of the <see cref="T:System.Security.AccessControl.PropagationFlags" /> values that specifies how Access Control Entries (ACEs) are propagated to child objects.</param>
      <param name="flags">One of the <see cref="T:System.Security.AccessControl.AuditFlags" /> values that specifies when to perform auditing.</param>
    </member>
    <member name="P:System.Security.AccessControl.FileSystemAuditRule.FileSystemRights">
      <summary>Gets the <see cref="T:System.Security.AccessControl.FileSystemRights" /> flags associated with the current <see cref="T:System.Security.AccessControl.FileSystemAuditRule" /> object.</summary>
      <returns>The <see cref="T:System.Security.AccessControl.FileSystemRights" /> flags associated with the current <see cref="T:System.Security.AccessControl.FileSystemAuditRule" /> object.</returns>
    </member>
    <member name="T:System.Security.AccessControl.FileSystemRights">
      <summary>Defines the access rights to use when creating access and audit rules.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.AppendData">
      <summary>Specifies the right to append data to the end of a file.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.ChangePermissions">
      <summary>Specifies the right to change the security and audit rules associated with a file or folder.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.CreateDirectories">
      <summary>Specifies the right to create a folder This right requires the <c>Synchronize</c> value.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.CreateFiles">
      <summary>Specifies the right to create a file. This right requires the <c>Synchronize</c> value.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.Delete">
      <summary>Specifies the right to delete a folder or file.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.DeleteSubdirectoriesAndFiles">
      <summary>Specifies the right to delete a folder and any files contained within that folder.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.ExecuteFile">
      <summary>Specifies the right to run an application file.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.FullControl">
      <summary>Specifies the right to exert full control over a folder or file, and to modify access control and audit rules.  This value represents the right to do anything with a file and is the combination of all rights in this enumeration.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.ListDirectory">
      <summary>Specifies the right to read the contents of a directory.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.Modify">
      <summary>Specifies the right to read, write, list folder contents, delete folders and files, and run application files.  This right includes the <see cref="F:System.Security.AccessControl.FileSystemRights.ReadAndExecute" /> right, the <see cref="F:System.Security.AccessControl.FileSystemRights.Write" /> right, and the <see cref="F:System.Security.AccessControl.FileSystemRights.Delete" /> right.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.Read">
      <summary>Specifies the right to open and copy folders or files as read-only.  This right includes the <see cref="F:System.Security.AccessControl.FileSystemRights.ReadData" /> right, <see cref="F:System.Security.AccessControl.FileSystemRights.ReadExtendedAttributes" /> right, <see cref="F:System.Security.AccessControl.FileSystemRights.ReadAttributes" /> right, and <see cref="F:System.Security.AccessControl.FileSystemRights.ReadPermissions" /> right.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.ReadAndExecute">
      <summary>Specifies the right to open and copy folders or files as read-only, and to run application files.  This right includes the <see cref="F:System.Security.AccessControl.FileSystemRights.Read" /> right and the <see cref="F:System.Security.AccessControl.FileSystemRights.ExecuteFile" /> right.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.ReadAttributes">
      <summary>Specifies the right to open and copy file system attributes from a folder or file.  For example, this value specifies the right to view the file creation or modified date.  This does not include the right to read data, extended file system attributes, or access and audit rules.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.ReadData">
      <summary>Specifies the right to open and copy a file or folder.  This does not include the right to read file system attributes, extended file system attributes, or access and audit rules.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.ReadExtendedAttributes">
      <summary>Specifies the right to open and copy extended file system attributes from a folder or file.  For example, this value specifies the right to view author and content information.  This does not include the right to read data, file system attributes, or access and audit rules.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.ReadPermissions">
      <summary>Specifies the right to open and copy access and audit rules from a folder or file.  This does not include the right to read data, file system attributes, and extended file system attributes.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.Synchronize">
      <summary>Specifies whether the application can wait for a file handle to synchronize with the completion of an I/O operation. This value is automatically set when allowing access and automatically excluded when denying access.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.TakeOwnership">
      <summary>Specifies the right to change the owner of a folder or file.  Note that owners of a resource have full access to that resource.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.Traverse">
      <summary>Specifies the right to list the contents of a folder and to run applications contained within that folder.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.Write">
      <summary>Specifies the right to create folders and files, and to add or remove data from files.  This right includes the <see cref="F:System.Security.AccessControl.FileSystemRights.WriteData" /> right, <see cref="F:System.Security.AccessControl.FileSystemRights.AppendData" /> right, <see cref="F:System.Security.AccessControl.FileSystemRights.WriteExtendedAttributes" /> right, and <see cref="F:System.Security.AccessControl.FileSystemRights.WriteAttributes" /> right.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.WriteAttributes">
      <summary>Specifies the right to open and write file system attributes to a folder or file. This does not include the ability to write data, extended attributes, or access and audit rules.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.WriteData">
      <summary>Specifies the right to open and write to a file or folder.  This does not include the right to open and write file system attributes, extended file system attributes, or access and audit rules.</summary>
    </member>
    <member name="F:System.Security.AccessControl.FileSystemRights.WriteExtendedAttributes">
      <summary>Specifies the right to open and write extended file system attributes to a folder or file.  This does not include the ability to write data, attributes, or access and audit rules.</summary>
    </member>
    <member name="T:System.Security.AccessControl.FileSystemSecurity">
      <summary>Represents the access control and audit security for a file or directory.</summary>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemSecurity.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> class that represents a new access control rule for the specified user, with the specified access rights, access control, and flags.</summary>
      <param name="identityReference">An <see cref="T:System.Security.Principal.IdentityReference" /> object that represents a user account.</param>
      <param name="accessMask">An integer that specifies an access type.</param>
      <param name="isInherited">
        <see langword="true" /> if the access rule is inherited; otherwise, <see langword="false" />.</param>
      <param name="inheritanceFlags">One of the <see cref="T:System.Security.AccessControl.InheritanceFlags" /> values that specifies how to propagate access masks to child objects.</param>
      <param name="propagationFlags">One of the <see cref="T:System.Security.AccessControl.PropagationFlags" /> values that specifies how to propagate Access Control Entries (ACEs) to child objects.</param>
      <param name="type">One of the <see cref="T:System.Security.AccessControl.AccessControlType" /> values that specifies whether access is allowed or denied.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="accessMask" />, <paramref name="inheritanceFlags" />, <paramref name="propagationFlags" />, or <paramref name="type" /> parameters specify an invalid value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="identityReference" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The <paramref name="accessMask" /> parameter is zero.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="identityReference" /> parameter is neither of type <see cref="T:System.Security.Principal.SecurityIdentifier" />, nor of a type such as <see cref="T:System.Security.Principal.NTAccount" /> that can be converted to type <see cref="T:System.Security.Principal.SecurityIdentifier" />.</exception>
      <returns>A new <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> object that represents a new access control rule for the specified user, with the specified access rights, access control, and flags.</returns>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemSecurity.AddAccessRule(System.Security.AccessControl.FileSystemAccessRule)">
      <summary>Adds the specified access control list (ACL) permission to the current file or directory.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> object that represents an access control list (ACL) permission to add to a file or directory.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemSecurity.AddAuditRule(System.Security.AccessControl.FileSystemAuditRule)">
      <summary>Adds the specified audit rule to the current file or directory.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.FileSystemAuditRule" /> object that represents an audit rule to add to a file or directory.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemSecurity.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.FileSystemAuditRule" /> class representing the specified audit rule for the specified user.</summary>
      <param name="identityReference">An <see cref="T:System.Security.Principal.IdentityReference" /> object that represents a user account.</param>
      <param name="accessMask">An integer that specifies an access type.</param>
      <param name="isInherited">
        <see langword="true" /> if the access rule is inherited; otherwise, <see langword="false" />.</param>
      <param name="inheritanceFlags">One of the <see cref="T:System.Security.AccessControl.InheritanceFlags" /> values that specifies how to propagate access masks to child objects.</param>
      <param name="propagationFlags">One of the <see cref="T:System.Security.AccessControl.PropagationFlags" /> values that specifies how to propagate Access Control Entries (ACEs) to child objects.</param>
      <param name="flags">One of the <see cref="T:System.Security.AccessControl.AuditFlags" /> values that specifies the type of auditing to perform.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="accessMask" />, <paramref name="inheritanceFlags" />, <paramref name="propagationFlags" />, or <paramref name="flags" /> properties specify an invalid value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="identityReference" /> property is <see langword="null" />.  
  
 -or-  
  
 The <paramref name="accessMask" /> property is zero.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="identityReference" /> property is neither of type <see cref="T:System.Security.Principal.SecurityIdentifier" />, nor of a type such as <see cref="T:System.Security.Principal.NTAccount" /> that can be converted to type <see cref="T:System.Security.Principal.SecurityIdentifier" />.</exception>
      <returns>A new <see cref="T:System.Security.AccessControl.FileSystemAuditRule" /> object representing the specified audit rule for the specified user.</returns>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemSecurity.RemoveAccessRule(System.Security.AccessControl.FileSystemAccessRule)">
      <summary>Removes all matching allow or deny access control list (ACL) permissions from the current file or directory.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> object that represents an access control list (ACL) permission to remove from a file or directory.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule" /> parameter is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if the access rule was removed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemSecurity.RemoveAccessRuleAll(System.Security.AccessControl.FileSystemAccessRule)">
      <summary>Removes all access control list (ACL) permissions for the specified user from the current file or directory.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> object that specifies a user whose access control list (ACL) permissions should be removed from a file or directory.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemSecurity.RemoveAccessRuleSpecific(System.Security.AccessControl.FileSystemAccessRule)">
      <summary>Removes a single matching allow or deny access control list (ACL) permission from the current file or directory.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> object that specifies a user whose access control list (ACL) permissions should be removed from a file or directory.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemSecurity.RemoveAuditRule(System.Security.AccessControl.FileSystemAuditRule)">
      <summary>Removes all matching allow or deny audit rules from the current file or directory.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.FileSystemAuditRule" /> object that represents an audit rule to remove from a file or directory.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule" /> parameter is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if the audit rule was removed; otherwise, <see langword="false" /></returns>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemSecurity.RemoveAuditRuleAll(System.Security.AccessControl.FileSystemAuditRule)">
      <summary>Removes all audit rules for the specified user from the current file or directory.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.FileSystemAuditRule" /> object that specifies a user whose audit rules should be removed from a file or directory.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemSecurity.RemoveAuditRuleSpecific(System.Security.AccessControl.FileSystemAuditRule)">
      <summary>Removes a single matching allow or deny audit rule from the current file or directory.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.FileSystemAuditRule" /> object that represents an audit rule to remove from a file or directory.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemSecurity.ResetAccessRule(System.Security.AccessControl.FileSystemAccessRule)">
      <summary>Adds the specified access control list (ACL) permission to the current file or directory and removes all matching ACL permissions.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> object that represents an access control list (ACL) permission to add to a file or directory.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemSecurity.SetAccessRule(System.Security.AccessControl.FileSystemAccessRule)">
      <summary>Sets the specified access control list (ACL) permission for the current file or directory.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> object that represents an access control list (ACL) permission to set for a file or directory.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.FileSystemSecurity.SetAuditRule(System.Security.AccessControl.FileSystemAuditRule)">
      <summary>Sets the specified audit rule for the current file or directory.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.FileSystemAuditRule" /> object that represents an audit rule to set for a file or directory.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.AccessControl.FileSystemSecurity.AccessRightType">
      <summary>Gets the enumeration that the <see cref="T:System.Security.AccessControl.FileSystemSecurity" /> class uses to represent access rights.</summary>
      <returns>A <see cref="T:System.Type" /> object representing the <see cref="T:System.Security.AccessControl.FileSystemRights" /> enumeration.</returns>
    </member>
    <member name="P:System.Security.AccessControl.FileSystemSecurity.AccessRuleType">
      <summary>Gets the enumeration that the <see cref="T:System.Security.AccessControl.FileSystemSecurity" /> class uses to represent access rules.</summary>
      <returns>A <see cref="T:System.Type" /> object representing the <see cref="T:System.Security.AccessControl.FileSystemAccessRule" /> class.</returns>
    </member>
    <member name="P:System.Security.AccessControl.FileSystemSecurity.AuditRuleType">
      <summary>Gets the type that the <see cref="T:System.Security.AccessControl.FileSystemSecurity" /> class uses to represent audit rules.</summary>
      <returns>A <see cref="T:System.Type" /> object representing the <see cref="T:System.Security.AccessControl.FileSystemAuditRule" /> class.</returns>
    </member>
  </members>
</doc>