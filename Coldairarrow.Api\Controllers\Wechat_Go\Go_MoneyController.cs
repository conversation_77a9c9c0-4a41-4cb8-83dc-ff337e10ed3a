﻿using Coldairarrow.Business.Wechat_Go;
using Coldairarrow.Entity.Wechat_Go;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Coldairarrow.Util.Helper;

namespace Coldairarrow.Api.Controllers.Wechat_Go
{
    [Route("/Wechat_Go/[controller]/[action]")]
    public class Go_MoneyController : BaseApiController
    {
        #region DI

        public Go_MoneyController(IGo_MoneyBusiness go_MoneyBus, IGo_ConfigBusiness go_ConfigBusiness)
        {
            _go_MoneyBus = go_MoneyBus;
            _go_ConfigBusiness = go_ConfigBusiness;
        }

        IGo_MoneyBusiness _go_MoneyBus { get; }
        IGo_ConfigBusiness _go_ConfigBusiness { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Go_Money>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _go_MoneyBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Go_Money> GetTheData(IdInputDTO input)
        {
            return await _go_MoneyBus.GetTheDataAsync(input.id);
        }

        
        /// <summary>
        /// 获取总数
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetTotal()
        {
            try
            {
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var data = _go_MoneyBus.GetTotalMoney(teamId);
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }

        }
        /// <summary>
        /// 获取总数
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetTotalMoney()
        {
            try
            {
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var money = _go_MoneyBus.GetTotalMoney(teamId);
                var moneyTotal = int.Parse(_go_ConfigBusiness.GetListByType("Money").FirstOrDefault().F_Content);
                var moneyUser = int.Parse(_go_ConfigBusiness.GetListByType("useMoney").FirstOrDefault().F_Content);
                var data = new Go_MoneyTotal() { 
                    totalMoney = moneyTotal +money,
                    userMoney = moneyUser,
                    surplusMoney = moneyTotal +money -moneyUser
                };
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }

        }
        /// <summary>
        /// 获取列表页数据
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetHotList()
        {
            try
            {
                var hot = Convert.ToInt32(HttpContext.Request.Form["hot"].ToString());
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var list = _go_MoneyBus.GetHotDataAsync(teamId, hot);
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
            
        }
        /// <summary>
        /// 获取某人的数据
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetUserList()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var list = _go_MoneyBus.GetUserDataAsync(openId, teamId);
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }

        }

        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetUserTotal()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var list = _go_MoneyBus.GetUserTotalMoney(openId, teamId);
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }

        }

        /// <summary>
        /// 缴纳某人钱
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult addMoney()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                string username = HttpContext.Request.Form["username"].ToString();
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var money = Convert.ToInt32(HttpContext.Request.Form["money"].ToString());
                var queryBeginDate = TimeStampsHelper.GoToDateTime(HttpContext.Request.Form["beginDate"].ToString());
                var queryEndDate = TimeStampsHelper.GoToDateTime(HttpContext.Request.Form["endDate"].ToString());
                var data = new Go_Money
                {
                    F_Id = Guid.NewGuid().ToString("N"),
                    F_CreateTime = DateTime.Now,
                    F_OpenId = openId,
                    F_TeamId = teamId,
                    F_Isable = 1,
                    F_Money = money,
                    F_UserName = username,
                    F_Descri = money==0?"请假": RoundDescri(),
                    F_StartDate = queryBeginDate,
                    F_EndDate = queryEndDate,
                    F_Date = queryBeginDate
                };
                     _go_MoneyBus.AddDataAsync(data).Wait();
                    return Success("缴纳成功");

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Go_Money data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _go_MoneyBus.AddDataAsync(data);
            }
            else
            {
                await _go_MoneyBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _go_MoneyBus.DeleteDataAsync(ids);
        }

        #endregion
        

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _go_MoneyBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Go_Money>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _go_MoneyBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion

        #region
        /// <summary>
        /// 
        /// </summary>
        /// <param name="money"></param>
        /// <returns></returns>
        public string RoundDescri()
        {
            var result = "一份回锅肉";
            Random rd = new Random();
            var number = rd.Next(1, 11);
            switch (number) {
                case 1:result = "一份毛血旺";break;
                case 2: result = "一份毛肚"; break;
                case 3: result = "一份鹅肠"; break;
                case 4: result = "一份黄喉"; break;
                case 5: result = "一份来凤鱼"; break;
                case 6: result = "一份辣子鸡"; break;
                case 7: result = "一份酸菜鱼"; break;
                case 8: result = "一份烧鸡公"; break;
                case 9: result = "一份猪黄喉"; break;
                case 10: result = "一份水煮鱼"; break;
                default:result = "一份回锅肉";break;
            }
            return result;
        }
        #endregion
    }
}