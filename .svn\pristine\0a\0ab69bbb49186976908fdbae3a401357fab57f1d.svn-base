/**
 * 处理下载的报表
 * @param {file}  文件对象
 * @param {filename}  需要保存的文件对象名称
 * 保存的文件格式为：filename-年月日.xlsx
 */
export function operateFile (file, filename) {
  let fileName = filename + "-" + new Date().getFullYear() + '' + (new Date().getMonth() + 1) + '' + new Date().getDate() + ".xls";
  let blobObject = new Blob([file], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
  //是IE浏览器
  if (!!window.ActiveXObject || "ActiveXObject" in window) {
    window.navigator.msSaveOrOpenBlob(blobObject, fileName);
  } else {//火狐谷歌都兼容
    let aTag = document.createElement('a')
    aTag.download = fileName
    aTag.href = URL.createObjectURL(blobObject)
    aTag.click()
    URL.revokeObjectURL(aTag.href)
  }
}
export function operateFileFun (file, filename) {
  let fileName = filename;
  let blobObject = new Blob([file], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
  //是IE浏览器
  if (!!window.ActiveXObject || "ActiveXObject" in window) {
    window.navigator.msSaveOrOpenBlob(blobObject, fileName);
  } else {//火狐谷歌都兼容
    let aTag = document.createElement('a')
    aTag.download = fileName
    aTag.href = URL.createObjectURL(blobObject)
    aTag.click()
    URL.revokeObjectURL(aTag.href)
  }
}
