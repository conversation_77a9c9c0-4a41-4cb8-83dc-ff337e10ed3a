﻿using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.HR_EmployeeInfoManage
{
    [Route("/HR_EmployeeInfoManage/[controller]/[action]")]
    public class HR_DisciplinaryRecordsController : BaseApiController
    {
        #region DI

        public HR_DisciplinaryRecordsController(IHR_DisciplinaryRecordsBusiness hR_DisciplinaryRecordsBus, IHR_FormalEmployeesBusiness hR_FormalEmployeesBus)
        {
            _hR_DisciplinaryRecordsBus = hR_DisciplinaryRecordsBus;
            _hR_FormalEmployeesBus = hR_FormalEmployeesBus;
        }

        IHR_DisciplinaryRecordsBusiness _hR_DisciplinaryRecordsBus { get; }
        IHR_FormalEmployeesBusiness _hR_FormalEmployeesBus { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_DisciplinaryRecordsDTO>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_DisciplinaryRecordsBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_DisciplinaryRecords> GetTheData(IdInputDTO input)
        {
            return await _hR_DisciplinaryRecordsBus.GetFormDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_DisciplinaryRecords data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);
                data.F_BusState = (int)ASKBusState.正常;
                await _hR_DisciplinaryRecordsBus.AddDataAsync(data);
            }
            else
            {
                await _hR_DisciplinaryRecordsBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_DisciplinaryRecordsBus.DeleteDataAsync(ids);
        }

        #endregion

        #region 导入数据
        /// <summary>
        /// 导入资料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public IActionResult UploadFileByForm(string ID)
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["DisciplinaryType"] = "奖惩类型";
            ht["DisciplinaryStartTime"] = "奖惩起始时间";
            ht["DisciplinaryEndTime"] = "奖惩结束时间";
            ht["DisciplinaryContent"] = "奖惩内容";
            ht["Remark"] = "描述";

            var list = new ExcelHelper<HR_DisciplinaryRecords>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                List<HR_DisciplinaryRecords> hR_DisciplinaryRecords = new List<HR_DisciplinaryRecords>();
                foreach (var disciplinaryRecords in list)
                {
                    InitEntity(disciplinaryRecords);
                    disciplinaryRecords.F_BusState = (int)ASKBusState.正常;
                    disciplinaryRecords.UserId =ID;
                    disciplinaryRecords.F_CreateUserId = "Admin";
                    disciplinaryRecords.F_CreateUserName = "超级管理员";
                    hR_DisciplinaryRecords.Add(disciplinaryRecords);
                }
                _hR_DisciplinaryRecordsBus.AddDataListAsync(hR_DisciplinaryRecords);
            }
            var res = new
            {
                name = file.FileName,
                status = "done",
            };

            return JsonContent(res.ToJson());
        }

        /// <summary>
        /// 导入资料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm1()
        {
            var file = Request.Form.Files.FirstOrDefault();
            var key = Request.Form["id"];
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["DisciplinaryType"] = "奖惩类型";
            ht["DisciplinaryStartTime"] = "奖惩起始时间";
            ht["DisciplinaryEndTime"] = "奖惩结束时间";
            ht["DisciplinaryContent"] = "奖惩内容";
            ht["Remark"] = "描述";

            var list = new ExcelHelper<HR_DisciplinaryRecords>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                List<HR_DisciplinaryRecords> hR_DisciplinaryRecords = new List<HR_DisciplinaryRecords>();
                foreach (var disciplinaryRecords in list)
                {
                    InitEntity(disciplinaryRecords);
                    disciplinaryRecords.F_BusState = (int)ASKBusState.正常;
                    disciplinaryRecords.UserId = key;
                    disciplinaryRecords.F_CreateUserId = "Admin";
                    disciplinaryRecords.F_CreateUserName = "超级管理员";
                    hR_DisciplinaryRecords.Add(disciplinaryRecords);
                }
                _hR_DisciplinaryRecordsBus.AddDataListAsync(hR_DisciplinaryRecords);
            }
            var res = new
            {
                name = file.FileName,
                status = "done",
            };

            return JsonContent(res.ToJson());
        }
        #endregion


        #region 导出Excel
        /// <summary>
        /// 模板下载
        /// </summary>
        [HttpPost]
        public FileContentResult DownloadTemplate()
        {
            return File(FileHelper.DownloadTemplate("/BusinessTemplate/奖惩记录模板.xls"), "application/vnd.ms-excel");
            //string filePath = Directory.GetCurrentDirectory() + "/BusinessTemplate/绩效模板.xls";
            //if (FileHelper.Exists(filePath))
            //{
            //    var bys = System.IO.File.ReadAllBytes(filePath);//excel表转换成流
            //    return File(bys, "application/vnd.ms-excel");
            //}
            //else
            //{
            //    throw new System.Exception("找不到模板");
            //}

        }
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name = "dtSource" > DataTable数据源 </ param >
        /// < param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name = "isSort" > 是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name = "dataList" > 多行表头数据集 </ param >
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_DisciplinaryRecordsBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "奖惩单.xlsx";
                //excelconfig.Title = "奖惩单";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "DisciplinaryType".ToLower(), ExcelColumn = "奖惩类型", Alignment = "left", Sort = 1 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "DisStartTime".ToLower(), ExcelColumn = "奖惩起始时间", Alignment = "left", Sort = 2 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "DisEndTime".ToLower(), ExcelColumn = "奖惩结束时间", Alignment = "left", Sort = 3 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "DisciplinaryContent".ToLower(), ExcelColumn = "奖惩内容", Alignment = "left", Sort = 4 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "Remark".ToLower(), ExcelColumn = "描述", Alignment = "left", Sort = 5 });
                //ExcelHelper.ExcelDownload(exportTable, excelconfig);
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion
        public class textData
        {
            public string ID { get; set; }
            public string name { get; set; }
        }
    }
}