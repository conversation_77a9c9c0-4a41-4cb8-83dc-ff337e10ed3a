<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Serilog.Settings.Configuration</name>
    </assembly>
    <members>
        <member name="T:Serilog.ConfigurationLoggerConfigurationExtensions">
            <summary>
            Extends <see cref="T:Serilog.LoggerConfiguration"/> with support for System.Configuration appSettings elements.
            </summary>
        </member>
        <member name="F:Serilog.ConfigurationLoggerConfigurationExtensions.DefaultSectionName">
            <summary>
            Configuration section name required by this package.
            </summary>
        </member>
        <member name="M:Serilog.ConfigurationLoggerConfigurationExtensions.Configuration(Serilog.Configuration.LoggerSettingsConfiguration,Microsoft.Extensions.Configuration.IConfiguration,System.String,Microsoft.Extensions.DependencyModel.DependencyContext)">
            <summary>
            Reads logger settings from the provided configuration object using the provided section name. Generally this
            is preferable over the other method that takes a configuration section. Only this version will populate
            IConfiguration parameters on target methods.
            </summary>
            <param name="settingConfiguration">Logger setting configuration.</param>
            <param name="configuration">A configuration object which contains a Serilog section.</param>
            <param name="sectionName">A section name for section which contains a Serilog section.</param>
            <param name="dependencyContext">The dependency context from which sink/enricher packages can be located. If not supplied, the platform
            default will be used.</param>
            <returns>An object allowing configuration to continue.</returns>
        </member>
        <member name="M:Serilog.ConfigurationLoggerConfigurationExtensions.Configuration(Serilog.Configuration.LoggerSettingsConfiguration,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.DependencyModel.DependencyContext)">
            <summary>
            Reads logger settings from the provided configuration object using the default section name. Generally this
            is preferable over the other method that takes a configuration section. Only this version will populate
            IConfiguration parameters on target methods.
            </summary>
            <param name="settingConfiguration">Logger setting configuration.</param>
            <param name="configuration">A configuration object which contains a Serilog section.</param>
            <param name="dependencyContext">The dependency context from which sink/enricher packages can be located. If not supplied, the platform
            default will be used.</param>
            <returns>An object allowing configuration to continue.</returns>
        </member>
        <member name="M:Serilog.ConfigurationLoggerConfigurationExtensions.ConfigurationSection(Serilog.Configuration.LoggerSettingsConfiguration,Microsoft.Extensions.Configuration.IConfigurationSection,Microsoft.Extensions.DependencyModel.DependencyContext)">
            <summary>
            Reads logger settings from the provided configuration section. Generally it is preferable to use the other
            extension method that takes the full configuration object.
            </summary>
            <param name="settingConfiguration">Logger setting configuration.</param>
            <param name="configSection">The Serilog configuration section</param>
            <param name="dependencyContext">The dependency context from which sink/enricher packages can be located. If not supplied, the platform
            default will be used.</param>
            <returns>An object allowing configuration to continue.</returns>
        </member>
        <member name="M:Serilog.ConfigurationLoggerConfigurationExtensions.Configuration(Serilog.Configuration.LoggerSettingsConfiguration,Microsoft.Extensions.Configuration.IConfiguration,System.String,Serilog.Settings.Configuration.ConfigurationAssemblySource)">
            <summary>
            Reads logger settings from the provided configuration object using the provided section name. Generally this
            is preferable over the other method that takes a configuration section. Only this version will populate
            IConfiguration parameters on target methods.
            </summary>
            <param name="settingConfiguration">Logger setting configuration.</param>
            <param name="configuration">A configuration object which contains a Serilog section.</param>
            <param name="sectionName">A section name for section which contains a Serilog section.</param>
            <param name="configurationAssemblySource">Defines how the package identifies assemblies to scan for sinks and other Types.</param>
            <returns>An object allowing configuration to continue.</returns>
        </member>
        <member name="M:Serilog.ConfigurationLoggerConfigurationExtensions.Configuration(Serilog.Configuration.LoggerSettingsConfiguration,Microsoft.Extensions.Configuration.IConfiguration,Serilog.Settings.Configuration.ConfigurationAssemblySource)">
            <summary>
            Reads logger settings from the provided configuration object using the default section name. Generally this
            is preferable over the other method that takes a configuration section. Only this version will populate
            IConfiguration parameters on target methods.
            </summary>
            <param name="settingConfiguration">Logger setting configuration.</param>
            <param name="configuration">A configuration object which contains a Serilog section.</param>
            <param name="configurationAssemblySource">Defines how the package identifies assemblies to scan for sinks and other Types.</param>
            <returns>An object allowing configuration to continue.</returns>
        </member>
        <member name="M:Serilog.ConfigurationLoggerConfigurationExtensions.ConfigurationSection(Serilog.Configuration.LoggerSettingsConfiguration,Microsoft.Extensions.Configuration.IConfigurationSection,Serilog.Settings.Configuration.ConfigurationAssemblySource)">
            <summary>
            Reads logger settings from the provided configuration section. Generally it is preferable to use the other
            extension method that takes the full configuration object.
            </summary>
            <param name="settingConfiguration">Logger setting configuration.</param>
            <param name="configSection">The Serilog configuration section</param>
            <param name="configurationAssemblySource">Defines how the package identifies assemblies to scan for sinks and other Types.</param>
            <returns>An object allowing configuration to continue.</returns>
        </member>
        <member name="T:Serilog.Settings.Configuration.ConfigurationAssemblySource">
            <summary>
            Defines how the package will identify the assemblies which are scanned for sinks and other Type information.
            </summary>
        </member>
        <member name="F:Serilog.Settings.Configuration.ConfigurationAssemblySource.UseLoadedAssemblies">
            <summary>
            Try to scan the assemblies already in memory. This is the default. If GetEntryAssembly is null, fallback to DLL scanning.
            </summary>
        </member>
        <member name="F:Serilog.Settings.Configuration.ConfigurationAssemblySource.AlwaysScanDllFiles">
            <summary>
            Scan for assemblies in DLLs from the working directory. This is the fallback when GetEntryAssembly is null.
            </summary>
        </member>
        <member name="T:Serilog.Settings.Configuration.ResolutionContext">
            <summary>
            Keeps track of available elements that are useful when resolving values in the settings system.
            </summary>
        </member>
        <member name="M:Serilog.Settings.Configuration.ResolutionContext.LookUpSwitchByName(System.String)">
            <summary>
            Looks up a switch in the declared LoggingLevelSwitches
            </summary>
            <param name="switchName">the name of a switch to look up</param>
            <returns>the LoggingLevelSwitch registered with the name</returns>
            <exception cref="T:System.InvalidOperationException">if no switch has been registered with <paramref name="switchName"/></exception>
        </member>
        <member name="T:Serilog.Settings.Configuration.SurrogateConfigurationMethods">
             <summary>
             Contains "fake extension" methods for the Serilog configuration API.
             By default the settings know how to find extension methods, but some configuration
             are actually "regular" method calls and would not be found otherwise.
            
             This static class contains internal methods that can be used instead.
            
             </summary>
        </member>
    </members>
</doc>
