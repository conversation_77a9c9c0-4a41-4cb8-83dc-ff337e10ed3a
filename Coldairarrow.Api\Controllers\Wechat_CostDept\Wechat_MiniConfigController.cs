﻿using Coldairarrow.Business.Wechat_CostDept;
using Coldairarrow.Entity.Wechat_CostDept;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Wechat_CostDept
{
    [Route("/Wechat_CostDept/[controller]/[action]")]
    public class Wechat_MiniConfigController : BaseApiController
    {
        #region DI

        public Wechat_MiniConfigController(IWechat_MiniConfigBusiness wechat_MiniConfigBus)
        {
            _wechat_MiniConfigBus = wechat_MiniConfigBus;
        }

        IWechat_MiniConfigBusiness _wechat_MiniConfigBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Wechat_MiniConfig>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _wechat_MiniConfigBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Wechat_MiniConfig> GetTheData(IdInputDTO input)
        {
            return await _wechat_MiniConfigBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Wechat_MiniConfig data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _wechat_MiniConfigBus.AddDataAsync(data);
            }
            else
            {
                await _wechat_MiniConfigBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _wechat_MiniConfigBus.DeleteDataAsync(ids);
        }



        /// <summary>
        /// 获取设置值
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetData()
        {
            try
            {
                string id = HttpContext.Request.Form["id"].ToString();
                var data = _wechat_MiniConfigBus.GetTheDataAsync(id).Result;
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        #endregion
    }
}