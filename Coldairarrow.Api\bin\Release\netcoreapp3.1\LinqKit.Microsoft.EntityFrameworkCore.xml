<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LinqKit.Microsoft.EntityFrameworkCore</name>
    </assembly>
    <members>
        <member name="T:System.Linq.AggregateExtensions">
            <summary> Extension methods for expression tree balancing. </summary>
        </member>
        <member name="M:System.Linq.AggregateExtensions.AggregateBalanced``1(``0[],System.Func{System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,``0})">
            <summary>
            Generates balanced binary trees for list of conditions.
            E.g.: AndAlso or OrElse
            The reason is avoid StackOverFlowExceptions:
            var result = lambdas.Aggregate(AndAlso); // StackOverflow when lambdas.Length is 20 000
            var result = lambdas.AggregateBalanced(AndAlso); // Ok still when lambdas.Length is 1 000 000
            </summary>
        </member>
        <member name="M:System.Linq.AggregateExtensions.AggregateBalanced``1(System.Linq.Expressions.Expression{``0}[],System.Func{System.Linq.Expressions.Expression{``0},System.Linq.Expressions.Expression{``0},System.Linq.Expressions.Expression{``0}})">
            <summary>
            Generates balanced binary trees for list of conditions. Generic version.
            E.g.: AndAlso or OrElse
            The reason is avoid StackOverFlowExceptions:
            var result = lambdas.Aggregate(AndAlso); // StackOverflow when lambdas.Length is 20 000
            var result = lambdas.AggregateBalanced(AndAlso); // Ok still when lambdas.Length is 1 000 000
            </summary>
        </member>
        <member name="M:System.Linq.AggregateExtensions.AggregateBalancedAsync``1(``0[],System.Func{System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,``0})">
            <summary>
            Generates balanced binary trees for list of conditions.
            E.g.: AndAlso or OrElse
            The reason is avoid StackOverFlowExceptions:
            var result = lambdas.Aggregate(AndAlso); // StackOverflow when lambdas.Length is 20 000
            var result = lambdas.AggregateBalanced(AndAlso); // Ok still when lambdas.Length is 1 000 000
            </summary>
        </member>
        <member name="M:System.Linq.AggregateExtensions.AggregateBalancedAsync``1(System.Linq.Expressions.Expression{``0}[],System.Func{System.Linq.Expressions.Expression{``0},System.Linq.Expressions.Expression{``0},System.Linq.Expressions.Expression{``0}})">
            <summary>
            Generates balanced binary trees for list of conditions. Generic version.
            E.g.: AndAlso or OrElse
            The reason is avoid StackOverFlowExceptions:
            var result = lambdas.Aggregate(AndAlso); // StackOverflow when lambdas.Length is 20 000
            var result = lambdas.AggregateBalanced(AndAlso); // Ok still when lambdas.Length is 1 000 000
            </summary>
        </member>
        <member name="T:LinqKit.ExpandableDbAsyncEnumerator`1">
            <summary>Class for async-await style list enumeration support (e.g. .ToListAsync())</summary>
        </member>
        <member name="M:LinqKit.ExpandableDbAsyncEnumerator`1.#ctor(System.Collections.Generic.IEnumerator{`0})">
            <summary>Class for async-await style list enumeration support (e.g. .ToListAsync())</summary>
        </member>
        <member name="M:LinqKit.ExpandableDbAsyncEnumerator`1.Dispose">
            <summary>Dispose, .NET using-pattern</summary>
        </member>
        <member name="M:LinqKit.ExpandableDbAsyncEnumerator`1.MoveNextAsync(System.Threading.CancellationToken)">
            <summary>Enumerator-pattern: MoveNextAsync</summary>
        </member>
        <member name="M:LinqKit.ExpandableDbAsyncEnumerator`1.MoveNextAsync">
            <summary>Enumerator-pattern: MoveNextAsync</summary>
        </member>
        <member name="M:LinqKit.ExpandableDbAsyncEnumerator`1.DisposeAsync">
            <summary>DisposeAsync pattern</summary>
        </member>
        <member name="M:LinqKit.ExpandableDbAsyncEnumerator`1.MoveNext(System.Threading.CancellationToken)">
            <summary>Enumerator-pattern: MoveNext</summary>
        </member>
        <member name="P:LinqKit.ExpandableDbAsyncEnumerator`1.Current">
            <summary>Enumerator-pattern: Current item</summary>
        </member>
        <member name="T:LinqKit.ExpandableQuery`1">
            <summary>
            An IQueryable wrapper that allows us to visit the query's expression tree just before LINQ to SQL gets to it.
            This is based on the excellent work of Tomas Petricek: http://tomasp.net/blog/linq-expand.aspx
            </summary>
        </member>
        <member name="M:LinqKit.ExpandableQuery`1.GetEnumerator">
            <summary> IQueryable enumeration </summary>
        </member>
        <member name="M:LinqKit.ExpandableQuery`1.ToString">
            <summary>
            IQueryable string presentation.
            </summary>
        </member>
        <member name="T:LinqKit.ExpressionExpander">
            <summary>
            Custom expresssion visitor for ExpandableQuery. This expands calls to Expression.Compile() and
            collapses captured lambda references in subqueries which LINQ to SQL can't otherwise handle.
            </summary>
        </member>
        <member name="M:LinqKit.ExpressionExpander.VisitInvocation(System.Linq.Expressions.InvocationExpression)">
            <summary>
            Flatten calls to Invoke so that Entity Framework can understand it. Calls to Invoke are generated
            by PredicateBuilder.
            </summary>
        </member>
        <member name="T:LinqKit.ExpressionStarter`1">
            <summary>
            ExpressionStarter{T} which eliminates the default 1=0 or 1=1 stub expressions
            </summary>
            <typeparam name="T">The type</typeparam>
        </member>
        <member name="P:LinqKit.ExpressionStarter`1.Predicate">
            <summary>The actual Predicate. It can only be set by calling Start.</summary>
        </member>
        <member name="P:LinqKit.ExpressionStarter`1.IsStarted">
            <summary>Determines if the predicate is started.</summary>
        </member>
        <member name="P:LinqKit.ExpressionStarter`1.UseDefaultExpression">
            <summary> A default expression to use only when the expression is null </summary>
        </member>
        <member name="P:LinqKit.ExpressionStarter`1.DefaultExpression">
            <summary>The default expression</summary>
        </member>
        <member name="M:LinqKit.ExpressionStarter`1.Start(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>Set the Expression predicate</summary>
            <param name="exp">The first expression</param>
        </member>
        <member name="M:LinqKit.ExpressionStarter`1.Or(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>Or</summary>
        </member>
        <member name="M:LinqKit.ExpressionStarter`1.And(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>And</summary>
        </member>
        <member name="M:LinqKit.ExpressionStarter`1.ToString">
            <summary> Show predicate string </summary>
        </member>
        <member name="M:LinqKit.ExpressionStarter`1.op_Implicit(LinqKit.ExpressionStarter{`0})~System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}}">
            <summary>
            Allows this object to be implicitely converted to an Expression{Func{T, bool}}.
            </summary>
            <param name="right"></param>
        </member>
        <member name="M:LinqKit.ExpressionStarter`1.op_Implicit(LinqKit.ExpressionStarter{`0})~System.Func{`0,System.Boolean}">
            <summary>
            Allows this object to be implicitely converted to an Expression{Func{T, bool}}.
            </summary>
            <param name="right"></param>
        </member>
        <member name="M:LinqKit.ExpressionStarter`1.op_Implicit(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})~LinqKit.ExpressionStarter{`0}">
            <summary>
            Allows this object to be implicitely converted to an Expression{Func{T, bool}}.
            </summary>
            <param name="right"></param>
        </member>
        <member name="M:LinqKit.ExpressionStarter`1.Compile">
            <summary></summary>
        </member>
        <member name="P:LinqKit.ExpressionStarter`1.Body">
            <summary></summary>
        </member>
        <member name="P:LinqKit.ExpressionStarter`1.NodeType">
            <summary></summary>
        </member>
        <member name="P:LinqKit.ExpressionStarter`1.Parameters">
            <summary></summary>
        </member>
        <member name="P:LinqKit.ExpressionStarter`1.Type">
            <summary></summary>
        </member>
        <member name="P:LinqKit.ExpressionStarter`1.Name">
            <summary></summary>
        </member>
        <member name="P:LinqKit.ExpressionStarter`1.ReturnType">
            <summary></summary>
        </member>
        <member name="P:LinqKit.ExpressionStarter`1.TailCall">
            <summary></summary>
        </member>
        <member name="P:LinqKit.ExpressionStarter`1.CanReduce">
            <summary></summary>
        </member>
        <member name="T:LinqKit.ExpressionVisitor">
            <summary>
            This comes from Matt Warren's sample:
            http://blogs.msdn.com/mattwar/archive/2007/07/31/linq-building-an-iqueryable-provider-part-ii.aspx
            </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.Visit(System.Linq.Expressions.Expression)">
            <summary> Visit expression tree </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitBinding(System.Linq.Expressions.MemberBinding)">
            <summary> Visit member binding </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitElementInitializer(System.Linq.Expressions.ElementInit)">
            <summary> Visit element initializer </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitUnary(System.Linq.Expressions.UnaryExpression)">
            <summary> Visit one-parameter expression </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitBinary(System.Linq.Expressions.BinaryExpression)">
            <summary> Visit two-parameter expression </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitTypeIs(System.Linq.Expressions.TypeBinaryExpression)">
            <summary> Visit type-is expression </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitConstant(System.Linq.Expressions.ConstantExpression)">
            <summary> Return constant expression </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitConditional(System.Linq.Expressions.ConditionalExpression)">
            <summary> Simplify conditional expression </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitParameter(System.Linq.Expressions.ParameterExpression)">
            <summary> Return parameter expression </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitMemberAccess(System.Linq.Expressions.MemberExpression)">
            <summary> Visit member access </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)">
            <summary> Visit method call </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitExpressionList(System.Collections.ObjectModel.ReadOnlyCollection{System.Linq.Expressions.Expression})">
            <summary> Visit list of expressions </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitMemberAssignment(System.Linq.Expressions.MemberAssignment)">
            <summary> Visit member assignment </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitMemberMemberBinding(System.Linq.Expressions.MemberMemberBinding)">
            <summary> Visit member binding </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitMemberListBinding(System.Linq.Expressions.MemberListBinding)">
            <summary> Visit member list binding </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitBindingList(System.Collections.ObjectModel.ReadOnlyCollection{System.Linq.Expressions.MemberBinding})">
            <summary> Visit list of bindings </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitElementInitializerList(System.Collections.ObjectModel.ReadOnlyCollection{System.Linq.Expressions.ElementInit})">
            <summary> Visit list of element-initializers </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitLambda(System.Linq.Expressions.LambdaExpression)">
            <summary> Visit lambda expression </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitNew(System.Linq.Expressions.NewExpression)">
            <summary> Visit new-expression </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitMemberInit(System.Linq.Expressions.MemberInitExpression)">
            <summary> Visit member init expression </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitListInit(System.Linq.Expressions.ListInitExpression)">
            <summary> Visit list init expression </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitNewArray(System.Linq.Expressions.NewArrayExpression)">
            <summary> Visit array expression </summary>
        </member>
        <member name="M:LinqKit.ExpressionVisitor.VisitInvocation(System.Linq.Expressions.InvocationExpression)">
            <summary> Visit invocation expression </summary>
        </member>
        <member name="T:LinqKit.Extensions">
            <summary>
            Refer to http://www.albahari.com/nutshell/linqkit.html and http://tomasp.net/blog/linq-expand.aspx for more information.
            </summary>
        </member>
        <member name="M:LinqKit.Extensions.AsExpandable``1(System.Linq.IQueryable{``0})">
            <summary>
            LinqKit: Returns wrapper that automatically expands expressions using a default QueryOptimizer.
            </summary>
        </member>
        <member name="M:LinqKit.Extensions.AsExpandable``1(System.Linq.IQueryable{``0},System.Func{System.Linq.Expressions.Expression,System.Linq.Expressions.Expression})">
            <summary>
            LinqKit: Returns wrapper that automatically expands expressions using a custom QueryOptimizer.
            </summary>
        </member>
        <member name="M:LinqKit.Extensions.Expand``1(System.Linq.Expressions.Expression{``0})">
            <summary> LinqKit: Expands expression </summary>
        </member>
        <member name="M:LinqKit.Extensions.Expand``1(LinqKit.ExpressionStarter{``0})">
            <summary> LinqKit: Expands expression </summary>
        </member>
        <member name="M:LinqKit.Extensions.Expand(System.Linq.Expressions.Expression)">
            <summary> LinqKit: Expands expression </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``2(System.Linq.Expressions.Expression{System.Func{``0,``1}},``0)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``3(System.Linq.Expressions.Expression{System.Func{``0,``1,``2}},``0,``1)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``4(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3}},``0,``1,``2)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``5(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4}},``0,``1,``2,``3)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``6(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5}},``0,``1,``2,``3,``4)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``7(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6}},``0,``1,``2,``3,``4,``5)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``8(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7}},``0,``1,``2,``3,``4,``5,``6)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``9(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8}},``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``10(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9}},``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``11(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10}},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``12(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11}},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``13(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12}},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``14(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13}},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``15(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14}},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``16(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15}},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="M:LinqKit.Extensions.Invoke``17(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15,``16}},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15)">
            <summary> LinqKit: Compile and invoke </summary>
        </member>
        <member name="T:LinqKit.Linq">
            <summary>
            Another good idea by Tomas Petricek.
            See http://tomasp.net/blog/dynamic-linq-queries.aspx for information on how it's used.
            </summary>
        </member>
        <member name="M:LinqKit.Linq.Expr``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Returns the given anonymous method as a lambda expression
            </summary>
        </member>
        <member name="M:LinqKit.Linq.Expr``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Returns the given anonymous method as a lambda expression
            </summary>
        </member>
        <member name="M:LinqKit.Linq.Expr``3(System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
            <summary>
            Returns the given anonymous method as a lambda expression
            </summary>
        </member>
        <member name="M:LinqKit.Linq.Func``1(System.Func{``0})">
            <summary>
            Returns the given anonymous function as a Func delegate
            </summary>
        </member>
        <member name="M:LinqKit.Linq.Func``2(System.Func{``0,``1})">
            <summary>
            Returns the given anonymous function as a Func delegate
            </summary>
        </member>
        <member name="M:LinqKit.Linq.Func``3(System.Func{``0,``1,``2})">
            <summary>
            Returns the given anonymous function as a Func delegate
            </summary>
        </member>
        <member name="T:LinqKit.LinqKitExtension">
            <summary>
            Extensibility point: If you want to modify expanded queries before executing them
            set your own functionality to override default QueryOptimizer.
            </summary>
        </member>
        <member name="F:LinqKit.LinqKitExtension.QueryOptimizer">
            <summary>
            Place to optimize your queries. Example: Add a reference to Nuget package Linq.Expression.Optimizer 
            and in your program initializers set LinqKitExtension.QueryOptimizer = ExpressionOptimizer.visit;
            </summary>
        </member>
        <member name="T:LinqKit.PredicateOperator">
            <summary> The Predicate Operator </summary>
        </member>
        <member name="F:LinqKit.PredicateOperator.Or">
            <summary> The "Or" </summary>
        </member>
        <member name="F:LinqKit.PredicateOperator.And">
            <summary> The "And" </summary>
        </member>
        <member name="T:LinqKit.PredicateBuilder">
            <summary>
            See http://www.albahari.com/expressions for information and examples.
            </summary>
        </member>
        <member name="M:LinqKit.PredicateBuilder.New``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary> Start an expression </summary>
        </member>
        <member name="M:LinqKit.PredicateBuilder.New``1(System.Boolean)">
            <summary> Create an expression with a stub expression true or false to use when the expression is not yet started. </summary>
        </member>
        <member name="M:LinqKit.PredicateBuilder.True``1">
            <summary> Always true </summary>
        </member>
        <member name="M:LinqKit.PredicateBuilder.False``1">
            <summary> Always false </summary>
        </member>
        <member name="M:LinqKit.PredicateBuilder.Or``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary> OR </summary>
        </member>
        <member name="M:LinqKit.PredicateBuilder.And``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary> AND </summary>
        </member>
        <member name="M:LinqKit.PredicateBuilder.Extend``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},LinqKit.PredicateOperator)">
            <summary>
            Extends the specified source Predicate with another Predicate and the specified PredicateOperator.
            </summary>
            <typeparam name="T">The type</typeparam>
            <param name="first">The source Predicate.</param>
            <param name="second">The second Predicate.</param>
            <param name="operator">The Operator (can be "And" or "Or").</param>
            <returns>Expression{Func{T, bool}}</returns>
        </member>
        <member name="M:LinqKit.PredicateBuilder.Extend``1(LinqKit.ExpressionStarter{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},LinqKit.PredicateOperator)">
            <summary>
            Extends the specified source Predicate with another Predicate and the specified PredicateOperator.
            </summary>
            <typeparam name="T">The type</typeparam>
            <param name="first">The source Predicate.</param>
            <param name="second">The second Predicate.</param>
            <param name="operator">The Operator (can be "And" or "Or").</param>
            <returns>Expression{Func{T, bool}}</returns>
        </member>
        <member name="T:LinqKit.ExtensionsEFCore">
            <summary>
            <seealso cref="T:LinqKit.Extensions"/>
            </summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.AsExpandableEFCore``1(System.Linq.IQueryable{``0})">
            <summary>
            LinqKit: Returns wrapper that automatically expands expressions using a default QueryOptimizer.
            </summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.AsExpandableEFCore``1(System.Linq.IQueryable{``0},System.Func{System.Linq.Expressions.Expression,System.Linq.Expressions.Expression})">
            <summary>
            LinqKit: Returns wrapper that automatically expands expressions using a custom QueryOptimizer.
            </summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``2(System.Linq.Expressions.Expression{System.Func{``0,``1}},``0)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``3(System.Linq.Expressions.Expression{System.Func{``0,``1,``2}},``0,``1)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``4(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3}},``0,``1,``2)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``5(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4}},``0,``1,``2,``3)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``6(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5}},``0,``1,``2,``3,``4)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``7(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6}},``0,``1,``2,``3,``4,``5)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``8(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7}},``0,``1,``2,``3,``4,``5,``6)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``9(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8}},``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``10(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9}},``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``11(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10}},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``12(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11}},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``13(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12}},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``14(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13}},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``15(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14}},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``16(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15}},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
        <member name="M:LinqKit.ExtensionsEFCore.InvokeEFCore``17(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15,``16}},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15)">
            <summary>LinqKit: Compile and invoke</summary>
        </member>
    </members>
</doc>
