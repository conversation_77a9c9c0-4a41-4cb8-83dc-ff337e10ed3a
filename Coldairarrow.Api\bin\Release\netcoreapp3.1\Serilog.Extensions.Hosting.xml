<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Serilog.Extensions.Hosting</name>
    </assembly>
    <members>
        <member name="T:Serilog.Extensions.Hosting.DiagnosticContext">
            <summary>
            Implements an ambient diagnostic context using <see cref="T:System.Threading.AsyncLocal`1"/>.
            </summary>
            <remarks>Consumers should use <see cref="T:Serilog.IDiagnosticContext"/> to set context properties.</remarks>
        </member>
        <member name="M:Serilog.Extensions.Hosting.DiagnosticContext.#ctor(Serilog.ILogger)">
            <summary>
            Construct a <see cref="T:Serilog.Extensions.Hosting.DiagnosticContext"/>.
            </summary>
            <param name="logger">A logger for binding properties in the context, or <c>null</c> to use <see cref="P:Serilog.Log.Logger"/>.</param>
        </member>
        <member name="M:Serilog.Extensions.Hosting.DiagnosticContext.BeginCollection">
            <summary>
            Start collecting properties to associate with the current diagnostic context. This will replace
            the active collector, if any.
            </summary>
            <returns>A collector that will receive properties added in the current diagnostic context.</returns>
        </member>
        <member name="M:Serilog.Extensions.Hosting.DiagnosticContext.Set(System.String,System.Object,System.Boolean)">
            <inheritdoc cref="M:Serilog.IDiagnosticContext.Set(System.String,System.Object,System.Boolean)"/>
        </member>
        <member name="T:Serilog.Extensions.Hosting.DiagnosticContextCollector">
            <summary>
            A container that receives properties added to a diagnostic context.
            </summary>
        </member>
        <member name="M:Serilog.Extensions.Hosting.DiagnosticContextCollector.#ctor(System.IDisposable)">
            <summary>
            Construct a <see cref="T:Serilog.Extensions.Hosting.DiagnosticContextCollector"/>.
            </summary>
            <param name="chainedDisposable">An object that will be disposed to signal completion/disposal of
            the collector.</param>
        </member>
        <member name="M:Serilog.Extensions.Hosting.DiagnosticContextCollector.AddOrUpdate(Serilog.Events.LogEventProperty)">
            <summary>
            Add the property to the context.
            </summary>
            <param name="property">The property to add.</param>
        </member>
        <member name="M:Serilog.Extensions.Hosting.DiagnosticContextCollector.TryComplete(System.Collections.Generic.IEnumerable{Serilog.Events.LogEventProperty}@)">
            <summary>
            Complete the context and retrieve the properties added to it, if any. This will
            stop collection and remove the collector from the original execution context and
            any of its children.
            </summary>
            <param name="properties">The collected properties, or null if no collection is active.</param>
            <returns>True if properties could be collected.</returns>
        </member>
        <member name="M:Serilog.Extensions.Hosting.DiagnosticContextCollector.Dispose">
            <inheritdoc/>
        </member>
        <member name="T:Serilog.Hosting.SerilogLoggerFactory">
            <summary>
            Implements <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> so that we can inject Serilog Logger.
            </summary>
        </member>
        <member name="M:Serilog.Hosting.SerilogLoggerFactory.#ctor(Serilog.ILogger,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Serilog.Hosting.SerilogLoggerFactory"/> class.
            </summary>
            <param name="logger">The Serilog logger; if not supplied, the static <see cref="T:Serilog.Log"/> will be used.</param>
            <param name="dispose">When <c>true</c>, dispose <paramref name="logger"/> when the framework disposes the provider. If the
            logger is not specified but <paramref name="dispose"/> is <c>true</c>, the <see cref="M:Serilog.Log.CloseAndFlush"/> method will be
            called on the static <see cref="T:Serilog.Log"/> class instead.</param>
        </member>
        <member name="M:Serilog.Hosting.SerilogLoggerFactory.Dispose">
            <summary>
            Disposes the provider.
            </summary>
        </member>
        <member name="M:Serilog.Hosting.SerilogLoggerFactory.CreateLogger(System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instance.
            </summary>
            <param name="categoryName">The category name for messages produced by the logger.</param>
            <returns>
            The <see cref="T:Microsoft.Extensions.Logging.ILogger" />.
            </returns>
        </member>
        <member name="M:Serilog.Hosting.SerilogLoggerFactory.AddProvider(Microsoft.Extensions.Logging.ILoggerProvider)">
            <summary>
            Adds an <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> to the logging system.
            </summary>
            <param name="provider">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" />.</param>
        </member>
        <member name="T:Serilog.IDiagnosticContext">
            <summary>
            Collects diagnostic information for packaging into wide events.
            </summary>
        </member>
        <member name="M:Serilog.IDiagnosticContext.Set(System.String,System.Object,System.Boolean)">
            <summary>
            Set the specified property on the current diagnostic context. The property will be collected
            and attached to the event emitted at the completion of the context.
            </summary>
            <param name="propertyName">The name of the property. Must be non-empty.</param>
            <param name="value">The property value.</param>
            <param name="destructureObjects">If true, the value will be serialized as structured
            data if possible; if false, the object will be recorded as a scalar or simple array.</param>
        </member>
        <member name="T:Serilog.SerilogHostBuilderExtensions">
            <summary>
            Extends <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> with Serilog configuration methods.
            </summary>
        </member>
        <member name="M:Serilog.SerilogHostBuilderExtensions.UseSerilog(Microsoft.Extensions.Hosting.IHostBuilder,Serilog.ILogger,System.Boolean,Serilog.Extensions.Logging.LoggerProviderCollection)">
            <summary>
            Sets Serilog as the logging provider.
            </summary>
            <param name="builder">The host builder to configure.</param>
            <param name="logger">The Serilog logger; if not supplied, the static <see cref="T:Serilog.Log"/> will be used.</param>
            <param name="dispose">When <c>true</c>, dispose <paramref name="logger"/> when the framework disposes the provider. If the
            logger is not specified but <paramref name="dispose"/> is <c>true</c>, the <see cref="M:Serilog.Log.CloseAndFlush"/> method will be
            called on the static <see cref="T:Serilog.Log"/> class instead.</param>
            <param name="providers">A <see cref="T:Serilog.Extensions.Logging.LoggerProviderCollection"/> registered in the Serilog pipeline using the
            <c>WriteTo.Providers()</c> configuration method, enabling other <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>s to receive events. By
            default, only Serilog sinks will receive events.</param>
            <returns>The host builder.</returns>
        </member>
        <member name="M:Serilog.SerilogHostBuilderExtensions.UseSerilog(Microsoft.Extensions.Hosting.IHostBuilder,System.Action{Microsoft.Extensions.Hosting.HostBuilderContext,Serilog.LoggerConfiguration},System.Boolean,System.Boolean)">
            <summary>Sets Serilog as the logging provider.</summary>
            <remarks>
            A <see cref="T:Microsoft.Extensions.Hosting.HostBuilderContext"/> is supplied so that configuration and hosting information can be used.
            The logger will be shut down when application services are disposed.
            </remarks>
            <param name="builder">The host builder to configure.</param>
            <param name="configureLogger">The delegate for configuring the <see cref="T:Serilog.LoggerConfiguration" /> that will be used to construct a <see cref="T:Serilog.Core.Logger" />.</param>
            <param name="preserveStaticLogger">Indicates whether to preserve the value of <see cref="P:Serilog.Log.Logger"/>.</param>
            <param name="writeToProviders">By default, Serilog does not write events to <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>s registered through
            the Microsoft.Extensions.Logging API. Normally, equivalent Serilog sinks are used in place of providers. Specify
            <c>true</c> to write events to all providers.</param>
            <returns>The host builder.</returns>
        </member>
        <member name="M:Serilog.SerilogHostBuilderExtensions.UseSerilog(Microsoft.Extensions.Hosting.IHostBuilder,System.Action{Microsoft.Extensions.Hosting.HostBuilderContext,System.IServiceProvider,Serilog.LoggerConfiguration},System.Boolean,System.Boolean)">
            <summary>Sets Serilog as the logging provider.</summary>
            <remarks>
            A <see cref="T:Microsoft.Extensions.Hosting.HostBuilderContext"/> is supplied so that configuration and hosting information can be used.
            The logger will be shut down when application services are disposed.
            </remarks>
            <param name="builder">The host builder to configure.</param>
            <param name="configureLogger">The delegate for configuring the <see cref="T:Serilog.LoggerConfiguration" /> that will be used to construct a <see cref="T:Serilog.Core.Logger" />.</param>
            <param name="preserveStaticLogger">Indicates whether to preserve the value of <see cref="P:Serilog.Log.Logger"/>.</param>
            <param name="writeToProviders">By default, Serilog does not write events to <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>s registered through
            the Microsoft.Extensions.Logging API. Normally, equivalent Serilog sinks are used in place of providers. Specify
            <c>true</c> to write events to all providers.</param>
            <returns>The host builder.</returns>
        </member>
    </members>
</doc>
