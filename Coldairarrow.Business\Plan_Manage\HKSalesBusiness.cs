﻿using Coldairarrow.Entity.Plan_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System;
using Coldairarrow.Util.DataAccess;

namespace Coldairarrow.Business.Plan_Manage
{
    public class HKSalesBusiness : BaseBusiness<HKSales>, IHKSalesBusiness, ITransientDependency
    {
        public HKSalesBusiness(IERPDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HKSales>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HKSales>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HKSales, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HKSales> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HKSales data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HKSales data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HKSales>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HKSales, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<A01_HKSales, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 运营驾驶舱
        public async Task<HKSalesDTO> GetHKSalesData(DateTime? date)
        {
            date = date ?? DateTime.Now;
            var dateStr = date.Value.ToString("yyyy-MM-dd");
            HKSalesDTO hKSalesDTO = new HKSalesDTO();
            hKSalesDTO.ytDatas = await this.Db.GetListBySqlAsync<HKSales>(@$"SELECT TOP (100) *
  FROM [Mysoft_ERP25].[dbo].A01_HKSales
  where createdate in (select max(createdate) from [Mysoft_ERP25].[dbo].[A01_HKSales] where 1=1  and CONVERT(varchar,[CreateDate],23)='{dateStr}') ");
            if (hKSalesDTO.ytDatas.Count > 0)
            {
                //获取各项目得数据
                hKSalesDTO.projectDatas = hKSalesDTO.ytDatas.GroupBy(x => x.projName).Select(item => new HKSales()
                {
                    projName = item.Key,
                    dayrgts = item.Sum(x => x.dayqyts),
                    dayrgmj = item.Sum(x => x.dayrgmj),
                    dayrgje = item.Sum(x => x.dayrgje),
                    dayqyts = item.Sum(x => x.dayqyts),
                    dayqymj = item.Sum(x => x.dayqymj),
                    dayqyje = item.Sum(x => x.dayqyje),
                    weekrgts = item.Sum(x => x.weekrgts),
                    weekrgmj = item.Sum(x => x.weekrgmj),
                    weekrgje = item.Sum(x => x.weekrgje),
                    weekqyts = item.Sum(x => x.weekqyts),
                    weekqymj = item.Sum(x => x.weekqymj),
                    weekqyje = item.Sum(x => x.weekqyje),
                    monthrgts = item.Sum(x => x.monthrgts),
                    monthrgmj = item.Sum(x => x.monthrgmj),
                    monthrgje = item.Sum(x => x.monthrgje),
                    monthqyts = item.Sum(x => x.monthqyts),
                    monthqymj = item.Sum(x => x.monthqymj),
                    monthqyje = item.Sum(x => x.monthqyje),
                    quartergts = item.Sum(x => x.quartergts),
                    quartergmj = item.Sum(x => x.quartergmj),
                    quartergje = item.Sum(x => x.quartergje),
                    quarteqyts = item.Sum(x => x.quarteqyts),
                    quarteqymj = item.Sum(x => x.quarteqymj),
                    quarteqyje = item.Sum(x => x.quarteqyje),
                    yearrgts = item.Sum(x => x.yearrgts),
                    yearrgmj = item.Sum(x => x.yearrgmj),
                    yearrgje = item.Sum(x => x.yearrgje),
                    yearqyts = item.Sum(x => x.yearqyts),
                    yearqymj = item.Sum(x => x.yearqymj),
                    yearqyje = item.Sum(x => x.yearqyje),
                }).ToList();
                //获取全项目得数据
                hKSalesDTO.allProjectData = new HKSales()
                {
                    dayrgts = hKSalesDTO.ytDatas.Sum(x => x.dayqyts),
                    dayrgmj = hKSalesDTO.ytDatas.Sum(x => x.dayrgmj),
                    dayrgje = hKSalesDTO.ytDatas.Sum(x => x.dayrgje),
                    dayqyts = hKSalesDTO.ytDatas.Sum(x => x.dayqyts),
                    dayqymj = hKSalesDTO.ytDatas.Sum(x => x.dayqymj),
                    dayqyje = hKSalesDTO.ytDatas.Sum(x => x.dayqyje),
                    weekrgts = hKSalesDTO.ytDatas.Sum(x => x.weekrgts),
                    weekrgmj = hKSalesDTO.ytDatas.Sum(x => x.weekrgmj),
                    weekrgje = hKSalesDTO.ytDatas.Sum(x => x.weekrgje),
                    weekqyts = hKSalesDTO.ytDatas.Sum(x => x.weekqyts),
                    weekqymj = hKSalesDTO.ytDatas.Sum(x => x.weekqymj),
                    weekqyje = hKSalesDTO.ytDatas.Sum(x => x.weekqyje),
                    monthrgts = hKSalesDTO.ytDatas.Sum(x => x.monthrgts),
                    monthrgmj = hKSalesDTO.ytDatas.Sum(x => x.monthrgmj),
                    monthrgje = hKSalesDTO.ytDatas.Sum(x => x.monthrgje),
                    monthqyts = hKSalesDTO.ytDatas.Sum(x => x.monthqyts),
                    monthqymj = hKSalesDTO.ytDatas.Sum(x => x.monthqymj),
                    monthqyje = hKSalesDTO.ytDatas.Sum(x => x.monthqyje),
                    quartergts = hKSalesDTO.ytDatas.Sum(x => x.quartergts),
                    quartergmj = hKSalesDTO.ytDatas.Sum(x => x.quartergmj),
                    quartergje = hKSalesDTO.ytDatas.Sum(x => x.quartergje),
                    quarteqyts = hKSalesDTO.ytDatas.Sum(x => x.quarteqyts),
                    quarteqymj = hKSalesDTO.ytDatas.Sum(x => x.quarteqymj),
                    quarteqyje = hKSalesDTO.ytDatas.Sum(x => x.quarteqyje),
                    yearrgts = hKSalesDTO.ytDatas.Sum(x => x.yearrgts),
                    yearrgmj = hKSalesDTO.ytDatas.Sum(x => x.yearrgmj),
                    yearrgje = hKSalesDTO.ytDatas.Sum(x => x.yearrgje),
                    yearqyts = hKSalesDTO.ytDatas.Sum(x => x.yearqyts),
                    yearqymj = hKSalesDTO.ytDatas.Sum(x => x.yearqymj),
                    yearqyje = hKSalesDTO.ytDatas.Sum(x => x.yearqyje),
                };
            }
            return hKSalesDTO;
        }
        #endregion


        #region 私有成员

        #endregion
    }
}