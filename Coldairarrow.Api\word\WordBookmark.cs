﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml;

namespace Coldairarrow.Api.Word
{
    public class WordBookmark
    {
        /// <summary> 获取文档(模板)书签</summary>
        /// <param name="doc"></param>
        public static void QueryDocMarkbook(WordprocessingDocument doc, Dictionary<string, string> labels)
        {
            try
            {
                //得到主文件的一部分。
                MainDocumentPart mainDocPart = doc.MainDocumentPart;

                IDictionary<String, BookmarkStart> bookmarkMap = new Dictionary<String, BookmarkStart>();

                foreach (BookmarkStart bookmarkStart in mainDocPart.RootElement.Descendants<BookmarkStart>())
                {
                    bookmarkMap[bookmarkStart.Name] = bookmarkStart;
                }

                foreach (BookmarkStart bookmarkStart in bookmarkMap.Values)
                {
                    var key = labels.Where(v => v.Key == bookmarkStart.Name).FirstOrDefault();
                    if (!string.IsNullOrEmpty(key.Key))
                    {
                        Run bookmarkText = bookmarkStart.NextSibling<Run>();
                        var textElement = new Text(key.Value);
                        var runElement = new Run(textElement);

                        bookmarkStart.InsertAfterSelf(runElement);
                        //if (bookmarkText != null)
                        //{
                        //    try
                        //    {
                        //        bookmarkText.GetFirstChild<Text>().Text = key.Value;
                        //    }
                        //    catch
                        //    {
                        //        break;
                        //    }
                        //}
                        //else
                        //{
                        //    var textElement = new Text(key.Value);
                        //    var runElement = new Run(textElement);

                        //    bookmarkStart.InsertAfterSelf(runElement);
                        //}
                    }

                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary> 获取文档(模板)书签</summary>
        /// <param name="doc"></param>
        public static void QueryDocMarkbookNew(WordprocessingDocument doc, Dictionary<string, string> labels)
        {
            try
            {
                MainDocumentPart mainDocPart = doc.MainDocumentPart;

                IDictionary<String, BookmarkStart> bookmarkMap = new Dictionary<String, BookmarkStart>();

                foreach (BookmarkStart bookmarkStart in mainDocPart.RootElement.Descendants<BookmarkStart>())
                {
                    bookmarkMap[bookmarkStart.Name] = bookmarkStart;
                }

                foreach (var key in labels)
                {
                    if (bookmarkMap.ContainsKey(key.Key) && !string.IsNullOrEmpty(key.Value))
                    {
                        BookmarkStart bookmarkStart = bookmarkMap[key.Key];

                        Run bookmarkText = bookmarkStart.NextSibling<Run>();
                        var textElements = ParseTextWithBreaks(key.Value);

                        foreach (var textElement in textElements)
                        {
                            bookmarkStart.InsertAfterSelf(textElement);
                        }

                        // 移除书签
                        bookmarkStart.Remove();
                        bookmarkStart.NextSibling<BookmarkEnd>()?.Remove();
                    }
                    else if (bookmarkMap.ContainsKey(key.Key) && string.IsNullOrEmpty(key.Value))
                    {
                        // 如果值为空，则移除书签
                        BookmarkStart bookmarkStart = bookmarkMap[key.Key];
                        bookmarkStart.Remove();
                        bookmarkStart.NextSibling<BookmarkEnd>()?.Remove();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        private static List<OpenXmlElement> ParseTextWithBreaks(string text)
        {
            var elements = new List<OpenXmlElement>();
            var parts = text.Split(new string[] { "\r\n" }, StringSplitOptions.None);

            foreach (var part in parts)
            {
                var textElement = new Text(part);
                var runElement = new Run(textElement);

                elements.Add(runElement);

                // 添加换行符
                elements.Add(new Break());
            }

            // 移除最后一个换行符
            if (elements.Count > 0)
            {
                elements.RemoveAt(elements.Count - 1);
            }

            return elements;
        }
        /// <summary> 获取文档(模板)书签</summary>
        private static Dictionary<string, BookmarkEnd> FindBookmarks(OpenXmlElement documentPart, Dictionary<string, BookmarkEnd> results = null, Dictionary<string, string> unmatched = null)
        {
            results = results ?? new Dictionary<string, BookmarkEnd>();
            unmatched = unmatched ?? new Dictionary<string, string>();

            foreach (var child in documentPart.Elements())
            {
                if (child is BookmarkStart)
                {
                    var bStart = child as BookmarkStart;
                    unmatched.Add(bStart.Id, bStart.Name);
                }

                if (child is BookmarkEnd)
                {
                    var bEnd = child as BookmarkEnd;
                    foreach (var orphanName in unmatched)
                    {
                        if (bEnd.Id == orphanName.Key)
                        {
                            results.Add(orphanName.Value, bEnd);
                        }
                    }
                }

                FindBookmarks(child, results, unmatched);
            }

            return results;
        }



    }
}
