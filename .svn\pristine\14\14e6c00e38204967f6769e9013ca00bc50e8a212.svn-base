﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Coldairarrow.Entity.Wechat_Shekou
{
    /// <summary>
    /// Caiwu_Recovery
    /// </summary>
    [Table("Caiwu_Recovery")]
    public class Caiwu_Recovery
    {

        /// <summary>
        /// 主键
        /// </summary>
        [Key, Column(Order = 1)]
        public Int32 F_Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime F_CreateDate { get; set; }

        /// <summary>
        /// F_ProjectName
        /// </summary>
        public String F_ProjectName { get; set; }

        /// <summary>
        /// F_StartMoney
        /// </summary>
        public Int32? F_StartMoney { get; set; }

        /// <summary>
        /// F_MidMoney
        /// </summary>
        public Int32? F_MidMoney { get; set; }

        /// <summary>
        /// F_EndMoney
        /// </summary>
        public Int32? F_EndMoney { get; set; }

        /// <summary>
        /// F_RemainMoney
        /// </summary>
        public Int32? F_RemainMoney { get; set; }

        /// <summary>
        /// F_RecoveryRate
        /// </summary>
        public Double? F_RecoveryRate { get; set; }

        /// <summary>
        /// F_RecoveryValue
        /// </summary>
        public Int32? F_RecoveryValue { get; set; }

        /// <summary>
        /// Year
        /// </summary>
        public Int32? Year { get; set; }

        /// <summary>
        /// Month
        /// </summary>
        public Int32? Month { get; set; }

    }
}