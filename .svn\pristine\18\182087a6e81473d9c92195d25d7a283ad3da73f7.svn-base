﻿<template>
  <a-modal
    :title="title"
    width="40%"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="()=>{this.visible=false}"
  >
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="entity" :rules="rules" v-bind="layout">
        <a-form-model-item label="OpenId" prop="F_Id">
          <a-input v-model="entity.F_Id" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_CreateTime" prop="F_CreateTime">
          <a-input v-model="entity.F_CreateTime" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="创建人Id" prop="F_CreatorId">
          <a-input v-model="entity.F_CreatorId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="参加人" prop="F_OpenId">
          <a-input v-model="entity.F_OpenId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_TeamId" prop="F_TeamId">
          <a-input v-model="entity.F_TeamId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_Money" prop="F_Money">
          <a-input v-model="entity.F_Money" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_Descri" prop="F_Descri">
          <a-input v-model="entity.F_Descri" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_Reason" prop="F_Reason">
          <a-input v-model="entity.F_Reason" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_Name" prop="F_Name">
          <a-input v-model="entity.F_Name" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_UserName" prop="F_UserName">
          <a-input v-model="entity.F_UserName" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_StartDate" prop="F_StartDate">
          <a-input v-model="entity.F_StartDate" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_EndDate" prop="F_EndDate">
          <a-input v-model="entity.F_EndDate" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_Date" prop="F_Date">
          <a-input v-model="entity.F_Date" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_Isable" prop="F_Isable">
          <a-input v-model="entity.F_Isable" autocomplete="off" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      layout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 18 }
      },
      visible: false,
      loading: false,
      entity: {},
      rules: {},
      title: ''
    }
  },
  methods: {
    init() {
      this.visible = true
      this.entity = {}
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    openForm(id, title) {
      this.init()

      if (id) {
        this.loading = true
        this.$http.post('/Wechat_Go/Go_Money/GetTheData', { id: id }).then(resJson => {
          this.loading = false

          this.entity = resJson.Data
        })
      }
    },
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.loading = true
        this.$http.post('/Wechat_Go/Go_Money/SaveData', this.entity).then(resJson => {
          this.loading = false

          if (resJson.Success) {
            this.$message.success('操作成功!')
            this.visible = false

            this.parentObj.getDataList()
          } else {
            this.$message.error(resJson.Msg)
          }
        })
      })
    }
  }
}
</script>
