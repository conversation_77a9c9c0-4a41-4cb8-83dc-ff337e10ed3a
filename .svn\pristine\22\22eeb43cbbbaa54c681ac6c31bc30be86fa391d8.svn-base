<template>
  <a-modal :title="title" width="60%" :visible="visible" :confirmLoading="loading" :maskClosable="false"
    @ok="handleSubmit" @cancel="
      () => {
        this.visible = false
      }
    ">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="12">
          <a-col :md="6" :sm="12">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="Condition">
                <a-select-option key="NameUser">姓名</a-select-option>
                <a-select-option key="EmployeeCode">员工编码</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="12">
            <a-form-item>
              <a-input v-model="Keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="12">
            <a-button type="primary" icon="search" @click="
                () => {
                  this.pagination.current = 1
                  this.getEmpList()
                }
              ">查询</a-button>
            <a-button style="margin-left: 8px" icon="sync" @click="reset">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <a-spin :spinning="loading">
      <a-table ref="table" :columns="columns" :rowKey="row => row.F_Id" :dataSource="data" :pagination="pagination"
        :loading="loading" @change="handleTableChange"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true">
        <span slot="Sex" slot-scope="text">
          <template>
            <span>{{ text | getSex }}</span>
          </template>
        </span>
        <span slot="DirthDate" slot-scope="text">
          <template>
            <span>{{ text | dayjs('YYYY-MM-DD') }}</span>
          </template>
        </span>
      </a-table>
    </a-spin>
  </a-modal>
</template>

<script>
const columns = [
  { title: '员工编码', dataIndex: 'EmployeesCode', width: 40 },
  { title: '姓名', dataIndex: 'NameUser', width: 60 },
  { title: '性别', dataIndex: 'Sex', width: 40, scopedSlots: { customRender: 'Sex' } },
  { title: '出生日期', dataIndex: 'DirthDate', width: 100, scopedSlots: { customRender: 'DirthDate' } },
  { title: '用工关系状态', dataIndex: 'EmployRelStatus', width: 100 }
]
export default {
  props: {
    //回调方法，返回选中的员工
    callBack: Function,
    //是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    queryParam: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 10,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      sorter: { field: 'F_CreateDate', order: 'desc' },
      queryParam: { condition: "NameUser" },
      columns,
      Param: {},
      visible: false,
      loading: false,
      selectedRowKeys: [],
      selectedUsers: [], //选择用户
      Condition: '',
      Keyword: '',
      entity: {},
      rules: {},
      title: ''
    }
  },
  created () {
    this.Param = this.queryParam
  },
  methods: {
    reset () {
      this.Condition = ''
      this.Keyword = ''
    },
    init () {
      this.visible = true
      this.selectUser = []
      this.data = []
      this.Condition = ''
      this.Keyword = ''
    },
    openForm (title) {
      this.title = title
      this.init()
      //查询员工信息
      this.getEmpList()
    },
    handleTableChange (pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.sorter = { ...sorter }
      this.getEmpList()
    },
    //查询员工信息
    getEmpList () {
      this.selectedRowKeys = []
      this.Param.Condition = this.Condition
      this.Param.Keyword = this.Keyword

      this.loading = true
      this.$http
        .post('/HR_EmployeeInfoManage/HR_FormalEmployees/GetDetailsTheData', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'F_CreateDate',
          SortType: this.sorter.order,
          Search: this.Param
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      console.log(selectedRows)
      this.selectedRowKeys = selectedRowKeys
      this.selectedUsers = selectedRows
    },
    handleSubmit () {
      if (this.selectedUsers.length == 0) {
        this.$message.warning('必须选中一行')
        return
      }
      if (!this.multiple && this.selectedUsers.length > 1) {
        this.$message.warning('只能选择一条数据')
        return
      }
      if (this.callBack) {
        this.visible = false
        this.callBack(this.selectedUsers)
      }
    }
  }
}
</script>
