﻿using Coldairarrow.Business.Base_Business;
using Coldairarrow.Entity.Base_Business;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static Coldairarrow.Util.Helper.TencentJsonHelper;

namespace Coldairarrow.Api.Controllers.Base_Business
{
    [Route("/Base_Business/[controller]/[action]")]
    public class Tencent_DailyRecordController : BaseApiController
    {
        #region DI

        public Tencent_DailyRecordController(ITencent_DailyRecordBusiness tencent_DailyRecordBus, ITencent_WeekPortrayBusiness tencent_WeekPortrayBus,
            ITencent_WeekMarketBusiness tencent_WeekMarketBus, ITencent_WeekRankBusiness tencent_WeekRankBus)
        {
            _tencent_DailyRecordBus = tencent_DailyRecordBus;
            _tencent_WeekPortrayBus = tencent_WeekPortrayBus;
            _tencent_WeekMarketBus = tencent_WeekMarketBus;
            _tencent_WeekRankBus = tencent_WeekRankBus;
        }

        ITencent_DailyRecordBusiness _tencent_DailyRecordBus { get; }
        ITencent_WeekPortrayBusiness _tencent_WeekPortrayBus { get; }
        ITencent_WeekMarketBusiness _tencent_WeekMarketBus { get; }
        ITencent_WeekRankBusiness _tencent_WeekRankBus { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Tencent_DailyRecord>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _tencent_DailyRecordBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Tencent_DailyRecord> GetTheData(IdInputDTO input)
        {
            return await _tencent_DailyRecordBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Tencent_DailyRecord data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _tencent_DailyRecordBus.AddDataAsync(data);
            }
            else
            {
                await _tencent_DailyRecordBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _tencent_DailyRecordBus.DeleteDataAsync(ids);
        }

        #endregion

        #region 二次开发
        [NoCheckJWT]
        [HttpGet]
        public void testData()
        {
            DateTime DateNow = DateTime.Now.AddDays(-5);
            DateTime day = new DateTime(DateNow.Year, DateNow.Month, DateNow.Day);
            getmallflowandnumdatabymallId(day);
            getcityoutsidepassengerflowtops(day);
            getlengthsofstaydata(day);
            getlocaltiyandoutsidepassengerflows(day);
            getgridperageportraitbymallIdS30(day);
            getgridperageportraitbymallIdS7(day);
            getgridperageportraitS(day);
            getlengthsofstaydata(day);
        }
        [NoCheckJWT]
        [HttpGet]
        public void getDailyData()
        {
            DateTime DateNow = DateTime.Now.AddDays(-1);
            DateTime day = new DateTime(DateNow.Year, DateNow.Month, DateNow.Day);
            getmallflowandnumdatabymallId(day);
            getcityoutsidepassengerflowtops(day);
            getlengthsofstaydata(day);
            getlocaltiyandoutsidepassengerflows(day);
            getgridperageportraitbymallIdS30(day);
            getgridperageportraitbymallIdS7(day);
            getgridperageportraitS(day);
            getlengthsofstaydata(day);
        }
        /// <summary>
        /// 处理周报的内容
        /// </summary>
        [NoCheckJWT]
        [HttpGet]
        public void getWeeklyData(string index)
        {
            var index_ = @"F:\F-帆软数据平台\周报\" + index;
            //var index_ = @"F:\F-帆软数据平台\周报\数据报表-统计报告-周报2021-06-28数据导出.xlsx";
            //var dateString = "2021-07-01";
            var dateString = MidStrEx(index_).Replace("-","");
            GetWeekMarket(index_, dateString);
            GetWeekPortray(index_, dateString);
            GetWeekRank(index_, dateString);
        }
        public static string MidStrEx(string sourse)
        {
            string result = string.Empty;
            string startstr = "数据报表-统计报告-周报";
            string endstr = "数据导出";
            int startindex, endindex;
            try
            {
                startindex = sourse.IndexOf(startstr);
                if (startindex == -1)
                    return result;
                string tmpstr = sourse.Substring(startindex + startstr.Length);
                endindex = tmpstr.IndexOf(endstr);
                if (endindex == -1)
                    return result;
                result = tmpstr.Remove(endindex);
            }
            catch (Exception ex)
            {
               
            }
            return result;
        }

        /// <summary>
        /// 获取客流统计
        /// </summary>
        /// <param name="day"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult getmallflowandnumdatabymallId(DateTime day)
        {

            try
            {
                var result = TencentHelper.getmallflowandnumdatabymallId(day);
                if (!result.IsNullOrEmpty())
                {
                    var jobj = JsonConvert.DeserializeObject(result) as JObject;
                    var flowandnum = new Flowandnum();
                    foreach (var item in jobj)
                    {

                        var date_ = item.Key.ToDateTime();
                        flowandnum = JsonConvert.DeserializeObject<Flowandnum>(item.Value.ToString());
                        var data = _tencent_DailyRecordBus.GetDataByDate(date_);
                        if (data.IsNullOrEmpty())
                        {
                            data = new Tencent_DailyRecord();
                            data.F_NewNum = flowandnum.new_num;
                            data.F_Num = flowandnum.num;
                            data.F_NewPercent = flowandnum.new_percent;
                            data.F_Flow = flowandnum.flow;
                            data.F_Percent = flowandnum.percent;
                            data.Id = IdHelper.GetId();
                            data.Date = date_;
                            _tencent_DailyRecordBus.AddDataAsync(data).Wait();
                        }
                        else
                        {
                            data.F_NewNum = flowandnum.new_num;
                            data.F_Num = flowandnum.num;
                            data.F_NewPercent = flowandnum.new_percent;
                            data.F_Flow = flowandnum.flow;
                            data.F_Percent = flowandnum.percent;
                            _tencent_DailyRecordBus.UpdateDataAsync(data).Wait();
                        }
                    }
                   
                }
                return Success(1);
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
        }
        /// <summary>
        /// 获取本外地客群
        /// </summary>
        /// <param name="day"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult getlocaltiyandoutsidepassengerflows(DateTime day)
        {

            try
            {
                var result = TencentHelper.getlocaltiyandoutsidepassengerflows(day);
                if (!result.IsNullOrEmpty())
                {
                    var jobj = JsonConvert.DeserializeObject(result) as JObject;
                    var passengernum = new Passengernum();
                    foreach (var item in jobj)
                    {

                        var date_ = item.Key.ToDateTime();
                        passengernum = JsonConvert.DeserializeObject<Passengernum>(item.Value.ToString());
                        var data = _tencent_DailyRecordBus.GetDataByDate(date_);
                        if (data.IsNullOrEmpty())
                        {
                            data = new Tencent_DailyRecord();
                            data.F_LocalNum = passengernum.localtiy_num;
                            data.F_OutsideNum = passengernum.outside_num;
                            data.F_OutsideRatio = passengernum.outside_ratio;
                            data.Id = IdHelper.GetId();
                            data.Date = date_;
                            _tencent_DailyRecordBus.AddDataAsync(data).Wait();
                        }
                        else
                        {
                            data.F_LocalNum = passengernum.localtiy_num;
                            data.F_OutsideNum = passengernum.outside_num;
                            data.F_OutsideRatio = passengernum.outside_ratio;
                            _tencent_DailyRecordBus.UpdateDataAsync(data).Wait();
                        }
                    }

                }
                return Success(1);
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
        }
        /// <summary>
        /// 获取外地客群城市到访的 TOP10
        /// </summary>
        /// <param name="day"></param>
        /// <returns></returns>
        public AjaxResult getcityoutsidepassengerflowtops(DateTime day)
        {

            try
            {
                var result = TencentHelper.getcityoutsidepassengerflowtops(day);
                if (!result.IsNullOrEmpty())
                {
                 var data = _tencent_DailyRecordBus.GetDataByDate(day);
                        if (data.IsNullOrEmpty())
                        {
                            data = new Tencent_DailyRecord();
                            data.F_PassengerTop = result;
                            data.Id = IdHelper.GetId();
                            data.Date = day;
                            _tencent_DailyRecordBus.AddDataAsync(data).Wait();
                        }
                        else
                        {
                              data.F_PassengerTop = result;
                             _tencent_DailyRecordBus.UpdateDataAsync(data).Wait();
                        }
                }
                return Success(1);
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
        }
        /// <summary>
        /// 获取30日频次
        /// </summary>
        /// <param name="day"></param>
        /// <returns></returns>
        public AjaxResult getgridperageportraitbymallIdS30(DateTime day)
        {

            try
            {
                var result = TencentHelper.getgridperageportraitbymallIdS30(day);
                if (!result.IsNullOrEmpty())
                {
                    var jobj = JsonConvert.DeserializeObject(result) as JObject;
                    var list = new List<PercentItem>();
                    foreach (var item in jobj)
                    {
                        var item_ = JsonConvert.DeserializeObject<PercentItem>(item.Value.ToString());
                        list.Add(item_);
                    }
                    var data = _tencent_DailyRecordBus.GetDataByDate(day);
                    if (data.IsNullOrEmpty())
                    {
                        data = new Tencent_DailyRecord();
                        data.F_PropertyThirty = list.ToJson();
                        data.Id = IdHelper.GetId();
                        data.Date = day;
                        _tencent_DailyRecordBus.AddDataAsync(data).Wait();
                    }
                    else
                    {
                        data.F_PropertyThirty = list.ToJson();
                        _tencent_DailyRecordBus.UpdateDataAsync(data).Wait();
                    }
                }
                return Success(1);
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
        }
        /// <summary>
        /// 获取7日频次
        /// </summary>
        /// <param name="day"></param>
        /// <returns></returns>
        public AjaxResult getgridperageportraitbymallIdS7(DateTime day)
        {

            try
            {
                var result = TencentHelper.getgridperageportraitbymallIdS7(day);
                if (!result.IsNullOrEmpty())
                {
                    var jobj = JsonConvert.DeserializeObject(result) as JObject;
                    var list = new List<PercentItem>();
                    foreach (var item in jobj)
                    {
                        var item_ = JsonConvert.DeserializeObject<PercentItem>(item.Value.ToString());
                        list.Add(item_);
                    }
                    var data = _tencent_DailyRecordBus.GetDataByDate(day);
                    if (data.IsNullOrEmpty())
                    {
                        data = new Tencent_DailyRecord();
                        data.F_PropertySeven = list.ToJson();
                        data.Id = IdHelper.GetId();
                        data.Date = day;
                        _tencent_DailyRecordBus.AddDataAsync(data).Wait();
                    }
                    else
                    {
                        data.F_PropertySeven = list.ToJson();
                        _tencent_DailyRecordBus.UpdateDataAsync(data).Wait();
                    }
                }
                return Success(1);
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
        }
        /// <summary>
        /// 活跃时间段
        /// </summary>
        /// <param name="day"></param>
        /// <returns></returns>
        public AjaxResult getgridperageportraitS(DateTime day)
        {

            try
            {
                var result = TencentHelper.getgridperageportraitS(day);
                if (!result.IsNullOrEmpty())
                {
                    var jobj = JsonConvert.DeserializeObject(result) as JObject;
                    var list = new List<PercentItem>();
                    foreach (var item in jobj)
                    {
                        var item_ = JsonConvert.DeserializeObject<PercentItem>(item.Value.ToString());
                        list.Add(item_);
                    }
                    var data = _tencent_DailyRecordBus.GetDataByDate(day);
                    if (data.IsNullOrEmpty())
                    {
                        data = new Tencent_DailyRecord();
                        data.F_PropertyTime = list.ToJson();
                        data.Id = IdHelper.GetId();
                        data.Date = day;
                        _tencent_DailyRecordBus.AddDataAsync(data).Wait();
                    }
                    else
                    {
                        data.F_PropertyTime = list.ToJson(); 
                        _tencent_DailyRecordBus.UpdateDataAsync(data).Wait();
                    }
                }
                return Success(1);
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
        }
        /// <summary>
        /// 停留时长
        /// </summary>
        /// <param name="day"></param>
        /// <returns></returns>
        public AjaxResult getlengthsofstaydata(DateTime day)
        {

            try
            {
                var result = TencentHelper.getlengthsofstaydata(day);
                if (!result.IsNullOrEmpty())
                {
                    var data = _tencent_DailyRecordBus.GetDataByDate(day);
                    if (data.IsNullOrEmpty())
                    {
                        data = new Tencent_DailyRecord();
                        data.F_LengthStay = result;
                        data.Id = IdHelper.GetId();
                        data.Date = day;
                        _tencent_DailyRecordBus.AddDataAsync(data).Wait();
                    }
                    else
                    {
                        data.F_LengthStay = result;
                        _tencent_DailyRecordBus.UpdateDataAsync(data).Wait();
                    }
                }
                return Success(1);
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
        }
        #endregion


        #region 处理附件内容
        /// <summary>
        /// 处理周报用户画像
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetWeekPortray(string index,string dateString)
        {
            try
            {
                DateTime startday = DateTime.ParseExact(dateString, "yyyyMMdd", System.Globalization.CultureInfo.CurrentCulture);
                DateTime endday = startday.AddDays(6);
                using (FileStream filesrc = new FileStream(index, FileMode.Open, FileAccess.Read))
                {
                    ISheet sheet = null;
                    ////获得工作簿里面的工作表
                    XSSFWorkbook workbook = new XSSFWorkbook(filesrc);
                    sheet = workbook.GetSheetAt(2);
                    //处理第3页，用户画像
                    for (int r = 0; r <= sheet.LastRowNum; r++)
                    {
                      
                        IRow row = sheet.GetRow(r);
                        if (row != null)
                        {

                            var portrayStatus = row.GetCell(0).ToString();
                            if (portrayStatus.IsNullOrEmpty())
                            {
                                return Error("第" + (r + 1) + "行第1列为空，请检查");
                            }
                            var portrayType = row.GetCell(1).ToString();
                            if (portrayType.IsNullOrEmpty())
                            {
                                return Error("第" + (r + 1) + "行第2列为空，请检查");
                            }
                            var portrayName = row.GetCell(2).ToString();
                            if (portrayName.IsNullOrEmpty())
                            {
                                return Error("第" + (r + 1) + "行第3列为空，请检查");
                            }
                            //var portrayValue = row.GetCell(3).NumericCellValue;
                            //var portrayValue = DateTime.FromOADate(value).ToString("yyyy-MM-dd");
                            var portrayValue = row.GetCell(3).ToString();
                            if (portrayValue.IsNullOrEmpty())
                            {
                                return Error("第" + (r + 1) + "行第4列为空，请检查");
                            }
                            var newPortray = new Tencent_WeekPortray()
                            {
                                F_Name = portrayName,
                                F_Status = portrayStatus,
                                F_Type = portrayType,
                                F_Value = portrayValue,
                                F_StartDate = startday,
                                F_EndDate = endday,
                                F_NumValue = Convert.ToDouble(portrayValue.Split('%')[0]),
                                Id = IdHelper.GetId()
                             };
                            _tencent_WeekPortrayBus.AddDataAsync(newPortray).Wait();
                        }
                    }
                    return Success("");
                }
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
        }
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetWeekMarket(string index, string dateString)
        {
            try
            {
                DateTime startday = DateTime.ParseExact(dateString, "yyyyMMdd", System.Globalization.CultureInfo.CurrentCulture);
                DateTime endday = startday.AddDays(6);
                using (FileStream filesrc = new FileStream(index, FileMode.Open, FileAccess.Read))
                {
                    ISheet sheet = null;
                    ////获得工作簿里面的工作表
                    XSSFWorkbook workbook = new XSSFWorkbook(filesrc);
                    sheet = workbook.GetSheetAt(1);
                    //处理第2页，商圈分析
                    for (int r = 0; r <= sheet.LastRowNum; r++)
                    {

                        IRow row = sheet.GetRow(r);
                        if (row != null)
                        {

                            var marketName = row.GetCell(0).ToString();
                            if (marketName.IsNullOrEmpty())
                            {
                                return Error("第" + (r + 1) + "行第1列为空，请检查");
                            }
                            var marketValue = row.GetCell(1).ToString();
                            if (marketValue.IsNullOrEmpty())
                            {
                                return Error("第" + (r + 1) + "行第2列为空，请检查");
                            }
                            var marketRate = row.GetCell(3).ToString();
                            if (marketRate.IsNullOrEmpty())
                            {
                                return Error("第" + (r + 1) + "行第3列为空，请检查");
                            }
                            var newMarket = new Tencent_WeekMarket()
                            {
                                F_Name = marketName,
                                F_Rate = marketRate,
                                F_Value = marketValue,
                                F_StartDate = startday,
                                F_EndDate = endday,
                                Id = IdHelper.GetId()
                            };
                            _tencent_WeekMarketBus.AddDataAsync(newMarket).Wait();
                        }
                    }
                    return Success("");
                }
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
        }
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetWeekRank(string index,string dateString)
        {
            try
            {
                DateTime startday = DateTime.ParseExact(dateString, "yyyyMMdd", System.Globalization.CultureInfo.CurrentCulture);
                DateTime endday = startday.AddDays(6);
                using (FileStream filesrc = new FileStream(index, FileMode.Open, FileAccess.Read))
                {
                    ISheet sheet = null;
                    ////获得工作簿里面的工作表
                    XSSFWorkbook workbook = new XSSFWorkbook(filesrc);
                    sheet = workbook.GetSheetAt(3);
                    //处理第4页，竞争分析
                    for (int r = 1; r <= sheet.LastRowNum; r++)
                    {

                        IRow row = sheet.GetRow(r);
                        if (row != null)
                        {

                            var rankName = row.GetCell(0).ToString();
                            if (rankName.IsNullOrEmpty())
                            {
                                return Error("第" + (r + 1) + "行第1列为空，请检查");
                            }
                            var rankNum = row.GetCell(1).ToString();
                            if (rankNum.IsNullOrEmpty())
                            {
                                return Error("第" + (r + 1) + "行第2列为空，请检查");
                            }
                            var rankLastNum = row.GetCell(2).ToString();
                            if (rankLastNum.IsNullOrEmpty())
                            {
                                return Error("第" + (r + 1) + "行第3列为空，请检查");
                            }
                            var newRank = new Tencent_WeekRank()
                            {
                                F_Name = rankName,
                                F_Num = int.Parse(rankNum),
                                F_LastNum = int.Parse(rankLastNum),
                                F_StartDate = startday,
                                F_EndDate = endday,
                                Id = IdHelper.GetId()
                            };
                            _tencent_WeekRankBus.AddDataAsync(newRank).Wait();
                        }
                    }
                    return Success("");
                }
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
        }
        #endregion
    }
}