﻿using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Business.Base_Manage
{
    public interface IBase_DepartmentBusiness
    {
        /// <summary>
        /// 获取一级部门
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Base_Department GetRootDept(string id);
        /// <summary>
        /// 通过递归获取他的所有下级
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        List<string> GetDepList(string id);
        /// <summary>
        /// 获取这个子部门内下所有部门信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        List<string> GetDetListByNode(string id);
        Task<List<Base_DepartmentTreeDTO>> GetTreeDataListAsync(DepartmentsTreeInputDTO input);
        List<Base_DepartmentTreeDTO> GetTreeDataList(PageInput<ConditionDTO> input);
        Task<List<Base_Department>> GetDataListAsync();
        Task<Base_Department> GetTheDataAsync(string id);
        Task<List<string>> GetChildrenIdsAsync(string departmentId);
        Task AddDataAsync(Base_Department newData);
        Task UpdateDataAsync(Base_Department theData);
        Task DeleteDataAsync(List<string> ids);
    }

    public class DepartmentsTreeInputDTO
    {
        public string parentId { get; set; }
    }

    public class Base_DepartmentTreeDTO : TreeModel
    {
        public object children { get => Children; }
        public string title { get => Text; }
        public string value { get => Id; }
        public string key { get => Id; }
    }
}