﻿using Coldairarrow.Business.AOP;
using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HolidayManage
{
    public class HR_AskLeaveBusiness : BaseBusiness<HR_AskLeave>, IHR_AskLeaveBusiness, ITransientDependency
    {
        private IConfiguration _configuration;
        private IHR_FormalEmployeesBusiness _hR_FormalEmployeesBusiness;
        public HR_AskLeaveBusiness(IConfiguration configuration, IDbAccessor db, IHR_FormalEmployeesBusiness hR_FormalEmployeesBusiness)
            : base(db)
        {
            _configuration = configuration;
            _hR_FormalEmployeesBusiness = hR_FormalEmployeesBusiness;
        }

        #region 外部接口

        public DataTable GetExcelEmpDataList(PageInput<ConditionDTO> input, string userId)
        {
            var q = from a in GetIQueryable()
                    join e in this.Db.GetIQueryable<HR_FormalEmployees>() on a.F_UserId equals e.F_Id
                    where e.BaseUserId == userId
                    select a;
            var where = LinqHelper.True<HR_AskLeave>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_AskLeave, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            where = where.AndIf(search.wfState.HasValue, x => x.F_WFState == search.wfState);
            if (search.selectIds != null && search.selectIds.Count > 0)
            {
                where = where.And(x => search.selectIds.Contains(x.F_Id));
            }

            return q.Where(where).OrderBy(input.SortField, input.SortType).ToDataTable();
        }

        /// <summary>
        /// 面向员工查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PageResult<HR_AskLeaveDTO>> GetEmpDataListAsync(PageInput<ConditionDTO> input, string userId)
        {
            Expression<Func<HR_AskLeave, HR_FormalEmployees, HR_AskLeaveDTO>> select = (a, e) => new HR_AskLeaveDTO
            {
                UserName = e.NameUser
            };
            select = select.BuildExtendSelectExpre();
            var q = from a in GetIQueryable().AsExpandable()
                    join e in this.Db.GetIQueryable<HR_FormalEmployees>() on a.F_UserId equals e.F_Id
                    where e.BaseUserId == userId
                    select @select.Invoke(a, e);
            var where = LinqHelper.True<HR_AskLeaveDTO>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                if (search.Condition == "F_WFState")
                {
                    var newWhere = DynamicExpressionParser.ParseLambda<HR_AskLeaveDTO, bool>(
                  ParsingConfig.Default, false, $@"{search.Condition} = @0", search.Keyword);
                    where = where.And(newWhere);
                }
                else
                {
                    var newWhere = DynamicExpressionParser.ParseLambda<HR_AskLeaveDTO, bool>(
                       ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                    where = where.And(newWhere);
                }
            }
            where = where.AndIf(search.wfState.HasValue, x => x.F_WFState == search.wfState);

            return await q.Where(where).GetPageResultAsync(input);
        }
        public async Task<PageResult<HR_AskLeaveDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_AskLeave, HR_FormalEmployees, HR_AskLeaveDTO>> select = (a, e) => new HR_AskLeaveDTO
            {
                UserName = e.NameUser

            };
            select = select.BuildExtendSelectExpre();
            var q = from a in GetIQueryable().AsExpandable()
                    join e in this.Db.GetIQueryable<HR_FormalEmployees>() on a.F_UserId equals e.F_Id
                    select @select.Invoke(a, e);
            var where = LinqHelper.True<HR_AskLeaveDTO>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                if (search.Condition == "F_WFState")
                {
                    var newWhere = DynamicExpressionParser.ParseLambda<HR_AskLeaveDTO, bool>(
                  ParsingConfig.Default, false, $@"{search.Condition} = @0", search.Keyword);
                    where = where.And(newWhere);
                }
                else
                {
                    var newWhere = DynamicExpressionParser.ParseLambda<HR_AskLeaveDTO, bool>(
                       ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                    where = where.And(newWhere);
                }

            }
            where = where.AndIf(search.wfState.HasValue, x => x.F_WFState == search.wfState);
            where = where.AndIf(search.isTerminate.HasValue, x => x.F_IsTerminate == search.isTerminate);
            where = where.AndIf(!search.EmpId.IsNullOrEmpty(), x => x.F_UserId == search.EmpId);
            where = where.AndIf(search.IsTerminate.HasValue, x => x.F_IsTerminate != search.IsTerminate);

            var returnList = await q.Where(where).GetPageResultAsync(input);
            if (returnList.Data != null)
            {
                returnList.Data.ForEach(i => i.TermLeaveTime = Db.GetIQueryable<HR_TermLeave>().Where(x => x.F_AskLeaveId == i.F_Id && x.F_WFState == (int)WFStates.完成流程).Sum(x => x.F_RealAskLeaveTime) ?? 0);
            }
            return returnList;
        }

        public async Task<HR_AskLeave> GetTheDataAsync(string id, string userId)
        {
            var model = await GetEntityAsync(id);
            if (model != null)
            {
                model.F_RemainingAmount = "0";
                var userModel = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(i => i.F_Id == model.F_UserId);
                if (userModel != null)
                {
                    model.EmployeesCode = userModel.EmployeesCode;
                    model.F_UserName = userModel.NameUser;
                    var moldeLe = Db.GetIQueryable<HR_HolidayLine>().Where(i => i.F_UserId == userModel.F_Id && i.F_HolidayTypes.Contains("年假")).ToList();
                    if (moldeLe.Count > 0)
                    {
                        if (model.F_WFState == 0)
                        {
                            model.F_RemainingAmount = (moldeLe.Sum(s => s.F_ActualAmount) - moldeLe.Sum(s => s.F_UsedLine)).Value.ToString("0.0");
                        }
                        else
                        {
                            model.F_RemainingAmount = ((moldeLe.Sum(s => s.F_ActualAmount) - moldeLe.Sum(s => s.F_UsedLine)).Value + Convert.ToDecimal(model.F_AskLeaveTime.Value)).ToString("0.0");
                        }

                    }
                }
            }
            return model;
        }
        /// <summary>
        /// 获取年假剩余信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public string GetAnnualLeaveSYInfo(string userId, DateTime? time)
        {
            if (userId.IsNullOrEmpty())
            {
                throw new BusException("参数不能为空");
            }
            StringBuilder info = new StringBuilder();

            var dataNow = DateTime.Now.Date;
            var hTime = dataNow;
            if (time.HasValue && time.Value.Date.Year < dataNow.Year)
            {
                hTime = time.Value;
            }
            var moldeLe = Db.GetIQueryable<HR_HolidayLine>().Where(x => x.F_UserId == userId && x.F_EffectTime <= hTime && x.F_EndTime >= hTime && x.F_HolidayTypes.Contains("年假")).ToList();
            if (moldeLe.Count() > 0)
            {
                var fd = moldeLe.FirstOrDefault(x => x.F_HolidayTypes == "法定年假");
                decimal syfdDay = 0; //剩余法定年假
                decimal syflDay = 0; //剩余福利年假
                if (fd == null)
                {
                    info.Append("剩余法定年假0天");
                }
                else
                {
                    syfdDay = fd.F_ActualAmount.Value - fd.F_UsedLine.Value;
                    info.Append($"剩余法定年假{syfdDay.ToString("0.0")}天");
                }
                var fl = moldeLe.FirstOrDefault(x => x.F_HolidayTypes == "福利年假");
                if (fl == null)
                {
                    info.Append("，福利年假0天");
                }
                else
                {
                    syflDay = fl.F_ActualAmount.Value - fl.F_UsedLine.Value;
                    info.Append($"，福利年假{syflDay.ToString("0.0")}天");
                }
                info.Append($"，合计{(syfdDay + syflDay).ToString("0.0")}天");
            }
            return info.ToString();
        }
        public string GetTimeAsync(string userId)
        {
            string remainingDays = "";
            var userModel = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(i => i.BaseUserId == userId);
            if (userModel != null)
            {
                //var moldeLe = Db.GetIQueryable<HR_HolidayLine>().FirstOrDefault(i => i.F_UserId == userModel.F_Id);
                var dataNow = DateTime.Now.Date;
                var moldeLe = Db.GetIQueryable<HR_HolidayLine>().Where(x => x.F_UserId == userModel.F_Id && x.F_EffectTime <= dataNow && x.F_EndTime >= dataNow && x.F_HolidayTypes.Contains("年假")).ToList();
                if (moldeLe.Count() > 0)
                {
                    remainingDays = (moldeLe.Sum(s => s.F_ActualAmount) - moldeLe.Sum(s => s.F_UsedLine)).Value.ToString("0.0");
                }
            }
            return remainingDays;
        }
        public HR_AskLeave GetById(string id)
        {
            return this.GetEntity(id);
        }

        /// <summary>
        /// 提交流程
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <returns>是否创建成功</returns>
        [DataAddLog(UserLogType.请假管理, "F_UserName", "请假（提交流程）")]
        //[Transactional]
        public bool CreateFlow(HR_AskLeave data, string url, IOperator op)
        {

            if (data.F_PeriodTime == 0)
            {
                data.F_StartTime = (data.F_StartTime.Value.ToString("yyyy-MM-dd") + " 09:00").ToDateTime();
            }
            else
            {
                data.F_StartTime = (data.F_StartTime.Value.ToString("yyyy-MM-dd") + " 13:00").ToDateTime();
            }
            if (data.F_PeriodTime2 == 0)
            {
                data.F_EndTime = (data.F_EndTime.Value.ToString("yyyy-MM-dd") + " 12:30").ToDateTime();
            }
            else
            {
                data.F_EndTime = (data.F_EndTime.Value.ToString("yyyy-MM-dd") + " 18:00").ToDateTime();
            }


            var YearHLInfo = "";
            var isGeneralManager = 0;
            var dataNow = DateTime.Now.Date;
            decimal leaveTime = Convert.ToDecimal(data.F_AskLeaveTime);
            var hTime = dataNow;
            var isLastFlow = false;//是否是去年的流程
            //时间判断:如果是本年2月15日前，选择去年日期，扣去年的法定假期，假期用完则不可提起
            if (dataNow <= Convert.ToDateTime(dataNow.Year.ToString() + "-02-15") && data.F_StartTime.Value.Year < dataNow.Year)
            {
                //不允许提除年假之外的流程
                if (data.F_AskLeaveType != "年假")
                {
                    throw new BusException("去年假期不能提除年假之外的流程申请!");
                }
                hTime = data.F_StartTime.Value;
                isLastFlow = true;
            }

            var hlEntity = Db.GetIQueryable<HR_HolidayLine>().Where(x => x.F_UserId == data.F_UserId && x.F_EffectTime <= hTime && x.F_EndTime >= hTime && x.F_HolidayTypes.Contains("年假")).ToList();
            var hlEntityB = Db.GetIQueryable<HR_HolidayLine>().FirstOrDefault(x => x.F_UserId == data.F_UserId && x.F_EffectTime <= dataNow && x.F_EndTime >= dataNow && x.F_HolidayTypes.Contains("病假"));
            var enModel = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(i => i.F_Id == data.F_UserId);
            if (!string.IsNullOrEmpty(enModel?.F_PositionId))
            {
                isGeneralManager = (Int32)(this.Db.GetIQueryable<Base_Post>()
                    .FirstOrDefault(i => i.F_Id == enModel.F_PositionId)?.F_IsDirectReports ?? 0);


                //var postModel = Db.GetIQueryable<Base_Post>().FirstOrDefault(i => i.F_Id == enModel.F_PositionId);
                //if (postModel?.F_IsDirectReports == 1)
                //{
                //    isGeneralManager = postModel.F_IsDirectReports.Value;
                //}
            }
            if (data.F_AskLeaveType == "年假")
            {
                if (data.F_StartTime.Value.Year != data.F_EndTime.Value.Year)
                {
                    throw new BusException("不能跨年请年假");
                }

                if (hlEntity.Count == 0)
                {
                    throw new BusException("对不起，你没有年假！");
                }
                else
                {
                    //如果是去年的流程则判断额度
                    //if (isLastFlow)
                    //{

                    //}
                    decimal? totalH = hlEntity.Sum(s => s.F_ActualAmount) - hlEntity.Sum(s => s.F_UsedLine);
                    var legalH = hlEntity.FirstOrDefault(i => i.F_HolidayTypes == "法定年假");
                    var welfare = hlEntity.FirstOrDefault(i => i.F_HolidayTypes == "福利年假");
                    decimal lealHDay = 0;
                    decimal welfareDay = 0;
                    if (totalH < leaveTime)
                    {
                        throw new BusException("对不起，你年假只有" + totalH + "天，不能请" + leaveTime + "天年假。");
                    }
                    else
                    {
                        if (leaveTime < totalH)
                        {
                            if ((legalH.F_ActualAmount - legalH.F_UsedLine) >= leaveTime)
                            {
                                legalH.F_UsedLine = legalH.F_UsedLine + leaveTime;
                                lealHDay = leaveTime;
                                YearHLInfo = "法定年假：" + leaveTime + " 天，福利年假：0 天；剩余法定年假：" + (legalH.F_ActualAmount - legalH.F_UsedLine) + " 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_ActualAmount)) + " 天";
                            }
                            else
                            {
                                //如果是去年的流程则判断额度
                                if (isLastFlow)
                                {
                                    throw new BusException("对不起，你去年法定年假只有" + (legalH.F_ActualAmount - legalH.F_UsedLine) + "天，不能请" + leaveTime + "天年假。");
                                }
                                var syLine = legalH.F_ActualAmount - legalH.F_UsedLine;
                                legalH.F_UsedLine = legalH.F_ActualAmount;
                                welfare.F_UsedLine = (decimal)(welfare == null ? 0 : welfare.F_UsedLine) + (leaveTime - syLine);
                                lealHDay = (decimal)syLine;
                                welfareDay = (decimal)(leaveTime - syLine);
                                //YearHLInfo = "法定年假：" + syLine + " 天,福利年假：" + (leaveTime - syLine) + "天";
                                YearHLInfo = "法定年假：" + syLine + " 天，福利年假：" + (leaveTime - syLine) + " 天；剩余法定年假：0 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_ActualAmount) - (decimal)(welfare == null ? 0 : welfare.F_UsedLine)) + " 天";
                            }
                            //hlEntity.F_StandardLine = 0;
                            //var syLeaveTime = (leaveTime - hlEntity.F_StandardLine);
                            //hlEntity.F_ActualAmount -= syLeaveTime;
                            //YearHLInfo = "法定年假：" + hlEntity.F_StandardLine + "天,福利年假：" + syLeaveTime + "天"; 
                        }
                        else
                        {
                            //如果是去年的流程则判断额度
                            if (isLastFlow)
                            {
                                throw new BusException("对不起，你去年法定年假只有" + (legalH.F_ActualAmount - legalH.F_UsedLine) + "天，不能请" + leaveTime + "天年假。");
                            }
                            if (welfare == null)
                            {
                                var syLine = legalH.F_ActualAmount - legalH.F_UsedLine;
                                legalH.F_UsedLine = legalH.F_ActualAmount;
                                //YearHLInfo = "法定年假：" + syLine + "天,福利年假：" + (leaveTime - syLine) + " 天";
                                lealHDay = (decimal)syLine;
                                welfareDay = (decimal)(leaveTime - syLine);
                                YearHLInfo = "法定年假：" + syLine + " 天，福利年假：" + (leaveTime - syLine) + " 天；剩余法定年假：0 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_ActualAmount) - (decimal)(welfare == null ? 0 : welfare.F_UsedLine)) + " 天";
                            }
                            else
                            {
                                var syLine = legalH.F_ActualAmount - legalH.F_UsedLine;
                                legalH.F_UsedLine = legalH.F_ActualAmount;
                                welfare.F_UsedLine = (decimal)(welfare == null ? 0 : welfare.F_ActualAmount);
                                //YearHLInfo = "法定年假：" + syLine + "天,福利年假：" + (leaveTime - syLine) + " 天";
                                lealHDay = (decimal)syLine;
                                welfareDay = (decimal)(leaveTime - syLine);
                                YearHLInfo = "法定年假：" + syLine + " 天，福利年假：" + (leaveTime - syLine) + " 天；剩余法定年假：0 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_ActualAmount) - (decimal)(welfare == null ? 0 : welfare.F_UsedLine)) + " 天";
                            }

                        }
                    }
                    //if (leaveTime > hlEntity.F_StandardLine)
                    //{
                    //    hlEntity.F_StandardLine = 0;
                    //}
                    //else
                    //{
                    //    hlEntity.F_StandardLine -= leaveTime;
                    //}
                    //hlEntity.F_ActualAmount -= leaveTime; 
                    data.F_StatutoryLeave = lealHDay;
                    data.F_WelfareLeave = welfareDay;
                }
            }
            if (data.F_AskLeaveType == "病假")
            {
                if (data.F_StartTime.Value.Year != data.F_EndTime.Value.Year)
                {
                    throw new BusException("不能跨年请病假");
                }

                if (hlEntityB == null)
                {
                    YearHLInfo = "带薪病假：0 天,无薪病假：" + leaveTime + " 天";
                }
                else
                {
                    if (hlEntityB.F_UsedLine + leaveTime >= hlEntityB.F_ActualAmount)
                    {
                        //YearHLInfo = "带薪病假：" + (hlEntityB.F_ActualAmount - hlEntityB.F_UsedLine) + "天,无薪病假：" + (leaveTime - (hlEntityB.F_ActualAmount - hlEntityB.F_UsedLine)) + " 天";
                        YearHLInfo = "带薪病假： " + (hlEntityB.F_ActualAmount - hlEntityB.F_UsedLine) + " 天，无薪病假：" + (leaveTime - (hlEntityB.F_ActualAmount - hlEntityB.F_UsedLine)) + "  天；剩余带薪病假：0 天";
                        hlEntityB.F_UsedLine = hlEntityB.F_ActualAmount;
                    }
                    else
                    {
                        hlEntityB.F_UsedLine = hlEntityB.F_UsedLine + leaveTime;
                        //YearHLInfo = "带薪病假：" + leaveTime + "天,无薪病假：0 天";
                        YearHLInfo = "带薪病假： " + leaveTime + " 天，无薪病假： 0 天；剩余带薪病假：" + (hlEntityB.F_ActualAmount - hlEntityB.F_UsedLine) + " 天";
                    }
                }
            }

            if (data.F_Id.IsNullOrEmpty())
            {
                data.F_TermLeaveTime = data.F_AskLeaveTime;
                InitEntity(data, op);
                data.F_BusState = (int)ASKBusState.正常;

                Add(data);
            }
            else
            {
                UpdateEntity(data, op);
                Edit(data);
            }
            bool ret = false;
            var entity = Db.GetIQueryable<Base_FileInfo>().Where(i => i.F_FileFolderId == data.F_FileId).Select(x => new FileInfoDTO() { Id = x.F_Id, FileName = x.F_FileName, FilePath = x.F_FilePath }).ToList();
            string token = string.Empty;
            Base_User user = null;
            var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
            ProjectDept projectDept = new ProjectDept();
            if (formalEmployees != null)
            {
                user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == formalEmployees.BaseUserId);
                token = JWTHelper.GetBusinessToken(data.F_Id, user != null ? user.UserName.Replace("@cqlandmark.com", "") : "");
                projectDept = _hR_FormalEmployeesBusiness.GetProjectDept(formalEmployees.F_Id);
            }
            //var user = db.getiqueryable<base_user>().firstordefault(x => x.id == data.f_createuserid);
            //string token = jWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
            var userName = user != null ? user.UserName.Replace("@cqlandmark.com", "") : "";
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            paramters.Add("reqCode", token);
            paramters.Add("reqNo", data.F_Id);
            paramters.Add("applyPerson", userName);
            var companyName = projectDept?.company;
            var companys= new List<string> { "重庆怡置商业管理有限公司", "重庆外商服务有限公司-商业" };
            paramters.Add("formId", companyName!=null&& companys.Contains(companyName)? _configuration["OASYFormId:AskLeaveFlow"] : _configuration["OAFormId:AskLeaveFlow"]);
            Dictionary<string, object> hrData = new Dictionary<string, object>();
            hrData.Add("vacate_type", data.F_AskLeaveType);
            hrData.Add("vacate_start_time", data.F_PeriodTime);
            hrData.Add("vacate_end_time", data.F_PeriodTime2);
            hrData.Add("vacate_start_date", data.F_StartTime.Value.ToString("yyyy-MM-dd"));
            hrData.Add("vacate_end_date", data.F_EndTime.Value.ToString("yyyy-MM-dd"));
            hrData.Add("vacate_day_count", data.F_AskLeaveTime.Value);
            hrData.Add("vacate_remain_time", (hlEntity.Sum(s => s.F_ActualAmount) - hlEntity.Sum(s => s.F_UsedLine)));
            hrData.Add("vacate_unit", "1");
            hrData.Add("vacate_remark", data.F_AskLeaveR);
            hrData.Add("vacate_files", string.Empty);
            hrData.Add("vacate_apply_date", data.F_CreateDate.ToString("yyyy-MM-dd"));
            hrData.Add("vacate_code", data.F_AskLeaveCode);
            hrData.Add("vacate_org", data.F_OrganizeInfo);
            hrData.Add("note", YearHLInfo);
            hrData.Add("files", entity);
            hrData.Add("is_under_general_manager", isGeneralManager);
            hrData.Add("project", OAHelper.GetNuLL(projectDept?.project));
            hrData.Add("dept", OAHelper.GetNuLL(projectDept?.dept));
            hrData.Add("oaCompany", OAHelper.GetNuLL(projectDept?.company));
            paramters.Add("hrData", hrData);

            string action = "create";
            string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);

            var retObj = str.ToObject<Dictionary<string, string>>();
            if (retObj != null)
            {
                if (retObj["errCode"] == "0000")
                {
                    data.F_WFId = retObj["oaReqId"];
                    data.F_WFState = (int)WFStates.审核中;
                    Update(data);
                    //如果是法定假期则修改假期额度
                    if (data.F_AskLeaveType == "年假")
                    {
                        Db.Update(hlEntity);
                    }
                    if (data.F_AskLeaveType == "病假")
                    {
                        if (hlEntityB != null)
                        {
                            Db.Update(hlEntityB);
                        }
                    }
                    ret = true;
                }
                else
                {
                    throw new BusException(retObj["errDesc"]);
                }
            }
            else
            {
                throw new BusException("创建流程失败");
            }
            return ret;
        }

        /// <summary>
        /// 提交、退回流程
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <param name="act">submit 提交  subnoback 提交不需回复   subback 提交需要回复  reject退回</param>
        /// <returns>是否成功</returns>
        [DataAddLog(UserLogType.请假管理, "F_UserName", "请假（提交、退回流程）")]
        public bool ActWorkflow(HR_AskLeave data, string url, IOperator op, string act = "submit")
        {

            if (data.F_PeriodTime == 0)
            {
                data.F_StartTime = (data.F_StartTime.Value.ToString("yyyy-MM-dd") + " 09:00").ToDateTime();
            }
            else
            {
                data.F_StartTime = (data.F_StartTime.Value.ToString("yyyy-MM-dd") + " 13:00").ToDateTime();
            }
            if (data.F_PeriodTime2 == 0)
            {
                data.F_EndTime = (data.F_EndTime.Value.ToString("yyyy-MM-dd") + " 12:30").ToDateTime();
            }
            else
            {
                data.F_EndTime = (data.F_EndTime.Value.ToString("yyyy-MM-dd") + " 18:00").ToDateTime();
            }
            //申请时间没有值时设置为当前时间
            if (!data.F_ApplyTime.HasValue)
            {
                data.F_ApplyTime = DateTime.Now;
            }

            if (!data.F_Id.IsNullOrEmpty())
            {
                HR_AskLeave oldEntity = GetById(data.F_Id);
                if (oldEntity != null)
                {
                    data.OldAskLeaveTime = oldEntity.F_AskLeaveTime;
                }
            }
            bool ret = false;
            var YearHLInfo = "";
            var isGeneralManager = 0;
            var dataNow = DateTime.Now.Date;
            decimal leaveTime = Convert.ToDecimal(data.F_AskLeaveTime);
            decimal oldleaveTime = Convert.ToDecimal(data.OldAskLeaveTime);
            var hlEntity = Db.GetIQueryable<HR_HolidayLine>().Where(x => x.F_UserId == data.F_UserId && x.F_EffectTime <= dataNow && x.F_EndTime >= dataNow && x.F_HolidayTypes.Contains("年假")).ToList();
            var hlEntityB = Db.GetIQueryable<HR_HolidayLine>().FirstOrDefault(x => x.F_UserId == data.F_UserId && x.F_EffectTime <= dataNow && x.F_EndTime >= dataNow && x.F_HolidayTypes.Contains("病假"));
            var enModel = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(i => i.F_Id == data.F_UserId);
            if (!string.IsNullOrEmpty(enModel?.F_PositionId))
            {
                isGeneralManager = (Int32)(this.Db.GetIQueryable<Base_Post>()
                    .FirstOrDefault(i => i.F_Id == enModel.F_PositionId)?.F_IsDirectReports ?? 0);

                //var postModel = Db.GetIQueryable<Base_Post>().FirstOrDefault(i => i.F_Id == enModel.F_PositionId);
                //if (postModel?.F_IsDirectReports == 1)
                //{
                //    isGeneralManager = postModel.F_IsDirectReports.Value;
                //}
            }
            if (data.F_AskLeaveType == "年假")
            {
                if (data.F_StartTime.Value.Year != data.F_EndTime.Value.Year)
                {
                    throw new BusException("不能跨年请假");
                }
                if (hlEntity.Count == 0)
                {
                    throw new BusException("对不起，你没有年假！");
                }
                else
                {

                    decimal? totalH = hlEntity.Sum(s => s.F_ActualAmount) - hlEntity.Sum(s => s.F_UsedLine) + Convert.ToDecimal(data.OldAskLeaveTime);
                    var legalH = hlEntity.FirstOrDefault(i => i.F_HolidayTypes == "法定年假");
                    var welfare = hlEntity.FirstOrDefault(i => i.F_HolidayTypes == "福利年假");
                    decimal lealHDay = 0;
                    decimal welfareDay = 0;
                    if (leaveTime != oldleaveTime)
                    {
                        if (totalH < leaveTime)
                        {
                            throw new BusException("对不起，你年假只有" + totalH + "天，不能请" + leaveTime + "天年假。");
                        }
                        //福利年假剩余天数
                        var welfNL = (decimal)(welfare == null ? 0 : welfare.F_ActualAmount) - (decimal)(welfare == null ? 0 : welfare.F_UsedLine);
                        if (leaveTime > oldleaveTime)
                        {
                            var lolT = leaveTime - oldleaveTime;
                            if (legalH.F_StandardLine >= (legalH.F_UsedLine + lolT))
                            {
                                //YearHLInfo = "法定年假：" + leaveTime + " 天,福利年假：0 天";
                                lealHDay = leaveTime;
                                YearHLInfo = "法定年假：" + leaveTime + " 天，福利年假：0 天；剩余法定年假：" + (legalH.F_ActualAmount - (legalH.F_UsedLine + lolT)) + " 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_ActualAmount)) + " 天";
                                legalH.F_UsedLine = legalH.F_UsedLine + lolT;
                            }
                            else
                            {
                                if (welfare.F_UsedLine - leaveTime >= 0)
                                {
                                    legalH.F_UsedLine = legalH.F_StandardLine;
                                    welfare.F_UsedLine = leaveTime;
                                    YearHLInfo = "法定年假：0 天，福利年假：" + leaveTime + " 天；剩余法定年假：0 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_ActualAmount) - ((decimal)(welfare == null ? 0 : welfare.F_UsedLine) + leaveTime)) + "  天";
                                }
                                else
                                {
                                    var lewelN = (leaveTime - welfare.F_UsedLine) - lolT;
                                    legalH.F_UsedLine = lewelN;
                                    welfare.F_UsedLine = welfare.F_UsedLine + lolT;
                                    lealHDay = (decimal)(lewelN);
                                    welfareDay = (decimal)(welfare == null ? 0 : welfare.F_UsedLine);
                                    //YearHLInfo = "法定年假：" + (legalH.F_StandardLine - legalH.F_UsedLine) + " 天,福利年假：" + (lolT - (legalH.F_StandardLine - legalH.F_UsedLine)) + " 天";
                                    YearHLInfo = "法定年假：" + (lewelN) + " 天，福利年假：" + (decimal)(welfare == null ? 0 : welfare.F_UsedLine) + " 天；剩余法定年假：0 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_ActualAmount) - (decimal)(welfare == null ? 0 : welfare.F_UsedLine)) + " 天";
                                }
                            }
                        }
                        else
                        {
                            var lolT = oldleaveTime - leaveTime;
                            //只有法定年假
                            if (legalH.F_StandardLine >= (legalH.F_UsedLine + lolT))
                            {
                                // YearHLInfo = "法定年假：" + leaveTime + " 天,福利年假：0 天";
                                lealHDay = (decimal)leaveTime;
                                YearHLInfo = "法定年假：" + leaveTime + " 天，福利年假：0 天；剩余法定年假：" + (legalH.F_ActualAmount - (legalH.F_UsedLine - lolT)) + " 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_ActualAmount)) + " 天";
                                legalH.F_UsedLine = legalH.F_UsedLine - lolT;
                            }
                            else
                            {
                                //变更后只有法定年假 
                                var restDay = (decimal)(welfare == null ? 0 : welfare.F_UsedLine) - lolT;
                                if (restDay <= 0)
                                {
                                    if (welfare != null)
                                    {
                                        welfare.F_UsedLine = 0;
                                    }
                                    legalH.F_UsedLine = legalH.F_UsedLine + restDay;
                                    lealHDay = (decimal)leaveTime;
                                    // YearHLInfo = "法定年假：" + leaveTime + " 天,福利年假：0 天";
                                    YearHLInfo = "法定年假：" + leaveTime + " 天，福利年假：0 天；剩余法定年假：" + (legalH.F_ActualAmount - (legalH.F_UsedLine)) + " 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_ActualAmount)) + " 天";

                                }
                                else
                                {

                                    var sdsds = (decimal)(welfare == null ? 0 : welfare.F_UsedLine) - leaveTime;
                                    if (sdsds >= 0)
                                    {
                                        //YearHLInfo = "法定年假：0 天,福利年假：" + leaveTime + " 天"; 

                                        lealHDay = (decimal)(lolT - ((decimal)(welfare == null ? 0 : welfare.F_UsedLine) - leaveTime));
                                        welfareDay = (decimal)(leaveTime - (lolT - ((decimal)(welfare == null ? 0 : welfare.F_UsedLine) - leaveTime)));
                                        YearHLInfo = "法定年假：" + (lolT - ((decimal)(welfare == null ? 0 : welfare.F_UsedLine) - leaveTime)) + " 天，福利年假：" + (leaveTime - (lolT - ((decimal)(welfare == null ? 0 : welfare.F_UsedLine) - leaveTime))) + " 天；剩余法定年假：0 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_ActualAmount) - ((decimal)(welfare == null ? 0 : welfare.F_UsedLine) - lolT)) + " 天";

                                        legalH.F_UsedLine = legalH.F_StandardLine;
                                        welfare.F_UsedLine = (decimal)(welfare == null ? 0 : welfare.F_UsedLine) - lolT;

                                    }
                                    else
                                    {
                                        // YearHLInfo = "法定年假：" + (leaveTime - (welfare.F_UsedLine - lolT)) + " 天,福利年假：0  天";
                                        lealHDay = (decimal)(leaveTime - ((decimal)(welfare == null ? 0 : welfare.F_UsedLine) - lolT));
                                        welfareDay = (decimal)(((decimal)(welfare == null ? 0 : welfare.F_UsedLine) - lolT));
                                        YearHLInfo = "法定年假：" + (leaveTime - ((decimal)(welfare == null ? 0 : welfare.F_UsedLine) - lolT)) + " 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_UsedLine) - lolT) + " 天；剩余法定年假：0 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_ActualAmount) - ((decimal)(welfare == null ? 0 : welfare.F_UsedLine) - lolT)) + " 天";

                                        legalH.F_UsedLine = legalH.F_StandardLine;
                                        welfare.F_UsedLine = ((decimal)(welfare == null ? 0 : welfare.F_UsedLine) - lolT);
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        if (legalH.F_ActualAmount >= legalH.F_UsedLine)
                        {
                            if ((decimal)(welfare == null ? 0 : welfare.F_UsedLine) >= leaveTime)
                            {
                                YearHLInfo = "法定年假：0 天，福利年假：" + leaveTime + " 天；剩余法定年假：0 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_ActualAmount) - (decimal)(welfare == null ? 0 : welfare.F_UsedLine)) + " 天";
                            }
                            else
                            {
                                var datainfo = leaveTime - (decimal)(welfare == null ? 0 : welfare.F_UsedLine);
                                lealHDay = (decimal)datainfo;
                                welfareDay = (decimal)(welfare == null ? 0 : welfare.F_UsedLine);
                                YearHLInfo = "法定年假：" + datainfo + " 天，福利年假：" + (decimal)(welfare == null ? 0 : welfare.F_UsedLine) + " 天；剩余法定年假：" + (legalH.F_ActualAmount - legalH.F_UsedLine) + " 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_ActualAmount) - (decimal)(welfare == null ? 0 : welfare.F_UsedLine)) + " 天";
                            };
                            //YearHLInfo = "法定年假：" + leaveTime + " 天,福利年假：0 天";


                        }
                        else
                        {
                            var welfN = (decimal)(welfare == null ? 0 : welfare.F_ActualAmount) - (decimal)(welfare == null ? 0 : welfare.F_UsedLine);
                            //YearHLInfo = "法定年假：" + (leaveTime - welfN) + " 天,福利年假：" + welfN + " 天";
                            lealHDay = (decimal)(leaveTime - welfN);
                            welfareDay = (decimal)(welfN);
                            YearHLInfo = "法定年假：" + (leaveTime - welfN) + " 天，福利年假：" + welfN + " 天；剩余法定年假：0 天，福利年假：" + ((decimal)(welfare == null ? 0 : welfare.F_ActualAmount) - (decimal)(welfare == null ? 0 : welfare.F_UsedLine)) + " 天";

                        }
                    }
                    data.F_StatutoryLeave = lealHDay;
                    data.F_WelfareLeave = welfareDay;
                }
                //if ()
                //{


                //decimal leaveTime = Convert.ToDecimal(data.F_AskLeaveTime);
                //    decimal leaveTimeOld = Convert.ToDecimal(data.OldAskLeaveTime);
                //    var actulmount = hlEntity.F_ActualAmount + leaveTimeOld;
                //    if (actulmount < leaveTime)
                //    {
                //        throw new BusException("对不起，你年假只有" + actulmount + "天，不能请" + leaveTime + "天年假。");
                //    }
                //    else
                //    {
                //        if (leaveTime > leaveTimeOld)
                //        {
                //            var leTime = leaveTime - leaveTimeOld;
                //            if (hlEntity.F_StandardLine == 0)
                //            {
                //                YearHLInfo = "法定年假：0 天,福利年假：" + leaveTime + "天";
                //            }
                //            else
                //            {
                //                if (hlEntity.F_StandardLine < leTime)
                //                {
                //                    hlEntity.F_StandardLine = 0;
                //                    YearHLInfo = "法定年假：" + hlEntity.F_StandardLine + leaveTimeOld + " 天,福利年假：" + (leaveTime - (hlEntity.F_StandardLine.Value + leaveTimeOld)) + "天";
                //                }
                //                else
                //                {
                //                    hlEntity.F_StandardLine -= leTime;
                //                    YearHLInfo = "法定年假：" + leaveTime + " 天,福利年假：0 天";
                //                }
                //            }
                //            hlEntity.F_ActualAmount -= leTime;
                //        }
                //        else if (leaveTime < leaveTimeOld)
                //        {
                //            var leTime = leaveTimeOld - leaveTime;
                //            hlEntity.F_ActualAmount += leTime;
                //            if ((hlEntity.F_ActualAmountLast - hlEntity.F_StandardLineLast) < hlEntity.F_ActualAmount)
                //            {
                //                hlEntity.F_StandardLine = hlEntity.F_ActualAmount - (hlEntity.F_ActualAmountLast - hlEntity.F_StandardLineLast);
                //                YearHLInfo = "法定年假：" + leaveTime + " 天,福利年假：0 天";
                //            }
                //            else
                //            {
                //                hlEntity.F_StandardLine = 0;
                //                YearHLInfo = "法定年假：" + ((hlEntity.F_ActualAmount + leaveTime) - (hlEntity.F_ActualAmountLast - hlEntity.F_StandardLineLast)) + " 天,福利年假：" + (hlEntity.F_ActualAmount - (((hlEntity.F_ActualAmount + leaveTime) - (hlEntity.F_ActualAmountLast - hlEntity.F_StandardLineLast)))) + " 天";
                //            }
                //            //if (hlEntity.F_StandardLine + leTime > hlEntity.F_StandardLineLast)
                //            //{
                //            //    hlEntity.F_StandardLine = hlEntity.F_StandardLineLast;
                //            //    YearHLInfo = "法定年假：" + leaveTime + " 天,福利年假：0 天";
                //            //}
                //            //else
                //            //{
                //            //    hlEntity.F_StandardLine += leTime;
                //            //    YearHLInfo = "法定年假：" + leaveTime + " 天,福利年假：0 天";
                //            //} 
                //        }
                //    }
                //}
                //}
            }
            if (data.F_AskLeaveType == "病假")
            {
                if (data.F_StartTime.Value.Year != data.F_EndTime.Value.Year)
                {
                    throw new BusException("不能跨年请假");
                }

                if (hlEntityB == null)
                {
                    YearHLInfo = "带薪病假：0 天,无薪病假：" + leaveTime + " 天";
                }
                else
                {
                    if (leaveTime == oldleaveTime)
                    {
                        YearHLInfo = "带薪病假：" + (hlEntityB.F_UsedLine - hlEntityB.F_ActualAmountLast) + " 天,无薪病假：" + (leaveTime - (hlEntityB.F_UsedLine - hlEntityB.F_ActualAmountLast)) + " 天";
                    }
                    else if (leaveTime > oldleaveTime)
                    {
                        var ler = leaveTime - oldleaveTime;
                        if (hlEntityB.F_UsedLine + ler > hlEntityB.F_ActualAmount)
                        {
                            hlEntityB.F_UsedLine = hlEntityB.F_ActualAmount;
                            YearHLInfo = "带薪病假：" + (hlEntityB.F_ActualAmount - hlEntityB.F_ActualAmountLast) + " 天,无薪病假：" + (leaveTime - (hlEntityB.F_UsedLine - hlEntityB.F_ActualAmountLast)) + " 天";
                        }
                        else
                        {
                            hlEntityB.F_UsedLine = hlEntityB.F_UsedLine + ler;
                            YearHLInfo = "带薪病假：" + (hlEntityB.F_UsedLine - hlEntityB.F_ActualAmountLast) + " 天,无薪病假：" + (leaveTime - (hlEntityB.F_UsedLine - hlEntityB.F_ActualAmountLast)) + " 天";
                        }
                    }
                    else
                    {
                        var ler = oldleaveTime - leaveTime;
                        if (hlEntityB.F_UsedLine == hlEntityB.F_ActualAmountLast)
                        {
                            YearHLInfo = "带薪病假：0 天,无薪病假：" + leaveTime + " 天";
                        }
                        hlEntityB.F_UsedLine = hlEntityB.F_UsedLine - ler;
                        YearHLInfo = "带薪病假：" + (hlEntityB.F_UsedLine - hlEntityB.F_ActualAmountLast) + " 天,无薪病假：" + (leaveTime - (hlEntityB.F_UsedLine - hlEntityB.F_ActualAmountLast)) + " 天";
                    }
                }
            }

            if (!data.F_Id.IsNullOrEmpty())
            {
                data.F_TermLeaveTime = data.F_AskLeaveTime;
                UpdateEntity(data, op);
                Edit(data);
            }
            var entity = Db.GetIQueryable<Base_FileInfo>().Where(i => i.F_FileFolderId == data.F_FileId).Select(x => new FileInfoDTO() { Id = x.F_Id, FileName = x.F_FileName, FilePath = x.F_FilePath }).ToList();
            string token = string.Empty;
            Base_User user = null;
            var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
            ProjectDept projectDept = new ProjectDept();
            if (formalEmployees != null)
            {
                user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == formalEmployees.BaseUserId);
                token = JWTHelper.GetBusinessToken(data.F_Id, user != null ? user.UserName.Replace("@cqlandmark.com", "") : "");
                projectDept = _hR_FormalEmployeesBusiness.GetProjectDept(formalEmployees.F_Id);
            }
            //var user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == data.F_CreateUserId);
            //string token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
            var model = this.GetEntity(data.F_Id);
            var userName = user != null ? user.UserName.Replace("@cqlandmark.com", "") : "";
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            paramters.Add("oaId", model.F_WFId);
            paramters.Add("reqCode", token);
            paramters.Add("reqNo", data.F_Id);
            paramters.Add("applyPerson", userName);
            var companyName = projectDept?.company;
            var companys = new List<string> { "重庆怡置商业管理有限公司", "重庆外商服务有限公司-商业" };
            paramters.Add("formId", companyName != null && companys.Contains(companyName) ? _configuration["OASYFormId:AskLeaveFlow"] : _configuration["OAFormId:AskLeaveFlow"]);
            //paramters.Add("sign", "");
            paramters.Add("act", act);
            Dictionary<string, object> hrData = new Dictionary<string, object>();
            hrData.Add("vacate_type", data.F_AskLeaveType);
            hrData.Add("vacate_start_time", data.F_PeriodTime);
            hrData.Add("vacate_end_time", data.F_PeriodTime2);
            hrData.Add("vacate_start_date", data.F_StartTime.Value.ToString("yyyy-MM-dd"));
            hrData.Add("vacate_end_date", data.F_EndTime.Value.ToString("yyyy-MM-dd"));
            hrData.Add("vacate_day_count", data.F_AskLeaveTime.Value);
            hrData.Add("vacate_remain_time", (hlEntity.Sum(s => s.F_ActualAmount) - hlEntity.Sum(s => s.F_UsedLine)));
            hrData.Add("vacate_unit", "1");
            hrData.Add("vacate_remark", data.F_AskLeaveR);
            hrData.Add("vacate_files", string.Empty);
            hrData.Add("vacate_apply_date", data.F_CreateDate.ToString("yyyy-MM-dd"));
            hrData.Add("vacate_code", data.F_AskLeaveCode);
            hrData.Add("vacate_org", data.F_OrganizeInfo);
            //hrData.Add("note", "");
            hrData.Add("note", YearHLInfo);
            hrData.Add("files", entity);
            //hrData.Add("project", OAHelper.GetNuLL(projectDept?.project));
            //hrData.Add("dept", OAHelper.GetNuLL(projectDept?.dept));
            hrData.Add("is_under_general_manager", isGeneralManager);
            hrData.Add("project", OAHelper.GetNuLL(projectDept?.project));
            hrData.Add("dept", OAHelper.GetNuLL(projectDept?.dept));
            hrData.Add("oaCompany", OAHelper.GetNuLL(projectDept?.company));
            paramters.Add("hrData", hrData);

            string action = "actWorkflow";
            string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);

            var retObj = str.ToObject<Dictionary<string, string>>();
            if (retObj != null)
            {
                if (retObj["errCode"] == "0000")
                {
                    if (act == "submit")
                    {
                        data.F_WFId = retObj["oaReqId"];
                        data.F_WFState = (int)WFStates.审核中;
                        Update(data);
                        //如果是法定假期则修改假期额度
                        if (data.F_AskLeaveType == "年假")
                        {
                            //修改假期额度
                            //var hlEntity = Db.GetIQueryable<HR_HolidayLine>().FirstOrDefault(x => x.F_UserId == data.F_UserId);
                            //if (hlEntity != null)
                            //{
                            //    decimal leaveTime = 0;
                            //    if (data.F_AskLeaveTime != data.OldAskLeaveTime)
                            //    {
                            //        //如果之前请假时长大于当前请假时长，把他们的差值加回去
                            //        if (data.OldAskLeaveTime > data.F_AskLeaveTime)
                            //        {
                            //            leaveTime = Convert.ToDecimal(data.OldAskLeaveTime - data.F_AskLeaveTime);
                            //            hlEntity.F_ActualAmount += leaveTime;
                            //            if (hlEntity.F_StandardLine > 0)
                            //            {
                            //                hlEntity.F_StandardLine += leaveTime;
                            //            }
                            //            else
                            //            {
                            //                //如果开始的标准额度大于等于请假时长，标准额度就等于开始标准额度
                            //                if (hlEntity.F_StandardLineLast >= leaveTime)
                            //                {
                            //                    hlEntity.F_StandardLine = hlEntity.F_StandardLineLast;
                            //                }
                            //                else
                            //                {   //否则加上请假时长                                    
                            //                    hlEntity.F_StandardLine += leaveTime;
                            //                }
                            //            }
                            //        }
                            //        else    //否则减去多的值
                            //        {
                            //            leaveTime = Convert.ToDecimal(data.F_AskLeaveTime - data.OldAskLeaveTime);
                            //            hlEntity.F_ActualAmount -= leaveTime;
                            //            if (leaveTime > hlEntity.F_StandardLine)
                            //            {
                            //                hlEntity.F_StandardLine = 0;
                            //            }
                            //            else
                            //            {
                            //                hlEntity.F_StandardLine -= leaveTime;
                            //            }
                            //        }
                            //    }

                            Db.Update(hlEntity);
                            //}
                        }

                        if (data.F_AskLeaveType == "病假")
                        {
                            if (hlEntityB != null)
                            {
                                Db.Update(hlEntityB);
                            }
                        }
                    }
                    ret = true;
                }
                else
                {
                    throw new BusException(retObj["errDesc"]);
                }
            }
            else
            {
                throw new BusException("创建流程失败");
            }

            return ret;
        }

        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <returns>是否成功</returns>
        [DataAddLog(UserLogType.请假管理, "F_UserName", "请假（强制归档流程）")]
        public bool ArchiveWorkflow(HR_AskLeave data, string url)
        {
            bool ret = false;
            //判断是否提交
            var AskLeaveModel = Db.GetIQueryable<HR_AskLeave>().FirstOrDefault(x => x.F_Id == data.F_Id);
            if (AskLeaveModel == null)
            {
                throw new Exception("该条请假记录不存在!");
            }
            if (AskLeaveModel.F_WFState == 4)
            {
                throw new Exception("该条请假记录已经撤销了!");
            }
            string token = string.Empty;
            Base_User user = null;
            var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
            if (formalEmployees != null)
            {
                user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == formalEmployees.BaseUserId);
                token = JWTHelper.GetBusinessToken(data.F_Id, user != null ? user.UserName.Replace("@cqlandmark.com", "") : "");
            }
            //var user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == data.F_CreateUserId);
            //string token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
            var model = this.GetEntity(data.F_Id);
            var userName = user != null ? user.UserName.Replace("@cqlandmark.com", "") : "";
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            paramters.Add("reqCode", token);
            paramters.Add("reqNo", data.F_Id);
            paramters.Add("applyPerson", userName);
            paramters.Add("oaId", model.F_WFId);
            string action = "archiveWorkflow";
            string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);

            var retObj = str.ToObject<Dictionary<string, string>>();
            if (retObj != null)
            {
                if (retObj["errCode"] == "0000")
                {
                    data.F_WFId = retObj["oaReqId"];
                    data.F_WFState = (int)WFStates.取消流程;
                    Update(data);
                    var dataNow = DateTime.Now.Date;
                    //如果是法定假期则,取消流程后要加回额度
                    if (data.F_AskLeaveType == "年假")
                    {

                        List<HR_HolidayLine> holidayLines = new List<HR_HolidayLine>();
                        //修改假期额度 
                        var hlEntity = Db.GetIQueryable<HR_HolidayLine>().Where(x => x.F_UserId == data.F_UserId && x.F_EffectTime <= dataNow && x.F_EndTime >= dataNow && x.F_HolidayTypes.Contains("年假")).ToList();
                        if (hlEntity.Count() > 0)
                        {
                            var legalH = hlEntity.FirstOrDefault(i => i.F_HolidayTypes == "法定年假");
                            var welfare = hlEntity.FirstOrDefault(i => i.F_HolidayTypes == "福利年假");
                            decimal syfdDay = 0; //剩余法定年假
                            decimal syflDay = 0; //剩余福利年假
                            if (legalH != null)
                            {
                                syfdDay = legalH.F_ActualAmount.Value - legalH.F_UsedLine.Value;
                                if (data.F_StatutoryLeave.HasValue)
                                {
                                    legalH.F_UsedLine = legalH.F_ActualAmount - (syfdDay + data.F_StatutoryLeave);
                                    Db.Update(legalH);
                                }
                            }
                            if (welfare != null)
                            {
                                syflDay = welfare.F_ActualAmount.Value - welfare.F_UsedLine.Value;
                                if (data.F_WelfareLeave.HasValue)
                                {
                                    welfare.F_UsedLine = welfare.F_ActualAmount - (syflDay + data.F_WelfareLeave);
                                    Db.Update(welfare);
                                }
                            }
                        }






                        //if (hlEntity.Count() > 0)
                        //{
                        //    decimal leaveTime = Convert.ToDecimal(data.F_AskLeaveTime);
                        //    var legalH = hlEntity.FirstOrDefault(i => i.F_HolidayTypes == "法定年假");
                        //    var welfare = hlEntity.FirstOrDefault(i => i.F_HolidayTypes == "福利年假");
                        //    if (welfare == null)
                        //    {
                        //        legalH.F_UsedLine = legalH.F_UsedLine - leaveTime;
                        //        holidayLines.Add(legalH);
                        //    }
                        //    else
                        //    {
                        //        var leH = (welfare.F_ActualAmount - welfare.F_UsedLine) - leaveTime;
                        //        if (leH >= 0)//
                        //        {
                        //            welfare.F_UsedLine = welfare.F_UsedLine - leaveTime;
                        //        }
                        //        else
                        //        {
                        //            legalH.F_UsedLine = legalH.F_UsedLine - (leaveTime - (welfare.F_ActualAmount - welfare.F_UsedLine));
                        //            welfare.F_UsedLine = welfare.F_ActualAmount;
                        //        }
                        //        holidayLines.Add(legalH);
                        //        holidayLines.Add(welfare);
                        //    }
                        //    Db.Update(holidayLines);
                        //}

                    }

                    if (data.F_AskLeaveType == "病假")
                    {
                        var hlEntity = Db.GetIQueryable<HR_HolidayLine>().FirstOrDefault(x => x.F_UserId == data.F_UserId && x.F_EffectTime <= dataNow && x.F_EndTime >= dataNow && x.F_HolidayTypes.Contains("病假"));
                        decimal leaveTime = Convert.ToDecimal(data.F_AskLeaveTime);
                        if (hlEntity != null)
                        {
                            if (hlEntity.F_ActualAmountLast + leaveTime >= hlEntity.F_ActualAmount)
                            {
                                hlEntity.F_UsedLine = hlEntity.F_ActualAmountLast;
                            }
                            else
                            {
                                hlEntity.F_UsedLine = hlEntity.F_UsedLine - leaveTime;
                            }
                            Db.Update(hlEntity);
                        }
                    }
                    ret = true;
                }
            }

            return ret;
        }

        public void Add(HR_AskLeave data)
        {
            var Entity = Db.GetIQueryable<HR_AskLeave>().Where(i => i.F_UserId == data.F_UserId && !(i.F_WFState == 0 || i.F_WFState == 4 || i.F_WFState == 2));
            foreach (var item in Entity)
            {
                if (!(item.F_StartTime > data.F_EndTime || item.F_EndTime < data.F_StartTime))
                {
                    throw new BusException("你请假的时间内，已经有请过假了！");
                }
            }
            data.F_WFState = (int)WFStates.草稿;
            data.F_ApplyTime = DateTime.Now;
            data.F_IsTerminate = 0;
            Insert(data);
        }

        public void Edit(HR_AskLeave data)
        {
            var Entity = Db.GetIQueryable<HR_AskLeave>().Where(i => i.F_UserId == data.F_UserId && !(i.F_WFState == 0 || i.F_WFState == 4 || i.F_WFState == 2));
            foreach (var item in Entity)
            {
                if (!(item.F_StartTime > data.F_EndTime || item.F_EndTime < data.F_StartTime))
                {
                    throw new BusException("你请假的时间内，已经有请过假了！");
                }
            }
            Update(data);
        }
        public async Task AddDataAsync(HR_AskLeave data)
        {
            data.F_WFState = (int)WFStates.草稿;
            data.F_ApplyTime = DateTime.Now;
            data.F_IsTerminate = 0;
            await InsertAsync(data);

        }

        public async Task UpdateDataAsync(HR_AskLeave data)
        {
            await UpdateAsync(data);
        }
        /// <summary>
        /// 修改数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public bool UpdateData(HR_AskLeave data)
        {
            var entity = this.GetEntity(data.F_Id);
            if (entity == null)
            {
                throw new System.Exception("未找到对象");
            }
            entity.F_UserId = data.F_UserId;
            entity.F_WFState = data.F_WFState;
            entity.F_AskLeaveType = data.F_AskLeaveType;
            entity.F_RemainingAmount = data.F_RemainingAmount;
            entity.F_Unit = data.F_Unit;
            entity.F_StartTime = data.F_StartTime;
            entity.F_PeriodTime = data.F_PeriodTime;
            entity.F_EndTime = data.F_EndTime;
            entity.F_PeriodTime2 = data.F_PeriodTime2;
            entity.F_AskLeaveTime = data.F_AskLeaveTime;
            entity.F_AskLeaveR = data.F_AskLeaveR;
            entity.F_Title = data.F_Title;
            entity.F_ModifyDate = data.F_ModifyDate;
            entity.F_ModifyUserId = data.F_ModifyUserId;
            entity.F_ModifyUserName = data.F_ModifyUserName;
            int rev = Update(entity);
            return rev == 1;
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        /// <summary>
        /// 请假管理员下载
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public DataTable GetExcelManagerList(ConditionDTO input)
        {
            string sql = @"select * from ( select c.Name,b.NameUser,a.F_AskLeaveType,a.F_StartTime,a.F_EndTime,
case when a.F_IsTerminate=1  then (a.F_AskLeaveTime-
(select sum(F_RealAskLeaveTime) from HR_TermLeave t where 1=1 and  t.F_AskLeaveId =a.F_Id and t.F_WFState =3)) else a.F_AskLeaveTime end
F_AskLeaveTime,a.F_WFState  
                             from  [dbo].[HR_AskLeave] a
							left join [HR_FormalEmployees] b on  a.F_UserId =b.F_Id
							left join [dbo].[Base_Department]c on b.F_DepartmentId=c.Id
                             where (a. F_WFState<>4 or a.F_WFState is null) and a.F_StartTime>=@beginDate and F_EndTime<=@endDate";
            if (input.isTerminate.HasValue)
            {
                sql += " and  a.F_IsTerminate =" + input.isTerminate;
            }
            sql += " ) dat where 1=1 and  dat.F_AskLeaveTime>0 ";
            var dt = Db.GetDataTableWithSql(sql, ("@beginDate", input.beginDate.ToString("yyyy-MM-dd").ToDateTime()), ("@endDate", input.endDate.AddDays(1).ToString("yyyy-MM-dd").ToDateTime()));

            return dt;
        }
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_AskLeave>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_AskLeave, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            if (search.selectIds != null && search.selectIds.Count > 0)
            {
                where = where.And(x => search.selectIds.Contains(x.F_Id));
            }
            where = where.AndIf(!search.EmpId.IsNullOrEmpty(), x => x.F_UserId == search.EmpId);

            return q.Where(where).OrderBy(input.SortField, input.SortType).ToDataTable();
        }

        /// <summary>
        /// 流程回调方法
        /// </summary>
        /// <param name="input"></param>
        [FlowCallBackLog(UserLogType.请假管理, "请假（回调）")]
        public void FlowCallBack(FlowInputDTO input)
        {

            if (input == null)
            {
                throw new BusException("参数错误");
            }
            var entity = this.GetEntity(input.id);
            if (entity == null)
            {
                throw new BusException("未找到对象");
            }
            if (entity.F_WFState == (int)WFStates.取消流程)
            {
                throw new BusException("该流程已取消，不能修改状态");
            }
            if (entity.F_WFState == (int)WFStates.完成流程)
            {
                throw new BusException("该流程已完成，不能修改状态");
            }
            if (entity.F_WFState == (int)WFStates.提交生效)
            {
                throw new BusException("该流程已提交生效，不能修改状态");
            }
            entity.F_WFState = input.status;
            entity.F_ModifyDate = DateTime.Now;
            entity.F_ModifyUserId = "System";
            entity.F_ModifyUserName = "System";
            Update(entity);
            if (input.status == (int)WFStates.完成流程)
            {
                var dataNow = DateTime.Now.Date;
                //如果是法定假期则,已用额度是流程审核完成后增加
                if (entity.F_AskLeaveType == "年假")
                {
                    var hTime = dataNow;
                    var isLastFlow = false;//是否是去年的流程
                                           //时间判断:如果是本年2月15日前，选择去年日期，扣去年的法定假期，假期用完则不可提起
                    if (dataNow <= Convert.ToDateTime(dataNow.Year.ToString() + "-02-15") && entity.F_StartTime.Value.Year < dataNow.Year)
                    {

                        hTime = entity.F_StartTime.Value;
                        isLastFlow = true;
                    }
                    //修改假期额度
                    var hlEntity = Db.GetIQueryable<HR_HolidayLine>().Where(x => x.F_UserId == entity.F_UserId && x.F_EffectTime <= hTime && x.F_EndTime >= hTime && x.F_HolidayTypes.Contains("年假")).ToList();
                    if (hlEntity != null)
                    {
                        decimal leaveTime = Convert.ToDecimal(entity.F_AskLeaveTime);
                        var legalH = hlEntity.FirstOrDefault(i => i.F_HolidayTypes == "法定年假");
                        var welfare = hlEntity.FirstOrDefault(i => i.F_HolidayTypes == "福利年假");
                        if (welfare == null)
                        {
                            legalH.F_ActualAmountLast = legalH.F_ActualAmountLast + leaveTime;
                        }
                        else
                        {
                            var leH = legalH.F_UsedLine + leaveTime;
                            if (leH <= legalH.F_StandardLine)
                            {
                                legalH.F_ActualAmountLast = legalH.F_ActualAmountLast + leaveTime;
                            }
                            else
                            {
                                legalH.F_ActualAmountLast = legalH.F_ActualAmount;

                                welfare.F_ActualAmountLast = welfare.F_ActualAmountLast + ((legalH.F_ActualAmountLast + leaveTime) - legalH.F_ActualAmount);
                            }
                        }

                        Db.Update(hlEntity);
                    }
                }
                if (entity.F_AskLeaveType == "病假")
                {
                    var hlEntity = Db.GetIQueryable<HR_HolidayLine>().FirstOrDefault(x => x.F_UserId == entity.F_UserId && x.F_EffectTime <= dataNow && x.F_EndTime >= dataNow && x.F_HolidayTypes.Contains("病假"));
                    decimal leaveTime = Convert.ToDecimal(entity.F_AskLeaveTime);
                    if (hlEntity != null)
                    {
                        hlEntity.F_ActualAmountLast = hlEntity.F_ActualAmountLast + leaveTime;
                        Db.Update(hlEntity);
                    }
                }
            }

            if (input.status == (int)WFStates.取消流程)
            {
                var dataNow = DateTime.Now.Date;
                //如果是法定假期则,已用额度是流程审核完成后增加
                if (entity.F_AskLeaveType == "年假")
                {
                    //修改假期额度
                    var hlEntity = Db.GetIQueryable<HR_HolidayLine>().Where(x => x.F_UserId == entity.F_UserId && x.F_EffectTime <= dataNow && x.F_EndTime >= dataNow && x.F_HolidayTypes.Contains("年假")).ToList();
                    if (hlEntity != null)
                    {
                        decimal leaveTime = Convert.ToDecimal(entity.F_AskLeaveTime);
                        var legalH = hlEntity.FirstOrDefault(i => i.F_HolidayTypes == "法定年假");
                        var welfare = hlEntity.FirstOrDefault(i => i.F_HolidayTypes == "福利年假");
                        if (welfare == null)
                        {
                            legalH.F_ActualAmountLast = legalH.F_ActualAmountLast - leaveTime;
                        }
                        else
                        {
                            var leH = legalH.F_UsedLine + leaveTime;
                            if (leH <= legalH.F_StandardLine)
                            {
                                legalH.F_ActualAmountLast = legalH.F_ActualAmountLast - leaveTime;
                            }
                            else
                            {
                                legalH.F_ActualAmountLast = legalH.F_ActualAmount;

                                welfare.F_ActualAmountLast = welfare.F_ActualAmountLast + ((legalH.F_ActualAmountLast - leaveTime) - legalH.F_ActualAmount);
                            }
                        }

                        Db.Update(hlEntity);
                    }
                }
                if (entity.F_AskLeaveType == "病假")
                {
                    var hlEntity = Db.GetIQueryable<HR_HolidayLine>().FirstOrDefault(x => x.F_UserId == entity.F_UserId && x.F_EffectTime <= dataNow && x.F_EndTime >= dataNow && x.F_HolidayTypes.Contains("病假"));
                    decimal leaveTime = Convert.ToDecimal(entity.F_AskLeaveTime);
                    if (hlEntity != null)
                    {
                        hlEntity.F_ActualAmountLast = hlEntity.F_ActualAmountLast - leaveTime;
                        Db.Update(hlEntity);
                    }
                }
            }
        }

        /// <summary>
        /// 获得假期台账报表
        /// </summary>
        /// <param name="reportCondition"></param>
        /// <returns></returns>
        public PageResult<HolidayLedgerDTO> GetHolidayLedger(PageInput<ReportConditionDTO> input)
        {
            if (input == null)
            {
                throw new BusException("参数错误");
            }
            if (!input.Search.year.HasValue)
            {
                throw new BusException("年份不能为空");
            }
            var search = input.Search;
            List<string> relStatus = new List<string>()
                {
                    "派驻",
                    "正式员工",
                    "试用员工",
                    "第三方用工",
                    "第三方员工",
                    "试用员工（延期转正）",
                    "正式员工（校招）",
                    "正式员工（销售）",
                    "顾问"
                };
            PageResult<HolidayLedgerDTO> returnList = new PageResult<HolidayLedgerDTO>();

            Expression<Func<HR_FormalEmployees, Base_Department, HR_FormalEmployeesDetailsDTO>> select = (e, d) => new HR_FormalEmployeesDetailsDTO
            {
                DepartmentName = d.Name,
                IdCardNumber = e.IdCardNumber
            };
            select = select.BuildExtendSelectExpre();
            //获取假期额度员工ID
            var userIds = Db.GetIQueryable<HR_HolidayLine>().Where(x => x.F_EffectTime.Value.Year == search.year.Value.Year).Select(x => x.F_UserId).Distinct();

            var q = from e in Db.GetIQueryable<HR_FormalEmployees>().Where(i => relStatus.Contains(i.EmployRelStatus)).AsExpandable()
                    join d in Db.GetIQueryable<Base_Department>() on e.F_DepartmentId equals d.Id into dept
                    from d in dept.DefaultIfEmpty()
                    where userIds.Contains(e.F_Id)
                    select @select.Invoke(e, d);
            var where = LinqHelper.True<HR_FormalEmployeesDetailsDTO>();
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_FormalEmployeesDetailsDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            var result = q.Where(where).GetPageResult(input);

            returnList.ErrorCode = result.ErrorCode;
            returnList.Msg = result.Msg;
            returnList.Success = result.Success;
            returnList.Total = result.Total;
            returnList.Data = new List<HolidayLedgerDTO>();

            if (result.Data != null && result.Data.Count > 0)
            {
                var empIds = result.Data.Select(x => x.F_Id);
                //获取假期额度
                var jqList = Db.GetIQueryable<HR_HolidayLine>().Where(x => empIds.Contains(x.F_UserId) && x.F_EffectTime.Value.Year == search.year.Value.Year).ToList();
                //获取请假数据
                var askList = Db.GetIQueryable<HR_AskLeave>().Where(x => empIds.Contains(x.F_UserId) && x.F_WFState == (int)WFStates.完成流程 && x.F_StartTime.Value.Year == search.year.Value.Year).ToList();
                var askIds = askList.Select(x => x.F_Id);
                //获取销假数据
                var terList = Db.GetIQueryable<HR_TermLeave>().Where(x => askIds.Contains(x.F_AskLeaveId) && x.F_WFState == (int)WFStates.完成流程).ToList();

                foreach (var item in result.Data)
                {
                    //法定年假
                    decimal StateAnnualLeave = jqList.Where(x => x.F_HolidayTypes == "法定年假" && x.F_UserId == item.F_Id).Sum(x => x.F_ActualAmount) ?? 0;
                    //已休法定年假天数
                    decimal StateAnnualLeaveDayed = jqList.Where(x => x.F_HolidayTypes == "法定年假" && x.F_UserId == item.F_Id).Sum(x => x.F_UsedLine) ?? 0;
                    //福利年假
                    decimal ComAnnualLeave = jqList.Where(x => x.F_HolidayTypes == "福利年假" && x.F_UserId == item.F_Id).Sum(x => x.F_ActualAmount) ?? 0;
                    //已休福利年假天数
                    decimal ComAnnualLeaveDayed = jqList.Where(x => x.F_HolidayTypes == "福利年假" && x.F_UserId == item.F_Id).Sum(x => x.F_UsedLine) ?? 0;
                    //本年病假天数
                    decimal SickLeaveDay = jqList.Where(x => x.F_HolidayTypes == "病假" && x.F_UserId == item.F_Id).Sum(x => x.F_ActualAmount) ?? 0;
                    //病假销假
                    var bjxjQuery = from t in terList
                                    join a in askList on t.F_AskLeaveId equals a.F_Id
                                    where t.F_UserId == item.F_Id && a.F_AskLeaveType == "病假" && a.F_IsTerminate == 1
                                    select t.F_RealAskLeaveTime;

                    //已休病假天数
                    decimal SickLeaveDayed = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "病假" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + bjxjQuery.Sum() ?? 0;
                    //本年产假天数
                    decimal MaternityLeaveDay = jqList.Where(x => x.F_HolidayTypes == "产假" && x.F_UserId == item.F_Id).Sum(x => x.F_ActualAmount) ?? 0;
                    //产假销假
                    var cjxjQuery = from t in terList
                                    join a in askList on t.F_AskLeaveId equals a.F_Id
                                    where t.F_UserId == item.F_Id && a.F_AskLeaveType == "产假" && a.F_IsTerminate == 1
                                    select t.F_RealAskLeaveTime;
                    //已休产假天数
                    decimal MaternityLeaveDayed = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "产假" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + cjxjQuery.Sum() ?? 0;
                    //本年丧假天数
                    decimal FuneralLeaveDay = jqList.Where(x => x.F_HolidayTypes == "丧假" && x.F_UserId == item.F_Id).Sum(x => x.F_ActualAmount) ?? 0;
                    //丧假销假
                    var sjxjQuery = from t in terList
                                    join a in askList on t.F_AskLeaveId equals a.F_Id
                                    where t.F_UserId == item.F_Id && a.F_AskLeaveType == "丧假" && a.F_IsTerminate == 1
                                    select t.F_RealAskLeaveTime;
                    //已休丧假天数
                    decimal FuneralLeaveDayed = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "丧假" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + sjxjQuery.Sum() ?? 0;
                    //本年婚假天数
                    decimal MarriageLeaveDay = jqList.Where(x => x.F_HolidayTypes == "婚假" && x.F_UserId == item.F_Id).Sum(x => x.F_ActualAmount) ?? 0;
                    //婚假销假
                    var hjxjQuery = from t in terList
                                    join a in askList on t.F_AskLeaveId equals a.F_Id
                                    where t.F_UserId == item.F_Id && a.F_AskLeaveType == "婚假" && a.F_IsTerminate == 1
                                    select t.F_RealAskLeaveTime;
                    //已休婚假天数
                    decimal MarriageLeaveDayed = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "婚假" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + sjxjQuery.Sum() ?? 0;
                    //事假销假
                    var shijxjQuery = from t in terList
                                      join a in askList on t.F_AskLeaveId equals a.F_Id
                                      where t.F_UserId == item.F_Id && a.F_AskLeaveType == "事假" && a.F_IsTerminate == 1
                                      select t.F_RealAskLeaveTime;
                    //已休事假天数
                    decimal Leave = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "事假" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + shijxjQuery.Sum() ?? 0;
                    //陪产假销假
                    var pcjxjQuery = from t in terList
                                     join a in askList on t.F_AskLeaveId equals a.F_Id
                                     where t.F_UserId == item.F_Id && a.F_AskLeaveType == "陪产假" && a.F_IsTerminate == 1
                                     select t.F_RealAskLeaveTime;
                    //已休陪产假天数
                    decimal PaternityLeave = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "陪产假" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + pcjxjQuery.Sum() ?? 0;
                    //会务培训其他假期销假
                    var ojxjQuery = from t in terList
                                    join a in askList on t.F_AskLeaveId equals a.F_Id
                                    where t.F_UserId == item.F_Id && a.F_AskLeaveType == "会务/培训/其他假期" && a.F_IsTerminate == 1
                                    select t.F_RealAskLeaveTime;
                    //会务培训其他假期
                    decimal OtherHolidays = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "会务/培训/其他假期" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + ojxjQuery.Sum() ?? 0;

                    //工作年限
                    string workYear = string.Empty;
                    if (item.F_StartWorkTime.HasValue)  //如果开始工作时间有值，(当前时间-开始工作时间)/365向上取整
                    {
                        workYear = Math.Ceiling((DateTime.Now - item.F_StartWorkTime.Value).TotalDays / 365) + "年";
                    }
                    else if (item.F_InTime.HasValue)  //如果入职时间有值，(当前时间-入职时间)/365向上取整
                    {
                        workYear = Math.Ceiling((DateTime.Now - item.F_InTime.Value).TotalDays / 365) + "年";
                    }

                    HolidayLedgerDTO model = new HolidayLedgerDTO()
                    {
                        F_Id = item.F_Id,
                        DepartmentName = item.DepartmentName,
                        WorkYear = workYear,
                        EntryDate = item.F_InTime,
                        Name = item.NameUser,
                        StateAnnualLeave = NumFilter(StateAnnualLeave),
                        ComAnnualLeave = NumFilter(ComAnnualLeave),
                        StateAnnualLeaveDay = NumFilter(StateAnnualLeave - StateAnnualLeaveDayed),
                        StateAnnualLeaveDayed = NumFilter(StateAnnualLeaveDayed),
                        ComAnnualLeaveDayed = NumFilter(ComAnnualLeaveDayed),
                        RemAnnualLeaveDay = NumFilter(StateAnnualLeave + ComAnnualLeave - StateAnnualLeaveDayed - ComAnnualLeaveDayed),
                        SickLeaveDay = NumFilter(SickLeaveDay),
                        SickLeaveDayed = NumFilter(SickLeaveDayed),
                        RemSickLeaveDay = NumFilter(SickLeaveDay - SickLeaveDayed),
                        MaternityLeaveDay = NumFilter(MaternityLeaveDay),
                        MaternityLeaveDayed = NumFilter(MaternityLeaveDayed),
                        RemMaternityLeaveDay = NumFilter(MaternityLeaveDay - MaternityLeaveDayed),
                        FuneralLeaveDay = NumFilter(FuneralLeaveDay),
                        FuneralLeaveDayed = NumFilter(FuneralLeaveDayed),
                        RemFuneralLeaveDay = NumFilter(FuneralLeaveDay - FuneralLeaveDayed),
                        MarriageLeaveDay = NumFilter(MarriageLeaveDay),
                        MarriageLeaveDayed = NumFilter(MarriageLeaveDayed),
                        RemMarriageLeaveDay = NumFilter(MarriageLeaveDay - MarriageLeaveDayed),
                        Leave = Leave,
                        PaternityLeave = PaternityLeave,
                        Absenteeism = 0,
                        OtherHolidays = OtherHolidays
                    };
                    model.RemStateAnnualLeaveDayed = model.StateAnnualLeave - model.StateAnnualLeaveDayed;
                    model.RemComAnnualLeaveDayed = model.ComAnnualLeave - model.ComAnnualLeaveDayed;
                    returnList.Data.Add(model);
                }
            }

            return returnList;
        }

        /// <summary>
        /// 排查节假日
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public List<HR_Calendar> GetCalender(DateTime startTime, DateTime endTime)
        {
            return Db.GetIQueryable<HR_Calendar>().Where(i => i.F_StartTime.Value.Date >= startTime && i.F_StartTime.Value.Date <= endTime).ToList();

        }
        #endregion

        #region 私有成员

        /// <summary>
        /// 如果小于0返回0
        /// </summary>
        /// <param name="num"></param>
        /// <returns></returns>
        private decimal NumFilter(decimal num)
        {
            decimal ret = 0;

            if (num > 0)
            {
                ret = num;
            }

            return ret;
        }

        #endregion

    }
}