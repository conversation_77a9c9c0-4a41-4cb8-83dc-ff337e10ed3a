﻿using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System;
using Coldairarrow.Util.DataAccess;

namespace Coldairarrow.Business.Base_Manage
{
    public class Base_MGIpLockBusiness : BaseBusiness<Base_MGIpLock>, IBase_MGIpLockBusiness, ITransientDependency
    {
        public Base_MGIpLockBusiness(IHWDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<Base_MGIpLock>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Base_MGIpLock>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Base_MGIpLock, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Base_MGIpLock> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(Base_MGIpLock data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Base_MGIpLock data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Base_MGIpLock>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Base_MGIpLock, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<Base_MGIpLock, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        /// <summary>
        /// 批量新增ip
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> SaveBathLockIpAsync(string ips)
        {
            var ipList = ips.Split(',')
                            .Select(ip => ip.Trim())
                            .Distinct()
                            .ToList();
            try
            {
                // 查询现有的IP和白名单IP
                var existingIps = await GetIQueryable().Select(x => x.F_IP).ToListAsync();
                var whiteIps = await Db.GetIQueryable<Base_HWWhiteIp>().Select(x => x.F_Ip).ToListAsync();

                // 筛选出需要新增的IP
                var newlyLockedIps = ipList.Except(existingIps).Except(whiteIps).ToList();

                if (newlyLockedIps.Any())
                {
                    // 创建一个异步操作列表
                    var ipLockTasks = newlyLockedIps.Select(async ip =>
                    {
                        var ipLock = new Base_MGIpLock()
                        {
                            F_Id = GuidHelper.GenerateKey(),
                            F_CreateDate = DateTime.Now,
                            F_IP = ip,
                            F_IsLock = 0,
                            F_LockTime = null,
                            F_System = "手动上传封禁"
                        };
                        return ipLock;
                    }).ToList();
                    var newIpLocks = await Task.WhenAll(ipLockTasks);

                    // 执行批量插入
                    this.Db.BulkInsert(newIpLocks.ToList());
                }

                return newlyLockedIps;
            }
            catch (Exception ex)
            {
                throw new Exception("手动上传封禁失败！", ex);
            }
        }
        #endregion

        #region 私有成员

        #endregion
    }
}