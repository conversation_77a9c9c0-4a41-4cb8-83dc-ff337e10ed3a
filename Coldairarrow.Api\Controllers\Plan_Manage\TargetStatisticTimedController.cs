﻿

using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Coldairarrow.Business.Plan_Manage;
using Coldairarrow.Entity.Plan_Manage;

namespace Coldairarrow.Api.Controllers.Plan_Manage
{
    /// <summary>
    /// 运营作战室-营销数据
    /// </summary>
    [Route("/Plan_Manage/[controller]/[action]")]
    public class TargetStatisticTimedController : BaseApiController
    {
        #region DI

        public TargetStatisticTimedController(ITargetStatisticTimedBusiness a01_TargetStatisticTimedBus)
        {
            _a01_TargetStatisticTimedBus = a01_TargetStatisticTimedBus;
        }

        ITargetStatisticTimedBusiness _a01_TargetStatisticTimedBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<TargetStatisticTimed>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _a01_TargetStatisticTimedBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<TargetStatisticTimed> GetTheData(IdInputDTO input)
        {
            return await _a01_TargetStatisticTimedBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(TargetStatisticTimed data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _a01_TargetStatisticTimedBus.AddDataAsync(data);
            }
            else
            {
                await _a01_TargetStatisticTimedBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _a01_TargetStatisticTimedBus.DeleteDataAsync(ids);
        }

        #endregion

        #region 营销数据总览
        /// <summary>
        /// 获取营销数据总览
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<TargetStatisticTimedDTO> GetTargetStatistic(DateInputDTO dateInputDTO)
        {
           return await _a01_TargetStatisticTimedBus.GetTargetStatistic(dateInputDTO.Date);
        }
        /// <summary>
        /// 获取营销数据总览
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<List<ProjectMarketTodayDTO>> GetProjectMarketToday(DateInputDTO dateInputDTO)
        {
            return await _a01_TargetStatisticTimedBus.GetProjectMarketToday(dateInputDTO.Date);
        }
        /// <summary>
        /// 获取营销趋势
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<List<TargetStatisticTimed>> GetTargetTrend(YunYingInput Input)
        {
            return await _a01_TargetStatisticTimedBus.GetTargetTrend(Input);
        }
        ///// <summary>
        ///// 获取营销数据总览
        ///// </summary>
        ///// <returns></returns>
        //[HttpPost]
        //public async Task<List<ProjectMarketTodayDTO>> GetProjectMarketToday()
        //{
        //    return await _a01_TargetStatisticTimedBus.GetProjectMarketToday();
        //}
        #endregion

    }
}