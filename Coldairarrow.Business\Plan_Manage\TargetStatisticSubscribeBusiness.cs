﻿using Coldairarrow.Entity.Plan_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Util.DataAccess;

namespace Coldairarrow.Business.Plan_Manage
{
    public class TargetStatisticSubscribeBusiness : BaseBusiness<TargetStatisticSubscribe>, ITargetStatisticSubscribeBusiness, ITransientDependency
    {
        public TargetStatisticSubscribeBusiness(IERPDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<TargetStatisticSubscribe>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<TargetStatisticSubscribe>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<TargetStatisticSubscribe, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<TargetStatisticSubscribe> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(TargetStatisticSubscribe data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(TargetStatisticSubscribe data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<TargetStatisticSubscribe>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<TargetStatisticSubscribe, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<A01_TargetStatisticSubscribe, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 运营驾驶舱数据
        public async Task<TargetStatisticSubscribeDTO> GetTargetStatisticSubscribe()
        {
            TargetStatisticSubscribeDTO targetStatistic = new TargetStatisticSubscribeDTO();
            //获取各项目得数据
            targetStatistic.projectDatas = await this.Db.GetListBySqlAsync<TargetStatisticSubscribe>(@"SELECT TOP 3 *
  FROM [dbo].[A01_TargetStatisticSubscribe]
WHERE ID IN (
 SELECT * FROM ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSubscribe] WHERE TeamProjGUID =N'8baf9a96-1647-43cd-9bcd-067448bd10c9' ORDER BY ID DESC) t1
  UNION ALL 
 SELECT * FROM  ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSubscribe] WHERE TeamProjGUID =N'1980EA50-A241-4096-8057-18A97E605DA9' ORDER BY ID DESC) t2
    UNION ALL
  SELECT * FROM  (  SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSubscribe] WHERE TeamProjGUID =N'DACC9453-86DA-4D09-BA92-621B0EC33967' ORDER BY ID DESC) t3)");
            //获取全项目得数据
            var targets = await this.Db.GetListBySqlAsync<TargetStatisticSubscribe>(@"SELECT  max(CreateDate) CreateDate,
sum([认购未签面积])  [认购未签面积]
      ,sum([认购未签金额]) [认购未签金额]
      ,sum([认购未签套数]) [认购未签套数]
      ,sum([认购未签面积商业]) [认购未签面积商业]
      ,sum([认购未签金额商业]) [认购未签金额商业]
      ,sum([认购未签套数商业]) [认购未签套数商业]
      ,sum([认购未签面积住宅]) [认购未签面积住宅]
      ,sum([认购未签金额住宅]) [认购未签金额住宅]
      ,sum([认购未签套数住宅]) [认购未签套数住宅]
      ,sum([认购未签面积车]) [认购未签面积车]
      ,sum([认购未签金额车]) [认购未签金额车]
      ,sum([认购未签套数车]) [认购未签套数车]
      ,sum([认购未签面积写字楼]) [认购未签面积写字楼]
      ,sum([认购未签金额写字楼]) [认购未签金额写字楼]
      ,sum([认购未签套数写字楼]) [认购未签套数写字楼]
      ,sum([一次性付款应收]) [一次性付款应收]
      ,sum([分期付款应收]) [分期付款应收]
      ,sum([银行按揭应收]) [银行按揭应收]
      ,sum([公积金应收]) [公积金应收]
      ,sum([逾期应收]) [逾期应收]
      ,sum([未逾期应收]) [未逾期应收]
      ,sum([一次性付款应收30]) [一次性付款应收30]
      ,sum([分期付款应收30]) [分期付款应收30]
      ,sum([银行按揭应收30]) [银行按揭应收30]
      ,sum([公积金应收30]) [公积金应收30]
      ,sum([一次性付款应收90]) [一次性付款应收90]
      ,sum([分期付款应收90]) [分期付款应收90]
      ,sum([银行按揭应收90]) [银行按揭应收90]
      ,sum([公积金应收90]) [公积金应收90]
      ,sum([一次性付款应收180]) [一次性付款应收180]
      ,sum([分期付款应收180]) [分期付款应收180]
      ,sum([银行按揭应收180]) [银行按揭应收180]
      ,sum([公积金应收180])  [公积金应收180]
      ,sum([一次性付款应收0]) [一次性付款应收0]
      ,sum([分期付款应收0]) [分期付款应收0]
      ,sum([银行按揭应收0]) [银行按揭应收0]
      ,sum([公积金应收0]) [公积金应收0]
  FROM [dbo].[A01_TargetStatisticSubscribe]
WHERE ID IN (
 SELECT * FROM ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSubscribe] WHERE TeamProjGUID =N'8baf9a96-1647-43cd-9bcd-067448bd10c9' ORDER BY ID DESC) t1
  UNION ALL 
 SELECT * FROM  ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSubscribe] WHERE TeamProjGUID =N'1980EA50-A241-4096-8057-18A97E605DA9' ORDER BY ID DESC) t2
    UNION ALL
  SELECT * FROM  (  SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSubscribe] WHERE TeamProjGUID =N'DACC9453-86DA-4D09-BA92-621B0EC33967' ORDER BY ID DESC) t3)");
            targetStatistic.allProjectData = targets.FirstOrDefault();
            return targetStatistic;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}