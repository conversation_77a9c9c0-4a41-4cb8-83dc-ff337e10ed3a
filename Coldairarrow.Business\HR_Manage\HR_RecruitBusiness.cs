﻿using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_RecruitBusiness : BaseBusiness<HR_Recruit>, IHR_RecruitBusiness, ITransientDependency
    {
        public HR_RecruitBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_Recruit>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            if (!string.IsNullOrEmpty(input.Keyword))
            {
                q = q.Where(i => i.F_BusState.Value == Convert.ToInt32(input.Keyword));
            }
            var where = LinqHelper.True<HR_Recruit>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_Recruit, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_Recruit, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<List<HR_Recruit>> GetListAsync(bool hot)
        {
            var dateNow = DateTime.Now;

            //招聘计划不用添加在这里
            //var phanRIds = Db.GetIQueryable<HR_RecruitPlan>().Where(i => i.F_StartTime <= dateNow && i.F_EndTime >= dateNow).Select(s => s.F_RecruitId).Distinct();
            //.Where(i => phanRIds.Contains(i.F_Id))

            //var q = GetIQueryable().Where(i => i.F_Start <= dateNow && i.F_End >= dateNow && i.F_BusState==3);
            var q = GetIQueryable().Where(i => i.F_Start <= dateNow && i.F_End >= dateNow);
            //if (hot)
            //{
            //    entity =   entity.Where(i => i.F_Hot.Value).ToList();
            //}
            //var postIds = entity.Select(s => s.F_Role).Distinct();
            //var postEntity = Db.GetIQueryable<Base_Post>().Where(i => postIds.Contains(i.F_Id)).ToList();

            //var temp =  (from a in entity
            //                  join b in postEntity on a.F_Role equals b.F_Id into abview
            //                  from ab in abview.DefaultIfEmpty()
            //                  select new HR_Recruit()
            //                  {
            //                      F_Id = a.F_Id,
            //                      F_RecruitName = a.F_RecruitName,
            //                      F_Role = a.F_Role,
            //                      F_Start = a.F_Start,
            //                      F_Salary = a.F_Salary,
            //                      F_City = a.F_City,
            //                      F_Education = a.F_Education,
            //                      F_Experience = a.F_Experience,
            //                      F_Type = a.F_Type,
            //                      RoleName= ab.F_Name,
            //                  }).ToList();
            //return temp;
            if (hot)
                return await q.Where(p => p.F_Hot == true).Select(p => new HR_Recruit() { F_Id = p.F_Id, F_RecruitName = p.F_RecruitName, F_Role = p.F_Role, F_Start = p.F_Start, F_Salary = p.F_Salary, F_City = p.F_City, F_Education = p.F_Education, F_Experience = p.F_Experience, F_Type = p.F_Type }).ToListAsync();
            else
                return await q.Select(p => new HR_Recruit() { F_Id = p.F_Id, F_RecruitName = p.F_RecruitName, F_Role = p.F_Role, F_Start = p.F_Start, F_Salary = p.F_Salary, F_City = p.F_City, F_Education = p.F_Education, F_Experience = p.F_Experience, F_Type = p.F_Type }).ToListAsync();
        }

        public async Task<HR_Recruit> GetTheDataAsync(string id, int isPost = 0)
        {
            var model = new HR_Recruit();
            if (isPost == 1)
            {
                model = Db.GetIQueryable<HR_Recruit>().FirstOrDefault(i => i.F_Role == id);
            }
            else
            {
                model = await GetEntityAsync(id);
            }
            //model.RoleName = Db.GetIQueryable<Base_Post>().FirstOrDefault(i => i.F_Id == model.F_Role)?.F_Name;
            return model;
        }
        /// <summary>
        /// 判断该岗位是否存在
        /// </summary>
        /// <param name="postId"></param>
        /// <returns></returns>
        public async Task<HR_Recruit> GetPostEntity(string postId)
        {
           var hR_Recruit= await this.Db.GetIQueryable<HR_Recruit>()
                .AsNoTracking().FirstOrDefaultAsync(i => i.F_Role == postId);
            return hR_Recruit;
        }

        [DataAddLog(UserLogType.招聘管理, "F_RecruitName", "招聘信息")]
        public async Task AddDataAsync(HR_Recruit data)
        {
            await InsertAsync(data);
        }

        [DataEditLog(UserLogType.招聘管理, "F_RecruitName", "招聘信息")]
        public async Task UpdateDataAsync(HR_Recruit data)
        {
            await UpdateAsync(data);
        }
        [DataDeleteJsonLog(UserLogType.招聘管理, "F_RecruitName", "招聘信息")]
        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }
        [DataEditJsonLog(UserLogType.招聘管理, "F_RecruitName", "招聘信息延迟一个月")]
        public async Task handleDelay(string id)
        {
            var model = GetEntity(id);
            //model.F_Start = model.F_Start.Value.AddMonths(1);
            model.F_End = model.F_End.Value.AddMonths(1);
            await UpdateAsync(model);
        }
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_Recruit>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_Recruit, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_Recruit, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        /// <summary>
        /// 获取数据
        /// </summary>
        /// <param name="id"></param>
        /// <param name="type">1、招聘信息 2、招聘计划 3、正在招聘 4、过期招聘</param>
        /// <returns></returns>
        public HRRecruit GetListData()
        {
            HRRecruit hRRecruit = new HRRecruit();
            var entity = Db.GetIQueryable<HR_Recruit>();
            // hRRecruit.Recruitment = entity.Where(i => i.F_BusState.IsNullOrEmpty || i.F_BusState.Value == 1);
            hRRecruit.Recruitment = entity.Where(i => !i.F_BusState.HasValue || (i.F_BusState.HasValue && i.F_BusState.Value == 1)).ToList();
            hRRecruit.RecPlan = entity.Where(i => i.F_BusState.HasValue && i.F_BusState.Value == 2).ToList();
            hRRecruit.AreLook = entity.Where(i => i.F_BusState.HasValue && i.F_BusState.Value == 3).ToList();
            hRRecruit.OverdueRec = entity.Where(i => (i.F_BusState.HasValue && i.F_BusState.Value == 4) || (i.F_End < DateTime.Now.Date)).ToList();
            return hRRecruit;
        }
        /// <summary>
        /// 编辑数据
        /// </summary>
        /// <param name="id"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public bool EditRec(string id, int type)
        {
            var model = Db.GetIQueryable<HR_Recruit>().FirstOrDefault(i => i.F_Id == id);
            if (model != null)
            {
                if (!model.F_End.HasValue || !model.F_Start.HasValue)
                {
                    model.F_Start = DateTime.Now.Date;
                    model.F_End = model.F_Start.Value.AddMonths(model.F_RecDemand.Value);
                }
                model.F_BusState = type;

                if (type == 3)
                {
                    if (model.F_End < DateTime.Now.Date)
                    {
                        model.F_Start = DateTime.Now.Date;
                        if (model.F_RecDemand.HasValue)
                        {
                            model.F_End = model.F_Start.Value.AddMonths(model.F_RecDemand.Value);
                        }
                        else
                        {
                            model.F_End = model.F_Start.Value.AddMonths(1);
                        }

                    }
                }
            }
            Db.Update(model);
            return true;
        }
        /// <summary>
        /// 获取岗位信息
        /// </summary>
        /// <returns></returns>
        public List<RecPost> RecrPost()
        {
            var sqlStr = "select c.F_Id as PostId,c.F_Name as PostName from [dbo].[HR_Recruit]a left join[dbo].[Base_Post] c on a.F_Role = c.F_Id";
            return Db.GetListBySql<RecPost>(sqlStr);
        }
        #endregion

        #region 私有成员

        #endregion
    }
}