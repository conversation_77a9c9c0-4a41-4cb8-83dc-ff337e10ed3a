﻿using Coldairarrow.Business.HR_DataDictionaryManage;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.HR_EmployeeInfoManage
{
    [Route("/HR_EmployeeInfoManage/[controller]/[action]")]
    public class HR_DepartureController : BaseApiController
    {
        #region DI

        public HR_DepartureController(IHR_DepartureBusiness hR_DepartureBus, IHR_DataDictionaryDetailsBusiness hR_DataDictionaryDetailsBus, IConfiguration configuration)
        {
            _configuration = configuration;
            _hR_DepartureBus = hR_DepartureBus;
            _hR_DataDictionaryDetailsBus = hR_DataDictionaryDetailsBus;
        }

        readonly IConfiguration _configuration;
        IHR_DepartureBusiness _hR_DepartureBus { get; }
        IHR_DataDictionaryDetailsBusiness _hR_DataDictionaryDetailsBus { get; }

        #endregion

        #region 获取

        [HttpGet]
        public AjaxResult GetInit()
        {
            var changesOperatingList = _hR_DataDictionaryDetailsBus.GetDetailList("离职变动操作", "");
            var changesTypeList = _hR_DataDictionaryDetailsBus.GetDetailList("离职变动类型", "");
            var user = this.GetOperator();
            var data = new
            {
                wfStates = EnumHelper.ToOptionList(typeof(WFStates)),
                changesOperatingList,
                changesTypeList,
                user
            };
            return Success(data);
        }
        /// <summary>
        /// 验证是否有在途流程和已完成流程
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ValidateIsDeparture(IdInputDTO input)
        {
            var res = _hR_DepartureBus.ValidateIsDeparture(input.id);
            
            return Success(res);
        }
        [HttpPost]
        public async Task<PageResult<HR_DepartureDTO>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_DepartureBus.GetDataListAsync(input);
        }
        [HttpPost]
        public async Task<PageResult<HR_DepartureDTO>> GetMyDataList(PageInput<ConditionDTO> input)
        {
            var op = GetOperator();
            input.Search.UserId = op?.UserId;
            return await _hR_DepartureBus.GetDataListAsync(input);
        }
        [HttpPost]
        public HR_DepartureDTO GetDepartureInfo(IdInputDTO input)
        {
            return _hR_DepartureBus.GetDepartureInfo(input.id);
        }


        #endregion

        #region 提交

        /// <summary>
        /// 保存并创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult SaveAndCreateFlow(HR_DepartureDTO data)
        {
            if (_hR_DepartureBus.ValidateIsDeparture(data.F_UserId))
            {
                throw new BusException("已有在途离职流程或已完成离职不能再发起");
            }
            
            var ret = _hR_DepartureBus.CreateFlow(data, _configuration["OAUrl"] + "rest/archives/", GetOperator());
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("创建流程失败");
            }
        }

        [HttpPost]
        public async Task SaveData(HR_Departure data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);
                Random ra = new Random();
                data.F_Code = DateTime.Now.ToString("yyyyMMdd") + ra.Next(100, 999);
                data.F_WFState = (int)WFStates.草稿;
                await _hR_DepartureBus.AddDataAsync(data);
            }
            else
            {
                await _hR_DepartureBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_DepartureBus.DeleteDataAsync(ids);
        }

        /// <summary>
        /// 流程回调方法
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult FlowCallBack(FlowInputDTO input)
        {
            _hR_DepartureBus.FlowCallBack(input);

            return Success();
        }

        /// <summary>
        /// 保存并创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ActWorkflow(HR_DepartureDTO data)
        {
            var ret = _hR_DepartureBus.ActWorkflow(data, _configuration["OAUrl"] + "rest/archives/", GetOperator());
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("提交流程失败");
            }
        }
        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ArchiveWorkflow(HR_DepartureDTO data)
        {
            var ret = _hR_DepartureBus.ArchiveWorkflow(data, _configuration["OAUrl"] + "rest/archives/");
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("强制归档失败");
            }
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_DepartureBus.GetExcelListAsync(input);
                if (exportTable != null && exportTable.Rows.Count > 0)
                {
                    var ChangesOperatingList = _hR_DataDictionaryDetailsBus.GetDetailList("离职变动操作", "");
                    var ChangesTypeList = _hR_DataDictionaryDetailsBus.GetDetailList("离职变动类型", "");
                    exportTable.Columns.Add("changesoperating");
                    exportTable.Columns.Add("changestype");
                    exportTable.Columns.Add("wfstate");
                    exportTable.Columns.Add("gender");
                    //exportTable.Columns.Add("starttime");
                    for (int i = 0; i < exportTable.Rows.Count; i++)
                    {
                        var sex = exportTable.Rows[i]["Sex"];
                        if (sex != null && !sex.ToString().IsNullOrEmpty())
                        {
                            var gender = sex.ToString() == "0" ? "女" : "男";
                            exportTable.Rows[i]["gender"] = gender;
                        }
                        var changesoperating = exportTable.Rows[i]["F_ChangesOperating"];
                        if (changesoperating != null && !changesoperating.ToString().IsNullOrEmpty())
                        {
                            var changesoperatingStr = ChangesOperatingList.FirstOrDefault(x => x.F_ItemValue == changesoperating.ToString())?.F_ItemName;
                            exportTable.Rows[i]["changesoperating"] = changesoperatingStr;
                        }
                        var changestype = exportTable.Rows[i]["F_ChangesType"];
                        if (changestype != null && !changestype.ToString().IsNullOrEmpty())
                        {
                            var askTypeStr = ChangesTypeList.FirstOrDefault(x => x.F_ItemValue == changestype.ToString())?.F_ItemName;
                            exportTable.Rows[i]["changestype"] = askTypeStr;
                        }
                        var wfstate = exportTable.Rows[i]["F_WFState"];
                        if (wfstate != null && !wfstate.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["wfstate"] = Enum.Parse(typeof(WFStates), wfstate.ToString()).ToString();
                        }
                    }
                }
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "离职";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                int sort = 0;
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "empcode", ExcelColumn = "员工编码", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "empname", ExcelColumn = "姓名", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "gender", ExcelColumn = "性别", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "idcardnumber", ExcelColumn = "身份证号码", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_originalemplstatus", ExcelColumn = "原用工状态", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_mobilzoriemplstatus", ExcelColumn = "目标用工状态", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "changesoperating", ExcelColumn = "变动操作", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "changestype", ExcelColumn = "变动类型", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_departuredate", ExcelColumn = "离职生效时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "wfstate", ExcelColumn = "流程状态", Alignment = "left", Sort = sort++ });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

    }
}