﻿using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.Serivces;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using <EMAIL>;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Base_Manage
{
    [Route("/Base_Manage/[controller]/[action]")]
    public class ZhaozhiHomeController : BaseApiController
    {

        /// <summary>
        /// 创建Access_token类
        /// </summary>

        public class Access_token
        {
            public string access_token { get; set; }
            public string expires_in { get; set; }

        }

        /// <summary>
        /// ACCESS_TOKEN最后一次更新时间,初始值为当前2小时前
        /// </summary>
        static DateTime _lastGetTimeOfAccessToken = DateTime.Now.AddSeconds(-7200);

        /// <summary>
        /// 存储微信访问凭证
        /// </summary>
        static string _AccessToken;
        /// <summary>
        /// 获取微信访问凭证
        /// </summary>
        [NoCheckJWT]
        [HttpPost]
        public string GetAccessToken()
        {
            try
            {
                var time = _lastGetTimeOfAccessToken.AddSeconds(7199);
                if (time <= DateTime.Now)
                {
                    //当前时间比上次获取时间超了7200s后执行
                    _lastGetTimeOfAccessToken = DateTime.Now;
                    string appid = "wx9f95191bf3693a2b"; //微信公众号appid
                    string secret = "cd7e673fc1284f7e2662c22418db37f1";  //微信公众号appsecret
                    string strUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appid + "&secret=" + secret;
                    /*
                        API：http://qydev.weixin.qq.com/wiki/index.php?title=%E4%B8%BB%E5%8A%A8%E8%B0%83%E7%94%A8#.E8.8E.B7.E5.8F.96AccessToken
                        正确的Json返回示例:
                        {
                           "access_token": "accesstoken000001",
                           "expires_in": 7200
                        }
                        错误的Json返回示例:
                        {
                           "errcode": 43003,
                           "errmsg": "require https"
                        }
                    */
                    HttpWebRequest req = (HttpWebRequest)HttpWebRequest.Create(strUrl);  //用GET形式请求指定的地址
                    req.Method = "GET";
                    using (WebResponse wr = req.GetResponse())
                    {
                        //HttpWebResponse myResponse = (HttpWebResponse)req.GetResponse();  
                        StreamReader reader = new StreamReader(wr.GetResponseStream(), Encoding.UTF8);
                        string content = reader.ReadToEnd();
                        reader.Close();
                        reader.Dispose();
                        //在这里对Access_token 赋值  

                        Access_token token = new Access_token();
                        token = JsonConvert.DeserializeObject<Access_token>(content);
                        _AccessToken = token.access_token;
                    }

                }
                return _AccessToken;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 新的获取最近文章
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult getArtListNew()
        {
            try
            {
                var access_token = GetAccessToken();
                if (String.IsNullOrEmpty(access_token))
                {
                    return Success();
                }
                else
                {
                    var myUrl = $"https://api.weixin.qq.com/cgi-bin/freepublish/batchget?access_token={access_token}";
                    //string gethtml = MyHttpHelper.HttpPost(myUrl, @"{ ""type"": ""news"",""offset"": ""0"",""count"": ""5""}");
                    string gethtml = MyHttpHelper.HttpPost(myUrl, @"{ ""offset"": ""0"",""count"": ""6""}");
                    if (!gethtml.IsNullOrEmpty())
                    {
                        var str = (JObject)JsonConvert.DeserializeObject(gethtml);
                        var sss = str["item"].Root.ToString();
                        LogHelper.WriteLog_LocalTxt(sss);
                        var pm = JsonConvert.DeserializeObject<Wechat_Articlecs>(sss);
                        var list = new List<Wechat_NewItem>();
                        foreach (var i in pm.item)
                        {
                            foreach (var j in i.content.news_item)
                            {
                                var item = j;
                                var newItem = new Wechat_NewItem();
                                newItem.title = item.title;
                                newItem.author = item.author;
                                newItem.digest = item.digest;
                                newItem.url = item.url;
                                newItem.thumb_url = item.thumb_url;
                                newItem.create_time = i.content.create_time;
                                list.Add(newItem);
                            }
                        }
                        return Success(list);
                    }
                    else
                    {
                        return Success("初始化jssdk失败");
                    }
                }
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
        }

        /// <summary>
        /// 获取首页得banner图
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult getOABannerList()
        {
            try
            {
                string str = HttpHelper.GetData("https://www.cqlandmark.com/rest/file/banner/list");
                if (!str.IsNullOrEmpty())
                {
                    var bannerDto = JsonConvert.DeserializeObject<BannerDto>(str);
                    if (bannerDto.success)
                    {
                        return Success(bannerDto.datas);
                    }
                }
                return Success();
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
        }

    }

    public class BannerDto
    {
        public bool success { get; set; }
        public List<string> datas { get; set; }
    }
}
