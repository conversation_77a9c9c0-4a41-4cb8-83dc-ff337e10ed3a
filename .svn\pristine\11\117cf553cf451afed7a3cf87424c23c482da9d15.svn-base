﻿<template>
  <a-modal :title="title" width="80%" :visible="visible" :confirmLoading="loading" @ok="handleSubmit"
    @cancel="()=>{this.visible=false}">
    <a-spin :spinning="loading">
      <a-alert message="这是发送机器人消息界面" type="info" show-icon />
      <a-form-model ref="form" :model="entity" :rules="rules" v-bind="layout">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="选择机器人" prop="F_Id">
              <SelectDiction ref="selectDiction" :Name="'企业微信机器人'" :Disabled="isEdit" @selectedvalue="selectRobot"
                :Value="entity.F_Robot"></SelectDiction>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="选择发送类型" prop="F_CreateDate">
              <SelectDiction ref="selectDiction" :Name="'机器人消息类别'" :Disabled="isEdit" @selectedvalue="selectMessageType"
                :Value="entity.F_Msgtype"></SelectDiction>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-form-model-item label="发送内容" prop="F_Content">
            <a-input v-model="entity.F_Content" autocomplete="off" type="textarea" />
          </a-form-model-item>
        </a-row>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import SelectDiction from '@/components/SelectDictionaries/DictionariesList'
export default {
  components: {
    SelectDiction,
  },
  props: {
    parentObj: Object
  },
  data () {
    return {
      layout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 18 }
      },
      visible: false,
      loading: false,
      entity: {},
      rules: {},
      title: ''
    }
  },
  methods: {
    init () {
      this.visible = true
      this.entity = {}
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    openForm (id, title) {
      this.init()

      if (id) {
        this.loading = true
        this.$http.post('/WorkWx_Robot/WorkWx_Logs/GetTheData', { id: id }).then(resJson => {
          this.loading = false

          this.entity = resJson.Data
        })
      }
    },
    handleSubmit () {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.loading = true
        this.$http.post('/WorkWx_Robot/WorkWx_Logs/SaveData', this.entity).then(resJson => {
          this.loading = false

          if (resJson.Success) {
            this.$message.success('操作成功!')
            this.visible = false

            this.parentObj.getDataList()
          } else {
            this.$message.error(resJson.Msg)
          }
        })
      })
    },
    //机器人选择
    selectRobot (value) {
      console.log(value)
      this.entity.F_Robot = value
    },
    //发送消息类型选择
    selectMessageType (value) {
      this.entity.F_Msgtype = value
    },
  }
}
</script>
