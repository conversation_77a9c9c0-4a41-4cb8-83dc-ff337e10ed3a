﻿using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using EFCore.Sharding;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Base_Manage
{
    [Route("/Base_Manage/[controller]/[action]")]
    public class Base_PostController : BaseApiController
    {
        #region DI

        public Base_PostController(IBase_PostBusiness base_PostBus, IDbAccessor dbAccessor)
        {
            _base_PostBus = base_PostBus;
            Db = dbAccessor;
        }

        IBase_PostBusiness _base_PostBus { get; }
        IDbAccessor Db { get; }
        #endregion

        #region 获取
        [HttpPost]
        public async Task<List<Base_Post>> GetAllList()
        {
            return await _base_PostBus.GetAllListAsync();
        }
        [HttpPost]
        [NoCheckJWT]
        public async Task<List<Base_PostOutDTO>> GetPostAllList()
        {
            var list = await _base_PostBus.GetAllListAsync();
            List<Base_PostOutDTO> postOutDTOs = new List<Base_PostOutDTO>();
            if (list.Count > 0)
            {
                foreach (var item in list)
                {
                    postOutDTOs.Add(MapHelper.Mapping<Base_PostOutDTO, Base_Post>(item));
                }
            }
            return postOutDTOs;
        }
        [HttpPost]
        public async Task<List<Base_PostTreeDTO>> GetTreeDataList(PostTreeInputDTO input)
        {
            return await _base_PostBus.GetTreeDataListAsync(input);
        }

        [HttpPost]
        public async Task<PageResult<Base_PostDTO>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _base_PostBus.GetDataListAsync(input);
        }
        [HttpPost]
        public async Task<List<Base_PostTreeDTO>> GetUserData(PostTreeInputDTO input)
        {
            var op = GetOperator();
            return await _base_PostBus.GetUserTreeDataAsync(input, op);
        }
        [HttpPost]
        public async Task<List<Base_PostTreeDTO>> GetUserDataList(PostTreeInputDTO input)
        {
            var op = GetOperator();
            return await _base_PostBus.GetUserTreeDataListAsync(input, op);
        }
        /// <summary>
        /// 设置岗位面试流程
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public List<Base_PostTreeDTO> GetPostProcessList(PostTreeInputDTO input)
        {
            var op = GetOperator();
            return _base_PostBus.GetPostProcessList(input, op);
        }
        [HttpPost]
        public Base_PostListDTO GetTheData(IdInputDTO input)
        {
            return _base_PostBus.GetFormDataAsync(input.id);
        }
        [NoCheckJWT]
        [HttpPost]
        public async Task<string> GetQualifications(IdInputDTO input)
        {
            return await _base_PostBus.GetQualifications(input.id);
        }

        [HttpPost]
        public async Task<PageResult<Base_PostDTO>> GetListPost(PageInput<ConditionDTO> input)
        {
            return await _base_PostBus.GetListPost(input);
        }

        public string GetNumber()
        {

            return _base_PostBus.GetNumber();
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Base_Post data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _base_PostBus.AddDataAsync(data);
            }
            else
            {
                await _base_PostBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task SaveModelData(Base_Post data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _base_PostBus.AddDataAsync(data);
            }
            else
            {
                await _base_PostBus.UpdateModelAsync(data);
            }
        }

        [HttpPost]
        public async Task SavePostInfoData(Base_Post data)
        {
            await _base_PostBus.UpdatePostInfoDataAsync(data);
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _base_PostBus.DeleteDataAsync(ids);
        }

        #endregion



        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _base_PostBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出岗位数据.xlsx";
                excelconfig.Title = "导出岗位数据";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_parentid", ExcelColumn = "上级岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_name", ExcelColumn = "岗位名称", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_encode", ExcelColumn = "岗位编号", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_departmentid", ExcelColumn = "部门名称", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_isdirectreports", ExcelColumn = "是否总经理直接下属", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_education", ExcelColumn = "要求学历", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_experience", ExcelColumn = "要求工作经验", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_seat", ExcelColumn = "是否办公座位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_supplies", ExcelColumn = "是否办公用品", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_computerpurch", ExcelColumn = "是否电脑采购", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_responsibility", ExcelColumn = "岗位职责", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_qualifications", ExcelColumn = "任职资格", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "excel.xls");

                return file;
            }
            catch
            {
                return null;
            }
        }
        #endregion
    }
}