﻿using Coldairarrow.Business.Wechat_Shekou;
using Coldairarrow.Entity.Wechat_Shekou;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Coldairarrow.Util.Helper;

namespace Coldairarrow.Api.Controllers.Wechat_Shekou
{
    [Route("/Wechat_Shekou/[controller]/[action]")]
    public class Shekou_CalendarController : BaseApiController
    {
        #region DI

        public Shekou_CalendarController(IShekou_CalendarBusiness shekou_CalendarBus,IShekou_CompanyUserBusiness shekou_CompanyUser)
        {
            _shekou_CalendarBus = shekou_CalendarBus;
            _shekou_CompanyUser = shekou_CompanyUser;
        }

        IShekou_CalendarBusiness _shekou_CalendarBus { get; }

        IShekou_CompanyUserBusiness _shekou_CompanyUser { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Shekou_Calendar>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _shekou_CalendarBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Shekou_Calendar> GetTheData(IdInputDTO input)
        {
            return await _shekou_CalendarBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Shekou_Calendar data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _shekou_CalendarBus.AddDataAsync(data);
            }
            else
            {
                await _shekou_CalendarBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _shekou_CalendarBus.DeleteDataAsync(ids);
        }

        #endregion
        

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _shekou_CalendarBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Shekou_Calendar>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _shekou_CalendarBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetExcel()
        {
            try
            {
                string index = @"E:\帆软\监管资金铺排计划日历-7月.xlsx";
                using (FileStream filesrc = new FileStream(index, FileMode.Open, FileAccess.Read))
                {
                    ISheet sheet = null;
                    ////获得工作簿里面的工作表
                    XSSFWorkbook workbook = new XSSFWorkbook(filesrc);
                    sheet = workbook.GetSheet("7月解禁日历");
                    //处理第1页
                    //从第4行开始处理
                    for (int r = 3; r <= sheet.LastRowNum; r=r+2)
                    {

                        IRow row = sheet.GetRow(r);
                        IRow rowContent = sheet.GetRow(r+1);
                        if (row != null)
                        {
                            for (int j=1;j<7;j++)
                            {
                                var celli = rowContent.GetCell(j);
                                var content = rowContent.GetCell(j).ToString();
                                if (!content.IsNullOrEmpty())
                                {
                                    var date = row.GetCell(j).DateCellValue;
                                    var newItem = new Shekou_Calendar()
                                    {
                                        Id = Guid.NewGuid().ToString("N"),
                                        F_Content = content,
                                        F_CreatTime = DateTime.Now,
                                        F_Isable = 1,
                                        F_Date = date
                                    };
                                    _shekou_CalendarBus.AddDataAsync(newItem).Wait();
                                };
                               
                            }
                         }                                    
                    }
                    return Success("");
                }
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
        }

        /// <summary>
        /// 发送短信
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult sendMessage()
        {
            var datetimeNow = DateTime.Today;
            var listNow = _shekou_CalendarBus.GetDataByDay(datetimeNow);
            if (!listNow.IsNullOrEmpty())
            {
                var str = datetimeNow.ToString("yyyy-MM-dd") + "计划提醒:" + listNow.F_Content;
                var userList = _shekou_CompanyUser.getUserByCall();
                //string users = string.Empty;
                //string users = string.Join(",",userList.Select(x=>x.Mobile).ToList());
                foreach (var u in userList)
                {
                     SendMessageHelper.SendSMSMsg(u.Mobile, str);
                }
              
                return Success("发送成功");
            }
            else
            {
                return Error("未查询到内容");
            }
            //var param = new
            //{
            //    Mobile = "18502343023",
            //    Content = str,
            //    Platform = "HR系统",
            //    SMSFrom = "蛇口短信"
            //};
            //var data = JsonConvert.SerializeObject(param);
            //var myUrl = $"http://finetest.cqlandmark.com/CarpApiMsg/sendMsg";
            //var myUrl = $"https://plan.cqlandmark.com/Ext_Base/Helper/SendMessage";
            //string gethtml = MyHttpHelper.HttpPost(myUrl, data);

        }

        #endregion
    }
}