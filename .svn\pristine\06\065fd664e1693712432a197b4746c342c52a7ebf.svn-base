﻿using System;
using System.Collections.Generic;
using System.Text;
using static Coldairarrow.Entity.Shop_Manage.Model.JDResultModel;

namespace Coldairarrow.Entity.Shop_Manage.Model
{
    /// <summary>
    /// 京东返回实体
    /// </summary>
    public class JDResultModel
    {
        public class SpAttr
        {
            /// <summary>
            /// 
            /// </summary>
            public string fare { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string qzcsp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string thwa { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string packType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string IsNewGoods { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string is7ToReturn { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string IsLqkt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string fxg { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string product_features { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string wltsys { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string SoldOversea { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string zywtzbs { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isPrescriptCat { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string mspd { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isYouShelfLife { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string ztsp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string wjtyd { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isOTCCat { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string yhzy { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isSamGoods { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string IsJX { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isLocalpurchase { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string IsJDMarket { get; set; }
        }

        public class ColorSizeItem
        {
            /// <summary>
            /// 
            /// </summary>
            public string size { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string color { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string specName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sizeSequence { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string specSequence { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string colorSequence { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string spec { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string skuId { get; set; }
        }

        public class SkuMark
        {
            /// <summary>
            /// 
            /// </summary>
            public string isSopWareService { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isSopJSOLTag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isPOPDistribution { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isGlobalPurchase { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isTimeMark { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isJDexpress { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isOripack { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isSopUseSelfStock { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isrecyclebag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isyy { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string presale { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isxg { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string pg { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string ispt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isSds { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string nosendWMS { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isQzcStoreStock { get; set; }
        }

        public class NewColorSizeItem
        {

            /// <summary>
            /// 
            /// </summary>
            public string color { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string imagePath { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sizeSequence { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string skuStatus { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string dist { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string specSequence { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string colorSequence { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string spec { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sequenceNo1 { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sequenceNo3 { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sequenceNo2 { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string size { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string specName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string yn { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string skuId { get; set; }
        }

        public class InfoItem
        {
            /// <summary>
            /// 
            /// </summary>
            public string skuId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string skuType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public InfoSpAttr spAttr { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string venderID { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isPop { get; set; }
            /// <summary>
            /// Member's Mark 咸味坚果混合装1.1KG 零食果干罐装
            /// </summary>
            public string skuName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string brandId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string brandName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> image { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> category { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string warestatus { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<InfoColorSizeItem> ColorSize { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> Color { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> Size { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> Spec { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public InfoSkuMark skuMark { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<InfoNewColorSizeItem> newColorSize { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string ptag { get; set; }
        }

        public class FDataExt
        {
        }

        public class BeltExt
        {
            /// <summary>
            /// 
            /// </summary>
            public string timeState { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string appEntireUrl { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string flowId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string detailImgUrl { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string timeStateNum { get; set; }
        }

        public class BeltListItem
        {
            /// <summary>
            /// 
            /// </summary>
            public string sku { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string beltUrl { get; set; }
            /// <summary>
            /// 年货节
            /// </summary>
            public string beltName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string beltBenefit { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string beltBenefitId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string beltForcast { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string beltBegin { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string beltEnd { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public BeltExt beltExt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string forecastDate { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string endDate { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string startDate { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string waistBandUrl { get; set; }
        }

        public class FData
        {
            /// <summary>
            /// 
            /// </summary>
            public FDataExt ext { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<BeltListItem> beltList { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bannerType { get; set; }
            /// <summary>
            /// 大促利益点腰带
            /// </summary>
            public string bannerName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bgImg { get; set; }
        }

        public class NewBanner
        {
            /// <summary>
            /// 
            /// </summary>
            public string enable { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string fId { get; set; }
            /// <summary>
            /// 腰带楼层
            /// </summary>
            public string floorName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public FData fData { get; set; }
        }

        public class FloatLayer
        {
            /// <summary>
            /// 
            /// </summary>
            public string Mobile_39632 { get; set; }
        }

        public class AbTest
        {
            /// <summary>
            /// 
            /// </summary>
            public FloatLayer floatLayer { get; set; }
        }

        public class Price
        {
            /// <summary>
            /// 
            /// </summary>
            public string id { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string m { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string p { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string op { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string pcp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string tpp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string tkp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sfp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string nup { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string ext { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string vdp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string fmp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string rp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sdnp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string qym { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string qynp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string secKillBanner { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string secKillAdvance { get; set; }
        }

        public class BeforeDesc
        {
            /// <summary>
            /// 
            /// </summary>
            public string color { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string fontSize { get; set; }
            /// <summary>
            /// 到手价
            /// </summary>
            public string text { get; set; }
        }


        public class Ext
        {
            /// <summary>
            /// 
            /// </summary>
            public string jdPrice { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string hideDoublePrice { get; set; }
        }

        public class PriceFloor
        {
            /// <summary>
            /// 
            /// </summary>
            public string enable { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string price { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string type { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public BeforeDesc beforeDesc { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Ext ext { get; set; }
        }

        public class NoticeFloorItem
        {
            /// <summary>
            /// 
            /// </summary>
            public string type { get; set; }
            /// <summary>
            /// 分享
            /// </summary>
            public string desc { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> icons { get; set; }
        }

        public class D
        {
            /// <summary>
            /// 
            /// </summary>
            public string venderId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string colType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string shopId { get; set; }
            /// <summary>
            /// 山姆会员商店官方旗舰店
            /// </summary>
            public string shopName { get; set; }
            /// <summary>
            /// 山姆会员商店官方旗舰店
            /// </summary>
            public string vender { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string hotLine { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string shopWebsite { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string po { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string url { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string type { get; set; }
        }

        public class Stock
        {
            /// <summary>
            /// 
            /// </summary>
            public string skuId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string realSkuId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string Drd { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string promiseResult { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isJDexpress { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public D D { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string StockState { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string rn { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string code { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string popFxgCode { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string afsCode { get; set; }
            /// <summary>
            /// 由 <span class='hl_red'>京东</span> 发货, 并提供售后服务. 
            /// </summary>
            public string serviceInfo { get; set; }
        }

        public class CommonInfo
        {
            /// <summary>
            /// 
            /// </summary>
            public string brandUrl { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string jdMini { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string baitiaoSwitch { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string dataReport { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string koSwitch { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string pyby3 { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string urgeLogin { get; set; }
        }

        public class Category
        {
            /// <summary>
            /// 
            /// </summary>
            public string categoryId { get; set; }
            /// <summary>
            /// 食品饮料
            /// </summary>
            public string categoryName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string categoryNameAlias { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string fatherCategoryId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string categoryClass { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string status { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string orderSort { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string yn { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string img { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string created { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string modified { get; set; }
        }

        public class Promov2Item
        {
            /// <summary>
            /// 
            /// </summary>
            public string id { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string hit { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string vl { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string pl { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string jl { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> pis { get; set; }
        }

        public class ResultExt
        {
            /// <summary>
            /// 
            /// </summary>
            public string noSaleFlag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string strategyFlag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isPlusLimit { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string limitUserFlag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string limitAreaFlag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string limitPeopleFlag { get; set; }
        }

        public class PlusLimitBuy
        {
            /// <summary>
            /// 
            /// </summary>
            public string resultFlag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string limitNum { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string noSaleFlag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public ResultExt resultExt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string limitTotal { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string limitEveDayNum { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string limitTotalNum { get; set; }
        }

        public class PurchaseCouponsItem
        {
            /// <summary>
            /// 
            /// </summary>
            public string userLabel { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string toUrl { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string roleId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string couponKind { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string discount { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string didget { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string couponstyle { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string batchId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string businessLabel { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string token { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string couponType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string quota { get; set; }
            /// <summary>
            /// 新人专享，仅可购买京东超市部分商品
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// 自领取后1天内有效
            /// </summary>
            public string timeDesc { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string hourcoupon { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string key { get; set; }
        }

        public class Purchase
        {
            /// <summary>
            /// 到手价
            /// </summary>
            public string purchaseDesc { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string purchasePrice { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<PurchaseCouponsItem> purchaseCoupons { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string hideDoublePrice { get; set; }
        }

        public class JdliveExplainStatus
        {
            /// <summary>
            /// 
            /// </summary>
            public string skuStatus { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string icon { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string h5Url { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string liveType { get; set; }
        }

        public class CouponsItem
        {
            /// <summary>
            /// 
            /// </summary>
            public string userLabel { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string toUrl { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string roleId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string couponKind { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string discount { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string didget { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string couponstyle { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string batchId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string businessLabel { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string token { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string couponType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string quota { get; set; }
            /// <summary>
            /// 新人专享，仅可购买京东超市部分商品
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// 自领取后1天内有效
            /// </summary>
            public string timeDesc { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string hourcoupon { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string key { get; set; }
        }

        public class Sku_info
        {
            /// <summary>
            /// 
            /// </summary>
            public string @global { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string limitCouponDesc { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sku { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string useDong { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string useJing { get; set; }
        }

        public class AvlCoupon
        {
            /// <summary>
            /// 
            /// </summary>
            public List<string> fissonCoupons { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<CouponsItem> coupons { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Sku_info sku_info { get; set; }
        }

        public class Recommendinfo
        {
        }

        public class Jxgf
        {
            /// <summary>
            /// 
            /// </summary>
            public string type { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Recommendinfo recommendinfo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> shopItemList { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string iconcode { get; set; }
        }

        public class Flag
        {
            /// <summary>
            /// 
            /// </summary>
            public string plusVender { get; set; }
        }

        public class Pricerate
        {
            /// <summary>
            /// 
            /// </summary>
            public string ratetype { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string rate { get; set; }
        }

        public class PayAddress
        {
            /// <summary>
            /// 
            /// </summary>
            public string wx_main_url { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string gray_global_url { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string main_url { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string global_url { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string gray_main_url { get; set; }
        }

        public class ShareInfo
        {
            /// <summary>
            /// Member's Mark 咸味坚果混合装1.1KG 零食果干罐装
            /// </summary>
            public string title { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string shortTitle { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bybtTitle { get; set; }
        }

        public class ForbidConfig
        {
            /// <summary>
            /// 
            /// </summary>
            public string redirectHK { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string liveDoor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string descDetailNotLinkTip { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bloodSugar { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bottomButtonTip { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string mainVideo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bybtBannerLink { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string shopSelectFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string recovery { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string businessEntry { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string shopDetail { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string couponFullReturn { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string productDetailVideo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bookIntroduction { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string footPrint { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string tryRead { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string couponFullReduction { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string rankFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string inspectReport { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string addressFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string commentReply { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string commentFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string couponGift { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string fluxStrip { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string pybyBannerLink { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string hotFitFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string openPlus { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string ldpFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string askFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string couponDiscount { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string panorama { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string commentVideo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sourceType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string multiFreight { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string descDetailNotLink { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string commonHeader { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string secneBuy { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string guccessLikeFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string adKey { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string selectService { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string markdownWarn { get; set; }
        }

        public class ChannelInfo
        {
            /// <summary>
            /// 
            /// </summary>
            public string sourceType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isShortItem { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public ForbidConfig forbidConfig { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string descDetailNotLinkTip { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bottomButtonTip { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string address { get; set; }
        }

        public class FloorForbids
        {
            /// <summary>
            /// 
            /// </summary>
            public string redirectHK { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string liveDoor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string descDetailNotLinkTip { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bloodSugar { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bottomButtonTip { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string mainVideo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bybtBannerLink { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string shopSelectFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string recovery { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string businessEntry { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string shopDetail { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string couponFullReturn { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string productDetailVideo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bookIntroduction { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string footPrint { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string tryRead { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string couponFullReduction { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string rankFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string inspectReport { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string addressFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string commentReply { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string commentFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string couponGift { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string fluxStrip { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string pybyBannerLink { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string hotFitFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string openPlus { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string ldpFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string askFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string couponDiscount { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string panorama { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string commentVideo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sourceType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string multiFreight { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string descDetailNotLink { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string commonHeader { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string secneBuy { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string guccessLikeFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string adKey { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string selectService { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string markdownWarn { get; set; }
        }

        public class Shop
        {
            /// <summary>
            /// 
            /// </summary>
            public string enable { get; set; }
            /// <summary>
            /// 店铺
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string link { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string type { get; set; }
        }

        public class AddCart
        {
            /// <summary>
            /// 
            /// </summary>
            public string enable { get; set; }
            /// <summary>
            /// 加入购物车
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string link { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string type { get; set; }
        }

        public class CustomerService
        {
            /// <summary>
            /// 
            /// </summary>
            public string enable { get; set; }
            /// <summary>
            /// 客服
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string link { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string type { get; set; }
        }

        public class Buy
        {
            /// <summary>
            /// 
            /// </summary>
            public string enable { get; set; }
            /// <summary>
            /// 立即购买
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string link { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string type { get; set; }
        }

        public class Cart
        {
            /// <summary>
            /// 
            /// </summary>
            public string enable { get; set; }
            /// <summary>
            /// 购物车
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string link { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string type { get; set; }
        }

        public class ButtonMap
        {
            /// <summary>
            /// 
            /// </summary>
            public Shop shop { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public AddCart addCart { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public CustomerService customerService { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Buy buy { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Cart cart { get; set; }
        }

        public class BottomBar
        {
            /// <summary>
            /// 正常购买
            /// </summary>
            public string sceneName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sceneType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public ButtonMap buttonMap { get; set; }
        }

        public class Left
        {
            /// <summary>
            /// 
            /// </summary>
            public string enable { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string type { get; set; }
            /// <summary>
            /// 加入购物车
            /// </summary>
            public string text { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string textColor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string subText { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string backgroundColor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string eventId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string eventUrl { get; set; }
        }

        public class Right
        {
            /// <summary>
            /// 
            /// </summary>
            public string enable { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string type { get; set; }
            /// <summary>
            /// 立即购买
            /// </summary>
            public string text { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string textColor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string subText { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string backgroundColor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string eventId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string eventUrl { get; set; }
        }

        public class Tips
        {
            /// <summary>
            /// 抱歉，此商品在所选区域暂不支持配送
            /// </summary>
            public string text { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string backgroupColor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string textColor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string amsg { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string amsgUrl { get; set; }
        }

        public class BottomNav
        {
            /// <summary>
            /// 
            /// </summary>
            public string enable { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string viewId { get; set; }
            /// <summary>
            /// 正常购买
            /// </summary>
            public string viewName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Left left { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Right right { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Tips tips { get; set; }
        }

        public class FloorSwitch
        {
            /// <summary>
            /// 
            /// </summary>
            public string floorGift { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string floorAd { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string floorRank { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string floorSpecialService { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string floorAddress { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string floorPromoComb { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string floorAboveTitleTag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string floorPrice { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string floorBanner { get; set; }
        }

        public class InfoSpAttr
        {
            /// <summary>
            /// 
            /// </summary>
            public string fare { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string qzcsp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string thwa { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string packType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string IsNewGoods { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string is7ToReturn { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string IsLqkt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string fxg { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string product_features { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string wltsys { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string SoldOversea { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string zywtzbs { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isPrescriptCat { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string mspd { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isYouShelfLife { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string ztsp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string wjtyd { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isOTCCat { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string yhzy { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isSamGoods { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string IsJX { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isLocalpurchase { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string IsJDMarket { get; set; }
        }

        public class InfoColorSizeItem
        {
            /// <summary>
            /// 
            /// </summary>
            public string size { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string color { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string specName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sizeSequence { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string specSequence { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string colorSequence { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string spec { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string skuId { get; set; }
        }

        public class InfoSkuMark
        {
            /// <summary>
            /// 
            /// </summary>
            public string isSopWareService { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isSopJSOLTag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isPOPDistribution { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isGlobalPurchase { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isTimeMark { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isJDexpress { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isOripack { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isSopUseSelfStock { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isrecyclebag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isyy { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string presale { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isxg { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string pg { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string ispt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isSds { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string nosendWMS { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isQzcStoreStock { get; set; }
        }


        public class InfoNewColorSizeItem
        {
            /// <summary>
            /// 
            /// </summary>
            public string color { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string imagePath { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sizeSequence { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string skuStatus { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string dist { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string specSequence { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string colorSequence { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string spec { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sequenceNo1 { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sequenceNo3 { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sequenceNo2 { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string size { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string specName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string yn { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string skuId { get; set; }
        }

        public class Item
        {
            /// <summary>
            /// 
            /// </summary>
            public string skuId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string skuType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public SpAttr spAttr { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string venderID { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isPop { get; set; }
            /// <summary>
            /// Member's Mark 咸味坚果混合装1.1KG 零食果干罐装
            /// </summary>
            public string skuName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string brandId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string brandName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> image { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> category { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string warestatus { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<ColorSizeItem> ColorSize { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> Color { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> Size { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> Spec { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public SkuMark skuMark { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<NewColorSizeItem> newColorSize { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string ptag { get; set; }
        }


        public class Name
        {
        }

        public class Id
        {
        }

        public class SkuPro
        {
            /// <summary>
            /// 
            /// </summary>
            public List<string> propArr { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> propName { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Name name { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Id id { get; set; }
        }

        public class Info
        {
            /// <summary>
            /// 
            /// </summary>
            public string errCode { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string retCode { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string msg { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bizRetCode { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bizMsg { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string areaId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sence { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public NewBanner newBanner { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public AbTest abTest { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Price price { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public PriceFloor priceFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<NoticeFloorItem> noticeFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Stock stock { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public CommonInfo commonInfo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> apiForbid { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Category category { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string pingou { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string ptqq { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string kanjia { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bigouinfo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string allOverImg { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string mainVideoId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string infoVideoId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string plusMemberType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string magicLevel { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string plusFlag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> chnImg { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string yuyueDraw { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string hasYuyue { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public PlusLimitBuy plusLimitBuy { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isMaskSku { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string hasSubscribe { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Purchase purchase { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string ruId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string model3DId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string wq_addr { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isFestival { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string upc { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string daojia { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isNeedEncrypt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string huanUrl { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string slowPayItem { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string slowPayItemUrl { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<List<string>> tagidList { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string rightUpImg { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public JdliveExplainStatus jdliveExplainStatus { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public AvlCoupon avlCoupon { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Jxgf jxgf { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Flag flag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Pricerate pricerate { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> uaShield { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string appArouse { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string digitalCurrencyTips { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public PayAddress payAddress { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public ShareInfo shareInfo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public ChannelInfo channelInfo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public FloorForbids floorForbids { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public BottomBar bottomBar { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public BottomNav bottomNav { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public FloorSwitch floorSwitch { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public InfoItem item { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> skuChooseArr { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string noSkuArea { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public SkuPro skuPro { get; set; }
        }
        public class MainItem
        {
            public Item item { get; set; }
        }
        public class JDCallBacktRoot
        {
            /// <summary>
            /// 
            /// </summary>
            public MainItem item { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Info info { get; set; }
        }

    }


    public class JdCallBackDTO
    {
        /// <summary>
        /// 商品id
        /// </summary>
        public string goodsId { get; set; }
        public JDCallBacktRoot data { get; set; }
        public string status { get; set; }
        public bool success { get; set; }
        public string msg { get; set; }
    }
    public class ReturnData
    {
        public JDCallBacktRoot item { get; set; }
    }
    public class Attachment
    {
        public string goods_id { get; set; }
        public string token { get; set; }
        public string batch_number { get; set; }
        public string sn { get; set; }
    }

}
