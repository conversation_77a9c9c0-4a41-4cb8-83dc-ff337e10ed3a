﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.Compression</name>
  </assembly>
  <members>
    <member name="T:System.IO.Compression.CompressionLevel">
      <summary>Specifies values that indicate whether a compression operation emphasizes speed or compression size.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.Fastest">
      <summary>The compression operation should complete as quickly as possible, even if the resulting file is not optimally compressed.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.NoCompression">
      <summary>No compression should be performed on the file.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.Optimal">
      <summary>The compression operation should be optimally compressed, even if the operation takes a longer time to complete.</summary>
    </member>
    <member name="T:System.IO.Compression.CompressionMode">
      <summary>Specifies whether to compress or decompress the underlying stream.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionMode.Compress">
      <summary>Compresses the underlying stream.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionMode.Decompress">
      <summary>Decompresses the underlying stream.</summary>
    </member>
    <member name="T:System.IO.Compression.DeflateStream">
      <summary>Provides methods and properties for compressing and decompressing streams by using the Deflate algorithm.</summary>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Compression.DeflateStream" /> class by using the specified stream and compression level.</summary>
      <param name="stream">The stream to compress.</param>
      <param name="compressionLevel">One of the enumeration values that indicates whether to emphasize speed or compression efficiency when compressing the stream.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The stream does not support write operations such as compression. (The <see cref="P:System.IO.Stream.CanWrite" /> property on the stream object is <see langword="false" />.)</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Compression.DeflateStream" /> class by using the specified stream and compression level, and optionally leaves the stream open.</summary>
      <param name="stream">The stream to compress.</param>
      <param name="compressionLevel">One of the enumeration values that indicates whether to emphasize speed or compression efficiency when compressing the stream.</param>
      <param name="leaveOpen">
        <see langword="true" /> to leave the stream object open after disposing the <see cref="T:System.IO.Compression.DeflateStream" /> object; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The stream does not support write operations such as compression. (The <see cref="P:System.IO.Stream.CanWrite" /> property on the stream object is <see langword="false" />.)</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Compression.DeflateStream" /> class by using the specified stream and compression mode.</summary>
      <param name="stream">The stream to compress or decompress.</param>
      <param name="mode">One of the enumeration values that indicates whether to compress or decompress the stream.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> is not a valid <see cref="T:System.IO.Compression.CompressionMode" /> value.
-or-
<see cref="T:System.IO.Compression.CompressionMode" /> is <see cref="F:System.IO.Compression.CompressionMode.Compress" /> and <see cref="P:System.IO.Stream.CanWrite" /> is <see langword="false" />.
-or-
<see cref="T:System.IO.Compression.CompressionMode" /> is <see cref="F:System.IO.Compression.CompressionMode.Decompress" /> and <see cref="P:System.IO.Stream.CanRead" /> is <see langword="false" />.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Compression.DeflateStream" /> class by using the specified stream and compression mode, and optionally leaves the stream open.</summary>
      <param name="stream">The stream to compress or decompress.</param>
      <param name="mode">One of the enumeration values that indicates whether to compress or decompress the stream.</param>
      <param name="leaveOpen">
        <see langword="true" /> to leave the stream open after disposing the <see cref="T:System.IO.Compression.DeflateStream" /> object; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> is not a valid <see cref="T:System.IO.Compression.CompressionMode" /> value.
-or-
<see cref="T:System.IO.Compression.CompressionMode" /> is <see cref="F:System.IO.Compression.CompressionMode.Compress" /> and <see cref="P:System.IO.Stream.CanWrite" /> is <see langword="false" />.
-or-
<see cref="T:System.IO.Compression.CompressionMode" /> is <see cref="F:System.IO.Compression.CompressionMode.Decompress" /> and <see cref="P:System.IO.Stream.CanRead" /> is <see langword="false" />.</exception>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.BaseStream">
      <summary>Gets a reference to the underlying stream.</summary>
      <returns>A stream object that represents the underlying stream.</returns>
      <exception cref="T:System.ObjectDisposedException">The underlying stream is closed.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous read operation. (Consider using the <see cref="M:System.IO.Stream.ReadAsync(System.Byte[],System.Int32,System.Int32)" /> method instead.)</summary>
      <param name="buffer">The byte array to read the data into.</param>
      <param name="offset">The byte offset in <paramref name="array" /> at which to begin reading data from the stream.</param>
      <param name="count">The maximum number of bytes to read.</param>
      <param name="asyncCallback">An optional asynchronous callback, to be called when the read operation is complete.</param>
      <param name="asyncState">A user-provided object that distinguishes this particular asynchronous read request from other requests.</param>
      <returns>An  object that represents the asynchronous read operation, which could still be pending.</returns>
      <exception cref="T:System.IO.IOException">The method tried to read asynchronously past the end of the stream, or a disk error occurred.</exception>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid.</exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed.</exception>
      <exception cref="T:System.NotSupportedException">The current <see cref="T:System.IO.Compression.DeflateStream" /> implementation does not support the read operation.</exception>
      <exception cref="T:System.InvalidOperationException">This call cannot be completed.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous write operation. (Consider using the <see cref="M:System.IO.Stream.WriteAsync(System.Byte[],System.Int32,System.Int32)" /> method instead.)</summary>
      <param name="array">The buffer to write data from.</param>
      <param name="offset">The byte offset in <paramref name="buffer" /> to begin writing from.</param>
      <param name="count">The maximum number of bytes to write.</param>
      <param name="asyncCallback">An optional asynchronous callback, to be called when the write operation is complete.</param>
      <param name="asyncState">A user-provided object that distinguishes this particular asynchronous write request from other requests.</param>
      <returns>An object that represents the asynchronous write operation, which could still be pending.</returns>
      <exception cref="T:System.IO.IOException">The method tried to write asynchronously past the end of the stream, or a disk error occurred.</exception>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid.</exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed.</exception>
      <exception cref="T:System.NotSupportedException">The current <see cref="T:System.IO.Compression.DeflateStream" /> implementation does not support the write operation.</exception>
      <exception cref="T:System.InvalidOperationException">The write operation cannot be performed because the stream is closed.</exception>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanRead">
      <summary>Gets a value indicating whether the stream supports reading while decompressing a file.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.IO.Compression.CompressionMode" /> value is <see langword="Decompress" />, and the underlying stream is opened and supports reading; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanSeek">
      <summary>Gets a value indicating whether the stream supports seeking.</summary>
      <returns>
        <see langword="false" /> in all cases.</returns>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanWrite">
      <summary>Gets a value indicating whether the stream supports writing.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.IO.Compression.CompressionMode" /> value is <see langword="Compress" />, and the underlying stream supports writing and is not closed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.CopyTo(System.IO.Stream,System.Int32)">
      <summary>Reads the bytes from the current Deflate stream and writes them to another stream, using a specified buffer size.</summary>
      <param name="destination">The stream to which the contents of the current Deflate stream will be copied.</param>
      <param name="bufferSize">The size of the buffer. This value must be greater than zero. The default size is 81920.</param>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.CopyToAsync(System.IO.Stream,System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously reads the bytes from the current Deflate stream and writes them to another stream, using a specified buffer size.</summary>
      <param name="destination">The stream to which the contents of the current Deflate stream will be copied.</param>
      <param name="bufferSize">The size, in bytes, of the buffer. This value must be greater than zero. The default size is 81920.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous copy operation.</returns>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.IO.Compression.DeflateStream" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.DisposeAsync">
      <summary>Asynchronously releases the unmanaged resources used by the <see cref="T:System.IO.Compression.DeflateStream" />.</summary>
      <returns>A task that represents the asynchronous dispose operation.</returns>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.EndRead(System.IAsyncResult)">
      <summary>Waits for the pending asynchronous read to complete. (Consider using the <see cref="M:System.IO.Stream.ReadAsync(System.Byte[],System.Int32,System.Int32)" /> method instead.)</summary>
      <param name="asyncResult">The reference to the pending asynchronous request to finish.</param>
      <returns>The number of bytes read from the stream, between 0 (zero) and the number of bytes you requested. <see cref="T:System.IO.Compression.DeflateStream" /> returns 0 only at the end of the stream; otherwise, it blocks until at least one byte is available.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> did not originate from a <see cref="M:System.IO.Compression.DeflateStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> method on the current stream.</exception>
      <exception cref="T:System.SystemException">An exception was thrown during a call to <see cref="M:System.Threading.WaitHandle.WaitOne" />.</exception>
      <exception cref="T:System.InvalidOperationException">The end call is invalid because asynchronous read operations for this stream are not yet complete.
-or-
The stream is <see langword="null" />.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.EndWrite(System.IAsyncResult)">
      <summary>Ends an asynchronous write operation. (Consider using the <see cref="M:System.IO.Stream.WriteAsync(System.Byte[],System.Int32,System.Int32)" /> method instead.)</summary>
      <param name="asyncResult">A reference to the outstanding asynchronous I/O request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> did not originate from a <see cref="M:System.IO.Compression.DeflateStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> method on the current stream.</exception>
      <exception cref="T:System.Exception">An exception was thrown during a call to <see cref="M:System.Threading.WaitHandle.WaitOne" />.</exception>
      <exception cref="T:System.InvalidOperationException">The stream is <see langword="null" />.
-or-
The end write call is invalid.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Flush">
      <summary>The current implementation of this method has no functionality.</summary>
      <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>Asynchronously clears all buffers for this Deflate stream, causes any buffered data to be written to the underlying device, and monitors cancellation requests.</summary>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous flush operation.</returns>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.Length">
      <summary>This property is not supported and always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <returns>A long value.</returns>
      <exception cref="T:System.NotSupportedException">This property is not supported on this stream.</exception>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.Position">
      <summary>This property is not supported and always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <returns>A long value.</returns>
      <exception cref="T:System.NotSupportedException">This property is not supported on this stream.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Reads a number of decompressed bytes into the specified byte array.</summary>
      <param name="array">The array to store decompressed bytes.</param>
      <param name="offset">The byte offset in <paramref name="array" /> at which the read bytes will be placed.</param>
      <param name="count">The maximum number of decompressed bytes to read.</param>
      <returns>The number of bytes that were read into the byte array.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.IO.Compression.CompressionMode" /> value was <see langword="Compress" /> when the object was created.
-or-
The underlying stream does not support reading.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is less than zero.
-or-
<paramref name="array" /> length minus the index starting point is less than <paramref name="count" />.</exception>
      <exception cref="T:System.IO.InvalidDataException">The data is in an invalid format.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Read(System.Span{System.Byte})">
      <summary>Reads a sequence of bytes from the current Deflate stream into a byte span and advances the position within the Deflate stream by the number of bytes read.</summary>
      <param name="buffer">A region of memory. When this method returns, the contents of this region are replaced by the bytes read from the current source.</param>
      <returns>The total number of bytes read into the buffer. This can be less than the number of bytes allocated in the buffer if that many bytes are not currently available, or zero (0) if the end of the stream has been reached.</returns>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously reads a sequence of bytes from the current Deflate stream, writes them to a byte array, advances the position within the Deflate stream by the number of bytes read, and monitors cancellation requests.</summary>
      <param name="array">The buffer to write the data into.</param>
      <param name="offset">The byte offset in <paramref name="array" /> at which to begin writing data from the Deflate stream.</param>
      <param name="count">The maximum number of bytes to read.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous read operation, which wraps the total number of bytes read into the <paramref name="array" />. The result value can be less than the number of bytes requested if the number of bytes currently available is less than the requested number, or it can be 0 (zero) if the end of the Deflate stream has been reached.</returns>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.ReadAsync(System.Memory{System.Byte},System.Threading.CancellationToken)">
      <summary>Asynchronously reads a sequence of bytes from the current Deflate stream, writes them to a byte memory range, advances the position within the Deflate stream by the number of bytes read, and monitors cancellation requests.</summary>
      <param name="buffer">The region of memory to write the data into.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous read operation, which wraps the total number of bytes read into the buffer. The result value can be less than the number of bytes allocated in the buffer if that many bytes are not currently available, or it can be 0 (zero) if the end of the Deflate stream has been reached.</returns>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.ReadByte">
      <summary>Reads a byte from the Deflate stream and advances the position within the stream by one byte, or returns -1 if at the end of the Deflate stream.</summary>
      <returns>The unsigned byte cast to an <see langword="Int32" />, or -1 if at the end of the stream.</returns>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>This operation is not supported and always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <param name="offset">The location in the stream.</param>
      <param name="origin">One of the <see cref="T:System.IO.SeekOrigin" /> values.</param>
      <returns>A long value.</returns>
      <exception cref="T:System.NotSupportedException">This property is not supported on this stream.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.SetLength(System.Int64)">
      <summary>This operation is not supported and always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <param name="value">The length of the stream.</param>
      <exception cref="T:System.NotSupportedException">This property is not supported on this stream.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Writes compressed bytes to the underlying stream from the specified byte array.</summary>
      <param name="array">The buffer that contains the data to compress.</param>
      <param name="offset">The byte offset in <paramref name="array" /> from which the bytes will be read.</param>
      <param name="count">The maximum number of bytes to write.</param>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Write(System.ReadOnlySpan{System.Byte})">
      <summary>Writes a sequence of bytes to the current Deflate stream and advances the current position within this Deflate stream by the number of bytes written.</summary>
      <param name="buffer">A region of memory. This method copies the contents of this region to the current Deflate stream.</param>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously writes compressed bytes to the underlying Deflate stream from the specified byte array.</summary>
      <param name="array">The buffer that contains the data to compress.</param>
      <param name="offset">The zero-based byte offset in <paramref name="array" /> from which to begin copying bytes to the Deflate stream.</param>
      <param name="count">The maximum number of bytes to write.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.WriteAsync(System.ReadOnlyMemory{System.Byte},System.Threading.CancellationToken)">
      <summary>Asynchronously writes compressed bytes to the underlying Deflate stream from the specified read-only memory region.</summary>
      <param name="buffer">The region of memory to write data from.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="T:System.IO.Compression.GZipStream">
      <summary>Provides methods and properties used to compress and decompress streams by using the GZip data format specification.</summary>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Compression.GZipStream" /> class by using the specified stream and compression level.</summary>
      <param name="stream">The stream to compress.</param>
      <param name="compressionLevel">One of the enumeration values that indicates whether to emphasize speed or compression efficiency when compressing the stream.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The stream does not support write operations such as compression. (The <see cref="P:System.IO.Stream.CanWrite" /> property on the stream object is <see langword="false" />.)</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Compression.GZipStream" /> class by using the specified stream and compression level, and optionally leaves the stream open.</summary>
      <param name="stream">The stream to write the compressed data to.</param>
      <param name="compressionLevel">One of the enumeration values that indicates whether to emphasize speed or compression efficiency when compressing the stream.</param>
      <param name="leaveOpen">
        <see langword="true" /> to leave the stream object open after disposing the <see cref="T:System.IO.Compression.GZipStream" /> object; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The stream does not support write operations such as compression. (The <see cref="P:System.IO.Stream.CanWrite" /> property on the stream object is <see langword="false" />.)</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Compression.GZipStream" /> class by using the specified stream and compression mode.</summary>
      <param name="stream">The stream the compressed or decompressed data is written to.</param>
      <param name="mode">One of the enumeration values that indicates whether to compress or decompress the stream.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> is not a valid <see cref="T:System.IO.Compression.CompressionMode" /> enumeration value.
-or-
<see cref="T:System.IO.Compression.CompressionMode" /> is <see cref="F:System.IO.Compression.CompressionMode.Compress" /> and <see cref="P:System.IO.Stream.CanWrite" /> is <see langword="false" />.
-or-
<see cref="T:System.IO.Compression.CompressionMode" /> is <see cref="F:System.IO.Compression.CompressionMode.Decompress" /> and <see cref="P:System.IO.Stream.CanRead" /> is <see langword="false" />.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Compression.GZipStream" /> class by using the specified stream and compression mode, and optionally leaves the stream open.</summary>
      <param name="stream">The stream to compress.</param>
      <param name="mode">One of the enumeration values that indicates whether to compress or decompress the stream.</param>
      <param name="leaveOpen">
        <see langword="true" /> to leave the stream open after disposing the <see cref="T:System.IO.Compression.GZipStream" /> object; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> is not a valid <see cref="T:System.IO.Compression.CompressionMode" /> value.
-or-
<see cref="T:System.IO.Compression.CompressionMode" /> is <see cref="F:System.IO.Compression.CompressionMode.Compress" /> and <see cref="P:System.IO.Stream.CanWrite" /> is <see langword="false" />.
-or-
<see cref="T:System.IO.Compression.CompressionMode" /> is <see cref="F:System.IO.Compression.CompressionMode.Decompress" /> and <see cref="P:System.IO.Stream.CanRead" /> is <see langword="false" />.</exception>
    </member>
    <member name="P:System.IO.Compression.GZipStream.BaseStream">
      <summary>Gets a reference to the underlying stream.</summary>
      <returns>A stream object that represents the underlying stream.</returns>
      <exception cref="T:System.ObjectDisposedException">The underlying stream is closed.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous read operation. (Consider using the <see cref="M:System.IO.Stream.ReadAsync(System.Byte[],System.Int32,System.Int32)" /> method instead.)</summary>
      <param name="array">The byte array to read the data into.</param>
      <param name="offset">The byte offset in <paramref name="array" /> at which to begin reading data from the stream.</param>
      <param name="count">The maximum number of bytes to read.</param>
      <param name="asyncCallback">An optional asynchronous callback, to be called when the read operation is complete.</param>
      <param name="asyncState">A user-provided object that distinguishes this particular asynchronous read request from other requests.</param>
      <returns>An object that represents the asynchronous read operation, which could still be pending.</returns>
      <exception cref="T:System.IO.IOException">The method tried to  read asynchronously past the end of the stream, or a disk error occurred.</exception>
      <exception cref="T:System.ArgumentException">One or more of the arguments is invalid.</exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed.</exception>
      <exception cref="T:System.NotSupportedException">The current <see cref="T:System.IO.Compression.GZipStream" /> implementation does not support the read operation.</exception>
      <exception cref="T:System.InvalidOperationException">A read operation cannot be performed because the stream is closed.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous write operation. (Consider using the <see cref="M:System.IO.Stream.WriteAsync(System.Byte[],System.Int32,System.Int32)" /> method instead.)</summary>
      <param name="array">The buffer containing data to write to the current stream.</param>
      <param name="offset">The byte offset in <paramref name="array" /> at which to begin writing.</param>
      <param name="count">The maximum number of bytes to write.</param>
      <param name="asyncCallback">An optional asynchronous callback to be called when the write operation is complete.</param>
      <param name="asyncState">A user-provided object that distinguishes this particular asynchronous write request from other requests.</param>
      <returns>An  object that represents the asynchronous write operation, which could still be pending.</returns>
      <exception cref="T:System.InvalidOperationException">The underlying stream is <see langword="null" />.
-or-
The underlying stream is closed.</exception>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanRead">
      <summary>Gets a value indicating whether the stream supports reading while decompressing a file.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.IO.Compression.CompressionMode" /> value is <see langword="Decompress," /> and the underlying stream supports reading and is not closed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanSeek">
      <summary>Gets a value indicating whether the stream supports seeking.</summary>
      <returns>
        <see langword="false" /> in all cases.</returns>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanWrite">
      <summary>Gets a value indicating whether the stream supports writing.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.IO.Compression.CompressionMode" /> value is <see langword="Compress" />, and the underlying stream supports writing and is not closed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.IO.Compression.GZipStream.CopyTo(System.IO.Stream,System.Int32)">
      <summary>Reads the bytes from the current GZip stream and writes them to another stream, using a specified buffer size.</summary>
      <param name="destination">The stream to which the contents of the current GZip stream will be copied.</param>
      <param name="bufferSize">The size of the buffer. This value must be greater than zero. The default size is 81920.</param>
    </member>
    <member name="M:System.IO.Compression.GZipStream.CopyToAsync(System.IO.Stream,System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously reads the bytes from the current GZip stream and writes them to another stream, using a specified buffer size.</summary>
      <param name="destination">The stream to which the contents of the current GZip stream will be copied.</param>
      <param name="bufferSize">The size, in bytes, of the buffer. This value must be greater than zero. The default size is 81920.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous copy operation.</returns>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.IO.Compression.GZipStream" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.IO.Compression.GZipStream.DisposeAsync">
      <summary>Asynchronously releases the unmanaged resources used by the <see cref="T:System.IO.Compression.GZipStream" />.</summary>
      <returns>A task that represents the asynchronous dispose operation.</returns>
    </member>
    <member name="M:System.IO.Compression.GZipStream.EndRead(System.IAsyncResult)">
      <summary>Waits for the pending asynchronous read to complete. (Consider using the <see cref="M:System.IO.Stream.ReadAsync(System.Byte[],System.Int32,System.Int32)" /> method instead.)</summary>
      <param name="asyncResult">The reference to the pending asynchronous request to finish.</param>
      <returns>The number of bytes read from the stream, between 0 (zero) and the number of bytes you requested. <see cref="T:System.IO.Compression.GZipStream" /> returns 0 only at the end of the stream; otherwise, it blocks until at least one byte is available.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> did not originate from a <see cref="M:System.IO.Compression.GZipStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> method on the current stream.</exception>
      <exception cref="T:System.InvalidOperationException">The end operation cannot be performed because the stream is closed.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.EndWrite(System.IAsyncResult)">
      <summary>Handles the end of an asynchronous write operation. (Consider using the <see cref="M:System.IO.Stream.WriteAsync(System.Byte[],System.Int32,System.Int32)" /> method instead.)</summary>
      <param name="asyncResult">The object that represents the asynchronous call.</param>
      <exception cref="T:System.InvalidOperationException">The underlying stream is <see langword="null" />.
-or-
The underlying stream is closed.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Flush">
      <summary>The current implementation of this method has no functionality.</summary>
      <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>Asynchronously clears all buffers for this GZip stream, causes any buffered data to be written to the underlying device, and monitors cancellation requests.</summary>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous flush operation.</returns>
    </member>
    <member name="P:System.IO.Compression.GZipStream.Length">
      <summary>This property is not supported and always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <returns>A long value.</returns>
      <exception cref="T:System.NotSupportedException">This property is not supported on this stream.</exception>
    </member>
    <member name="P:System.IO.Compression.GZipStream.Position">
      <summary>This property is not supported and always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <returns>A long value.</returns>
      <exception cref="T:System.NotSupportedException">This property is not supported on this stream.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Reads a number of decompressed bytes into the specified byte array.</summary>
      <param name="array">The array used to store decompressed bytes.</param>
      <param name="offset">The byte offset in <paramref name="array" /> at which the read bytes will be placed.</param>
      <param name="count">The maximum number of decompressed bytes to read.</param>
      <returns>The number of bytes that were decompressed into the byte array. If the end of the stream has been reached, zero or the number of bytes read is returned.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.IO.Compression.CompressionMode" /> value was <see langword="Compress" /> when the object was created.
-or-
The underlying stream does not support reading.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is less than zero.
-or-
<paramref name="array" /> length minus the index starting point is less than <paramref name="count" />.</exception>
      <exception cref="T:System.IO.InvalidDataException">The data is in an invalid format.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Read(System.Span{System.Byte})">
      <summary>Reads a sequence of bytes from the current GZip stream into a byte span and advances the position within the GZip stream by the number of bytes read.</summary>
      <param name="buffer">A region of memory. When this method returns, the contents of this region are replaced by the bytes read from the current source.</param>
      <returns>The total number of bytes read into the buffer. This can be less than the number of bytes allocated in the buffer if that many bytes are not currently available, or zero (0) if the end of the stream has been reached.</returns>
    </member>
    <member name="M:System.IO.Compression.GZipStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously reads a sequence of bytes from the current GZip stream into a byte array, advances the position within the GZip stream by the number of bytes read, and monitors cancellation requests.</summary>
      <param name="array">The buffer to write the data into.</param>
      <param name="offset">The byte offset in <paramref name="array" /> at which to begin writing data from the GZip stream.</param>
      <param name="count">The maximum number of bytes to read.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous read operation, which wraps the total number of bytes read into the <paramref name="array" />. The result value can be less than the number of bytes requested if the number of bytes currently available is less than the requested number, or it can be 0 (zero) if the end of the GZip stream has been reached.</returns>
    </member>
    <member name="M:System.IO.Compression.GZipStream.ReadAsync(System.Memory{System.Byte},System.Threading.CancellationToken)">
      <summary>Asynchronously reads a sequence of bytes from the current GZip stream into a byte memory region, advances the position within the GZip stream by the number of bytes read, and monitors cancellation requests.</summary>
      <param name="buffer">The region of memory to write the data into.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous read operation, which wraps the total number of bytes read into the buffer. The result value can be less than the number of bytes allocated in the buffer if that many bytes are not currently available, or it can be 0 (zero) if the end of the GZip stream has been reached.</returns>
    </member>
    <member name="M:System.IO.Compression.GZipStream.ReadByte">
      <summary>Reads a byte from the GZip stream and advances the position within the stream by one byte, or returns -1 if at the end of the GZip stream.</summary>
      <returns>The unsigned byte cast to an <see langword="Int32" />, or -1 if at the end of the stream.</returns>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>This property is not supported and always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <param name="offset">The location in the stream.</param>
      <param name="origin">One of the <see cref="T:System.IO.SeekOrigin" /> values.</param>
      <returns>A long value.</returns>
      <exception cref="T:System.NotSupportedException">This property is not supported on this stream.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.SetLength(System.Int64)">
      <summary>This property is not supported and always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <param name="value">The length of the stream.</param>
      <exception cref="T:System.NotSupportedException">This property is not supported on this stream.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Writes compressed bytes to the underlying GZip stream from the specified byte array.</summary>
      <param name="array">The buffer that contains the data to compress.</param>
      <param name="offset">The byte offset in <paramref name="array" /> from which the bytes will be read.</param>
      <param name="count">The maximum number of bytes to write.</param>
      <exception cref="T:System.ObjectDisposedException">The write operation cannot be performed because the stream is closed.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Write(System.ReadOnlySpan{System.Byte})">
      <summary>Writes a sequence of bytes to the current GZip stream from a read-only byte span and advances the current position within this GZip stream by the number of bytes written.</summary>
      <param name="buffer">A region of memory. This method copies the contents of this region to the current GZip stream.</param>
    </member>
    <member name="M:System.IO.Compression.GZipStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously writes compressed bytes to the underlying GZip stream from the specified byte array.</summary>
      <param name="array">The buffer that contains the data to compress.</param>
      <param name="offset">The zero-based byte offset in <paramref name="array" /> from which to begin copying bytes to the GZip stream.</param>
      <param name="count">The maximum number of bytes to write.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="M:System.IO.Compression.GZipStream.WriteAsync(System.ReadOnlyMemory{System.Byte},System.Threading.CancellationToken)">
      <summary>Asynchronously writes compressed bytes to the underlying GZip stream from the specified read-only byte memory region.</summary>
      <param name="buffer">The region of memory to write data from.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="T:System.IO.Compression.ZipArchive">
      <summary>Represents a package of compressed files in the zip archive format.</summary>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Compression.ZipArchive" /> class from the specified stream.</summary>
      <param name="stream">The stream that contains the archive to be read.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed or does not support reading.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream are not in the zip archive format.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Compression.ZipArchive" /> class from the specified stream and with the specified mode.</summary>
      <param name="stream">The input or output stream.</param>
      <param name="mode">One of the enumeration values that indicates whether the zip archive is used to read, create, or update entries.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.
-or-
<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.
-or-
<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Compression.ZipArchive" /> class on the specified stream for the specified mode, and optionally leaves the stream open.</summary>
      <param name="stream">The input or output stream.</param>
      <param name="mode">One of the enumeration values that indicates whether the zip archive is used to read, create, or update entries.</param>
      <param name="leaveOpen">
        <see langword="true" /> to leave the stream open after the <see cref="T:System.IO.Compression.ZipArchive" /> object is disposed; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.
-or-
<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.
-or-
<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode,System.Boolean,System.Text.Encoding)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Compression.ZipArchive" /> class on the specified stream for the specified mode, uses the specified encoding for entry names, and optionally leaves the stream open.</summary>
      <param name="stream">The input or output stream.</param>
      <param name="mode">One of the enumeration values that indicates whether the zip archive is used to read, create, or update entries.</param>
      <param name="leaveOpen">
        <see langword="true" /> to leave the stream open after the <see cref="T:System.IO.Compression.ZipArchive" /> object is disposed; otherwise, <see langword="false" />.</param>
      <param name="entryNameEncoding">The encoding to use when reading or writing entry names in this archive. Specify a value for this parameter only when an encoding is required for interoperability with zip archive tools and libraries that do not support UTF-8 encoding for entry names.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.
-or-
<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.
-or-
<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.CreateEntry(System.String)">
      <summary>Creates an empty entry that has the specified path and entry name in the zip archive.</summary>
      <param name="entryName">A path, relative to the root of the archive, that specifies the name of the entry to be created.</param>
      <returns>An empty entry in the zip archive.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.CreateEntry(System.String,System.IO.Compression.CompressionLevel)">
      <summary>Creates an empty entry that has the specified entry name and compression level in the zip archive.</summary>
      <param name="entryName">A path, relative to the root of the archive, that specifies the name of the entry to be created.</param>
      <param name="compressionLevel">One of the enumeration values that indicates whether to emphasize speed or compression effectiveness when creating the entry.</param>
      <returns>An empty entry in the zip archive.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.Dispose">
      <summary>Releases the resources used by the current instance of the <see cref="T:System.IO.Compression.ZipArchive" /> class.</summary>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.Dispose(System.Boolean)">
      <summary>Called by the <see cref="M:System.IO.Compression.ZipArchive.Dispose" /> and <see cref="M:System.Object.Finalize" /> methods to release the unmanaged resources used by the current instance of the <see cref="T:System.IO.Compression.ZipArchive" /> class, and optionally finishes writing the archive and releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to finish writing the archive and release unmanaged and managed resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="P:System.IO.Compression.ZipArchive.Entries">
      <summary>Gets the collection of entries that are currently in the zip archive.</summary>
      <returns>The collection of entries that are currently in the zip archive.</returns>
      <exception cref="T:System.NotSupportedException">The zip archive does not support reading.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
      <exception cref="T:System.IO.InvalidDataException">The zip archive is corrupt, and its entries cannot be retrieved.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.GetEntry(System.String)">
      <summary>Retrieves a wrapper for the specified entry in the zip archive.</summary>
      <param name="entryName">A path, relative to the root of the archive, that identifies the entry to retrieve.</param>
      <returns>A wrapper for the specified entry in the archive; <see langword="null" /> if the entry does not exist in the archive.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support reading.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
      <exception cref="T:System.IO.InvalidDataException">The zip archive is corrupt, and its entries cannot be retrieved.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchive.Mode">
      <summary>Gets a value that describes the type of action the zip archive can perform on entries.</summary>
      <returns>One of the enumeration values that describes the type of action (read, create, or update) the zip archive can perform on entries.</returns>
    </member>
    <member name="T:System.IO.Compression.ZipArchiveEntry">
      <summary>Represents a compressed file within a zip archive.</summary>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Archive">
      <summary>Gets the zip archive that the entry belongs to.</summary>
      <returns>The zip archive that the entry belongs to, or <see langword="null" /> if the entry has been deleted.</returns>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.CompressedLength">
      <summary>Gets the compressed size of the entry in the zip archive.</summary>
      <returns>The compressed size of the entry in the zip archive.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the property is not available because the entry has been modified.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Crc32" />
    <member name="M:System.IO.Compression.ZipArchiveEntry.Delete">
      <summary>Deletes the entry from the zip archive.</summary>
      <exception cref="T:System.IO.IOException">The entry is already open for reading or writing.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive for this entry was opened in a mode other than <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive for this entry has been disposed.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.ExternalAttributes">
      <summary>OS and application specific file attributes.</summary>
      <returns>The external attributes written by the application when this entry was written. It is both host OS and application dependent.</returns>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.FullName">
      <summary>Gets the relative path of the entry in the zip archive.</summary>
      <returns>The relative path of the entry in the zip archive.</returns>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.LastWriteTime">
      <summary>Gets or sets the last time the entry in the zip archive was changed.</summary>
      <returns>The last time the entry in the zip archive was changed.</returns>
      <exception cref="T:System.NotSupportedException">The attempt to set this property failed, because the zip archive for the entry is in <see cref="F:System.IO.Compression.ZipArchiveMode.Read" /> mode.</exception>
      <exception cref="T:System.IO.IOException">The archive mode is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />.
-or-
The archive mode is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and the entry has been opened.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An attempt was made to set this property to a value that is either earlier than 1980 January 1 0:00:00 (midnight) or later than 2107 December 31 23:59:58 (one second before midnight).</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Length">
      <summary>Gets the uncompressed size of the entry in the zip archive.</summary>
      <returns>The uncompressed size of the entry in the zip archive.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the property is not available because the entry has been modified.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Name">
      <summary>Gets the file name of the entry in the zip archive.</summary>
      <returns>The file name of the entry in the zip archive.</returns>
    </member>
    <member name="M:System.IO.Compression.ZipArchiveEntry.Open">
      <summary>Opens the entry from the zip archive.</summary>
      <returns>The stream that represents the contents of the entry.</returns>
      <exception cref="T:System.IO.IOException">The entry is already currently open for writing.
-or-
The entry has been deleted from the archive.
-or-
The archive for this entry was opened with the <see cref="F:System.IO.Compression.ZipArchiveMode.Create" /> mode, and this entry has already been written to.</exception>
      <exception cref="T:System.IO.InvalidDataException">The entry is either missing from the archive or is corrupt and cannot be read.
-or-
The entry has been compressed by using a compression method that is not supported.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive for this entry has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchiveEntry.ToString">
      <summary>Retrieves the relative path of the entry in the zip archive.</summary>
      <returns>The relative path of the entry, which is the value stored in the <see cref="P:System.IO.Compression.ZipArchiveEntry.FullName" /> property.</returns>
    </member>
    <member name="T:System.IO.Compression.ZipArchiveMode">
      <summary>Specifies values for interacting with zip archive entries.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Create">
      <summary>Only creating new archive entries is permitted.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Read">
      <summary>Only reading archive entries is permitted.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Update">
      <summary>Both read and write operations are permitted for archive entries.</summary>
    </member>
  </members>
</doc>