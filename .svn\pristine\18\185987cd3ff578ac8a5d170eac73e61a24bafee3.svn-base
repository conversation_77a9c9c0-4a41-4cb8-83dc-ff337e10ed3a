﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Coldairarrow.Util.Helper
{
    public static class TimeStampsHelper
    {
        /// <summary>
        /// 将时间戳转换为时间
        /// </summary>
        /// <returns></returns>
        public static DateTime GoToDateTime(string TimeStamps)
        {
            var TimeStamp = long.Parse(TimeStamps);
            System.DateTime startTime = TimeZone.CurrentTimeZone.ToLocalTime(new System.DateTime(1970, 1, 1));
            var date = startTime.AddMilliseconds(TimeStamp);
            //var date = new DateTime(1970, 1, 1).AddMilliseconds(TimeStamp);
            ////处理成北京时间
            //date.AddHours(8);
            //new DateTime().AddMilliseconds(621355968000000000/10000).AddMilliseconds(TimeStamps);//效果同上
            return date;
        }
        public static string getWeek(DateTime dete)
        {
            string[] Day = new string[] { "星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六" };
            string week = Day[Convert.ToInt32(dete.DayOfWeek.ToString("d"))].ToString();
            return week;
        }
        /// <summary>
        /// 获取本月的每天
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static List<DateTime> GetDayByMonth(DateTime date)
        {
            //当月第一天0时0分0秒：

            var startTime = date.AddDays(1 - date.Day).Date;
            List<DateTime> dateTimes = new List<DateTime>();
            //当月最后一天23时59分59秒：
            var endTime = date.AddDays(1 - date.Day).Date.AddMonths(1).AddSeconds(-1);
            for (var time = startTime; time <= endTime; time= time.AddDays(1))
            {
                dateTimes.Add(time);
            }
            return dateTimes;
        }
        //获取某年的周六周日
        public static int GetWMDay(int year)
        {
            int retNum = 0;
            DateTime counYear = Convert.ToDateTime(year + "-01-01");
            DateTime nestYear = counYear.AddYears(1);
            for (DateTime i = counYear; i < nestYear; i = i.AddDays(1))
            {
                if (i.DayOfWeek == DayOfWeek.Saturday || i.DayOfWeek == DayOfWeek.Sunday)
                {
                    retNum++;
                }
            }
            return retNum;
        }
        /// <summary>  
        /// 得到本周第一天(以星期天为第一天)  
        /// </summary>  
        /// <param name="datetime"></param>  
        /// <returns></returns>  
        public static DateTime GetWeekFirstDaySun(DateTime datetime)
        {
            //星期天为第一天  
            int weeknow = Convert.ToInt32(datetime.DayOfWeek);
            int daydiff = (-1) * weeknow;

            //本周第一天  
            string FirstDay = datetime.AddDays(daydiff).ToString("yyyy-MM-dd");
            return Convert.ToDateTime(FirstDay);
        }

        /// <summary>  
        /// 得到本周第一天(以星期一为第一天)  
        /// </summary>  
        /// <param name="datetime"></param>  
        /// <returns></returns>  
        public static DateTime GetWeekFirstDayMon(DateTime datetime)
        {
            //星期一为第一天  
            int weeknow = Convert.ToInt32(datetime.DayOfWeek);

            //因为是以星期一为第一天，所以要判断weeknow等于0时，要向前推6天。  
            weeknow = (weeknow == 0 ? (7 - 1) : (weeknow - 1));
            int daydiff = (-1) * weeknow;

            //本周第一天  
            string FirstDay = datetime.AddDays(daydiff).ToString("yyyy-MM-dd");
            return Convert.ToDateTime(FirstDay);
        }

        /// <summary>  
        /// 得到本周最后一天(以星期六为最后一天)  
        /// </summary>  
        /// <param name="datetime"></param>  
        /// <returns></returns>  
        public static DateTime GetWeekLastDaySat(DateTime datetime)
        {
            //星期六为最后一天  
            int weeknow = Convert.ToInt32(datetime.DayOfWeek);
            int daydiff = (7 - weeknow) - 1;

            //本周最后一天  
            string LastDay = datetime.AddDays(daydiff).ToString("yyyy-MM-dd");
            return Convert.ToDateTime(LastDay);
        }

        /// <summary>  
        /// 得到本周最后一天(以星期天为最后一天)  
        /// </summary>  
        /// <param name="datetime"></param>  
        /// <returns></returns>  
        public static DateTime GetWeekLastDaySun(DateTime datetime)
        {
            //星期天为最后一天  
            int weeknow = Convert.ToInt32(datetime.DayOfWeek);
            weeknow = (weeknow == 0 ? 7 : weeknow);
            int daydiff = (7 - weeknow);

            //本周最后一天  
            string LastDay = datetime.AddDays(daydiff).ToString("yyyy-MM-dd");
            return Convert.ToDateTime(LastDay);
        }
        /// <summary>
        /// 获取过去15天/过去7周/过去月
        /// </summary>
        /// <param name="timeType"></param>
        /// <returns></returns>
        public static List<TrendTime> GetTrendTime(string timeType,string businessType= "Other")
        {
            List<TrendTime> timeStrList = new List<TrendTime>();
            var index = 0;
            var Upper = new List<string> { "一", "二", "三", "四", "五", "六", "七" };
            switch (timeType)
            {
                ///每天(过去15天)
                case "day":
                    var date = businessType == "sales" ? DateTime.Now.AddDays(-1):DateTime.Now;
                    for (var day = 0; day > -15; day--)
                    {

                        TrendTime trendTime = new TrendTime()
                        {
                            time = date.AddDays(day).ToString("yyyy-MM-dd"),
                            timeStr = date.AddDays(day).ToString("MM.dd")
                        };
                        timeStrList.Add(trendTime);
                    }
                    break;
                //每周(过去7周的最后一天)
                case "week":
                    for (var day = 0; day >= -6; day--)
                    {
                        var current = index == 0 ? DateTime.Now : GetWeekLastDaySun(timeStrList[index - 1].time.ToDateTime()).AddDays(-7);
                        TrendTime trendTime = new TrendTime()
                        {
                            time = index == 0 ? DateTime.Now.ToString("yyyy-MM-dd") : GetWeekLastDaySun(current).ToString("yyyy-MM-dd"),
                            timeStr = index == 0 ? "本周" : index == 1 ? "上周" : $"前{Upper[index]}周"
                        };

                        for (var weekDay = -1; weekDay >= -6; weekDay--)
                        {
                            trendTime.times.Add(trendTime.time.ToDateTime().AddDays(weekDay).ToString("yyyy-MM-dd"));
                        }
                        timeStrList.Add(trendTime);
                        ++index;
                    }
                    break;
                //每月
                case "month":
                    for (var day = 0; day >= -11; day--)
                    {
                        var current = index == 0 ? DateTime.Now : timeStrList[index - 1].time.ToDateTime().AddMonths(-1);
                        TrendTime trendTime = new TrendTime()
                        {
                            time = index == 0 ? DateTime.Now.ToString("yyyy-MM-dd") : current.AddMonths(1).AddDays(-Convert.ToInt32(current.Day)).ToString("yyyy-MM-dd"),
                        };
                        trendTime.timeStr = trendTime.time.ToDateTime().ToString("yyyy.MM");
                        timeStrList.Add(trendTime);
                        ++index;
                    }
                    break;
            }
            return timeStrList.OrderBy(x => x.time).ToList();
        }
    }
    public class TrendTime
    {
        public string time { get; set; }
        public List<string> times { get; set; } = new List<string>();
        public string timeStr { get; set; }
    }
}
