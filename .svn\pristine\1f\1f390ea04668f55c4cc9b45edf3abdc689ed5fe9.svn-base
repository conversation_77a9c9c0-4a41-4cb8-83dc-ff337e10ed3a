<template>
  <div style="padding: 30px;">
    <a-card>
      <div>

        <a-button @click="refresh">刷新</a-button>
        &nbsp;
        <a-button @click="back">返回</a-button>
        &nbsp;
        <a-button type="primary" @click="saveExam">保存</a-button>

      </div>
      <a-divider>基本信息</a-divider>
      <a-form-model :model="baseInfo">
        <a-row :gutter="20">
          <a-col :span="12">
            <a-form-model-item labelAlign="left" label="问题名称">
              <a-input style="width: 90%;" v-model="baseInfo.name"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item labelAlign="left" label="问题类型">
              <a-select style="width: 90%;" v-model="baseInfo.desc">
                <a-select-option v-for="(item,index) in baseInfo.types" :key="item.id">{{item.desc}}</a-select-option>

              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="20">
          <a-col :span="24">
            <a-form-model-item label="解析">
              <a-input v-model="baseInfo.countLimit" style="width: 95%;" :rows="4" type="textarea" />
            </a-form-model-item>
          </a-col>

        </a-row>

      </a-form-model>
      <a-divider>问题明细</a-divider>
      <div style="margin-bottom: 15px;">

        <a-button type="primary" @click="handleAdd" plain>添加答案</a-button>
        &nbsp;
        <a-button type="primary" @click="handleReset">全部清除</a-button>

      </div>
      <a-table :columns="columns" :data-source="detailInfo" :pagination="false">
        <template slot="name" slot-scope="text,record,index">
          <a-input type="text" v-model="detailInfo.name" @change="handleChange"></a-input>
        </template>
        <template slot="isAnswer" slot-scope="text,record,index">
          <a-select v-model="text">
            <a-select-option value="1">是</a-select-option>
            <a-select-option value="0">否</a-select-option>
          </a-select>
        </template>
        <template slot="action" slot-scope="text,record,index">
          <a-button @click="handleDelete(index)" type="danger">删除</a-button>
        </template>
      </a-table>

    </a-card>
    <question-select @func="getSelect" ref="questionSelect"></question-select>
    <question-single-select @func="getSingleSelect" ref="questionSingleSelect"></question-single-select>

  </div>

</template>

<script> 
import '@/assets/school/css/bootstrap.min.css'
import '@/assets/school/css/base.css'
import '@/assets/school/css/high-quality-courses.css'
import { getExamCardListAll, getExamCardListBy, examStatusChange, createExam, getQuestionType } from '../../../../api/exam'
import QuestionSingleSelect from './QuestionSingleSelect.vue'
import QuestionSelect from './QuestionSelect.vue'

const columns = [
  {
    title: '序号',
    dataIndex: 'serial',
    align: 'center',
    key: 'serial',
    ellipsis: true,
    customRender: (text, record, index) => `${index + 1}`,
    width: '5%'
  },
  {
    title: '答案内容',
    dataIndex: 'name',
    align: 'center',
    ellipsis: true,
    key: 'name',
    scopedSlots: { customRender: 'name' },
    width: '50%'
  },

  {
    title: '是否为正确答案',
    dataIndex: 'isAnswer',
    align: 'center',
    ellipsis: true,
    key: 'isAnswer',
    scopedSlots: { customRender: 'isAnswer' },
    width: '15%'
  },

  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    key: 'action',
    scopedSlots: { customRender: 'action' },
    width: '30%'
  }

]

export default {
  name: "ExamCreate",
  components: {
    QuestionSingleSelect,
    QuestionSelect
  },
  data() {
    return {
      columns,
      value: "",
      baseInfo: {
        name: "",
        desc: "",
        types: []
      },
      detailInfo: [],
      question: {
        name: "",
        id: "",
        type: ""
      },
      userData: {
        userId: "",
        userName: ""
      }
    }
  }, methods: {
    refresh() {
      this.$router.go(0);
    },
    back() {
      const routeUrl = this.$router.resolve({
        path: `/School/Exam/ExamCardManage`
      })
      window.open(routeUrl.href, '_self')
    },
    handleAdd() {
      this.detailInfo.push(this.question);
    },
    handleDelete(index) {
      this.detailInfo.splice(index, 1)
    },
    handleReset() {
      this.detailInfo.splice(0);
    },
    handleChange(e) {
      const value = e.target.value;
      this.value = value;
    },
    handleChoose() {
      this.$refs.questionSelect.show();
    },
    handleSelect(index) {
      this.$refs.questionSingleSelect.show(index);
    },
    getSelect(values) {
      for (let i = 0; i < values.length; i++) {
        this.detailInfo.push(values[i]);
      }
    },
    getSingleSelect(index, value) {
      this.detailInfo.splice(index, 1, value[0]);
    },
    getQuestionType() {
      getQuestionType().then(res => {
        if (res.success) {
          this.baseInfo.types = res.data;
        }
      })
    },
    saveExam() {
      let datas = this.detailInfo;
      this.baseInfo.radios = [];
      this.baseInfo.checks = [];
      this.baseInfo.judges = [];
      //保存筛选存放
      for (let i = 0; i < datas.length; i++) {
        if (datas[i].type === "单选题") {
          this.baseInfo.radios.push(datas[i])
        }
        if (datas[i].type === "多选题") {
          this.baseInfo.checks.push(datas[i])
        }
        if (datas[i].type === "判断题") {
          this.baseInfo.judges.push(datas[i])
        }
      }
      createExam(this.baseInfo, this.userData.userId).then(res => {
      })
    }
  },
  mounted() {
    this.$http.post('/Base_Manage/Home/GetHomeInfo', {}).then((resJson) => {
      this.userData.userId = resJson.Data.UserId;
      this.userData.userName = resJson.Data.Name;
      this.baseInfo.userName = resJson.Data.Name;
    });

    this.getQuestionType();


  }
}

</script>

<style>
</style>
