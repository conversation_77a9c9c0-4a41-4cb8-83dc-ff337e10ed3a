﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Linq.Parallel</name>
  </assembly>
  <members>
    <member name="T:System.Linq.OrderedParallelQuery`1">
      <summary>Represents a sorted, parallel sequence.</summary>
      <typeparam name="TSource">The type of elements in the source collection.</typeparam>
    </member>
    <member name="M:System.Linq.OrderedParallelQuery`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through the sequence.</summary>
      <returns>An enumerator that iterates through the sequence.</returns>
    </member>
    <member name="T:System.Linq.ParallelEnumerable">
      <summary>Provides a set of methods for querying objects that implement ParallelQuery{TSource}. This is the parallel equivalent of <see cref="T:System.Linq.Enumerable" />.</summary>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Aggregate``1(System.Linq.<PERSON>l<PERSON>{``0},System.Func{``0,``0,``0})">
      <summary>Applies in parallel an accumulator function over a sequence.</summary>
      <param name="source">A sequence to aggregate over.</param>
      <param name="func">An accumulator function to be invoked on each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The final accumulator value.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="func" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Aggregate``2(System.Linq.ParallelQuery{``0},``1,System.Func{``1,``0,``1})">
      <summary>Applies in parallel an accumulator function over a sequence. The specified seed value is used as the initial accumulator value.</summary>
      <param name="source">A sequence to aggregate over.</param>
      <param name="seed">The initial accumulator value.</param>
      <param name="func">An accumulator function to be invoked on each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
      <returns>The final accumulator value.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="func" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Aggregate``3(System.Linq.ParallelQuery{``0},``1,System.Func{``1,``0,``1},System.Func{``1,``1,``1},System.Func{``1,``2})">
      <summary>Applies in parallel an accumulator function over a sequence. This overload is not available in the sequential implementation.</summary>
      <param name="source">A sequence to aggregate over.</param>
      <param name="seed">The initial accumulator value.</param>
      <param name="updateAccumulatorFunc">An accumulator function to be invoked on each element in a partition.</param>
      <param name="combineAccumulatorsFunc">An accumulator function to be invoked on the yielded accumulator result from each partition.</param>
      <param name="resultSelector">A function to transform the final accumulator value into the result value.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
      <typeparam name="TResult">The type of the resulting value.</typeparam>
      <returns>The transformed final accumulator value.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="updateAccumulatorFunc" /> or <paramref name="combineAccumulatorsFunc" /> or <paramref name="resultSelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Aggregate``3(System.Linq.ParallelQuery{``0},``1,System.Func{``1,``0,``1},System.Func{``1,``2})">
      <summary>Applies in parallel an accumulator function over a sequence. The specified seed value is used as the initial accumulator value, and the specified function is used to select the result value.</summary>
      <param name="source">A sequence to aggregate over.</param>
      <param name="seed">The initial accumulator value.</param>
      <param name="func">An accumulator function to be invoked on each element.</param>
      <param name="resultSelector">A function to transform the final accumulator value into the result value.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
      <typeparam name="TResult">The type of the resulting value.</typeparam>
      <returns>The transformed final accumulator value.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="func" /> or <paramref name="resultSelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Aggregate``3(System.Linq.ParallelQuery{``0},System.Func{``1},System.Func{``1,``0,``1},System.Func{``1,``1,``1},System.Func{``1,``2})">
      <summary>Applies in parallel an accumulator function over a sequence. This overload is not available in the sequential implementation.</summary>
      <param name="source">A sequence to aggregate over.</param>
      <param name="seedFactory">A function that returns the initial accumulator value.</param>
      <param name="updateAccumulatorFunc">An accumulator function to be invoked on each element in a partition.</param>
      <param name="combineAccumulatorsFunc">An accumulator function to be invoked on the yielded accumulator result from each partition.</param>
      <param name="resultSelector">A function to transform the final accumulator value into the result value.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
      <typeparam name="TResult">The type of the resulting value.</typeparam>
      <returns>The transformed final accumulator value.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="seedFactory" /> or <paramref name="updateAccumulatorFunc" /> or <paramref name="combineAccumulatorsFunc" /> or <paramref name="resultSelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.All``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Boolean})">
      <summary>Determines in parallel whether all elements of a sequence satisfy a condition.</summary>
      <param name="source">A sequence whose elements to apply the predicate to.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="true" /> if every element of the source sequence passes the test in the specified predicate, or if the sequence is empty; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Any``1(System.Linq.ParallelQuery{``0})">
      <summary>Determines whether a parallel sequence contains any elements.</summary>
      <param name="source">The sequence to check for emptiness.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="true" /> if the source sequence contains any elements; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Any``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Boolean})">
      <summary>Determines in parallel whether any element of a sequence satisfies a condition.</summary>
      <param name="source">A sequence to whose elements the predicate will be applied.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="true" /> if any elements in the source sequence pass the test in the specified predicate; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.AsEnumerable``1(System.Linq.ParallelQuery{``0})">
      <summary>Converts a <see cref="T:System.Linq.ParallelQuery`1" /> into an <see cref="T:System.Collections.Generic.IEnumerable`1" /> to force sequential evaluation of the query.</summary>
      <param name="source">The sequence to cast as <see cref="T:System.Collections.Generic.IEnumerable`1" />.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The input sequence typed as <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.AsOrdered(System.Linq.ParallelQuery)">
      <summary>Enables treatment of a data source as if it were ordered, overriding the default of unordered. AsOrdered may only be invoked on non-generic sequences returned by AsParallel, ParallelEnumerable.Range, and ParallelEnumerable.Repeat.</summary>
      <param name="source">The input sequence.</param>
      <returns>The source sequence which will maintain the original ordering in the subsequent query operators.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">thrown if <paramref name="source" /> contains no elements
-or-
if AsOrdered is called midway through a query. It is allowed to be called immediately after <see cref="M:System.Linq.ParallelEnumerable.AsParallel(System.Collections.IEnumerable)" />, <see cref="M:System.Linq.ParallelEnumerable.Range(System.Int32,System.Int32)" /> or <see cref="M:System.Linq.ParallelEnumerable.Repeat``1(``0,System.Int32)" />.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.AsOrdered``1(System.Linq.ParallelQuery{``0})">
      <summary>Enables treatment of a data source as if it were ordered, overriding the default of unordered. AsOrdered may only be invoked on generic sequences returned by AsParallel, ParallelEnumerable.Range, and ParallelEnumerable.Repeat.</summary>
      <param name="source">The input sequence.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The source sequence which will maintain the original ordering in the subsequent query operators.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">Thrown if <paramref name="source" /> contains no elements
-or-
if <paramref name="source" /> is not one of AsParallel, ParallelEnumerable.Range, or ParallelEnumerable.Repeat.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.AsParallel(System.Collections.IEnumerable)">
      <summary>Enables parallelization of a query.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to convert to a <see cref="T:System.Linq.ParallelQuery" />.</param>
      <returns>The source as a ParallelQuery to bind to ParallelEnumerable extension methods.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.AsParallel``1(System.Collections.Concurrent.Partitioner{``0})">
      <summary>Enables parallelization of a query, as sourced by a custom partitioner that is responsible for splitting the input sequence into partitions.</summary>
      <param name="source">A partitioner over the input sequence.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The <paramref name="source" /> as a ParallelQuery to bind to ParallelEnumerable extension methods.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.AsParallel``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Enables parallelization of a query.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to convert to a <see cref="T:System.Linq.ParallelQuery`1" />.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The source as a <see cref="T:System.Linq.ParallelQuery`1" /> to bind to ParallelEnumerable extension methods.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.AsSequential``1(System.Linq.ParallelQuery{``0})">
      <summary>Converts a <see cref="T:System.Linq.ParallelQuery`1" /> into an <see cref="T:System.Collections.Generic.IEnumerable`1" /> to force sequential evaluation of the query.</summary>
      <param name="source">A <see cref="T:System.Linq.ParallelQuery`1" /> to convert to an <see cref="T:System.Collections.Generic.IEnumerable`1" />.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The source as an <see cref="T:System.Collections.Generic.IEnumerable`1" /> to bind to sequential extension methods.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.AsUnordered``1(System.Linq.ParallelQuery{``0})">
      <summary>Allows an intermediate query to be treated as if no ordering is implied among the elements.</summary>
      <param name="source">The input sequence.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The source sequence with arbitrary order.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average(System.Linq.ParallelQuery{System.Decimal})">
      <summary>Computes in parallel the average of a sequence of values.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average(System.Linq.ParallelQuery{System.Double})">
      <summary>Computes in parallel the average of a sequence of values.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average(System.Linq.ParallelQuery{System.Int32})">
      <summary>Computes in parallel the average of a sequence of values.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum or count of the elements in the sequence is larger than <see cref="F:System.Int32.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average(System.Linq.ParallelQuery{System.Int64})">
      <summary>Computes in parallel the average of a sequence of values.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum or count of the elements in the sequence is larger than <see cref="F:System.Int32.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average(System.Linq.ParallelQuery{System.Nullable{System.Decimal}})">
      <summary>Computes in parallel the average of a sequence of values.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average(System.Linq.ParallelQuery{System.Nullable{System.Double}})">
      <summary>Computes in parallel the average of a sequence of values.</summary>
      <param name="source">The source sequence.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">A sequence of values that are used to calculate an average.The average of the sequence of values.<paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average(System.Linq.ParallelQuery{System.Nullable{System.Int32}})">
      <summary>Computes in parallel the average of a sequence of values.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum or count of the elements in the sequence is larger than <see cref="F:System.Int32.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average(System.Linq.ParallelQuery{System.Nullable{System.Int64}})">
      <summary>Computes in parallel the average of a sequence of values.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum or count of the elements in the sequence is larger than <see cref="F:System.Int32.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average(System.Linq.ParallelQuery{System.Nullable{System.Single}})">
      <summary>Computes in parallel the average of a sequence of values.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average(System.Linq.ParallelQuery{System.Single})">
      <summary>Computes in parallel the average of a sequence of values.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Decimal})">
      <summary>Computes in parallel the average of a sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
      <exception cref="T:System.OverflowException">(Thrown as inner exception in an <see cref="T:System.AggregateException" />). The <paramref name="selector" /> function returns a value greater than MaxValue for the element type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Double})">
      <summary>Computes in parallel the average of a sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
      <exception cref="T:System.OverflowException">(Thrown as inner exception in an <see cref="T:System.AggregateException" />). The <paramref name="selector" /> function returns a value greater than MaxValue for the element type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Int32})">
      <summary>Computes in parallel the average of a sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum or count of the elements in the sequence is larger than <see cref="F:System.Int32.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
      <exception cref="T:System.OverflowException">(Thrown as inner exception in an <see cref="T:System.AggregateException" />). The <paramref name="selector" /> function returns a value greater than MaxValue for the element type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Int64})">
      <summary>Computes in parallel the average of a sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum or count of the elements in the sequence is larger than <see cref="F:System.Int32.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
      <exception cref="T:System.OverflowException">(Thrown as inner exception in an <see cref="T:System.AggregateException" />). The <paramref name="selector" /> function returns a value greater than MaxValue for the element type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Decimal}})">
      <summary>Computes in parallel the average of a sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
      <exception cref="T:System.OverflowException">(Thrown as inner exception in an <see cref="T:System.AggregateException" />). The <paramref name="selector" /> function returns a value greater than MaxValue for the element type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Double}})">
      <summary>Computes in parallel the average of a sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
      <exception cref="T:System.OverflowException">(Thrown as inner exception in an <see cref="T:System.AggregateException" />). The <paramref name="selector" /> function returns a value greater than MaxValue for the element type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Int32}})">
      <summary>Computes in parallel the average of a sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum or count of the elements in the sequence is larger than <see cref="F:System.Int32.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
      <exception cref="T:System.OverflowException">(Thrown as inner exception in an <see cref="T:System.AggregateException" />). The <paramref name="selector" /> function returns a value greater than MaxValue for the element type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Int64}})">
      <summary>Computes in parallel the average of a sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum or count of the elements in the sequence is larger than <see cref="F:System.Int64.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
      <exception cref="T:System.OverflowException">(Thrown as inner exception in an <see cref="T:System.AggregateException" />). The <paramref name="selector" /> function returns a value greater than MaxValue for the element type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Single}})">
      <summary>Computes in parallel the average of a sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
      <exception cref="T:System.OverflowException">(Thrown as inner exception in an <see cref="T:System.AggregateException" />). The <paramref name="selector" /> function returns a value greater than MaxValue for the element type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Average``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Single})">
      <summary>Computes in parallel the average of a sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
      <exception cref="T:System.OverflowException">(Thrown as inner exception in an <see cref="T:System.AggregateException" />). The <paramref name="selector" /> function returns a value greater than MaxValue for the element type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Cast``1(System.Linq.ParallelQuery)">
      <summary>Converts the elements of a ParallelQuery to the specified type.</summary>
      <param name="source">The sequence that contains the elements to be converted.</param>
      <typeparam name="TResult">The type to convert the elements of <paramref name="source" /> to.</typeparam>
      <returns>A sequence that contains each element of the source sequence converted to the specified type.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidCastException">The type of the source sequence could not be converted to <paramref name="TResult" />.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Concat``1(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>This Concat overload should never be called. This method is marked as obsolete and always throws <see cref="T:System.NotSupportedException" /> when called.</summary>
      <param name="first">This parameter is not used.</param>
      <param name="second">This parameter is not used.</param>
      <typeparam name="TSource">This type parameter is not used.</typeparam>
      <returns>This overload always throws a <see cref="T:System.NotSupportedException" />.</returns>
      <exception cref="T:System.NotSupportedException">The exception that occurs when this method is called.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Concat``1(System.Linq.ParallelQuery{``0},System.Linq.ParallelQuery{``0})">
      <summary>Concatenates two parallel sequences.</summary>
      <param name="first">The first sequence to concatenate.</param>
      <param name="second">The sequence to concatenate to the first sequence.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>A sequence that contains the concatenated elements of the two input sequences.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is a null reference (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Contains``1(System.Linq.ParallelQuery{``0},``0)">
      <summary>Determines in parallel whether a sequence contains a specified element by using the default equality comparer.</summary>
      <param name="source">A sequence in which to locate a value.</param>
      <param name="value">The value to locate in the sequence.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="true" /> if the source sequence contains an element that has the specified value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Contains``1(System.Linq.ParallelQuery{``0},``0,System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Determines in parallel whether a sequence contains a specified element by using a specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <param name="source">A sequence in which to locate a value.</param>
      <param name="value">The value to locate in the sequence.</param>
      <param name="comparer">An equality comparer to compare values.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="true" /> if the source sequence contains an element that has the specified value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Count``1(System.Linq.ParallelQuery{``0})">
      <summary>Returns the number of elements in a parallel sequence.</summary>
      <param name="source">A sequence that contains elements to be counted.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The number of elements in the input sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The number of elements in source is larger than <see cref="F:System.Int32.MaxValue" />. (In this case the InnerException is <see cref="T:System.OverflowException" />) -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Count``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Boolean})">
      <summary>Returns a number that represents how many elements in the specified parallel sequence satisfy a condition.</summary>
      <param name="source">A sequence that contains elements to be counted.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A number that represents how many elements in the sequence satisfy the condition in the predicate function.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The number of elements in source is larger than <see cref="F:System.Int32.MaxValue" />. (In this case the InnerException is <see cref="T:System.OverflowException" />) -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.DefaultIfEmpty``1(System.Linq.ParallelQuery{``0})">
      <summary>Returns the elements of the specified parallel sequence or the type parameter's default value in a singleton collection if the sequence is empty.</summary>
      <param name="source">The sequence to return a default value for if it is empty.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A sequence that contains default(TSource) if <paramref name="source" /> is empty; otherwise, <paramref name="source" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.DefaultIfEmpty``1(System.Linq.ParallelQuery{``0},``0)">
      <summary>Returns the elements of the specified parallel sequence or the specified value in a singleton collection if the sequence is empty.</summary>
      <param name="source">The sequence to return the specified value for if it is empty.</param>
      <param name="defaultValue">The value to return if the sequence is empty.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A sequence that contains defaultValue if <paramref name="source" /> is empty; otherwise, <paramref name="source" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Distinct``1(System.Linq.ParallelQuery{``0})">
      <summary>Returns distinct elements from a parallel sequence by using the default equality comparer to compare values.</summary>
      <param name="source">The sequence to remove duplicate elements from.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A sequence that contains distinct elements from the source sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Distinct``1(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Returns distinct elements from a parallel sequence by using a specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</summary>
      <param name="source">The sequence to remove duplicate elements from.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A sequence that contains distinct elements from the source sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ElementAt``1(System.Linq.ParallelQuery{``0},System.Int32)">
      <summary>Returns the element at a specified index in a parallel sequence.</summary>
      <param name="source">A sequence to return an element from.</param>
      <param name="index">The zero-based index of the element to retrieve.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The element at the specified position in the source sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0 or greater than or equal to the number of elements in <paramref name="source" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ElementAtOrDefault``1(System.Linq.ParallelQuery{``0},System.Int32)">
      <summary>Returns the element at a specified index in a parallel sequence or a default value if the index is out of range.</summary>
      <param name="source">A sequence to return an element from.</param>
      <param name="index">The zero-based index of the element to retrieve.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>default(TSource) if the index is outside the bounds of the source sequence; otherwise, the element at the specified position in the source sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Empty``1">
      <summary>Returns an empty ParallelQuery{TResult} that has the specified type argument.</summary>
      <typeparam name="TResult">The type to assign to the type parameter of the returned generic sequence.</typeparam>
      <returns>An empty sequence whose type argument is <paramref name="TResult" />.</returns>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Except``1(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>This Except overload should never be called. This method is marked as obsolete and always throws <see cref="T:System.NotSupportedException" /> when called.</summary>
      <param name="first">This parameter is not used.</param>
      <param name="second">This parameter is not used.</param>
      <typeparam name="TSource">This type parameter is not used.</typeparam>
      <returns>This overload always throws a <see cref="T:System.NotSupportedException" />.</returns>
      <exception cref="T:System.NotSupportedException">The exception that occurs when this method is called.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Except``1(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>This Except overload should never be called. This method is marked as obsolete and always throws <see cref="T:System.NotSupportedException" /> when called.</summary>
      <param name="first">This parameter is not used.</param>
      <param name="second">This parameter is not used.</param>
      <param name="comparer">This parameter is not used.</param>
      <typeparam name="TSource">This type parameter is not used.</typeparam>
      <returns>This overload always throws a <see cref="T:System.NotSupportedException" />.</returns>
      <exception cref="T:System.NotSupportedException">The exception that occurs when this method is called.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Except``1(System.Linq.ParallelQuery{``0},System.Linq.ParallelQuery{``0})">
      <summary>Produces the set difference of two parallel sequences by using the default equality comparer to compare values.</summary>
      <param name="first">A sequence whose elements that are not also in <paramref name="second" /> will be returned.</param>
      <param name="second">A sequence whose elements that also occur in the first sequence will cause those elements to be removed from the returned sequence.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>A sequence that contains the set difference of the elements of two sequences.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Except``1(System.Linq.ParallelQuery{``0},System.Linq.ParallelQuery{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Produces the set difference of two parallel sequences by using the specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</summary>
      <param name="first">A sequence whose elements that are not also in <paramref name="second" /> will be returned.</param>
      <param name="second">A sequence whose elements that also occur in the first sequence will cause those elements to be removed from the returned sequence.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>A sequence that contains the set difference of the elements of two sequences.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.First``1(System.Linq.ParallelQuery{``0})">
      <summary>Returns the first element of a parallel sequence.</summary>
      <param name="source">The sequence to return the first element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The first element in the specified sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.First``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the first element in a parallel sequence that satisfies a specified condition.</summary>
      <param name="source">The sequence to return an element from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The first element in the sequence that passes the test in the specified predicate function.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">No element in <paramref name="source" /> satisfies the condition in <paramref name="predicate" />.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.FirstOrDefault``1(System.Linq.ParallelQuery{``0})">
      <summary>Returns the first element of a parallel sequence, or a default value if the sequence contains no elements.</summary>
      <param name="source">The sequence to return the first element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>default(TSource) if <paramref name="source" /> is empty; otherwise, the first element in <paramref name="source" />.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.FirstOrDefault``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the first element of the parallel sequence that satisfies a condition or a default value if no such element is found.</summary>
      <param name="source">The sequence to return an element from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>default(TSource) if <paramref name="source" /> is empty or if no element passes the test specified by predicate; otherwise, the first element in <paramref name="source" /> that passes the test specified by predicate.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ForAll``1(System.Linq.ParallelQuery{``0},System.Action{``0})">
      <summary>Invokes in parallel the specified action for each element in the <paramref name="source" />.</summary>
      <param name="source">The <see cref="T:System.Linq.ParallelQuery`1" /> whose elements will be processed by <paramref name="action" />.</param>
      <param name="action">An Action to invoke on each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.GroupBy``2(System.Linq.ParallelQuery{``0},System.Func{``0,``1})">
      <summary>Groups in parallel the elements of a sequence according to a specified key selector function.</summary>
      <param name="source">An OrderedParallelQuery{TSource}that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>A sequence of groups that are sorted descending according to <paramref name="TKey" />.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.GroupBy``2(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Groups in parallel the elements of a sequence according to a specified key selector function and compares the keys by using a specified <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <param name="source">An <see cref="T:System.Linq.OrderedParallelQuery`1" /> that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />&gt;.</typeparam>
      <returns>A sequence of groups that are sorted descending according to <paramref name="TKey" />.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.GroupBy``3(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Func{``0,``2})">
      <summary>Groups in parallel the elements of a sequence according to a specified key selector function and projects the elements for each group by using a specified function.</summary>
      <param name="source">An <see cref="T:System.Linq.OrderedParallelQuery`1" /> that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <param name="elementSelector">A function to map each source element to an element in an <see cref="T:System.Linq.IGrouping`2" />.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the elements in the <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <returns>A sequence of groups that are sorted descending according to <paramref name="TKey" />.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.GroupBy``3(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Func{``0,``2},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Groups in parallel the elements of a sequence according to a key selector function. The keys are compared by using a comparer and each group's elements are projected by using a specified function.</summary>
      <param name="source">An OrderedParallelQuery{TSource}that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <param name="elementSelector">A function to map each source element to an element in an IGrouping.</param>
      <param name="comparer">An IComparer{TSource} to compare keys.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the elements in the IGrouping</typeparam>
      <returns>A sequence of groups that are sorted descending according to <paramref name="TKey" />.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.GroupBy``3(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2})">
      <summary>Groups in parallel the elements of a sequence according to a specified key selector function and creates a result value from each group and its key.</summary>
      <param name="source">A sequence whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="resultSelector">A function to create a result value from each group.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TResult">The type of the result value returned by <paramref name="resultSelector" />.</typeparam>
      <returns>A sequence of elements of type <paramref name="TResult" /> where each element represents a projection over a group and its key.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.GroupBy``3(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Groups in parallel the elements of a sequence according to a specified key selector function and creates a result value from each group and its key. The keys are compared by using a specified comparer.</summary>
      <param name="source">A sequence whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="resultSelector">A function to create a result value from each group.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TResult">The type of the result value returned by <paramref name="resultSelector" />.</typeparam>
      <returns>A sequence of groups.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.GroupBy``4(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Func{``0,``2},System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3})">
      <summary>Groups in parallel the elements of a sequence according to a specified key selector function and creates a result value from each group and its key. The elements of each group are projected by using a specified function.</summary>
      <param name="source">A sequence whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="elementSelector">A function to map each source element to an element in an IGrouping&lt;TKey, TElement&gt;.</param>
      <param name="resultSelector">A function to create a result value from each group.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the elements in each IGrouping{TKey, TElement}.</typeparam>
      <typeparam name="TResult">The type of the result value returned by <paramref name="resultSelector" />.</typeparam>
      <returns>A sequence of elements of type <paramref name="TResult" /> where each element represents a projection over a group and its key.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.GroupBy``4(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Func{``0,``2},System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Groups the elements of a sequence according to a specified key selector function and creates a result value from each group and its key. Key values are compared by using a specified comparer, and the elements of each group are projected by using a specified function.</summary>
      <param name="source">A sequence whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="elementSelector">A function to map each source element to an element in an IGrouping{Key, TElement}.</param>
      <param name="resultSelector">A function to create a result value from each group.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the elements in each IGrouping{TKey, TElement}.</typeparam>
      <typeparam name="TResult">The type of the result value returned by <paramref name="resultSelector" />.</typeparam>
      <returns>A sequence of elements of type <paramref name="TResult" /> where each element represents a projection over a group and its key.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.GroupJoin``4(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3})">
      <summary>This GroupJoin overload should never be called. This method is marked as obsolete and always throws <see cref="T:System.NotSupportedException" /> when called.</summary>
      <param name="outer">This parameter is not used.</param>
      <param name="inner">This parameter is not used.</param>
      <param name="outerKeySelector">This parameter is not used.</param>
      <param name="innerKeySelector">This parameter is not used.</param>
      <param name="resultSelector">This parameter is not used.</param>
      <typeparam name="TOuter">This type parameter is not used.</typeparam>
      <typeparam name="TInner">This type parameter is not used.</typeparam>
      <typeparam name="TKey">This type parameter is not used.</typeparam>
      <typeparam name="TResult">This type parameter is not used.</typeparam>
      <returns>This overload always throws a <see cref="T:System.NotSupportedException" />.</returns>
      <exception cref="T:System.NotSupportedException">The exception that occurs when this method is called.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.GroupJoin``4(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>This GroupJoin overload should never be called. This method is marked as obsolete and always throws <see cref="T:System.NotSupportedException" /> when called.</summary>
      <param name="outer">This parameter is not used.</param>
      <param name="inner">This parameter is not used.</param>
      <param name="outerKeySelector">This parameter is not used.</param>
      <param name="innerKeySelector">This parameter is not used.</param>
      <param name="resultSelector">This parameter is not used.</param>
      <param name="comparer">This parameter is not used.</param>
      <typeparam name="TOuter">This type parameter is not used.</typeparam>
      <typeparam name="TInner">This type parameter is not used.</typeparam>
      <typeparam name="TKey">This type parameter is not used.</typeparam>
      <typeparam name="TResult">This type parameter is not used.</typeparam>
      <returns>This overload always throws a <see cref="T:System.NotSupportedException" />.</returns>
      <exception cref="T:System.NotSupportedException">The exception that occurs when this method is called.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.GroupJoin``4(System.Linq.ParallelQuery{``0},System.Linq.ParallelQuery{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3})">
      <summary>Correlates in parallel the elements of two sequences based on equality of keys and groups the results. The default equality comparer is used to compare keys.</summary>
      <param name="outer">The first sequence to join.</param>
      <param name="inner">The sequence to join to the first sequence.</param>
      <param name="outerKeySelector">A function to extract the join key from each element of the first sequence.</param>
      <param name="innerKeySelector">A function to extract the join key from each element of the second sequence.</param>
      <param name="resultSelector">A function to create a result element from an element from the first sequence and a collection of matching elements from the second sequence.</param>
      <typeparam name="TOuter">The type of the elements of the first sequence.</typeparam>
      <typeparam name="TInner">The type of the elements of the second sequence.</typeparam>
      <typeparam name="TKey">The type of the keys returned by the key selector functions.</typeparam>
      <typeparam name="TResult">The type of the result elements.</typeparam>
      <returns>A sequence that has elements of type <paramref name="TResult" /> that are obtained by performing a grouped join on two sequences.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.GroupJoin``4(System.Linq.ParallelQuery{``0},System.Linq.ParallelQuery{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>Correlates in parallel the elements of two sequences based on key equality and groups the results. A specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> is used to compare keys.</summary>
      <param name="outer">The first sequence to join.</param>
      <param name="inner">The sequence to join to the first sequence.</param>
      <param name="outerKeySelector">A function to extract the join key from each element of the first sequence.</param>
      <param name="innerKeySelector">A function to extract the join key from each element of the second sequence.</param>
      <param name="resultSelector">A function to create a result element from an element from the first sequence and a collection of matching elements from the second sequence.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to hash and compare keys.</param>
      <typeparam name="TOuter">The type of the elements of the first sequence.</typeparam>
      <typeparam name="TInner">The type of the elements of the second sequence.</typeparam>
      <typeparam name="TKey">The type of the keys returned by the key selector functions.</typeparam>
      <typeparam name="TResult">The type of the result elements.</typeparam>
      <returns>A sequence that has elements of type <paramref name="TResult" /> that are obtained by performing a grouped join on two sequences.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Intersect``1(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>This Intersect overload should never be called. This method is marked as obsolete and always throws <see cref="T:System.NotSupportedException" /> when called.</summary>
      <param name="first">This parameter is not used.</param>
      <param name="second">This parameter is not used.</param>
      <typeparam name="TSource">This type parameter is not used.</typeparam>
      <returns>This overload always throws a <see cref="T:System.NotSupportedException" />.</returns>
      <exception cref="T:System.NotSupportedException">The exception that occurs when this method is called.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Intersect``1(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>This Intersect overload should never be called. This method is marked as obsolete and always throws <see cref="T:System.NotSupportedException" /> when called.</summary>
      <param name="first">This parameter is not used.</param>
      <param name="second">This parameter is not used.</param>
      <param name="comparer">This parameter is not used.</param>
      <typeparam name="TSource">This type parameter is not used.</typeparam>
      <returns>This overload always throws a <see cref="T:System.NotSupportedException" />.</returns>
      <exception cref="T:System.NotSupportedException">The exception that occurs when this method is called.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Intersect``1(System.Linq.ParallelQuery{``0},System.Linq.ParallelQuery{``0})">
      <summary>Produces the set intersection of two parallel sequences by using the default equality comparer to compare values.</summary>
      <param name="first">A sequence whose distinct elements that also appear in <paramref name="second" /> will be returned.</param>
      <param name="second">A sequence whose distinct elements that also appear in the first sequence will be returned.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>A sequence that contains the elements that form the set intersection of two sequences.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Intersect``1(System.Linq.ParallelQuery{``0},System.Linq.ParallelQuery{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Produces the set intersection of two parallel sequences by using the specified IEqualityComparer{T} to compare values.</summary>
      <param name="first">A sequence whose distinct elements that also appear in <paramref name="second" /> will be returned.</param>
      <param name="second">A sequence whose distinct elements that also appear in the first sequence will be returned.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>A sequence that contains the elements that form the set intersection of two sequences.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Join``4(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,``1,``3})">
      <summary>This Join overload should never be called. This method is marked as obsolete and always throws <see cref="T:System.NotSupportedException" /> when invoked.</summary>
      <param name="outer">This parameter is not used.</param>
      <param name="inner">This parameter is not used.</param>
      <param name="outerKeySelector">This parameter is not used.</param>
      <param name="innerKeySelector">This parameter is not used.</param>
      <param name="resultSelector">This parameter is not used.</param>
      <typeparam name="TOuter">This type parameter is not used.</typeparam>
      <typeparam name="TInner">This type parameter is not used.</typeparam>
      <typeparam name="TKey">This type parameter is not used.</typeparam>
      <typeparam name="TResult">This type parameter is not used.</typeparam>
      <returns>This overload always throws a <see cref="T:System.NotSupportedException" />.</returns>
      <exception cref="T:System.NotSupportedException">The exception that occurs when this method is called.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Join``4(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,``1,``3},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>This Join overload should never be called. This method is marked as obsolete and always throws <see cref="T:System.NotSupportedException" /> when invoked.</summary>
      <param name="outer">This parameter is not used.</param>
      <param name="inner">This parameter is not used.</param>
      <param name="outerKeySelector">This parameter is not used.</param>
      <param name="innerKeySelector">This parameter is not used.</param>
      <param name="resultSelector">This parameter is not used.</param>
      <param name="comparer">This parameter is not used.</param>
      <typeparam name="TOuter">This type parameter is not used.</typeparam>
      <typeparam name="TInner">This type parameter is not used.</typeparam>
      <typeparam name="TKey">This type parameter is not used.</typeparam>
      <typeparam name="TResult">This type parameter is not used.</typeparam>
      <returns>This overload always throws a <see cref="T:System.NotSupportedException" />.</returns>
      <exception cref="T:System.NotSupportedException">The exception that occurs when this method is called.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Join``4(System.Linq.ParallelQuery{``0},System.Linq.ParallelQuery{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,``1,``3})">
      <summary>Correlates in parallel the elements of two sequences based on matching keys. The default equality comparer is used to compare keys.</summary>
      <param name="outer">The first sequence to join.</param>
      <param name="inner">The sequence to join to the first sequence.</param>
      <param name="outerKeySelector">A function to extract the join key from each element of the first sequence.</param>
      <param name="innerKeySelector">A function to extract the join key from each element of the second sequence.</param>
      <param name="resultSelector">A function to create a result element from two matching elements.</param>
      <typeparam name="TOuter">The type of the elements of the first sequence.</typeparam>
      <typeparam name="TInner">The type of the elements of the second sequence.</typeparam>
      <typeparam name="TKey">The type of the keys returned by the key selector functions.</typeparam>
      <typeparam name="TResult">The type of the result elements.</typeparam>
      <returns>A sequence that has elements of type <paramref name="TResult" /> that are obtained by performing an inner join on two sequences.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Join``4(System.Linq.ParallelQuery{``0},System.Linq.ParallelQuery{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,``1,``3},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>Correlates in parallel the elements of two sequences based on matching keys. A specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> is used to compare keys.</summary>
      <param name="outer">The first sequence to join.</param>
      <param name="inner">The sequence to join to the first sequence.</param>
      <param name="outerKeySelector">A function to extract the join key from each element of the first sequence.</param>
      <param name="innerKeySelector">A function to extract the join key from each element of the second sequence.</param>
      <param name="resultSelector">A function to create a result element from two matching elements.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to hash and compare keys.</param>
      <typeparam name="TOuter">The type of the elements of the first sequence.</typeparam>
      <typeparam name="TInner">The type of the elements of the second sequence.</typeparam>
      <typeparam name="TKey">The type of the keys returned by the key selector functions.</typeparam>
      <typeparam name="TResult">The type of the result elements.</typeparam>
      <returns>A sequence that has elements of type <paramref name="TResult" /> that are obtained by performing an inner join on two sequences.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="action" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Last``1(System.Linq.ParallelQuery{``0})">
      <summary>Returns the last element of a parallel sequence.</summary>
      <param name="source">The sequence to return the last element from.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The value at the last position in the source sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Last``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the last element of a parallel sequence that satisfies a specified condition.</summary>
      <param name="source">The sequence to return an element from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The last element in the sequence that passes the test in the specified predicate function.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">No element in <paramref name="source" /> satisfies the condition in <paramref name="predicate" />.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.LastOrDefault``1(System.Linq.ParallelQuery{``0})">
      <summary>Returns the last element of a parallel sequence, or a default value if the sequence contains no elements.</summary>
      <param name="source">The sequence to return an element from.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>default() if the source sequence is empty; otherwise, the last element in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.LastOrDefault``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the last element of a parallel sequence that satisfies a condition, or a default value if no such element is found.</summary>
      <param name="source">The sequence to return an element from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>default() if the sequence is empty or if no elements pass the test in the predicate function; otherwise, the last element that passes the test in the predicate function.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.LongCount``1(System.Linq.ParallelQuery{``0})">
      <summary>Returns an Int64 that represents the total number of elements in a parallel sequence.</summary>
      <param name="source">A sequence that contains elements to be counted.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The number of elements in the input sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The number of elements in source is larger than <see cref="F:System.Int64.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.OverflowException">The computed result is greater than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.LongCount``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Boolean})">
      <summary>Returns an Int64 that represents how many elements in a parallel sequence satisfy a condition.</summary>
      <param name="source">A sequence that contains elements to be counted.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A number that represents how many elements in the sequence satisfy the condition in the predicate function.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The number of elements in source is larger than <see cref="F:System.Int64.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.OverflowException">The computed result is greater than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max(System.Linq.ParallelQuery{System.Decimal})">
      <summary>Returns the maximum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max(System.Linq.ParallelQuery{System.Double})">
      <summary>Returns the maximum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max(System.Linq.ParallelQuery{System.Int32})">
      <summary>Returns the maximum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max(System.Linq.ParallelQuery{System.Int64})">
      <summary>Returns the maximum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max(System.Linq.ParallelQuery{System.Nullable{System.Decimal}})">
      <summary>Returns the maximum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max(System.Linq.ParallelQuery{System.Nullable{System.Double}})">
      <summary>Returns the maximum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max(System.Linq.ParallelQuery{System.Nullable{System.Int32}})">
      <summary>Returns the maximum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max(System.Linq.ParallelQuery{System.Nullable{System.Int64}})">
      <summary>Returns the maximum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max(System.Linq.ParallelQuery{System.Nullable{System.Single}})">
      <summary>Returns the maximum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max(System.Linq.ParallelQuery{System.Single})">
      <summary>Returns the maximum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max``1(System.Linq.ParallelQuery{``0})">
      <summary>Returns the maximum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <typeparam name="TSource">The type of elements in the source sequence.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Decimal})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the maximum value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Double})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the maximum value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Int32})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the maximum value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Int64})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the maximum value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Decimal}})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the maximum value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Double}})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the maximum value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Int32}})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the maximum value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Int64}})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the maximum value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Single}})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the maximum value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Single})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the maximum value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Max``2(System.Linq.ParallelQuery{``0},System.Func{``0,``1})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the maximum value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the value returned by <paramref name="selector" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min(System.Linq.ParallelQuery{System.Decimal})">
      <summary>Returns the minimum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min(System.Linq.ParallelQuery{System.Double})">
      <summary>Returns the minimum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min(System.Linq.ParallelQuery{System.Int32})">
      <summary>Returns the minimum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min(System.Linq.ParallelQuery{System.Int64})">
      <summary>Returns the minimum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min(System.Linq.ParallelQuery{System.Nullable{System.Decimal}})">
      <summary>Returns the minimum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min(System.Linq.ParallelQuery{System.Nullable{System.Double}})">
      <summary>Returns the minimum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min(System.Linq.ParallelQuery{System.Nullable{System.Int32}})">
      <summary>Returns the minimum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min(System.Linq.ParallelQuery{System.Nullable{System.Int64}})">
      <summary>Returns the minimum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min(System.Linq.ParallelQuery{System.Nullable{System.Single}})">
      <summary>Returns the minimum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min(System.Linq.ParallelQuery{System.Single})">
      <summary>Returns the minimum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min``1(System.Linq.ParallelQuery{``0})">
      <summary>Returns the minimum value in a parallel sequence of values.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Decimal})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the minimum value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Double})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the minimum value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Int32})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the minimum value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Int64})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the minimum value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Decimal}})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the minimum value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Double}})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the minimum value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Int32}})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the minimum value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Int64}})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the minimum value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Single}})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the minimum value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Single})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the minimum value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Min``2(System.Linq.ParallelQuery{``0},System.Func{``0,``1})">
      <summary>Invokes in parallel a transform function on each element of a sequence and returns the minimum value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the value returned by <paramref name="selector" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements and <paramref name="TSource" /> is a non-nullable value type.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.OfType``1(System.Linq.ParallelQuery)">
      <summary>Filters the elements of a ParallelQuery based on a specified type.</summary>
      <param name="source">The sequence with elements to filter.</param>
      <typeparam name="TResult">The type to filter the elements of the sequence on.</typeparam>
      <returns>A sequence that contains elements from the input sequence filtered by type.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.OrderBy``2(System.Linq.ParallelQuery{``0},System.Func{``0,``1})">
      <summary>Sorts in parallel the elements of a sequence in ascending order according to a key.</summary>
      <param name="source">A sequence of values to order.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An OrderedParallelQuery{TSource} whose elements are sorted according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.OrderBy``2(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Collections.Generic.IComparer{``1})">
      <summary>Sorts in parallel the elements of a sequence in ascending order by using a specified comparer.</summary>
      <param name="source">A sequence of values to order.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <param name="comparer">An IComparer{TKey} to compare keys.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An OrderedParallelQuery{TSource} whose elements are sorted according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.OrderByDescending``2(System.Linq.ParallelQuery{``0},System.Func{``0,``1})">
      <summary>Sorts in parallel the elements of a sequence in descending order according to a key.</summary>
      <param name="source">A sequence of values to order.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An OrderedParallelQuery{TSource} whose elements are sorted descending according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.OrderByDescending``2(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Collections.Generic.IComparer{``1})">
      <summary>Sorts the elements of a sequence in descending order by using a specified comparer.</summary>
      <param name="source">A sequence of values to order.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <param name="comparer">An IComparer{TKey} to compare keys.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An OrderedParallelQuery{TSource} whose elements are sorted descending according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="KeySelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Range(System.Int32,System.Int32)">
      <summary>Generates a parallel sequence of integral numbers within a specified range.</summary>
      <param name="start">The value of the first integer in the sequence.</param>
      <param name="count">The number of sequential integers to generate.</param>
      <returns>An IEnumerable&lt;Int32&gt; in C# or IEnumerable(Of Int32) in Visual Basic that contains a range of sequential integral numbers.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is less than 0 -or- <paramref name="start" /> + <paramref name="count" /> - 1 is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Repeat``1(``0,System.Int32)">
      <summary>Generates a parallel sequence that contains one repeated value.</summary>
      <param name="element">The value to be repeated.</param>
      <param name="count">The number of times to repeat the value in the generated sequence.</param>
      <typeparam name="TResult">The type of the value to be repeated in the result sequence.</typeparam>
      <returns>A sequence that contains a repeated value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is less than 0.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Reverse``1(System.Linq.ParallelQuery{``0})">
      <summary>Inverts the order of the elements in a parallel sequence.</summary>
      <param name="source">A sequence of values to reverse.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A sequence whose elements correspond to those of the input sequence in reverse order.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Select``2(System.Linq.ParallelQuery{``0},System.Func{``0,``1})">
      <summary>Projects in parallel each element of a sequence into a new form.</summary>
      <param name="source">A sequence of values to invoke a transform function on.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of elements returned by selector.</typeparam>
      <returns>A sequence whose elements are the result of invoking the transform function on each element of <paramref name="source" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Select``2(System.Linq.ParallelQuery{``0},System.Func{``0,System.Int32,``1})">
      <summary>Projects in parallel each element of a sequence into a new form by incorporating the element's index.</summary>
      <param name="source">A sequence of values to invoke a transform function on.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of elements returned by selector.</typeparam>
      <returns>A sequence whose elements are the result of invoking the transform function on each element of <paramref name="source" />, based on the index supplied to <paramref name="selector" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.OverflowException">More than <see cref="F:System.Int32.MaxValue" /> elements are enumerated by the query. This condition might occur in streaming scenarios.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.SelectMany``2(System.Linq.ParallelQuery{``0},System.Func{``0,System.Collections.Generic.IEnumerable{``1}})">
      <summary>Projects in parallel each element of a sequence to an <see cref="T:System.Collections.Generic.IEnumerable`1" /> and flattens the resulting sequences into one sequence.</summary>
      <param name="source">A sequence of values to project.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the elements of the sequence returned by selector.</typeparam>
      <returns>A sequence whose elements are the result of invoking the one-to-many transform function on each element of the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.SelectMany``2(System.Linq.ParallelQuery{``0},System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}})">
      <summary>Projects in parallel each element of a sequence to an <see cref="T:System.Collections.Generic.IEnumerable`1" />, and flattens the resulting sequences into one sequence. The index of each source element is used in the projected form of that element.</summary>
      <param name="source">A sequence of values to project.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the elements of the sequence returned by selector.</typeparam>
      <returns>A sequence whose elements are the result of invoking the one-to-many transform function on each element of the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.OverflowException">More than <see cref="F:System.Int32.MaxValue" /> elements are enumerated by the query.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.SelectMany``3(System.Linq.ParallelQuery{``0},System.Func{``0,System.Collections.Generic.IEnumerable{``1}},System.Func{``0,``1,``2})">
      <summary>Projects each element of a sequence to an <see cref="T:System.Collections.Generic.IEnumerable`1" />, flattens the resulting sequences into one sequence, and invokes a result selector function on each element therein.</summary>
      <param name="source">A sequence of values to project.</param>
      <param name="collectionSelector">A transform function to apply to each source element; the second parameter of the function represents the index of the source element.</param>
      <param name="resultSelector">A function to create a result element from an element from the first sequence and a collection of matching elements from the second sequence.</param>
      <typeparam name="TSource">The type of the intermediate elements collected by <paramref name="collectionSelector" />.</typeparam>
      <typeparam name="TCollection">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of elements in the result sequence.</typeparam>
      <returns>A sequence whose elements are the result of invoking the one-to-many transform function <paramref name="collectionSelector" /> on each element of <paramref name="source" /> based on the index supplied to <paramref name="collectionSelector" />, and then mapping each of those sequence elements and their corresponding source element to a result element.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.OverflowException">More than <see cref="F:System.Int32.MaxValue" /> elements are enumerated by the query.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.SelectMany``3(System.Linq.ParallelQuery{``0},System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}},System.Func{``0,``1,``2})">
      <summary>Projects each element of a sequence to an <see cref="T:System.Collections.Generic.IEnumerable`1" />, flattens the resulting sequences into one sequence, and invokes a result selector function on each element therein. The index of each source element is used in the intermediate projected form of that element.</summary>
      <param name="source">A sequence of values to project.</param>
      <param name="collectionSelector">A transform function to apply to each source element; the second parameter of the function represents the index of the source element.</param>
      <param name="resultSelector">A function to create a result element from an element from the first sequence and a collection of matching elements from the second sequence.</param>
      <typeparam name="TSource">The type of the intermediate elements collected by <paramref name="collectionSelector" />.</typeparam>
      <typeparam name="TCollection">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of elements to return.</typeparam>
      <returns>A sequence whose elements are the result of invoking the one-to-many transform function <paramref name="collectionSelector" /> on each element of <paramref name="source" /> based on the index supplied to <paramref name="collectionSelector" />, and then mapping each of those sequence elements and their corresponding source element to a result element.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.OverflowException">More than <see cref="F:System.Int32.MaxValue" /> elements are enumerated by the query.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.SequenceEqual``1(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>This SequenceEqual overload should never be called. This method is marked as obsolete and always throws <see cref="T:System.NotSupportedException" /> when called.</summary>
      <param name="first">This parameter is not used.</param>
      <param name="second">This parameter is not used.</param>
      <typeparam name="TSource">This type parameter is not used.</typeparam>
      <returns>This overload always throws a <see cref="T:System.NotSupportedException" />.</returns>
      <exception cref="T:System.NotSupportedException">Thrown every time this method is called.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.SequenceEqual``1(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>This SequenceEqual overload should never be called. This method is marked as obsolete and always throws <see cref="T:System.NotSupportedException" /> when called.</summary>
      <param name="first">This parameter is not used.</param>
      <param name="second">This parameter is not used.</param>
      <param name="comparer">This parameter is not used.</param>
      <typeparam name="TSource">This type parameter is not used.</typeparam>
      <returns>This overload always throws a <see cref="T:System.NotSupportedException" />.</returns>
      <exception cref="T:System.NotSupportedException">Thrown every time this method is called.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.SequenceEqual``1(System.Linq.ParallelQuery{``0},System.Linq.ParallelQuery{``0})">
      <summary>Determines whether two parallel sequences are equal by comparing the elements by using the default equality comparer for their type.</summary>
      <param name="first">A sequence to compare to second.</param>
      <param name="second">A sequence to compare to the first input sequence.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>
        <see langword="true" /> if the two source sequences are of equal length and their corresponding elements are equal according to the default equality comparer for their type; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.SequenceEqual``1(System.Linq.ParallelQuery{``0},System.Linq.ParallelQuery{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Determines whether two parallel sequences are equal by comparing their elements by using a specified IEqualityComparer{T}.</summary>
      <param name="first">A sequence to compare to <paramref name="second" />.</param>
      <param name="second">A sequence to compare to the first input sequence.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to use to compare elements.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>
        <see langword="true" /> if the two source sequences are of equal length and their corresponding elements are equal according to the default equality comparer for their type; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Single``1(System.Linq.ParallelQuery{``0})">
      <summary>Returns the only element of a parallel sequence, and throws an exception if there is not exactly one element in the sequence.</summary>
      <param name="source">The sequence to return the single element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The single element of the input sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">The input sequence contains more than one element. -or- The input sequence is empty.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Single``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the only element of a parallel sequence that satisfies a specified condition, and throws an exception if more than one such element exists.</summary>
      <param name="source">The sequence to return the single element of.</param>
      <param name="predicate">A function to test an element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The single element of the input sequence that satisfies a condition.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">No element satisfies the condition in <paramref name="predicate" />. -or- More than one element satisfies the condition in <paramref name="predicate" />.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.SingleOrDefault``1(System.Linq.ParallelQuery{``0})">
      <summary>Returns the only element of a parallel sequence, or a default value if the sequence is empty; this method throws an exception if there is more than one element in the sequence.</summary>
      <param name="source">The sequence to return the single element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The single element of the input sequence, or default() if the sequence contains no elements.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> has more than one element.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.SingleOrDefault``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the only element of a parallel sequence that satisfies a specified condition or a default value if no such element exists; this method throws an exception if more than one element satisfies the condition.</summary>
      <param name="source">The sequence to return the single element of.</param>
      <param name="predicate">A function to test an element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The single element of the input sequence that satisfies the condition, or default() if no such element is found.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> is empty or multiple elements are returned.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Skip``1(System.Linq.ParallelQuery{``0},System.Int32)">
      <summary>Bypasses a specified number of elements in a parallel sequence and then returns the remaining elements.</summary>
      <param name="source">The sequence to return elements from.</param>
      <param name="count">The number of elements to skip before returning the remaining elements.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>A sequence that contains the elements that occur after the specified index in the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.OverflowException">Count is greater than <see cref="F:System.Int32.MaxValue" /></exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.SkipWhile``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Boolean})">
      <summary>Bypasses elements in a parallel sequence as long as a specified condition is true and then returns the remaining elements.</summary>
      <param name="source">The sequence to return elements from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>A sequence that contains the elements from the input sequence starting at the first element in the linear series that does not pass the test specified by predicate.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.SkipWhile``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Int32,System.Boolean})">
      <summary>Bypasses elements in a parallel sequence as long as a specified condition is true and then returns the remaining elements. The element's index is used in the logic of the predicate function.</summary>
      <param name="source">The sequence to return elements from.</param>
      <param name="predicate">A function to test each source element for a condition; the second parameter of the function represents the index of the source element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>A sequence that contains the elements from the input sequence starting at the first element in the linear series that does not pass the test specified by predicate.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.OverflowException">More than <see cref="F:System.Int32.MaxValue" /> elements are enumerated by the query.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum(System.Linq.ParallelQuery{System.Decimal})">
      <summary>Computes in parallel the sum of a sequence of values.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Decimal.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum(System.Linq.ParallelQuery{System.Double})">
      <summary>Computes in parallel the sum of a sequence of values.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Double.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum(System.Linq.ParallelQuery{System.Int32})">
      <summary>Computes in parallel the sum of a sequence of values.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Int32.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum(System.Linq.ParallelQuery{System.Int64})">
      <summary>Computes in parallel the sum of a sequence of values.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Int64.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum(System.Linq.ParallelQuery{System.Nullable{System.Decimal}})">
      <summary>Computes in parallel the sum of a sequence of values.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Decimal.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum(System.Linq.ParallelQuery{System.Nullable{System.Double}})">
      <summary>Computes in parallel the sum of a sequence of values.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Double.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum(System.Linq.ParallelQuery{System.Nullable{System.Int32}})">
      <summary>Computes in parallel the sum of a sequence of values.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Int32.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum(System.Linq.ParallelQuery{System.Nullable{System.Int64}})">
      <summary>Computes in parallel the sum of a sequence of values.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Int64.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum(System.Linq.ParallelQuery{System.Nullable{System.Single}})">
      <summary>Computes in parallel the sum of a sequence of values.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Single.MaxValue" />. -or-  One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum(System.Linq.ParallelQuery{System.Single})">
      <summary>Computes in parallel the sum of a sequence of values.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Single.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Decimal})">
      <summary>Computes in parallel the sum of the sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Decimal.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Double})">
      <summary>Computes in parallel the sum of the sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Double.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Int32})">
      <summary>Computes in parallel the sum of the sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements in the source sequence.</typeparam>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Int32.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Int64})">
      <summary>Computes in parallel the sum of the sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Int64.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Decimal}})">
      <summary>Computes in parallel the sum of the sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Decimal.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Double}})">
      <summary>Computes in parallel the sum of the sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Double.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Int32}})">
      <summary>Computes in parallel the sum of the sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Int32.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Int64}})">
      <summary>Computes in parallel the sum of the sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Int64.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Nullable{System.Single}})">
      <summary>Computes in parallel the sum of the sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Single.MaxValue" />. -or-  One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Sum``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Single})">
      <summary>Computes in parallel the sum of the sequence of values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the sum of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values in the sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">The sum is larger than <see cref="F:System.Single.MaxValue" />. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Take``1(System.Linq.ParallelQuery{``0},System.Int32)">
      <summary>Returns a specified number of contiguous elements from the start of a parallel sequence.</summary>
      <param name="source">The sequence to return elements from.</param>
      <param name="count">The number of elements to return.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>A sequence that contains the specified number of elements from the start of the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.TakeWhile``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Boolean})">
      <summary>Returns elements from a parallel sequence as long as a specified condition is true.</summary>
      <param name="source">The sequence to return elements from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>A sequence that contains the elements from the input sequence that occur before the element at which the test no longer passes.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.TakeWhile``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Int32,System.Boolean})">
      <summary>Returns elements from a parallel sequence as long as a specified condition is true. The element's index is used in the logic of the predicate function.</summary>
      <param name="source">The sequence to return elements from.</param>
      <param name="predicate">A function to test each source element for a condition; the second parameter of the function represents the index of the source element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>A sequence that contains elements from the input sequence that occur before the element at which the test no longer passes.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.OverflowException">More than <see cref="F:System.Int32.MaxValue" /> elements are enumerated by this query.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ThenBy``2(System.Linq.OrderedParallelQuery{``0},System.Func{``0,``1})">
      <summary>Performs in parallel a subsequent ordering of the elements in a sequence in ascending order according to a key.</summary>
      <param name="source">An OrderedParallelQuery{TSource} that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An OrderedParallelQuery{TSource} whose elements are sorted according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ThenBy``2(System.Linq.OrderedParallelQuery{``0},System.Func{``0,``1},System.Collections.Generic.IComparer{``1})">
      <summary>Performs in parallel a subsequent ordering of the elements in a sequence in ascending order by using a specified comparer.</summary>
      <param name="source">An OrderedParallelQuery{TSource} that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <param name="comparer">An IComparer{TKey} to compare keys.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An OrderedParallelQuery{TSource} whose elements are sorted according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ThenByDescending``2(System.Linq.OrderedParallelQuery{``0},System.Func{``0,``1})">
      <summary>Performs in parallel a subsequent ordering of the elements in a sequence in descending order, according to a key.</summary>
      <param name="source">An OrderedParallelQuery{TSource} that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>A sequence whose elements are sorted descending according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ThenByDescending``2(System.Linq.OrderedParallelQuery{``0},System.Func{``0,``1},System.Collections.Generic.IComparer{``1})">
      <summary>Performs in parallel a subsequent ordering of the elements in a sequence in descending order by using a specified comparer.</summary>
      <param name="source">An OrderedParallelQuery{TSource} that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <param name="comparer">An IComparer{TKey} to compare keys.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>A sequence whose elements are sorted descending according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ToArray``1(System.Linq.ParallelQuery{``0})">
      <summary>Creates an array from a <see cref="T:System.Linq.ParallelQuery`1" />.</summary>
      <param name="source">A sequence to create an array from.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>An array that contains the elements from the input sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ToDictionary``2(System.Linq.ParallelQuery{``0},System.Func{``0,``1})">
      <summary>Creates a <see cref="T:System.Collections.Generic.Dictionary`2" /> from a <see cref="T:System.Linq.ParallelQuery`1" /> according to a specified key selector function.</summary>
      <param name="source">A sequence to create a <see cref="T:System.Collections.Generic.Dictionary`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>A <see cref="T:System.Collections.Generic.Dictionary`2" /> that contains keys and values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">
        <paramref name="keySelector" /> produces a key that is a null reference (Nothing in Visual Basic). -or- <paramref name="keySelector" /> produces duplicate keys for two elements. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ToDictionary``2(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Creates a <see cref="T:System.Collections.Generic.Dictionary`2" /> from a <see cref="T:System.Linq.ParallelQuery`1" /> according to a specified key selector function and key comparer.</summary>
      <param name="source">A sequence to create a <see cref="T:System.Collections.Generic.Dictionary`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>A <see cref="T:System.Collections.Generic.Dictionary`2" /> that contains keys and values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">
        <paramref name="keySelector" /> produces a key that is a null reference (Nothing in Visual Basic). -or- <paramref name="keySelector" /> produces duplicate keys for two elements. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ToDictionary``3(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Func{``0,``2})">
      <summary>Creates a <see cref="T:System.Collections.Generic.Dictionary`2" /> from a <see cref="T:System.Linq.ParallelQuery`1" /> according to specified key selector and element selector functions.</summary>
      <param name="source">A sequence to create a <see cref="T:System.Collections.Generic.Dictionary`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="elementSelector">A transform function to produce a result element value from each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the value returned by <paramref name="elementSelector" />.</typeparam>
      <returns>A <see cref="T:System.Collections.Generic.Dictionary`2" /> that contains values of type <paramref name="TElement" /> selected from the input sequence</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="elementSelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">
        <paramref name="keySelector" /> produces a key that is a null reference (Nothing in Visual Basic). -or- <paramref name="keySelector" /> produces duplicate keys for two elements. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ToDictionary``3(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Func{``0,``2},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Creates a <see cref="T:System.Collections.Generic.Dictionary`2" /> from a <see cref="T:System.Linq.ParallelQuery`1" /> according to a specified key selector function, a comparer, and an element selector function.</summary>
      <param name="source">A sequence to create a <see cref="T:System.Collections.Generic.Dictionary`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="elementSelector">A transform function to produce a result element value from each element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the value returned by <paramref name="elementSelector" />.</typeparam>
      <returns>A <see cref="T:System.Collections.Generic.Dictionary`2" /> that contains values of type <paramref name="TElement" /> selected from the input sequence</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="elementSelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">
        <paramref name="keySelector" /> produces a key that is a null reference (Nothing in Visual Basic). -or- <paramref name="keySelector" /> produces duplicate keys for two elements. -or- One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ToList``1(System.Linq.ParallelQuery{``0})">
      <summary>Creates a <see cref="T:System.Collections.Generic.List`1" /> from an <see cref="T:System.Linq.ParallelQuery`1" />.</summary>
      <param name="source">A sequence to create a <see cref="T:System.Collections.Generic.List`1" /> from.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A <see cref="T:System.Collections.Generic.List`1" /> that contains elements from the input sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ToLookup``2(System.Linq.ParallelQuery{``0},System.Func{``0,``1})">
      <summary>Creates an <see cref="T:System.Linq.ILookup`2" /> from a <see cref="T:System.Linq.ParallelQuery`1" /> according to a specified key selector function.</summary>
      <param name="source">The sequence to create a <see cref="T:System.Linq.ILookup`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>A <see cref="T:System.Linq.ILookup`2" /> that contains keys and values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ToLookup``2(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Creates an <see cref="T:System.Linq.ILookup`2" /> from a <see cref="T:System.Linq.ParallelQuery`1" /> according to a specified key selector function and key comparer.</summary>
      <param name="source">The sequence to create a <see cref="T:System.Linq.ILookup`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>A <see cref="T:System.Linq.ILookup`2" /> that contains keys and values.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ToLookup``3(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Func{``0,``2})">
      <summary>Creates an <see cref="T:System.Linq.ILookup`2" /> from a <see cref="T:System.Linq.ParallelQuery`1" /> according to specified key selector and element selector functions.</summary>
      <param name="source">The sequence to create a <see cref="T:System.Linq.ILookup`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="elementSelector">A transform function to produce a result element value from each element.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the value returned by <paramref name="elementSelector" />.</typeparam>
      <returns>A <see cref="T:System.Linq.ILookup`2" /> that contains values of type <paramref name="TElement" /> selected from the input sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="elementSelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.ToLookup``3(System.Linq.ParallelQuery{``0},System.Func{``0,``1},System.Func{``0,``2},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Creates an <see cref="T:System.Linq.ILookup`2" /> from a <see cref="T:System.Linq.ParallelQuery`1" /> according to a specified key selector function, a comparer and an element selector function.</summary>
      <param name="source">The sequence to create a <see cref="T:System.Linq.ILookup`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="elementSelector">A transform function to produce a result element value from each element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the value returned by <paramref name="elementSelector" />.</typeparam>
      <returns>A Lookup&lt;(Of &lt;(TKey, TElement&gt;)&gt;) that contains values of type TElement selected from the input sequence.</returns>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="elementSelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Union``1(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>This Union overload should never be called. This method is marked as obsolete and always throws <see cref="T:System.NotSupportedException" /> when called.</summary>
      <param name="first">This parameter is not used.</param>
      <param name="second">This parameter is not used.</param>
      <typeparam name="TSource">This type parameter is not used.</typeparam>
      <returns>This overload always throws a <see cref="T:System.NotSupportedException" />.</returns>
      <exception cref="T:System.NotSupportedException">The exception that occurs when this method is called.</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Union``1(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>This Union overload should never be called. This method is marked as obsolete and always throws <see cref="T:System.NotSupportedException" /> when called.</summary>
      <param name="first">This parameter is not used.</param>
      <param name="second">This parameter is not used.</param>
      <param name="comparer">This parameter is not used.</param>
      <typeparam name="TSource">This type parameter is not used.</typeparam>
      <returns>This overload always throws a <see cref="T:System.NotSupportedException" />.</returns>
      <exception cref="T:System.NotSupportedException">The exception that occurs when this method is called.</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Union``1(System.Linq.ParallelQuery{``0},System.Linq.ParallelQuery{``0})">
      <summary>Produces the set union of two parallel sequences by using the default equality comparer.</summary>
      <param name="first">A sequence whose distinct elements form the first set for the union.</param>
      <param name="second">A sequence whose distinct elements form the second set for the union.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>A sequence that contains the elements from both input sequences, excluding duplicates.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Union``1(System.Linq.ParallelQuery{``0},System.Linq.ParallelQuery{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Produces the set union of two parallel sequences by using a specified IEqualityComparer{T}.</summary>
      <param name="first">A sequence whose distinct elements form the first set for the union.</param>
      <param name="second">A sequence whose distinct elements form the second set for the union.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>A sequence that contains the elements from both input sequences, excluding duplicates.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Where``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Boolean})">
      <summary>Filters in parallel a sequence of values based on a predicate.</summary>
      <param name="source">A sequence to filter.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of source.</typeparam>
      <returns>A sequence that contains elements from the input sequence that satisfy the condition.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Where``1(System.Linq.ParallelQuery{``0},System.Func{``0,System.Int32,System.Boolean})">
      <summary>Filters in parallel a sequence of values based on a predicate. Each element's index is used in the logic of the predicate function.</summary>
      <param name="source">A sequence to filter.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of source.</typeparam>
      <returns>A sequence that contains elements from the input sequence that satisfy the condition.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.OverflowException">More than <see cref="F:System.Int32.MaxValue" /> elements are enumerated by the query.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.WithCancellation``1(System.Linq.ParallelQuery{``0},System.Threading.CancellationToken)">
      <summary>Sets the <see cref="T:System.Threading.CancellationToken" /> to associate with the query.</summary>
      <param name="source">A ParallelQuery on which to set the option.</param>
      <param name="cancellationToken">A cancellation token.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>ParallelQuery representing the same query as source, but with the registered cancellation token.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <paramref name="cancellationToken" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="WithCancellation" /> is used multiple times in the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.WithDegreeOfParallelism``1(System.Linq.ParallelQuery{``0},System.Int32)">
      <summary>Sets the degree of parallelism to use in a query. Degree of parallelism is the maximum number of concurrently executing tasks that will be used to process the query.</summary>
      <param name="source">A ParallelQuery on which to set the limit on the degrees of parallelism.</param>
      <param name="degreeOfParallelism">The degree of parallelism for the query. The default value is Math.Min(<see cref="P:System.Environment.ProcessorCount" />, <see langword="MAX_SUPPORTED_DOP" />) where <see langword="MAX_SUPPORTED_DOP" /> is 512.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>ParallelQuery representing the same query as source, with the limit on the degrees of parallelism set.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="degreeOfParallelism" /> is less than 1 or greater than 511.</exception>
      <exception cref="T:System.InvalidOperationException">WithDegreeOfParallelism is used multiple times in the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.WithExecutionMode``1(System.Linq.ParallelQuery{``0},System.Linq.ParallelExecutionMode)">
      <summary>Sets the execution mode of the query.</summary>
      <param name="source">A ParallelQuery on which to set the option.</param>
      <param name="executionMode">The mode in which to execute the query.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>ParallelQuery representing the same query as source, but with the registered execution mode.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="executionMode" /> is not a valid <see cref="T:System.Linq.ParallelExecutionMode" /> value.</exception>
      <exception cref="T:System.InvalidOperationException">WithExecutionMode is used multiple times in the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.WithMergeOptions``1(System.Linq.ParallelQuery{``0},System.Linq.ParallelMergeOptions)">
      <summary>Sets the merge options for this query, which specify how the query will buffer output.</summary>
      <param name="source">A ParallelQuery on which to set the option.</param>
      <param name="mergeOptions">The merge options to set for this query.</param>
      <typeparam name="TSource">The type of elements of <paramref name="source" />.</typeparam>
      <returns>ParallelQuery representing the same query as source, but with the registered merge options.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mergeOptions" /> is not a valid <see cref="T:System.Linq.ParallelMergeOptions" /> value.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="WithMergeOptions" /> is used multiple times in the query.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Zip``3(System.Linq.ParallelQuery{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``1,``2})">
      <summary>This Zip overload should never be called. This method is marked as obsolete and always throws <see cref="T:System.NotSupportedException" /> when invoked.</summary>
      <param name="first">This parameter is not used.</param>
      <param name="second">This parameter is not used.</param>
      <param name="resultSelector">This parameter is not used.</param>
      <typeparam name="TFirst">This type parameter is not used.</typeparam>
      <typeparam name="TSecond">This type parameter is not used.</typeparam>
      <typeparam name="TResult">This type parameter is not used.</typeparam>
      <returns>This overload always throws a <see cref="T:System.NotSupportedException" />.</returns>
      <exception cref="T:System.NotSupportedException">The exception that occurs when this method is called.</exception>
    </member>
    <member name="M:System.Linq.ParallelEnumerable.Zip``3(System.Linq.ParallelQuery{``0},System.Linq.ParallelQuery{``1},System.Func{``0,``1,``2})">
      <summary>Merges in parallel two sequences by using the specified predicate function.</summary>
      <param name="first">The first sequence to zip.</param>
      <param name="second">The second sequence to zip.</param>
      <param name="resultSelector">A function to create a result element from two matching elements.</param>
      <typeparam name="TFirst">The type of the elements of the first sequence.</typeparam>
      <typeparam name="TSecond">The type of the elements of the second sequence.</typeparam>
      <typeparam name="TResult">The type of the return elements.</typeparam>
      <returns>A sequence that has elements of type <paramref name="TResult" /> that are obtained by performing <paramref name="resultSelector" /> pairwise on two sequences. If the sequence lengths are unequal, this truncates to the length of the shorter sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> or <paramref name="resultSelector" /> is a null reference (Nothing in Visual Basic).</exception>
      <exception cref="T:System.OperationCanceledException">The query was canceled with the token passed in through <paramref name="WithCancellation" />.</exception>
      <exception cref="T:System.AggregateException">One or more exceptions occurred during the evaluation of the query.</exception>
    </member>
    <member name="T:System.Linq.ParallelExecutionMode">
      <summary>The query execution mode is a hint that specifies how the system should handle performance trade-offs when parallelizing queries.</summary>
    </member>
    <member name="F:System.Linq.ParallelExecutionMode.Default">
      <summary>This is the default setting. PLINQ will examine the query's structure and will only parallelize the query if will likely result in speedup. If the query structure indicates that speedup is not likely to be obtained, then PLINQ will execute the query as an ordinary LINQ to Objects query.</summary>
    </member>
    <member name="F:System.Linq.ParallelExecutionMode.ForceParallelism">
      <summary>Parallelize the entire query, even if that means using high-overhead algorithms. Use this flag in cases where you know that parallel execution of the query will result in speedup, but PLINQ in the Default mode would execute it as sequential.</summary>
    </member>
    <member name="T:System.Linq.ParallelMergeOptions">
      <summary>Specifies the preferred type of output merge to use in a query. In other words, it indicates how PLINQ should merge the results from the various partitions back into a single result sequence. This is a hint only, and may not be respected by the system when parallelizing all queries.</summary>
    </member>
    <member name="F:System.Linq.ParallelMergeOptions.AutoBuffered">
      <summary>Use a merge with output buffers of a size chosen by the system. Results will accumulate into an output buffer before they are available to the consumer of the query.</summary>
    </member>
    <member name="F:System.Linq.ParallelMergeOptions.Default">
      <summary>Use the default merge type, which is AutoBuffered.</summary>
    </member>
    <member name="F:System.Linq.ParallelMergeOptions.FullyBuffered">
      <summary>Use a merge with full output buffers. The system will accumulate all of the results before making any of them available to the consumer of the query.</summary>
    </member>
    <member name="F:System.Linq.ParallelMergeOptions.NotBuffered">
      <summary>Use a merge without output buffers. As soon as result elements have been computed, make that element available to the consumer of the query.</summary>
    </member>
    <member name="T:System.Linq.ParallelQuery">
      <summary>Represents a parallel sequence.</summary>
    </member>
    <member name="M:System.Linq.ParallelQuery.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the sequence.</summary>
      <returns>An enumerator that iterates through the sequence.</returns>
    </member>
    <member name="T:System.Linq.ParallelQuery`1">
      <summary>Represents a parallel sequence.</summary>
      <typeparam name="TSource">The type of element in the source sequence.</typeparam>
    </member>
    <member name="M:System.Linq.ParallelQuery`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through the sequence.</summary>
      <returns>An enumerator that iterates through the sequence.</returns>
    </member>
  </members>
</doc>