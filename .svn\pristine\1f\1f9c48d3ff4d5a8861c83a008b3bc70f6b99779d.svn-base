﻿using Coldairarrow.Entity;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HR_EmployeeInfoManage
{
    public interface IHR_PositiveBusiness
    {
        Task<PageResult<HR_PositiveDTO>> GetDataListAsync(PageInput<ConditionDTO> input, string UserId, bool IsMylist = false);
        Task<HR_Positive> GetTheDataAsync(string id);
        HR_Positive GetTheData(string id);
        /// <summary>
        /// 根据正式员工id查询转正信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        HR_Positive GetUserData(string Userid);
        Task<HR_PositiveThenDTO> GetFormDataAsync(string id);

        /// <summary>
        /// 报错数据
        /// </summary>
        /// <param name="data"></param>
        void SaveData(HR_PositiveThenDTO data);
        HR_PositiveThenDTO GetMyFormData(string id);
        Task AddDataAsync(HR_Positive data);
        void AddData(HR_Positive data);
        Task UpdateDataAsync(HR_Positive data);
        void UpdateData(HR_Positive data);
        Task DeleteDataAsync(List<string> ids);
        Task<HR_PositiveThenDTO> GetFlowFormDataAsync(string id);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
        /// <summary>
        /// 业务删除转正员工
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task BusDeleteDataAsync(List<string> ids);
        /// <summary>
        /// 提交流程
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <returns>是否创建成功</returns>
        bool CreateFlow(HR_PositiveThenDTO data, string url);
        /// <summary>
        /// 流程回调方法
        /// </summary>
        /// <param name="input"></param>
        void FlowCallBack(FlowInputDTO input);
        /// <summary>
        /// 提交，退回流程
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <param name="act">submit:提交  reject：退回</param>
        /// <returns>是否创建成功</returns>
        bool ActWorkflow(HR_PositiveThenDTO data, string url, string act = "submit");

        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <returns>是否成功</returns>
        bool ArchiveWorkflow(HR_PositiveThenDTO data, string url);
        /// <summary>
        /// 是否延期转正
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        void IsPutPositive(IsYqPositive data);
    }
    [Map(typeof(HR_Positive))]
    public class HR_PositiveDTO : HR_Positive
    {
        /// <summary>
        /// 员工编码
        /// </summary>
        public String EmployeesCode { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public String NameUser { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public String F_DepartmentId { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public String F_DepartmentName { get; set; }
        /// <summary>
        /// 入职后组织
        /// </summary>
        public String F_InductionOrg { get; set; }
        /// <summary>
        /// 职位Id
        /// </summary>
        public String F_PositionId { get; set; }
        /// <summary>
        /// 职位名称
        /// </summary>
        public String F_PositionName { get; set; }
        /// <summary>
        /// 用工关系状态
        /// </summary>
        public String EmployRelStatus { get; set; }
        /// <summary>
        /// 身份证号码
        /// </summary>
        public String IdCardNumber { get; set; }
        /// <summary>
        /// 入职日期
        /// </summary>
        public DateTime? F_InductionDate { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public int? Sex { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public string SexName { get => Sex == 0 ? "女" : "男"; }
        /// <summary>
        /// 手机号码
        /// </summary>
        public String MobilePhone { get; set; }
        //流程状态
        public string WFStateText { get => F_WFState != null ? Enum.GetName(typeof(WFStates), F_WFState) : Enum.GetName(typeof(WFStates), 0); }
        //转正状态
        public string BusStateText { get => F_BusState != null ? Enum.GetName(typeof(PosBusState), F_BusState) : Enum.GetName(typeof(PosBusState), 0); }
    }

    [Map(typeof(HR_Positive))]
    public class HR_PositiveThenDTO : HR_Positive
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        public String F_DepartmentId { get; set; }
        /// <summary>
        /// 员工编码
        /// </summary>
        public String F_CompanyId { get; set; }
        /// <summary>
        /// 员工编码
        /// </summary>
        public String EmployeesCode { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public String NameUser { get; set; }
        /// <summary>
        /// 入职后组织
        /// </summary>
        public String F_InductionOrg { get; set; }
        /// <summary>
        /// 职位Id
        /// </summary>
        public String F_PositionId { get; set; }
        /// <summary>
        /// 职位名称
        /// </summary>
        public String F_PositionName { get; set; }
        /// <summary>
        /// 联系电话
        /// </summary>
        public String MobilePhone { get; set; }
        /// <summary>
        /// 入职日期
        /// </summary>
        public DateTime? F_InductionDate { get; set; }

        /// <summary>
        /// 试用期（月）
        /// </summary>
        public Int32? F_ProbationPeriod { get; set; }
        /// <summary>
        /// 原用工状态
        /// </summary>
        public String F_InductionEmployRelStatus { get; set; }

        /// <summary>
        /// 目标用工关系状态
        /// </summary>
        public String EmployRelStatus { get; set; }
        /// <summary>
        /// 变动原因
        /// </summary>
        public String F_ChangesReason { get; set; }
        /// <summary>
        /// 是否总经理直接下属
        /// </summary>
        public int? F_IsDirectReports { get; set; }
    }
}