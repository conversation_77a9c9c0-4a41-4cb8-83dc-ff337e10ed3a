<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Http.Extensions</name>
    </assembly>
    <members>
        <member name="M:Microsoft.AspNetCore.Http.HttpContextServerVariableExtensions.GetServerVariable(Microsoft.AspNetCore.Http.HttpContext,System.String)">
            <summary>
            Gets the value of a server variable for the current request.
            </summary>
            <param name="context">The http context for the request.</param>
            <param name="variableName">The name of the variable.</param>
            <returns>
            <c>null</c> if the server does not support the <see cref="T:Microsoft.AspNetCore.Http.Features.IServerVariablesFeature"/> feature.
            May return null or empty if the variable does not exist or is not set.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.StreamCopyOperation.CopyToAsync(System.IO.Stream,System.IO.Stream,System.Nullable{System.Int64},System.Threading.CancellationToken)">
            <summary>Asynchronously reads the given number of bytes from the source stream and writes them to another stream.</summary>
            <returns>A task that represents the asynchronous copy operation.</returns>
            <param name="source">The stream from which the contents will be copied.</param>
            <param name="destination">The stream to which the contents of the current stream will be copied.</param>
            <param name="count">The count of bytes to be copied.</param>
            <param name="cancel">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.StreamCopyOperation.CopyToAsync(System.IO.Stream,System.IO.Stream,System.Nullable{System.Int64},System.Int32,System.Threading.CancellationToken)">
            <summary>Asynchronously reads the given number of bytes from the source stream and writes them to another stream, using a specified buffer size.</summary>
            <returns>A task that represents the asynchronous copy operation.</returns>
            <param name="source">The stream from which the contents will be copied.</param>
            <param name="destination">The stream to which the contents of the current stream will be copied.</param>
            <param name="count">The count of bytes to be copied.</param>
            <param name="bufferSize">The size, in bytes, of the buffer. This value must be greater than zero. The default size is 4096.</param>
            <param name="cancel">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Extensions.UriHelper">
            <summary>
            A helper class for constructing encoded Uris for use in headers and other Uris.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.UriHelper.BuildRelative(Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.QueryString,Microsoft.AspNetCore.Http.FragmentString)">
            <summary>
            Combines the given URI components into a string that is properly encoded for use in HTTP headers.
            </summary>
            <param name="pathBase">The first portion of the request path associated with application root.</param>
            <param name="path">The portion of the request path that identifies the requested resource.</param>
            <param name="query">The query, if any.</param>
            <param name="fragment">The fragment, if any.</param>
            <returns>The combined URI components, properly encoded for use in HTTP headers.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.UriHelper.BuildAbsolute(System.String,Microsoft.AspNetCore.Http.HostString,Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.QueryString,Microsoft.AspNetCore.Http.FragmentString)">
            <summary>
            Combines the given URI components into a string that is properly encoded for use in HTTP headers.
            Note that unicode in the HostString will be encoded as punycode.
            </summary>
            <param name="scheme">http, https, etc.</param>
            <param name="host">The host portion of the uri normally included in the Host header. This may include the port.</param>
            <param name="pathBase">The first portion of the request path associated with application root.</param>
            <param name="path">The portion of the request path that identifies the requested resource.</param>
            <param name="query">The query, if any.</param>
            <param name="fragment">The fragment, if any.</param>
            <returns>The combined URI components, properly encoded for use in HTTP headers.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.UriHelper.FromAbsolute(System.String,System.String@,Microsoft.AspNetCore.Http.HostString@,Microsoft.AspNetCore.Http.PathString@,Microsoft.AspNetCore.Http.QueryString@,Microsoft.AspNetCore.Http.FragmentString@)">
            <summary>
            Separates the given absolute URI string into components. Assumes no PathBase.
            </summary>
            <param name="uri">A string representation of the uri.</param>
            <param name="scheme">http, https, etc.</param>
            <param name="host">The host portion of the uri normally included in the Host header. This may include the port.</param>
            <param name="path">The portion of the request path that identifies the requested resource.</param>
            <param name="query">The query, if any.</param>
            <param name="fragment">The fragment, if any.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.UriHelper.Encode(System.Uri)">
            <summary>
            Generates a string from the given absolute or relative Uri that is appropriately encoded for use in
            HTTP headers. Note that a unicode host name will be encoded as punycode.
            </summary>
            <param name="uri">The Uri to encode.</param>
            <returns>The encoded string version of <paramref name="uri"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.UriHelper.GetEncodedUrl(Microsoft.AspNetCore.Http.HttpRequest)">
            <summary>
            Returns the combined components of the request URL in a fully escaped form suitable for use in HTTP headers
            and other HTTP operations.
            </summary>
            <param name="request">The request to assemble the uri pieces from.</param>
            <returns>The encoded string version of the URL from <paramref name="request"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.UriHelper.GetEncodedPathAndQuery(Microsoft.AspNetCore.Http.HttpRequest)">
            <summary>
            Returns the relative URI.
            </summary>
            <param name="request">The request to assemble the uri pieces from.</param>
            <returns>The path and query off of <paramref name="request"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.UriHelper.GetDisplayUrl(Microsoft.AspNetCore.Http.HttpRequest)">
            <summary>
            Returns the combined components of the request URL in a fully un-escaped form (except for the QueryString)
            suitable only for display. This format should not be used in HTTP headers or other HTTP operations.
            </summary>
            <param name="request">The request to assemble the uri pieces from.</param>
            <returns>The combined components of the request URL in a fully un-escaped form (except for the QueryString)
            suitable only for display.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.ResponseExtensions.Redirect(Microsoft.AspNetCore.Http.HttpResponse,System.String,System.Boolean,System.Boolean)">
            <summary>
            Returns a redirect response (HTTP 301, HTTP 302, HTTP 307 or HTTP 308) to the client.
            </summary>
            <param name="response">The <see cref="T:Microsoft.AspNetCore.Http.HttpResponse"/> to redirect.</param>
            <param name="location">The URL to redirect the client to. This must be properly encoded for use in http headers where only ASCII characters are allowed.</param>
            <param name="permanent"><c>True</c> if the redirect is permanent (301 or 308), otherwise <c>false</c> (302 or 307).</param>
            <param name="preserveMethod"><c>True</c> if the redirect needs to reuse the method and body (308 or 307), otherwise <c>false</c> (301 or 302).</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.SendFileResponseExtensions">
            <summary>
            Provides extensions for HttpResponse exposing the SendFile extension.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.SendFileResponseExtensions.SendFileAsync(Microsoft.AspNetCore.Http.HttpResponse,Microsoft.Extensions.FileProviders.IFileInfo,System.Threading.CancellationToken)">
            <summary>
            Sends the given file using the SendFile extension.
            </summary>
            <param name="response"></param>
            <param name="file">The file.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.SendFileResponseExtensions.SendFileAsync(Microsoft.AspNetCore.Http.HttpResponse,Microsoft.Extensions.FileProviders.IFileInfo,System.Int64,System.Nullable{System.Int64},System.Threading.CancellationToken)">
            <summary>
            Sends the given file using the SendFile extension.
            </summary>
            <param name="response"></param>
            <param name="file">The file.</param>
            <param name="offset">The offset in the file.</param>
            <param name="count">The number of bytes to send, or null to send the remainder of the file.</param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.SendFileResponseExtensions.SendFileAsync(Microsoft.AspNetCore.Http.HttpResponse,System.String,System.Threading.CancellationToken)">
            <summary>
            Sends the given file using the SendFile extension.
            </summary>
            <param name="response"></param>
            <param name="fileName">The full path to the file.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/>.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.SendFileResponseExtensions.SendFileAsync(Microsoft.AspNetCore.Http.HttpResponse,System.String,System.Int64,System.Nullable{System.Int64},System.Threading.CancellationToken)">
            <summary>
            Sends the given file using the SendFile extension.
            </summary>
            <param name="response"></param>
            <param name="fileName">The full path to the file.</param>
            <param name="offset">The offset in the file.</param>
            <param name="count">The number of bytes to send, or null to send the remainder of the file.</param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.StreamCopyOperationInternal.CopyToAsync(System.IO.Stream,System.IO.Stream,System.Nullable{System.Int64},System.Threading.CancellationToken)">
            <summary>Asynchronously reads the given number of bytes from the source stream and writes them to another stream.</summary>
            <returns>A task that represents the asynchronous copy operation.</returns>
            <param name="source">The stream from which the contents will be copied.</param>
            <param name="destination">The stream to which the contents of the current stream will be copied.</param>
            <param name="count">The count of bytes to be copied.</param>
            <param name="cancel">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.StreamCopyOperationInternal.CopyToAsync(System.IO.Stream,System.IO.Stream,System.Nullable{System.Int64},System.Int32,System.Threading.CancellationToken)">
            <summary>Asynchronously reads the given number of bytes from the source stream and writes them to another stream, using a specified buffer size.</summary>
            <returns>A task that represents the asynchronous copy operation.</returns>
            <param name="source">The stream from which the contents will be copied.</param>
            <param name="destination">The stream to which the contents of the current stream will be copied.</param>
            <param name="count">The count of bytes to be copied.</param>
            <param name="bufferSize">The size, in bytes, of the buffer. This value must be greater than zero. The default size is 4096.</param>
            <param name="cancel">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
        </member>
    </members>
</doc>
