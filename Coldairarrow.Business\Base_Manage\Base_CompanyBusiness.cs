﻿using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

namespace Coldairarrow.Business.Base_Manage
{
    public class Base_CompanyBusiness : BaseBusiness<Base_Company>, IBase_CompanyBusiness, ITransientDependency
    {
        public Base_CompanyBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口
        public async Task<List<Base_CompanyTreeDTO>> GetTreeDataListAsync(CompanyTreeInputDTO input)
        {
            var where = LinqHelper.True<Base_Company>();
            if (!input.parentId.IsNullOrEmpty())
                where = where.And(x => x.F_ParentId == input.parentId);

            var list = await GetIQueryable().Where(where).ToListAsync();
            var treeList = list
                .Select(x => new Base_CompanyTreeDTO
                {
                    Id = x.F_Id,
                    ParentId = x.F_ParentId,
                    Text = x.F_FullName,
                    Value = x.F_Id,
                    F_Description = x.F_Description,
                    F_FullName = x.F_FullName,
                    F_EnCode = x.F_EnCode,
                    F_BusinessScope = x.F_BusinessScope,
                    F_FoundedTime = x.F_FoundedTime.HasValue ? x.F_FoundedTime.Value.ToString("yyyy-MM-dd") : "",
                    F_Manager = x.F_Manager,
                    F_Nature = x.F_Nature,
                    F_ShortName = x.F_ShortName,
                }).ToList();

            return TreeHelper.BuildTree(treeList);
        }

        public async Task<PageResult<Base_Company>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Base_Company>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Base_Company, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Base_Company> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        public Base_Company GetTheData(string id)
        {
            return GetEntity(id);
        }
        public async Task AddDataAsync(Base_Company data)
        {
            await InsertAsync(data);
        }
        public async Task UpdateDataAsync(Base_Company data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        #endregion

        #region 私有成员

        #endregion
    }
}