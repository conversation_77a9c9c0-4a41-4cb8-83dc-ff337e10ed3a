﻿using Coldairarrow.Entity.Wechat_Go;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Util.DataAccess;
using System;

namespace Coldairarrow.Business.Wechat_Go
{
    public class Go_TeamBusiness : BaseBusiness<Go_Team>, IGo_TeamBusiness, ITransientDependency
    {
        public Go_TeamBusiness(IGoDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<Go_Team>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Go_Team>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Go_Team, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Go_Team> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public  List<Go_Team> GetMyTeam(string openId)
        {
            var list = (from a in Db.GetIQueryable<Go_TeamUser>()
                        where a.F_OpenId == openId
                        join b in Db.GetIQueryable<Go_Team>()
                        on a.F_TeamId equals b.Id
                        select b).ToList();
            return list;
        }
        public List<Go_TeamDTO> GetHotDataAsync(int hot)
        {
            var teamList = from p in Db.GetIQueryable<Go_TeamUser>() group p by p.F_TeamId into g
                           select new
                           {
                               teamId = g.Key,
                               count = g.Count()
                           };
            if (hot==2)
            {
                var list = (from a in Db.GetIQueryable<Go_Team>()
                            join b in teamList on a.Id equals b.teamId
                            select new Go_TeamDTO { 
                                Count = b.count,
                                Id = a.Id,
                                F_Name = a.F_Name,
                                F_Describe = a.F_Describe,
                                F_Cover = a.F_Cover,
                                F_Icon = a.F_Icon,
                                CreateTime = a.CreateTime
                            }).OrderByDescending(x => x.CreateTime).Take(6).ToList();
                return list;
            }
            else
            {
                var list = (from a in Db.GetIQueryable<Go_Team>()
                            join b in teamList on a.Id equals b.teamId
                            select new Go_TeamDTO
                            {
                                Count = b.count,
                                Id = a.Id,
                                F_Name = a.F_Name,
                                F_Describe = a.F_Describe,
                                F_Cover = a.F_Cover,
                                F_Icon = a.F_Icon,
                                CreateTime = a.CreateTime
                            }).OrderByDescending(x => x.CreateTime).ToList();
                return list;
            }
        }
        public List<Go_TeamDTO> GetTeamByOpenId(string openId)
        {
            var team = (from a in Db.GetIQueryable<Go_TeamUser>()
                          where a.F_OpenId == openId && a.F_IsAble == 1
                          select a).FirstOrDefault();
            if (!team.IsNullOrEmpty())
            {
                var teamId = team.F_TeamId;
                var teamList = from p in Db.GetIQueryable<Go_TeamUser>()
                               where p.F_TeamId == teamId
                               group p by p.F_TeamId into g
                               select new
                               {
                                   teamId = g.Key,
                                   count = g.Count()
                               };
                var list = (from a in Db.GetIQueryable<Go_Team>()
                            where a.Id == teamId && a.F_IsAble == 1
                            join b in teamList on a.Id equals b.teamId
                            select new Go_TeamDTO
                            {
                                Count = b.count,
                                Id = a.Id,
                                F_Name = a.F_Name,
                                F_Describe = a.F_Describe,
                                F_Cover = a.F_Cover,
                                F_Icon = a.F_Icon,
                                CreateTime = a.CreateTime
                            }).ToList();
                return list;
            }
            else
            {
                var list = new List<Go_TeamDTO>();
                return list;
            }

        }
        public List<Go_MiniUserDTO> GetTeamUser(string teamId)
        {
            var list = (from a in Db.GetIQueryable<Go_TeamUser>()
                        where a.F_TeamId == teamId
                        join b in Db.GetIQueryable<Go_MiniUser>()
                        on a.F_OpenId equals b.F_Id
                        select new Go_MiniUserDTO
                        {
                           U_RealName = b.U_RealName,
                           post = a.F_UserType,
                           U_HeadIcon = b.U_HeadIcon,
                           F_Id = b.F_Id,
                           U_NickName = b.U_NickName,
                           U_Gender = b.U_Gender
                        }).OrderByDescending(x => x.post).ToList();
            return list;
        }
        public List<Go_MiniUserDTO> getDayUserRank(string teamId,string type)
        {
            var date_ = DateTime.Today;
            //今日或昨天
            switch (type)
            {
                case "day": date_ = DateTime.Today.AddDays(-1); break;
                default: date_ = DateTime.Today; break;
            }
            var userList = from a in Db.GetIQueryable<Go_TeamUser>()
                           where a.F_TeamId == teamId
                           join b in Db.GetIQueryable<Go_Record>()
                           on a.F_OpenId equals b.F_OpenId
                           where b.F_Date == date_ && b.F_Isable == 1
                           group new { a=a ,b=b } by a.F_OpenId into g 
                           select new {
                               openId = g.Key,
                               score = g.Sum(x => x.b.F_Score)
                           };
            var list = (from c in Db.GetIQueryable<Go_TeamUser>()
                        where c.F_TeamId == teamId
                        join a in Db.GetIQueryable<Go_MiniUser>()
                        on c.F_OpenId equals a.F_Id
                        join b in userList on a.F_Id equals b.openId
                        into r from re in r.DefaultIfEmpty()
                        select new Go_MiniUserDTO
                        {
                            F_Id = a.F_Id,
                            Count = (re.score == null) ? 0 : re.score,
                            U_RealName =a.U_RealName,
                            U_HeadIcon = a.U_HeadIcon,
                            U_NickName = a.U_NickName

                        }).OrderByDescending(x =>x.Count).ToList();
            return list;
        }
        public List<Go_MiniUserDTO> getWeekUserRank(string teamId,string type)
        {
            var startdate = DateTime.Today;
            var enddate = DateTime.Today.AddDays(1);
            switch (type)
            {
                case "year": startdate = DateTime.Today.AddDays(-Convert.ToInt32(DateTime.Now.Date.DayOfYear)); break;
                case "week": startdate = DateTime.Today.AddDays(-Convert.ToInt32(DateTime.Now.Date.DayOfWeek)); break;
                case "month": startdate = DateTime.Today.AddDays(-Convert.ToInt32((DateTime.Now.Date.Day - 1))); break;
                case "lastmonth":
                    enddate = DateTime.Today.AddDays(-Convert.ToInt32((DateTime.Now.Date.Day - 1)));
                    startdate = enddate.AddMonths(-1);break;
                default: startdate = DateTime.Today.AddDays(-Convert.ToInt32((DateTime.Now.Date.Day-1))); break;
            }
            var userList = from a in Db.GetIQueryable<Go_TeamUser>()
                           where a.F_TeamId == teamId
                           join b in Db.GetIQueryable<Go_Record>()
                           on a.F_OpenId equals b.F_OpenId
                           where b.F_Date >= startdate &&b.F_Date < enddate && b.F_Isable == 1
                           group new { a = a, b = b } by a.F_OpenId into g
                           select new
                           {
                               openId = g.Key,
                               score = g.Sum(x => x.b.F_Score)
                           };
            var list = (from c in Db.GetIQueryable<Go_TeamUser>()
                        where c.F_TeamId == teamId
                        join a in Db.GetIQueryable<Go_MiniUser>()
                        on c.F_OpenId equals a.F_Id
                        join b in userList on a.F_Id equals b.openId
                        into r
                        from re in r.DefaultIfEmpty()
                        select new Go_MiniUserDTO
                        {
                            F_Id = a.F_Id,
                            Count = (re.score == null) ? 0 : re.score,
                            U_RealName = a.U_RealName,
                            U_HeadIcon = a.U_HeadIcon,
                            U_NickName = a.U_NickName

                        }).OrderByDescending(x => x.Count).ToList();
            return list;
        }
        public List<Go_MiniUserDTO> getLastMonthUserRank(string teamId, string type)
        {
            var enddate = DateTime.Today;
            enddate = DateTime.Today.AddDays(-Convert.ToInt32((DateTime.Now.Date.Day - 1)));
            var startdate = enddate.AddMonths(-1);
            var userList = from a in Db.GetIQueryable<Go_TeamUser>()
                           where a.F_TeamId == teamId
                           join b in Db.GetIQueryable<Go_Record>()
                           on a.F_OpenId equals b.F_OpenId
                           where b.F_Date >= startdate && b.F_Isable == 1 &&b.F_Date < enddate
                           group new { a = a, b = b } by a.F_OpenId into g
                           select new
                           {
                               openId = g.Key,
                               score = g.Sum(x => x.b.F_Score)
                           };
            var list = (from a in Db.GetIQueryable<Go_MiniUser>()
                        join b in userList on a.F_Id equals b.openId
                        select new Go_MiniUserDTO
                        {
                            F_Id = a.F_Id,
                            Count = b.score,
                            U_RealName = a.U_RealName,
                            U_HeadIcon = a.U_HeadIcon,
                            U_NickName = a.U_NickName

                        }).OrderByDescending(x => x.Count).ToList();
            return list;
        }
       
        public List<Go_TeamDTO> getDayTeamRank(string type)
        {
            var date_ = DateTime.Today;
            switch (type)
            {
                case "day": date_ = DateTime.Today.AddDays(-1); break;
                default: date_ = DateTime.Today; break;
            }
            var userList = from a in Db.GetIQueryable<Go_Record>()
                       where a.F_Isable ==1 && a.F_Score ==1 && a.F_Date == date_
                       group a by a.F_OpenId into g
                       select new
                       {
                           openId = g.Key,
                           score = g.Sum(x=>x.F_Score)
                       };
            var teamlist =  from a in Db.GetIQueryable<Go_TeamUser>()
                            join b in userList on a.F_OpenId equals b.openId
                            group new { a = a, b = b } by a.F_TeamId into g
                            select new
                            {
                                teamId = g.Key,
                                score = g.Sum(x => x.b.score)
                            };
            var teamList_ = from p in Db.GetIQueryable<Go_TeamUser>()
                           group p by p.F_TeamId into g
                           select new
                           {
                               teamId = g.Key,
                               count = g.Count()
                           };
            var list = (from a in Db.GetIQueryable<Go_Team>()
                        join b in teamlist on a.Id equals b.teamId
                        join c in teamList_ on a.Id equals c.teamId
                        where c.count >= 6
                        select new Go_TeamDTO
                        {
                            Count = b.score,
                            Id = a.Id,
                            F_Name = a.F_Name,
                            Average = Math.Round(((double)b.score / c.count),2)
                        }).OrderByDescending(x => x.Average).ToList();
            return list;
        }
        public List<Go_TeamDTO> getWeekTeamRank(string type)
        {
            var date_ = DateTime.Today;
            switch (type)
            {
                case "year": date_ = DateTime.Today.AddDays(-Convert.ToInt32(DateTime.Now.Date.DayOfYear)); break;
                case "week": date_ = DateTime.Today.AddDays(-Convert.ToInt32(DateTime.Now.Date.DayOfWeek)); break;
                default: date_ = DateTime.Today.AddDays(-Convert.ToInt32((DateTime.Now.Date.Day-1))); break;
            }
            var userList = from a in Db.GetIQueryable<Go_Record>()
                           where a.F_Isable == 1 && a.F_Score == 1 && a.F_Date >= date_
                           group a by a.F_OpenId into g
                           select new
                           {
                               openId = g.Key,
                               score = g.Sum(x => x.F_Score)
                           };
            var teamlist = from a in Db.GetIQueryable<Go_TeamUser>()
                           join b in userList on a.F_OpenId equals b.openId
                           group new { a = a, b = b } by a.F_TeamId into g
                           select new
                           {
                               teamId = g.Key,
                               score = g.Sum(x => x.b.score)
                           };
            var teamList_ = from p in Db.GetIQueryable<Go_TeamUser>()
                            group p by p.F_TeamId into g
                            select new
                            {
                                teamId = g.Key,
                                count = g.Count()
                            };
            var list = (from a in Db.GetIQueryable<Go_Team>()
                        join b in teamlist on a.Id equals b.teamId
                        join c in teamList_ on a.Id equals c.teamId
                        where c.count >=6
                        select new Go_TeamDTO
                        {
                            Count = b.score,
                            Id = a.Id,
                            F_Name = a.F_Name,
                            Average = Math.Round(((double)b.score / c.count), 2)
                        }).OrderByDescending(x => x.Average).ToList();
            return list;
        }
        public List<Go_TeamDTO> getlastMonthTeamRank(string type)
        {
            var enddate = DateTime.Today;
            enddate = DateTime.Today.AddDays(-Convert.ToInt32((DateTime.Now.Date.Day - 1)));
            var startdate = enddate.AddMonths(-1);
            var userList = from a in Db.GetIQueryable<Go_Record>()
                           where a.F_Isable == 1 && a.F_Score == 1 && a.F_Date >= startdate
                           && a.F_Date < enddate
                           group a by a.F_OpenId into g
                           select new
                           {
                               openId = g.Key,
                               score = g.Sum(x => x.F_Score)
                           };
            var teamlist = from a in Db.GetIQueryable<Go_TeamUser>()
                           join b in userList on a.F_OpenId equals b.openId
                           group new { a = a, b = b } by a.F_TeamId into g
                           select new
                           {
                               teamId = g.Key,
                               score = g.Sum(x => x.b.score)
                           };
            var teamList_ = from p in Db.GetIQueryable<Go_TeamUser>()
                            group p by p.F_TeamId into g
                            select new
                            {
                                teamId = g.Key,
                                count = g.Count()
                            };
            var list = (from a in Db.GetIQueryable<Go_Team>()
                        join b in teamlist on a.Id equals b.teamId
                        join c in teamList_ on a.Id equals c.teamId
                        where c.count >= 6
                        select new Go_TeamDTO
                        {
                            Count = b.score,
                            Id = a.Id,
                            F_Name = a.F_Name,
                            Average = Math.Round(((double)b.score / c.count), 2)
                        }).OrderByDescending(x => x.Average).ToList();
            return list;
        }
        public async Task AddDataAsync(Go_Team data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Go_Team data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Go_Team>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Go_Team, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<Go_Team, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}