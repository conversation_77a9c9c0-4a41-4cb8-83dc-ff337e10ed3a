<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.WebHost">
            <summary>
            Provides convenience methods for creating instances of <see cref="T:Microsoft.AspNetCore.Hosting.IWebHost"/> and <see cref="T:Microsoft.AspNetCore.Hosting.IWebHostBuilder"/> with pre-configured defaults.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.WebHost.Start(Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Initializes and starts a new <see cref="T:Microsoft.AspNetCore.Hosting.IWebHost"/> with pre-configured defaults.
            See <see cref="M:Microsoft.AspNetCore.WebHost.CreateDefaultBuilder"/> for details.
            </summary>
            <param name="app">A delegate that handles requests to the application.</param>
            <returns>A started <see cref="T:Microsoft.AspNetCore.Hosting.IWebHost"/> that hosts the application.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebHost.Start(System.String,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Initializes and starts a new <see cref="T:Microsoft.AspNetCore.Hosting.IWebHost"/> with pre-configured defaults.
            See <see cref="M:Microsoft.AspNetCore.WebHost.CreateDefaultBuilder"/> for details.
            </summary>
            <param name="url">The URL the hosted application will listen on.</param>
            <param name="app">A delegate that handles requests to the application.</param>
            <returns>A started <see cref="T:Microsoft.AspNetCore.Hosting.IWebHost"/> that hosts the application.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebHost.Start(System.Action{Microsoft.AspNetCore.Routing.IRouteBuilder})">
            <summary>
            Initializes and starts a new <see cref="T:Microsoft.AspNetCore.Hosting.IWebHost"/> with pre-configured defaults.
            See <see cref="M:Microsoft.AspNetCore.WebHost.CreateDefaultBuilder"/> for details.
            </summary>
            <param name="routeBuilder">A delegate that configures the router for handling requests to the application.</param>
            <returns>A started <see cref="T:Microsoft.AspNetCore.Hosting.IWebHost"/> that hosts the application.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebHost.Start(System.String,System.Action{Microsoft.AspNetCore.Routing.IRouteBuilder})">
            <summary>
            Initializes and starts a new <see cref="T:Microsoft.AspNetCore.Hosting.IWebHost"/> with pre-configured defaults.
            See <see cref="M:Microsoft.AspNetCore.WebHost.CreateDefaultBuilder"/> for details.
            </summary>
            <param name="url">The URL the hosted application will listen on.</param>
            <param name="routeBuilder">A delegate that configures the router for handling requests to the application.</param>
            <returns>A started <see cref="T:Microsoft.AspNetCore.Hosting.IWebHost"/> that hosts the application.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebHost.StartWith(System.Action{Microsoft.AspNetCore.Builder.IApplicationBuilder})">
            <summary>
            Initializes and starts a new <see cref="T:Microsoft.AspNetCore.Hosting.IWebHost"/> with pre-configured defaults.
            See <see cref="M:Microsoft.AspNetCore.WebHost.CreateDefaultBuilder"/> for details.
            </summary>
            <param name="app">The delegate that configures the <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.</param>
            <returns>A started <see cref="T:Microsoft.AspNetCore.Hosting.IWebHost"/> that hosts the application.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebHost.StartWith(System.String,System.Action{Microsoft.AspNetCore.Builder.IApplicationBuilder})">
            <summary>
            Initializes and starts a new <see cref="T:Microsoft.AspNetCore.Hosting.IWebHost"/> with pre-configured defaults.
            See <see cref="M:Microsoft.AspNetCore.WebHost.CreateDefaultBuilder"/> for details.
            </summary>
            <param name="url">The URL the hosted application will listen on.</param>
            <param name="app">The delegate that configures the <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.</param>
            <returns>A started <see cref="T:Microsoft.AspNetCore.Hosting.IWebHost"/> that hosts the application.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebHost.CreateDefaultBuilder">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Hosting.WebHostBuilder"/> class with pre-configured defaults.
            </summary>
            <remarks>
              The following defaults are applied to the returned <see cref="T:Microsoft.AspNetCore.Hosting.WebHostBuilder"/>:
                use Kestrel as the web server and configure it using the application's configuration providers,
                set the <see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.ContentRootPath"/> to the result of <see cref="M:System.IO.Directory.GetCurrentDirectory"/>,
                load <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from 'appsettings.json' and 'appsettings.[<see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.EnvironmentName"/>].json',
                load <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from User Secrets when <see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.EnvironmentName"/> is 'Development' using the entry assembly,
                load <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from environment variables,
                configure the <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> to log to the console and debug output,
                adds the HostFiltering middleware,
                adds the ForwardedHeaders middleware if ASPNETCORE_FORWARDEDHEADERS_ENABLED=true,
                and enable IIS integration.
            </remarks>
            <returns>The initialized <see cref="T:Microsoft.AspNetCore.Hosting.IWebHostBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebHost.CreateDefaultBuilder(System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Hosting.WebHostBuilder"/> class with pre-configured defaults.
            </summary>
            <remarks>
              The following defaults are applied to the returned <see cref="T:Microsoft.AspNetCore.Hosting.WebHostBuilder"/>:
                use Kestrel as the web server and configure it using the application's configuration providers,
                set the <see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.ContentRootPath"/> to the result of <see cref="M:System.IO.Directory.GetCurrentDirectory"/>,
                load <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from 'appsettings.json' and 'appsettings.[<see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.EnvironmentName"/>].json',
                load <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from User Secrets when <see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.EnvironmentName"/> is 'Development' using the entry assembly,
                load <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from environment variables,
                load <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from supplied command line args,
                configure the <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> to log to the console and debug output,
                adds the HostFiltering middleware,
                adds the ForwardedHeaders middleware if ASPNETCORE_FORWARDEDHEADERS_ENABLED=true,
                and enable IIS integration.
            </remarks>
            <param name="args">The command line args.</param>
            <returns>The initialized <see cref="T:Microsoft.AspNetCore.Hosting.IWebHostBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.WebHost.CreateDefaultBuilder``1(System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Hosting.WebHostBuilder"/> class with pre-configured defaults using typed Startup.
            </summary>
            <remarks>
              The following defaults are applied to the returned <see cref="T:Microsoft.AspNetCore.Hosting.WebHostBuilder"/>:
                use Kestrel as the web server and configure it using the application's configuration providers,
                set the <see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.ContentRootPath"/> to the result of <see cref="M:System.IO.Directory.GetCurrentDirectory"/>,
                load <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from 'appsettings.json' and 'appsettings.[<see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.EnvironmentName"/>].json',
                load <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from User Secrets when <see cref="P:Microsoft.Extensions.Hosting.IHostEnvironment.EnvironmentName"/> is 'Development' using the entry assembly,
                load <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from environment variables,
                load <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> from supplied command line args,
                configure the <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> to log to the console and debug output,
                enable IIS integration.
            </remarks>
            <typeparam name ="TStartup">The type containing the startup methods for the application.</typeparam>
            <param name="args">The command line args.</param>
            <returns>The initialized <see cref="T:Microsoft.AspNetCore.Hosting.IWebHostBuilder"/>.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Hosting.GenericHostBuilderExtensions">
            <summary>
            Extension methods for configuring the IWebHostBuilder.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Hosting.GenericHostBuilderExtensions.ConfigureWebHostDefaults(Microsoft.Extensions.Hosting.IHostBuilder,System.Action{Microsoft.AspNetCore.Hosting.IWebHostBuilder})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Hosting.IWebHostBuilder"/> class with pre-configured defaults.
            </summary>
            <remarks>
              The following defaults are applied to the <see cref="T:Microsoft.AspNetCore.Hosting.IWebHostBuilder"/>:
                use Kestrel as the web server and configure it using the application's configuration providers,
                adds the HostFiltering middleware,
                adds the ForwardedHeaders middleware if ASPNETCORE_FORWARDEDHEADERS_ENABLED=true,
                and enable IIS integration.
            </remarks>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder" /> instance to configure</param>
            <param name="configure">The configure callback</param>
            <returns>The <see cref="T:Microsoft.Extensions.Hosting.IHostBuilder"/> for chaining.</returns>
        </member>
    </members>
</doc>
