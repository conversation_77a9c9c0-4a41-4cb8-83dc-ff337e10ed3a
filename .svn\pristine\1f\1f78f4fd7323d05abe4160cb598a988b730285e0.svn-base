﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Coldairarrow.Entity.Base_Manage
{
    /// <summary>
    /// Base_WechatUser
    /// </summary>
    [Table("Base_WechatUser")]
    public class Base_WechatUser
    {

        /// <summary>
        /// 微信id
        /// </summary>
        [Key, Column(Order = 1)]
        public String Id { get; set; }

        /// <summary>
        /// Unionid
        /// </summary>
        public String UnionId { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        public String Mobile { get; set; }

        /// <summary>
        /// MobileSecurity
        /// </summary>
        public String MobileSecurity { get; set; }

        /// <summary>
        /// AD账号
        /// </summary>
        public String W_ADNumber { get; set; }

        /// <summary>
        /// HRGUid
        /// </summary>
        public String HR_UserId { get; set; }

        /// <summary>
        /// HR_FormalId
        /// </summary>
        public String HR_FormalId { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        public String W_Email { get; set; }

        /// <summary>
        /// 用户类型
        /// </summary>
        public Int32? W_UserType { get; set; }

        /// <summary>
        /// 真实姓名
        /// </summary>
        public String W_Realname { get; set; }

        /// <summary>
        /// 性别，0男，1女
        /// </summary>
        public Int32? W_Gender { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public String W_NickName { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
        public String W_City { get; set; }

        /// <summary>
        /// 省份
        /// </summary>
        public String W_Province { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        public String W_Country { get; set; }

        /// <summary>
        /// 头像
        /// </summary>
        public String W_Icon { get; set; }

        /// <summary>
        /// 用户地址
        /// </summary>
        public String W_Address { get; set; }

        /// <summary>
        /// 语言
        /// </summary>
        public String W_Language { get; set; }

        /// <summary>
        /// 0未认证，1已认证
        /// </summary>
        public Int32? W_IsRegister { get; set; }

        /// <summary>
        /// W_LastLogin
        /// </summary>
        public DateTime? W_LastLogin { get; set; }

        /// <summary>
        /// 注册时间
        /// </summary>
        public DateTime? W_FirstLogin { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? F_CreateDate { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public String F_CreateUserId { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public String F_CreateUserName { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        public DateTime? F_ModifyDate { get; set; }

        /// <summary>
        /// 修改人Id
        /// </summary>
        public String F_ModifyUserId { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public String F_ModifyUserName { get; set; }

        /// <summary>
        /// HR_DeptId
        /// </summary>
        public String HR_DeptId { get; set; }

        /// <summary>
        /// Token
        /// </summary>
        [NotMapped]
        public string Token { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        [NotMapped]
        public string DepartmentName { get; set; }
    }
}