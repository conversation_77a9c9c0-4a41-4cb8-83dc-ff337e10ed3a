﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel.EventBasedAsync</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.AsyncCompletedEventArgs">
      <summary>Provides data for the MethodName<see langword="Completed" /> event.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.#ctor(System.Exception,System.Boolean,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" /> class.</summary>
      <param name="error">Any error that occurred during the asynchronous operation.</param>
      <param name="cancelled">A value indicating whether the asynchronous operation was canceled.</param>
      <param name="userState">The optional user-supplied state object passed to the <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" /> method.</param>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled">
      <summary>Gets a value indicating whether an asynchronous operation has been canceled.</summary>
      <returns>
        <see langword="true" /> if the background operation has been canceled; otherwise <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Error">
      <summary>Gets a value indicating which error occurred during an asynchronous operation.</summary>
      <returns>An <see cref="T:System.Exception" /> instance, if an error occurred during an asynchronous operation; otherwise <see langword="null" />.</returns>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.RaiseExceptionIfNecessary">
      <summary>Raises a user-supplied exception if an asynchronous operation failed.</summary>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> property is <see langword="true" />.</exception>
      <exception cref="T:System.Reflection.TargetInvocationException">The <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> property has been set by the asynchronous operation. The <see cref="P:System.Exception.InnerException" /> property holds a reference to <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" />.</exception>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.UserState">
      <summary>Gets the unique identifier for the asynchronous task.</summary>
      <returns>An object reference that uniquely identifies the asynchronous task; otherwise, <see langword="null" /> if no value has been set.</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncCompletedEventHandler">
      <summary>Represents the method that will handle the MethodName<see langword="Completed" /> event of an asynchronous operation.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">An <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:System.ComponentModel.AsyncOperation">
      <summary>Tracks the lifetime of an asynchronous operation.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Finalize">
      <summary>Finalizes the asynchronous operation.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.OperationCompleted">
      <summary>Ends the lifetime of an asynchronous operation.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" /> has been called previously for this task.</exception>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Post(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Invokes a delegate on the thread or context appropriate for the application model.</summary>
      <param name="d">A <see cref="T:System.Threading.SendOrPostCallback" /> object that wraps the delegate to be called when the operation ends.</param>
      <param name="arg">An argument for the delegate contained in the <paramref name="d" /> parameter.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)" /> method has been called previously for this task.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Ends the lifetime of an asynchronous operation.</summary>
      <param name="d">A <see cref="T:System.Threading.SendOrPostCallback" /> object that wraps the delegate to be called when the operation ends.</param>
      <param name="arg">An argument for the delegate contained in the <paramref name="d" /> parameter.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" /> has been called previously for this task.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.SynchronizationContext">
      <summary>Gets the <see cref="T:System.Threading.SynchronizationContext" /> object that was passed to the constructor.</summary>
      <returns>The <see cref="T:System.Threading.SynchronizationContext" /> object that was passed to the constructor.</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.UserSuppliedState">
      <summary>Gets or sets an object used to uniquely identify an asynchronous operation.</summary>
      <returns>The state object passed to the asynchronous method invocation.</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncOperationManager">
      <summary>Provides concurrency management for classes that support asynchronous method calls. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperationManager.CreateOperation(System.Object)">
      <summary>Returns an <see cref="T:System.ComponentModel.AsyncOperation" /> for tracking the duration of a particular asynchronous operation.</summary>
      <param name="userSuppliedState">An object used to associate a piece of client state, such as a task ID, with a particular asynchronous operation.</param>
      <returns>An <see cref="T:System.ComponentModel.AsyncOperation" /> that you can use to track the duration of an asynchronous method invocation.</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncOperationManager.SynchronizationContext">
      <summary>Gets or sets the synchronization context for the asynchronous operation.</summary>
      <returns>The synchronization context for the asynchronous operation.</returns>
    </member>
    <member name="T:System.ComponentModel.BackgroundWorker">
      <summary>Executes an operation on a separate thread.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.BackgroundWorker" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.CancelAsync">
      <summary>Requests cancellation of a pending background operation.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation" /> is <see langword="false" />.</exception>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.CancellationPending">
      <summary>Gets a value indicating whether the application has requested cancellation of a background operation.</summary>
      <returns>
        <see langword="true" /> if the application has requested cancellation of a background operation; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose(System.Boolean)">
      <param name="disposing" />
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.DoWork">
      <summary>Occurs when <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync" /> is called.</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.IsBusy">
      <summary>Gets a value indicating whether the <see cref="T:System.ComponentModel.BackgroundWorker" /> is running an asynchronous operation.</summary>
      <returns>
        <see langword="true" />, if the <see cref="T:System.ComponentModel.BackgroundWorker" /> is running an asynchronous operation; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnDoWork(System.ComponentModel.DoWorkEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnProgressChanged(System.ComponentModel.ProgressChangedEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnRunWorkerCompleted(System.ComponentModel.RunWorkerCompletedEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.ProgressChanged">
      <summary>Occurs when <see cref="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)" /> is called.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)">
      <summary>Raises the <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> event.</summary>
      <param name="percentProgress">The percentage, from 0 to 100, of the background operation that is complete.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> property is set to <see langword="false" />.</exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32,System.Object)">
      <summary>Raises the <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> event.</summary>
      <param name="percentProgress">The percentage, from 0 to 100, of the background operation that is complete.</param>
      <param name="userState">The state object passed to <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" />.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> property is set to <see langword="false" />.</exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync">
      <summary>Starts execution of a background operation.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> is <see langword="true" />.</exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)">
      <summary>Starts execution of a background operation.</summary>
      <param name="argument">A parameter for use by the background operation to be executed in the <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> event handler.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> is <see langword="true" />.</exception>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted">
      <summary>Occurs when the background operation has completed, has been canceled, or has raised an exception.</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.ComponentModel.BackgroundWorker" /> can report progress updates.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.ComponentModel.BackgroundWorker" /> supports progress updates; otherwise <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.ComponentModel.BackgroundWorker" /> supports asynchronous cancellation.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.ComponentModel.BackgroundWorker" /> supports cancellation; otherwise <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventArgs">
      <summary>Provides data for the <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> event handler.</summary>
    </member>
    <member name="M:System.ComponentModel.DoWorkEventArgs.#ctor(System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DoWorkEventArgs" /> class.</summary>
      <param name="argument">Specifies an argument for an asynchronous operation.</param>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Argument">
      <summary>Gets a value that represents the argument of an asynchronous operation.</summary>
      <returns>An <see cref="T:System.Object" /> representing the argument of an asynchronous operation.</returns>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Result">
      <summary>Gets or sets a value that represents the result of an asynchronous operation.</summary>
      <returns>An <see cref="T:System.Object" /> representing the result of an asynchronous operation.</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> event. This class cannot be inherited.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:System.ComponentModel.DoWorkEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventArgs">
      <summary>Provides data for the <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> event.</summary>
    </member>
    <member name="M:System.ComponentModel.ProgressChangedEventArgs.#ctor(System.Int32,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.ProgressChangedEventArgs" /> class.</summary>
      <param name="progressPercentage">The percentage of an asynchronous task that has been completed.</param>
      <param name="userState">A unique user state.</param>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.ProgressPercentage">
      <summary>Gets the asynchronous task progress percentage.</summary>
      <returns>A percentage value indicating the asynchronous task progress.</returns>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.UserState">
      <summary>Gets a unique user state.</summary>
      <returns>A unique <see cref="T:System.Object" /> indicating the user state.</returns>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> event of the <see cref="T:System.ComponentModel.BackgroundWorker" /> class. This class cannot be inherited.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:System.ComponentModel.ProgressChangedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventArgs">
      <summary>Provides data for the MethodName<see langword="Completed" /> event.</summary>
    </member>
    <member name="M:System.ComponentModel.RunWorkerCompletedEventArgs.#ctor(System.Object,System.Exception,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" /> class.</summary>
      <param name="result">The result of an asynchronous operation.</param>
      <param name="error">Any error that occurred during the asynchronous operation.</param>
      <param name="cancelled">A value indicating whether the asynchronous operation was canceled.</param>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.Result">
      <summary>Gets a value that represents the result of an asynchronous operation.</summary>
      <returns>An <see cref="T:System.Object" /> representing the result of an asynchronous operation.</returns>
      <exception cref="T:System.Reflection.TargetInvocationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> is not <see langword="null" />. The <see cref="P:System.Exception.InnerException" /> property holds a reference to <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> is <see langword="true" />.</exception>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.UserState">
      <summary>Gets a value that represents the user state.</summary>
      <returns>An <see cref="T:System.Object" /> representing the user state.</returns>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" /> event of a <see cref="T:System.ComponentModel.BackgroundWorker" /> class.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" /> that contains the event data.</param>
    </member>
  </members>
</doc>