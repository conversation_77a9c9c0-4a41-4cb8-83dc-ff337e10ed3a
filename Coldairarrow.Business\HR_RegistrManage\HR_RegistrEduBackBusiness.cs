﻿using Coldairarrow.Entity.HR_RegistrManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.HR_RegistrManage
{
    public class HR_RegistrEduBackBusiness : BaseBusiness<HR_RegistrEduBack>, IHR_RegistrEduBackBusiness, ITransientDependency
    {
        public HR_RegistrEduBackBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_RegistrEduBack>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RegistrEduBack>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RegistrEduBack, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_RegistrEduBack> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_RegistrEduBack data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_RegistrEduBack data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RegistrEduBack>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RegistrEduBack, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_RegistrEduBack, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        public async Task AddDataListAsync(List<HR_RegistrEduBack> data)
        {
            await Task.Run(() => BulkInsert(data));
        }

        public async Task UpdateDataListAsync(List<HR_RegistrEduBack> data)
        {
            await UpdateAsync(data);
        }

        public int AddData(HR_RegistrEduBack data)
        {
            return Insert(data);
        }

        public void AddListData(List<HR_RegistrEduBack> data)
        {
            BulkInsert(data);
        }

        public int UpdatListeData(List<HR_RegistrEduBack> data)
        {
            return Update(data);
        }

        public int UpdateData(HR_RegistrEduBack data)
        {
            return Update(data);
        }

        public int DeleteData(HR_RegistrEduBack data)
        {
            return Delete(data);
        }

        public int DeleteDataListeData(List<HR_RegistrEduBack> data)
        {
            return Delete(data);
        }
        public async Task DeleteDataAsync(string id)
        {
            await DeleteAsync(this.GetIQueryable().Where(i => i.UserId == id).ToList());
        }
        #endregion

        #region 私有成员

        #endregion
    }
}