﻿<template>
  <a-card :bordered="false" :hoverable="true">
    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="hanldleAdd()">新建</a-button>
      <a-button type="primary" icon="minus" @click="handleDelete(selectedRowKeys)" :disabled="!hasSelected()"
        :loading="loading">删除</a-button>
      <a-button type="primary" icon="redo" @click="getDataList()">刷新</a-button>
    </div>

    <div class="table-page-search-wrapper">
      <!-- <a-form layout="inline">
        <a-row :gutter="10">
          <a-col :md="4" :sm="24">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="queryParam.condition">
                <a-select-option key="F_FullName">公司名称</a-select-option>
                <a-select-option key="F_EnCode">公司代码</a-select-option>
                <a-select-option key="F_ShortName">公司简称</a-select-option>
                <a-select-option key="F_Nature">公司性质</a-select-option>
                <a-select-option key="F_FoundedTime">成立时间</a-select-option>
                <a-select-option key="F_Manager">负责人</a-select-option>
                <a-select-option key="F_BusinessScope">经营范围</a-select-option>
                <a-select-option key="F_Description">备注</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item>
              <a-input v-model="queryParam.keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-button type="primary" icon="search" @click="() => {this.pagination.current = 1; this.getDataList()}">查询
            </a-button>
            <a-button style="margin-left: 8px" icon="sync" @click="() => (queryParam = {})">重置</a-button>
          </a-col>
        </a-row>
      </a-form> -->
    </div>

    <a-table ref="table" :columns="columns" :rowKey="row => row.Id" :dataSource="data" :pagination="false"
      :loading="loading" @change="handleTableChange"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true"
      :defaultExpandAllRows="true">
      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record.Id)">编辑</a>
          <a-divider type="vertical" />
          <a @click="handleDelete([record.Id])">删除</a>
        </template>
      </span>
    </a-table>

    <edit-form ref="editForm" :parentObj="this"></edit-form>
  </a-card>
</template>

<script>
import EditForm from './EditForm'
import { dictTypeInfo } from '../../../api/dict/dictValue';
const columns = [
  { title: '公司名称', dataIndex: 'F_FullName', width: '20%' },
  { title: '公司代码', dataIndex: 'F_EnCode', width: '15%' },
  { title: '公司简称', dataIndex: 'F_ShortName', width: '15%' },
  { title: '公司性质', dataIndex: 'F_Nature', width: '15%' },
  { title: '负责人', dataIndex: 'F_Manager', width: '10%' },
  { title: '成立时间', dataIndex: 'F_FoundedTime', width: '10%' },
  // { title: '经营范围', dataIndex: 'F_BusinessScope', width: '10%' },
  // { title: '备注', dataIndex: 'F_Description', width: '20%' },
  { title: '操作', dataIndex: 'action', scopedSlots: { customRender: 'action' }, width: '15%' }
]

export default {
  components: {
    EditForm
  },
  mounted () {
    this.getDataList()
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 10,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      filters: {},
      sorter: { field: 'F_SortCode', order: 'asc' },
      loading: false,
      columns,
      queryParam: {},
      selectedRowKeys: [],
      attrTypeOptions: [],
    }
  },
  created () {
    this.commonApi.dictTypeInfo('民族').then(res => {
      this.attrTypeOptions = res;
      console.log(this.attrTypeOptions);
    });

  },
  methods: {
    handleTableChange (pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.filters = { ...filters }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    getDataList () {
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/Base_Manage/Base_Company/GetTreeDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'Id',
          SortType: this.sorter.order,
          Search: this.queryParam,
          ...this.filters
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          let pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange (selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    hasSelected () {
      return this.selectedRowKeys.length > 0
    },
    hanldleAdd () {
      this.$refs.editForm.openForm()
    },
    handleEdit (id) {
      this.$refs.editForm.openForm(id)
    },
    handleDelete (ids) {
      var thisObj = this
      this.$confirm({
        title: '确认删除吗?',
        onOk () {
          return new Promise((resolve, reject) => {
            thisObj.$http.post('/Base_Manage/Base_Company/DeleteData', ids).then(resJson => {
              resolve()

              if (resJson.Success) {
                thisObj.$message.success('操作成功!')

                thisObj.getDataList()
              } else {
                thisObj.$message.error(resJson.Msg)
              }
            })
          })
        }
      })
    }
  }
}
</script>