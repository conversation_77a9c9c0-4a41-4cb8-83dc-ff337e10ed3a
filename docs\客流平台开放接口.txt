瑞为智慧商场平台开放接口
会员、非访客、频次相关接口已经废弃
 会员、非访客、频次相关接口已经废弃
 通信方式
 平台地址
 认证流程
 状态码定义
 接口
 鉴权接口
o 1 获取 accessToken 接口
 商场数据接口
o 查询商场接口
o 查询分析列表接口-旧
o 查询分析列表接口-新
o 查询商场节点结构接口
o 查询商场设备接口
o 节点新建接口
o 节点编辑接口
o 设备挂载新增接口
o 设备挂载编辑接口
 客流接口
o 客流统计接口
o 单日小时客流统计接口
o 日客流统计接口
o 实时滞留量接口
o 驻留时长查询接口
o 业态偏好查询接口
o 数据明细查询接口
o 数据统计查询接口
o 根据节点类型查询时段数据接口
o 根据节点类型查询日数据接口
o 根据节点类型查询进店数据接口
o 商场爬楼量接口
o 商场逛店个数分布查询接口
o 商铺关联关系接口
o 商场多天平均游逛店数接口
o 商场多天实时滞留量接口
 客流来源和客流走向接口
 销售数据录入接口
修订记录
日期
修订版
本
修改描述
2019-10-
10
1.0 初稿
2019-12-
19
1.7.0
1. 新增查询商场节点结构接口;
2. 新增查询商场设备接口;
2020-01-
15
1.7.4
1. 新增客流统计接口
2. 新增实时滞留量接口
2020-06-
04
V.2.4
1. 修改抓拍列表接口
2. 单日小时客流统计接口
3. 日客流统计接口
2020-06-
23
V2.5
1. 节点新建接口
2. 节点编辑接口
3. 设备挂载新增接口
4. 设备挂载编辑接口
2020-08-
13
V2.7
1. 新增客流来源客流去向接口
2020-11-
26
V.3.1
1. 实时滞留量接口添加人数返回
2020-09-
22
V4.0
1. 客流统计接口添加出店人次
2. 单日小时客流统计接口添加出店人次
3. 日客流统计接口添加出店人次
2021-11-
29
V4.3.3
1. 修改抓拍列表接口(添加上级节点去重-客流处理状态)
2021-12-
29
V4.4.3
1. 新增抓拍列表接口，去除敏感字。替代旧接口，后续旧接口
会下线
2022-01-
17
V4.4.5
1. 新增驻留时长查询接口
2. 新增业态偏好查询接口
3. 新增数据明细查询接口
2022-01-
21
V4.5.0
查询商场接口加关联 code
查询分析列表接口-旧加关联 code
查询分析列表接口-新加关联 code
节点新建接口加关联 code
节点编辑接口加关联 code
客流统计接口加关联 code
单日小时客流统计接口加关联 code
日客流统计接口加关联 code
实时滞留量接口加关联 code
驻留时长查询接口加关联 code
业态偏好查询接口加关联 code
数据明细查询接口加关联 code
客流来源和客流走向接口加关联 code
2022-03-
31
V4.7.1
新增接口：
 根据节点类型查询时段数据
 根据节点类型查询日数据
 根据节点类型查询进店数据
 商场爬楼量接口
 商场逛店个数分布接口
 商铺关联关系接口
2022-05-
10
V4.8.0 新增接口：
 商场多天平均游逛店数
 商场多天实时滞留量
2022-06-
29
V4.9.1 查询分析列表接口-旧加批次和滞留时长
查询分析列表接口-新加批次和滞留时长
2022-11-
10
V5.2.0
 新增数据统计查询接口（类似数据明细接口，但是不组装树
形，不聚合数据）
通信方式
 只支持 HTTPS 请求,采用 POST 方式
 数据传输格式: json (注:部分接口采用 form 表单形式)
平台地址
 联调环境：
o http://api.reconova.ops365.cn
 预发布环境：
o https://api.premall.reconova.com
 生产环境：
o https://api.mall.reconova.com
认证流程
1. 向平台申请 applicationId 和 sk,配置到开发者应用程序中;
2. 通过鉴权接口使用 applicationId 和 sk 获取 accessToken(有效时间为 7 天)，重
复获取将导致上次获取的 accessToken 失效;
3. 将 accessToken 放在请求头中调用平台的业务接口.
4. 建议开发者使用中控服务器统一获取和刷新 accessToken，其他业务逻辑服务器所
使用的 accessToken 均来自于该中控服务器，不应该各自去刷新，否则容易造成冲
突，导致 accessToken 覆盖而影响业务；
状态码定义
状态码 描述
-1 用户未登陆
-2 数据库异常
-3 系统异常
-4 参数异常
-5 请求方式不支持,请在 POST/GET 切换一下
100 applicationId 或 sk 不正确
101 非法的 accessToken
102 accessToken 已失效
103 商场无权限
104 非法的接口地址
接口
鉴权接口
1 获取 accessToken 接口
· 接口描述
获取 accessToken,用于接口鉴权
重复调用,失效旧的 accessToken,生成新的 accessToken · 接口地址
/api/access/token/get
· 请求参数
请求格式 参数名 描述 类型 是否必填 备注
body
applicationId 应用 ID string 是
sk 应用密钥 string 是
· 返回数据
code
integer
状态码
data
{
accessToken string
expiresIn
long
有效时间
}
message
string
错误信息
 返回示例:
{
"message": "成功",
"code": 0,
"data": {
"accessToken": "e08d23d7-7e9c-452d-af3f-993ca019eeab",
"expiresIn": 604800 //token 有效时间
}
}
商场数据接口
查询商场接口
· 接口描述
获取应用下所有的商场信息
· 接口地址
/merchant/api/mall/list · 请求参数
请求格式 参数名 描述 类型 是否必填 备注
header accessToken accessToken string 是
· 返回数据
code
integer
状态码
data
[{
areaCode
string
商场编码
name
string
商场名称
relCode 关联 code
}]
message
string
错误信息
 返回示例
{
"message": "成功",
"code": 0,
"data": [
{
"areaCode": "000G-0001-0001", //商场编码
"name": "商场 9n9r3-默认", //商场名称
"relCode":"asb"//关联 code
},
{
"areaCode": "000G-0001-0002",
"name": "商场 001",
"relCode":"adb"
}
]
}
查询分析列表接口-旧
· 接口描述
获取商场分析记录
· 接口地址
/snapdata/api/snap/data/list · 请求参数
请求格
式
参数名 描述 类型
是否
必填
备注
header accessToken accessToken string 是 参数放在请求头 header 中
body
areaCode 商场编码 string 是
id 起始主键位
置
long 否
pageNo 页码 integer 是 不能小于 1
pageSize 页面大小 integer 是 不能大于 500
snapTime 分析时间 string 是 格式: yyyy-MM-dd
startTime 开始时刻 string 否
格式: HH:mm:ss(系统默认
00:00:00)
endTime 结束时刻 string 否
格式:HH:mm:ss(系统默认
23:59:59) 开始时刻不能大于结束
时刻
· 返回数据
code
integer
状态码
data
{
list
[{
age
integer
年龄 -1:头肩或无特征
deviceCode
string
设备编码
fullBodyUrl
string
全身图路径
gatewayFaceId
string
出入口 reid
gatewayRecordStatus
integer
出入口库-客流处理状态 0 :计入客流 1:去
重
gatewaySnapType
integer
出入口抓拍类型 0 质量合格 1 低质量
id
string
唯一主键
mallCode
string
商户编码
nodeCode
string
节点编码
relCode 关联 code
nodeName
string
节点名称
overallFaceId
string
总库 reid
overallRecordStatus
integer
总库-轨迹处理状态 0：计入轨迹 1：去重
overallSnapType
integer
总库分析类型 0 质量合格 1 低质量
sex
integer
性别，0：男 1：女 -1:头肩或无特征
snapTime
string
分析时间
userStatus
integer
客流方向标志,1 店外徘徊, 2:入店,4:出店,
userType
integer
用户类型 0:普通客户 2:非访客
nodeRecordStatus
integer
上级节点去重-客流处理状态 0：计入客流
1：去重 -1:作为报表系统汇总判断，不计
入业务处理
batchId
string
批次 id
resideTime
integer
停留时长,单位秒
bodyFeature
(全身图特征，只有开
了
改功能才进行数据返
回)
otherOrnaments
string
其他饰品，1 为领带，5
为围巾，9 为背包
格式：0,1,2,3
bottomType
Integer
裤子类型：-1 为不识别，
0 为短裙，1 为长裤，2 为
牛仔裤，3 为短裤，4 为
连体裤，5 为紧身裤，6
为长裙，7 为丝袜
bottomColor
Integer
裤子颜色: -1 为不识别，
0 为黑色，1 为灰色，2 为
白色，3 为橙色，4 为粉
色，5，为红色，6 为绿
色，7 为棕色，8 为蓝
色，9 为黄色，10 为紫
色，11 为金色，12 为银
色，13 为其他
frockType
Integer
上衣类型：-1 为不识别，
0 为 T 恤，1 为衬衫，2 为
披肩，3 为运动衫，4 为
背心，5 为毛衣，6 为连
衣裙，7 为外套，8 为卫
衣，9 为马夹，10 为无袖
套衫
frockColor
Integer
上衣颜色: -1 为不识别，
0 为黑色，1 为灰色，2 为
白色，3 为橙色，4 为粉
色，5，为红色，6 为绿
色，7 为棕色，8 为蓝
色，9 为黄色，10 为紫
色，11 为金色，12 为银
色，13 为其他
}]
pageNo
integer
example: 1
当前页
pageSize
integer
example: 20
每页大小
total
integer
example: 100
总数
}
message
string
错误信息
 返回示例:
{
"message": "成功",
"code": 0,
"data": {
"list": [
{
"id": "201321232552038400",
"mallCode": "000G-0001-0001",
"fullBodyUrl": "http://mall.reconova.com/node1/s1/upload/snap/0/2019-07-
10/DJ201906220017/1312341241412467968.jpg",
"snapTime": "2019-07-10 20:58:38",
"nodeCode": "000G-0001-0001-0003",
"relCode":"adb",
"nodeName": "品牌 75cyk/商场 9n9r3-默认/出入口 356b5",
"deviceCode": "DJ201906220017",
"sex": 1,
"age": 27,
"gatewayFaceId": "201321232795176960",
"gatewaySnapType": 0,
"gatewayRecordStatus": 0,
"overallFaceId": "201321233009086464",
"overallSnapType": 0,
"overallRecordStatus": 0,
"userStatus": 2,
"nodeRecordStatus":0,
"batchId":"1234",
"resideTime":123,
"bodyFeature": {"frockType": 0,"bottomType": 0,"frockColor": 0,"bottomColor":
0,"otherOrnaments": "0,1,2,"}}},
{
"id": "201321244849737728",
"mallCode": "000G-0001-0001",
"fullBodyUrl": "http://mall.reconova.com/node1/s1/upload/snap/0/2019-07-
10/DJ201906220017/1312341241412467968.jpg",
"snapTime": "2019-07-10 20:58:41",
"nodeCode": "000G-0001-0001-0003",
"relCode":"addb",
"nodeName": "品牌 75cyk/商场 9n9r3-默认/出入口 356b5",
"deviceCode": "DJ201906220017",
"sex": 1,
"age": 27,
"gatewayFaceId": "201321232795176960",
"gatewaySnapType": 0,
"gatewayRecordStatus": 1,
"overallFaceId": "201321233009086464",
"overallSnapType": 0,
"overallRecordStatus": 1,
"userStatus": 2,
"userType": 0,
"nodeRecordStatus":0,
"batchId":"1234",
"resideTime":123,
"bodyFeature": {"frockType": 0,"bottomType": 0,"frockColor": 0,"bottomColor":
0,"otherOrnaments": "0,1,2,"}}
],
"pageNo": 1,
"pageSize": 2,
"total": 630
}
}
查询分析列表接口-新
· 接口描述
获取商场分析记录
· 接口地址
/snapdata/api/snap/data/record
· 请求参数
请求格
式
参数名 描述 类型
是否
必填
备注
header accessToken accessToken string 是 参数放在请求头 header 中
body
areaCode 商场编码 string 是
id 起始主键位
置
long 否
pageNo 页码 integer 是 不能小于 1
pageSize 页面大小 integer 是 不能大于 500
snapTime 分析时间 string 是 格式: yyyy-MM-dd
startTime 开始时刻 string 否
格式: HH:mm:ss(系统默认
00:00:00)
endTime 结束时刻 string 否
格式:HH:mm:ss(系统默认
23:59:59) 开始时刻不能大于结束
时刻
· 返回数据
code
integer
状态码
data
{
list
[{
age
integer
年龄 -1:头肩或无特征
deviceCode
string
设备编码
fullBodyUrl
string
全身图路径
gatewayReid
string
出入口 reid
gatewayRecordStatus integer
出入口库-客流处理状态 0 :计入客流 1:去
重
gatewaySnapType
integer
出入口抓拍类型 0 质量合格 1 低质量
id
string
唯一主键
mallCode
string
商户编码
nodeCode
string
节点编码
relCode 关联 code
nodeName
string
节点名称
overallReid
string
总库 reid
overallRecordStatus
integer
总库-轨迹处理状态 0：计入轨迹 1：去重
overallSnapType
integer
总分析类型 0 质量合格 1 低质量
sex
integer
性别，0：男 1：女 -1:头肩或无特征
snapTime
string
分析时间
userStatus
integer
客流方向标志,1 店外徘徊, 2:入店,4:出店,
userType
integer
用户类型 0:普通客户 2:非访客
nodeRecordStatus
integer
上级节点去重-客流处理状态 0：计入客流
1：去重 -1:作为报表系统汇总判断，不计
入业务处理
feature 特征信息
batchId
string
批次 id
resideTime
integer
停留时长,单位秒
bodyFeature
(全身图特征，只有开
了
改功能才进行数据返
回)
otherOrnaments
string
其他：0 为帽子，1 为领
带，2 为项链，3 为眼
镜，4 为墨镜，5 为围
巾，6 为手表手链，7 为
手套，8 为挎包，9 为背
包，10 为手提包，11 为
皮带
格式：0,1,2,3
bottomType
Integer
裤子类型：-1 为不识别，
0 为短裙，1 为长裤，2 为
牛仔裤，3 为短裤，4 为
连体裤，5 为紧身裤，6
为长裙，7 为丝袜
bottomColor
Integer
裤子颜色: -1 为不识别，
0 为黑色，1 为灰色，2 为
白色，3 为橙色，4 为粉
色，5，为红色，6 为绿
色，7 为棕色，8 为蓝
色，9 为黄色，10 为紫
色，11 为金色，12 为银
色，13 为其他
frockType
Integer
上衣类型：-1 为不识别，
0 为 T 恤，1 为衬衫，2 为
披肩，3 为运动衫，4 为
背心，5 为毛衣，6 为连
衣裙，7 为外套，8 为卫
衣，9 为马夹，10 为无袖
套衫
frockColor
Integer
上衣颜色: -1 为不识别，
0 为黑色，1 为灰色，2 为
白色，3 为橙色，4 为粉
色，5，为红色，6 为绿
色，7 为棕色，8 为蓝
色，9 为黄色，10 为紫
色，11 为金色，12 为银
色，13 为其他
}]
pageNo
integer
example: 1
当前页
pageSize
integer
example: 20
每页大小
total
integer
example: 100
总数
}
message
string
错误信息
 返回示例:
{
"message": "成功",
"code": 0,
"data": {
"list": [
{
"id": "201321232552038400",
"mallCode": "000G-0001-0001",
"fullBodyUrl": "http://mall.reconova.com/node1/s1/upload/snap/0/2019-07-
10/DJ201906220017/1312341241412467968.jpg",
"snapTime": "2019-07-10 20:58:38",
"nodeCode": "000G-0001-0001-0003",
"relCode":"addb",
"nodeName": "品牌 75cyk/商场 9n9r3-默认/出入口 356b5",
"deviceCode": "DJ201906220017",
"sex": 1,
"age": 27,
"gatewayReid": "201321232795176960",
"gatewaySnapType": 0,
"gatewayRecordStatus": 0,
"overallReid": "201321233009086464",
"overallSnapType": 0,
"overallRecordStatus": 0,
"userStatus": 2,
"nodeRecordStatus":0,
"batchId":"1234",
"resideTime":123,
"feature":{}
},
{
"id": "201321244849737728",
"mallCode": "000G-0001-0001",
"fullBodyUrl": "http://mall.reconova.com/node1/s1/upload/snap/0/2019-07-
10/DJ201906220017/1312341241412467968.jpg",
"snapTime": "2019-07-10 20:58:41",
"nodeCode": "000G-0001-0001-0003",
"relCode":"addbw",
"nodeName": "品牌 75cyk/商场 9n9r3-默认/出入口 356b5",
"deviceCode": "DJ201906220017",
"sex": 1,
"age": 27,
"gatewayReid": "201321232795176960",
"gatewaySnapType": 0,
"gatewayRecordStatus": 1,
"overallReid": "201321233009086464",
"overallSnapType": 0,
"overallRecordStatus": 1,
"userStatus": 2,
"userType": 0,
"nodeRecordStatus":0,
"batchId":"1234",
"resideTime":123,
"feature":{},
"bodyFeature": {"frockType": 0,"bottomType": 0,"frockColor": 0,"bottomColor":
0,"otherOrnaments": "0,1,2,"}}
],
"pageNo": 1,
"pageSize": 2,
"total": 630
}
}
查询商场节点结构接口
· 接口描述
按商场编码或商场下任意节点编码查询节点结构,返回节点层级结构数据
· 接口地址
/merchant/api/mall/area/tree · 请求参数
请求格式 参数名 描述 类型 是否必填 备注
header accessToken accessToken string 是 参数放在请求头 header 中
body areaCode 节点编码 string 是 商场编码或商场下任意节点编码
· 返回数据
code
integer
状态码
data
[{
name
string
节点名称
areaCode
string
节点编码
parentAreaCode
string
父节点编码
areaType
int
节点类型
100:合作方
200:品牌
300:商场
400:楼层
500:商铺
600:出入口
688: 过店
700:关注区域
800:楼栋
areaTypeName
string
节点类型名称
relCode 关联 code，用于存储商铺编号等外部关联 code
children
[节点]
子节点列表
}]
message
string
错误信息
 返回示例:
{
"message": "成功",
"code": 0,
"data": [{
"parentAreaCode": "0001-0004",
"areaCode": "0001-0004-0001",
"name": "青青测试 mall",
"areaType": 300,
"areaTypeName": "商场",
"relCode": "666",
"children": [{
"parentAreaCode": "0001-0004-0001",
"areaCode": "0001-0004-0001-000M",
"name": "1F",
"areaType": 400,
"areaTypeName": "楼层",
"children": [{
"parentAreaCode": "0001-0004-0001-000M",
"areaCode": "0001-0004-0001-000M-0001",
"name": "出入口 1312qweq",
"areaType": 600,
"areaTypeName": "出入口(店计)",
"relCode": "6",
"children": []
},
{
"parentAreaCode": "0001-0004-0001-000M",
"areaCode": "0001-0004-0001-000M-0002",
"name": "出入口 2",
"areaType": 600,
"areaTypeName": "出入口(店计)",
"children": []
}]
},
{
"parentAreaCode": "0001-0004-0001",
"areaCode": "0001-0004-0001-0001",
"name": "出入口 778",
"areaType": 600,
"areaTypeName": "出入口(店计)",
"relCode": "7",
"children": []
},
{
"parentAreaCode": "0001-0004-0001",
"areaCode": "0001-0004-0001-0004",
"name": "关注区域 4w9",
"areaType": 700,
"areaTypeName": "关注区域(店计)",
"relCode": "8",
"children": []
}]
}]
}
查询商场设备接口
· 接口描述
通过该接口可以查询商场下挂载的设备信息。如传参是楼层编码，则平台返回的设备是归
属到楼层下的，包括楼层出入口和楼层商铺下的出入口等所有设备
· 接口地址
/device/api/device/list
· 请求参数
请求格式 参数名 描述 类型 是否必填 备注
header accessToken accessToken string 是 参数放在请求头 header 中
body areaCode 区域编码 string 是
· 返回数据
code
integer
状态码
data
[{
deviceName
string
设备名称
deviceCode
string
设备编码
deviceType
int
设备类型
deviceBarCode
string
设备条码
onlineStatusName
string
设备状态（在线/离线）
nodes
[
string
挂载节点
]
}]
message string
错误信息
 返回示例:
{
"message": "成功",
"code": 0,
"data": [
{
"deviceName": "v170test",
"deviceCode": "DJ5a19352159ca",
"deviceType": 0,
"deviceBarCode": "19352159ca",
"onlineStatusName": "离线",
"nodes": [
"0001-0001-0001-0001"
]},
{
"deviceName": "test-name",
"deviceCode": "DJ201911130001",
"deviceType": 0,
"deviceBarCode": "1911130001",
"onlineStatusName": "离线",
"nodes": [
"0001-0001-0001-0002",
"0001-0001-0001-0003"
]}
]
}
节点新建接口
· 接口描述
新建商场之下的节点
· 接口地址
/merchant/api/merchant/area/node/create · 请求参数
请求格
式
参数名 描述 类型
是否必
填
备注
header accessToken accessToken string 是 参数放在请求头 header 中
body
areaCode
上级节点编
号
string 是
商场及以下节点（不包含叶子节
点）
name 节点名称 String 是 创建的节点的名称
areaType 节点类型 String 是
400:楼层,500:商铺,600:出入
口,700:关注区域
relCode 关联 code String 否 关联 code · 返回数据
code
integer
状态码，0 成功，其他失败
data
{
areaCode
string
节点编码
relCode 关联 code
}
message
string
错误信息
 返回示例:
{
"message": "成功",
"code": 0,
"data": {
"areaCode": "0001-0001-0001-000H",
"relCode":"1ds"
}
}
节点编辑接口
· 接口描述
对节点名称进行修改
· 接口地址
/merchant/api/merchant/area/node/update · 请求参数
请求格式 参数名 描述 类型 是否必填 备注
header accessToken accessToken string 是 参数放在请求头 header 中
body
areaCode 节点编码 string 是 商场之下节点编码
name 节点名称 string 是 修改后的节点名称
relCode 关联 code string 否 关联 code
· 返回数据
code
integer
状态码，0 成功，其他失败
message
string
错误信息
 返回示例:
{
"message": "成功",
"code": 0
}
设备挂载新增接口
· 接口描述
对接方可以通过该接口，将设备挂载到相应的叶子节点上（出入口/关注区域）。前置条
件：需要技术支持人员进行设备添加及启用操作。
· 接口地址
/device/api/device/mount/add · 请求参数
请求格式 参数名 描述 类型 是否必填 备注
header accessToken accessToken string 是 参数放在请求头 header 中
body
areaCode 节点编码 string 是 所挂载的节点所在的商场编码
areaCodes 节点编码集合 list 是 挂载的节点编码集合
deviceCode 设备编码 string 是 要挂载的设备编码
deviceName 设备名称 string 是 挂载的设备的名称
· 返回数据
code
integer
状态码，0 成功，其他失败，所有节点编码都准确才能成功，否则操作失败，不
进行挂载操作
message
string
错误信息
 返回示例:
{
"message": "成功",
"code": 0
}
设备挂载编辑接口
· 接口描述
对接方可以对已挂载设备的名称及挂载节点进行调整
· 接口地址
/device/api/device/mount/update · 请求参数
请求格式 参数名 描述 类型 是否必填 备注
header accessToken accessToken string 是 参数放在请求头 header 中
body
areaCode 节点编码 string 是 所挂载的节点所在的商场编码
areaCodes 节点编码集合 list 否 挂载的节点编码集合
deviceCode 设备编码 string 是 要挂载的设备编码
deviceName 设备名称 string 否 挂载的设备的名称
· 返回数据
code
integer
状态码，0 成功，其他失败，所有节点编码都准确才能成功，否则操作失败，不
进行挂载操作
message
string
错误信息
 返回示例:
{
"message": "成功",
"code": 0
}
客流接口
客流统计接口
· 接口描述
客流统计接口
· 接口地址
/snapdata/api/report/customer · 请求参数
请求格
式
参数名 描述 类型
是否必
填
备注
header accessToken accessToken string 是
body
areaCode 节点编码 string 是 商场编码或商场下任意节点编码
startDate 开始日期 string 是 example = "2019-06-29"
endDate 结束日期 string 是 example = "2019-06-29"
startHour 开始时刻 int 否 时段为选填参数（时段传两位例
endHour 结束时刻 int 否 如：09，11）
· 返回数据（传时段）
code
integer
状态码
data
[{
nodeCode
string
节点编码
relCode
String
关联 code
visitors
int
人数
intoTrips
int
进店人次
outTrips 出店人次
malesage1
int
12 岁以下男
malesage2
int
13-20 男
malesage3
int
21-25 男
malesage4
int
26-30 男
malesage5
int
31-35 男
malesage6
int
36-45 男
malesage7
int
46 以上男
femalesage1
int
0-12 女
femalesage2
int
13-20 女
femalesage3
int
21-25 女
femalesage4
int
26-30 女
femalesage5
int
31-35 女
femalesage6
int
36-45 女
femalesage7
int
46 以上女
hour
string
时段
}]
message
string
错误信息
 返回示例:
{
"message": "成功",
"code": 0,
"data": [
{
"nodeCode": "0001-0001-0001-0001",
"relCode":"adb",
"visitors": 1,
"intoTrips": 1,
"outTrips": 1,
"malesage1": 0,
"malesage2": 0,
"malesage3": 0,
"malesage4": 0,
"malesage5": 0,
"malesage6": 0,
"malesage7": 0,
"femalesage1": 0,
"femalesage2": 0,
"femalesage3": 0,
"femalesage4": 0,
"femalesage5": 0,
"femalesage6": 0,
"femalesage7": 0,
"hour": "2019-12-05 17:00-18:00"
}
]
}· 返回数据（不传时段）
code
integer
状态码
data
[{
nodeCode
string
节点编码
relCode
String
关联 code
visitors
int
人数
intoTrips
int
进店人次
outTrips
int
出店人次
malesage1
int
12 岁以下男
malesage2
int
13-20 男
malesage3
int
21-25 男
malesage4
int
26-30 男
malesage5
int
31-35 男
malesage6
int
36-45 男
malesage7
int
46 以上男
femalesage1
int
0-12 女
femalesage2
int
13-20 女
femalesage3
int
21-25 女
femalesage4
int
26-30 女
femalesage5
int
31-35 女
femalesage6
int
36-45 女
femalesage7
int
46 以上女
day
string
日期
averageStayTime
double
平均停留时长（min）
}]
message
string
错误信息
 返回示例:
{
"message": "成功",
"code": 0,
"data": [
{
"nodeCode": "0001-0001-0001-0001",
"relCode":"adb",
"visitors": 2,
"intoTrips": 3,
"outTrips": 1,
"malesage1": 0,
"malesage2": 0,
"malesage3": 0,
"malesage4": 0,
"malesage5": 0,
"malesage6": 0,
"malesage7": 0,
"femalesage1": 0,
"femalesage2": 0,
"femalesage3": 0,
"femalesage4": 0,
"femalesage5": 0,
"femalesage6": 0,
"femalesage7": 0,
"day": "2019-12-05",
"averageStayTime": 0
} ]
}
单日小时客流统计接口
· 接口描述
单日小时客流统计接口
· 接口地址
/snapdata/api/customer/count/hour · 请求参数
请求格式 参数名 描述 类型 是否必填 备注
header accessToken accessToken string 是
body areaCode 节点编码 string 是 商场编码或商场下任意节点编码
date 查询日期 string 是 example = "2019-06-29" · 返回数据
code
integer
状态码
data
[{
nodeCode
string
节点编码
relCode
String
关联 code
visitors
int
人数
intoTrips
int
进店人次
outTrips 出店人次
malesage1
int
12 岁以下男
malesage2
int
13-20 男
malesage3
int
21-25 男
malesage4
int
26-30 男
malesage5
int
31-35 男
malesage6
int
36-45 男
malesage7
int
46 以上男
femalesage1
int
0-12 女
femalesage2
int
13-20 女
femalesage3
int
21-25 女
femalesage4
int
26-30 女
femalesage5
int
31-35 女
femalesage6
int
36-45 女
femalesage7
int
46 以上女
hour
string
时段
}]
message
string
错误信息
 返回示例:
{
"message":"成功",
"code":0,
"data":[
{
"nodeCode":"083L-0001-0001-0001",
"relCode":"adb",
"visitors":1,
"intoTrips":1,
"outTrips":1,
"malesage1":0,
"malesage2":0,
"malesage3":0,
"malesage4":0,
"malesage5":0,
"malesage6":0,
"malesage7":0,
"femalesage1":0,
"femalesage2":0,
"femalesage3":1,
"femalesage4":0,
"femalesage5":0,
"femalesage6":0,
"femalesage7":0,
"members":0,
"hour":"2020-06-02 01:00-02:00"
}
]
}
日客流统计接口
· 接口描述
日客流统计接口
· 接口地址
/snapdata/api/customer/count/day · 请求参数
请求格式 参数名 描述 类型 是否必填 备注
header accessToken accessToken string 是
body areaCode 节点编码 string 是 商场编码或商场下任意节点编码
startDate 开始日期 string 是 example = "2019-06-29"
endDate 结束日期 string 是 example = "2019-06-29" · 返回数据（传时段）
code
integer
状态码
data
[{
nodeCode
string
节点编码
relCode
String
关联 code
visitors
int
人数
intoTrips
int
进店人次
outTrips
int
出店人次
malesage1
int
12 岁以下男
malesage2
int
13-20 男
malesage3
int
21-25 男
malesage4
int
26-30 男
malesage5
int
31-35 男
malesage6
int
36-45 男
malesage7
int
46 以上男
femalesage1
int
0-12 女
femalesage2
int
13-20 女
femalesage3
int
21-25 女
femalesage4
int
26-30 女
femalesage5
int
31-35 女
femalesage6
int
36-45 女
femalesage7
int
46 以上女
day
string
日期
}]
message
string
错误信息
 返回示例:
{
"message":"成功",
"code":0,
"data":[
{
"nodeCode":"0875-0001-0001",
"relCode":"adb",
"visitors":18,
"intoTrips":19,
"outTrips":19,
"malesage1":0,
"malesage2":1,
"malesage3":0,
"malesage4":3,
"malesage5":1,
"malesage6":1,
"malesage7":2,
"femalesage1":1,
"femalesage2":1,
"femalesage3":6,
"femalesage4":0,
"femalesage5":2,
"femalesage6":0,
"femalesage7":0,
"members":5,
"day":"2020-06-02"
}
]
}
实时滞留量接口
· 接口描述
实时滞留量接口
· 接口地址
/snapdata/api/mall/stayTrip · 请求参数
请求格
式
参数名 描述 类型
是否
必填
备注
header accessToken accessToken string 是
body areaCodes 节点编码 string[] 是
商场编码或商场下节点编码（不支
持出入口和关注区域）集合
· 返回数据
code
integer
状态码
data
{[
stayTrips
int
实时滞留量
areaCode
string
区域编码
relCode
String
关联 code
date
string
最新统计时间
visitors
int
实时汇总人数
]}
message
string
错误信息
 返回示例
{
"message": "成功",
"code": 0,
"data": [
{
"stayTrips": 0,
"visitors": 10,
"areaCode": "0001-0001-0001",
"relCode":"adb",
"date": "2020-01-15 11:46:17"
}
]
}
驻留时长查询接口
· 接口描述
驻留时长查询接口
· 接口地址
/snapdata/api/stay/time · 请求参数
请求格式 参数名 描述 类型 是否必填 备注
header accessToken accessToken string 是
body
areaCode 节点编码 string 是 商场编码或商场下任意节点编码
date 查询日期 string 是 example = "2019-06-29" · 返回数据
code
integer
状态码
data
{
areaCode
String
节点编码
relCode
String
关联 code
stayTime
float
平均驻留时长（单位：分钟）
stayTimePct
List<float>
访客滞留时长占比（<1 小时，1-3 小时，3-5 小时，>5 小时）, 百分比，小数点后两位
}
message
string
错误信息
 返回示例
{
"message": "成功",
"code": 0,
"data":
{
"areaCode":"0001-0002-0002",
"relCode":"adb",
"stayTime": 10.7,
"stayTimePct": [25.00, 25.00, 25.00, 25.00]
}
}
业态偏好查询接口
· 接口描述
业态偏好查询接口
· 接口地址
/snapdata/api/customer/tag · 请求参数
请求格式 参数名 描述 类型 是否必填 备注
header accessToken accessToken string 是
body
areaCode 节点编码 string 是 商场编码
startDate 开始日期 LocalDate 是 example = "2019-06-29"
endDate 结束日期 LocalDate 是 最大查询范围 15 天
· 返回数据
code
integer
状态码
data
areaCode
节点编码 String
relCode
关联 code String
[
name
String
名称
value
int
数值
percent
double
百分比
]
message
string
错误信息
 返回示例
{
"code": 0,
"message": "成功",
"data": {
"areaCode":"0001-0002-0002",
"relCode":"adb",
"tagPreferences":[
{
"name": "超市(1.45%)",
"value": 7221,
"percent": 1.45
},
{
"name": "零售(59.56%)",
"value": 297391,
"percent": 59.56
},
{
"name": "影院(0.95%)",
"value": 4739,
"percent": 0.95
},
{
"name": "儿童生活配套(14.28%)",
"value": 71296,
"percent": 14.28
},
{
"name": "餐饮(23.77%)",
"value": 118676,
"percent": 23.77
}
]
}
}
数据明细查询接口
· 接口描述
数据明细查询接口
· 接口地址
/snapdata/api/data/detail · 请求参数
请求格
式
参数名 描述 类型
是
否
必
填
备注
header accessToken accessToken string 是
body
nodeCodes 节点编码列表 List<String> 是 商场及商场以下节点编码
periodType
查询周期：日-1；
周-2；月-3；季度4；半年-5
int 是 example = 1
start 开始时间 String 是
注意是字符串，日-2022-
11-10，第一周-2022-01，
月-2022-03，第一季度2022-1，上半年-2022-1
end 结束时间 String 是
注意是字符串，日-2022-
11-10，第一周-2022-01，
月-2022-03，第一季度2022-1，上半年-2022-1 · 返回数据
code
integer
状态码
data
{[
name
String
节点名称
areaCode
String
节点编码
relCode
String
关联 code
parentAreaCode
String
节点父节点编码
children
List
子节点列表
visitors 人数
intoTrips 进店人次
malesage1
int
12 岁以下男
malesage2
int
13-20 男
malesage3
int
21-25 男
malesage4
int
26-30 男
malesage5
int
31-35 男
malesage6
int
36-45 男
malesage7
int
46 以上男
femalesage1
int
0-12 女
femalesage2
int
13-20 女
femalesage3
int
21-25 女
femalesage4
int
26-30 女
femalesage5
int
31-35 女
femalesage6
int
36-45 女
femalesage7
int
46 以上女
]}
message
string
错误信息
 返回示例
{
"code": 0,
"message": "成功",
"data": [
{
"name": "演示",
"parentAreaCode": "0001-0001",
"areaCode": "0001-0001-0001",
"relCode":"adb",
"children": [
{
"name": "B1 楼层",
"parentAreaCode": "0001-0001-0001",
"areaCode": "0001-0001-0001-0003",
"relCode":"adb",
"children": [
{
"name": "商铺",
"parentAreaCode": "0001-0001-0001-0003",
"areaCode": "0001-0001-0001-0003-0004",
"relCode":"adb",
"children": [
{
"name": "出入口 4",
"parentAreaCode": "0001-0001-0001-0003-0004",
"areaCode": "0001-0001-0001-0003-0004-0001",
"relCode":"adb",
"children": null,
"visitors": 336,
"intoTrips": 507,
"malesage1": 0,
"malesage2": 0,
"malesage3": 22,
"malesage4": 123,
"malesage5": 74,
"malesage6": 6,
"malesage7": 0,
"femalesage1": 0,
"femalesage2": 0,
"femalesage3": 17,
"femalesage4": 58,
"femalesage5": 26,
"femalesage6": 10,
"femalesage7": 0
}
],
"visitors": 336,
"intoTrips": 507,
"malesage1": 0,
"malesage2": 0,
"malesage3": 22,
"malesage4": 123,
"malesage5": 74,
"malesage6": 6,
"malesage7": 0,
"femalesage1": 0,
"femalesage2": 0,
"femalesage3": 17,
"femalesage4": 58,
"femalesage5": 26,
"femalesage6": 10,
"femalesage7": 0
}
],
"visitors": 336,
"intoTrips": 507,
"malesage1": 0,
"malesage2": 0,
"malesage3": 22,
"malesage4": 123,
"malesage5": 74,
"malesage6": 6,
"malesage7": 0,
"femalesage1": 0,
"femalesage2": 0,
"femalesage3": 17,
"femalesage4": 58,
"femalesage5": 26,
"femalesage6": 10,
"femalesage7": 0
},
{
"name": "B2 楼层",
"parentAreaCode": "0001-0001-0001",
"areaCode": "0001-0001-0001-0005",
"relCode":"adb",
"children": [
{
"name": "出入口 8",
"parentAreaCode": "0001-0001-0001-0005",
"areaCode": "0001-0001-0001-0005-0002",
"relCode":"adb",
"children": null,
"visitors": 0,
"intoTrips": 0,
"malesage1": 0,
"malesage2": 0,
"malesage3": 0,
"malesage4": 0,
"malesage5": 0,
"malesage6": 0,
"malesage7": 0,
"femalesage1": 0,
"femalesage2": 0,
"femalesage3": 0,
"femalesage4": 0,
"femalesage5": 0,
"femalesage6": 0,
"femalesage7": 0
}
],
"visitors": 0,
"intoTrips": 0,
"malesage1": 0,
"malesage2": 0,
"malesage3": 0,
"malesage4": 0,
"malesage5": 0,
"malesage6": 0,
"malesage7": 0,
"femalesage1": 0,
"femalesage2": 0,
"femalesage3": 0,
"femalesage4": 0,
"femalesage5": 0,
"femalesage6": 0,
"femalesage7": 0
}
],
"visitors": 406,
"intoTrips": 1012,
"malesage1": 0,
"malesage2": 0,
"malesage3": 28,
"malesage4": 140,
"malesage5": 86,
"malesage6": 7,
"malesage7": 0,
"femalesage1": 0,
"femalesage2": 0,
"femalesage3": 25,
"femalesage4": 74,
"femalesage5": 33,
"femalesage6": 13,
"femalesage7": 0
}
]
}
数据统计查询接口
· 接口描述
数据统计查询接口
· 接口地址
/snapdata/api/data/statistics · 请求参数
请求格
式
参数名 描述 类型
是
否
必
填
备注
header accessToken accessToken string 是
body nodeCodes 节点编码列表 List<String>
节
点/
关
联
编
码
列
表
商场及商
场以下节
点编码
不
能
两
个
都
为
空
relCodes 关联编码列表 List<String>
节
点/
关
联
编
码
列
表
不
能
两
个
都
为
空
商场及商
场以下节
点的关联
编码
mallCode 商场编码 string 是 商场编码
periodType
查询周期：日1；周-2；月-3；
季度-4；半年-5
int 是
example =
1
start 开始时间 String 是
注意是字
符串，日2022-11-
10，第一
周-2022-
01，月2022-03，
第一季度2022-1，
上半年2022-1
end 结束时间 String 是
注意是字
符串，日-
2022-11-
10，第一
周-2022-
01，月2022-03，
第一季度2022-1，
上半年2022-1 · 返回数据
code
integer
状态码
data
[{
period
String
周期，例如 2022-11-10
periodType
Integer
周期类型，和请求参数一致
list
name
String
节点名称
areaCode
String
节点编码
relCode
String
关联 code
parentAreaCode
String
节点父节点编码
children
List
子节点列表
visitors 人数
intoTrips 进店人次
malesage1
int
12 岁以下男
malesage2
int
13-20 男
malesage3
int
21-25 男
malesage4
int
26-30 男
malesage5
int
31-35 男
malesage6
int
36-45 男
malesage7
int
46 以上男
femalesage1
int
0-12 女
femalesage2
int
13-20 女
femalesage3
int
21-25 女
femalesage4
int
26-30 女
femalesage5
int
31-35 女
femalesage6
int
36-45 女
femalesage7
int
46 以上女
}]
message
string
错误信息
 返回示例
{
"code": 0,
"message": "成功
",
"data": [
{
"period": "2022-
02",
"periodType": 3,
"list": [
{
"name": "演示商场
",
"parentAreaCode":
"0001-0002",
"areaCode":
"0001-0002-0002",
"areaType": 300,
"relCode":
"000035",
"children": null,
"visitors":
235803,
"intoTrips":
338069,
"malesage1":
4464,
"malesage2":
3715,
"malesage3":
7891,
"malesage4":
30265,
"malesage5":
28707,
"malesage6":
14671,
"malesage7":
1413,
"femalesage1":
3163,
"femalesage2":
5841,
"femalesage3":
16377,
"femalesage4":
59678,
"femalesage5":
38574,
"femalesage6":
18474,
"femalesage7":
2570
},
{
"name": "一层",
"parentAreaCode":
"0001-0002-0002",
"areaCode":
"0001-0002-0002-
000K",
"areaType": 400,
"relCode": "1F",
"children": null,
"visitors":
239135,
"intoTrips":
297385,
"malesage1":
4209,
"malesage2":
3889,
"malesage3":
8936,
"malesage4":
31755,
"malesage5":
29644,
"malesage6":
15459,
"malesage7":
1401,
"femalesage1":
3247,
"femalesage2":
6151,
"femalesage3":
17420,
"femalesage4":
58636,
"femalesage5":
37346,
"femalesage6":
18460,
"femalesage7":
2582
}
]
},
{
"period": "2022-
03",
"periodType": 3,
"list": [
{
"name": "演示商场
",
"parentAreaCode":
"0001-0002",
"areaCode":
"0001-0002-0002",
"areaType": 300,
"relCode":
"000035",
"children": null,
"visitors": 0,
"intoTrips": 0,
"malesage1": 0,
"malesage2": 0,
"malesage3": 0,
"malesage4": 0,
"malesage5": 0,
"malesage6": 0,
"malesage7": 0,
"femalesage1": 0,
"femalesage2": 0,
"femalesage3": 0,
"femalesage4": 0,
"femalesage5": 0,
"femalesage6": 0,
"femalesage7": 0
},
{
"name": "一层",
"parentAreaCode":
"0001-0002-0002",
"areaCode":
"0001-0002-0002-
000K",
"areaType": 400,
"relCode": "1F",
"children": null,
"visitors": 0,
"intoTrips": 0,
"malesage1": 0,
"malesage2": 0,
"malesage3": 0,
"malesage4": 0,
"malesage5": 0,
"malesage6": 0,
"malesage7": 0,
"femalesage1": 0,
"femalesage2": 0,
"femalesage3": 0,
"femalesage4": 0,
"femalesage5": 0,
"femalesage6": 0,
"femalesage7": 0
}
]
}
]
}
根据节点类型查询时段数据接口
· 接口描述
根据节点类型查询时段数据
· 接口地址
/snapdata/api/data/nodeType/hour · 请求参数
请求格
式
参数名 描述 类型
是否
必填
备注
header accessToken accessToken string 是
body
areaCode 商场编码 String 是
nodeType
查询节点类型：300:商场，400:楼层，
500:商铺,600:出入口,700:关注区
域,800:楼栋,900:区域
int 是
startDate 开始日期（最大查询范围 7 天） LocalDate 是
2023-
03-19
endDate 结束日期（最大查询范围 7 天） LocalDate 是
2023-
03-25 · 返回数据
code
integer
状态码
data
{
areaCode
String 节点编
码
relCode
String
关联 code
record
List
数据列表
[{
nodeCode String 节点编码
nodeType int 节点类型
relCode String 关联 code
visitors int 人数
intoTrips int 人次
males int 男性人数
females int 女性人数
date LocalDate 日期
hour int 小时
}]
}
message string
错误信息
 返回示例
{
"code": 0,
"message": "成功",
"data": {
"areaCode": "01DD-0001-0002",
"relCode": "商场",
"record": [
{
"nodeCode": "01DD-0001-0002-0002-0004",
"nodeType": 500,
"relCode": "sssssss",
"visitors": 2,
"intoTrips": 2,
"males": 1,
"females": 1,
"date": "2022-03-23",
"hour": 11
}
]
}
}
根据节点类型查询日数据接口
· 接口描述
根据节点类型查询日数据
· 接口地址
/snapdata/api/data/nodeType/day · 请求参数
请求格
式
参数名 描述 类型
是否
必填
备注
header accessToken accessToken string 是
body
areaCode 商场编码 String 是
nodeType
查询节点类型：300:商场，400:楼层，
500:商铺,600:出入口,700:关注区
域,800:楼栋,900:区域
int 是
startDate 开始日期（最大查询范围 92 天） LocalDate 是
2023-
03-19
endDate 结束日期（最大查询范围 92 天） LocalDate 是
2023-
03-25 · 返回数据
code
integer
状态码
data
{
areaCode
String 节点编
码
relCode
String
关联 code
record
List
数据列表
[{
nodeCode String 节点编码
nodeType int 节点类型
relCode String 关联 code
visitors int 人数
intoTrips int 人次
males int 男性人数
females int 女性人数
date LocalDate 日期
}]
}
message
string
错误信息
 返回示例
{
"code": 0,
"message": "成功",
"data": {
"areaCode": "01DD-0001-0002",
"relCode": "商场",
"record": [
{
"nodeCode": "01DD-0001-0002-0002-0004",
"nodeType": 500,
"relCode": "sssssss",
"visitors": 2,
"intoTrips": 2,
"males": 1,
"females": 1,
"date": "2022-03-23"
}
]
}
}
根据节点类型查询进店数据接口
· 接口描述
根据节点类型查询进店数据
· 接口地址
/snapdata/api/data/nodeType/intoShop
· 请求参数
请求格
式
参数名 描述 类型
是否必
填
备注
header accessToken accessToken string 是
body
areaCode 商场编码 String 是
nodeType
查询节点类型：300:商场，400:
楼层
int 是
startDate
开始日期（最大查询范围 7
天）
LocalDate 是
2023-03-
19
endDate
结束日期（最大查询范围 7
天）
LocalDate 是
2023-03-
25 · 返回数据
code
integer
状态码
data
{
areaCode
String 节点编
码
relCode
String
关联 code
record
List
数据列表
[{
nodeCode String 节点编码
nodeType int 节点类型
relCode String 关联 code
intoShopCount int 进店人数
totalCount int 总人数
intoShopRate float 进店率（百分比，2 位小数）
date LocalDate 日期
}]
}
message
string
错误信息
 返回示例
{
"code": 0,
"message": "成功",
"data": {
"areaCode": "01DD-0001-0002",
"relCode": "商场",
"record": [
{
"date": "2022-03-23",
"nodeCode": "01DD-0001-0002",
"relCode": "商场",
"nodeType": 300,
"intoShopCount": 2,
"totalCount": 4,
"intoShopRate": 50 //（即 50%）
}
]
}
}
商场爬楼量接口
· 接口描述
商场爬楼量查询接口
· 接口地址
/snapdata/api/floorVisitor/dist · 请求参数
请求格
式
参数名 描述 类型
是否必
填
备注
header accessToken accessToken string 是
body
areaCode 商场编码 String 是
startDate
开始日期（最大查询范围 7
天）
LocalDate 是
2023-03-
19
endDate
结束日期（最大查询范围 7
天）
LocalDate 是
2023-03-
25 · 返回数据
code
integer
状态码
data
{
areaCode
String 节点编
码
relCode
String
关联 code
record
List
数据列表
[{
visitorCount int 人数
floorCount int 爬楼量
date LocalDate 日期
}]
}
message
string
错误信息
 返回示例
{
"code": 0,
"message": "成功",
"data": {
"areaCode": "0001-0002-0002",
"relCode": null,
"record": [
{
"date": "2022-02-22",
"floorCount": 1,
"visitorCount": 18560
},
{
"date": "2022-02-22",
"floorCount": 2,
"visitorCount": 5797
},
{
"date": "2022-02-22",
"floorCount": 3,
"visitorCount": 1823
},
{
"date": "2022-02-22",
"floorCount": 4,
"visitorCount": 579
},
{
"date": "2022-02-22",
"floorCount": 5,
"visitorCount": 135
},
{
"date": "2022-02-22",
"floorCount": 6,
"visitorCount": 11
}
]
}
}
商场逛店个数分布查询接口
· 接口描述
商场逛店个数分布查询接口
· 接口地址
/snapdata/api/shopVisitor/dist · 请求参数
请求格
式
参数名 描述 类型
是否必
填
备注
header accessToken accessToken string 是
body
areaCode 商场编码 String 是
startDate
开始日期（最大查询范围 92
天）
LocalDate 是
2023-03-
19
endDate
结束日期（最大查询范围 92
天）
LocalDate 是
2023-03-
25 · 返回数据
code
integer
状态码
data
{
areaCode
String 节点编
码
relCode
String
关联 code
record
List
数据列表
[{
visitorCount int 人数
ordinal int，0:1-3;1:4-6;2:7-9;3:10-12;4:13 以上 逛店分类标识
date LocalDate 日期
}]
}
message
string
错误信息
 返回示例
{
"code": 0,
"message": "成功",
"data": {
"areaCode": "0001-0002-0002",
"relCode": null,
"record": [
{
"date": "2022-02-22",
"ordinal": 0,
"visitorCount": 13235
},
{
"date": "2022-02-22",
"ordinal": 1,
"visitorCount": 122
},
{
"date": "2022-02-22",
"ordinal": 2,
"visitorCount": 1
},
{
"date": "2022-02-22",
"ordinal": 3,
"visitorCount": 1
},
{
"date": "2022-02-22",
"ordinal": 4,
"visitorCount": 0
}
]
}
}
商铺关联关系接口
· 接口描述
商铺关联关系接口
· 接口地址
/snapdata/api/shop/relation · 请求参数
请求格
式
参数名 描述 类型
是否必
填
备注
header accessToken accessToken string 是
body
areaCode 商场编码 String 是
startDate
开始日期（最大查询范围 7
天）
LocalDate 是
2023-03-
19
endDate
结束日期（最大查询范围 7
天）
LocalDate 是
2023-03-
25
· 返回数据
code
integer
状态码
data
{
areaCode
String 节点编
码
relCode
String
关联 code
record
List
数据列表
[{
source String 起点商铺名称
count int 起点商铺人数
target String 目标商铺名称
sameCount int 起点和目标商铺的相同人数
}]
}
message
string
错误信息
 返回示例
{
"code": 0,
"message": "成功",
"data": {
"areaCode": "0001-0002-0002",
"relCode": null,
"record": [
{
"source": "STARBUCKS",
"count": 278,
"target": "MUJI",
"sameCount": 23
},
{
"source": "STARBUCKS",
"count": 278,
"target": "珠宝街",
"sameCount": 19
},
{
"source": "STARBUCKS",
"count": 278,
"target": "永辉超市",
"sameCount": 19
}
]
}
}
商场多天平均游逛店数接口
· 接口描述
商场多天平均游逛店数接口
· 接口地址
/snapdata/api/shopVisitor/avg · 请求参数
请求格
式
参数名 描述 类型
是否必
填
备注
header accessToken accessToken string 是
body
areaCode 商场编码 String 是
startDate 开始日期（最大查询范围 10 LocalDate 是 2023-03-
天） 19
endDate
结束日期（最大查询范围 10
天）
LocalDate 是
2023-03-
25 · 返回数据
code
integer
状态码
data
{
areaCode
String 节点编
码
relCode
String
关联 code
record
List
数据列表
[{
date LocalDate 日期
avgCount float 平均游逛店数
}]
}
message
string
错误信息
 返回示例
{
"code": 0,
"message": "成功",
"data": {
"areaCode": "0001-0002-0002",
"relCode": "000035",
"record": [
{
"date": "2022-02-28",
"avgCount": 1.2
},
{
"date": "2022-02-27",
"avgCount": 1.1
},
{
"date": "2022-02-26",
"avgCount": 1.2
},
{
"date": "2022-02-25",
"avgCount": 1.2
},
{
"date": "2022-02-24",
"avgCount": 1.2
},
{
"date": "2022-02-23",
"avgCount": 1.2
},
{
"date": "2022-02-22",
"avgCount": 1.2
},
{
"date": "2022-02-21",
"avgCount": 1.2
}
]
}
}
商场多天实时滞留量接口
· 接口描述
商场多天实时滞留量接口
· 接口地址
/snapdata/api/mall/multi/stayTrip · 请求参数
请求格
式
参数名 描述 类型
是否必
填
备注
header accessToken accessToken string 是
body
areaCode 商场编码 String 是
startDate
开始日期（最大查询范围 10
天）
LocalDate 是
2023-03-
19
endDate
结束日期（最大查询范围 10
天）
LocalDate 是
2023-03-
25 · 返回数据
code
integer
状态码
data
{
areaCode
String 节点编
码
relCode
String
关联 code
record
List
数据列表
[{
date LocalDate 日期
stayTrips Integer 实时滞留量
}]
}
message
string
错误信息
 返回示例
{
"code": 0,
"message": "成功",
"data": {
"areaCode": "01DD-0001-0002",
"relCode": "商场",
"record": [
{
"stayTrips": 186,
"date": "2022-05-10"
}
]
}
}
客流来源和客流走向接口
· 接口描述
根据传入的顾客类型、性别、年龄和店铺，查询此店铺的符合条件的客流来源和客流走向
的每个店铺、楼层的人数。
· 接口地址
/snapdata/api/passengerFlow
/ckdata/api/passengerFlow(旧接口) · 请求参数
请求
格式
参数名 描述 类型
是
否
必
填
备注
header accessToken accessToken string 是 参数放在请求头 header 中
body
userType
用户类型(多
选)
List<Integer> 否
顾客类型 0:普通客户
为空表示查询普通客户
sex 性别(多选) List<Integer> 否
性别 0：男 1：女
为空表示查询所有
age
年龄段(多
选)
List<Integer> 否
年龄集合 0: 0-12 岁、1: 13-
20 岁、2: 21-25 岁、3: 26-30
岁、4: 31-35 岁、5: 36-45
岁、6: 46 岁以上
为空表示查询所有
date 查询日期 string 是 格式：yyyy-MM-dd
nodeCode 查询编码 string 是
区域、楼栋、楼层、商铺节点
编码
· 返回数据
code
integer
状态码
data
{
mallCode
string
商场编码
mallName
string
商场名称
flowSource
来源客流
[{
nodeCode
string
节点编码
relCode
String
关联 code
nodeName
string
节点名称
nodeType
integer
节点类型 400:楼层,500:商铺,600:出入口,700:关
注区域,800:楼栋,900:区域
number
integer
客流人数
}]
flowTrend
去向客流
[{
nodeCode
string
节点编码
relCode
String
关联 code
nodeName
string
节点名称
nodeType
integer
节点类型 400:楼层,500:商铺,600:出入口,700:关
注区域,800:楼栋,900:区域
number
integer
客流人数
}]
}
message
string
错误信息
 返回示例:
{
"message": "成功",
"code": 0,
"data": {
"mallCode": "0002-0001-0001",
"mallName": "测试商场 0807",
"flowSource": [
{
"nodeCode": "0002-0001-0001-0003-0002",
"relCode":"adb",
"nodeName": "商铺 006",
"nodeType": 500,
"number": 4
},
{
"nodeCode": "0002-0001-0001-0002-0003",
"relCode":"adb",
"nodeName": "商铺 007",
"nodeType": 500,
"number": 2
}
],
"flowTrend": [
{
"nodeCode": "0002-0001-0001-0003-0002",
"relCode":"adb",
"nodeName": "商铺 006",
"nodeType": 500,
"number": 4
},
{
"nodeCode": "0002-0001-0001-0002-0003",
"relCode":"adb",
"nodeName": "商铺 007",
"nodeType": 500,
"number": 6
}
]
}}
销售数据录入接口
· 接口描述
传入销售数据
· 接口地址
/snapdata/api/sales/input · 请求参数
请求
格式
参数名 描述 类型 是否必填 备注
header accessToken accessToken string 是 参数放在请求头 header 中
body
amount 金额 Double 是 小数点两位
areaCode 商场编码 string 是 导入到哪个商场
tradeCount 交易次数 Integer 是 交易次数
tradeDate 销售日期 String 是 格式：yyyy-MM-dd
tradeNo 销售单号 string 是 销售单号
tradeTime 销售时间 string 是 格式：HH:mm:ss · 返回数据
code
integer
状态码
data
{
}
message string
错误信息
 返回示例:
{
"message": "成功",
"code": 0,
"data": {
}}