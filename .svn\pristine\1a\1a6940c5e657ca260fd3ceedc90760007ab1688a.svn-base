﻿<template>
  <div>
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="entity" :rules="rules" v-bind="layout">
        <a-row :gutter="24">
          <a-divider orientation="left">离职人员信息</a-divider>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="离职员工" prop="F_UserId" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.EmpName" placeholder="" disabled style="width: 100%" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="离职员工编码" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.EmpCode" disabled placeholder="" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="职位" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.PostName" disabled placeholder="" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="职级" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.F_Rank" disabled placeholder="" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="所属组织" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.OrgInfo" disabled placeholder="" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="联系电话" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.MobilePhone" :disabled="isEdit" :maxLength="11" placeholder="请输入联系电话" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-divider orientation="left">离职事务信息</a-divider>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="原用工状态" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.F_OriginalEmplStatus" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="目标用工状态" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.F_MobilzOriEmplStatus" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="变动操作" prop="F_ChangesOperating" :labelCol="{ span: 8 }"
                :wrapperCol="{ span: 14 }">
                <SelectDiction ref="ChangesOperating" :Name="'离职变动操作'" :disabled="isEdit"
                  @selectedvalue="selectChangesOperating" :Value="entity.F_ChangesOperating" style="width: 100%;">
                </SelectDiction>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="变动类型" prop="F_ChangesType" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <SelectDiction ref="ChangesType" :Name="'离职变动类型'" :disabled="isEdit" @selectedvalue="selectChangesType"
                  :Value="entity.F_ChangesType" style="width: 100%;"></SelectDiction>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="离职原因" prop="F_DepartureReason" :labelCol="{ span: 8 }"
                :wrapperCol="{ span: 14 }">
                <SelectDiction ref="DepartureReason" :Name="'离职原因'" :disabled="isEdit"
                  @selectedvalue="selectDepartureReason" :Value="entity.F_DepartureReason" style="width: 100%;">
                </SelectDiction>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="预计离职生效日期" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-date-picker v-model="entity.F_DepartureDate" :disabled="isEdit" style="width: 100%" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="实际离职生效日期" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-date-picker v-model="entity.F_TrueDepartureDate" :disabled="isEdit" style="width: 100%" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="备注" :labelCol="{ span: 4 }" :wrapperCol="{ span: 19 }">
                <a-textarea v-model="entity.F_Remark" placeholder="请输入" :maxLength="1000" :rows="4" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="附件" :labelCol="{ span: 3 }" :wrapperCol="{ span: 19 }">
                <uploadInfo ref="fj" :fileEntity="fileEntity" @handleUpload="handleUpload"></uploadInfo>
              </a-form-item>
            </a-col>
          </a-row>
        </a-row>
      </a-form-model>
    </a-spin>
    <div v-if="entity.F_WFState == 2" style="text-align:center;margin-bottom: 50px;">
      <a-button key="archiveWorkflow" :loading="loading" @click="archiveWorkflow">
        强制归档
      </a-button>
      &nbsp;&nbsp;
      <a-button key="createFlow" :loading="loading" @click="createFlow">
        提交流程
      </a-button>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import SelectDiction from '@/components/SelectDictionaries/DictionariesList'
import uploadInfo from '@/components/TemplateUpload/upload'
export default {
  components: {
    SelectDiction,
    uploadInfo
  },
  created () {
    this.id = this.$route.query.id
    console.log(this.id)
    this.openForm(this.id)
  },
  data () {
    return {
      id: '',
      layout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 18 }
      },
      visible: false,
      loading: false,
      isEdit: false,
      entity: {},
      rules: {
        F_UserId: [{ required: true, message: '请选择离职员工', trigger: 'blur' }],
        F_ChangesOperating: [{ required: true, message: '请选择变动操作', trigger: 'change' }],
        F_ChangesType: [{ required: true, message: '请选择变动类型', trigger: 'change' }],
        F_DepartureReason: [{ required: true, message: '请选择离职原因', trigger: 'change' }]
      },
      title: '',
      fileEntity: {}
    }
  },
  methods: {
    handleUpload (res) {
      if (this.fileEntity.Files === null) {
        this.fileEntity.Files = res.Files
      } else {
        res.Files.forEach(e => {
          this.fileEntity.Files.push(e)
        })
      }
    },
    //离职变动操作
    selectChangesOperating (value) {
      this.entity.F_ChangesOperating = value
    },
    //离职变动类型
    selectChangesType (value) {
      this.entity.F_ChangesType = value
    },
    //离职原因
    selectDepartureReason (value) {
      this.entity.F_DepartureReason = value
    },
    SeletedEmp (user) {
      this.entity.EmpName = user[0].NameUser
      this.entity.EmpCode = user[0].EmployeesCode
      this.entity.MobilePhone = user[0].MobilePhone
      this.entity.PostName = user[0].PostName
      this.entity.OrgInfo = user[0].CompanyName
      this.entity.F_UserId = user[0].F_Id
    },
    init () {
      this.visible = true
      this.entity = {
        F_UserId: '',
        EmpName: '',
        EmpCode: '',
        PostName: '',
        F_Rank: '',
        OrgInfo: '',
        MobilePhone: '',
        F_OriginalEmplStatus: '',
        F_MobilzOriEmplStatus: '',
        F_ChangesOperating: '',
        F_ChangesType: '',
        F_DepartureReason: '',
        F_DepartureDate: null,
        F_TrueDepartureDate: null
      }
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    moment,
    getCurrentData () {
      return new Date().toLocaleDateString()
    },
    openForm (id, isEdit) {
      this.isEdit = isEdit
      this.init()

      if (id) {
        this.loading = true
        this.$http.post('/HR_EmployeeInfoManage/HR_Departure/GetDepartureInfo', { id: id }).then(resJson => {
          this.loading = false
          if (resJson.Data.F_DepartureDate) {
            resJson.Data.F_DepartureDate = moment(resJson.Data.F_DepartureDate)
          }

          this.entity = resJson.Datathis.$http
            .post('/Base_Manage/Base_FileInfo/LoadFiles', { FileFolderId: this.entity.F_FileId })
            .then(data => {
              this.fileEntity = data
            })
        })
      }
    },
    //保存并创建流程
    createFlow () {
      this.$http
        .post('/Base_Manage/Base_FileInfo/UploadFilesSave', { FileInfo: JSON.stringify(this.fileEntity) })
        .then(data => {
          this.entity.F_FileId = data.F_Id
          this.$refs['form'].validate(valid => {
            if (!valid) {
              return
            }
            this.loading = true
            this.$http.post('/HR_EmployeeInfoManage/HR_Departure/ActWorkflow', this.entity).then(resJson => {
              this.loading = false

              if (resJson.Success) {
                this.entity.F_WFState = 1
                this.$message.success('提交流程成功!')

                this.parentObj.getDataList()
              } else {
                this.$message.error(resJson.Msg)
              }
            })
          })
        })
    },
    //流程强制归档
    archiveWorkflow () {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        const self = this
        this.$confirm({
          title: '确认强制归档吗?',
          onOk () {
            self.loading = true
            self.$http.post('/HR_EmployeeInfoManage/HR_Departure/ArchiveWorkflow', self.entity).then(resJson => {
              self.loading = false

              if (resJson.Success) {
                self.entity.F_WFState = 4
                self.$message.success('归档成功!')

                self.$router.push({ path: '/Home/Introduce' })
                //closePage()
              } else {
                self.$message.error(resJson.Msg)
              }
            })
          }
        })
      })
    }
  }
}
</script>
