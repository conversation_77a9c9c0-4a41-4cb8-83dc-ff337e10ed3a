﻿using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage.Extensions;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using System.Transactions;

namespace Coldairarrow.Api.Controllers.HR_EmployeeInfoManage
{
    [Route("/HR_EmployeeInfoManage/[controller]/[action]")]
    public class HR_PositiveController : BaseApiController
    {
        #region DI

        public HR_PositiveController(IHR_PositiveBusiness hR_PositiveBus, IHR_InductionBusiness hR_InductionBus, IHR_FormalEmployeesBusiness hR_FormalEmployeesBus, IConfiguration configuration)
        {
            _hR_PositiveBus = hR_PositiveBus;
            this.hR_FormalEmployeesBus = hR_FormalEmployeesBus;
            _configuration = configuration;
            this._hR_InductionBus = hR_InductionBus;
        }
        readonly IConfiguration _configuration;
        IHR_PositiveBusiness _hR_PositiveBus { get; }
        IHR_FormalEmployeesBusiness hR_FormalEmployeesBus { get; }
        IHR_InductionBusiness _hR_InductionBus { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_PositiveDTO>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_PositiveBus.GetDataListAsync(input, "");
        }
        /// <summary>
        /// 获取我要转正列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PageResult<HR_PositiveDTO>> GetMyDataList(PageInput<ConditionDTO> input)
        {
            var op = this.GetOperator();
            var condition = input.Search;
            return await _hR_PositiveBus.GetDataListAsync(input, op.UserId, true);
        }
        [HttpPost]
        public async Task<HR_PositiveThenDTO> GetLoginEmp(IdInputDTO input)
        {
            //var op = this.GetOperator();
            //if(op.)
            //_hR_PositiveBus.GetTheData(op.)
            //return _hR_FormalEmployeesBus.GetDataByUserId(op?.UserId);
            //if(input.get)
            return await _hR_PositiveBus.GetFormDataAsync(input.id);
        }


        [HttpPost]
        public async Task<HR_PositiveThenDTO> GetTheData(IdInputDTO input)
        {
            //if(input.get)
            return await _hR_PositiveBus.GetFormDataAsync(input.id);
        }
        /// <summary>
        /// 获取流程的数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<HR_PositiveThenDTO> GetFlowTheData(IdInputDTO input)
        {
            //if(input.get)
            return await _hR_PositiveBus.GetFlowFormDataAsync(input.id);
        }
        //[HttpPost]
        //public AjaxResult GetMyTheData(IdInputDTO input)
        //{
        //    return  _hR_PositiveBus.GetMyFormData(input.id);
        //}
        #endregion

        #region 提交
        [HttpPost]
        //[Transactional]
        public AjaxResult SaveData(HR_PositiveThenDTO data)
        {
            _hR_PositiveBus.SaveData(data);
            return Success();
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_PositiveBus.DeleteDataAsync(ids);
        }
        /// <summary>
        /// 业务删除转正
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task BusDeleteData(List<string> ids)
        {
            await _hR_PositiveBus.BusDeleteDataAsync(ids);
        }
        #endregion

        #region 流程
        /// <summary>
        /// 保存并创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        //[Transactional]
        public AjaxResult SaveAndCreateFlow(HR_PositiveThenDTO data)
        {
            try
            {
                //using (TransactionScope scope = new TransactionScope())
                //{
                //if (data.F_Id.IsNullOrEmpty())
                //{
                //    //保存正式员工数据库
                //    if (!string.IsNullOrWhiteSpace(data.F_UserId))
                //    {
                //        //更新正式员工数据
                //        var formalEmployees = hR_FormalEmployeesBus.GetTheData(data.F_UserId);
                //        if (formalEmployees != null)
                //        {
                //            formalEmployees.EmployRelStatus = data.EmployRelStatus;
                //            formalEmployees.MobilePhone = data.MobilePhone;
                //            formalEmployees.F_PositionId = data.F_PositionId;
                //            formalEmployees.F_DepartmentId = data.F_DepartmentId;
                //            UpdateEntity(formalEmployees);
                //            hR_FormalEmployeesBus.UpdateData(formalEmployees);
                //        }
                //    }
                //    //新增员工转正表
                //    HR_Positive hR_Positive = new HR_Positive()
                //    {
                //        F_UserId = data.F_UserId,
                //        F_PositiveDate = data.F_PositiveDate,
                //        F_ProbationPeriod = data.F_ProbationPeriod,
                //        F_PositivePosition = data.F_PositivePosition,
                //        F_PositiveOrg = data.F_PositiveOrg,
                //        F_PositiveRand = data.F_PositiveRand,
                //        F_InspectionResults = data.F_InspectionResults,
                //        F_WorkConclusion = data.F_WorkConclusion,
                //        F_ChangesOperating = data.F_ChangesOperating,
                //        F_ChangesType = data.F_ChangesType
                //    };
                //    InitEntity(hR_Positive);
                //    _hR_PositiveBus.AddData(hR_Positive);
                //}
                //else
                //{
                //    if (!string.IsNullOrWhiteSpace(data.F_Id))
                //    {
                //        var positive = _hR_PositiveBus.GetTheData(data.F_Id);
                //        if (positive != null)
                //        {
                //            positive.F_UserId = data.F_UserId;
                //            positive.F_PositiveDate = data.F_PositiveDate;
                //            positive.F_ProbationPeriod = data.F_ProbationPeriod;
                //            positive.F_PositivePosition = data.F_PositivePosition;
                //            positive.F_PositiveOrg = data.F_PositiveOrg;
                //            positive.F_PositiveRand = data.F_PositiveRand;
                //            positive.F_InspectionResults = data.F_InspectionResults;
                //            positive.F_WorkConclusion = data.F_WorkConclusion;
                //            positive.F_ChangesOperating = data.F_ChangesOperating;
                //            positive.F_ChangesType = data.F_ChangesType;
                //            _hR_PositiveBus.UpdateData(positive);
                //            if (!string.IsNullOrWhiteSpace(positive.F_UserId))
                //            {
                //                var formalEmployees = hR_FormalEmployeesBus.GetTheData(positive.F_UserId);
                //                formalEmployees.EmployRelStatus = data.EmployRelStatus;
                //                formalEmployees.MobilePhone = data.MobilePhone;
                //                formalEmployees.F_PositionId = data.F_PositionId;
                //                formalEmployees.F_DepartmentId = data.F_DepartmentId;
                //                hR_FormalEmployeesBus.UpdateData(formalEmployees);
                //            }
                //        }
                //    }
                //    //_hR_PositiveBus.UpdateData(data);
                //}
                ////更新入职数据
                //if (!data.F_UserId.IsNullOrEmpty())
                //{
                //    var induction = _hR_InductionBus.GetUserData(data.F_UserId);
                //    if (induction != null)
                //    {
                //        induction.F_InductionDate = data.F_InductionDate;
                //        induction.F_InductionEmployRelStatus = data.F_InductionEmployRelStatus;
                //        induction.F_InductionOrg = data.F_InductionOrg;
                //        induction.F_ProbationPeriod = data.F_ProbationPeriod;
                //        _hR_InductionBus.UpdateData(induction);
                //    }
                //}
                //    scope.Complete();
                //}
                var ret = _hR_PositiveBus.CreateFlow(data, _configuration["OAUrl"] + "rest/archives/");

                if (ret)
                {
                    return Success();
                }
                else
                {
                    return Error("创建流程失败");
                }

            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 流程编辑
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        //[Transactional]
        public AjaxResult Edit(HR_PositiveThenDTO data)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(data.F_Id))
                {
                    var positive = _hR_PositiveBus.GetTheData(data.F_Id);
                    if (positive != null)
                    {
                        positive.F_UserId = data.F_UserId;
                        positive.F_PositiveDate = data.F_PositiveDate;
                        positive.F_ProbationPeriod = data.F_ProbationPeriod;
                        positive.F_PositivePosition = data.F_PositivePosition;
                        positive.F_PositiveOrg = data.F_PositiveOrg;
                        positive.F_PositiveRand = data.F_PositiveRand;
                        positive.F_InspectionResults = data.F_InspectionResults;
                        positive.F_WorkConclusion = data.F_WorkConclusion;
                        positive.F_ChangesOperating = data.F_ChangesOperating;
                        positive.F_ChangesType = data.F_ChangesType;
                        _hR_PositiveBus.UpdateData(positive);
                        if (!string.IsNullOrWhiteSpace(positive.F_UserId))
                        {
                            var formalEmployees = hR_FormalEmployeesBus.GetTheData(positive.F_UserId);
                            formalEmployees.EmployRelStatus = data.EmployRelStatus;
                            formalEmployees.MobilePhone = data.MobilePhone;
                            formalEmployees.F_PositionId = data.F_PositionId;
                            formalEmployees.F_DepartmentId = data.F_DepartmentId;
                            formalEmployees.EncryptForm();
                            hR_FormalEmployeesBus.UpdateData(formalEmployees);
                        }
                    }
                }
                return Success();
            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult CreateFlow(HR_PositiveThenDTO data)
        {
            var ret = _hR_PositiveBus.CreateFlow(data, _configuration["OAUrl"] + "rest/archives/");
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("创建流程失败");
            }
        }

        /// <summary>
        /// 流程回调方法
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult FlowCallBack(FlowInputDTO input)
        {
            _hR_PositiveBus.FlowCallBack(input);

            return Success();
        }

        /// <summary>
        ///提交退回流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ActWorkflow(HR_PositiveThenDTO data)
        {
            try
            {
                //using (TransactionScope scope = new TransactionScope())
                //{
                //if (data.F_Id.IsNullOrEmpty())
                //{
                //    //保存正式员工数据库
                //    if (!string.IsNullOrWhiteSpace(data.F_UserId))
                //    {
                //        //更新正式员工数据
                //        var formalEmployees = hR_FormalEmployeesBus.GetTheData(data.F_UserId);
                //        if (formalEmployees != null)
                //        {
                //            formalEmployees.EmployRelStatus = data.EmployRelStatus;
                //            formalEmployees.MobilePhone = data.MobilePhone;
                //            formalEmployees.F_PositionId = data.F_PositionId;
                //            formalEmployees.F_DepartmentId = data.F_DepartmentId;
                //            UpdateEntity(formalEmployees);
                //            hR_FormalEmployeesBus.UpdateData(formalEmployees);
                //        }
                //    }
                //    //新增员工转正表
                //    HR_Positive hR_Positive = new HR_Positive()
                //    {
                //        F_UserId = data.F_UserId,
                //        F_PositiveDate = data.F_PositiveDate,
                //        F_ProbationPeriod = data.F_ProbationPeriod,
                //        F_PositivePosition = data.F_PositivePosition,
                //        F_PositiveOrg = data.F_PositiveOrg,
                //        F_PositiveRand = data.F_PositiveRand,
                //        F_InspectionResults = data.F_InspectionResults,
                //        F_WorkConclusion = data.F_WorkConclusion,
                //        F_ChangesOperating = data.F_ChangesOperating,
                //        F_ChangesType = data.F_ChangesType
                //    };
                //    InitEntity(hR_Positive);
                //    _hR_PositiveBus.AddData(hR_Positive);
                //}
                //else
                //{
                //    if (!string.IsNullOrWhiteSpace(data.F_Id))
                //    {
                //        var positive = _hR_PositiveBus.GetTheData(data.F_Id);
                //        if (positive != null)
                //        {
                //            positive.F_UserId = data.F_UserId;
                //            positive.F_PositiveDate = data.F_PositiveDate;
                //            positive.F_ProbationPeriod = data.F_ProbationPeriod;
                //            positive.F_PositivePosition = data.F_PositivePosition;
                //            positive.F_PositiveOrg = data.F_PositiveOrg;
                //            positive.F_PositiveRand = data.F_PositiveRand;
                //            positive.F_InspectionResults = data.F_InspectionResults;
                //            positive.F_WorkConclusion = data.F_WorkConclusion;
                //            positive.F_ChangesOperating = data.F_ChangesOperating;
                //            positive.F_ChangesType = data.F_ChangesType;
                //            _hR_PositiveBus.UpdateData(positive);
                //            if (!string.IsNullOrWhiteSpace(positive.F_UserId))
                //            {
                //                var formalEmployees = hR_FormalEmployeesBus.GetTheData(positive.F_UserId);
                //                formalEmployees.EmployRelStatus = data.EmployRelStatus;
                //                formalEmployees.MobilePhone = data.MobilePhone;
                //                formalEmployees.F_PositionId = data.F_PositionId;
                //                formalEmployees.F_DepartmentId = data.F_DepartmentId;
                //                hR_FormalEmployeesBus.UpdateData(formalEmployees);
                //            }
                //        }
                //    }
                //    //_hR_PositiveBus.UpdateData(data);
                //}
                ////更新入职数据
                //if (!data.F_UserId.IsNullOrEmpty())
                //{
                //    var induction = _hR_InductionBus.GetUserData(data.F_UserId);
                //    if (induction != null)
                //    {
                //        induction.F_InductionDate = data.F_InductionDate;
                //        induction.F_InductionEmployRelStatus = data.F_InductionEmployRelStatus;
                //        induction.F_InductionOrg = data.F_InductionOrg;
                //        induction.F_ProbationPeriod = data.F_ProbationPeriod;
                //        _hR_InductionBus.UpdateData(induction);
                //    }
                //}
                //    scope.Complete();
                //}
                var ret = _hR_PositiveBus.ActWorkflow(data, _configuration["OAUrl"] + "rest/archives/");

                if (ret)
                {
                    return Success();
                }
                else
                {
                    return Error("创建流程失败");
                }

            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ArchiveWorkflow(HR_PositiveThenDTO data)
        {
            var ret = _hR_PositiveBus.ArchiveWorkflow(data, _configuration["OAUrl"] + "rest/archives/");
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("强制归档失败");
            }
        }

        /// <summary>
        /// 是否延期转正
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult IsPutPositive(IsYqPositive data)
        {
           _hR_PositiveBus.IsPutPositive(data);
            return Success("提交成功");
        }
        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_PositiveBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "转正单.xlsx";
                excelconfig.Title = "转正单";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "employeescode", ExcelColumn = "员工编码", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "nameuser", ExcelColumn = "员工姓名", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_departmentname", ExcelColumn = "所属部门", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_inductionorg", ExcelColumn = "所属组织", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_positionname", ExcelColumn = "任职职位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "employrelstatus", ExcelColumn = "用工关系状态", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "idcardnumber", ExcelColumn = "身份证号码", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "sexname", ExcelColumn = "性别", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_inductiondate", ExcelColumn = "入职日期", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "mobilephone", ExcelColumn = "手机号码", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
    }
}