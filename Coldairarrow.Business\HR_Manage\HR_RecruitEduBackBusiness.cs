﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_RecruitEduBackBusiness : BaseBusiness<HR_RecruitEduBack>, IHR_RecruitEduBackBusiness, ITransientDependency
    {
        public HR_RecruitEduBackBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_RecruitEduBack>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RecruitEduBack>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RecruitEduBack, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_RecruitEduBack> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_RecruitEduBack data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_RecruitEduBack data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RecruitEduBack>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RecruitEduBack, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_RecruitEduBack, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        public int AddData(HR_RecruitEduBack data)
        {
            return Insert(data);
        }

        public int UpdateData(HR_RecruitEduBack data)
        {
            return Update(data);
        }

        public int DeleteData(HR_RecruitEduBack data)
        {
            return Delete(data);
        }
        public int UpdateListData(List<HR_RecruitEduBack> data)
        {
            return Update(data);
        }

        public void AddListData(List<HR_RecruitEduBack> data)
        {
            BulkInsert(data);
        }
        #endregion

        #region 私有成员

        #endregion
    }
}