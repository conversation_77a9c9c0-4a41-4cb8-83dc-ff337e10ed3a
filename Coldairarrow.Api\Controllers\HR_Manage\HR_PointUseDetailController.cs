﻿using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Microsoft.Extensions.Caching.Distributed;
using Coldairarrow.Util.Excel.Model;
using sun.misc;

namespace Coldairarrow.Api.Controllers.HR_Manage
{
    [Route("/HR_Manage/[controller]/[action]")]
    public class HR_PointUseDetailController : BaseApiController
    {
        #region DI

        public HR_PointUseDetailController(IHR_PointUseDetailBusiness hR_PointUseDetailBus, IDistributedCache cache)
        {
            _hR_PointUseDetailBus = hR_PointUseDetailBus;
            _cache = cache;
        }
        private readonly IDistributedCache _cache;
        IHR_PointUseDetailBusiness _hR_PointUseDetailBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_PointUseDetail>> GetDataList(PageInput<ProductConditinDto> input)
        {
            return await _hR_PointUseDetailBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_PointUseDetail> GetTheData(IdInputDTO input)
        {
            return await _hR_PointUseDetailBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task AddUserPonitData(HR_PointUseDetail data)
        {
            await _hR_PointUseDetailBus.AddUserPonitData(data);
        }


        [HttpPost]
        public async Task SaveData(HR_PointUseDetail data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_PointUseDetailBus.AddDataAsync(data);
            }
            else
            {
                await _hR_PointUseDetailBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_PointUseDetailBus.DeleteDataAsync(ids);
        }


        /// <summary>
        /// 获取余额明细
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetPointList()
        {
            try
            {
                string id = HttpContext.Request.Form["hrId"].ToString();
                var point = _hR_PointUseDetailBus.GetThePoint(id);
                return Success(point);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public async Task<FileContentResult> ExcelDownload(PageInput<ProductConditinDto> input)
        {
            try
            {
                input.PageRows = 10000;
                var pageResult = await _hR_PointUseDetailBus.GetDataListAsync(input);
                //取出数据源
                DataTable exportTable = pageResult.Data.ToDataTable();
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                //excelconfig.Title = "人员异动详情";
                //excelconfig.TitleFont = "微软雅黑";
                //excelconfig.TitlePoint = 20;
                excelconfig.FileName = "员工福豆使用明细.xls";
                excelconfig.IsAllSizeColumn = true;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>
                {
                    new ColumnModel() { Column = "f_createdate", ExcelColumn = "创建时间", Sort = 1 },
                    new ColumnModel() { Column = "f_username", ExcelColumn = "员工姓名", Sort = 2 },
                    new ColumnModel() { Column = "f_ordertypename", ExcelColumn = "消费类型", Sort = 3 },
                    new ColumnModel() { Column = "f_usenumber", ExcelColumn = "消费额度", Sort = 5 },
                    new ColumnModel() { Column = "f_resnumer", ExcelColumn = "消费前额度", Sort = 4 },
                    new ColumnModel() { Column = "f_restypename", ExcelColumn = "消费状态", Sort = 6 },
                    new ColumnModel() { Column = "f_backnumer", ExcelColumn = "消费后额度", Sort = 7 },
                    new ColumnModel() { Column = "f_describe", ExcelColumn = "订单描述", Sort = 8 },
                };

                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<AjaxResult> UploadFileByForm()
        {
            var Month = Request.Form["Month"];
            if (string.IsNullOrEmpty(Month))
            {
                throw new BusException("请选择月份");
            }
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return Error("上传文件不能为空");

            if (Request.Form.Files.Count > 1)
                return Error("只能上传一个文件");

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            var res = await _hR_PointUseDetailBus.ImportSaveData(physicPath, this.GetOperator(), Month);
            return res;
        }

        /// <summary>
        /// 模板下载
        /// </summary>
        [HttpPost]
        public FileContentResult DownloadTemplate()
        {
            string filePath = Directory.GetCurrentDirectory() + "/BusinessTemplate/员工积分额度模板.xls";
            if (FileHelper.Exists(filePath))
            {
                var bys = System.IO.File.ReadAllBytes(filePath);//excel表转换成流
                return File(bys, "application/vnd.ms-excel");
            }
            else
            {
                throw new BusException("找不到模板");
            }

        }
        #endregion
    }
}