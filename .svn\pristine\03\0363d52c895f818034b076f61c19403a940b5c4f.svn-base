@charset "utf-8";
@media ( min-width : 768px) {
  .container {
      max-width: 95%;
  }
}
@media ( min-width : 992px) {
  .container {
      max-width: 90%;
  }
  .media-right h4 {
    font-size: 18px;
  }
}
@media ( min-width : 1200px) {
  .container {
      max-width: 90%;
  }
}
.list-group-item {
  font-weight: 600;
  color: #282828;
  font-size: 14px;
  min-height: 58px;
  padding: 18px 20px;
}
.media-title {
  margin-top: 15px;
  margin-bottom: 5px; 
  font-size: 18px;
  font-family: Lantinghei SC;
  font-weight: 600;
  color: #282828;
}
.media-box {
  padding: 15px 0;
  margin: 0 15px;
  border-bottom: 1px solid #EDEDED;
}
.left-content {
  padding: 15px 0;
  margin-bottom: 20px;
  background: #fff;
  box-shadow: 3px 3px 6px #ededed;
}
.right-content {
  padding: 15px 0;
  background: #fff;
}
.md-left {
  padding: 0;
}
.mt {
  margin-top: 8px;
  margin-bottom: 5px;
}
.media-cont {
  color: #8A8A8A;
}
.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: #BABABA;
  font-size: 12px;
  width: 80%;
}
.mr {
  margin-right: 10%;
}
.flex-scan {
  display: flex;
  align-items: center;
  justify-content:flex-start;
}
.flex-scan img{
  margin-right: 5px;
}
.recomm-title {
  font-size: 24px;
  font-weight: 800;
  color: #282828;
  margin-bottom: 15px;
  padding-left: 20px;
}
.page-select {
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  float: right;
  background: #b59029fc;
  color: #fff;
  margin-right: 10px;
}
.page-noselect {
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  float: right;
  background: #fff;
  color: #333;
  margin-right: 10px;
}
.page-noselect:hover {
  background: #b59029fc;
  cursor: pointer;
}

.default-rec {
  margin-bottom: 20px;
  padding: 17px 17px 17px 2px;
  background: #fff;
  box-shadow: 3px 3px 6px #ededed;
}
.default-det img {
  width: 100%;
}
.default-det div {
  padding-left: 5px;
  margin-top: 10px;
  font-size: 16px;
  font-weight: 600;
  color: #282828;
}

