<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Diagnostics.HealthChecks</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Diagnostics.HealthChecks.DelegateHealthCheck">
            <summary>
            A simple implementation of <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck"/> which uses a provided delegate to
            implement the check.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Diagnostics.HealthChecks.DelegateHealthCheck.#ctor(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult}})">
            <summary>
            Create an instance of <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.DelegateHealthCheck"/> from the specified delegate.
            </summary>
            <param name="check">A delegate which provides the code to execute when the health check is run.</param>
        </member>
        <member name="M:Microsoft.Extensions.Diagnostics.HealthChecks.DelegateHealthCheck.CheckHealthAsync(Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext,System.Threading.CancellationToken)">
            <summary>
            Runs the health check, returning the status of the component being checked.
            </summary>
            <param name="context">A context object associated with the current execution.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> that can be used to cancel the health check.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that completes when the health check has finished, yielding the status of the component being checked.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckLogScope.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckLogScope"/> with the provided name.
            </summary>
            <param name="healthCheckName">The name of the health check being executed.</param>
        </member>
        <member name="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckPublisherOptions">
            <summary>
            Options for the default service that executes <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheckPublisher"/> instances.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckPublisherOptions.Delay">
            <summary>
            Gets or sets the initial delay applied after the application starts before executing 
            <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheckPublisher"/> instances. The delay is applied once at startup, and does
            not apply to subsequent iterations. The default value is 5 seconds.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckPublisherOptions.Period">
            <summary>
            Gets or sets the period of <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheckPublisher"/> execution. The default value is
            30 seconds.
            </summary>
            <remarks>
            The <see cref="P:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckPublisherOptions.Period"/> cannot be set to a value lower than 1 second.
            </remarks>
        </member>
        <member name="P:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckPublisherOptions.Predicate">
            <summary>
            Gets or sets a predicate that is used to filter the set of health checks executed.
            </summary>
            <remarks>
            If <see cref="P:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckPublisherOptions.Predicate"/> is <c>null</c>, the health check publisher service will run all
            registered health checks - this is the default behavior. To run a subset of health checks,
            provide a function that filters the set of checks. The predicate will be evaluated each period.
            </remarks>
        </member>
        <member name="P:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckPublisherOptions.Timeout">
            <summary>
            Gets or sets the timeout for executing the health checks an all <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheckPublisher"/> 
            instances. Use <see cref="F:System.Threading.Timeout.InfiniteTimeSpan"/> to execute with no timeout.
            The default value is 30 seconds.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckService">
            <summary>
            A service which can be used to check the status of <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck"/> instances 
            registered in the application.
            </summary>
            <remarks>
            <para>
            The default implementation of <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckService"/> is registered in the dependency
            injection container as a singleton service by calling 
            <see cref="M:Microsoft.Extensions.DependencyInjection.HealthCheckServiceCollectionExtensions.AddHealthChecks(Microsoft.Extensions.DependencyInjection.IServiceCollection)"/>.
            </para>
            <para>
            The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/> returned by 
            <see cref="M:Microsoft.Extensions.DependencyInjection.HealthCheckServiceCollectionExtensions.AddHealthChecks(Microsoft.Extensions.DependencyInjection.IServiceCollection)"/>
            provides a convenience API for registering health checks.
            </para>
            <para>
            <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck"/> implementations can be registered through extension methods provided by
            <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckService.CheckHealthAsync(System.Threading.CancellationToken)">
            <summary>
            Runs all the health checks in the application and returns the aggregated status.
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> which can be used to cancel the health checks.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task`1"/> which will complete when all the health checks have been run,
            yielding a <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthReport"/> containing the results.
            </returns>
        </member>
        <member name="M:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckService.CheckHealthAsync(System.Func{Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckRegistration,System.Boolean},System.Threading.CancellationToken)">
            <summary>
            Runs the provided health checks and returns the aggregated status
            </summary>
            <param name="predicate">
            A predicate that can be used to include health checks based on user-defined criteria.
            </param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> which can be used to cancel the health checks.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task`1"/> which will complete when all the health checks have been run,
            yielding a <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthReport"/> containing the results.
            </returns>
        </member>
        <member name="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckServiceOptions">
            <summary>
            Options for the default implementation of <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckService"/>
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckServiceOptions.Registrations">
            <summary>
            Gets the health check registrations.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderAddCheckExtensions">
            <summary>
            Provides basic extension methods for registering <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck"/> instances in an <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderAddCheckExtensions.AddCheck(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck,System.Nullable{Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus},System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds a new health check with the specified name and implementation.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="instance">An <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck"/> instance.</param>
            <param name="failureStatus">
            The <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus"/> that should be reported when the health check reports a failure. If the provided value
            is <c>null</c>, then <see cref="F:Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Unhealthy"/> will be reported.
            </param>
            <param name="tags">A list of tags that can be used to filter health checks.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderAddCheckExtensions.AddCheck(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck,System.Nullable{Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus},System.Collections.Generic.IEnumerable{System.String},System.Nullable{System.TimeSpan})">
            <summary>
            Adds a new health check with the specified name and implementation.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="instance">An <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck"/> instance.</param>
            <param name="failureStatus">
            The <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus"/> that should be reported when the health check reports a failure. If the provided value
            is <c>null</c>, then <see cref="F:Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Unhealthy"/> will be reported.
            </param>
            <param name="tags">A list of tags that can be used to filter health checks.</param>
            <param name="timeout">An optional <see cref="T:System.TimeSpan"/> representing the timeout of the check.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderAddCheckExtensions.AddCheck``1(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.Nullable{Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus},System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds a new health check with the specified name and implementation.
            </summary>
            <typeparam name="T">The health check implementation type.</typeparam>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="failureStatus">
            The <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus"/> that should be reported when the health check reports a failure. If the provided value
            is <c>null</c>, then <see cref="F:Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Unhealthy"/> will be reported.
            </param>
            <param name="tags">A list of tags that can be used to filter health checks.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
            <remarks>
            This method will use <see cref="M:Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetServiceOrCreateInstance``1(System.IServiceProvider)"/> to create the health check
            instance when needed. If a service of type <typeparamref name="T"/> is registered in the dependency injection container
            with any lifetime it will be used. Otherwise an instance of type <typeparamref name="T"/> will be constructed with
            access to services from the dependency injection container.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderAddCheckExtensions.AddCheck``1(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.Nullable{Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus},System.Collections.Generic.IEnumerable{System.String},System.Nullable{System.TimeSpan})">
            <summary>
            Adds a new health check with the specified name and implementation.
            </summary>
            <typeparam name="T">The health check implementation type.</typeparam>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="failureStatus">
            The <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus"/> that should be reported when the health check reports a failure. If the provided value
            is <c>null</c>, then <see cref="F:Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Unhealthy"/> will be reported.
            </param>
            <param name="tags">A list of tags that can be used to filter health checks.</param>
            <param name="timeout">An optional <see cref="T:System.TimeSpan"/> representing the timeout of the check.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
            <remarks>
            This method will use <see cref="M:Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetServiceOrCreateInstance``1(System.IServiceProvider)"/> to create the health check
            instance when needed. If a service of type <typeparamref name="T"/> is registered in the dependency injection container
            with any lifetime it will be used. Otherwise an instance of type <typeparamref name="T"/> will be constructed with
            access to services from the dependency injection container.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderAddCheckExtensions.AddTypeActivatedCheck``1(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.Object[])">
            <summary>
            Adds a new type activated health check with the specified name and implementation.
            </summary>
            <typeparam name="T">The health check implementation type.</typeparam>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="args">Additional arguments to provide to the constructor.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
            <remarks>
            This method will use <see cref="M:Microsoft.Extensions.DependencyInjection.ActivatorUtilities.CreateInstance``1(System.IServiceProvider,System.Object[])"/> to create the health check
            instance when needed. Additional arguments can be provided to the constructor via <paramref name="args"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderAddCheckExtensions.AddTypeActivatedCheck``1(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.Nullable{Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus},System.Object[])">
            <summary>
            Adds a new type activated health check with the specified name and implementation.
            </summary>
            <typeparam name="T">The health check implementation type.</typeparam>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="failureStatus">
            The <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus"/> that should be reported when the health check reports a failure. If the provided value
            is <c>null</c>, then <see cref="F:Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Unhealthy"/> will be reported.
            </param>
            <param name="args">Additional arguments to provide to the constructor.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
            <remarks>
            This method will use <see cref="M:Microsoft.Extensions.DependencyInjection.ActivatorUtilities.CreateInstance``1(System.IServiceProvider,System.Object[])"/> to create the health check
            instance when needed. Additional arguments can be provided to the constructor via <paramref name="args"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderAddCheckExtensions.AddTypeActivatedCheck``1(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.Nullable{Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus},System.Collections.Generic.IEnumerable{System.String},System.Object[])">
            <summary>
            Adds a new type activated health check with the specified name and implementation.
            </summary>
            <typeparam name="T">The health check implementation type.</typeparam>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="failureStatus">
            The <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus"/> that should be reported when the health check reports a failure. If the provided value
            is <c>null</c>, then <see cref="F:Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Unhealthy"/> will be reported.
            </param>
            <param name="tags">A list of tags that can be used to filter health checks.</param>
            <param name="args">Additional arguments to provide to the constructor.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
            <remarks>
            This method will use <see cref="M:Microsoft.Extensions.DependencyInjection.ActivatorUtilities.CreateInstance``1(System.IServiceProvider,System.Object[])"/> to create the health check
            instance when needed. Additional arguments can be provided to the constructor via <paramref name="args"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderAddCheckExtensions.AddTypeActivatedCheck``1(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.Nullable{Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus},System.Collections.Generic.IEnumerable{System.String},System.TimeSpan,System.Object[])">
            <summary>
            Adds a new type activated health check with the specified name and implementation.
            </summary>
            <typeparam name="T">The health check implementation type.</typeparam>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="failureStatus">
            The <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus"/> that should be reported when the health check reports a failure. If the provided value
            is <c>null</c>, then <see cref="F:Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Unhealthy"/> will be reported.
            </param>
            <param name="tags">A list of tags that can be used to filter health checks.</param>
            <param name="args">Additional arguments to provide to the constructor.</param>
            <param name="timeout">A <see cref="T:System.TimeSpan"/> representing the timeout of the check.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
            <remarks>
            This method will use <see cref="M:Microsoft.Extensions.DependencyInjection.ActivatorUtilities.CreateInstance``1(System.IServiceProvider,System.Object[])"/> to create the health check
            instance when needed. Additional arguments can be provided to the constructor via <paramref name="args"/>.
            </remarks>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderDelegateExtensions">
            <summary>
            Provides extension methods for registering delegates with the <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderDelegateExtensions.AddCheck(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.Func{Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult},System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds a new health check with the specified name and implementation.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="tags">A list of tags that can be used to filter health checks.</param>
            <param name="check">A delegate that provides the health check implementation.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderDelegateExtensions.AddCheck(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.Func{Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult},System.Collections.Generic.IEnumerable{System.String},System.Nullable{System.TimeSpan})">
            <summary>
            Adds a new health check with the specified name and implementation.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="tags">A list of tags that can be used to filter health checks.</param>
            <param name="check">A delegate that provides the health check implementation.</param>
            <param name="timeout">An optional <see cref="T:System.TimeSpan"/> representing the timeout of the check.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderDelegateExtensions.AddCheck(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.Func{System.Threading.CancellationToken,Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult},System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds a new health check with the specified name and implementation.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="tags">A list of tags that can be used to filter health checks.</param>
            <param name="check">A delegate that provides the health check implementation.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderDelegateExtensions.AddCheck(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.Func{System.Threading.CancellationToken,Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult},System.Collections.Generic.IEnumerable{System.String},System.Nullable{System.TimeSpan})">
            <summary>
            Adds a new health check with the specified name and implementation.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="tags">A list of tags that can be used to filter health checks.</param>
            <param name="check">A delegate that provides the health check implementation.</param>
            <param name="timeout">An optional <see cref="T:System.TimeSpan"/> representing the timeout of the check.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderDelegateExtensions.AddAsyncCheck(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.Func{System.Threading.Tasks.Task{Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult}},System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds a new health check with the specified name and implementation.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="tags">A list of tags that can be used to filter health checks.</param>
            <param name="check">A delegate that provides the health check implementation.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderDelegateExtensions.AddAsyncCheck(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.Func{System.Threading.Tasks.Task{Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult}},System.Collections.Generic.IEnumerable{System.String},System.Nullable{System.TimeSpan})">
            <summary>
            Adds a new health check with the specified name and implementation.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="tags">A list of tags that can be used to filter health checks.</param>
            <param name="check">A delegate that provides the health check implementation.</param>
            <param name="timeout">An optional <see cref="T:System.TimeSpan"/> representing the timeout of the check.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderDelegateExtensions.AddAsyncCheck(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult}},System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds a new health check with the specified name and implementation.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="tags">A list of tags that can be used to filter health checks.</param>
            <param name="check">A delegate that provides the health check implementation.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthChecksBuilderDelegateExtensions.AddAsyncCheck(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult}},System.Collections.Generic.IEnumerable{System.String},System.Nullable{System.TimeSpan})">
            <summary>
            Adds a new health check with the specified name and implementation.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</param>
            <param name="name">The name of the health check.</param>
            <param name="tags">A list of tags that can be used to filter health checks.</param>
            <param name="check">A delegate that provides the health check implementation.</param>
            <param name="timeout">An optional <see cref="T:System.TimeSpan"/> representing the timeout of the check.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/>.</returns>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.HealthCheckServiceCollectionExtensions">
            <summary>
            Provides extension methods for registering <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckService"/> in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HealthCheckServiceCollectionExtensions.AddHealthChecks(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds the <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckService"/> to the container, using the provided delegate to register
            health checks.
            </summary>
            <remarks>
            This operation is idempotent - multiple invocations will still only result in a single
            <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckService"/> instance in the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>. It can be invoked
            multiple times in order to get access to the <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/> in multiple places.
            </remarks>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckService"/> to.</param>
            <returns>An instance of <see cref="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder"/> from which health checks can be registered.</returns>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder">
            <summary>
            A builder used to register health checks.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder.Add(Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckRegistration)">
            <summary>
            Adds a <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckRegistration"/> for a health check.
            </summary>
            <param name="registration">The <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckRegistration"/>.</param>
        </member>
        <member name="P:Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder.Services">
            <summary>
            Gets the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> into which <see cref="T:Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck"/> instances should be registered.
            </summary>
        </member>
    </members>
</doc>
