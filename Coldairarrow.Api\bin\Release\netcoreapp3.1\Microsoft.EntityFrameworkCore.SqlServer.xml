<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.EntityFrameworkCore.SqlServer</name>
    </assembly>
    <members>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Design.Internal.SqlServerAnnotationCodeGenerator">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Design.Internal.SqlServerAnnotationCodeGenerator.#ctor(Microsoft.EntityFrameworkCore.Design.AnnotationCodeGeneratorDependencies)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Design.Internal.SqlServerAnnotationCodeGenerator.IsHandledByConvention(Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Infrastructure.IAnnotation)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Design.Internal.SqlServerAnnotationCodeGenerator.GenerateFluentApi(Microsoft.EntityFrameworkCore.Metadata.IKey,Microsoft.EntityFrameworkCore.Infrastructure.IAnnotation)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Design.Internal.SqlServerAnnotationCodeGenerator.GenerateFluentApi(Microsoft.EntityFrameworkCore.Metadata.IIndex,Microsoft.EntityFrameworkCore.Infrastructure.IAnnotation)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Design.Internal.SqlServerDesignTimeServices">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Design.Internal.SqlServerDesignTimeServices.ConfigureDesignTimeServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogDefaultDecimalTypeColumn">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogByteIdentityColumn">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogFoundDefaultSchema">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogFoundTypeAlias">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogFoundColumn">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogFoundForeignKey">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogPrincipalTableNotInSelectionSet">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogMissingSchema">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogMissingTable">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogFoundSequence">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogFoundTable">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogFoundIndex">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogFoundPrimaryKey">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogFoundUniqueConstraint">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogPrincipalColumnNotFound">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Diagnostics.Internal.SqlServerLoggingDefinitions.LogReflexiveConstraintIgnored">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.ISqlServerOptions">
            <summary>
                <para>
                    Options set at the <see cref="T:System.IServiceProvider" /> singleton level to control
                    SQL Server specific options.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> and multiple registrations
                    are allowed. This means a single instance of each service is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" />
                    instances. The implementation must be thread-safe.
                    This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
                </para>
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.ISqlServerOptions.RowNumberPagingEnabled">
            <summary>
                Reflects the option set by <see cref="M:Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder.UseRowNumberForPaging(System.Boolean)" />.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerOptionsExtension">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerOptionsExtension.#ctor">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerOptionsExtension.#ctor(Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerOptionsExtension)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerOptionsExtension.Info">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerOptionsExtension.Clone">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerOptionsExtension.RowNumberPaging">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerOptionsExtension.WithRowNumberPaging(System.Boolean)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerOptionsExtension.ApplyServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.DecimalTypeDefaultWarning(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation},Microsoft.EntityFrameworkCore.Metadata.IProperty)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.ByteIdentityColumnWarning(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation},Microsoft.EntityFrameworkCore.Metadata.IProperty)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.ColumnFound(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding},System.String,System.String,System.Int32,System.String,System.Int32,System.Int32,System.Int32,System.Boolean,System.Boolean,System.String,System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.ForeignKeyFound(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding},System.String,System.String,System.String,System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.DefaultSchemaFound(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding},System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.TypeAliasFound(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding},System.String,System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.PrimaryKeyFound(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding},System.String,System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.UniqueConstraintFound(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding},System.String,System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.IndexFound(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding},System.String,System.String,System.Boolean)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.ForeignKeyReferencesMissingPrincipalTableWarning(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding},System.String,System.String,System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.ForeignKeyPrincipalColumnMissingWarning(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding},System.String,System.String,System.String,System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.MissingSchemaWarning(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding},System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.MissingTableWarning(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding},System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.SequenceFound(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding},System.String,System.String,System.Boolean,System.Int32,System.Int64,System.Int64,System.Int64)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.TableFound(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding},System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerLoggerExtensions.ReflexiveConstraintIgnored(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding},System.String,System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerModelValidator">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" />. This means a single instance
                    is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instances. The implementation must be thread-safe.
                    This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerModelValidator.#ctor(Microsoft.EntityFrameworkCore.Infrastructure.ModelValidatorDependencies,Microsoft.EntityFrameworkCore.Infrastructure.RelationalModelValidatorDependencies)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerModelValidator.Validate(Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerModelValidator.ValidateDefaultDecimalMapping(Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerModelValidator.ValidateByteIdentityMapping(Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerModelValidator.ValidateNonKeyValueGeneration(Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerModelValidator.ValidateIndexIncludeProperties(Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerModelValidator.ValidateSharedTableCompatibility(System.Collections.Generic.IReadOnlyList{Microsoft.EntityFrameworkCore.Metadata.IEntityType},System.String,Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerModelValidator.ValidateSharedColumnsCompatibility(System.Collections.Generic.IReadOnlyList{Microsoft.EntityFrameworkCore.Metadata.IEntityType},System.String,Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerModelValidator.ValidateSharedKeysCompatibility(System.Collections.Generic.IReadOnlyList{Microsoft.EntityFrameworkCore.Metadata.IEntityType},System.String,Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerOptions">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> and multiple registrations
                    are allowed. This means a single instance of each service is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" />
                    instances. The implementation must be thread-safe.
                    This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerOptions.Initialize(Microsoft.EntityFrameworkCore.Infrastructure.IDbContextOptions)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerOptions.Validate(Microsoft.EntityFrameworkCore.Infrastructure.IDbContextOptions)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerOptions.RowNumberPagingEnabled">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.IdentityBadType(System.Object,System.Object,System.Object)">
            <summary>
                Identity value generation cannot be used for the property '{property}' on entity type '{entityType}' because the property type is '{propertyType}'. Identity value generation can only be used with signed integer properties.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.UnqualifiedDataType(System.Object)">
            <summary>
                Data type '{dataType}' is not supported in this form. Either specify the length explicitly in the type name, for example as '{dataType}(16)', or remove the data type and use APIs such as HasMaxLength to allow EF choose the data type.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.UnqualifiedDataTypeOnProperty(System.Object,System.Object)">
            <summary>
                Data type '{dataType}' for property '{property}' is not supported in this form. Either specify the length explicitly in the type name, for example as '{dataType}(16)', or remove the data type and use APIs such as HasMaxLength to allow EF choose the data type.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.SequenceBadType(System.Object,System.Object,System.Object)">
            <summary>
                SQL Server sequences cannot be used to generate values for the property '{property}' on entity type '{entityType}' because the property type is '{propertyType}'. Sequences can only be used with integer properties.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.IndexTableRequired">
            <summary>
                SQL Server requires the table name to be specified for rename index operations. Specify table name in the call to MigrationBuilder.RenameIndex.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.AlterMemoryOptimizedTable">
            <summary>
                To set memory-optimized on a table on or off the table needs to be dropped and recreated.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.AlterIdentityColumn">
            <summary>
                To change the IDENTITY property of a column, the column needs to be dropped and recreated.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.TransientExceptionDetected">
            <summary>
                An exception has been raised that is likely due to a transient failure. Consider enabling transient error resiliency by adding 'EnableRetryOnFailure()' to the 'UseSqlServer' call.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.NonKeyValueGeneration(System.Object,System.Object)">
            <summary>
                The property '{property}' on entity type '{entityType}' is configured to use 'SequenceHiLo' value generator, which is only intended for keys. If this was intentional configure an alternate key on the property, otherwise call 'ValueGeneratedNever' or configure store generation for this property.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.MultipleIdentityColumns(System.Object,System.Object)">
            <summary>
                The properties {properties} are configured to use 'Identity' value generator and are mapped to the same table '{table}'. Only one column per table can be configured as 'Identity'. Call 'ValueGeneratedNever' for properties that should not use 'Identity'.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.IncompatibleTableMemoryOptimizedMismatch(System.Object,System.Object,System.Object,System.Object,System.Object)">
            <summary>
                Cannot use table '{table}' for entity type '{entityType}' since it is being used for entity type '{otherEntityType}' and entity type '{memoryOptimizedEntityType}' is marked as memory-optimized, but entity type '{nonMemoryOptimizedEntityType}' is not.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.NoInitialCatalog">
            <summary>
                The database name could not be determined. To use EnsureDeleted, the connection string must specify Initial Catalog.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.DuplicateColumnNameValueGenerationStrategyMismatch(System.Object,System.Object,System.Object,System.Object,System.Object,System.Object)">
            <summary>
                '{entityType1}.{property1}' and '{entityType2}.{property2}' are both mapped to column '{columnName}' in '{table}' but are configured with different value generation strategies.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.InvalidTableToIncludeInScaffolding(System.Object)">
            <summary>
                The specified table '{table}' is not valid. Specify tables using the format '[schema].[table]'.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.InvalidColumnNameForFreeText">
            <summary>
                The expression passed to the 'propertyReference' parameter of the 'FreeText' method is not a valid reference to a property. The expression should represent a reference to a full-text indexed property on the object referenced in the from clause: 'from e in context.Entities where EF.Functions.FreeText(e.SomeProperty, textToSearchFor) select e'
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.IncludePropertyDuplicated(System.Object,System.Object)">
            <summary>
                Include property '{entityType}.{property}' cannot be defined multiple times
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.IncludePropertyInIndex(System.Object,System.Object)">
            <summary>
                Include property '{entityType}.{property}' is already included in the index
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.IncludePropertyNotFound(System.Object,System.Object)">
            <summary>
                Include property '{entityType}.{property}' not found
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.DuplicateKeyMismatchedClustering(System.Object,System.Object,System.Object,System.Object,System.Object,System.Object)">
            <summary>
                The keys {key1} on '{entityType1}' and {key2} on '{entityType2}' are both mapped to '{table}.{keyName}' but with different clustering.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerStrings.FunctionOnClient(System.Object)">
            <summary>
                The '{methodName}' method is not supported because the query has switched to client-evaluation. Inspect the log to determine which query expressions are triggering client-evaluation.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogDefaultDecimalTypeColumn(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                No type was specified for the decimal column '{property}' on entity type '{entityType}'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values using 'HasColumnType()'.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogByteIdentityColumn(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                The property '{property}' on entity type '{entityType}' is of type 'byte', but is set up to use a SQL Server identity column. This requires that values starting at 255 and counting down will be used for temporary key values. A temporary key value is needed for every entity inserted in a single call to 'SaveChanges'. Care must be taken that these values do not collide with real key values.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogFoundDefaultSchema(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                Found default schema {defaultSchema}.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogFoundTypeAlias(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                Found type alias with name: {alias} which maps to underlying data type {dataType}.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogFoundColumn(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                Found column with table: {tableName}, column name: {columnName}, ordinal: {ordinal}, data type: {dataType}, maximum length: {maxLength}, precision: {precision}, scale: {scale}, nullable: {isNullable}, identity: {isIdentity}, default value: {defaultValue}, computed value: {computedValue}
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogFoundForeignKey(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                Found foreign key on table: {tableName}, name: {foreignKeyName}, principal table: {principalTableName}, delete action: {deleteAction}.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogPrincipalTableNotInSelectionSet(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                For foreign key {fkName} on table {tableName}, unable to model the end of the foreign key on principal table {principaltableName}. This is usually because the principal table was not included in the selection set.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogMissingSchema(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                Unable to find a schema in the database matching the selected schema {schema}.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogMissingTable(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                Unable to find a table in the database matching the selected table {table}.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogFoundSequence(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                Found sequence name: {name}, data type: {dataType}, cyclic: {isCyclic}, increment: {increment}, start: {start}, minimum: {min}, maximum: {max}.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogFoundTable(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                Found table with name: {name}.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogFoundIndex(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                Found index with name: {indexName}, table: {tableName}, is unique: {isUnique}.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogFoundPrimaryKey(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                Found primary key with name: {primaryKeyName}, table: {tableName}.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogFoundUniqueConstraint(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                Found unique constraint with name: {uniqueConstraintName}, table: {tableName}.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogPrincipalColumnNotFound(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                For foreign key {foreignKeyName} on table {tableName}, unable to find the column called {principalColumnName} on the foreign key's principal table, {principaltableName}. Skipping foreign key.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerResources.LogReflexiveConstraintIgnored(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger)">
            <summary>
                Skipping foreign key '{foreignKeyName}' on table '{tableName}' since all of its columns reference themselves.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.Prefix">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.Clustered">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.Include">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.CreatedOnline">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.ValueGenerationStrategy">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.HiLoSequenceName">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.HiLoSequenceSchema">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.MemoryOptimized">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.Identity">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.IdentitySeed">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.IdentityIncrement">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.EditionOptions">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.MaxDatabaseSize">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.ServiceTierSql">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServer.Metadata.Internal.SqlServerAnnotationNames.PerformanceLevelSql">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerHistoryRepository">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />. This means that each
                    <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instance will use its own instance of this service.
                    The implementation may depend on other services registered with any lifetime.
                    The implementation does not need to be thread-safe.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerHistoryRepository.#ctor(Microsoft.EntityFrameworkCore.Migrations.HistoryRepositoryDependencies)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerHistoryRepository.ExistsSql">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerHistoryRepository.InterpretExistsResult(System.Object)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerHistoryRepository.GetCreateIfNotExistsScript">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerHistoryRepository.GetBeginIfNotExistsScript(System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerHistoryRepository.GetBeginIfExistsScript(System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerHistoryRepository.GetEndIfScript">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerMigrationsAnnotationProvider">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" />. This means a single instance
                    is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instances. The implementation must be thread-safe.
                    This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerMigrationsAnnotationProvider.#ctor(Microsoft.EntityFrameworkCore.Migrations.MigrationsAnnotationProviderDependencies)">
            <summary>
                Initializes a new instance of this class.
            </summary>
            <param name="dependencies"> Parameter object containing dependencies for this service. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerMigrationsAnnotationProvider.For(Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerMigrationsAnnotationProvider.For(Microsoft.EntityFrameworkCore.Metadata.IEntityType)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerMigrationsAnnotationProvider.For(Microsoft.EntityFrameworkCore.Metadata.IKey)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerMigrationsAnnotationProvider.For(Microsoft.EntityFrameworkCore.Metadata.IIndex)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerMigrationsAnnotationProvider.For(Microsoft.EntityFrameworkCore.Metadata.IProperty)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerMigrationsAnnotationProvider.ForRemove(Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal.SqlServerMigrationsAnnotationProvider.ForRemove(Microsoft.EntityFrameworkCore.Metadata.IEntityType)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Query.Internal.SqlServerCompiledQueryCacheKeyGenerator">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />. This means that each
                    <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instance will use its own instance of this service.
                    The implementation may depend on other services registered with any lifetime.
                    The implementation does not need to be thread-safe.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Query.Internal.SqlServerCompiledQueryCacheKeyGenerator.#ctor(Microsoft.EntityFrameworkCore.Query.CompiledQueryCacheKeyGeneratorDependencies,Microsoft.EntityFrameworkCore.Query.RelationalCompiledQueryCacheKeyGeneratorDependencies)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Query.Internal.SqlServerCompiledQueryCacheKeyGenerator.GenerateCacheKey(System.Linq.Expressions.Expression,System.Boolean)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Query.Internal.SqlServerQueryTranslationPostprocessorFactory">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" />. This means a single instance
                    is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instances. The implementation must be thread-safe.
                    This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
                </para>
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Scaffolding.Internal.SqlDataReaderExtension">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Scaffolding.Internal.SqlDataReaderExtension.GetValueOrDefault``1(System.Data.Common.DbDataReader,System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Scaffolding.Internal.SqlDataReaderExtension.GetValueOrDefault``1(System.Data.Common.DbDataRecord,System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Scaffolding.Internal.SqlServerCodeGenerator">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Scaffolding.Internal.SqlServerCodeGenerator.#ctor(Microsoft.EntityFrameworkCore.Scaffolding.ProviderCodeGeneratorDependencies)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.EntityFrameworkCore.SqlServer.Scaffolding.Internal.SqlServerCodeGenerator" /> class.
            </summary>
            <param name="dependencies"> The dependencies. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Scaffolding.Internal.SqlServerCodeGenerator.GenerateUseProvider(System.String,Microsoft.EntityFrameworkCore.Design.MethodCallCodeFragment)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Scaffolding.Internal.SqlServerDatabaseModelFactory">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Scaffolding.Internal.SqlServerDatabaseModelFactory.#ctor(Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Scaffolding.Internal.SqlServerDatabaseModelFactory.Create(System.String,Microsoft.EntityFrameworkCore.Scaffolding.DatabaseModelFactoryOptions)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Scaffolding.Internal.SqlServerDatabaseModelFactory.Create(System.Data.Common.DbConnection,Microsoft.EntityFrameworkCore.Scaffolding.DatabaseModelFactoryOptions)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.ISqlServerConnection">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />. This means that each
                    <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instance will use its own instance of this service.
                    The implementation may depend on other services registered with any lifetime.
                    The implementation does not need to be thread-safe.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.ISqlServerConnection.CreateMasterConnection">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerBoolTypeMapping">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerBoolTypeMapping.#ctor(System.String,System.Nullable{System.Data.DbType})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerBoolTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerBoolTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerBoolTypeMapping.GenerateNonNullSqlLiteral(System.Object)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerByteArrayTypeMapping">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerByteArrayTypeMapping.#ctor(System.String,System.Nullable{System.Int32},System.Boolean,Microsoft.EntityFrameworkCore.ChangeTracking.ValueComparer,System.Nullable{System.Data.SqlDbType},System.Nullable{Microsoft.EntityFrameworkCore.Storage.StoreTypePostfix})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerByteArrayTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters,System.Nullable{System.Data.SqlDbType})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerByteArrayTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerByteArrayTypeMapping.ConfigureParameter(System.Data.Common.DbParameter)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerByteArrayTypeMapping.GenerateNonNullSqlLiteral(System.Object)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerByteTypeMapping">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerByteTypeMapping.#ctor(System.String,System.Nullable{System.Data.DbType})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerByteTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerByteTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerByteTypeMapping.GenerateNonNullSqlLiteral(System.Object)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerConnection">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />. This means that each
                    <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instance will use its own instance of this service.
                    The implementation may depend on other services registered with any lifetime.
                    The implementation does not need to be thread-safe.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerConnection.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalConnectionDependencies)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerConnection.CreateDbConnection">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerConnection.CreateMasterConnection">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerConnection.IsMultipleActiveResultSetsEnabled">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerConnection.SupportsAmbientTransactions">
            <summary>
                Indicates whether the store connection supports ambient transactions
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />. This means that each
                    <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instance will use its own instance of this service.
                    The implementation may depend on other services registered with any lifetime.
                    The implementation does not need to be thread-safe.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreatorDependencies,Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.ISqlServerConnection,Microsoft.EntityFrameworkCore.Storage.IRawSqlCommandBuilder)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.RetryDelay">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.RetryTimeout">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.Create">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.CreateAsync(System.Threading.CancellationToken)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.HasTables">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.HasTablesAsync(System.Threading.CancellationToken)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.Exists">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.ExistsAsync(System.Threading.CancellationToken)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.Delete">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDatabaseCreator.DeleteAsync(System.Threading.CancellationToken)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDateTimeOffsetTypeMapping">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDateTimeOffsetTypeMapping.#ctor(System.String,System.Nullable{System.Data.DbType})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDateTimeOffsetTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDateTimeOffsetTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDateTimeOffsetTypeMapping.SqlLiteralFormatString">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDateTimeOffsetTypeMapping.ConfigureParameter(System.Data.Common.DbParameter)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDateTimeTypeMapping">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDateTimeTypeMapping.#ctor(System.String,System.Nullable{System.Data.DbType})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDateTimeTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDateTimeTypeMapping.ConfigureParameter(System.Data.Common.DbParameter)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDateTimeTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDateTimeTypeMapping.SqlLiteralFormatString">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDecimalTypeMapping">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDecimalTypeMapping.#ctor(System.String,System.Nullable{System.Data.DbType},System.Nullable{System.Int32},System.Nullable{System.Int32},Microsoft.EntityFrameworkCore.Storage.StoreTypePostfix)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDecimalTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDecimalTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDecimalTypeMapping.ConfigureParameter(System.Data.Common.DbParameter)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDoubleTypeMapping">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDoubleTypeMapping.#ctor(System.String,System.Nullable{System.Data.DbType})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDoubleTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDoubleTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDoubleTypeMapping.GenerateNonNullSqlLiteral(System.Object)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerDoubleTypeMapping.ConfigureParameter(System.Data.Common.DbParameter)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.#ctor(Microsoft.EntityFrameworkCore.Storage.ExecutionStrategyDependencies)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.RetriesOnFailure">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute``2(``0,System.Func{Microsoft.EntityFrameworkCore.DbContext,``0,``1},System.Func{Microsoft.EntityFrameworkCore.DbContext,``0,Microsoft.EntityFrameworkCore.Storage.ExecutionResult{``1}})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync``2(``0,System.Func{Microsoft.EntityFrameworkCore.DbContext,``0,System.Threading.CancellationToken,System.Threading.Tasks.Task{``1}},System.Func{Microsoft.EntityFrameworkCore.DbContext,``0,System.Threading.CancellationToken,System.Threading.Tasks.Task{Microsoft.EntityFrameworkCore.Storage.ExecutionResult{``1}}},System.Threading.CancellationToken)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategyFactory">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />. This means that each
                    <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instance will use its own instance of this service.
                    The implementation may depend on other services registered with any lifetime.
                    The implementation does not need to be thread-safe.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategyFactory.#ctor(Microsoft.EntityFrameworkCore.Storage.ExecutionStrategyDependencies)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategyFactory.CreateDefaultStrategy(Microsoft.EntityFrameworkCore.Storage.ExecutionStrategyDependencies)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerFloatTypeMapping">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerFloatTypeMapping.#ctor(System.String,System.Nullable{System.Data.DbType})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerFloatTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerFloatTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerFloatTypeMapping.GenerateNonNullSqlLiteral(System.Object)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerFloatTypeMapping.ConfigureParameter(System.Data.Common.DbParameter)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerLongTypeMapping">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerLongTypeMapping.#ctor(System.String,System.Nullable{System.Data.DbType})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerLongTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerLongTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerLongTypeMapping.GenerateNonNullSqlLiteral(System.Object)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerShortTypeMapping">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerShortTypeMapping.#ctor(System.String,System.Nullable{System.Data.DbType})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerShortTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerShortTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerShortTypeMapping.GenerateNonNullSqlLiteral(System.Object)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerSqlGenerationHelper">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" />. This means a single instance
                    is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instances. The implementation must be thread-safe.
                    This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerSqlGenerationHelper.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalSqlGenerationHelperDependencies)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerSqlGenerationHelper.BatchTerminator">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerSqlGenerationHelper.EscapeIdentifier(System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerSqlGenerationHelper.EscapeIdentifier(System.Text.StringBuilder,System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerSqlGenerationHelper.DelimitIdentifier(System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerSqlGenerationHelper.DelimitIdentifier(System.Text.StringBuilder,System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerSqlVariantTypeMapping">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerSqlVariantTypeMapping.#ctor(System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerSqlVariantTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerSqlVariantTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerStringTypeMapping">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerStringTypeMapping.#ctor(System.String,System.Boolean,System.Nullable{System.Int32},System.Boolean,System.Nullable{System.Data.SqlDbType},System.Nullable{Microsoft.EntityFrameworkCore.Storage.StoreTypePostfix})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerStringTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters,System.Nullable{System.Data.SqlDbType})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerStringTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerStringTypeMapping.ConfigureParameter(System.Data.Common.DbParameter)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerStringTypeMapping.GenerateNonNullSqlLiteral(System.Object)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerTimeSpanTypeMapping">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerTimeSpanTypeMapping.#ctor(System.String,System.Nullable{System.Data.DbType})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerTimeSpanTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerTimeSpanTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerTimeSpanTypeMapping.ConfigureParameter(System.Data.Common.DbParameter)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerTransientExceptionDetector">
            <summary>
                Detects the exceptions caused by SQL Server transient failures.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerTransientExceptionDetector.ShouldRetryOn(System.Exception)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerTypeMappingSource">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" />. This means a single instance
                    is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instances. The implementation must be thread-safe.
                    This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerTypeMappingSource.#ctor(Microsoft.EntityFrameworkCore.Storage.TypeMappingSourceDependencies,Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSourceDependencies)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerTypeMappingSource.ValidateMapping(Microsoft.EntityFrameworkCore.Storage.CoreTypeMapping,Microsoft.EntityFrameworkCore.Metadata.IProperty)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerTypeMappingSource.FindMapping(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingInfo@)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerUdtTypeMapping">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerUdtTypeMapping.#ctor(System.Type,System.String,System.Func{System.Object,System.Linq.Expressions.Expression},Microsoft.EntityFrameworkCore.Storage.StoreTypePostfix,System.String,Microsoft.EntityFrameworkCore.Storage.ValueConversion.ValueConverter,Microsoft.EntityFrameworkCore.ChangeTracking.ValueComparer,Microsoft.EntityFrameworkCore.ChangeTracking.ValueComparer,System.Nullable{System.Data.DbType},System.Boolean,System.Nullable{System.Int32},System.Boolean,System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerUdtTypeMapping.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters,System.Func{System.Object,System.Linq.Expressions.Expression},System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerUdtTypeMapping.UdtTypeName">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerUdtTypeMapping.LiteralGenerator">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerUdtTypeMapping.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                Creates a copy of this mapping.
            </summary>
            <param name="parameters"> The parameters for this mapping. </param>
            <returns> The newly created mapping. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerUdtTypeMapping.ConfigureParameter(System.Data.Common.DbParameter)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerUdtTypeMapping.GenerateCodeLiteral(System.Object)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerUdtTypeMapping.CreateSqlHierarchyIdMapping(System.Type)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerUdtTypeMapping.CreateSqlSpatialMapping(System.Type,System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.ISqlServerUpdateSqlGenerator">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" />. This means a single instance
                    is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instances. The implementation must be thread-safe.
                    This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.ISqlServerUpdateSqlGenerator.AppendBulkInsertOperation(System.Text.StringBuilder,System.Collections.Generic.IReadOnlyList{Microsoft.EntityFrameworkCore.Update.ModificationCommand},System.Int32)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.#ctor(Microsoft.EntityFrameworkCore.Update.ModificationCommandBatchFactoryDependencies,System.Nullable{System.Int32})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.UpdateSqlGenerator">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.CanAddCommand(Microsoft.EntityFrameworkCore.Update.ModificationCommand)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.IsCommandTextValid">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.GetParameterCount">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ResetCommandText">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.GetCommandText">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.UpdateCachedCommandText(System.Int32)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatchFactory">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />. This means that each
                    <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instance will use its own instance of this service.
                    The implementation may depend on other services registered with any lifetime.
                    The implementation does not need to be thread-safe.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatchFactory.#ctor(Microsoft.EntityFrameworkCore.Update.ModificationCommandBatchFactoryDependencies,Microsoft.EntityFrameworkCore.Infrastructure.IDbContextOptions)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatchFactory.Create">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerUpdateSqlGenerator">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" />. This means a single instance
                    is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instances. The implementation must be thread-safe.
                    This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerUpdateSqlGenerator.#ctor(Microsoft.EntityFrameworkCore.Update.UpdateSqlGeneratorDependencies)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerUpdateSqlGenerator.AppendBulkInsertOperation(System.Text.StringBuilder,System.Collections.Generic.IReadOnlyList{Microsoft.EntityFrameworkCore.Update.ModificationCommand},System.Int32)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerUpdateSqlGenerator.AppendSelectAffectedCountCommand(System.Text.StringBuilder,System.String,System.String,System.Int32)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerUpdateSqlGenerator.AppendBatchHeader(System.Text.StringBuilder)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerUpdateSqlGenerator.AppendIdentityWhereCondition(System.Text.StringBuilder,Microsoft.EntityFrameworkCore.Update.ColumnModification)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerUpdateSqlGenerator.AppendRowsAffectedWhereCondition(System.Text.StringBuilder,System.Int32)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.ISqlServerSequenceValueGeneratorFactory">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.ISqlServerSequenceValueGeneratorFactory.Create(Microsoft.EntityFrameworkCore.Metadata.IProperty,Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerSequenceValueGeneratorState,Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.ISqlServerConnection,Microsoft.EntityFrameworkCore.Storage.IRawSqlCommandBuilder,Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Database.Command})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.ISqlServerValueGeneratorCache">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" />. This means a single instance
                    is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instances. The implementation must be thread-safe.
                    This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.ISqlServerValueGeneratorCache.GetOrAddSequenceState(Microsoft.EntityFrameworkCore.Metadata.IProperty,Microsoft.EntityFrameworkCore.Storage.IRelationalConnection)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerSequenceHiLoValueGenerator`1">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerSequenceHiLoValueGenerator`1.#ctor(Microsoft.EntityFrameworkCore.Storage.IRawSqlCommandBuilder,Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.ISqlServerUpdateSqlGenerator,Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerSequenceValueGeneratorState,Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.ISqlServerConnection,Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Database.Command})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerSequenceHiLoValueGenerator`1.GetNewLowValue">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerSequenceHiLoValueGenerator`1.GetNewLowValueAsync(System.Threading.CancellationToken)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerSequenceHiLoValueGenerator`1.GeneratesTemporaryValues">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerSequenceValueGeneratorFactory">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerSequenceValueGeneratorFactory.#ctor(Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.ISqlServerUpdateSqlGenerator)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerSequenceValueGeneratorFactory.Create(Microsoft.EntityFrameworkCore.Metadata.IProperty,Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerSequenceValueGeneratorState,Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.ISqlServerConnection,Microsoft.EntityFrameworkCore.Storage.IRawSqlCommandBuilder,Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Database.Command})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerSequenceValueGeneratorState">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerSequenceValueGeneratorState.#ctor(Microsoft.EntityFrameworkCore.Metadata.ISequence)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerSequenceValueGeneratorState.Sequence">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerValueGeneratorCache">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" />. This means a single instance
                    is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instances. The implementation must be thread-safe.
                    This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerValueGeneratorCache.#ctor(Microsoft.EntityFrameworkCore.ValueGeneration.ValueGeneratorCacheDependencies)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.EntityFrameworkCore.ValueGeneration.ValueGeneratorCache" /> class.
            </summary>
            <param name="dependencies"> Parameter object containing dependencies for this service. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerValueGeneratorCache.GetOrAddSequenceState(Microsoft.EntityFrameworkCore.Metadata.IProperty,Microsoft.EntityFrameworkCore.Storage.IRelationalConnection)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerValueGeneratorSelector">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />. This means that each
                    <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instance will use its own instance of this service.
                    The implementation may depend on other services registered with any lifetime.
                    The implementation does not need to be thread-safe.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerValueGeneratorSelector.#ctor(Microsoft.EntityFrameworkCore.ValueGeneration.ValueGeneratorSelectorDependencies,Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.ISqlServerSequenceValueGeneratorFactory,Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.ISqlServerConnection,Microsoft.EntityFrameworkCore.Storage.IRawSqlCommandBuilder,Microsoft.EntityFrameworkCore.Diagnostics.IDiagnosticsLogger{Microsoft.EntityFrameworkCore.DbLoggerCategory.Database.Command})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerValueGeneratorSelector.Cache">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerValueGeneratorSelector.Select(Microsoft.EntityFrameworkCore.Metadata.IProperty,Microsoft.EntityFrameworkCore.Metadata.IEntityType)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.ValueGeneration.Internal.SqlServerValueGeneratorSelector.Create(Microsoft.EntityFrameworkCore.Metadata.IProperty,Microsoft.EntityFrameworkCore.Metadata.IEntityType)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId">
            <summary>
                <para>
                    Event IDs for SQL Server events that correspond to messages logged to an <see cref="T:Microsoft.Extensions.Logging.ILogger" />
                    and events sent to a <see cref="T:System.Diagnostics.DiagnosticSource" />.
                </para>
                <para>
                    These IDs are also used with <see cref="T:Microsoft.EntityFrameworkCore.Diagnostics.WarningsConfigurationBuilder" /> to configure the
                    behavior of warnings.
                </para>
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.DecimalTypeDefaultWarning">
            <summary>
                <para>
                    No explicit type for a decimal column.
                </para>
                <para>
                    This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation" /> category.
                </para>
                <para>
                    This event uses the <see cref="T:Microsoft.EntityFrameworkCore.Diagnostics.PropertyEventData" /> payload when used with a <see cref="T:System.Diagnostics.DiagnosticSource" />.
                </para>
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.ByteIdentityColumnWarning">
            <summary>
                <para>
                    A byte property is set up to use a SQL Server identity column.
                </para>
                <para>
                    This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Model.Validation" /> category.
                </para>
                <para>
                    This event uses the <see cref="T:Microsoft.EntityFrameworkCore.Diagnostics.PropertyEventData" /> payload when used with a <see cref="T:System.Diagnostics.DiagnosticSource" />.
                </para>
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.ColumnFound">
            <summary>
                A column was found.
                This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.DefaultSchemaFound">
            <summary>
                A default schema was found.
                This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.TypeAliasFound">
            <summary>
                A type alias was found.
                This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.MissingSchemaWarning">
            <summary>
                The database is missing a schema.
                This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.MissingTableWarning">
            <summary>
                The database is missing a table.
                This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.ForeignKeyReferencesMissingPrincipalTableWarning">
            <summary>
                A foreign key references a missing table at the principal end.
                This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.TableFound">
            <summary>
                A table was found.
                This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.SequenceFound">
            <summary>
                A sequence was found.
                This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.PrimaryKeyFound">
            <summary>
                Primary key was found.
                This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.UniqueConstraintFound">
            <summary>
                An unique constraint was found.
                This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.IndexFound">
            <summary>
                An index was found.
                This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.ForeignKeyFound">
            <summary>
                A foreign key was found.
                This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.ForeignKeyPrincipalColumnMissingWarning">
            <summary>
                A principal column referenced by a foreign key was not found.
                This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Diagnostics.SqlServerEventId.ReflexiveConstraintIgnored">
            <summary>
                A reflexive foreign key constraint was skipped.
                This event is in the <see cref="T:Microsoft.EntityFrameworkCore.DbLoggerCategory.Scaffolding" /> category.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerDatabaseFacadeExtensions">
            <summary>
                SQL Server specific extension methods for <see cref="P:Microsoft.EntityFrameworkCore.DbContext.Database" />.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDatabaseFacadeExtensions.IsSqlServer(Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade)">
            <summary>
                <para>
                    Returns true if the database provider currently in use is the SQL Server provider.
                </para>
                <para>
                    This method can only be used after the <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> has been configured because
                    it is only then that the provider is known. This means that this method cannot be used
                    in <see cref="M:Microsoft.EntityFrameworkCore.DbContext.OnConfiguring(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder)" /> because this is where application code sets the
                    provider to use as part of configuring the context.
                </para>
            </summary>
            <param name="database"> The facade from <see cref="P:Microsoft.EntityFrameworkCore.DbContext.Database" />. </param>
            <returns> True if SQL Server is being used; false otherwise. </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerDbContextOptionsExtensions">
            <summary>
                SQL Server specific extension methods for <see cref="T:Microsoft.EntityFrameworkCore.DbContextOptionsBuilder" />.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbContextOptionsExtensions.UseSqlServer(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder,System.String,System.Action{Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder})">
            <summary>
                Configures the context to connect to a Microsoft SQL Server database.
            </summary>
            <param name="optionsBuilder"> The builder being used to configure the context. </param>
            <param name="connectionString"> The connection string of the database to connect to. </param>
            <param name="sqlServerOptionsAction">An optional action to allow additional SQL Server specific configuration.</param>
            <returns> The options builder so that further configuration can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbContextOptionsExtensions.UseSqlServer(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder,System.Data.Common.DbConnection,System.Action{Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder})">
            <summary>
                Configures the context to connect to a Microsoft SQL Server database.
            </summary>
            <param name="optionsBuilder"> The builder being used to configure the context. </param>
            <param name="connection">
                An existing <see cref="T:System.Data.Common.DbConnection" /> to be used to connect to the database. If the connection is
                in the open state then EF will not open or close the connection. If the connection is in the closed
                state then EF will open and close the connection as needed.
            </param>
            <param name="sqlServerOptionsAction">An optional action to allow additional SQL Server specific configuration.</param>
            <returns> The options builder so that further configuration can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbContextOptionsExtensions.UseSqlServer``1(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder{``0},System.String,System.Action{Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder})">
            <summary>
                Configures the context to connect to a Microsoft SQL Server database.
            </summary>
            <typeparam name="TContext"> The type of context to be configured. </typeparam>
            <param name="optionsBuilder"> The builder being used to configure the context. </param>
            <param name="connectionString"> The connection string of the database to connect to. </param>
            <param name="sqlServerOptionsAction">An optional action to allow additional SQL Server specific configuration.</param>
            <returns> The options builder so that further configuration can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbContextOptionsExtensions.UseSqlServer``1(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder{``0},System.Data.Common.DbConnection,System.Action{Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder})">
            <summary>
                Configures the context to connect to a Microsoft SQL Server database.
            </summary>
            <typeparam name="TContext"> The type of context to be configured. </typeparam>
            <param name="optionsBuilder"> The builder being used to configure the context. </param>
            <param name="connection">
                An existing <see cref="T:System.Data.Common.DbConnection" /> to be used to connect to the database. If the connection is
                in the open state then EF will not open or close the connection. If the connection is in the closed
                state then EF will open and close the connection as needed.
            </param>
            <param name="sqlServerOptionsAction">An optional action to allow additional SQL Server specific configuration.</param>
            <returns> The options builder so that further configuration can be chained. </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions">
            <summary>
                Provides CLR methods that get translated to database functions when used in LINQ to Entities queries.
                The methods on this class are accessed via <see cref="P:Microsoft.EntityFrameworkCore.EF.Functions" />.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.FreeText(Microsoft.EntityFrameworkCore.DbFunctions,System.String,System.String,System.Int32)">
            <summary>
                <para>
                    A DbFunction method stub that can be used in LINQ queries to target the SQL Server FREETEXT store function.
                </para>
            </summary>
            <remarks>
                This DbFunction method has no in-memory implementation and will throw if the query switches to client-evaluation.
                This can happen if the query contains one or more expressions that could not be translated to the store.
            </remarks>
            <param name="_">DbFunctions instance</param>
            <param name="propertyReference">The property on which the search will be performed.</param>
            <param name="freeText">The text that will be searched for in the property.</param>
            <param name="languageTerm">A Language ID from the sys.syslanguages table.</param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.FreeText(Microsoft.EntityFrameworkCore.DbFunctions,System.String,System.String)">
            <summary>
                <para>
                    A DbFunction method stub that can be used in LINQ queries to target the SQL Server FREETEXT store function.
                </para>
            </summary>
            <remarks>
                This DbFunction method has no in-memory implementation and will throw if the query switches to client-evaluation.
                This can happen if the query contains one or more expressions that could not be translated to the store.
            </remarks>
            <param name="_">DbFunctions instance</param>
            <param name="propertyReference">The property on which the search will be performed.</param>
            <param name="freeText">The text that will be searched for in the property.</param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.Contains(Microsoft.EntityFrameworkCore.DbFunctions,System.String,System.String,System.Int32)">
            <summary>
                <para>
                    A DbFunction method stub that can be used in LINQ queries to target the SQL Server CONTAINS store function.
                </para>
            </summary>
            <remarks>
                This DbFunction method has no in-memory implementation and will throw if the query switches to client-evaluation.
                This can happen if the query contains one or more expressions that could not be translated to the store.
            </remarks>
            <param name="_">DbFunctions instance</param>
            <param name="propertyReference">The property on which the search will be performed.</param>
            <param name="searchCondition">The text that will be searched for in the property and the condition for a match.</param>
            <param name="languageTerm">A Language ID from the sys.syslanguages table.</param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.Contains(Microsoft.EntityFrameworkCore.DbFunctions,System.String,System.String)">
            <summary>
                <para>
                    A DbFunction method stub that can be used in LINQ queries to target the SQL Server CONTAINS store function.
                </para>
            </summary>
            <remarks>
                This DbFunction method has no in-memory implementation and will throw if the query switches to client-evaluation.
                This can happen if the query contains one or more expressions that could not be translated to the store.
            </remarks>
            <param name="_">DbFunctions instance</param>
            <param name="propertyReference">The property on which the search will be performed.</param>
            <param name="searchCondition">The text that will be searched for in the property and the condition for a match.</param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffYear(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
                Counts the number of year boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(YEAR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of year boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffYear(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
                Counts the number of year boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(YEAR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of year boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffYear(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
                Counts the number of year boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(YEAR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of year boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffYear(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
                Counts the number of year boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(YEAR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of year boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMonth(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
                Counts the number of month boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MONTH,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of month boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMonth(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
                Counts the number of month boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MONTH,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of month boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMonth(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
                Counts the number of month boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MONTH,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of month boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMonth(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
                Counts the number of month boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MONTH,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of month boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffDay(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
                Counts the number of day boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(DAY,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of day boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffDay(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
                Counts the number of day boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(DAY,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of day boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffDay(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
                Counts the number of day boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(DAY,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of day boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffDay(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
                Counts the number of day boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(DAY,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of day boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffHour(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
                Counts the number of hour boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(HOUR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of hour boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffHour(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
                Counts the number of hour boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(HOUR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of hour boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffHour(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
                Counts the number of hour boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(HOUR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of hour boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffHour(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
                Counts the number of hour boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(HOUR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of hour boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffHour(Microsoft.EntityFrameworkCore.DbFunctions,System.TimeSpan,System.TimeSpan)">
            <summary>
                Counts the number of hour boundaries crossed between the startTimeSpan and endTimeSpan.
                Corresponds to SQL Server's DATEDIFF(HOUR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startTimeSpan">Starting timespan for the calculation.</param>
            <param name="endTimeSpan">Ending timespan for the calculation.</param>
            <returns>Number of hour boundaries crossed between the timespans.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffHour(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan})">
            <summary>
                Counts the number of hour boundaries crossed between the startTimeSpan and endTimeSpan.
                Corresponds to SQL Server's DATEDIFF(HOUR,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startTimeSpan">Starting timespan for the calculation.</param>
            <param name="endTimeSpan">Ending timespan for the calculation.</param>
            <returns>Number of hour boundaries crossed between the timespans.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMinute(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
                Counts the number of minute boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MINUTE,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of minute boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMinute(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
                Counts the number of minute boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MINUTE,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of minute boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMinute(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
                Counts the number of minute boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MINUTE,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of minute boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMinute(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
                Counts the number of minute boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MINUTE,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of minute boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMinute(Microsoft.EntityFrameworkCore.DbFunctions,System.TimeSpan,System.TimeSpan)">
            <summary>
                Counts the number of minute boundaries crossed between the startTimeSpan and endTimeSpan.
                Corresponds to SQL Server's DATEDIFF(MINUTE,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startTimeSpan">Starting timespan for the calculation.</param>
            <param name="endTimeSpan">Ending timespan for the calculation.</param>
            <returns>Number of minute boundaries crossed between the timespans.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMinute(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan})">
            <summary>
                Counts the number of minute boundaries crossed between the startTimeSpan and endTimeSpan.
                Corresponds to SQL Server's DATEDIFF(MINUTE,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startTimeSpan">Starting timespan for the calculation.</param>
            <param name="endTimeSpan">Ending timespan for the calculation.</param>
            <returns>Number of minute boundaries crossed between the timespans.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffSecond(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
                Counts the number of second boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(SECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of second boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffSecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
                Counts the number of second boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(SECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of second boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffSecond(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
                Counts the number of second boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(SECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of second boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffSecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
                Counts the number of second boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(SECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of second boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffSecond(Microsoft.EntityFrameworkCore.DbFunctions,System.TimeSpan,System.TimeSpan)">
            <summary>
                Counts the number of second boundaries crossed between the startTimeSpan and endTimeSpan.
                Corresponds to SQL Server's DATEDIFF(SECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startTimeSpan">Starting timespan for the calculation.</param>
            <param name="endTimeSpan">Ending timespan for the calculation.</param>
            <returns>Number of second boundaries crossed between the timespans.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffSecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan})">
            <summary>
                Counts the number of second boundaries crossed between the startTimeSpan and endTimeSpan.
                Corresponds to SQL Server's DATEDIFF(SECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startTimeSpan">Starting timespan for the calculation.</param>
            <param name="endTimeSpan">Ending timespan for the calculation.</param>
            <returns>Number of second boundaries crossed between the timespans.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMillisecond(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
                Counts the number of millisecond boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MILLISECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of millisecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMillisecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
                Counts the number of millisecond boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MILLISECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of millisecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMillisecond(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
                Counts the number of millisecond boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MILLISECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of millisecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMillisecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
                Counts the number of millisecond boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MILLISECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of millisecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMillisecond(Microsoft.EntityFrameworkCore.DbFunctions,System.TimeSpan,System.TimeSpan)">
            <summary>
                Counts the number of millisecond boundaries crossed between the startTimeSpan and endTimeSpan.
                Corresponds to SQL Server's DATEDIFF(MILLISECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startTimeSpan">Starting timespan for the calculation.</param>
            <param name="endTimeSpan">Ending timespan for the calculation.</param>
            <returns>Number of millisecond boundaries crossed between the timespans.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMillisecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan})">
            <summary>
                Counts the number of millisecond boundaries crossed between the startTimeSpan and endTimeSpan.
                Corresponds to SQL Server's DATEDIFF(MILLISECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startTimeSpan">Starting timespan for the calculation.</param>
            <param name="endTimeSpan">Ending timespan for the calculation.</param>
            <returns>Number of millisecond boundaries crossed between the timespans.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMicrosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
                Counts the number of microsecond boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MICROSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of microsecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMicrosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
                Counts the number of microsecond boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MICROSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of microsecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMicrosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
                Counts the number of microsecond boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MICROSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of microsecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMicrosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
                Counts the number of microsecond boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(MICROSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of microsecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMicrosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.TimeSpan,System.TimeSpan)">
            <summary>
                Counts the number of microsecond boundaries crossed between the startTimeSpan and endTimeSpan.
                Corresponds to SQL Server's DATEDIFF(MICROSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startTimeSpan">Starting timespan for the calculation.</param>
            <param name="endTimeSpan">Ending timespan for the calculation.</param>
            <returns>Number of microsecond boundaries crossed between the timespans.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffMicrosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan})">
            <summary>
                Counts the number of microsecond boundaries crossed between the startTimeSpan and endTimeSpan.
                Corresponds to SQL Server's DATEDIFF(MICROSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startTimeSpan">Starting timespan for the calculation.</param>
            <param name="endTimeSpan">Ending timespan for the calculation.</param>
            <returns>Number of microsecond boundaries crossed between the timespans.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffNanosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTime,System.DateTime)">
            <summary>
                Counts the number of nanosecond boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(NANOSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of nanosecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffNanosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
                Counts the number of nanosecond boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(NANOSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of nanosecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffNanosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
                Counts the number of nanosecond boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(NANOSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of nanosecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffNanosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset})">
            <summary>
                Counts the number of nanosecond boundaries crossed between the startDate and endDate.
                Corresponds to SQL Server's DATEDIFF(NANOSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startDate">Starting date for the calculation.</param>
            <param name="endDate">Ending date for the calculation.</param>
            <returns>Number of nanosecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffNanosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.TimeSpan,System.TimeSpan)">
            <summary>
                Counts the number of nanosecond boundaries crossed between the startTimeSpan and endTimeSpan.
                Corresponds to SQL Server's DATEDIFF(NANOSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startTimeSpan">Starting timespan for the calculation.</param>
            <param name="endTimeSpan">Ending timespan for the calculation.</param>
            <returns>Number of nanosecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.DateDiffNanosecond(Microsoft.EntityFrameworkCore.DbFunctions,System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan})">
            <summary>
                Counts the number of nanosecond boundaries crossed between the startTimeSpan and endTimeSpan.
                Corresponds to SQL Server's DATEDIFF(NANOSECOND,startDate,endDate).
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="startTimeSpan">Starting timespan for the calculation.</param>
            <param name="endTimeSpan">Ending timespan for the calculation.</param>
            <returns>Number of nanosecond boundaries crossed between the dates.</returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerDbFunctionsExtensions.IsDate(Microsoft.EntityFrameworkCore.DbFunctions,System.String)">
            <summary>
                Validate if the given string is a valid date.
                Corresponds to the SQL Server's ISDATE('date').
            </summary>
            <param name="_">The DbFunctions instance.</param>
            <param name="expression">Expression to validate</param>
            <returns>true for valid date and false otherwise.</returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerEntityTypeBuilderExtensions">
            <summary>
                SQL Server specific extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder" />.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeBuilderExtensions.IsMemoryOptimized(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder,System.Boolean)">
            <summary>
                Configures the table that the entity maps to when targeting SQL Server as memory-optimized.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="memoryOptimized"> A value indicating whether the table is memory-optimized. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeBuilderExtensions.IsMemoryOptimized``1(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder{``0},System.Boolean)">
            <summary>
                Configures the table that the entity maps to when targeting SQL Server as memory-optimized.
            </summary>
            <typeparam name="TEntity"> The entity type being configured. </typeparam>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="memoryOptimized"> A value indicating whether the table is memory-optimized. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeBuilderExtensions.IsMemoryOptimized(Microsoft.EntityFrameworkCore.Metadata.Builders.OwnedNavigationBuilder,System.Boolean)">
            <summary>
                Configures the table that the entity maps to when targeting SQL Server as memory-optimized.
            </summary>
            <param name="collectionOwnershipBuilder"> The builder for the entity type being configured. </param>
            <param name="memoryOptimized"> A value indicating whether the table is memory-optimized. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeBuilderExtensions.IsMemoryOptimized``2(Microsoft.EntityFrameworkCore.Metadata.Builders.OwnedNavigationBuilder{``0,``1},System.Boolean)">
            <summary>
                Configures the table that the entity maps to when targeting SQL Server as memory-optimized.
            </summary>
            <typeparam name="TEntity"> The entity type being configured. </typeparam>
            <typeparam name="TRelatedEntity"> The entity type that this relationship targets. </typeparam>
            <param name="collectionOwnershipBuilder"> The builder for the entity type being configured. </param>
            <param name="memoryOptimized"> A value indicating whether the table is memory-optimized. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeBuilderExtensions.IsMemoryOptimized(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionEntityTypeBuilder,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Configures the table that the entity maps to when targeting SQL Server as memory-optimized.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="memoryOptimized"> A value indicating whether the table is memory-optimized. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeBuilderExtensions.CanSetIsMemoryOptimized(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionEntityTypeBuilder,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Returns a value indicating whether the mapped table can be configured as memory-optimized.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="memoryOptimized"> A value indicating whether the table is memory-optimized. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the mapped table can be configured as memory-optimized. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeBuilderExtensions.ForSqlServerIsMemoryOptimized(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder,System.Boolean)">
            <summary>
                Configures the table that the entity maps to when targeting SQL Server as memory-optimized.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="memoryOptimized"> A value indicating whether the table is memory-optimized. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeBuilderExtensions.ForSqlServerIsMemoryOptimized``1(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder{``0},System.Boolean)">
            <summary>
                Configures the table that the entity maps to when targeting SQL Server as memory-optimized.
            </summary>
            <typeparam name="TEntity"> The entity type being configured. </typeparam>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="memoryOptimized"> A value indicating whether the table is memory-optimized. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeBuilderExtensions.ForSqlServerIsMemoryOptimized(Microsoft.EntityFrameworkCore.Metadata.Builders.OwnedNavigationBuilder,System.Boolean)">
            <summary>
                Configures the table that the entity maps to when targeting SQL Server as memory-optimized.
            </summary>
            <param name="collectionOwnershipBuilder"> The builder for the entity type being configured. </param>
            <param name="memoryOptimized"> A value indicating whether the table is memory-optimized. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeBuilderExtensions.ForSqlServerIsMemoryOptimized``2(Microsoft.EntityFrameworkCore.Metadata.Builders.OwnedNavigationBuilder{``0,``1},System.Boolean)">
            <summary>
                Configures the table that the entity maps to when targeting SQL Server as memory-optimized.
            </summary>
            <typeparam name="TEntity"> The entity type being configured. </typeparam>
            <typeparam name="TRelatedEntity"> The entity type that this relationship targets. </typeparam>
            <param name="collectionOwnershipBuilder"> The builder for the entity type being configured. </param>
            <param name="memoryOptimized"> A value indicating whether the table is memory-optimized. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeBuilderExtensions.ForSqlServerIsMemoryOptimized(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionEntityTypeBuilder,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Configures the table that the entity maps to when targeting SQL Server as memory-optimized.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type being configured. </param>
            <param name="memoryOptimized"> A value indicating whether the table is memory-optimized. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerEntityTypeExtensions">
            <summary>
                Extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.IEntityType" /> for SQL Server-specific metadata.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeExtensions.IsMemoryOptimized(Microsoft.EntityFrameworkCore.Metadata.IEntityType)">
            <summary>
                Returns a value indicating whether the entity type is mapped to a memory-optimized table.
            </summary>
            <param name="entityType"> The entity type. </param>
            <returns> <c>true</c> if the entity type is mapped to a memory-optimized table. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeExtensions.SetIsMemoryOptimized(Microsoft.EntityFrameworkCore.Metadata.IMutableEntityType,System.Boolean)">
            <summary>
                Sets a value indicating whether the entity type is mapped to a memory-optimized table.
            </summary>
            <param name="entityType"> The entity type. </param>
            <param name="memoryOptimized"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeExtensions.SetIsMemoryOptimized(Microsoft.EntityFrameworkCore.Metadata.IConventionEntityType,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Sets a value indicating whether the entity type is mapped to a memory-optimized table.
            </summary>
            <param name="entityType"> The entity type. </param>
            <param name="memoryOptimized"> The value to set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerEntityTypeExtensions.GetIsMemoryOptimizedConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionEntityType)">
            <summary>
                Gets the configuration source for the memory-optimized setting.
            </summary>
            <param name="entityType"> The entity type. </param>
            <returns> The configuration source for the memory-optimized setting. </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions">
            <summary>
                SQL Server specific extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder" />.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.IsClustered(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder,System.Boolean)">
            <summary>
                Configures whether the index is clustered when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="clustered"> A value indicating whether the index is clustered. </param>
            <returns> A builder to further configure the index. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.IsClustered``1(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder{``0},System.Boolean)">
            <summary>
                Configures whether the index is clustered when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="clustered"> A value indicating whether the index is clustered. </param>
            <returns> A builder to further configure the index. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.IsClustered(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Configures whether the index is clustered when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="clustered"> A value indicating whether the index is clustered. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.CanSetIsClustered(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Returns a value indicating whether the index can be configured as clustered.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="clustered"> A value indicating whether the index is clustered. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the index can be configured as clustered. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.IncludeProperties(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder,System.String[])">
            <summary>
                Configures index include properties when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="propertyNames"> An array of property names to be used in 'include' clause. </param>
            <returns> A builder to further configure the index. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.IncludeProperties``1(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
                Configures index include properties when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="includeExpression">
                <para>
                    A lambda expression representing the property(s) to be included in the 'include' clause
                    (<c>blog => blog.Url</c>).
                </para>
                <para>
                    If multiple properties are to be included then specify an anonymous type including the
                    properties (<c>post => new { post.Title, post.BlogId }</c>).
                </para>
            </param>
            <returns> A builder to further configure the index. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.IncludeProperties(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder,System.Collections.Generic.IReadOnlyList{System.String},System.Boolean)">
            <summary>
                Configures index include properties when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="propertyNames"> An array of property names to be used in 'include' clause. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.CanSetIncludeProperties(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder,System.Collections.Generic.IReadOnlyList{System.String},System.Boolean)">
            <summary>
                Returns a value indicating whether the given include properties can be set.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="propertyNames"> An array of property names to be used in 'include' clause. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the given include properties can be set. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.IsCreatedOnline(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder,System.Boolean)">
            <summary>
                Configures whether the index is created with online option when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="createdOnline"> A value indicating whether the index is created with online option. </param>
            <returns> A builder to further configure the index. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.IsCreatedOnline``1(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder{``0},System.Boolean)">
            <summary>
                Configures whether the index is created with online option when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="createdOnline"> A value indicating whether the index is created with online option. </param>
            <returns> A builder to further configure the index. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.IsCreatedOnline(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Configures whether the index is created with online option when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="createdOnline"> A value indicating whether the index is created with online option. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.CanSetIsCreatedOnline(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Returns a value indicating whether the index can be configured with online option when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="createdOnline"> A value indicating whether the index is created with online option. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
            <returns> <c>true</c> if the index can be configured with online option when targeting SQL Server. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.ForSqlServerIsClustered(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder,System.Boolean)">
            <summary>
                Configures whether the index is clustered when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="clustered"> A value indicating whether the index is clustered. </param>
            <returns> A builder to further configure the index. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.ForSqlServerIsClustered``1(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder{``0},System.Boolean)">
            <summary>
                Configures whether the index is clustered when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="clustered"> A value indicating whether the index is clustered. </param>
            <returns> A builder to further configure the index. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.ForSqlServerIsClustered(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Configures whether the index is clustered when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="clustered"> A value indicating whether the index is clustered. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.ForSqlServerInclude(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder,System.String[])">
            <summary>
                Configures index include properties when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="propertyNames"> An array of property names to be used in 'include' clause. </param>
            <returns> A builder to further configure the index. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.ForSqlServerInclude``1(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
                Configures index include properties when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="includeExpression">
                <para>
                    A lambda expression representing the property(s) to be included in the 'include' clause
                    (<c>blog => blog.Url</c>).
                </para>
                <para>
                    If multiple properties are to be included then specify an anonymous type including the
                    properties (<c>post => new { post.Title, post.BlogId }</c>).
                </para>
            </param>
            <returns> A builder to further configure the index. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.ForSqlServerInclude(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder,System.Collections.Generic.IReadOnlyList{System.String},System.Boolean)">
            <summary>
                Configures index include properties when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="propertyNames"> An array of property names to be used in 'include' clause. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.ForSqlServerIsCreatedOnline(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder,System.Boolean)">
            <summary>
                Configures whether the index is created with online option when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="createdOnline"> A value indicating whether the index is created with online option. </param>
            <returns> A builder to further configure the index. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.ForSqlServerIsCreatedOnline``1(Microsoft.EntityFrameworkCore.Metadata.Builders.IndexBuilder{``0},System.Boolean)">
            <summary>
                Configures whether the index is created with online option when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="createdOnline"> A value indicating whether the index is created with online option. </param>
            <returns> A builder to further configure the index. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexBuilderExtensions.ForSqlServerIsCreatedOnline(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Configures whether the index is created with online option when targeting SQL Server.
            </summary>
            <param name="indexBuilder"> The builder for the index being configured. </param>
            <param name="createdOnline"> A value indicating whether the index is created with online option. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerIndexExtensions">
            <summary>
                Extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.IIndex" /> for SQL Server-specific metadata.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexExtensions.IsClustered(Microsoft.EntityFrameworkCore.Metadata.IIndex)">
            <summary>
                Returns a value indicating whether the index is clustered.
            </summary>
            <param name="index"> The index. </param>
            <returns> <c>true</c> if the index is clustered. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexExtensions.SetIsClustered(Microsoft.EntityFrameworkCore.Metadata.IMutableIndex,System.Nullable{System.Boolean})">
            <summary>
                Sets a value indicating whether the index is clustered.
            </summary>
            <param name="value"> The value to set. </param>
            <param name="index"> The index. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexExtensions.SetIsClustered(Microsoft.EntityFrameworkCore.Metadata.IConventionIndex,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Sets a value indicating whether the index is clustered.
            </summary>
            <param name="value"> The value to set. </param>
            <param name="index"> The index. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexExtensions.GetIsClusteredConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionIndex)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for whether the index is clustered.
            </summary>
            <param name="property"> The property. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for whether the index is clustered. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexExtensions.GetIncludeProperties(Microsoft.EntityFrameworkCore.Metadata.IIndex)">
            <summary>
                Returns included property names, or <c>null</c> if they have not been specified.
            </summary>
            <param name="index"> The index. </param>
            <returns> The included property names, or <c>null</c> if they have not been specified. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexExtensions.SetIncludeProperties(Microsoft.EntityFrameworkCore.Metadata.IMutableIndex,System.Collections.Generic.IReadOnlyList{System.String})">
            <summary>
                Sets included property names.
            </summary>
            <param name="index"> The index. </param>
            <param name="properties"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexExtensions.SetIncludeProperties(Microsoft.EntityFrameworkCore.Metadata.IConventionIndex,System.Collections.Generic.IReadOnlyList{System.String},System.Boolean)">
            <summary>
                Sets included property names.
            </summary>
            <param name="index"> The index. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <param name="properties"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexExtensions.GetIncludePropertiesConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionIndex)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the included property names.
            </summary>
            <param name="index"> The index. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the included property names. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexExtensions.IsCreatedOnline(Microsoft.EntityFrameworkCore.Metadata.IIndex)">
            <summary>
                Returns a value indicating whether the index is online.
            </summary>
            <param name="index"> The index. </param>
            <returns> <c>true</c> if the index is online. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexExtensions.SetIsCreatedOnline(Microsoft.EntityFrameworkCore.Metadata.IMutableIndex,System.Nullable{System.Boolean})">
            <summary>
                Sets a value indicating whether the index is online.
            </summary>
            <param name="index"> The index. </param>
            <param name="createdOnline"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexExtensions.SetIsCreatedOnline(Microsoft.EntityFrameworkCore.Metadata.IConventionIndex,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Sets a value indicating whether the index is online.
            </summary>
            <param name="index"> The index. </param>
            <param name="createdOnline"> The value to set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerIndexExtensions.GetIsCreatedOnlineConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionIndex)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for whether the index is online.
            </summary>
            <param name="index"> The index. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for whether the index is online. </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerKeyBuilderExtensions">
            <summary>
                SQL Server specific extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Builders.KeyBuilder" />.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerKeyBuilderExtensions.IsClustered(Microsoft.EntityFrameworkCore.Metadata.Builders.KeyBuilder,System.Boolean)">
            <summary>
                Configures whether the key is clustered when targeting SQL Server.
            </summary>
            <param name="keyBuilder"> The builder for the key being configured. </param>
            <param name="clustered"> A value indicating whether the key is clustered. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerKeyBuilderExtensions.IsClustered(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionKeyBuilder,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Configures whether the key is clustered when targeting SQL Server.
            </summary>
            <param name="keyBuilder"> The builder for the key being configured. </param>
            <param name="clustered"> A value indicating whether the key is clustered. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerKeyBuilderExtensions.CanSetIsClustered(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionKeyBuilder,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Returns a value indicating whether the key can be configured as clustered.
            </summary>
            <param name="keyBuilder"> The builder for the key being configured. </param>
            <param name="clustered"> A value indicating whether the key is clustered. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the key can be configured as clustered. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerKeyBuilderExtensions.ForSqlServerIsClustered(Microsoft.EntityFrameworkCore.Metadata.Builders.KeyBuilder,System.Boolean)">
            <summary>
                Configures whether the key is clustered when targeting SQL Server.
            </summary>
            <param name="keyBuilder"> The builder for the key being configured. </param>
            <param name="clustered"> A value indicating whether the key is clustered. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerKeyBuilderExtensions.ForSqlServerIsClustered(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionKeyBuilder,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Configures whether the key is clustered when targeting SQL Server.
            </summary>
            <param name="keyBuilder"> The builder for the key being configured. </param>
            <param name="clustered"> A value indicating whether the key is clustered. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerKeyExtensions">
            <summary>
                Extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.IKey" /> for SQL Server-specific metadata.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerKeyExtensions.IsClustered(Microsoft.EntityFrameworkCore.Metadata.IKey)">
            <summary>
                Returns a value indicating whether the key is clustered.
            </summary>
            <param name="key"> The key. </param>
            <returns> <c>true</c> if the key is clustered. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerKeyExtensions.SetIsClustered(Microsoft.EntityFrameworkCore.Metadata.IMutableKey,System.Nullable{System.Boolean})">
            <summary>
                Sets a value indicating whether the key is clustered.
            </summary>
            <param name="key"> The key. </param>
            <param name="clustered"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerKeyExtensions.SetIsClustered(Microsoft.EntityFrameworkCore.Metadata.IConventionKey,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
                Sets a value indicating whether the key is clustered.
            </summary>
            <param name="key"> The key. </param>
            <param name="clustered"> The value to set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerKeyExtensions.GetIsClusteredConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionKey)">
            <summary>
                Gets the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for whether the key is clustered.
            </summary>
            <param name="key"> The key. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for whether the key is clustered. </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationBuilderExtensions">
            <summary>
                SQL Server specific extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder" />.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationBuilderExtensions.IsSqlServer(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <summary>
                <para>
                    Returns true if the database provider currently in use is the SQL Server provider.
                </para>
            </summary>
            <param name="migrationBuilder">
                The migrationBuilder from the parameters on <see cref="M:Microsoft.EntityFrameworkCore.Migrations.Migration.Up(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)" /> or
                <see cref="M:Microsoft.EntityFrameworkCore.Migrations.Migration.Down(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)" />.
            </param>
            <returns> True if SQL Server is being used; false otherwise. </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.Migrations.Operations.SqlServerCreateDatabaseOperation">
            <summary>
                A SQL Server-specific <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation" /> to create a database.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.Migrations.Operations.SqlServerCreateDatabaseOperation.Name">
            <summary>
                The name of the database.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.Migrations.Operations.SqlServerCreateDatabaseOperation.FileName">
            <summary>
                The filename to use for the database, or <c>null</c> to let SQL Server choose.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.Migrations.Operations.SqlServerDropDatabaseOperation">
            <summary>
                A SQL Server-specific <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation" /> to drop a database.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.Migrations.Operations.SqlServerDropDatabaseOperation.Name">
            <summary>
                The name of the database.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator">
            <summary>
                <para>
                    SQL Server-specific implementation of <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationsSqlGenerator" />.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />. This means that each
                    <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instance will use its own instance of this service.
                    The implementation may depend on other services registered with any lifetime.
                    The implementation does not need to be thread-safe.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.#ctor(Microsoft.EntityFrameworkCore.Migrations.MigrationsSqlGeneratorDependencies,Microsoft.EntityFrameworkCore.Migrations.IMigrationsAnnotationProvider)">
            <summary>
                Creates a new <see cref="T:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator" /> instance.
            </summary>
            <param name="dependencies"> Parameter object containing dependencies for this service. </param>
            <param name="migrationsAnnotations"> Provider-specific Migrations annotations to use. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(System.Collections.Generic.IReadOnlyList{Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation},Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
                Generates commands from a list of operations.
            </summary>
            <param name="operations"> The operations. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <returns> The list of commands to be executed or scripted. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                <para>
                    Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation" /> by making calls on the given
                    <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
                </para>
                <para>
                    This method uses a double-dispatch mechanism to call one of the 'Generate' methods that are
                    specific to a certain subtype of <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation" />. Typically database providers
                    will override these specific methods rather than this method. However, providers can override
                    this methods to handle provider-specific operations.
                </para>
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.AddColumnOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder,System.Boolean)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.AddColumnOperation" /> by making calls on the given
                <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
            <param name="terminate"> Indicates whether or not to terminate the command after generating SQL for the operation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.AddForeignKeyOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder,System.Boolean)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.AddForeignKeyOperation" /> by making calls on the given
                <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
            <param name="terminate"> Indicates whether or not to terminate the command after generating SQL for the operation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.AddPrimaryKeyOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder,System.Boolean)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.AddPrimaryKeyOperation" /> by making calls on the given
                <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
            <param name="terminate"> Indicates whether or not to terminate the command after generating SQL for the operation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.AlterColumnOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.AlterColumnOperation" />
                by making calls on the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.RenameIndexOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.RenameIndexOperation" />
                by making calls on the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.RenameSequenceOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.RenameSequenceOperation" />
                by making calls on the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.RestartSequenceOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.RestartSequenceOperation" /> by making calls on the given
                <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />, and then terminates the final command.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.CreateTableOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder,System.Boolean)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.CreateTableOperation" /> by making calls on the given
                <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
            <param name="terminate"> Indicates whether or not to terminate the command after generating SQL for the operation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.RenameTableOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.RenameTableOperation" />
                by making calls on the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.DropTableOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder,System.Boolean)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.DropTableOperation" /> by making calls on the given
                <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
            <param name="terminate"> Indicates whether or not to terminate the command after generating SQL for the operation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.CreateIndexOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder,System.Boolean)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.CreateIndexOperation" /> by making calls on the given
                <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
            <param name="terminate"> Indicates whether or not to terminate the command after generating SQL for the operation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.DropPrimaryKeyOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder,System.Boolean)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.DropPrimaryKeyOperation" /> by making calls on the given
                <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
            <param name="terminate"> Indicates whether or not to terminate the command after generating SQL for the operation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.EnsureSchemaOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.EnsureSchemaOperation" />
                by making calls on the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.CreateSequenceOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.CreateSequenceOperation" /> by making calls on the given
                <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />, and then terminates the final command.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.SqlServerCreateDatabaseOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.SqlServerCreateDatabaseOperation" />
                by making calls on the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.SqlServerDropDatabaseOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.SqlServerDropDatabaseOperation" />
                by making calls on the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.AlterDatabaseOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.AlterDatabaseOperation" />
                by making calls on the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.AlterTableOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.AlterTableOperation" />
                by making calls on the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.DropForeignKeyOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder,System.Boolean)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.DropForeignKeyOperation" /> by making calls on the given
                <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
            <param name="terminate"> Indicates whether or not to terminate the command after generating SQL for the operation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.DropIndexOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder,System.Boolean)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.DropIndexOperation" />
                by making calls on the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
            <param name="terminate"> Indicates whether or not to terminate the command after generating SQL for the operation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.DropColumnOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder,System.Boolean)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.DropColumnOperation" /> by making calls on the given
                <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
            <param name="terminate"> Indicates whether or not to terminate the command after generating SQL for the operation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.RenameColumnOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.RenameColumnOperation" />
                by making calls on the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.SqlOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.SqlOperation" /> by making calls on the given
                <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />, and then terminates the final command.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Generate(Microsoft.EntityFrameworkCore.Migrations.Operations.InsertDataOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder,System.Boolean)">
            <summary>
                Builds commands for the given <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.InsertDataOperation" /> by making calls on the given
                <see cref="T:Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
            <param name="terminate"> Indicates whether or not to terminate the command after generating SQL for the operation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.SequenceOptions(System.String,System.String,Microsoft.EntityFrameworkCore.Migrations.Operations.SequenceOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Generates a SQL fragment configuring a sequence with the given options.
            </summary>
            <param name="schema"> The schema that contains the sequence, or <c>null</c> to use the default schema. </param>
            <param name="name"> The sequence name. </param>
            <param name="operation"> The sequence options. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to add the SQL fragment. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.ColumnDefinition(System.String,System.String,System.String,Microsoft.EntityFrameworkCore.Migrations.Operations.ColumnOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Generates a SQL fragment for a column definition for the given column metadata.
            </summary>
            <param name="schema"> The schema that contains the table, or <c>null</c> to use the default schema. </param>
            <param name="table"> The table that contains the column. </param>
            <param name="name"> The column name. </param>
            <param name="operation"> The column metadata. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to add the SQL fragment. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.ComputedColumnDefinition(System.String,System.String,System.String,Microsoft.EntityFrameworkCore.Migrations.Operations.ColumnOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Generates a SQL fragment for a computed column definition for the given column metadata.
            </summary>
            <param name="schema"> The schema that contains the table, or <c>null</c> to use the default schema. </param>
            <param name="table"> The table that contains the column. </param>
            <param name="name"> The column name. </param>
            <param name="operation"> The column metadata. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to add the SQL fragment. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Rename(System.String,System.String,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Generates a rename.
            </summary>
            <param name="name"> The old name. </param>
            <param name="newName"> The new name. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Rename(System.String,System.String,System.String,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Generates a rename.
            </summary>
            <param name="name"> The old name. </param>
            <param name="newName"> The new name. </param>
            <param name="type"> If not <c>null</c>, then appends literal for type of object being renamed (e.g. column or index.) </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.Transfer(System.String,System.String,System.String,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Generates a transfer from one schema to another..
            </summary>
            <param name="newSchema"> The schema to transfer to. </param>
            <param name="schema"> The schema to transfer from. </param>
            <param name="name"> The name of the item to transfer. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.IndexTraits(Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Generates a SQL fragment for traits of an index from a <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.CreateIndexOperation" />,
                <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.AddPrimaryKeyOperation" />, or <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.AddUniqueConstraintOperation" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to add the SQL fragment. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.IndexOptions(Microsoft.EntityFrameworkCore.Migrations.Operations.CreateIndexOperation,Microsoft.EntityFrameworkCore.Metadata.IModel,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Generates a SQL fragment for extras (filter, included columns, options) of an index from a <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.CreateIndexOperation" />.
            </summary>
            <param name="operation"> The operation. </param>
            <param name="model"> The target model which may be <c>null</c> if the operations exist without a model. </param>
            <param name="builder"> The command builder to use to add the SQL fragment. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.ForeignKeyAction(Microsoft.EntityFrameworkCore.Migrations.ReferentialAction,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Generates a SQL fragment for the given referential action.
            </summary>
            <param name="referentialAction"> The referential action. </param>
            <param name="builder"> The command builder to use to add the SQL fragment. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.DropDefaultConstraint(System.String,System.String,System.String,Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Generates a SQL fragment to drop default constraints for a column.
            </summary>
            <param name="schema"> The schema that contains the table. </param>
            <param name="tableName"> The table that contains the column.</param>
            <param name="columnName"> The column. </param>
            <param name="builder"> The command builder to use to add the SQL fragment. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.GetIndexesToRebuild(Microsoft.EntityFrameworkCore.Metadata.IProperty,Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation)">
            <summary>
                Gets the list of indexes that need to be rebuilt when the given property is changing.
            </summary>
            <param name="property"> The property. </param>
            <param name="currentOperation"> The operation which may require a rebuild. </param>
            <returns> The list of indexes affected. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.DropIndexes(System.Collections.Generic.IEnumerable{Microsoft.EntityFrameworkCore.Metadata.IIndex},Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Generates SQL to drop the given indexes.
            </summary>
            <param name="indexes"> The indexes to drop. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.CreateIndexes(System.Collections.Generic.IEnumerable{Microsoft.EntityFrameworkCore.Metadata.IIndex},Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder)">
            <summary>
                Generates SQL to create the given indexes.
            </summary>
            <param name="indexes"> The indexes to create. </param>
            <param name="builder"> The command builder to use to build the commands. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.AddDescription(Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder,System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
                <para>
                    Generates add commands for descriptions on tables and columns.
                </para>
            </summary>
            <param name="builder"> The command builder to use to build the commands. </param>
            <param name="description"> The new description to be applied. </param>
            <param name="schema"> The schema of the table. </param>
            <param name="table"> The name of the table. </param>
            <param name="column"> The name of the column. </param>
            <param name="omitSchemaVariable">
                Indicates whether the @defaultSchema variable declaraion should be omitted.
            </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.DropDescription(Microsoft.EntityFrameworkCore.Migrations.MigrationCommandListBuilder,System.String,System.String,System.String,System.Boolean)">
            <summary>
                <para>
                    Generates drop commands for descriptions on tables and columns.
                </para>
            </summary>
            <param name="builder"> The command builder to use to build the commands. </param>
            <param name="schema"> The schema of the table. </param>
            <param name="table"> The name of the table. </param>
            <param name="column"> The name of the column. </param>
            <param name="omitSchemaVariable">
                Indicates whether the @defaultSchema variable declaraion should be omitted.
            </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Migrations.SqlServerMigrationsSqlGenerator.UseLegacyIndexFilters(Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
                Checks whether or not <see cref="T:Microsoft.EntityFrameworkCore.Migrations.Operations.CreateIndexOperation" /> should have a filter generated for it by
                Migrations.
            </summary>
            <param name="model"> The target model. </param>
            <returns> True if a filter should be generated. </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions">
            <summary>
                SQL Server specific extension methods for <see cref="T:Microsoft.EntityFrameworkCore.ModelBuilder" />.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.UseHiLo(Microsoft.EntityFrameworkCore.ModelBuilder,System.String,System.String)">
            <summary>
                Configures the model to use a sequence-based hi-lo pattern to generate values for key properties
                marked as <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />, when targeting SQL Server.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="name"> The name of the sequence. </param>
            <param name="schema">The schema of the sequence. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.HasHiLoSequence(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.String,System.String,System.Boolean)">
            <summary>
                Configures the database sequence used for the hi-lo pattern to generate values for key properties
                marked as <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />, when targeting SQL Server.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="name"> The name of the sequence. </param>
            <param name="schema">The schema of the sequence. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> A builder to further configure the sequence. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.CanSetHiLoSequence(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.String,System.String,System.Boolean)">
            <summary>
                Returns a value indicating whether the given name and schema can be set for the hi-lo sequence.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="name"> The name of the sequence. </param>
            <param name="schema">The schema of the sequence. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the given name and schema can be set for the hi-lo sequence. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.UseIdentityColumns(Microsoft.EntityFrameworkCore.ModelBuilder,System.Int32,System.Int32)">
            <summary>
                Configures the model to use the SQL Server IDENTITY feature to generate values for key properties
                marked as <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />, when targeting SQL Server. This is the default
                behavior when targeting SQL Server.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="seed"> The value that is used for the very first row loaded into the table. </param>
            <param name="increment"> The incremental value that is added to the identity value of the previous row that was loaded. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.HasIdentityColumnSeed(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Configures the default seed for SQL Server IDENTITY.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="seed"> The value that is used for the very first row loaded into the table. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.CanSetIdentityColumnSeed(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Returns a value indicating whether the given value can be set as the default seed for SQL Server IDENTITY.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="seed"> The value that is used for the very first row loaded into the table. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the given value can be set as the seed for SQL Server IDENTITY. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.HasIdentityColumnIncrement(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Configures the default increment for SQL Server IDENTITY.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="increment"> The incremental value that is added to the identity value of the previous row that was loaded. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.CanSetIdentityColumnIncrement(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Returns a value indicating whether the given value can be set as the default increment for SQL Server IDENTITY.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="increment"> The incremental value that is added to the identity value of the previous row that was loaded. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the given value can be set as the default increment for SQL Server IDENTITY. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.HasValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.Nullable{Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy},System.Boolean)">
            <summary>
                Configures the default value generation strategy for key properties marked as <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />,
                when targeting SQL Server.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="valueGenerationStrategy"> The value generation strategy. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.CanSetValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.Nullable{Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy},System.Boolean)">
            <summary>
                Returns a value indicating whether the given value can be set as the default value generation strategy.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="valueGenerationStrategy"> The value generation strategy. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the given value can be set as the default value generation strategy. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.HasDatabaseMaxSize(Microsoft.EntityFrameworkCore.ModelBuilder,System.String)">
            <summary>
                <para>
                    Configures the maximum size for Azure SQL Database.
                </para>
                <para>
                    Units must be included, e.g. "100 MB". See Azure SQL Database documentation for all supported values.
                </para>
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="maxSize"> The maximum size of the database. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.HasDatabaseMaxSize(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.String,System.Boolean)">
            <summary>
                <para>
                    Attempts to configure the maximum size for Azure SQL Database.
                </para>
                <para>
                    Units must be included, e.g. "100 MB". See Azure SQL Database documentation for all supported values.
                </para>
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="maxSize"> The maximum size of the database. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.CanSetDatabaseMaxSize(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.String,System.Boolean)">
            <summary>
                Returns a value indicating whether the given value can be set as the maximum size of the database.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="maxSize"> The maximum size of the database. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the given value can be set as the maximum size of the database. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.HasServiceTier(Microsoft.EntityFrameworkCore.ModelBuilder,System.String)">
            <summary>
                <para>
                    Configures the service tier (EDITION) for Azure SQL Database as a string literal.
                </para>
                <para>
                    See Azure SQL Database documentation for supported values.
                </para>
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="serviceTier"> The service tier of the database as a string literal. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.HasServiceTierSql(Microsoft.EntityFrameworkCore.ModelBuilder,System.String)">
            <summary>
                <para>
                    Configures the service tier (EDITION) for Azure SQL Database as a SQL expression.
                </para>
                <para>
                    See Azure SQL Database documentation for supported values.
                </para>
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="serviceTier"> The expression for the service tier of the database. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.HasServiceTierSql(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.String,System.Boolean)">
            <summary>
                <para>
                    Attempts to configure the service tier (EDITION) for Azure SQL Database.
                </para>
                <para>
                    See Azure SQL Database documentation for supported values.
                </para>
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="serviceTier"> The expression for the service tier of the database. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.CanSetServiceTierSql(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.String,System.Boolean)">
            <summary>
                Returns a value indicating whether the given value can be set as the service tier of the database.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="serviceTier"> The expression for the service tier of the database. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the given value can be set as the service tier of the database. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.HasPerformanceLevel(Microsoft.EntityFrameworkCore.ModelBuilder,System.String)">
            <summary>
                <para>
                    Configures the performance level (SERVICE_OBJECTIVE) for Azure SQL Database as a string literal.
                </para>
                <para>
                    See Azure SQL Database documentation for supported values.
                </para>
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="performanceLevel"> The performance level of the database as a string literal. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.HasPerformanceLevelSql(Microsoft.EntityFrameworkCore.ModelBuilder,System.String)">
            <summary>
                <para>
                    Configures the performance level (SERVICE_OBJECTIVE) for Azure SQL Database as a SQL expression.
                </para>
                <para>
                    See Azure SQL Database documentation for supported values.
                </para>
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="performanceLevel"> The expression for the performance level of the database. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.HasPerformanceLevelSql(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.String,System.Boolean)">
            <summary>
                <para>
                    Attempts to configure the performance level (SERVICE_OBJECTIVE) for Azure SQL Database.
                </para>
                <para>
                    See Azure SQL Database documentation for supported values.
                </para>
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="performanceLevel"> The expression for the performance level of the database. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.CanSetPerformanceLevelSql(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.String,System.Boolean)">
            <summary>
                Returns a value indicating whether the given value can be set as the performance level of the database.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="performanceLevel"> The performance level of the database expression. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the given value can be set as the performance level of the database. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.ForSqlServerUseSequenceHiLo(Microsoft.EntityFrameworkCore.ModelBuilder,System.String,System.String)">
            <summary>
                Configures the model to use a sequence-based hi-lo pattern to generate values for key properties
                marked as <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />, when targeting SQL Server.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="name"> The name of the sequence. </param>
            <param name="schema">The schema of the sequence. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.ForSqlServerHasHiLoSequence(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.String,System.String,System.Boolean)">
            <summary>
                Configures the database sequence used for the hi-lo pattern to generate values for key properties
                marked as <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />, when targeting SQL Server.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="name"> The name of the sequence. </param>
            <param name="schema">The schema of the sequence. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> A builder to further configure the sequence. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.ForSqlServerUseIdentityColumns(Microsoft.EntityFrameworkCore.ModelBuilder,System.Int32,System.Int32)">
            <summary>
                Configures the model to use the SQL Server IDENTITY feature to generate values for key properties
                marked as <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />, when targeting SQL Server. This is the default
                behavior when targeting SQL Server.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="seed"> The value that is used for the very first row loaded into the table. </param>
            <param name="increment"> The incremental value that is added to the identity value of the previous row that was loaded. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.ForSqlServerHasIdentitySeed(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Configures the default seed for SQL Server IDENTITY.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="seed"> The value that is used for the very first row loaded into the table. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.ForSqlServerHasIdentityIncrement(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Configures the default increment for SQL Server IDENTITY.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="increment"> The incremental value that is added to the identity value of the previous row that was loaded. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelBuilderExtensions.ForSqlServerHasValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,System.Nullable{Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy},System.Boolean)">
            <summary>
                Configures the default value generation strategy for key properties marked as <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />,
                when targeting SQL Server.
            </summary>
            <param name="modelBuilder"> The model builder. </param>
            <param name="valueGenerationStrategy"> The value generation strategy. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerModelExtensions">
            <summary>
                Extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.IModel" /> for SQL Server-specific metadata.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.DefaultHiLoSequenceName">
            <summary>
                The default name for the hi-lo sequence.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetHiLoSequenceName(Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
                Returns the name to use for the default hi-lo sequence.
            </summary>
            <param name="model"> The model. </param>
            <returns> The name to use for the default hi-lo sequence. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetHiLoSequenceName(Microsoft.EntityFrameworkCore.Metadata.IMutableModel,System.String)">
            <summary>
                Sets the name to use for the default hi-lo sequence.
            </summary>
            <param name="model"> The model. </param>
            <param name="name"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetHiLoSequenceName(Microsoft.EntityFrameworkCore.Metadata.IConventionModel,System.String,System.Boolean)">
            <summary>
                Sets the name to use for the default hi-lo sequence.
            </summary>
            <param name="model"> The model. </param>
            <param name="name"> The value to set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetHiLoSequenceNameConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionModel)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the default hi-lo sequence name.
            </summary>
            <param name="model"> The model. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the default hi-lo sequence name. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetHiLoSequenceSchema(Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
                Returns the schema to use for the default hi-lo sequence.
                <see cref="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.UseHiLo(Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder,System.String,System.String)" />
            </summary>
            <param name="model"> The model. </param>
            <returns> The schema to use for the default hi-lo sequence. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetHiLoSequenceSchema(Microsoft.EntityFrameworkCore.Metadata.IMutableModel,System.String)">
            <summary>
                Sets the schema to use for the default hi-lo sequence.
            </summary>
            <param name="model"> The model. </param>
            <param name="value"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetHiLoSequenceSchema(Microsoft.EntityFrameworkCore.Metadata.IConventionModel,System.String,System.Boolean)">
            <summary>
                Sets the schema to use for the default hi-lo sequence.
            </summary>
            <param name="model"> The model. </param>
            <param name="value"> The value to set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetHiLoSequenceSchemaConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionModel)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the default hi-lo sequence schema.
            </summary>
            <param name="model"> The model. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the default hi-lo sequence schema. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetIdentitySeed(Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
                Returns the default identity seed.
            </summary>
            <param name="model"> The model. </param>
            <returns> The default identity seed. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetIdentitySeed(Microsoft.EntityFrameworkCore.Metadata.IMutableModel,System.Nullable{System.Int32})">
            <summary>
                Sets the default identity seed.
            </summary>
            <param name="model"> The model. </param>
            <param name="seed"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetIdentitySeed(Microsoft.EntityFrameworkCore.Metadata.IConventionModel,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Sets the default identity seed.
            </summary>
            <param name="model"> The model. </param>
            <param name="seed"> The value to set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetIdentitySeedConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionModel)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the default schema.
            </summary>
            <param name="model"> The model. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the default schema. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetIdentityIncrement(Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
                Returns the default identity increment.
            </summary>
            <param name="model"> The model. </param>
            <returns> The default identity increment. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetIdentityIncrement(Microsoft.EntityFrameworkCore.Metadata.IMutableModel,System.Nullable{System.Int32})">
            <summary>
                Sets the default identity increment.
            </summary>
            <param name="model"> The model. </param>
            <param name="increment"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetIdentityIncrement(Microsoft.EntityFrameworkCore.Metadata.IConventionModel,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Sets the default identity increment.
            </summary>
            <param name="model"> The model. </param>
            <param name="increment"> The value to set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetIdentityIncrementConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionModel)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the default identity increment.
            </summary>
            <param name="model"> The model. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the default identity increment. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" /> to use for properties
                of keys in the model, unless the property has a strategy explicitly set.
            </summary>
            <param name="model"> The model. </param>
            <returns> The default <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" />. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.IMutableModel,System.Nullable{Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy})">
            <summary>
                Sets the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" /> to use for properties
                of keys in the model that don't have a strategy explicitly set.
            </summary>
            <param name="model"> The model. </param>
            <param name="value"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.IConventionModel,System.Nullable{Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy},System.Boolean)">
            <summary>
                Sets the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" /> to use for properties
                of keys in the model that don't have a strategy explicitly set.
            </summary>
            <param name="model"> The model. </param>
            <param name="value"> The value to set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetValueGenerationStrategyConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionModel)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the default <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" />.
            </summary>
            <param name="model"> The model. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the default <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" />. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetDatabaseMaxSize(Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
                Returns the maximum size of the database.
            </summary>
            <param name="model"> The model. </param>
            <returns> The maximum size of the database. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetDatabaseMaxSize(Microsoft.EntityFrameworkCore.Metadata.IMutableModel,System.String)">
            <summary>
                Sets the maximum size of the database.
            </summary>
            <param name="model"> The model. </param>
            <param name="value"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetDatabaseMaxSize(Microsoft.EntityFrameworkCore.Metadata.IConventionModel,System.String,System.Boolean)">
            <summary>
                Sets the maximum size of the database.
            </summary>
            <param name="model"> The model. </param>
            <param name="value"> The value to set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetDatabaseMaxSizeConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionModel)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the maximum size of the database.
            </summary>
            <param name="model"> The model. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the maximum size of the database. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetServiceTierSql(Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
                Returns the service tier of the database.
            </summary>
            <param name="model"> The model. </param>
            <returns> The service tier of the database. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetServiceTierSql(Microsoft.EntityFrameworkCore.Metadata.IMutableModel,System.String)">
            <summary>
                Sets the service tier of the database.
            </summary>
            <param name="model"> The model. </param>
            <param name="value"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetServiceTierSql(Microsoft.EntityFrameworkCore.Metadata.IConventionModel,System.String,System.Boolean)">
            <summary>
                Sets the service tier of the database.
            </summary>
            <param name="model"> The model. </param>
            <param name="value"> The value to set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetServiceTierSqlConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionModel)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the service tier of the database.
            </summary>
            <param name="model"> The model. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the service tier of the database. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetPerformanceLevelSql(Microsoft.EntityFrameworkCore.Metadata.IModel)">
            <summary>
                Returns the performance level of the database.
            </summary>
            <param name="model"> The model. </param>
            <returns> The performance level of the database. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetPerformanceLevelSql(Microsoft.EntityFrameworkCore.Metadata.IMutableModel,System.String)">
            <summary>
                Sets the performance level of the database.
            </summary>
            <param name="model"> The model. </param>
            <param name="value"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.SetPerformanceLevelSql(Microsoft.EntityFrameworkCore.Metadata.IConventionModel,System.String,System.Boolean)">
            <summary>
                Sets the performance level of the database.
            </summary>
            <param name="model"> The model. </param>
            <param name="value"> The value to set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerModelExtensions.GetPerformanceLevelSqlConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionModel)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the performance level of the database.
            </summary>
            <param name="model"> The model. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the performance level of the database. </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions">
            <summary>
                SQL Server specific extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder" />.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.UseHiLo(Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder,System.String,System.String)">
            <summary>
                Configures the key property to use a sequence-based hi-lo pattern to generate values for new entities,
                when targeting SQL Server. This method sets the property to be <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="name"> The name of the sequence. </param>
            <param name="schema"> The schema of the sequence. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.UseHiLo``1(Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder{``0},System.String,System.String)">
            <summary>
                Configures the key property to use a sequence-based hi-lo pattern to generate values for new entities,
                when targeting SQL Server. This method sets the property to be <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />.
            </summary>
            <typeparam name="TProperty"> The type of the property being configured. </typeparam>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="name"> The name of the sequence. </param>
            <param name="schema"> The schema of the sequence. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.HasHiLoSequence(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.String,System.String,System.Boolean)">
            <summary>
                Configures the database sequence used for the hi-lo pattern to generate values for the key property,
                when targeting SQL Server.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="name"> The name of the sequence. </param>
            <param name="schema">The schema of the sequence. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> A builder to further configure the sequence. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.CanSetHiLoSequence(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.String,System.String,System.Boolean)">
            <summary>
                Returns a value indicating whether the given name and schema can be set for the hi-lo sequence.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="name"> The name of the sequence. </param>
            <param name="schema">The schema of the sequence. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the given name and schema can be set for the hi-lo sequence. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.UseIdentityColumn(Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder,System.Int32,System.Int32)">
            <summary>
                Configures the key property to use the SQL Server IDENTITY feature to generate values for new entities,
                when targeting SQL Server. This method sets the property to be <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="seed"> The value that is used for the very first row loaded into the table. </param>
            <param name="increment"> The incremental value that is added to the identity value of the previous row that was loaded. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.UseIdentityColumn``1(Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder{``0},System.Int32,System.Int32)">
            <summary>
                Configures the key property to use the SQL Server IDENTITY feature to generate values for new entities,
                when targeting SQL Server. This method sets the property to be <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />.
            </summary>
            <typeparam name="TProperty"> The type of the property being configured. </typeparam>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="seed"> The value that is used for the very first row loaded into the table. </param>
            <param name="increment"> The incremental value that is added to the identity value of the previous row that was loaded. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.HasIdentityColumnSeed(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Configures the seed for SQL Server IDENTITY.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="seed"> The value that is used for the very first row loaded into the table. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.CanSetIdentityColumnSeed(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Returns a value indicating whether the given value can be set as the seed for SQL Server IDENTITY.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="seed"> The value that is used for the very first row loaded into the table. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the given value can be set as the seed for SQL Server IDENTITY. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.HasIdentityColumnIncrement(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Configures the increment for SQL Server IDENTITY.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="increment"> The incremental value that is added to the identity value of the previous row that was loaded. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.CanSetIdentityColumnIncrement(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Returns a value indicating whether the given value can be set as the increment for SQL Server IDENTITY.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="increment"> The incremental value that is added to the identity value of the previous row that was loaded. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the given value can be set as the default increment for SQL Server IDENTITY. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.HasValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.Nullable{Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy},System.Boolean)">
            <summary>
                Configures the value generation strategy for the key property, when targeting SQL Server.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="valueGenerationStrategy"> The value generation strategy. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.CanSetValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.Nullable{Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy},System.Boolean)">
            <summary>
                Returns a value indicating whether the given value can be set as the value generation strategy.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="valueGenerationStrategy"> The value generation strategy. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> <c>true</c> if the given value can be set as the default value generation strategy. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.ForSqlServerUseSequenceHiLo(Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder,System.String,System.String)">
            <summary>
                Configures the key property to use a sequence-based hi-lo pattern to generate values for new entities,
                when targeting SQL Server. This method sets the property to be <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="name"> The name of the sequence. </param>
            <param name="schema"> The schema of the sequence. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.ForSqlServerUseSequenceHiLo``1(Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder{``0},System.String,System.String)">
            <summary>
                Configures the key property to use a sequence-based hi-lo pattern to generate values for new entities,
                when targeting SQL Server. This method sets the property to be <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />.
            </summary>
            <typeparam name="TProperty"> The type of the property being configured. </typeparam>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="name"> The name of the sequence. </param>
            <param name="schema"> The schema of the sequence. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.ForSqlServerHasHiLoSequence(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.String,System.String,System.Boolean)">
            <summary>
                Configures the database sequence used for the hi-lo pattern to generate values for the key property,
                when targeting SQL Server.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="name"> The name of the sequence. </param>
            <param name="schema">The schema of the sequence. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns> A builder to further configure the sequence. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.UseSqlServerIdentityColumn(Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder,System.Int32,System.Int32)">
            <summary>
                Configures the key property to use the SQL Server IDENTITY feature to generate values for new entities,
                when targeting SQL Server. This method sets the property to be <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="seed"> The value that is used for the very first row loaded into the table. </param>
            <param name="increment"> The incremental value that is added to the identity value of the previous row that was loaded. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.UseSqlServerIdentityColumn``1(Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder{``0},System.Int32,System.Int32)">
            <summary>
                Configures the key property to use the SQL Server IDENTITY feature to generate values for new entities,
                when targeting SQL Server. This method sets the property to be <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" />.
            </summary>
            <typeparam name="TProperty"> The type of the property being configured. </typeparam>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="seed"> The value that is used for the very first row loaded into the table. </param>
            <param name="increment"> The incremental value that is added to the identity value of the previous row that was loaded. </param>
            <returns> The same builder instance so that multiple calls can be chained. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.ForSqlServerHasIdentitySeed(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Configures the seed for SQL Server IDENTITY.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="seed"> The value that is used for the very first row loaded into the table. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.ForSqlServerHasIdentityIncrement(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Configures the increment for SQL Server IDENTITY.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="increment"> The incremental value that is added to the identity value of the previous row that was loaded. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyBuilderExtensions.ForSqlServerHasValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.Nullable{Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy},System.Boolean)">
            <summary>
                Configures the value generation strategy for the key property, when targeting SQL Server.
            </summary>
            <param name="propertyBuilder"> The builder for the property being configured. </param>
            <param name="valueGenerationStrategy"> The value generation strategy. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
            <returns>
                The same builder instance if the configuration was applied,
                <c>null</c> otherwise.
            </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions">
            <summary>
                Extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Metadata.IProperty" /> for SQL Server-specific metadata.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.GetHiLoSequenceName(Microsoft.EntityFrameworkCore.Metadata.IProperty)">
            <summary>
                Returns the name to use for the hi-lo sequence.
            </summary>
            <param name="property"> The property. </param>
            <returns> The name to use for the hi-lo sequence. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.SetHiLoSequenceName(Microsoft.EntityFrameworkCore.Metadata.IMutableProperty,System.String)">
            <summary>
                Sets the name to use for the hi-lo sequence.
            </summary>
            <param name="property"> The property. </param>
            <param name="name"> The sequence name to use. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.SetHiLoSequenceName(Microsoft.EntityFrameworkCore.Metadata.IConventionProperty,System.String,System.Boolean)">
            <summary>
                Sets the name to use for the hi-lo sequence.
            </summary>
            <param name="property"> The property. </param>
            <param name="name"> The sequence name to use. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.GetHiLoSequenceNameConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionProperty)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the hi-lo sequence name.
            </summary>
            <param name="property"> The property. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the hi-lo sequence name. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.GetHiLoSequenceSchema(Microsoft.EntityFrameworkCore.Metadata.IProperty)">
            <summary>
                Returns the schema to use for the hi-lo sequence.
            </summary>
            <param name="property"> The property. </param>
            <returns> The schema to use for the hi-lo sequence. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.SetHiLoSequenceSchema(Microsoft.EntityFrameworkCore.Metadata.IMutableProperty,System.String)">
            <summary>
                Sets the schema to use for the hi-lo sequence.
            </summary>
            <param name="property"> The property. </param>
            <param name="schema"> The schema to use. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.SetHiLoSequenceSchema(Microsoft.EntityFrameworkCore.Metadata.IConventionProperty,System.String,System.Boolean)">
            <summary>
                Sets the schema to use for the hi-lo sequence.
            </summary>
            <param name="property"> The property. </param>
            <param name="schema"> The schema to use. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.GetHiLoSequenceSchemaConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionProperty)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the hi-lo sequence schema.
            </summary>
            <param name="property"> The property. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the hi-lo sequence schema. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.FindHiLoSequence(Microsoft.EntityFrameworkCore.Metadata.IProperty)">
            <summary>
                Finds the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ISequence" /> in the model to use for the hi-lo pattern.
            </summary>
            <returns> The sequence to use, or <c>null</c> if no sequence exists in the model. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.GetIdentitySeed(Microsoft.EntityFrameworkCore.Metadata.IProperty)">
            <summary>
                Returns the identity seed.
            </summary>
            <param name="property"> The property. </param>
            <returns> The identity seed. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.SetIdentitySeed(Microsoft.EntityFrameworkCore.Metadata.IMutableProperty,System.Nullable{System.Int32})">
            <summary>
                Sets the identity seed.
            </summary>
            <param name="property"> The property. </param>
            <param name="seed"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.SetIdentitySeed(Microsoft.EntityFrameworkCore.Metadata.IConventionProperty,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Sets the identity seed.
            </summary>
            <param name="property"> The property. </param>
            <param name="seed"> The value to set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.GetIdentitySeedConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionProperty)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the identity seed.
            </summary>
            <param name="property"> The property. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the identity seed. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.GetIdentityIncrement(Microsoft.EntityFrameworkCore.Metadata.IProperty)">
            <summary>
                Returns the identity increment.
            </summary>
            <param name="property"> The property. </param>
            <returns> The identity increment. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.SetIdentityIncrement(Microsoft.EntityFrameworkCore.Metadata.IMutableProperty,System.Nullable{System.Int32})">
            <summary>
                Sets the identity increment.
            </summary>
            <param name="property"> The property. </param>
            <param name="increment"> The value to set. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.SetIdentityIncrement(Microsoft.EntityFrameworkCore.Metadata.IConventionProperty,System.Nullable{System.Int32},System.Boolean)">
            <summary>
                Sets the identity increment.
            </summary>
            <param name="property"> The property. </param>
            <param name="increment"> The value to set. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.GetIdentityIncrementConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionProperty)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the identity increment.
            </summary>
            <param name="property"> The property. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the identity increment. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.GetValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.IProperty)">
            <summary>
                <para>
                    Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" /> to use for the property.
                </para>
                <para>
                    If no strategy is set for the property, then the strategy to use will be taken from the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.IModel" />.
                </para>
            </summary>
            <returns> The strategy, or <see cref="F:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy.None" /> if none was set. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.SetValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.IMutableProperty,System.Nullable{Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy})">
            <summary>
                Sets the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" /> to use for the property.
            </summary>
            <param name="property"> The property. </param>
            <param name="value"> The strategy to use. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.SetValueGenerationStrategy(Microsoft.EntityFrameworkCore.Metadata.IConventionProperty,System.Nullable{Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy},System.Boolean)">
            <summary>
                Sets the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" /> to use for the property.
            </summary>
            <param name="property"> The property. </param>
            <param name="value"> The strategy to use. </param>
            <param name="fromDataAnnotation"> Indicates whether the configuration was specified using a data annotation. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.GetValueGenerationStrategyConfigurationSource(Microsoft.EntityFrameworkCore.Metadata.IConventionProperty)">
            <summary>
                Returns the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" />.
            </summary>
            <param name="property"> The property. </param>
            <returns> The <see cref="T:Microsoft.EntityFrameworkCore.Metadata.ConfigurationSource" /> for the <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" />. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerPropertyExtensions.IsCompatibleWithValueGeneration(Microsoft.EntityFrameworkCore.Metadata.IProperty)">
            <summary>
                Returns a value indicating whether the property is compatible with any <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" />.
            </summary>
            <param name="property"> The property. </param>
            <returns> <c>true</c> if compatible. </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder">
            <summary>
                <para>
                    Allows SQL Server specific configuration to be performed on <see cref="T:Microsoft.EntityFrameworkCore.DbContextOptions" />.
                </para>
                <para>
                    Instances of this class are returned from a call to
                    <see
                        cref="M:Microsoft.EntityFrameworkCore.SqlServerDbContextOptionsExtensions.UseSqlServer(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder,System.String,System.Action{Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder})" />
                    and it is not designed to be directly constructed in your application code.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder.#ctor(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder" /> class.
            </summary>
            <param name="optionsBuilder"> The options builder. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder.UseRowNumberForPaging(System.Boolean)">
            <summary>
                Use a ROW_NUMBER() in queries instead of OFFSET/FETCH. This method is backwards-compatible to SQL Server 2005.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder.EnableRetryOnFailure">
            <summary>
                Configures the context to use the default retrying <see cref="T:Microsoft.EntityFrameworkCore.Storage.IExecutionStrategy" />.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder.EnableRetryOnFailure(System.Int32)">
            <summary>
                Configures the context to use the default retrying <see cref="T:Microsoft.EntityFrameworkCore.Storage.IExecutionStrategy" />.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder.EnableRetryOnFailure(System.Int32,System.TimeSpan,System.Collections.Generic.ICollection{System.Int32})">
            <summary>
                Configures the context to use the default retrying <see cref="T:Microsoft.EntityFrameworkCore.Storage.IExecutionStrategy" />.
            </summary>
            <param name="maxRetryCount"> The maximum number of retry attempts. </param>
            <param name="maxRetryDelay"> The maximum delay between retries. </param>
            <param name="errorNumbersToAdd"> Additional SQL error numbers that should be considered transient. </param>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerConventionSetBuilder">
            <summary>
                <para>
                    A builder for building conventions for SQL Server.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> and multiple registrations
                    are allowed. This means that each <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instance will use its own
                    set of instances of this service.
                    The implementations may depend on other services registered with any lifetime.
                    The implementations do not need to be thread-safe.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerConventionSetBuilder.#ctor(Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.ProviderConventionSetBuilderDependencies,Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.RelationalConventionSetBuilderDependencies,Microsoft.EntityFrameworkCore.Storage.ISqlGenerationHelper)">
            <summary>
                Creates a new <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerConventionSetBuilder" /> instance.
            </summary>
            <param name="dependencies"> The core dependencies for this service. </param>
            <param name="relationalDependencies"> The relational dependencies for this service. </param>
            <param name="sqlGenerationHelper"> The SQL generation helper to use. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerConventionSetBuilder.CreateConventionSet">
            <summary>
                Builds and returns the convention set for the current database provider.
            </summary>
            <returns> The convention set for the current database provider. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerConventionSetBuilder.Build">
            <summary>
                <para>
                    Call this method to build a <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.ConventionSet" /> for SQL Server when using
                    the <see cref="T:Microsoft.EntityFrameworkCore.ModelBuilder" /> outside of <see cref="M:Microsoft.EntityFrameworkCore.DbContext.OnModelCreating(Microsoft.EntityFrameworkCore.ModelBuilder)" />.
                </para>
                <para>
                    Note that it is unusual to use this method.
                    Consider using <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> in the normal way instead.
                </para>
            </summary>
            <returns> The convention set. </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerIndexConvention">
            <summary>
                A convention that configures the filter for unique non-clustered indexes with nullable columns
                to filter out null values.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerIndexConvention.#ctor(Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.ProviderConventionSetBuilderDependencies,Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.RelationalConventionSetBuilderDependencies,Microsoft.EntityFrameworkCore.Storage.ISqlGenerationHelper)">
            <summary>
                Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerIndexConvention" />.
            </summary>
            <param name="dependencies"> Parameter object containing dependencies for this convention. </param>
            <param name="relationalDependencies">  Parameter object containing relational dependencies for this convention. </param>
            <param name="sqlGenerationHelper"> SQL command generation helper service. </param>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerIndexConvention.Dependencies">
            <summary>
                Parameter object containing service dependencies.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerIndexConvention.ProcessEntityTypeBaseTypeChanged(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionEntityTypeBuilder,Microsoft.EntityFrameworkCore.Metadata.IConventionEntityType,Microsoft.EntityFrameworkCore.Metadata.IConventionEntityType,Microsoft.EntityFrameworkCore.Metadata.Conventions.IConventionContext{Microsoft.EntityFrameworkCore.Metadata.IConventionEntityType})">
            <summary>
                Called after the base type of an entity type changes.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type. </param>
            <param name="newBaseType"> The new base entity type. </param>
            <param name="oldBaseType"> The old base entity type. </param>
            <param name="context"> Additional information associated with convention execution. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerIndexConvention.ProcessIndexAdded(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder,Microsoft.EntityFrameworkCore.Metadata.Conventions.IConventionContext{Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder})">
            <summary>
                Called after an index is added to the entity type.
            </summary>
            <param name="indexBuilder"> The builder for the index. </param>
            <param name="context"> Additional information associated with convention execution. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerIndexConvention.ProcessIndexUniquenessChanged(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder,Microsoft.EntityFrameworkCore.Metadata.Conventions.IConventionContext{Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder})">
            <summary>
                Called after the uniqueness for an index is changed.
            </summary>
            <param name="indexBuilder"> The builder for the index. </param>
            <param name="context"> Additional information associated with convention execution. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerIndexConvention.ProcessPropertyNullabilityChanged(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,Microsoft.EntityFrameworkCore.Metadata.Conventions.IConventionContext{Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder})">
            <summary>
                Called after the nullability for a property is changed.
            </summary>
            <param name="propertyBuilder"> The builder for the property. </param>
            <param name="context"> Additional information associated with convention execution. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerIndexConvention.ProcessIndexAnnotationChanged(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder,System.String,Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation,Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation,Microsoft.EntityFrameworkCore.Metadata.Conventions.IConventionContext{Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation})">
            <summary>
                Called after an annotation is changed on an index.
            </summary>
            <param name="indexBuilder"> The builder for the index. </param>
            <param name="name"> The annotation name. </param>
            <param name="annotation"> The new annotation. </param>
            <param name="oldAnnotation"> The old annotation.  </param>
            <param name="context"> Additional information associated with convention execution. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerIndexConvention.ProcessPropertyAnnotationChanged(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.String,Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation,Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation,Microsoft.EntityFrameworkCore.Metadata.Conventions.IConventionContext{Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation})">
            <summary>
                Called after an annotation is changed on a property.
            </summary>
            <param name="propertyBuilder"> The builder for the property. </param>
            <param name="name"> The annotation name. </param>
            <param name="annotation"> The new annotation. </param>
            <param name="oldAnnotation"> The old annotation.  </param>
            <param name="context"> Additional information associated with convention execution. </param>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerMemoryOptimizedTablesConvention">
            <summary>
                A convention that configures indexes as non-clustered for memory-optimized tables.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerMemoryOptimizedTablesConvention.#ctor(Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.ProviderConventionSetBuilderDependencies,Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.RelationalConventionSetBuilderDependencies)">
            <summary>
                Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerMemoryOptimizedTablesConvention" />.
            </summary>
            <param name="dependencies"> Parameter object containing dependencies for this convention. </param>
            <param name="relationalDependencies">  Parameter object containing relational dependencies for this convention. </param>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerMemoryOptimizedTablesConvention.Dependencies">
            <summary>
                Parameter object containing service dependencies.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerMemoryOptimizedTablesConvention.ProcessEntityTypeAnnotationChanged(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionEntityTypeBuilder,System.String,Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation,Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation,Microsoft.EntityFrameworkCore.Metadata.Conventions.IConventionContext{Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation})">
            <summary>
                Called after an annotation is changed on an entity type.
            </summary>
            <param name="entityTypeBuilder"> The builder for the entity type. </param>
            <param name="name"> The annotation name. </param>
            <param name="annotation"> The new annotation. </param>
            <param name="oldAnnotation"> The old annotation.  </param>
            <param name="context"> Additional information associated with convention execution. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerMemoryOptimizedTablesConvention.ProcessKeyAdded(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionKeyBuilder,Microsoft.EntityFrameworkCore.Metadata.Conventions.IConventionContext{Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionKeyBuilder})">
            <summary>
                Called after a key is added to the entity type.
            </summary>
            <param name="keyBuilder"> The builder for the key. </param>
            <param name="context"> Additional information associated with convention execution. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerMemoryOptimizedTablesConvention.ProcessIndexAdded(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder,Microsoft.EntityFrameworkCore.Metadata.Conventions.IConventionContext{Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionIndexBuilder})">
            <summary>
                Called after an index is added to the entity type.
            </summary>
            <param name="indexBuilder"> The builder for the index. </param>
            <param name="context"> Additional information associated with convention execution. </param>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerStoreGenerationConvention">
            <summary>
                A convention that ensures that properties aren't configured to have a default value, as computed column
                or using a <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" /> at the same time.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerStoreGenerationConvention.#ctor(Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.ProviderConventionSetBuilderDependencies,Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.RelationalConventionSetBuilderDependencies)">
            <summary>
                Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerStoreGenerationConvention" />.
            </summary>
            <param name="dependencies"> Parameter object containing dependencies for this convention. </param>
            <param name="relationalDependencies">  Parameter object containing relational dependencies for this convention. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerStoreGenerationConvention.ProcessPropertyAnnotationChanged(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.String,Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation,Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation,Microsoft.EntityFrameworkCore.Metadata.Conventions.IConventionContext{Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation})">
            <summary>
                Called after an annotation is changed on a property.
            </summary>
            <param name="propertyBuilder"> The builder for the property. </param>
            <param name="name"> The annotation name. </param>
            <param name="annotation"> The new annotation. </param>
            <param name="oldAnnotation"> The old annotation.  </param>
            <param name="context"> Additional information associated with convention execution. </param>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerValueGenerationConvention">
            <summary>
                A convention that configures store value generation as <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd" /> on properties that are
                part of the primary key and not part of any foreign keys, were configured to have a database default value
                or were configured to use a <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" />.
                It also configures properties as <see cref="F:Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAddOrUpdate" /> if they were configured as computed columns.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerValueGenerationConvention.#ctor(Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.ProviderConventionSetBuilderDependencies,Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.RelationalConventionSetBuilderDependencies)">
            <summary>
                Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerValueGenerationConvention" />.
            </summary>
            <param name="dependencies"> Parameter object containing dependencies for this convention. </param>
            <param name="relationalDependencies">  Parameter object containing relational dependencies for this convention. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerValueGenerationConvention.ProcessPropertyAnnotationChanged(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionPropertyBuilder,System.String,Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation,Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation,Microsoft.EntityFrameworkCore.Metadata.Conventions.IConventionContext{Microsoft.EntityFrameworkCore.Metadata.IConventionAnnotation})">
            <summary>
                Called after an annotation is changed on a property.
            </summary>
            <param name="propertyBuilder"> The builder for the property. </param>
            <param name="name"> The annotation name. </param>
            <param name="annotation"> The new annotation. </param>
            <param name="oldAnnotation"> The old annotation.  </param>
            <param name="context"> Additional information associated with convention execution. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerValueGenerationConvention.GetValueGenerated(Microsoft.EntityFrameworkCore.Metadata.IConventionProperty)">
            <summary>
                Returns the store value generation strategy to set for the given property.
            </summary>
            <param name="property"> The property. </param>
            <returns> The store value generation strategy to set for the given property. </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerValueGenerationConvention.GetValueGenerated(Microsoft.EntityFrameworkCore.Metadata.IProperty)">
            <summary>
                Returns the store value generation strategy to set for the given property.
            </summary>
            <param name="property"> The property. </param>
            <returns> The store value generation strategy to set for the given property. </returns>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerValueGenerationStrategyConvention">
            <summary>
                A convention that configures the default model <see cref="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy" /> as
                <see cref="F:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy.IdentityColumn" />.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerValueGenerationStrategyConvention.#ctor(Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.ProviderConventionSetBuilderDependencies,Microsoft.EntityFrameworkCore.Metadata.Conventions.Infrastructure.RelationalConventionSetBuilderDependencies)">
            <summary>
                Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerValueGenerationStrategyConvention" />.
            </summary>
            <param name="dependencies"> Parameter object containing dependencies for this convention. </param>
            <param name="relationalDependencies">  Parameter object containing relational dependencies for this convention. </param>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerValueGenerationStrategyConvention.Dependencies">
            <summary>
                Parameter object containing service dependencies.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerValueGenerationStrategyConvention.ProcessModelInitialized(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,Microsoft.EntityFrameworkCore.Metadata.Conventions.IConventionContext{Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder})">
            <summary>
                Called after a model is initialized.
            </summary>
            <param name="modelBuilder"> The builder for the model. </param>
            <param name="context"> Additional information associated with convention execution. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.Metadata.Conventions.SqlServerValueGenerationStrategyConvention.ProcessModelFinalized(Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder,Microsoft.EntityFrameworkCore.Metadata.Conventions.IConventionContext{Microsoft.EntityFrameworkCore.Metadata.Builders.IConventionModelBuilder})">
            <summary>
                Called after a model is finalized.
            </summary>
            <param name="modelBuilder"> The builder for the model. </param>
            <param name="context"> Additional information associated with convention execution. </param>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy">
            <summary>
                Defines two strategies to use across the EF Core stack when generating key values
                from SQL Server database columns.
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy.None">
            <summary>
                No SQL Server-specific strategy
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy.SequenceHiLo">
            <summary>
                <para>
                    A sequence-based hi-lo pattern where blocks of IDs are allocated from the server and
                    used client-side for generating keys.
                </para>
                <para>
                    This is an advanced pattern--only use this strategy if you are certain it is what you need.
                </para>
            </summary>
        </member>
        <member name="F:Microsoft.EntityFrameworkCore.Metadata.SqlServerValueGenerationStrategy.IdentityColumn">
            <summary>
                A pattern that uses a normal SQL Server <c>Identity</c> column in the same way as EF6 and earlier.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy">
            <summary>
                An <see cref="T:Microsoft.EntityFrameworkCore.Storage.IExecutionStrategy" /> implementation for retrying failed executions
                on SQL Server.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy.#ctor(Microsoft.EntityFrameworkCore.DbContext)">
            <summary>
                Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy" />.
            </summary>
            <param name="context"> The context on which the operations will be invoked. </param>
            <remarks>
                The default retry limit is 6, which means that the total amount of time spent before failing is about a minute.
            </remarks>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy.#ctor(Microsoft.EntityFrameworkCore.Storage.ExecutionStrategyDependencies)">
            <summary>
                Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy" />.
            </summary>
            <param name="dependencies"> Parameter object containing service dependencies. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy.#ctor(Microsoft.EntityFrameworkCore.DbContext,System.Int32)">
            <summary>
                Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy" />.
            </summary>
            <param name="context"> The context on which the operations will be invoked. </param>
            <param name="maxRetryCount"> The maximum number of retry attempts. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy.#ctor(Microsoft.EntityFrameworkCore.Storage.ExecutionStrategyDependencies,System.Int32)">
            <summary>
                Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy" />.
            </summary>
            <param name="dependencies"> Parameter object containing service dependencies. </param>
            <param name="maxRetryCount"> The maximum number of retry attempts. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy.#ctor(Microsoft.EntityFrameworkCore.DbContext,System.Int32,System.TimeSpan,System.Collections.Generic.ICollection{System.Int32})">
            <summary>
                Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy" />.
            </summary>
            <param name="context"> The context on which the operations will be invoked. </param>
            <param name="maxRetryCount"> The maximum number of retry attempts. </param>
            <param name="maxRetryDelay"> The maximum delay between retries. </param>
            <param name="errorNumbersToAdd"> Additional SQL error numbers that should be considered transient. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy.#ctor(Microsoft.EntityFrameworkCore.Storage.ExecutionStrategyDependencies,System.Int32,System.TimeSpan,System.Collections.Generic.ICollection{System.Int32})">
            <summary>
                Creates a new instance of <see cref="T:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy" />.
            </summary>
            <param name="dependencies"> Parameter object containing service dependencies. </param>
            <param name="maxRetryCount"> The maximum number of retry attempts. </param>
            <param name="maxRetryDelay"> The maximum delay between retries. </param>
            <param name="errorNumbersToAdd"> Additional SQL error numbers that should be considered transient. </param>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy.ShouldRetryOn(System.Exception)">
            <summary>
                Determines whether the specified exception represents a transient failure that can be
                compensated by a retry. Additional exceptions to retry on can be passed to the constructor.
            </summary>
            <param name="exception"> The exception object to be verified. </param>
            <returns>
                <c>true</c> if the specified exception is considered as transient, otherwise <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerRetryingExecutionStrategy.GetNextDelay(System.Exception)">
            <summary>
                Determines whether the operation should be retried and the delay before the next attempt.
            </summary>
            <param name="lastException"> The exception thrown during the last execution attempt. </param>
            <returns>
                Returns the delay indicating how long to wait for before the next execution attempt if the operation should be retried;
                <c>null</c> otherwise
            </returns>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.SqlServerServiceCollectionExtensions">
            <summary>
                SQL Server specific extension methods for <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.SqlServerServiceCollectionExtensions.AddEntityFrameworkSqlServer(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
                <para>
                    Adds the services required by the Microsoft SQL Server database provider for Entity Framework
                    to an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
                </para>
                <para>
                    Calling this method is no longer necessary when building most applications, including those that
                    use dependency injection in ASP.NET or elsewhere.
                    It is only needed when building the internal service provider for use with
                    the <see cref="M:Microsoft.EntityFrameworkCore.DbContextOptionsBuilder.UseInternalServiceProvider(System.IServiceProvider)" /> method.
                    This is not recommend other than for some advanced scenarios.
                </para>
            </summary>
            <param name="serviceCollection"> The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to. </param>
            <returns>
                The same service collection so that multiple calls can be chained.
            </returns>
        </member>
    </members>
</doc>
