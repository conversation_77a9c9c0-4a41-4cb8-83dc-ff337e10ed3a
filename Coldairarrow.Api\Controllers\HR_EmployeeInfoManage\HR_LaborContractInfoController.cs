﻿using Coldairarrow.Business.HR_DataDictionaryManage;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.HR_EmployeeInfoManage
{
    [Route("/HR_EmployeeInfoManage/[controller]/[action]")]
    public class HR_LaborContractInfoController : BaseApiController
    {
        #region DI

        public HR_LaborContractInfoController(IHR_LaborContractInfoBusiness hR_LaborContractInfoBus, IHR_DataDictionaryDetailsBusiness hR_DataDictionaryDetailsBus, IConfiguration configuration)
        {
            _hR_LaborContractInfoBus = hR_LaborContractInfoBus;
            _configuration = configuration;
            _hR_DataDictionaryDetailsBus = hR_DataDictionaryDetailsBus;
        }

        IHR_LaborContractInfoBusiness _hR_LaborContractInfoBus { get; }
        readonly IConfiguration _configuration;
        IHR_DataDictionaryDetailsBusiness _hR_DataDictionaryDetailsBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public AjaxResult GetInit()
        {
            var contractDurationList = _hR_DataDictionaryDetailsBus.GetDetailList("合同期限类型", "");
            var data = new
            {
                wfStates = EnumHelper.ToOptionList(typeof(WFStates)),
                contractDurationList
            };
            return Success(data);
        }

        [HttpPost]
        public async Task<PageResult<HR_LaborContractInfoDTO>> GetLaborContractListt(PageInput<LaborContractConditionDTO> input)
        {
            if (input.Search.queryType == 3)
            {
                return await _hR_LaborContractInfoBus.GetLaborContractEmpList(input);
            }
            else
            {
                return await _hR_LaborContractInfoBus.GetLaborContractList(input);
            }
        }

        [HttpPost]
        public async Task<PageResult<HR_LaborContractInfo>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_LaborContractInfoBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_LaborContractInfo> GetTheData(IdInputDTO input)
        {
            return await _hR_LaborContractInfoBus.GetTheDataAsync(input.id);
        }

        [HttpPost]
        public HR_LaborContractInfoDTO GetLaborContractInfo(LaborContractInputDTO input)
        {
            return _hR_LaborContractInfoBus.GetLaborContractInfo(input);
        }
        /// <summary>
        /// 续签时获取信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public HR_LaborContractInfoDTO GetXQLaborContractInfo(LaborContractInputDTO input)
        {
            return _hR_LaborContractInfoBus.GetXQLaborContractInfo(input);
        }
        /// <summary>
        /// 续签时获取信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public HR_LaborContractInfoDTO GetXQLaborContractInfoById(LaborContractInputDTO input)
        {
            return _hR_LaborContractInfoBus.GetXQLaborContractInfoById(input);
        }
        /// <summary>
        /// 变更时获取信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public HR_LaborContractInfoDTO GetBGLaborContractInfo(LaborContractInputDTO input)
        {
            return _hR_LaborContractInfoBus.GetBGLaborContractInfo(input);
        }
        /// <summary>
        /// 根据用户ID获取最后签订的劳动合同信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpPost]
        public HR_LaborContractInfo GetLaborContract(LaborContractInputDTO input)
        {
            return _hR_LaborContractInfoBus.GetLaborContract(input.id);
        }

        #endregion

        #region 提交

        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ArchiveWorkflow(HR_LaborContractInfoDTO data)
        {
            var ret = _hR_LaborContractInfoBus.ArchiveWorkflow(data, _configuration["OAUrl"] + "rest/archives/");
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("强制归档失败");
            }
        }

        [HttpPost]
        public async Task ChangeSaveData(HR_LaborContractInfo data)
        {
            await _hR_LaborContractInfoBus.ChangeSaveData(data);
        }
        [HttpPost]
        public async Task SaveData(HR_LaborContractInfo data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);
                //data.F_WFState = (int)WFStates.草稿;

                await _hR_LaborContractInfoBus.AddDataAsync(data);
                if (data.F_WFState == (int)WFStates.提交生效)
                {
                    _hR_LaborContractInfoBus.FlowCallBack(new FlowInputDTO() { id = data.F_Id, status = data.F_WFState.Value });
                }
            }
            else
            {
                await _hR_LaborContractInfoBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_LaborContractInfoBus.DeleteDataAsync(ids);
        }


        /// <summary>
        /// 流程回调
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult FlowCallBack(FlowInputDTO input)
        {
            _hR_LaborContractInfoBus.FlowCallBack(input);

            return Success();
        }

        /// <summary>
        /// 保存并创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult SaveAndCreateFlow(HR_LaborContractInfoDTO data)
        {
            var ret = _hR_LaborContractInfoBus.CreateFlow(data, _configuration["OAUrl"] + "rest/archives/", GetOperator());
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("创建流程失败");
            }
        }
        /// <summary>
        /// 保存并创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ActWorkflow(HR_LaborContractInfoDTO data)
        {
            var ret = _hR_LaborContractInfoBus.ActWorkflow(data, _configuration["OAUrl"] + "rest/archives/", GetOperator());
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("提交流程失败");
            }
        }

        #endregion

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<LaborContractConditionDTO> input)
        {
            try
            {
                //取出数据源
                DataTable exportTable = _hR_LaborContractInfoBus.GetExcelListAsync(input);

                if (exportTable != null && exportTable.Rows.Count > 0)
                {
                    exportTable.Columns.Add("wfstate");
                    for (int i = 0; i < exportTable.Rows.Count; i++)
                    {
                        var wfstate = exportTable.Rows[i]["F_WFState"];
                        if (wfstate != null && !wfstate.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["wfstate"] = Enum.Parse(typeof(WFStates), wfstate.ToString()).ToString();
                        }
                    }
                }
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "劳动合同";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                int sort = 0;
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "empcode", ExcelColumn = "员工编码", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "empname", ExcelColumn = "姓名", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "contractnumber", ExcelColumn = "合同编号", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "subjectlaborcontract", ExcelColumn = "劳动合同主体", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "repparty", ExcelColumn = "甲方单位代表人", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "contractdurationdate", ExcelColumn = "合同期限类型", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "contracteffectdate", ExcelColumn = "合同生效日期", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "contractenddate", ExcelColumn = "合同终止日期", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "signingdate", ExcelColumn = "签订日期", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "terminationdate", ExcelColumn = "解除日期", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "changestype", ExcelColumn = "变动类型", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "wfstate", ExcelColumn = "流程状态", Alignment = "left", Sort = sort++ });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

    }
}