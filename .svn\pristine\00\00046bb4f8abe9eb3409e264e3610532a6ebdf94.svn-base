﻿using AutoMapper;
using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HR_DataDictionaryManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage.Extensions;
using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HR_EmployeeInfoManage
{
    public class HR_DepartureBusiness : BaseBusiness<HR_Departure>, IHR_DepartureBusiness, ITransientDependency
    {
        readonly IMapper _mapper;
        IConfiguration _configuration;
        IBase_DepartmentBusiness _departmentBusiness;
        public HR_DepartureBusiness(IDbAccessor db, IConfiguration configuration, IMapper mapper, IBase_DepartmentBusiness departmentBusiness)
            : base(db)
        {
            _configuration = configuration;
            _mapper = mapper;
            _departmentBusiness = departmentBusiness;
        }

        #region 外部接口

        /// <summary>
        /// 导出Excel
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_Departure, HR_FormalEmployees, HR_DepartureDTO>> select = (d, e) => new HR_DepartureDTO
            {
                EmpCode = e.EmployeesCode,
                EmpName = e.NameUser,
                Sex = e.Sex,
                IdCardNumber = e.IdCardNumberStr,
                DirthDate = e.DirthDate,
                BaseUserId = e.BaseUserId
            };
            select = select.BuildExtendSelectExpre();
            var q = from d in this.Db.GetIQueryable<HR_Departure>().AsExpandable()
                    join e in this.Db.GetIQueryable<HR_FormalEmployees>() on d.F_UserId equals e.F_Id into emp
                    from e in emp.DefaultIfEmpty()
                    select @select.Invoke(d, e);
            var where = LinqHelper.True<HR_DepartureDTO>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_DepartureDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            if (search.selectIds != null && search.selectIds.Count > 0)
            {
                where = where.And(x => search.selectIds.Contains(x.F_Id));
            }
            where = where.AndIf(!search.UserId.IsNullOrEmpty(), x => x.BaseUserId == search.UserId);
            where = where.AndIf(search.wfState.HasValue, x => x.F_WFState == search.wfState);

            return q.Where(where).OrderBy(input.SortField, input.SortType).ToList().ToDataTable();
        }

        /// <summary>
        /// 分页查询 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PageResult<HR_DepartureDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_Departure, HR_FormalEmployees, HR_DepartureDTO>> select = (d, e) => new HR_DepartureDTO
            {
                EmpCode = e.EmployeesCode,
                EmpName = e.NameUser,
                Sex = e.Sex,
                IdCardNumber = e.IdCardNumberStr,
                DirthDate = e.DirthDate,
                BaseUserId = e.BaseUserId
            };
            select = select.BuildExtendSelectExpre();
            var q = from d in this.Db.GetIQueryable<HR_Departure>().AsExpandable()
                    join e in this.Db.GetIQueryable<HR_FormalEmployees>() on d.F_UserId equals e.F_Id into emp
                    from e in emp.DefaultIfEmpty()
                    select @select.Invoke(d, e);
            var where = LinqHelper.True<HR_DepartureDTO>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_DepartureDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            where = where.AndIf(!search.UserId.IsNullOrEmpty(), x => x.BaseUserId == search.UserId);
            where = where.AndIf(search.wfState.HasValue, x => x.F_WFState == search.wfState);
            //.OrderBy(x => x.F_WFState).ThenByDescending(x => x.F_TrueDepartureDate)
            return await q.Where(where).GetPageResultAsync(input);
        }

        /// <summary>
        /// 修改时获取离职相关联数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public HR_DepartureDTO GetDepartureInfo(string id)
        {
            HR_Departure model = Db.GetIQueryable<HR_Departure>().FirstOrDefault(x => x.F_Id == id);
            HR_DepartureDTO entity = null;
            if (model != null)
            {
                entity = _mapper.Map<HR_DepartureDTO>(model);
                var emp = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == entity.F_UserId);
                if (emp != null)
                {
                    emp.DecryptFormal();
                    entity.F_UserId = emp.F_Id;
                    entity.EmpCode = emp.EmployeesCode;
                    entity.EmpName = emp.NameUser;
                    entity.MobilePhone = emp.MobilePhone;
                    entity.F_Rank = emp.F_Rank;
                    var postion = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == emp.F_PositionId);
                    entity.F_IsDirectReports = postion?.F_IsDirectReports;
                    entity.PostName = postion?.F_Name;
                    var com = Db.GetIQueryable<Base_Company>().FirstOrDefault(x => x.F_Id == emp.F_CompanyId);
                    var dept = Db.GetIQueryable<Base_Department>().FirstOrDefault(x => x.Id == emp.F_DepartmentId);
                    entity.OrgInfo = $"{com?.F_FullName}_{dept?.Name}";
                    entity.F_IsDirectReports = postion.F_IsDirectReports;
                }
            }

            return entity;
        }
        public async Task<HR_Departure> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public void Add(HR_Departure data)
        {
            Insert(data);
        }

        public void Edit(HR_Departure data)
        {
            Update(data);
        }
        [DataAddLog(UserLogType.离职管理, "F_Code", "保存草稿")]
        public async Task AddDataAsync(HR_Departure data)
        {
            await InsertAsync(data);
        }
        [DataEditLog(UserLogType.离职管理, "F_Code", "修改保存草稿")]
        public async Task UpdateDataAsync(HR_Departure data)
        {
            await UpdateAsync(data);
        }
        [DataDeleteLog(UserLogType.离职管理, "F_Code", "保存草稿")]
        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public void FlowCallBack(FlowInputDTO input)
        {
            if (input == null)
            {
                throw new BusException("参数错误");
            }

            var entity = GetIQueryable().FirstOrDefault(x => x.F_Id == input.id);
            if (entity == null)
            {
                throw new BusException("未找到对象");
            }
            entity.F_WFState = input.status;
            entity.F_ModifyDate = DateTime.Now;
            entity.F_ModifyUserId = "System";
            entity.F_ModifyUserName = "System";

            if (input.status == (int)WFStates.完成流程)
            {
                if (input.actual_leave_date.IsNullOrEmpty())
                {
                    entity.F_TrueDepartureDate = DateTime.Now;
                }
                else
                {
                    entity.F_TrueDepartureDate = input.actual_leave_date.ToDateTime();
                }
                this.Update(entity);
                var emp = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == entity.F_UserId);
                if (emp != null)
                {
                    //修改员工状态
                    emp.EmployRelStatus = entity.F_MobilzOriEmplStatus;
                    emp.F_ModifyDate = DateTime.Now;
                    emp.F_ModifyUserId = "System";
                    emp.F_ModifyUserName = "System";
                    Db.Update(emp);

                    //修改劳动合同为解除
                    var ht = Db.GetIQueryable<HR_LaborContractInfo>().Where(x => x.ContractState == "生效" && x.IsNewContract == 1 && x.UserId == emp.F_Id).ToList();
                    if (ht != null && ht.Count > 0)
                    {
                        ht.ForEach(x => x.ContractState = "解除");
                        Db.Update(ht);
                    }
                    //修改任职经历结束时间
                    var ceModel = Db.GetIQueryable<HR_CompanyEmploy>().Where(x => x.F_UserId == entity.F_UserId).OrderByDescending(x => x.F_CreateDate).FirstOrDefault();
                    if (ceModel != null)
                    {
                        ceModel.F_EndTime = entity.F_TrueDepartureDate;
                        Db.Update(ceModel);
                    }

                }
            }
            else
            {
                this.Update(entity);
            }
        }


        /// <summary>
        /// 提交流程
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <returns>是否创建成功</returns>
        /// 
        [DataAddLog(UserLogType.离职管理, "EmpName", "离职（提交流程）")]
        //[Transactional]
        public bool CreateFlow(HR_DepartureDTO data, string url, IOperator op)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data, op);
                Random ra = new Random();
                data.F_Code = DateTime.Now.ToString("yyyyMMdd") + ra.Next(100, 999);
                data.F_WFState = (int)WFStates.草稿;

                Add(data);
            }
            else
            {
                UpdateEntity(data, op);
                Edit(data);
            }
            if (data.IsAudit)
            {
                data.F_WFId = null;
                data.F_WFState = (int)WFStates.提交生效;
                data.F_TrueDepartureDate = data.F_DepartureDate;
                Update(data); 
                var emp = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
                if (emp != null)
                {
                    //修改员工状态
                    emp.EmployRelStatus = data.F_MobilzOriEmplStatus;
                    emp.F_ModifyDate = DateTime.Now;
                    emp.F_ModifyUserId = "System";
                    emp.F_ModifyUserName = "System";
                    Db.Update(emp); 
                    //修改劳动合同为解除
                    var ht = Db.GetIQueryable<HR_LaborContractInfo>().Where(x => x.ContractState == "生效" && x.IsNewContract == 1 && x.UserId == emp.F_Id).ToList();
                    if (ht != null && ht.Count > 0)
                    {
                        ht.ForEach(x => x.ContractState = "解除");
                        Db.Update(ht);
                    }
                    //修改任职经历结束时间
                    var ceModel = Db.GetIQueryable<HR_CompanyEmploy>().Where(x => x.F_UserId == data.F_UserId).OrderByDescending(x => x.F_CreateDate).FirstOrDefault();
                    if (ceModel != null)
                    {
                        ceModel.F_EndTime = data.F_TrueDepartureDate;
                        Db.Update(ceModel);
                    } 
                }
                return true;
            }
            bool ret = false;
           
            var entity = Db.GetIQueryable<Base_FileInfo>().Where(i => i.F_FileFolderId == data.F_FileId).Select(x => new FileInfoDTO() { Id = x.F_Id, FileName = x.F_FileName, FilePath = x.F_FilePath }).ToList();
            string token = string.Empty;
            Base_User user = null;
            var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
            var project = "";
            if (formalEmployees != null)
            {
                user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == formalEmployees.BaseUserId);
                token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
                var post = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == formalEmployees.F_PositionId);
                data.F_IsDirectReports = post?.F_IsDirectReports;
                project = !formalEmployees.ND.IsNullOrEmpty() ?
               this.Db.GetIQueryable<HR_DataDictionaryDetails>()
               .FirstOrDefault(i => i.F_ItemValue == formalEmployees.ND).F_OAValue : "";
            }
            //var user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == data.F_CreateUserId);
            //string token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            var userName = user != null ? user.UserName.Replace("@cqlandmark.com", "") : "";
            paramters.Add("reqCode", token);
            paramters.Add("reqNo", data.F_Id);
            paramters.Add("applyPerson", userName);
            paramters.Add("formId", _configuration["OAFormId:DepartureFlow"]);
            Dictionary<string, object> hrData = new Dictionary<string, object>();
            hrData.Add("leave_date", data.F_DepartureDate.Value.ToString("yyyy-MM-dd"));  //预计离职日期 ：“yyyy-MM-dd”
            hrData.Add("leave_reason", data.F_DepartureReason);  //离职原因，0:个人发展、1：家庭原因，2：薪酬福利，3：工作环境，4：试用期离职，5：其他
            hrData.Add("old_employ_status", data.F_OriginalEmplStatus); //原用工状态，0：正式员工，1：正式员工(校招),2:第三方员工，3：试用员工(延期转正)
            hrData.Add("target_employ_status", data.F_MobilzOriEmplStatus); //目标用工状态：0：主动离职
            hrData.Add("change_operating", data.F_ChangesOperating);
            hrData.Add("change_type", data.F_ChangesType);
            hrData.Add("phone", data.MobilePhone);
            hrData.Add("note", OAHelper.GetNuLL(data.F_Remark));
            hrData.Add("project", OAHelper.GetNuLL(project));
            hrData.Add("files", entity);
            hrData.Add("apply_date", data.F_CreateDate.ToString("yyyy-MM-dd"));
            hrData.Add("code", data.F_Code);
            hrData.Add("org", data.OrgInfo);
            hrData.Add("job", data.PostName);
            hrData.Add("is_under_general_manager", data.F_IsDirectReports);//是否是总经理及直系下属
            hrData.Add("hr_dept", _departmentBusiness.GetRootDept(formalEmployees.F_DepartmentId)?.Name);   //员工所在一级部门
            paramters.Add("hrData", hrData);

            string action = "create";
            string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);

            var retObj = str.ToObject<Dictionary<string, string>>();
            if (retObj != null)
            {
                if (retObj["errCode"] == "0000")
                {
                    data.F_WFId = retObj["oaReqId"];
                    data.F_WFState = (int)WFStates.审核中;
                    Update(data);
                    ret = true;
                }
                else
                {
                    throw new BusException(retObj["errDesc"]);
                }
            }
            else
            {
                throw new BusException("创建流程失败");
            }

            return ret;
        }

        /// <summary>
        /// 提交、退回流程
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <param name="act">submit 提交  subnoback 提交不需回复   subback 提交需要回复  reject退回</param>
        /// <returns>是否成功</returns>
        /// 
        [DataEditLog(UserLogType.离职管理, "EmpName", "离职（提交、退回流程）")]
        //[Transactional]
        public bool ActWorkflow(HR_DepartureDTO data, string url, IOperator op, string act = "submit")
        {
            if (!data.F_Id.IsNullOrEmpty())
            {
                UpdateEntity(data, op);
                Edit(data);
            }
            bool ret = false;

            var entity = Db.GetIQueryable<Base_FileInfo>().Where(i => i.F_FileFolderId == data.F_FileId).Select(x => new FileInfoDTO() { Id = x.F_Id, FileName = x.F_FileName, FilePath = x.F_FilePath }).ToList();
            string token = string.Empty;
            Base_User user = null;
            var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
            var project = "";
            if (formalEmployees != null)
            {
                user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == formalEmployees.BaseUserId);
                token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
                var post = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == formalEmployees.F_PositionId);
                data.F_IsDirectReports = post?.F_IsDirectReports;
                project = !formalEmployees.ND.IsNullOrEmpty() ?
               this.Db.GetIQueryable<HR_DataDictionaryDetails>()
               .FirstOrDefault(i => i.F_ItemValue == formalEmployees.ND).F_OAValue : "";
            }
            //var user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == data.F_CreateUserId);
            //string token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
            var model = this.GetEntity(data.F_Id);
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            var userName = user != null ? user.UserName.Replace("@cqlandmark.com", "") : "";
            paramters.Add("oaId", model.F_WFId);
            paramters.Add("reqCode", token);
            paramters.Add("reqNo", data.F_Id);
            paramters.Add("applyPerson", userName);
            paramters.Add("formId", _configuration["OAFormId:DepartureFlow"]);
            //paramters.Add("sign", "");
            paramters.Add("act", act);
            Dictionary<string, object> hrData = new Dictionary<string, object>();
            hrData.Add("leave_date", data.F_DepartureDate.Value.ToString("yyyy-MM-dd"));  //预计离职日期 ：“yyyy-MM-dd”
            hrData.Add("leave_reason", data.F_DepartureReason);  //离职原因，0:个人发展、1：家庭原因，2：薪酬福利，3：工作环境，4：试用期离职，5：其他
            hrData.Add("old_employ_status", data.F_OriginalEmplStatus); //原用工状态，0：正式员工，1：正式员工(校招),2:第三方员工，3：试用员工(延期转正)
            hrData.Add("target_employ_status", data.F_MobilzOriEmplStatus); //目标用工状态：0：主动离职
            hrData.Add("change_operating", data.F_ChangesOperating);
            hrData.Add("change_type", data.F_ChangesType);
            hrData.Add("phone", data.MobilePhone);
            hrData.Add("note",OAHelper.GetNuLL(data.F_Remark));
            hrData.Add("project", OAHelper.GetNuLL(project));
            hrData.Add("files", entity);
            hrData.Add("apply_date", data.F_CreateDate.ToString("yyyy-MM-dd"));
            hrData.Add("code", data.F_Code);
            hrData.Add("org", data.OrgInfo);
            hrData.Add("job", data.PostName);
            hrData.Add("is_under_general_manager", data.F_IsDirectReports);//是否是总经理及直系下属
            hrData.Add("hr_dept", _departmentBusiness.GetRootDept(formalEmployees.F_DepartmentId)?.Name);   //员工所在一级部门
            paramters.Add("hrData", hrData);
            string action = "actWorkflow";
            string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);

            var retObj = str.ToObject<Dictionary<string, string>>();
            if (retObj != null)
            {
                if (retObj["errCode"] == "0000")
                {
                    if (act == "submit")
                    {
                        data.F_WFId = retObj["oaReqId"];
                        data.F_WFState = (int)WFStates.审核中;
                        Update(data);
                    }
                    ret = true;
                }
                else
                {
                    throw new BusException(retObj["errDesc"]);
                }
            }
            else
            {
                throw new BusException("创建流程失败");
            }

            return ret;
        }
        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <returns>是否成功</returns>
        /// 
        [DataEditLog(UserLogType.离职管理, "EmpName", "离职（强制归档）")]
        public bool ArchiveWorkflow(HR_DepartureDTO data, string url)
        {
            bool ret = false;

            string token = string.Empty;
            Base_User user = null;
            var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
            if (formalEmployees != null)
            {
                user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == formalEmployees.BaseUserId);
                token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
            }
            //var user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == data.F_CreateUserId);
            //string token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
            var model = this.GetEntity(data.F_Id);
            var userName = user != null ? user.UserName.Replace("@cqlandmark.com", "") : "";
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            paramters.Add("reqCode", token);
            paramters.Add("reqNo", data.F_Id);
            paramters.Add("applyPerson", userName);
            paramters.Add("oaId", model.F_WFId);
            string action = "archiveWorkflow";
            string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);

            var retObj = str.ToObject<Dictionary<string, string>>();
            if (retObj != null)
            {
                if (retObj["errCode"] == "0000")
                {
                    data.F_WFId = retObj["oaReqId"];
                    data.F_WFState = (int)WFStates.取消流程;
                    Update(data);
                    ret = true;
                }
                else
                {
                    throw new BusException(retObj["errDesc"]);
                }
            }
            else
            {
                throw new BusException("创建流程失败");
            }

            return ret;
        }

        /// <summary>
        /// 验证是否有已经在流程中的和已完成的离职信息
        /// </summary>
        /// <param name="empId"></param>
        public bool ValidateIsDeparture(string empId)
        {
            int count = GetIQueryable().Where(x => x.F_UserId == empId && x.F_WFState.HasValue && x.F_WFState != (int)WFStates.草稿 && x.F_WFState != (int)WFStates.取消流程).Count();

            return count > 0;
        }

        #endregion

        #region 私有成员

        #endregion
    }
}