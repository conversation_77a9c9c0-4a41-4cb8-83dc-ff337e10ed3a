{"format": 1, "restore": {"E:\\Code\\HRSystem\\Coldairarrow.Api\\05.Coldairarrow.Api.csproj": {}}, "projects": {"E:\\Code\\HRSystem\\Aliyun.Base\\Aliyun.Base.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Code\\HRSystem\\Aliyun.Base\\Aliyun.Base.csproj", "projectName": "Aliyun.Base", "projectPath": "E:\\Code\\HRSystem\\Aliyun.Base\\Aliyun.Base.csproj", "packagesPath": "d:\\Users\\lijf\\.nuget\\packages\\", "outputPath": "E:\\Code\\HRSystem\\Aliyun.Base\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\vs\\gongxiang\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["d:\\Users\\lijf\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Aliyun.OSS.SDK.NetCore": {"target": "Package", "version": "[2.13.0, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}, "aliyun-net-sdk-nlp-automl": {"target": "Package", "version": "[0.0.9, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "E:\\Code\\HRSystem\\Coldairarrow.Api\\05.Coldairarrow.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Code\\HRSystem\\Coldairarrow.Api\\05.Coldairarrow.Api.csproj", "projectName": "Coldairarrow.Api", "projectPath": "E:\\Code\\HRSystem\\Coldairarrow.Api\\05.Coldairarrow.Api.csproj", "packagesPath": "d:\\Users\\lijf\\.nuget\\packages\\", "outputPath": "E:\\Code\\HRSystem\\Coldairarrow.Api\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\vs\\gongxiang\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["d:\\Users\\lijf\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"E:\\Code\\HRSystem\\Aliyun.Base\\Aliyun.Base.csproj": {"projectPath": "E:\\Code\\HRSystem\\Aliyun.Base\\Aliyun.Base.csproj"}, "E:\\Code\\HRSystem\\Coldairarrow.Business\\04.Coldairarrow.Business.csproj": {"projectPath": "E:\\Code\\HRSystem\\Coldairarrow.Business\\04.Coldairarrow.Business.csproj"}, "E:\\Code\\HRSystem\\Coldairarrow.Entity\\02.Coldairarrow.Entity.csproj": {"projectPath": "E:\\Code\\HRSystem\\Coldairarrow.Entity\\02.Coldairarrow.Entity.csproj"}, "E:\\Code\\HRSystem\\Coldairarrow.Util\\01.Coldairarrow.Util.csproj": {"projectPath": "E:\\Code\\HRSystem\\Coldairarrow.Util\\01.Coldairarrow.Util.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Colder.Logging.Serilog": {"target": "Package", "version": "[1.0.2, )"}, "DocumentFormat.OpenXml": {"target": "Package", "version": "[2.13.1, )"}, "IKVM": {"target": "Package", "version": "[8.2.0, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[3.1.8, )"}, "NETCore.Encrypt": {"target": "Package", "version": "[2.0.9, )"}, "NSwag.AspNetCore": {"target": "Package", "version": "[13.7.4, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "UAParser": {"target": "Package", "version": "[3.1.47, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "E:\\Code\\HRSystem\\Coldairarrow.Business\\04.Coldairarrow.Business.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Code\\HRSystem\\Coldairarrow.Business\\04.Coldairarrow.Business.csproj", "projectName": "Coldairarrow.Business", "projectPath": "E:\\Code\\HRSystem\\Coldairarrow.Business\\04.Coldairarrow.Business.csproj", "packagesPath": "d:\\Users\\lijf\\.nuget\\packages\\", "outputPath": "E:\\Code\\HRSystem\\Coldairarrow.Business\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\vs\\gongxiang\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["d:\\Users\\lijf\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"E:\\Code\\HRSystem\\Aliyun.Base\\Aliyun.Base.csproj": {"projectPath": "E:\\Code\\HRSystem\\Aliyun.Base\\Aliyun.Base.csproj"}, "E:\\Code\\HRSystem\\Coldairarrow.Entity\\02.Coldairarrow.Entity.csproj": {"projectPath": "E:\\Code\\HRSystem\\Coldairarrow.Entity\\02.Coldairarrow.Entity.csproj"}, "E:\\Code\\HRSystem\\Coldairarrow.IBusiness\\03.Coldairarrow.IBusiness.csproj": {"projectPath": "E:\\Code\\HRSystem\\Coldairarrow.IBusiness\\03.Coldairarrow.IBusiness.csproj"}, "E:\\Code\\HRSystem\\Coldairarrow.Util\\01.Coldairarrow.Util.csproj": {"projectPath": "E:\\Code\\HRSystem\\Coldairarrow.Util\\01.Coldairarrow.Util.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "System.ServiceModel.Duplex": {"target": "Package", "version": "[4.4.4, )"}, "System.ServiceModel.Http": {"target": "Package", "version": "[4.4.*, )"}, "System.ServiceModel.NetTcp": {"target": "Package", "version": "[4.4.*, )"}, "System.ServiceModel.Security": {"target": "Package", "version": "[4.4.*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "E:\\Code\\HRSystem\\Coldairarrow.Entity\\02.Coldairarrow.Entity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Code\\HRSystem\\Coldairarrow.Entity\\02.Coldairarrow.Entity.csproj", "projectName": "Coldairarrow.Entity", "projectPath": "E:\\Code\\HRSystem\\Coldairarrow.Entity\\02.Coldairarrow.Entity.csproj", "packagesPath": "d:\\Users\\lijf\\.nuget\\packages\\", "outputPath": "E:\\Code\\HRSystem\\Coldairarrow.Entity\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\vs\\gongxiang\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["d:\\Users\\lijf\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"E:\\Code\\HRSystem\\Coldairarrow.Util\\01.Coldairarrow.Util.csproj": {"projectPath": "E:\\Code\\HRSystem\\Coldairarrow.Util\\01.Coldairarrow.Util.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[4.7.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "E:\\Code\\HRSystem\\Coldairarrow.IBusiness\\03.Coldairarrow.IBusiness.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Code\\HRSystem\\Coldairarrow.IBusiness\\03.Coldairarrow.IBusiness.csproj", "projectName": "Coldairarrow.IBusiness", "projectPath": "E:\\Code\\HRSystem\\Coldairarrow.IBusiness\\03.Coldairarrow.IBusiness.csproj", "packagesPath": "d:\\Users\\lijf\\.nuget\\packages\\", "outputPath": "E:\\Code\\HRSystem\\Coldairarrow.IBusiness\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\vs\\gongxiang\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["d:\\Users\\lijf\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"E:\\Code\\HRSystem\\Aliyun.Base\\Aliyun.Base.csproj": {"projectPath": "E:\\Code\\HRSystem\\Aliyun.Base\\Aliyun.Base.csproj"}, "E:\\Code\\HRSystem\\Coldairarrow.Entity\\02.Coldairarrow.Entity.csproj": {"projectPath": "E:\\Code\\HRSystem\\Coldairarrow.Entity\\02.Coldairarrow.Entity.csproj"}, "E:\\Code\\HRSystem\\Coldairarrow.Util\\01.Coldairarrow.Util.csproj": {"projectPath": "E:\\Code\\HRSystem\\Coldairarrow.Util\\01.Coldairarrow.Util.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "E:\\Code\\HRSystem\\Coldairarrow.Util\\01.Coldairarrow.Util.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Code\\HRSystem\\Coldairarrow.Util\\01.Coldairarrow.Util.csproj", "projectName": "Coldairarrow.Util", "projectPath": "E:\\Code\\HRSystem\\Coldairarrow.Util\\01.Coldairarrow.Util.csproj", "packagesPath": "d:\\Users\\lijf\\.nuget\\packages\\", "outputPath": "E:\\Code\\HRSystem\\Coldairarrow.Util\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\vs\\gongxiang\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["d:\\Users\\lijf\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"E:\\Code\\HRSystem\\Aliyun.Base\\Aliyun.Base.csproj": {"projectPath": "E:\\Code\\HRSystem\\Aliyun.Base\\Aliyun.Base.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"AlibabaCloud.SDK.Waf-openapi20211001": {"target": "Package", "version": "[4.4.0, )"}, "AutoMapper": {"target": "Package", "version": "[10.0.0, )"}, "CSRedisCore": {"target": "Package", "version": "[3.6.5, )"}, "Caching.CSRedis": {"target": "Package", "version": "[3.6.5, )"}, "Castle.Core.AsyncInterceptor": {"target": "Package", "version": "[1.7.0, )"}, "EFCore.Sharding.SqlServer": {"target": "Package", "version": "[3.1.8.4, )"}, "IdHelper.Zookeeper": {"target": "Package", "version": "[1.5.1, )"}, "Microsoft.AspNetCore": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.2.5, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[3.1.8, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[3.1.8, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[3.1.8, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[3.1.8, )"}, "Microsoft.Extensions.Primitives": {"target": "Package", "version": "[3.1.32, )"}, "MySqlConnector": {"target": "Package", "version": "[0.69.9, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "NPOI": {"target": "Package", "version": "[2.5.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "NodaTime": {"target": "Package", "version": "[3.0.0, )"}, "Npgsql": {"target": "Package", "version": "[4.0.16, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[2.19.180, )"}, "QRCoder": {"target": "Package", "version": "[1.3.9, )"}, "Quartz": {"target": "Package", "version": "[3.1.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[3.4.0, )"}, "Serilog.Sinks.ElasticSearch": {"target": "Package", "version": "[8.4.1, )"}, "SharpZipLib": {"target": "Package", "version": "[1.3.3, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.16, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[4.7.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}, "System.Drawing.Common": {"target": "Package", "version": "[4.7.3, )"}, "System.Linq.Dynamic.Core": {"target": "Package", "version": "[1.3.14, )"}, "System.ServiceModel.Primitives": {"target": "Package", "version": "[4.7.0, )"}, "System.Text.Encoding.CodePages": {"target": "Package", "version": "[4.7.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}}}