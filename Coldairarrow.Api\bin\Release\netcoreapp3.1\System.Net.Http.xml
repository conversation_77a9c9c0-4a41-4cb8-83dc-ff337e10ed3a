﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Http</name>
  </assembly>
  <members>
    <member name="T:System.Net.Http.ByteArrayContent">
      <summary>Provides HTTP content based on a byte array.</summary>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.ByteArrayContent" /> class.</summary>
      <param name="content">The content used to initialize the <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="content" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[],System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.ByteArrayContent" /> class.</summary>
      <param name="content">The content used to initialize the <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <param name="offset">The offset, in bytes, in the <paramref name="content" /> parameter used to initialize the <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <param name="count">The number of bytes in the <paramref name="content" /> starting from the <paramref name="offset" /> parameter used to initialize the <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="content" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> parameter is less than zero.
-or-
The <paramref name="offset" /> parameter is greater than the length of content specified by the <paramref name="content" /> parameter.
-or-
The <paramref name="count" /> parameter is less than zero.
-or-
The <paramref name="count" /> parameter is greater than the length of content specified by the <paramref name="content" /> parameter - minus the <paramref name="offset" /> parameter.</exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.CreateContentReadStreamAsync">
      <summary>Creates an HTTP content stream as an asynchronous operation for reading whose backing store is memory from the <see cref="T:System.Net.Http.ByteArrayContent" />.</summary>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialize and write the byte array provided in the constructor to an HTTP content stream as an asynchronous operation.</summary>
      <param name="stream">The target stream.</param>
      <param name="context">Information about the transport, like channel binding token. This parameter may be <see langword="null" />.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.TryComputeLength(System.Int64@)">
      <summary>Determines whether a byte array has a valid length in bytes.</summary>
      <param name="length">The length in bytes of the byte array.</param>
      <returns>
        <see langword="true" /> if <paramref name="length" /> is a valid length; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.ClientCertificateOption">
      <summary>Specifies how client certificates are provided.</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Automatic">
      <summary>The <see cref="T:System.Net.Http.HttpClientHandler" /> will attempt to provide  all available client certificates  automatically.</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Manual">
      <summary>The application manually provides the client certificates to the <see cref="T:System.Net.Http.WebRequestHandler" />. This value is the default.</summary>
    </member>
    <member name="T:System.Net.Http.DelegatingHandler">
      <summary>A type for HTTP handlers that delegate the processing of HTTP response messages to another handler, called the inner handler.</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Net.Http.DelegatingHandler" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Creates a new instance of the <see cref="T:System.Net.Http.DelegatingHandler" /> class with a specific inner handler.</summary>
      <param name="innerHandler">The inner handler which is responsible for processing the HTTP response messages.</param>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.DelegatingHandler" />, and optionally disposes of the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to releases only unmanaged resources.</param>
    </member>
    <member name="P:System.Net.Http.DelegatingHandler.InnerHandler">
      <summary>Gets or sets the inner handler which processes the HTTP response messages.</summary>
      <returns>The inner handler for HTTP response messages.</returns>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Sends an HTTP request to the inner handler to send to the server as an asynchronous operation.</summary>
      <param name="request">The HTTP request message to send to the server.</param>
      <param name="cancellationToken">A cancellation token to cancel operation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="request" /> was <see langword="null" />.</exception>
    </member>
    <member name="T:System.Net.Http.FormUrlEncodedContent">
      <summary>A container for name/value tuples encoded using application/x-www-form-urlencoded MIME type.</summary>
    </member>
    <member name="M:System.Net.Http.FormUrlEncodedContent.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.FormUrlEncodedContent" /> class with a specific collection of name/value pairs.</summary>
      <param name="nameValueCollection">A collection of name/value pairs.</param>
    </member>
    <member name="T:System.Net.Http.Headers.AuthenticationHeaderValue">
      <summary>Represents authentication information in Authorization, ProxyAuthorization, WWW-Authenticate, and Proxy-Authenticate header values.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> class.</summary>
      <param name="scheme">The scheme to use for authorization.</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> class.</summary>
      <param name="scheme">The scheme to use for authorization.</param>
      <param name="parameter">The credentials containing the authentication information of the user agent for the resource being requested.</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an  <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Parameter">
      <summary>Gets the credentials containing the authentication information of the user agent for the resource being requested.</summary>
      <returns>The credentials containing the authentication information.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> instance.</summary>
      <param name="input">A string that represents authentication header value information.</param>
      <returns>An <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid authentication header value information.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Scheme">
      <summary>Gets the scheme to use for authorization.</summary>
      <returns>The scheme to use for authorization.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.TryParse(System.String,System.Net.Http.Headers.AuthenticationHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.CacheControlHeaderValue">
      <summary>Represents the value of the Cache-Control header.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Extensions">
      <summary>Cache-extension tokens, each with an optional assigned value.</summary>
      <returns>A collection of cache-extension tokens each with an optional assigned value.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.GetHashCode">
      <summary>Serves as a hash function for a  <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxAge">
      <summary>The maximum age, specified in seconds, that the HTTP client is willing to accept a response.</summary>
      <returns>The time in seconds.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStale">
      <summary>Whether an HTTP client is willing to accept a response that has exceeded its expiration time.</summary>
      <returns>
        <see langword="true" /> if the HTTP client is willing to accept a response that has exceed the expiration time; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStaleLimit">
      <summary>The maximum time, in seconds, an HTTP client is willing to accept a response that has exceeded its expiration time.</summary>
      <returns>The time in seconds.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MinFresh">
      <summary>The freshness lifetime, in seconds, that an HTTP client is willing to accept a response.</summary>
      <returns>The time in seconds.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MustRevalidate">
      <summary>Whether the origin server require revalidation of a cache entry on any subsequent use when the cache entry becomes stale.</summary>
      <returns>
        <see langword="true" /> if the origin server requires revalidation of a cache entry on any subsequent use when the entry becomes stale; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCache">
      <summary>Whether an HTTP client is willing to accept a cached response.</summary>
      <returns>
        <see langword="true" /> if the HTTP client is not willing to accept a cached response; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCacheHeaders">
      <summary>A collection of fieldnames in the "no-cache" directive in a cache-control header field on an HTTP response.</summary>
      <returns>A collection of fieldnames.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoStore">
      <summary>Whether a cache must not store any part of either the HTTP request message or any response.</summary>
      <returns>
        <see langword="true" /> if a cache must not store any part of either the HTTP request message or any response; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoTransform">
      <summary>Whether a cache or proxy must not change any aspect of the entity-body.</summary>
      <returns>
        <see langword="true" /> if a cache or proxy must not change any aspect of the entity-body; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.OnlyIfCached">
      <summary>Whether a cache should either respond using a cached entry that is consistent with the other constraints of the HTTP request, or respond with a 504 (Gateway Timeout) status.</summary>
      <returns>
        <see langword="true" /> if a cache should either respond using a cached entry that is consistent with the other constraints of the HTTP request, or respond with a 504 (Gateway Timeout) status; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> instance.</summary>
      <param name="input">A string that represents cache-control header value information.</param>
      <returns>A <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid cache-control header value information.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Private">
      <summary>Whether all or part of the HTTP response message is intended for a single user and must not be cached by a shared cache.</summary>
      <returns>
        <see langword="true" /> if the HTTP response message is intended for a single user and must not be cached by a shared cache; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.PrivateHeaders">
      <summary>A collection fieldnames in the "private" directive in a cache-control header field on an HTTP response.</summary>
      <returns>A collection of fieldnames.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.ProxyRevalidate">
      <summary>Whether the origin server require revalidation of a cache entry on any subsequent use when the cache entry becomes stale for shared user agent caches.</summary>
      <returns>
        <see langword="true" /> if the origin server requires revalidation of a cache entry on any subsequent use when the entry becomes stale for shared user agent caches; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Public">
      <summary>Whether an HTTP response may be cached by any cache, even if it would normally be non-cacheable or cacheable only within a non- shared cache.</summary>
      <returns>
        <see langword="true" /> if the HTTP response may be cached by any cache, even if it would normally be non-cacheable or cacheable only within a non- shared cache; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.SharedMaxAge">
      <summary>The shared maximum age, specified in seconds, in an HTTP response that overrides the "max-age" directive in a cache-control header or an Expires header for a shared cache.</summary>
      <returns>The time in seconds.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.TryParse(System.String,System.Net.Http.Headers.CacheControlHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.ContentDispositionHeaderValue">
      <summary>Represents the value of the Content-Disposition header.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.Net.Http.Headers.ContentDispositionHeaderValue)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> class.</summary>
      <param name="source">A <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> class.</summary>
      <param name="dispositionType">A string that contains a <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.CreationDate">
      <summary>The date at which   the file was created.</summary>
      <returns>The file creation date.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.DispositionType">
      <summary>The disposition type for a content body part.</summary>
      <returns>The disposition type.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileName">
      <summary>A suggestion for how to construct a filename for   storing the message payload to be used if the entity is   detached and stored in a separate file.</summary>
      <returns>A suggested filename.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileNameStar">
      <summary>A suggestion for how to construct filenames for   storing message payloads to be used if the entities are    detached and stored in a separate files.</summary>
      <returns>A suggested filename of the form filename*.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an  <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ModificationDate">
      <summary>The date at   which the file was last modified.</summary>
      <returns>The file modification date.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Name">
      <summary>The name for a content body part.</summary>
      <returns>The name for the content body part.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Parameters">
      <summary>A set of parameters included the Content-Disposition header.</summary>
      <returns>A collection of parameters.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> instance.</summary>
      <param name="input">A string that represents content disposition header value information.</param>
      <returns>An <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid content disposition header value information.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ReadDate">
      <summary>The date the file was last read.</summary>
      <returns>The last read date.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Size">
      <summary>The approximate size, in bytes, of the file.</summary>
      <returns>The approximate size, in bytes.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentDispositionHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.ContentRangeHeaderValue">
      <summary>Represents the value of the Content-Range header.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> class.</summary>
      <param name="length">The starting or ending point of the range, in bytes.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> class.</summary>
      <param name="from">The position, in bytes, at which to start sending data.</param>
      <param name="to">The position, in bytes, at which to stop sending data.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64,System.Int64)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> class.</summary>
      <param name="from">The position, in bytes, at which to start sending data.</param>
      <param name="to">The position, in bytes, at which to stop sending data.</param>
      <param name="length">The starting or ending point of the range, in bytes.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified Object is equal to the current <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.From">
      <summary>Gets the position at which to start sending data.</summary>
      <returns>The position, in bytes, at which to start sending data.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasLength">
      <summary>Gets whether the Content-Range header has a length specified.</summary>
      <returns>
        <see langword="true" /> if the Content-Range has a length specified; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasRange">
      <summary>Gets whether the Content-Range has a range specified.</summary>
      <returns>
        <see langword="true" /> if the Content-Range has a range specified; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Length">
      <summary>Gets the length of the full entity-body.</summary>
      <returns>The length of the full entity-body.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> instance.</summary>
      <param name="input">A string that represents content range header value information.</param>
      <returns>An <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid content range header value information.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.To">
      <summary>Gets the position at which to stop sending data.</summary>
      <returns>The position at which to stop sending data.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentRangeHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Unit">
      <summary>The range units used.</summary>
      <returns>A <see cref="T:System.String" /> that contains range units.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.EntityTagHeaderValue">
      <summary>Represents an entity-tag header value.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> class.</summary>
      <param name="tag">A string that contains an <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> class.</summary>
      <param name="tag">A string that contains an  <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</param>
      <param name="isWeak">A value that indicates if this entity-tag header is a weak validator. If the entity-tag header is weak validator, then <paramref name="isWeak" /> should be set to <see langword="true" />. If the entity-tag header is a strong validator, then <paramref name="isWeak" /> should be set to <see langword="false" />.</param>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Any">
      <summary>Gets the entity-tag header value.</summary>
      <returns>Returns <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.IsWeak">
      <summary>Gets whether the entity-tag is prefaced by a weakness indicator.</summary>
      <returns>
        <see langword="true" /> if the entity-tag is prefaced by a weakness indicator; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> instance.</summary>
      <param name="input">A string that represents entity tag header value information.</param>
      <returns>An <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid entity tag header value information.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Tag">
      <summary>Gets the opaque quoted string.</summary>
      <returns>An opaque quoted string.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.TryParse(System.String,System.Net.Http.Headers.EntityTagHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpContentHeaders">
      <summary>Represents the collection of Content Headers as defined in RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Allow">
      <summary>Gets the value of the <see langword="Allow" /> content header on an HTTP response.</summary>
      <returns>The value of the <see langword="Allow" /> header on an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentDisposition">
      <summary>Gets the value of the <see langword="Content-Disposition" /> content header on an HTTP response.</summary>
      <returns>The value of the <see langword="Content-Disposition" /> content header on an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentEncoding">
      <summary>Gets the value of the <see langword="Content-Encoding" /> content header on an HTTP response.</summary>
      <returns>The value of the <see langword="Content-Encoding" /> content header on an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLanguage">
      <summary>Gets the value of the <see langword="Content-Language" /> content header on an HTTP response.</summary>
      <returns>The value of the <see langword="Content-Language" /> content header on an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLength">
      <summary>Gets or sets the value of the <see langword="Content-Length" /> content header on an HTTP response.</summary>
      <returns>The value of the <see langword="Content-Length" /> content header on an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLocation">
      <summary>Gets or sets the value of the <see langword="Content-Location" /> content header on an HTTP response.</summary>
      <returns>The value of the <see langword="Content-Location" /> content header on an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentMD5">
      <summary>Gets or sets the value of the <see langword="Content-MD5" /> content header on an HTTP response.</summary>
      <returns>The value of the <see langword="Content-MD5" /> content header on an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentRange">
      <summary>Gets or sets the value of the <see langword="Content-Range" /> content header on an HTTP response.</summary>
      <returns>The value of the <see langword="Content-Range" /> content header on an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentType">
      <summary>Gets or sets the value of the <see langword="Content-Type" /> content header on an HTTP response.</summary>
      <returns>The value of the <see langword="Content-Type" /> content header on an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Expires">
      <summary>Gets or sets the value of the <see langword="Expires" /> content header on an HTTP response.</summary>
      <returns>The value of the <see langword="Expires" /> content header on an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.LastModified">
      <summary>Gets or sets the value of the <see langword="Last-Modified" /> content header on an HTTP response.</summary>
      <returns>The value of the <see langword="Last-Modified" /> content header on an HTTP response.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaders">
      <summary>A collection of headers and their values as defined in RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.HttpHeaders" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Adds the specified header and its values into the <see cref="T:System.Net.Http.Headers.HttpHeaders" /> collection.</summary>
      <param name="name">The header to add to the collection.</param>
      <param name="values">A list of header values to add to the collection.</param>
      <exception cref="T:System.ArgumentException">The name cannot be null or empty.</exception>
      <exception cref="T:System.ArgumentNullException">The values cannot be null or empty.</exception>
      <exception cref="T:System.InvalidOperationException">Misused header name. Make sure request headers are used with HttpRequestMessage, response headers with HttpResponseMessage, and content headers with HttpContent objects.</exception>
      <exception cref="T:System.FormatException">The header name format is invalid.
-or-
New line characters in header values must be followed by a white-space character.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.String)">
      <summary>Adds the specified header and its value into the <see cref="T:System.Net.Http.Headers.HttpHeaders" /> collection.</summary>
      <param name="name">The header to add to the collection.</param>
      <param name="value">The content of the header.</param>
      <exception cref="T:System.ArgumentException">The name cannot be null or empty.</exception>
      <exception cref="T:System.InvalidOperationException">Misused header name. Make sure request headers are used with HttpRequestMessage, response headers with HttpResponseMessage, and content headers with HttpContent objects.</exception>
      <exception cref="T:System.FormatException">The header name format is invalid.
-or-
New line characters in header values must be followed by a white-space character.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Clear">
      <summary>Removes all headers from the <see cref="T:System.Net.Http.Headers.HttpHeaders" /> collection.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Contains(System.String)">
      <summary>Returns if  a specific header exists in the <see cref="T:System.Net.Http.Headers.HttpHeaders" /> collection.</summary>
      <param name="name">The specific header.</param>
      <returns>
        <see langword="true" /> is the specified header exists in the collection; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetEnumerator">
      <summary>Returns an enumerator that can iterate through the <see cref="T:System.Net.Http.Headers.HttpHeaders" /> instance.</summary>
      <returns>An enumerator for the <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetValues(System.String)">
      <summary>Returns all header values for a specified header stored in the <see cref="T:System.Net.Http.Headers.HttpHeaders" /> collection.</summary>
      <param name="name">The specified header to return values for.</param>
      <returns>An array of header strings.</returns>
      <exception cref="T:System.InvalidOperationException">The header cannot be found.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Remove(System.String)">
      <summary>Removes the specified header from the <see cref="T:System.Net.Http.Headers.HttpHeaders" /> collection.</summary>
      <param name="name">The name of the header to remove from the collection.</param>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gets an enumerator that can iterate through a <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>An instance of an implementation of an <see cref="T:System.Collections.IEnumerator" /> that can iterate through a <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.HttpHeaders" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Returns a value that indicates whether the specified header and its values were added to the <see cref="T:System.Net.Http.Headers.HttpHeaders" /> collection without validating the provided information.</summary>
      <param name="name">The header to add to the collection.</param>
      <param name="values">The values of the header.</param>
      <returns>
        <see langword="true" /> if the specified header <paramref name="name" /> and <paramref name="values" /> could be added to the collection; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.String)">
      <summary>Returns a value that indicates whether the specified header and its value were added to the <see cref="T:System.Net.Http.Headers.HttpHeaders" /> collection without validating the provided information.</summary>
      <param name="name">The header to add to the collection.</param>
      <param name="value">The content of the header.</param>
      <returns>
        <see langword="true" /> if the specified header <paramref name="name" /> and <paramref name="value" /> could be added to the collection; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
      <summary>Return if a specified header and specified values are stored in the <see cref="T:System.Net.Http.Headers.HttpHeaders" /> collection.</summary>
      <param name="name">The specified header.</param>
      <param name="values">The specified header values.</param>
      <returns>
        <see langword="true" /> is the specified header <paramref name="name" /> and <see langword="values" /> are stored in the collection; otherwise <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaderValueCollection`1">
      <summary>Represents a collection of header values.</summary>
      <typeparam name="T">The header collection type.</typeparam>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Add(`0)">
      <summary>Adds an entry to the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <param name="item">The item to add to the header collection.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Clear">
      <summary>Removes all entries from the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Contains(`0)">
      <summary>Determines if the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> contains an item.</summary>
      <param name="item">The item to find to the header collection.</param>
      <returns>
        <see langword="true" /> if the entry is contained in the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> instance; otherwise, <see langword="false" /></returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.CopyTo(`0[],System.Int32)">
      <summary>Copies the entire <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> to a compatible one-dimensional <see cref="T:System.Array" />, starting at the specified index of the target array.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.Count">
      <summary>Gets the number of headers in the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>The number of headers in a collection</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>An enumerator for the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> instance.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> instance is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> instance is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ParseAdd(System.String)">
      <summary>Parses and adds an entry to the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <param name="input">The entry to add.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Remove(`0)">
      <summary>Removes the specified item from the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <param name="item">The item to remove.</param>
      <returns>
        <see langword="true" /> if the <paramref name="item" /> was removed from the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> instance; otherwise, <see langword="false" /></returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>An enumerator for the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> object. object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.TryParseAdd(System.String)">
      <summary>Determines whether the input could be parsed and added to the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <param name="input">The entry to validate.</param>
      <returns>
        <see langword="true" /> if the <paramref name="input" /> could be parsed and added to the <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> instance; otherwise, <see langword="false" /></returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpRequestHeaders">
      <summary>Represents the collection of Request Headers as defined in RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Accept">
      <summary>Gets the value of the <see langword="Accept" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Accept" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptCharset">
      <summary>Gets the value of the <see langword="Accept-Charset" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Accept-Charset" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptEncoding">
      <summary>Gets the value of the <see langword="Accept-Encoding" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Accept-Encoding" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptLanguage">
      <summary>Gets the value of the <see langword="Accept-Language" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Accept-Language" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Authorization">
      <summary>Gets or sets the value of the <see langword="Authorization" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Authorization" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.CacheControl">
      <summary>Gets or sets the value of the <see langword="Cache-Control" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Cache-Control" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Connection">
      <summary>Gets the value of the <see langword="Connection" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Connection" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ConnectionClose">
      <summary>Gets or sets a value that indicates if the <see langword="Connection" /> header for an HTTP request contains Close.</summary>
      <returns>
        <see langword="true" /> if the <see langword="Connection" /> header contains Close, otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Date">
      <summary>Gets or sets the value of the <see langword="Date" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Date" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Expect">
      <summary>Gets the value of the <see langword="Expect" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Expect" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ExpectContinue">
      <summary>Gets or sets a value that indicates if the <see langword="Expect" /> header for an HTTP request contains Continue.</summary>
      <returns>
        <see langword="true" /> if the <see langword="Expect" /> header contains Continue, otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.From">
      <summary>Gets or sets the value of the <see langword="From" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="From" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Host">
      <summary>Gets or sets the value of the <see langword="Host" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Host" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfMatch">
      <summary>Gets the value of the <see langword="If-Match" /> header for an HTTP request.</summary>
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.
The value of the <see langword="If-Match" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfModifiedSince">
      <summary>Gets or sets the value of the <see langword="If-Modified-Since" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="If-Modified-Since" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfNoneMatch">
      <summary>Gets the value of the <see langword="If-None-Match" /> header for an HTTP request.</summary>
      <returns>Gets the value of the <see langword="If-None-Match" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfRange">
      <summary>Gets or sets the value of the <see langword="If-Range" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="If-Range" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfUnmodifiedSince">
      <summary>Gets or sets the value of the <see langword="If-Unmodified-Since" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="If-Unmodified-Since" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.MaxForwards">
      <summary>Gets or sets the value of the <see langword="Max-Forwards" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Max-Forwards" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Pragma">
      <summary>Gets the value of the <see langword="Pragma" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Pragma" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ProxyAuthorization">
      <summary>Gets or sets the value of the <see langword="Proxy-Authorization" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Proxy-Authorization" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Range">
      <summary>Gets or sets the value of the <see langword="Range" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Range" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Referrer">
      <summary>Gets or sets the value of the <see langword="Referer" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Referer" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TE">
      <summary>Gets the value of the <see langword="TE" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="TE" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Trailer">
      <summary>Gets the value of the <see langword="Trailer" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Trailer" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncoding">
      <summary>Gets the value of the <see langword="Transfer-Encoding" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Transfer-Encoding" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncodingChunked">
      <summary>Gets or sets a value that indicates if the <see langword="Transfer-Encoding" /> header for an HTTP request contains chunked.</summary>
      <returns>
        <see langword="true" /> if the <see langword="Transfer-Encoding" /> header contains chunked, otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Upgrade">
      <summary>Gets the value of the <see langword="Upgrade" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Upgrade" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.UserAgent">
      <summary>Gets the value of the <see langword="User-Agent" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="User-Agent" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Via">
      <summary>Gets the value of the <see langword="Via" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Via" /> header for an HTTP request.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Warning">
      <summary>Gets the value of the <see langword="Warning" /> header for an HTTP request.</summary>
      <returns>The value of the <see langword="Warning" /> header for an HTTP request.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpResponseHeaders">
      <summary>Represents the collection of Response Headers as defined in RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.AcceptRanges">
      <summary>Gets the value of the <see langword="Accept-Ranges" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Accept-Ranges" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Age">
      <summary>Gets or sets the value of the <see langword="Age" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Age" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.CacheControl">
      <summary>Gets or sets the value of the <see langword="Cache-Control" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Cache-Control" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Connection">
      <summary>Gets the value of the <see langword="Connection" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Connection" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ConnectionClose">
      <summary>Gets or sets a value that indicates if the <see langword="Connection" /> header for an HTTP response contains Close.</summary>
      <returns>
        <see langword="true" /> if the <see langword="Connection" /> header contains Close, otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Date">
      <summary>Gets or sets the value of the <see langword="Date" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Date" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ETag">
      <summary>Gets or sets the value of the <see langword="ETag" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="ETag" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Location">
      <summary>Gets or sets the value of the <see langword="Location" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Location" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Pragma">
      <summary>Gets the value of the <see langword="Pragma" /> header for an HTTP response.</summary>
      <returns>Returns <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.
The value of the <see langword="Pragma" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ProxyAuthenticate">
      <summary>Gets the value of the <see langword="Proxy-Authenticate" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Proxy-Authenticate" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.RetryAfter">
      <summary>Gets or sets the value of the <see langword="Retry-After" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Retry-After" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Server">
      <summary>Gets the value of the <see langword="Server" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Server" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Trailer">
      <summary>Gets the value of the <see langword="Trailer" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Trailer" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncoding">
      <summary>Gets the value of the <see langword="Transfer-Encoding" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Transfer-Encoding" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncodingChunked">
      <summary>Gets or sets a value that indicates if the <see langword="Transfer-Encoding" /> header for an HTTP response contains chunked.</summary>
      <returns>
        <see langword="true" /> if the <see langword="Transfer-Encoding" /> header contains chunked, otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Upgrade">
      <summary>Gets the value of the <see langword="Upgrade" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Upgrade" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Vary">
      <summary>Gets the value of the <see langword="Vary" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Vary" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Via">
      <summary>Gets the value of the <see langword="Via" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Via" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Warning">
      <summary>Gets the value of the <see langword="Warning" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="Warning" /> header for an HTTP response.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.WwwAuthenticate">
      <summary>Gets the value of the <see langword="WWW-Authenticate" /> header for an HTTP response.</summary>
      <returns>The value of the <see langword="WWW-Authenticate" /> header for an HTTP response.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeHeaderValue">
      <summary>Represents a media type used in a Content-Type header as defined in the RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> class.</summary>
      <param name="source">A <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> object used to initialize the new instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> class.</summary>
      <param name="mediaType">The source represented as a string to initialize the new instance.</param>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.CharSet">
      <summary>Gets or sets the character set.</summary>
      <returns>The character set.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.MediaType">
      <summary>Gets or sets the media-type header value.</summary>
      <returns>The media-type header value.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.Parameters">
      <summary>Gets or sets the media-type header value parameters.</summary>
      <returns>The media-type header value parameters.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> instance.</summary>
      <param name="input">A string that represents media type header value information.</param>
      <returns>A <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid media type header value information.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue">
      <summary>Represents a media type with an additional quality factor used in a Content-Type header.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> class.</summary>
      <param name="mediaType">A <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> represented as string to initialize the new instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> class.</summary>
      <param name="mediaType">A <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> represented as string to initialize the new instance.</param>
      <param name="quality">The quality associated with this header value.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> instance.</summary>
      <param name="input">A string that represents media type with quality header value information.</param>
      <returns>A <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid media type with quality header value information.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Quality">
      <summary>Gets or sets the quality value for the <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <returns>The quality value for the <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeWithQualityHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueHeaderValue">
      <summary>Represents a name/value pair used in various headers as defined in RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.Net.Http.Headers.NameValueHeaderValue)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> class.</summary>
      <param name="source">A <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> object used to initialize the new instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> class.</summary>
      <param name="name">The header name.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> class.</summary>
      <param name="name">The header name.</param>
      <param name="value">The header value.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Name">
      <summary>Gets the header name.</summary>
      <returns>The header name.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> instance.</summary>
      <param name="input">A string that represents name value header value information.</param>
      <returns>A <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid name value header value information.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Value">
      <summary>Gets the header value.</summary>
      <returns>The header value.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue">
      <summary>Represents a name/value pair with parameters used in various headers as defined in RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.Net.Http.Headers.NameValueWithParametersHeaderValue)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> class.</summary>
      <param name="source">A <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> object used to initialize the new instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> class.</summary>
      <param name="name">The header name.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> class.</summary>
      <param name="name">The header name.</param>
      <param name="value">The header value.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parameters">
      <summary>Gets the parameters from the <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> object.</summary>
      <returns>A collection containing the parameters.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> instance.</summary>
      <param name="input">A string that represents name value with parameter header value information.</param>
      <returns>A <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid name value with parameter header value information.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueWithParametersHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.ProductHeaderValue">
      <summary>Represents a product token value in a User-Agent header.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> class.</summary>
      <param name="name">The product name.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> class.</summary>
      <param name="name">The product name value.</param>
      <param name="version">The product version value.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Name">
      <summary>Gets the name of the product token.</summary>
      <returns>The name of the product token.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> instance.</summary>
      <param name="input">A string that represents product header value information.</param>
      <returns>A <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Version">
      <summary>Gets the version of the product token.</summary>
      <returns>The version of the product token.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.ProductInfoHeaderValue">
      <summary>Represents a value which can either be a product or a comment in a User-Agent header.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.Net.Http.Headers.ProductHeaderValue)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> class.</summary>
      <param name="product">A <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> object used to initialize the new instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> class.</summary>
      <param name="comment">A comment value.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> class.</summary>
      <param name="productName">The product name value.</param>
      <param name="productVersion">The product version value.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Comment">
      <summary>Gets the comment from the <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> object.</summary>
      <returns>The comment value this <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> instance.</summary>
      <param name="input">A string that represents product info header value information.</param>
      <returns>A <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid product info header value information.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Product">
      <summary>Gets the product from the <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> object.</summary>
      <returns>The product value from this <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductInfoHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RangeConditionHeaderValue">
      <summary>Represents an If-Range header value which can either be a date/time or an entity-tag value.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> class.</summary>
      <param name="date">A date value used to initialize the new instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.Net.Http.Headers.EntityTagHeaderValue)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> class.</summary>
      <param name="entityTag">An <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> object used to initialize the new instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> class.</summary>
      <param name="entityTag">An entity tag represented as a string used to initialize the new instance.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.Date">
      <summary>Gets the date from the <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> object.</summary>
      <returns>The date from the <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> object.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.EntityTag">
      <summary>Gets the entity tag from the <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> object.</summary>
      <returns>The entity tag from the <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> instance.</summary>
      <param name="input">A string that represents range condition header value information.</param>
      <returns>A <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid range Condition header value information.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeConditionHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RangeHeaderValue">
      <summary>Represents a Range header value.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> class with a byte range.</summary>
      <param name="from">The position at which to start sending data.</param>
      <param name="to">The position at which to stop sending data.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="from" /> is greater than <paramref name="to" />
-or-
<paramref name="from" /> or <paramref name="to" /> is less than 0.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> instance.</summary>
      <param name="input">A string that represents range header value information.</param>
      <returns>A <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid range header value information.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Ranges">
      <summary>Gets the ranges specified from the <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> object.</summary>
      <returns>The ranges from the <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> information.</summary>
      <param name="input">he string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Unit">
      <summary>Gets the unit from the <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> object.</summary>
      <returns>The unit from the <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> object.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RangeItemHeaderValue">
      <summary>Represents a byte range in a Range header value.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> class.</summary>
      <param name="from">The position at which to start sending data.</param>
      <param name="to">The position at which to stop sending data.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="from" /> is greater than <paramref name="to" />
-or-
<paramref name="from" /> or <paramref name="to" /> is less than 0.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.From">
      <summary>Gets the position at which to start sending data.</summary>
      <returns>The position at which to start sending data.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.To">
      <summary>Gets the position at which to stop sending data.</summary>
      <returns>The position at which to stop sending data.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RetryConditionHeaderValue">
      <summary>Represents a Retry-After header value which can either be a date/time or a timespan value.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> class.</summary>
      <param name="date">The date and time offset used to initialize the new instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.TimeSpan)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> class.</summary>
      <param name="delta">The delta, in seconds, used to initialize the new instance.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Date">
      <summary>Gets the date and time offset from the <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> object.</summary>
      <returns>The date and time offset from the <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> object.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Delta">
      <summary>Gets the delta in seconds from the <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> object.</summary>
      <returns>The delta in seconds from the <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> instance.</summary>
      <param name="input">A string that represents retry condition header value information.</param>
      <returns>A <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid retry condition header value information.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RetryConditionHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.StringWithQualityHeaderValue">
      <summary>Represents a string header value with an optional quality.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> class.</summary>
      <param name="value">The string used to initialize the new instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> class.</summary>
      <param name="value">A string used to initialize the new instance.</param>
      <param name="quality">A quality factor used to initialize the new instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified Object is equal to the current <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> instance.</summary>
      <param name="input">A string that represents quality header value information.</param>
      <returns>A <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid string with quality header value information.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Quality">
      <summary>Gets the quality factor from the <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> object.</summary>
      <returns>The quality factor from the <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.StringWithQualityHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Value">
      <summary>Gets the string value from the <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> object.</summary>
      <returns>The string value from the <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> object.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingHeaderValue">
      <summary>Represents an accept-encoding header value.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.Net.Http.Headers.TransferCodingHeaderValue)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> class.</summary>
      <param name="source">A <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> object used to initialize the new instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> class.</summary>
      <param name="value">A string used to initialize the new instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified Object is equal to the current <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Parameters">
      <summary>Gets the transfer-coding parameters.</summary>
      <returns>The transfer-coding parameters.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> instance.</summary>
      <param name="input">A string that represents transfer-coding header value information.</param>
      <returns>A <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid transfer-coding header value information.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Value">
      <summary>Gets the transfer-coding value.</summary>
      <returns>The transfer-coding value.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue">
      <summary>Represents an Accept-Encoding header value.with optional quality factor.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> class.</summary>
      <param name="value">A string used to initialize the new instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> class.</summary>
      <param name="value">A string used to initialize the new instance.</param>
      <param name="quality">A value for the quality factor.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> instance.</summary>
      <param name="input">A string that represents transfer-coding value information.</param>
      <returns>A <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid transfer-coding with quality header value information.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Quality">
      <summary>Gets the quality factor from the <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</summary>
      <returns>The quality factor from the <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingWithQualityHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.ViaHeaderValue">
      <summary>Represents the value of a Via header.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> class.</summary>
      <param name="protocolVersion">The protocol version of the received protocol.</param>
      <param name="receivedBy">The host and port that the request or response was received by.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> class.</summary>
      <param name="protocolVersion">The protocol version of the received protocol.</param>
      <param name="receivedBy">The host and port that the request or response was received by.</param>
      <param name="protocolName">The protocol name of the received protocol.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> class.</summary>
      <param name="protocolVersion">The protocol version of the received protocol.</param>
      <param name="receivedBy">The host and port that the request or response was received by.</param>
      <param name="protocolName">The protocol name of the received protocol.</param>
      <param name="comment">The comment field used to identify the software of the recipient proxy or gateway.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.Comment">
      <summary>Gets the comment field used to identify the software of the recipient proxy or gateway.</summary>
      <returns>The comment field used to identify the software of the recipient proxy or gateway.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> instance.</summary>
      <param name="input">A string that represents via header value information.</param>
      <returns>A <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid via header value information.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolName">
      <summary>Gets the protocol name of the received protocol.</summary>
      <returns>The protocol name.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolVersion">
      <summary>Gets the protocol version of the received protocol.</summary>
      <returns>The protocol version.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ReceivedBy">
      <summary>Gets the host and port that the request or response was received by.</summary>
      <returns>The host and port that the request or response was received by.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.TryParse(System.String,System.Net.Http.Headers.ViaHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.WarningHeaderValue">
      <summary>Represents a warning value used by the Warning header.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> class.</summary>
      <param name="code">The specific warning code.</param>
      <param name="agent">The host that attached the warning.</param>
      <param name="text">A quoted-string containing the warning text.</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String,System.DateTimeOffset)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> class.</summary>
      <param name="code">The specific warning code.</param>
      <param name="agent">The host that attached the warning.</param>
      <param name="text">A quoted-string containing the warning text.</param>
      <param name="date">The date/time stamp of the warning.</param>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Agent">
      <summary>Gets the host that attached the warning.</summary>
      <returns>The host that attached the warning.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Code">
      <summary>Gets the specific warning code.</summary>
      <returns>The specific warning code.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Date">
      <summary>Gets the date/time stamp of the warning.</summary>
      <returns>The date/time stamp of the warning.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> object.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.GetHashCode">
      <summary>Serves as a hash function for an <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Parse(System.String)">
      <summary>Converts a string to an <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> instance.</summary>
      <param name="input">A string that represents authentication header value information.</param>
      <returns>Returns a <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> is a <see langword="null" /> reference.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> is not valid authentication header value information.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> instance.</summary>
      <returns>Returns a copy of the current instance.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Text">
      <summary>Gets a quoted-string containing the warning text.</summary>
      <returns>A quoted-string containing the warning text.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.TryParse(System.String,System.Net.Http.Headers.WarningHeaderValue@)">
      <summary>Determines whether a string is valid <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> information.</summary>
      <param name="input">The string to validate.</param>
      <param name="parsedValue">The <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="input" /> is valid <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.HttpClient">
      <summary>Provides a base class for sending HTTP requests and receiving HTTP responses from a resource identified by a URI.</summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpClient" /> class using a <see cref="T:System.Net.Http.HttpClientHandler" /> that is disposed when this instance is disposed.</summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpClient" /> class with the specified handler. The handler is disposed when this instance is disposed.</summary>
      <param name="handler">The HTTP handler stack to use for sending requests.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="handler" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpClient" /> class with the provided handler, and specifies whether that handler should be disposed when this instance is disposed.</summary>
      <param name="handler">The <see cref="T:System.Net.Http.HttpMessageHandler" /> responsible for processing the HTTP response messages.</param>
      <param name="disposeHandler">
        <see langword="true" /> if the inner handler should be disposed of by HttpClient.Dispose; <see langword="false" /> if you intend to reuse the inner handler.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="handler" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.BaseAddress">
      <summary>Gets or sets the base address of Uniform Resource Identifier (URI) of the Internet resource used when sending requests.</summary>
      <returns>The base address of Uniform Resource Identifier (URI) of the Internet resource used when sending requests.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.CancelPendingRequests">
      <summary>Cancel all pending requests on this instance.</summary>
    </member>
    <member name="P:System.Net.Http.HttpClient.DefaultProxy">
      <summary>Gets or sets the global Http proxy.</summary>
      <returns>A proxy used by every call that instantiates a <see cref="T:System.Net.HttpWebRequest" />.</returns>
      <exception cref="T:System.ArgumentNullException">The value passed cannot be <see langword="null" />.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.DefaultRequestHeaders">
      <summary>Gets the headers which should be sent with each request.</summary>
      <returns>The headers which should be sent with each request.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClient.DefaultRequestVersion">
      <summary>Gets or sets the default HTTP version used on subsequent requests made by this <see cref="T:System.Net.Http.HttpClient" /> instance.</summary>
      <returns>The default version to use for any requests made with this <see cref="T:System.Net.Http.HttpClient" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">In a set operation, <see langword="DefaultRequestVersion" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Http.HttpClient" /> instance has already started one or more requests.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Http.HttpClient" /> instance has already been disposed.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String)">
      <summary>Send a DELETE request to the specified Uri as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String,System.Threading.CancellationToken)">
      <summary>Send a DELETE request to the specified Uri with a cancellation token as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri)">
      <summary>Send a DELETE request to the specified Uri as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>Send a DELETE request to the specified Uri with a cancellation token as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.HttpClient" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to releases only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String)">
      <summary>Send a GET request to the specified Uri as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption)">
      <summary>Send a GET request to the specified Uri with an HTTP completion option as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="completionOption">An HTTP completion option value that indicates when the operation should be considered completed.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Send a GET request to the specified Uri with an HTTP completion option and a cancellation token as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="completionOption">An HTTP  completion option value that indicates when the operation should be considered completed.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Threading.CancellationToken)">
      <summary>Send a GET request to the specified Uri with a cancellation token as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri)">
      <summary>Send a GET request to the specified Uri as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption)">
      <summary>Send a GET request to the specified Uri with an HTTP completion option as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="completionOption">An HTTP completion option value that indicates when the operation should be considered completed.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Send a GET request to the specified Uri with an HTTP completion option and a cancellation token as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="completionOption">An HTTP  completion option value that indicates when the operation should be considered completed.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>Send a GET request to the specified Uri with a cancellation token as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.String)">
      <summary>Sends a GET request to the specified Uri and return the response body as a byte array in an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.Uri)">
      <summary>Send a GET request to the specified Uri and return the response body as a byte array in an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.String)">
      <summary>Send a GET request to the specified Uri and return the response body as a stream in an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.Uri)">
      <summary>Send a GET request to the specified Uri and return the response body as a stream in an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.String)">
      <summary>Send a GET request to the specified Uri and return the response body as a string in an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.Uri)">
      <summary>Send a GET request to the specified Uri and return the response body as a string in an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.MaxResponseContentBufferSize">
      <summary>Gets or sets the maximum number of bytes to buffer when reading the response content.</summary>
      <returns>The maximum number of bytes to buffer when reading the response content. The default value for this property is 2 gigabytes.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The size specified is less than or equal to zero.</exception>
      <exception cref="T:System.InvalidOperationException">An operation has already been started on the current instance.</exception>
      <exception cref="T:System.ObjectDisposedException">The current instance has been disposed.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PatchAsync(System.String,System.Net.Http.HttpContent)">
      <summary>Sends a PATCH request to a Uri designated as a string as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.PatchAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Sends a PATCH request with a cancellation token to a Uri represented as a string as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.PatchAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>Sends a PATCH request as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.PatchAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Sends a PATCH request with a cancellation token as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent)">
      <summary>Send a POST request to the specified Uri as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Send a POST request with a cancellation token as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>Send a POST request to the specified Uri as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Send a POST request with a cancellation token as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent)">
      <summary>Send a PUT request to the specified Uri as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Send a PUT request with a cancellation token as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>Send a PUT request to the specified Uri as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Send a PUT request with a cancellation token as an asynchronous operation.</summary>
      <param name="requestUri">The Uri the request is sent to.</param>
      <param name="content">The HTTP request content sent to the server.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="requestUri" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage)">
      <summary>Send an HTTP request as an asynchronous operation.</summary>
      <param name="request">The HTTP request message to send.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="request" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption)">
      <summary>Send an HTTP request as an asynchronous operation.</summary>
      <param name="request">The HTTP request message to send.</param>
      <param name="completionOption">When the operation should complete (as soon as a response is available or after reading the whole response content).</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="request" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Send an HTTP request as an asynchronous operation.</summary>
      <param name="request">The HTTP request message to send.</param>
      <param name="completionOption">When the operation should complete (as soon as a response is available or after reading the whole response content).</param>
      <param name="cancellationToken">The cancellation token to cancel operation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="request" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Send an HTTP request as an asynchronous operation.</summary>
      <param name="request">The HTTP request message to send.</param>
      <param name="cancellationToken">The cancellation token to cancel operation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="request" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The request message was already sent by the <see cref="T:System.Net.Http.HttpClient" /> instance.</exception>
      <exception cref="T:System.Net.Http.HttpRequestException">The request failed due to an underlying issue such as network connectivity, DNS failure, server certificate validation or timeout.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.Timeout">
      <summary>Gets or sets the timespan to wait before the request times out.</summary>
      <returns>The timespan to wait before the request times out.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The timeout specified is less than or equal to zero and is not <see cref="F:System.Threading.Timeout.InfiniteTimeSpan" />.</exception>
      <exception cref="T:System.InvalidOperationException">An operation has already been started on the current instance.</exception>
      <exception cref="T:System.ObjectDisposedException">The current instance has been disposed.</exception>
    </member>
    <member name="T:System.Net.Http.HttpClientHandler">
      <summary>The default message handler used by <see cref="T:System.Net.Http.HttpClient" /> in .NET Framework and .NET Core 2.0 and earlier.</summary>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.#ctor">
      <summary>Creates an instance of a <see cref="T:System.Net.Http.HttpClientHandler" /> class.</summary>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect">
      <summary>Gets or sets a value that indicates whether the handler should follow redirection responses.</summary>
      <returns>
        <see langword="true" /> if the handler should follow redirection responses; otherwise <see langword="false" />. The default value is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AutomaticDecompression">
      <summary>Gets or sets the type of decompression method used by the handler for automatic decompression of the HTTP content response.</summary>
      <returns>The automatic decompression method used by the handler.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.CheckCertificateRevocationList">
      <summary>Gets or sets a value that indicates whether the certificate is checked against the certificate authority revocation list.</summary>
      <returns>
        <see langword="true" /> if the certificate revocation list is checked; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.PlatformNotSupportedException">.NET Framework 4.7.1 only: This property is not implemented.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.ClientCertificateOptions">
      <summary>Gets or sets a value that indicates if the certificate is automatically picked from the certificate store or if the caller is allowed to pass in a specific client certificate.</summary>
      <returns>The collection of security certificates associated with this handler.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.ClientCertificates">
      <summary>Gets the collection of security certificates that are associated requests to the server.</summary>
      <returns>The X509CertificateCollection that is presented to the server when performing certificate based client authentication.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.CookieContainer">
      <summary>Gets or sets the cookie container used to store server cookies by the handler.</summary>
      <returns>The cookie container used to store server cookies by the handler.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Credentials">
      <summary>Gets or sets authentication information used by this handler.</summary>
      <returns>The authentication credentials associated with the handler. The default is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.DangerousAcceptAnyServerCertificateValidator">
      <summary>Gets a cached delegate that always returns <see langword="true" />.</summary>
      <returns>A cached delegate that always returns <see langword="true" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.DefaultProxyCredentials">
      <summary>When the default (system) proxy is being used, gets or sets the credentials to submit to the default proxy server for authentication. The default proxy is used only when <see cref="P:System.Net.Http.HttpClientHandler.UseProxy" /> is set to <see langword="true" /> and <see cref="P:System.Net.Http.HttpClientHandler.Proxy" /> is set to <see langword="null" />.</summary>
      <returns>The credentials needed to authenticate a request to the default proxy server.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.HttpClientHandler" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to releases only unmanaged resources.</param>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections">
      <summary>Gets or sets the maximum number of redirects that the handler follows.</summary>
      <returns>The maximum number of redirection responses that the handler follows. The default value is 50.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxConnectionsPerServer">
      <summary>Gets or sets the maximum number of concurrent connections (per server endpoint) allowed when making requests using an <see cref="T:System.Net.Http.HttpClient" /> object. Note that the limit is per server endpoint, so for example a value of 256 would permit 256 concurrent connections to http://www.adatum.com/ and another 256 to http://www.adventure-works.com/.</summary>
      <returns>The maximum number of concurrent connections (per server endpoint) allowed by an <see cref="T:System.Net.Http.HttpClient" /> object.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxRequestContentBufferSize">
      <summary>Gets or sets the maximum request content buffer size used by the handler.</summary>
      <returns>The maximum request content buffer size in bytes. The default value is 2 gigabytes.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxResponseHeadersLength">
      <summary>Gets or sets the maximum length, in kilobytes (1024 bytes), of the response headers. For example, if the value is 64, then 65536 bytes are allowed for the maximum response headers' length.</summary>
      <returns>The maximum length, in kilobytes (1024 bytes), of the response headers.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.PreAuthenticate">
      <summary>Gets or sets a value that indicates whether the handler sends an Authorization header with the request.</summary>
      <returns>
        <see langword="true" /> for the handler to send an HTTP Authorization header with requests after authentication has taken place; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Properties">
      <summary>Gets a writable dictionary (that is, a map) of custom properties for the <see cref="T:System.Net.Http.HttpClient" /> requests. The dictionary is initialized empty; you can insert and query key-value pairs for your custom handlers and special processing.</summary>
      <returns>a writable dictionary of custom properties.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Proxy">
      <summary>Gets or sets proxy information used by the handler.</summary>
      <returns>The proxy information used by the handler. The default value is <see langword="null" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Creates an instance of  <see cref="T:System.Net.Http.HttpResponseMessage" /> based on the information provided in the <see cref="T:System.Net.Http.HttpRequestMessage" /> as an operation that will not block.</summary>
      <param name="request">The HTTP request message.</param>
      <param name="cancellationToken">A cancellation token to cancel the operation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="request" /> was <see langword="null" />.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.ServerCertificateCustomValidationCallback">
      <summary>Gets or sets a callback method to validate the server certificate.</summary>
      <returns>A callback method to validate the server certificate.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SslProtocols">
      <summary>Gets or sets the TLS/SSL protocol used by the <see cref="T:System.Net.Http.HttpClient" /> objects managed by the HttpClientHandler object.</summary>
      <returns>One of the values defined in the <see cref="T:System.Security.Authentication.SslProtocols" /> enumeration.</returns>
      <exception cref="T:System.PlatformNotSupportedException">.NET Framework 4.7.1 only: This property is not implemented.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsAutomaticDecompression">
      <summary>Gets a value that indicates whether the handler supports automatic response content decompression.</summary>
      <returns>
        <see langword="true" /> if the if the handler supports automatic response content decompression; otherwise <see langword="false" />. The default value is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsProxy">
      <summary>Gets a value that indicates whether the handler supports proxy settings.</summary>
      <returns>
        <see langword="true" /> if the if the handler supports proxy settings; otherwise <see langword="false" />. The default value is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsRedirectConfiguration">
      <summary>Gets a value that indicates whether the handler supports configuration settings for the <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> and <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" /> properties.</summary>
      <returns>
        <see langword="true" /> if the if the handler supports configuration settings for the <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> and <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" /> properties; otherwise <see langword="false" />. The default value is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseCookies">
      <summary>Gets or sets a value that indicates whether the handler uses the  <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> property  to store server cookies and uses these cookies when sending requests.</summary>
      <returns>
        <see langword="true" /> if the if the handler supports uses the  <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> property  to store server cookies and uses these cookies when sending requests; otherwise <see langword="false" />. The default value is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseDefaultCredentials">
      <summary>Gets or sets a value that controls whether default credentials are sent with requests by the handler.</summary>
      <returns>
        <see langword="true" /> if the default credentials are used; otherwise <see langword="false" />. The default value is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseProxy">
      <summary>Gets or sets a value that indicates whether the handler uses a proxy for requests.</summary>
      <returns>
        <see langword="true" /> if the handler should use a proxy for requests; otherwise <see langword="false" />. The default value is <see langword="true" />.</returns>
    </member>
    <member name="T:System.Net.Http.HttpCompletionOption">
      <summary>Indicates if <see cref="T:System.Net.Http.HttpClient" /> operations should be considered completed either as soon as a response is available, or after reading the entire response message including the content.</summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseContentRead">
      <summary>The operation should complete after reading the entire response including the content.</summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseHeadersRead">
      <summary>The operation should complete as soon as a response is available and headers are read. The content is not read yet.</summary>
    </member>
    <member name="T:System.Net.Http.HttpContent">
      <summary>A base class representing an HTTP entity body and content headers.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpContent" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream)">
      <summary>Serialize the HTTP content into a stream of bytes and copies it to the stream object provided as the <paramref name="stream" /> parameter.</summary>
      <param name="stream">The target stream.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialize the HTTP content into a stream of bytes and copies it to the stream object provided as the <paramref name="stream" /> parameter.</summary>
      <param name="stream">The target stream.</param>
      <param name="context">Information about the transport (channel binding token, for example). This parameter may be <see langword="null" />.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.CreateContentReadStreamAsync">
      <summary>Serialize the HTTP content to a memory stream as an asynchronous operation.</summary>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose">
      <summary>Releases the unmanaged resources and disposes of the managed resources used by the <see cref="T:System.Net.Http.HttpContent" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.HttpContent" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to releases only unmanaged resources.</param>
    </member>
    <member name="P:System.Net.Http.HttpContent.Headers">
      <summary>Gets the HTTP content headers as defined in RFC 2616.</summary>
      <returns>The content headers as defined in RFC 2616.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync">
      <summary>Serialize the HTTP content to a memory buffer as an asynchronous operation.</summary>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync(System.Int64)">
      <summary>Serialize the HTTP content to a memory buffer as an asynchronous operation.</summary>
      <param name="maxBufferSize">The maximum size, in bytes, of the buffer to use.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsByteArrayAsync">
      <summary>Serialize the HTTP content to a byte array as an asynchronous operation.</summary>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStreamAsync">
      <summary>Serialize the HTTP content and return a stream that represents the content as an asynchronous operation.</summary>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStringAsync">
      <summary>Serialize the HTTP content to a string as an asynchronous operation.</summary>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialize the HTTP content to a stream as an asynchronous operation.</summary>
      <param name="stream">The target stream.</param>
      <param name="context">Information about the transport (channel binding token, for example). This parameter may be <see langword="null" />.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.TryComputeLength(System.Int64@)">
      <summary>Determines whether the HTTP content has a valid length in bytes.</summary>
      <param name="length">The length in bytes of the HTTP content.</param>
      <returns>
        <see langword="true" /> if <paramref name="length" /> is a valid length; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.HttpMessageHandler">
      <summary>A base type for HTTP message handlers.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpMessageHandler" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose">
      <summary>Releases the unmanaged resources and disposes of the managed resources used by the <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.HttpMessageHandler" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to releases only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Send an HTTP request as an asynchronous operation.</summary>
      <param name="request">The HTTP request message to send.</param>
      <param name="cancellationToken">The cancellation token to cancel operation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="request" /> was <see langword="null" />.</exception>
    </member>
    <member name="T:System.Net.Http.HttpMessageInvoker">
      <summary>A specialty class that allows applications to call the <see cref="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)" /> method on an HTTP handler chain.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Initializes an instance of a <see cref="T:System.Net.Http.HttpMessageInvoker" /> class with a specific <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
      <param name="handler">The <see cref="T:System.Net.Http.HttpMessageHandler" /> responsible for processing the HTTP response messages.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>Initializes an instance of a <see cref="T:System.Net.Http.HttpMessageInvoker" /> class with a specific <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
      <param name="handler">The <see cref="T:System.Net.Http.HttpMessageHandler" /> responsible for processing the HTTP response messages.</param>
      <param name="disposeHandler">
        <see langword="true" /> if the inner handler should be disposed of by Dispose(), <see langword="false" /> if you intend to reuse the inner handler.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose">
      <summary>Releases the unmanaged resources and disposes of the managed resources used by the <see cref="T:System.Net.Http.HttpMessageInvoker" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.HttpMessageInvoker" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to releases only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Send an HTTP request as an asynchronous operation.</summary>
      <param name="request">The HTTP request message to send.</param>
      <param name="cancellationToken">The cancellation token to cancel operation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="request" /> was <see langword="null" />.</exception>
    </member>
    <member name="T:System.Net.Http.HttpMethod">
      <summary>A helper class for retrieving and comparing standard HTTP methods and for creating new HTTP methods.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMethod.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpMethod" /> class with a specific HTTP method.</summary>
      <param name="method">The HTTP method.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Delete">
      <summary>Represents an HTTP DELETE protocol method.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Net.Http.HttpMethod)">
      <summary>Determines whether the specified <see cref="T:System.Net.Http.HttpMethod" /> is equal to the current <see cref="T:System.Object" />.</summary>
      <param name="other">The HTTP method to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified object is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Object" />.</summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified object is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Get">
      <summary>Represents an HTTP GET protocol method.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.GetHashCode">
      <summary>Serves as a hash function for this type.</summary>
      <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Head">
      <summary>Represents an HTTP HEAD protocol method. The HEAD method is identical to GET except that the server only returns message-headers in the response, without a message-body.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Method">
      <summary>An HTTP method.</summary>
      <returns>An HTTP method represented as a <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Equality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>The equality operator for comparing two <see cref="T:System.Net.Http.HttpMethod" /> objects.</summary>
      <param name="left">The left <see cref="T:System.Net.Http.HttpMethod" /> to an equality operator.</param>
      <param name="right">The right  <see cref="T:System.Net.Http.HttpMethod" /> to an equality operator.</param>
      <returns>
        <see langword="true" /> if the specified <paramref name="left" /> and <paramref name="right" /> parameters are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Inequality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>The inequality operator for comparing two <see cref="T:System.Net.Http.HttpMethod" /> objects.</summary>
      <param name="left">The left <see cref="T:System.Net.Http.HttpMethod" /> to an inequality operator.</param>
      <param name="right">The right  <see cref="T:System.Net.Http.HttpMethod" /> to an inequality operator.</param>
      <returns>
        <see langword="true" /> if the specified <paramref name="left" /> and <paramref name="right" /> parameters are inequal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Options">
      <summary>Represents an HTTP OPTIONS protocol method.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Patch" />
    <member name="P:System.Net.Http.HttpMethod.Post">
      <summary>Represents an HTTP POST protocol method that is used to post a new entity as an addition to a URI.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Put">
      <summary>Represents an HTTP PUT protocol method that is used to replace an entity identified by a URI.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.ToString">
      <summary>Returns a string that represents the current object.</summary>
      <returns>A string representing the current object.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Trace">
      <summary>Represents an HTTP TRACE protocol method.</summary>
      <returns>Returns <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="T:System.Net.Http.HttpRequestException">
      <summary>A base class for exceptions thrown by the <see cref="T:System.Net.Http.HttpClient" /> and <see cref="T:System.Net.Http.HttpMessageHandler" /> classes.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpRequestException" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpRequestException" /> class with a specific message that describes the current exception.</summary>
      <param name="message">A message that describes the current exception.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpRequestException" /> class with a specific message that describes the current exception and an inner exception.</summary>
      <param name="message">A message that describes the current exception.</param>
      <param name="inner">The inner exception.</param>
    </member>
    <member name="T:System.Net.Http.HttpRequestMessage">
      <summary>Represents a HTTP request message.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpRequestMessage" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpRequestMessage" /> class with an HTTP method and a request <see cref="T:System.Uri" />.</summary>
      <param name="method">The HTTP method.</param>
      <param name="requestUri">A string that represents the request  <see cref="T:System.Uri" />.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.Uri)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpRequestMessage" /> class with an HTTP method and a request <see cref="T:System.Uri" />.</summary>
      <param name="method">The HTTP method.</param>
      <param name="requestUri">The <see cref="T:System.Uri" /> to request.</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Content">
      <summary>Gets or sets the contents of the HTTP message.</summary>
      <returns>The content of a message</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose">
      <summary>Releases the unmanaged resources and disposes of the managed resources used by the <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.HttpRequestMessage" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to releases only unmanaged resources.</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Headers">
      <summary>Gets the collection of HTTP request headers.</summary>
      <returns>The collection of HTTP request headers.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Method">
      <summary>Gets or sets the HTTP method used by the HTTP request message.</summary>
      <returns>The HTTP method used by the request message. The default is the GET method.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Properties">
      <summary>Gets a set of properties for the HTTP request.</summary>
      <returns>Returns <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.RequestUri">
      <summary>Gets or sets the <see cref="T:System.Uri" /> used for the HTTP request.</summary>
      <returns>The <see cref="T:System.Uri" /> used for the HTTP request.</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.ToString">
      <summary>Returns a string that represents the current object.</summary>
      <returns>A string representation of the current object.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Version">
      <summary>Gets or sets the HTTP message version.</summary>
      <returns>The HTTP message version. The default value is <c>1.1</c>, unless you're targeting .NET Core 2.1 or 2.2. In that case, the default value is <c>2.0</c>.</returns>
    </member>
    <member name="T:System.Net.Http.HttpResponseMessage">
      <summary>Represents a HTTP response message including the status code and data.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpResponseMessage" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor(System.Net.HttpStatusCode)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Http.HttpResponseMessage" /> class with a specific <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" />.</summary>
      <param name="statusCode">The status code of the HTTP response.</param>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Content">
      <summary>Gets or sets the content of a HTTP response message.</summary>
      <returns>The content of the HTTP response message.</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose">
      <summary>Releases the unmanaged resources and disposes of unmanaged resources used by the <see cref="T:System.Net.Http.HttpResponseMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.HttpResponseMessage" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to releases only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode">
      <summary>Throws an exception if the <see cref="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode" /> property for the HTTP response is <see langword="false" />.</summary>
      <returns>The HTTP response message if the call is successful.</returns>
      <exception cref="T:System.Net.Http.HttpRequestException">The HTTP response is unsuccessful.</exception>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Headers">
      <summary>Gets the collection of HTTP response headers.</summary>
      <returns>The collection of HTTP response headers.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode">
      <summary>Gets a value that indicates if the HTTP response was successful.</summary>
      <returns>
        <see langword="true" /> if <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" /> was in the range 200-299; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.ReasonPhrase">
      <summary>Gets or sets the reason phrase which typically is sent by servers together with the status code.</summary>
      <returns>The reason phrase sent by the server.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.RequestMessage">
      <summary>Gets or sets the request message which led to this response message.</summary>
      <returns>The request message which led to this response message.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.StatusCode">
      <summary>Gets or sets the status code of the HTTP response.</summary>
      <returns>The status code of the HTTP response.</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.ToString">
      <summary>Returns a string that represents the current object.</summary>
      <returns>A string representation of the current object.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.TrailingHeaders">
      <summary>Gets the collection of trailing headers included in an HTTP response.</summary>
      <returns>The collection of trailing headers in the HTTP response.</returns>
      <exception cref="T:System.Net.Http.HttpRequestException">PROTOCOL_ERROR: The HTTP/2 response contains pseudo-headers in the Trailing Headers Frame.</exception>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Version">
      <summary>Gets or sets the HTTP message version.</summary>
      <returns>The HTTP message version. The default is 1.1.</returns>
    </member>
    <member name="T:System.Net.Http.MessageProcessingHandler">
      <summary>A base type for handlers which only do some small processing of request and/or response messages.</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor">
      <summary>Creates an instance of a <see cref="T:System.Net.Http.MessageProcessingHandler" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Creates an instance of a <see cref="T:System.Net.Http.MessageProcessingHandler" /> class with a specific inner handler.</summary>
      <param name="innerHandler">The inner handler which is responsible for processing the HTTP response messages.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessRequest(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Performs processing on each request sent to the server.</summary>
      <param name="request">The HTTP request message to process.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The HTTP request message that was processed.</returns>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessResponse(System.Net.Http.HttpResponseMessage,System.Threading.CancellationToken)">
      <summary>Perform processing on each response from the server.</summary>
      <param name="response">The HTTP response message to process.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The HTTP response message that was processed.</returns>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Sends an HTTP request to the inner handler to send to the server as an asynchronous operation.</summary>
      <param name="request">The HTTP request message to send to the server.</param>
      <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="request" /> was <see langword="null" />.</exception>
    </member>
    <member name="T:System.Net.Http.MultipartContent">
      <summary>Provides a collection of <see cref="T:System.Net.Http.HttpContent" /> objects that get serialized using the multipart/* content type specification.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Net.Http.MultipartContent" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String)">
      <summary>Creates a new instance of the <see cref="T:System.Net.Http.MultipartContent" /> class.</summary>
      <param name="subtype">The subtype of the multipart content.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="subtype" /> was <see langword="null" /> or contains only white space characters.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String,System.String)">
      <summary>Creates a new instance of the <see cref="T:System.Net.Http.MultipartContent" /> class.</summary>
      <param name="subtype">The subtype of the multipart content.</param>
      <param name="boundary">The boundary string for the multipart content.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="subtype" /> was <see langword="null" /> or an empty string.
The <paramref name="boundary" /> was <see langword="null" /> or contains only white space characters.
-or-
The <paramref name="boundary" /> ends with a space character.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The length of the <paramref name="boundary" /> was greater than 70.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Add(System.Net.Http.HttpContent)">
      <summary>Add multipart HTTP content to a collection of <see cref="T:System.Net.Http.HttpContent" /> objects that get serialized using the multipart/* content type specification.</summary>
      <param name="content">The HTTP content to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="content" /> was <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.CreateContentReadStreamAsync">
      <summary>Serializes the HTTP content to a stream using the multipart/* encoding as an asynchronous operation.</summary>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.MultipartContent" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to releases only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection of <see cref="T:System.Net.Http.HttpContent" /> objects that get serialized using the multipart/* content type specification.</summary>
      <returns>An object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialize the multipart HTTP content to a stream as an asynchronous operation.</summary>
      <param name="stream">The target stream.</param>
      <param name="context">Information about the transport (channel binding token, for example). This parameter may be <see langword="null" />.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.System#Collections#IEnumerable#GetEnumerator">
      <summary>The explicit implementation of the <see cref="M:System.Net.Http.MultipartContent.GetEnumerator" /> method.</summary>
      <returns>An object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.TryComputeLength(System.Int64@)">
      <summary>Determines whether the HTTP multipart content has a valid length in bytes.</summary>
      <param name="length">The length in bytes of the HHTP content.</param>
      <returns>
        <see langword="true" /> if <paramref name="length" /> is a valid length; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.MultipartFormDataContent">
      <summary>Provides a container for content encoded using multipart/form-data MIME type.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Net.Http.MultipartFormDataContent" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor(System.String)">
      <summary>Creates a new instance of the <see cref="T:System.Net.Http.MultipartFormDataContent" /> class.</summary>
      <param name="boundary">The boundary string for the multipart form data content.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="boundary" /> was <see langword="null" /> or contains only white space characters.
-or-
The <paramref name="boundary" /> ends with a space character.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The length of the <paramref name="boundary" /> was greater than 70.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent)">
      <summary>Add HTTP content to a collection of <see cref="T:System.Net.Http.HttpContent" /> objects that get serialized to multipart/form-data MIME type.</summary>
      <param name="content">The HTTP content to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="content" /> was <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String)">
      <summary>Add HTTP content to a collection of <see cref="T:System.Net.Http.HttpContent" /> objects that get serialized to multipart/form-data MIME type.</summary>
      <param name="content">The HTTP content to add to the collection.</param>
      <param name="name">The name for the HTTP content to add.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="name" /> was <see langword="null" /> or contains only white space characters.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="content" /> was <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String,System.String)">
      <summary>Add HTTP content to a collection of <see cref="T:System.Net.Http.HttpContent" /> objects that get serialized to multipart/form-data MIME type.</summary>
      <param name="content">The HTTP content to add to the collection.</param>
      <param name="name">The name for the HTTP content to add.</param>
      <param name="fileName">The file name for the HTTP content to add to the collection.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="name" /> was <see langword="null" /> or contains only white space characters.
-or-
The <paramref name="fileName" /> was <see langword="null" /> or contains only white space characters.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="content" /> was <see langword="null" />.</exception>
    </member>
    <member name="T:System.Net.Http.ReadOnlyMemoryContent" />
    <member name="M:System.Net.Http.ReadOnlyMemoryContent.#ctor(System.ReadOnlyMemory{System.Byte})">
      <param name="content" />
    </member>
    <member name="T:System.Net.Http.SocketsHttpHandler">
      <summary>Provides the default message handler used by <see cref="T:System.Net.Http.HttpClient" /> in .NET Core 2.1 and later.</summary>
    </member>
    <member name="M:System.Net.Http.SocketsHttpHandler.#ctor" />
    <member name="P:System.Net.Http.SocketsHttpHandler.AllowAutoRedirect" />
    <member name="P:System.Net.Http.SocketsHttpHandler.AutomaticDecompression">
      <summary>Gets or sets the type of decompression method used by the handler for automatic decompression of the HTTP content response.</summary>
      <returns>The type of decompression method used by the handler for automatic decompression of the HTTP content response.</returns>
    </member>
    <member name="P:System.Net.Http.SocketsHttpHandler.ConnectTimeout" />
    <member name="P:System.Net.Http.SocketsHttpHandler.CookieContainer">
      <summary>Gets or sets the managed cookie container object.</summary>
      <returns>The managed cookie container object.</returns>
    </member>
    <member name="P:System.Net.Http.SocketsHttpHandler.Credentials" />
    <member name="P:System.Net.Http.SocketsHttpHandler.DefaultProxyCredentials">
      <summary>When the default (system) proxy is used, gets or sets the credentials used to submit to the default proxy server for authentication.</summary>
      <returns>The credentials used to authenticate the user to an authenticating proxy.</returns>
    </member>
    <member name="P:System.Net.Http.SocketsHttpHandler.Expect100ContinueTimeout" />
    <member name="P:System.Net.Http.SocketsHttpHandler.MaxAutomaticRedirections">
      <summary>Gets or sets the maximum number of allowed HTTP redirects.</summary>
      <returns>The maximum number of allowed HTTP redirects.</returns>
    </member>
    <member name="P:System.Net.Http.SocketsHttpHandler.MaxConnectionsPerServer">
      <summary>Gets or sets the maximum number of simultaneous TCP connections allowed to a single server.</summary>
      <returns>The maximum number of simultaneous TCP connections allowed to a single server.</returns>
    </member>
    <member name="P:System.Net.Http.SocketsHttpHandler.MaxResponseDrainSize">
      <summary>Gets or sets the maximum amount of data that can be drained from responses in bytes.</summary>
      <returns>The maximum amount of data that can be drained from responses in bytes.</returns>
    </member>
    <member name="P:System.Net.Http.SocketsHttpHandler.MaxResponseHeadersLength">
      <summary>Gets or sets the maximum length, in kilobytes (1024 bytes), of the response headers.</summary>
      <returns>The maximum size of the header portion from the server response, in kilobytes.</returns>
    </member>
    <member name="P:System.Net.Http.SocketsHttpHandler.PooledConnectionIdleTimeout">
      <summary>Gets or sets how long a connection can be idle in the pool to be considered reusable.</summary>
      <returns>The maximum idle time for a connection in the pool. The default value for this property is 2 minutes.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified is less than <see cref="F:System.TimeSpan.Zero" /> or is equal to <see cref="F:System.Threading.Timeout.InfiniteTimeSpan" />.</exception>
    </member>
    <member name="P:System.Net.Http.SocketsHttpHandler.PooledConnectionLifetime" />
    <member name="P:System.Net.Http.SocketsHttpHandler.PreAuthenticate">
      <summary>Gets or sets a value that indicates whether the handler sends an Authorization header with the request.</summary>
      <returns>
        <see langword="true" /> if the handler sends an Authorization header with the request; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Http.SocketsHttpHandler.Properties" />
    <member name="P:System.Net.Http.SocketsHttpHandler.Proxy">
      <summary>Gets or sets the custom proxy when the <see cref="P:System.Net.Http.SocketsHttpHandler.UseProxy" /> property is <see langword="true" />.</summary>
      <returns>The custom proxy.</returns>
    </member>
    <member name="P:System.Net.Http.SocketsHttpHandler.ResponseDrainTimeout" />
    <member name="P:System.Net.Http.SocketsHttpHandler.SslOptions">
      <summary>Gets or sets the set of options used for client TLS authentication.</summary>
      <returns>The set of options used for client TLS authentication.</returns>
    </member>
    <member name="P:System.Net.Http.SocketsHttpHandler.UseCookies" />
    <member name="P:System.Net.Http.SocketsHttpHandler.UseProxy" />
    <member name="T:System.Net.Http.StreamContent">
      <summary>Provides HTTP content based on a stream.</summary>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream)">
      <summary>Creates a new instance of the <see cref="T:System.Net.Http.StreamContent" /> class.</summary>
      <param name="content">The content used to initialize the <see cref="T:System.Net.Http.StreamContent" />.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream,System.Int32)">
      <summary>Creates a new instance of the <see cref="T:System.Net.Http.StreamContent" /> class.</summary>
      <param name="content">The content used to initialize the <see cref="T:System.Net.Http.StreamContent" />.</param>
      <param name="bufferSize">The size, in bytes, of the buffer for the <see cref="T:System.Net.Http.StreamContent" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="content" /> was <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="bufferSize" /> was less than or equal to zero.</exception>
    </member>
    <member name="M:System.Net.Http.StreamContent.CreateContentReadStreamAsync">
      <summary>Write the HTTP stream content to a memory stream as an asynchronous operation.</summary>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.StreamContent.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Http.StreamContent" /> and optionally disposes of the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to releases only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialize the HTTP content to a stream as an asynchronous operation.</summary>
      <param name="stream">The target stream.</param>
      <param name="context">Information about the transport (channel binding token, for example). This parameter may be <see langword="null" />.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.Http.StreamContent.TryComputeLength(System.Int64@)">
      <summary>Determines whether the stream content has a valid length in bytes.</summary>
      <param name="length">The length in bytes of the stream content.</param>
      <returns>
        <see langword="true" /> if <paramref name="length" /> is a valid length; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Http.StringContent">
      <summary>Provides HTTP content based on a string.</summary>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String)">
      <summary>Creates a new instance of the <see cref="T:System.Net.Http.StringContent" /> class.</summary>
      <param name="content">The content used to initialize the <see cref="T:System.Net.Http.StringContent" />.</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding)">
      <summary>Creates a new instance of the <see cref="T:System.Net.Http.StringContent" /> class.</summary>
      <param name="content">The content used to initialize the <see cref="T:System.Net.Http.StringContent" />.</param>
      <param name="encoding">The encoding to use for the content.</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding,System.String)">
      <summary>Creates a new instance of the <see cref="T:System.Net.Http.StringContent" /> class.</summary>
      <param name="content">The content used to initialize the <see cref="T:System.Net.Http.StringContent" />.</param>
      <param name="encoding">The encoding to use for the content.</param>
      <param name="mediaType">The media type to use for the content.</param>
    </member>
  </members>
</doc>