﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Coldairarrow.Entity.HR_EmployeeInfoManage
{
    /// <summary>
    /// 员工离职
    /// </summary>
    [Table("HR_Departure")]
    public class HR_Departure
    {

        /// <summary>
        /// F_Id
        /// </summary>
        [Key, Column(Order = 1)]
        public String F_Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime F_CreateDate { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public String F_CreateUserId { get; set; }

        /// <summary>
        /// 创建人名
        /// </summary>
        public String F_CreateUserName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? F_ModifyDate { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public String F_ModifyUserId { get; set; }

        /// <summary>
        /// 修改人名
        /// </summary>
        public String F_ModifyUserName { get; set; }

        /// <summary>
        /// 流程Guid
        /// </summary>
        public String F_WFId { get; set; }

        /// <summary>
        /// 业务状态
        /// </summary>
        public Int32? F_BusState { get; set; }

        /// <summary>
        /// 流程状态
        /// </summary>
        public Int32? F_WFState { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public String F_Remark { get; set; }

        /// <summary>
        /// 编号
        /// </summary>
        public String F_Code { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public String F_UserId { get; set; }

        /// <summary>
        /// 原用工状态
        /// </summary>
        public String F_OriginalEmplStatus { get; set; }

        /// <summary>
        /// 目标用工状态
        /// </summary>
        public String F_MobilzOriEmplStatus { get; set; }

        /// <summary>
        /// 变动操作
        /// </summary>
        public String F_ChangesOperating { get; set; }

        /// <summary>
        /// 变动类型
        /// </summary>
        public String F_ChangesType { get; set; }

        /// <summary>
        /// 离职原因
        /// </summary>
        public String F_DepartureReason { get; set; }

        /// <summary>
        /// 预计离职生效日期
        /// </summary>
        public DateTime? F_DepartureDate { get; set; }

        /// <summary>
        /// 实际离职生效日期
        /// </summary>
        public DateTime? F_TrueDepartureDate { get; set; }
        /// <summary>
        /// 附件ID
        /// </summary>
        public string F_FileId { get; set; }
    }
}