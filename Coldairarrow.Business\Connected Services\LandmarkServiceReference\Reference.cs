﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//
//     对此文件的更改可能导致不正确的行为，并在以下条件下丢失:
//     代码重新生成。
// </auto-generated>
//------------------------------------------------------------------------------

namespace LandmarkServiceReference
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="LandmarkServiceReference.IService")]
    public interface IService
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/HelloWorld", ReplyAction="http://tempuri.org/IService/HelloWorldResponse")]
        System.Threading.Tasks.Task<string> HelloWorldAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/Send", ReplyAction="http://tempuri.org/IService/SendResponse")]
        System.Threading.Tasks.Task<string> SendAsync(string Mobile, string Content);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetWindowsAds", ReplyAction="http://tempuri.org/IService/GetWindowsAdsResponse")]
        System.Threading.Tasks.Task<string> GetWindowsAdsAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/SetWindowsAds", ReplyAction="http://tempuri.org/IService/SetWindowsAdsResponse")]
        System.Threading.Tasks.Task<string> SetWindowsAdsAsync(string loginName, string guid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/CheckLogin", ReplyAction="http://tempuri.org/IService/CheckLoginResponse")]
        System.Threading.Tasks.Task<string> CheckLoginAsync(string username, string password);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetCommissionByMonth", ReplyAction="http://tempuri.org/IService/GetCommissionByMonthResponse")]
        System.Threading.Tasks.Task<string> GetCommissionByMonthAsync(string month);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetCommissionByYear", ReplyAction="http://tempuri.org/IService/GetCommissionByYearResponse")]
        System.Threading.Tasks.Task<string> GetCommissionByYearAsync(string year);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetLoginToken", ReplyAction="http://tempuri.org/IService/GetLoginTokenResponse")]
        System.Threading.Tasks.Task<string> GetLoginTokenAsync(string loginName, string sysName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetUserList", ReplyAction="http://tempuri.org/IService/GetUserListResponse")]
        System.Threading.Tasks.Task<string> GetUserListAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetUserInfo", ReplyAction="http://tempuri.org/IService/GetUserInfoResponse")]
        System.Threading.Tasks.Task<string> GetUserInfoAsync(string usercode, string password);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetRoomInfo", ReplyAction="http://tempuri.org/IService/GetRoomInfoResponse")]
        System.Threading.Tasks.Task<string> GetRoomInfoAsync(string CstName, string CardId, string MobileTel);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetRoomStatus", ReplyAction="http://tempuri.org/IService/GetRoomStatusResponse")]
        System.Threading.Tasks.Task<string> GetRoomStatusAsync(System.Guid roomguid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetprojectInfo", ReplyAction="http://tempuri.org/IService/GetprojectInfoResponse")]
        System.Threading.Tasks.Task<string> GetprojectInfoAsync(string projectGuid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetBldInfo", ReplyAction="http://tempuri.org/IService/GetBldInfoResponse")]
        System.Threading.Tasks.Task<string> GetBldInfoAsync(string proGuid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetCRMProject", ReplyAction="http://tempuri.org/IService/GetCRMProjectResponse")]
        System.Threading.Tasks.Task<string> GetCRMProjectAsync(string proGuid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetSalesMonthData", ReplyAction="http://tempuri.org/IService/GetSalesMonthDataResponse")]
        System.Threading.Tasks.Task<string> GetSalesMonthDataAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetSalesData", ReplyAction="http://tempuri.org/IService/GetSalesDataResponse")]
        System.Threading.Tasks.Task<string> GetSalesDataAsync(System.Nullable<System.DateTime> dt);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetSalesDataAll", ReplyAction="http://tempuri.org/IService/GetSalesDataAllResponse")]
        System.Threading.Tasks.Task<string> GetSalesDataAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetCommissionData", ReplyAction="http://tempuri.org/IService/GetCommissionDataResponse")]
        System.Threading.Tasks.Task<string> GetCommissionDataAsync(int year, int month);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetmyFunction", ReplyAction="http://tempuri.org/IService/GetmyFunctionResponse")]
        System.Threading.Tasks.Task<string> GetmyFunctionAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetCostInfo", ReplyAction="http://tempuri.org/IService/GetCostInfoResponse")]
        System.Threading.Tasks.Task<string> GetCostInfoAsync(System.Guid projguid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetPayInfo", ReplyAction="http://tempuri.org/IService/GetPayInfoResponse")]
        System.Threading.Tasks.Task<string> GetPayInfoAsync(System.DateTime BgnDate, System.DateTime EndDate, string projguid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetApplyInfo", ReplyAction="http://tempuri.org/IService/GetApplyInfoResponse")]
        System.Threading.Tasks.Task<string> GetApplyInfoAsync(System.DateTime BgnDate, System.DateTime EndDate, string Contractname, string ApplyByName, string verb);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/Getcb_Contract", ReplyAction="http://tempuri.org/IService/Getcb_ContractResponse")]
        System.Threading.Tasks.Task<string> Getcb_ContractAsync(string Contractcode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetApplySubject", ReplyAction="http://tempuri.org/IService/GetApplySubjectResponse")]
        System.Threading.Tasks.Task<string> GetApplySubjectAsync(string Contractcode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/EntryRefundInfo", ReplyAction="http://tempuri.org/IService/EntryRefundInfoResponse")]
        System.Threading.Tasks.Task<string> EntryRefundInfoAsync(string RefundInfo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/EntryPayInvoice", ReplyAction="http://tempuri.org/IService/EntryPayInvoiceResponse")]
        System.Threading.Tasks.Task<string> EntryPayInvoiceAsync(string PayInvoice);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetContracts", ReplyAction="http://tempuri.org/IService/GetContractsResponse")]
        System.Threading.Tasks.Task<string> GetContractsAsync(string DocCode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetRoomContract", ReplyAction="http://tempuri.org/IService/GetRoomContractResponse")]
        System.Threading.Tasks.Task<string> GetRoomContractAsync(string roomguid, string phone);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetAmount", ReplyAction="http://tempuri.org/IService/GetAmountResponse")]
        System.Threading.Tasks.Task<string> GetAmountAsync(string salestr, string roomstr, string amountstr, string jkr);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetRoomList", ReplyAction="http://tempuri.org/IService/GetRoomListResponse")]
        System.Threading.Tasks.Task<string> GetRoomListAsync(string BldGUID, string keywords);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/GetRoomListByDate", ReplyAction="http://tempuri.org/IService/GetRoomListByDateResponse")]
        System.Threading.Tasks.Task<string> GetRoomListByDateAsync(string startStr, string endStr, string teamname, string projname, string bldguid);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/ERPdaiban", ReplyAction="http://tempuri.org/IService/ERPdaibanResponse")]
        System.Threading.Tasks.Task<string> ERPdaibanAsync(string usercode, string keywords, int pageSize, int pageIndex);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/ERPapprove", ReplyAction="http://tempuri.org/IService/ERPapproveResponse")]
        System.Threading.Tasks.Task<string> ERPapproveAsync(string usercode, string keywords, int pageSize, int pageIndex);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/ERPtodo", ReplyAction="http://tempuri.org/IService/ERPtodoResponse")]
        System.Threading.Tasks.Task<string> ERPtodoAsync(string usercode, string processname, string ProjectCompany, string qsdate, string enddate, int pageSize, int pageIndex);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/ERPdone", ReplyAction="http://tempuri.org/IService/ERPdoneResponse")]
        System.Threading.Tasks.Task<string> ERPdoneAsync(string usercode, string processname, string ProjectCompany, string qsdate, string enddate, int pageSize, int pageIndex);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/ERPraise", ReplyAction="http://tempuri.org/IService/ERPraiseResponse")]
        System.Threading.Tasks.Task<string> ERPraiseAsync(string usercode, string processname, string ProjectCompany, string qsdate, string enddate, int pageSize, int pageIndex);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService/ERPcc", ReplyAction="http://tempuri.org/IService/ERPccResponse")]
        System.Threading.Tasks.Task<string> ERPccAsync(string usercode, string processname, string ProjectCompany, string qsdate, string enddate, int pageSize, int pageIndex);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public interface IServiceChannel : LandmarkServiceReference.IService, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public partial class ServiceClient : System.ServiceModel.ClientBase<LandmarkServiceReference.IService>, LandmarkServiceReference.IService
    {
        
        /// <summary>
        /// 实现此分部方法，配置服务终结点。
        /// </summary>
        /// <param name="serviceEndpoint">要配置的终结点</param>
        /// <param name="clientCredentials">客户端凭据</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public ServiceClient() : 
                base(ServiceClient.GetDefaultBinding(), ServiceClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.BasicHttpBinding_IService.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ServiceClient(EndpointConfiguration endpointConfiguration) : 
                base(ServiceClient.GetBindingForEndpoint(endpointConfiguration), ServiceClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ServiceClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(ServiceClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ServiceClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(ServiceClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public ServiceClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public System.Threading.Tasks.Task<string> HelloWorldAsync()
        {
            return base.Channel.HelloWorldAsync();
        }
        
        public System.Threading.Tasks.Task<string> SendAsync(string Mobile, string Content)
        {
            return base.Channel.SendAsync(Mobile, Content);
        }
        
        public System.Threading.Tasks.Task<string> GetWindowsAdsAsync()
        {
            return base.Channel.GetWindowsAdsAsync();
        }
        
        public System.Threading.Tasks.Task<string> SetWindowsAdsAsync(string loginName, string guid)
        {
            return base.Channel.SetWindowsAdsAsync(loginName, guid);
        }
        
        public System.Threading.Tasks.Task<string> CheckLoginAsync(string username, string password)
        {
            return base.Channel.CheckLoginAsync(username, password);
        }
        
        public System.Threading.Tasks.Task<string> GetCommissionByMonthAsync(string month)
        {
            return base.Channel.GetCommissionByMonthAsync(month);
        }
        
        public System.Threading.Tasks.Task<string> GetCommissionByYearAsync(string year)
        {
            return base.Channel.GetCommissionByYearAsync(year);
        }
        
        public System.Threading.Tasks.Task<string> GetLoginTokenAsync(string loginName, string sysName)
        {
            return base.Channel.GetLoginTokenAsync(loginName, sysName);
        }
        
        public System.Threading.Tasks.Task<string> GetUserListAsync()
        {
            return base.Channel.GetUserListAsync();
        }
        
        public System.Threading.Tasks.Task<string> GetUserInfoAsync(string usercode, string password)
        {
            return base.Channel.GetUserInfoAsync(usercode, password);
        }
        
        public System.Threading.Tasks.Task<string> GetRoomInfoAsync(string CstName, string CardId, string MobileTel)
        {
            return base.Channel.GetRoomInfoAsync(CstName, CardId, MobileTel);
        }
        
        public System.Threading.Tasks.Task<string> GetRoomStatusAsync(System.Guid roomguid)
        {
            return base.Channel.GetRoomStatusAsync(roomguid);
        }
        
        public System.Threading.Tasks.Task<string> GetprojectInfoAsync(string projectGuid)
        {
            return base.Channel.GetprojectInfoAsync(projectGuid);
        }
        
        public System.Threading.Tasks.Task<string> GetBldInfoAsync(string proGuid)
        {
            return base.Channel.GetBldInfoAsync(proGuid);
        }
        
        public System.Threading.Tasks.Task<string> GetCRMProjectAsync(string proGuid)
        {
            return base.Channel.GetCRMProjectAsync(proGuid);
        }
        
        public System.Threading.Tasks.Task<string> GetSalesMonthDataAsync()
        {
            return base.Channel.GetSalesMonthDataAsync();
        }
        
        public System.Threading.Tasks.Task<string> GetSalesDataAsync(System.Nullable<System.DateTime> dt)
        {
            return base.Channel.GetSalesDataAsync(dt);
        }
        
        public System.Threading.Tasks.Task<string> GetSalesDataAllAsync()
        {
            return base.Channel.GetSalesDataAllAsync();
        }
        
        public System.Threading.Tasks.Task<string> GetCommissionDataAsync(int year, int month)
        {
            return base.Channel.GetCommissionDataAsync(year, month);
        }
        
        public System.Threading.Tasks.Task<string> GetmyFunctionAsync()
        {
            return base.Channel.GetmyFunctionAsync();
        }
        
        public System.Threading.Tasks.Task<string> GetCostInfoAsync(System.Guid projguid)
        {
            return base.Channel.GetCostInfoAsync(projguid);
        }
        
        public System.Threading.Tasks.Task<string> GetPayInfoAsync(System.DateTime BgnDate, System.DateTime EndDate, string projguid)
        {
            return base.Channel.GetPayInfoAsync(BgnDate, EndDate, projguid);
        }
        
        public System.Threading.Tasks.Task<string> GetApplyInfoAsync(System.DateTime BgnDate, System.DateTime EndDate, string Contractname, string ApplyByName, string verb)
        {
            return base.Channel.GetApplyInfoAsync(BgnDate, EndDate, Contractname, ApplyByName, verb);
        }
        
        public System.Threading.Tasks.Task<string> Getcb_ContractAsync(string Contractcode)
        {
            return base.Channel.Getcb_ContractAsync(Contractcode);
        }
        
        public System.Threading.Tasks.Task<string> GetApplySubjectAsync(string Contractcode)
        {
            return base.Channel.GetApplySubjectAsync(Contractcode);
        }
        
        public System.Threading.Tasks.Task<string> EntryRefundInfoAsync(string RefundInfo)
        {
            return base.Channel.EntryRefundInfoAsync(RefundInfo);
        }
        
        public System.Threading.Tasks.Task<string> EntryPayInvoiceAsync(string PayInvoice)
        {
            return base.Channel.EntryPayInvoiceAsync(PayInvoice);
        }
        
        public System.Threading.Tasks.Task<string> GetContractsAsync(string DocCode)
        {
            return base.Channel.GetContractsAsync(DocCode);
        }
        
        public System.Threading.Tasks.Task<string> GetRoomContractAsync(string roomguid, string phone)
        {
            return base.Channel.GetRoomContractAsync(roomguid, phone);
        }
        
        public System.Threading.Tasks.Task<string> GetAmountAsync(string salestr, string roomstr, string amountstr, string jkr)
        {
            return base.Channel.GetAmountAsync(salestr, roomstr, amountstr, jkr);
        }
        
        public System.Threading.Tasks.Task<string> GetRoomListAsync(string BldGUID, string keywords)
        {
            return base.Channel.GetRoomListAsync(BldGUID, keywords);
        }
        
        public System.Threading.Tasks.Task<string> GetRoomListByDateAsync(string startStr, string endStr, string teamname, string projname, string bldguid)
        {
            return base.Channel.GetRoomListByDateAsync(startStr, endStr, teamname, projname, bldguid);
        }
        
        public System.Threading.Tasks.Task<string> ERPdaibanAsync(string usercode, string keywords, int pageSize, int pageIndex)
        {
            return base.Channel.ERPdaibanAsync(usercode, keywords, pageSize, pageIndex);
        }
        
        public System.Threading.Tasks.Task<string> ERPapproveAsync(string usercode, string keywords, int pageSize, int pageIndex)
        {
            return base.Channel.ERPapproveAsync(usercode, keywords, pageSize, pageIndex);
        }
        
        public System.Threading.Tasks.Task<string> ERPtodoAsync(string usercode, string processname, string ProjectCompany, string qsdate, string enddate, int pageSize, int pageIndex)
        {
            return base.Channel.ERPtodoAsync(usercode, processname, ProjectCompany, qsdate, enddate, pageSize, pageIndex);
        }
        
        public System.Threading.Tasks.Task<string> ERPdoneAsync(string usercode, string processname, string ProjectCompany, string qsdate, string enddate, int pageSize, int pageIndex)
        {
            return base.Channel.ERPdoneAsync(usercode, processname, ProjectCompany, qsdate, enddate, pageSize, pageIndex);
        }
        
        public System.Threading.Tasks.Task<string> ERPraiseAsync(string usercode, string processname, string ProjectCompany, string qsdate, string enddate, int pageSize, int pageIndex)
        {
            return base.Channel.ERPraiseAsync(usercode, processname, ProjectCompany, qsdate, enddate, pageSize, pageIndex);
        }
        
        public System.Threading.Tasks.Task<string> ERPccAsync(string usercode, string processname, string ProjectCompany, string qsdate, string enddate, int pageSize, int pageIndex)
        {
            return base.Channel.ERPccAsync(usercode, processname, ProjectCompany, qsdate, enddate, pageSize, pageIndex);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_IService))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("找不到名称为“{0}”的终结点。", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_IService))
            {
                return new System.ServiceModel.EndpointAddress("http://weixin.cqlandmark.com/Services/Service.svc");
            }
            throw new System.InvalidOperationException(string.Format("找不到名称为“{0}”的终结点。", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return ServiceClient.GetBindingForEndpoint(EndpointConfiguration.BasicHttpBinding_IService);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return ServiceClient.GetEndpointAddress(EndpointConfiguration.BasicHttpBinding_IService);
        }
        
        public enum EndpointConfiguration
        {
            
            BasicHttpBinding_IService,
        }
    }
}
