﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Diagnostics.TextWriterTraceListener</name>
  </assembly>
  <members>
    <member name="T:System.Diagnostics.ConsoleTraceListener">
      <summary>Directs tracing or debugging output to either the standard output or the standard error stream.</summary>
    </member>
    <member name="M:System.Diagnostics.ConsoleTraceListener.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.ConsoleTraceListener" /> class with trace output written to the standard output stream.</summary>
    </member>
    <member name="M:System.Diagnostics.ConsoleTraceListener.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.ConsoleTraceListener" /> class with an option to write trace output to the standard output stream or the standard error stream.</summary>
      <param name="useErrorStream">
        <see langword="true" /> to write tracing and debugging output to the standard error stream; <see langword="false" /> to write tracing and debugging output to the standard output stream.</param>
    </member>
    <member name="M:System.Diagnostics.ConsoleTraceListener.Close">
      <summary>Closes the output to the stream specified for this trace listener.</summary>
    </member>
    <member name="T:System.Diagnostics.DelimitedListTraceListener">
      <summary>Directs tracing or debugging output to a text writer, such as a stream writer, or to a stream, such as a file stream.</summary>
    </member>
    <member name="M:System.Diagnostics.DelimitedListTraceListener.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.DelimitedListTraceListener" /> class that writes to the specified output stream.</summary>
      <param name="stream">The <see cref="T:System.IO.Stream" /> to receive the output.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.DelimitedListTraceListener.#ctor(System.IO.Stream,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.DelimitedListTraceListener" /> class that writes to the specified output stream and has the specified name.</summary>
      <param name="stream">The <see cref="T:System.IO.Stream" /> to receive the output.</param>
      <param name="name">The name of the new instance of the trace listener.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.DelimitedListTraceListener.#ctor(System.IO.TextWriter)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.DelimitedListTraceListener" /> class that writes to the specified text writer.</summary>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> to receive the output.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.DelimitedListTraceListener.#ctor(System.IO.TextWriter,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.DelimitedListTraceListener" /> class that writes to the specified text writer and has the specified name.</summary>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> to receive the output.</param>
      <param name="name">The name of the new instance of the trace listener.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.DelimitedListTraceListener.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.DelimitedListTraceListener" /> class that writes to the specified file.</summary>
      <param name="fileName">The name of the file to receive the output.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.DelimitedListTraceListener.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.DelimitedListTraceListener" /> class that writes to the specified file and has the specified name.</summary>
      <param name="fileName">The name of the file to receive the output.</param>
      <param name="name">The name of the new instance of the trace listener.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Diagnostics.DelimitedListTraceListener.Delimiter">
      <summary>Gets or sets the delimiter for the delimited list.</summary>
      <returns>The delimiter for the delimited list.</returns>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Diagnostics.DelimitedListTraceListener.Delimiter" /> is set to <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Diagnostics.DelimitedListTraceListener.Delimiter" /> is set to an empty string ("").</exception>
    </member>
    <member name="M:System.Diagnostics.DelimitedListTraceListener.GetSupportedAttributes">
      <summary>Returns the custom configuration file attribute supported by the delimited trace listener.</summary>
      <returns>A string array that contains the single value "delimiter".</returns>
    </member>
    <member name="M:System.Diagnostics.DelimitedListTraceListener.TraceData(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.Object)">
      <summary>Writes trace information, a data object, and event information to the output file or stream.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">A name used to identify the output, typically the name of the application that generated the trace event.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values specifying the type of event that has caused the trace.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="data">A data object to write to the output file or stream.</param>
    </member>
    <member name="M:System.Diagnostics.DelimitedListTraceListener.TraceData(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.Object[])">
      <summary>Writes trace information, an array of data objects, and event information to the output file or stream.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">A name used to identify the output, typically the name of the application that generated the trace event.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values specifying the type of event that has caused the trace.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="data">An array of data objects to write to the output file or stream.</param>
    </member>
    <member name="M:System.Diagnostics.DelimitedListTraceListener.TraceEvent(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.String)">
      <summary>Writes trace information, a message, and event information to the output file or stream.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">A name used to identify the output, typically the name of the application that generated the trace event.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values specifying the type of event that has caused the trace.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="message">The trace message to write to the output file or stream.</param>
    </member>
    <member name="M:System.Diagnostics.DelimitedListTraceListener.TraceEvent(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.String,System.Object[])">
      <summary>Writes trace information, a formatted array of objects, and event information to the output file or stream.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">A name used to identify the output, typically the name of the application that generated the trace event.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values specifying the type of event that has caused the trace.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="format">A format string that contains zero or more format items that correspond to objects in the <paramref name="args" /> array.</param>
      <param name="args">An array containing zero or more objects to format.</param>
    </member>
    <member name="T:System.Diagnostics.TextWriterTraceListener">
      <summary>Directs tracing or debugging output to a <see cref="T:System.IO.TextWriter" /> or to a <see cref="T:System.IO.Stream" />, such as <see cref="T:System.IO.FileStream" />.</summary>
    </member>
    <member name="M:System.Diagnostics.TextWriterTraceListener.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TextWriterTraceListener" /> class with <see cref="T:System.IO.TextWriter" /> as the output recipient.</summary>
    </member>
    <member name="M:System.Diagnostics.TextWriterTraceListener.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TextWriterTraceListener" /> class, using the stream as the recipient of the debugging and tracing output.</summary>
      <param name="stream">A <see cref="T:System.IO.Stream" /> that represents the stream the <see cref="T:System.Diagnostics.TextWriterTraceListener" /> writes to.</param>
      <exception cref="T:System.ArgumentNullException">The stream is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.TextWriterTraceListener.#ctor(System.IO.Stream,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TextWriterTraceListener" /> class with the specified name, using the stream as the recipient of the debugging and tracing output.</summary>
      <param name="stream">A <see cref="T:System.IO.Stream" /> that represents the stream the <see cref="T:System.Diagnostics.TextWriterTraceListener" /> writes to.</param>
      <param name="name">The name of the new instance.</param>
      <exception cref="T:System.ArgumentNullException">The stream is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.TextWriterTraceListener.#ctor(System.IO.TextWriter)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TextWriterTraceListener" /> class using the specified writer as recipient of the tracing or debugging output.</summary>
      <param name="writer">A <see cref="T:System.IO.TextWriter" /> that receives the output from the <see cref="T:System.Diagnostics.TextWriterTraceListener" />.</param>
      <exception cref="T:System.ArgumentNullException">The writer is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.TextWriterTraceListener.#ctor(System.IO.TextWriter,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TextWriterTraceListener" /> class with the specified name, using the specified writer as recipient of the tracing or debugging output.</summary>
      <param name="writer">A <see cref="T:System.IO.TextWriter" /> that receives the output from the <see cref="T:System.Diagnostics.TextWriterTraceListener" />.</param>
      <param name="name">The name of the new instance.</param>
      <exception cref="T:System.ArgumentNullException">The writer is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.TextWriterTraceListener.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TextWriterTraceListener" /> class, using the file as the recipient of the debugging and tracing output.</summary>
      <param name="fileName">The name of the file the <see cref="T:System.Diagnostics.TextWriterTraceListener" /> writes to.</param>
      <exception cref="T:System.ArgumentNullException">The file is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.TextWriterTraceListener.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TextWriterTraceListener" /> class with the specified name, using the file as the recipient of the debugging and tracing output.</summary>
      <param name="fileName">The name of the file the <see cref="T:System.Diagnostics.TextWriterTraceListener" /> writes to.</param>
      <param name="name">The name of the new instance.</param>
      <exception cref="T:System.ArgumentNullException">The stream is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.TextWriterTraceListener.Close">
      <summary>Closes the <see cref="P:System.Diagnostics.TextWriterTraceListener.Writer" /> so that it no longer receives tracing or debugging output.</summary>
    </member>
    <member name="M:System.Diagnostics.TextWriterTraceListener.Dispose(System.Boolean)">
      <summary>Disposes this <see cref="T:System.Diagnostics.TextWriterTraceListener" /> object.</summary>
      <param name="disposing">
        <see langword="true" /> to release managed resources; if <see langword="false" />, <see cref="M:System.Diagnostics.TextWriterTraceListener.Dispose(System.Boolean)" /> has no effect.</param>
    </member>
    <member name="M:System.Diagnostics.TextWriterTraceListener.Flush">
      <summary>Flushes the output buffer for the <see cref="P:System.Diagnostics.TextWriterTraceListener.Writer" />.</summary>
    </member>
    <member name="M:System.Diagnostics.TextWriterTraceListener.Write(System.String)">
      <summary>Writes a message to this instance's <see cref="P:System.Diagnostics.TextWriterTraceListener.Writer" />.</summary>
      <param name="message">A message to write.</param>
    </member>
    <member name="M:System.Diagnostics.TextWriterTraceListener.WriteLine(System.String)">
      <summary>Writes a message to this instance's <see cref="P:System.Diagnostics.TextWriterTraceListener.Writer" /> followed by a line terminator. The default line terminator is a carriage return followed by a line feed (\r\n).</summary>
      <param name="message">A message to write.</param>
    </member>
    <member name="P:System.Diagnostics.TextWriterTraceListener.Writer">
      <summary>Gets or sets the text writer that receives the tracing or debugging output.</summary>
      <returns>A <see cref="T:System.IO.TextWriter" /> that represents the writer that receives the tracing or debugging output.</returns>
    </member>
    <member name="T:System.Diagnostics.XmlWriterTraceListener">
      <summary>Directs tracing or debugging output as XML-encoded data to a <see cref="T:System.IO.TextWriter" /> or to a <see cref="T:System.IO.Stream" />, such as a <see cref="T:System.IO.FileStream" />.</summary>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.XmlWriterTraceListener" /> class, using the specified stream as the recipient of the debugging and tracing output.</summary>
      <param name="stream">A <see cref="T:System.IO.Stream" /> that represents the stream the trace listener writes to.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.#ctor(System.IO.Stream,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.XmlWriterTraceListener" /> class with the specified name, using the specified stream as the recipient of the debugging and tracing output.</summary>
      <param name="stream">A <see cref="T:System.IO.Stream" /> that represents the stream the trace listener writes to.</param>
      <param name="name">The name of the new instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.#ctor(System.IO.TextWriter)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.XmlWriterTraceListener" /> class using the specified writer as the recipient of the debugging and tracing output.</summary>
      <param name="writer">A <see cref="T:System.IO.TextWriter" /> that receives the output from the trace listener.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.#ctor(System.IO.TextWriter,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.XmlWriterTraceListener" /> class with the specified name, using the specified writer as the recipient of the debugging and tracing output.</summary>
      <param name="writer">A <see cref="T:System.IO.TextWriter" /> that receives the output from the trace listener.</param>
      <param name="name">The name of the new instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.XmlWriterTraceListener" /> class, using the specified file as the recipient of the debugging and tracing output.</summary>
      <param name="filename">The name of the file to write to.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="filename" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.XmlWriterTraceListener" /> class with the specified name, using the specified file as the recipient of the debugging and tracing output.</summary>
      <param name="filename">The name of the file to write to.</param>
      <param name="name">The name of the new instance.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.Close">
      <summary>Closes the <see cref="P:System.Diagnostics.TextWriterTraceListener.Writer" /> for this listener so that it no longer receives tracing or debugging output.</summary>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.Fail(System.String,System.String)">
      <summary>Writes trace information including an error message and a detailed error message to the file or stream.</summary>
      <param name="message">The error message to write.</param>
      <param name="detailMessage">The detailed error message to append to the error message.</param>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.TraceData(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.Object)">
      <summary>Writes trace information, a data object, and event information to the file or stream.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">The source name.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="data">A data object to emit.</param>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.TraceData(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.Object[])">
      <summary>Writes trace information, data objects, and event information to the file or stream.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">The source name.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="data">An array of data objects to emit.</param>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.TraceEvent(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.String)">
      <summary>Writes trace information, a message, and event information to the file or stream.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">The source name.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="message">The message to write.</param>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.TraceEvent(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.String,System.Object[])">
      <summary>Writes trace information, a formatted message, and event information to the file or stream.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">The source name.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="format">A format string that contains zero or more format items that correspond to objects in the <paramref name="args" /> array.</param>
      <param name="args">An object array containing zero or more objects to format.</param>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.TraceTransfer(System.Diagnostics.TraceEventCache,System.String,System.Int32,System.String,System.Guid)">
      <summary>Writes trace information including the identity of a related activity, a message, and event information to the file or stream.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">The source name.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="message">A trace message to write.</param>
      <param name="relatedActivityId">A <see cref="T:System.Guid" /> structure that identifies a related activity.</param>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.Write(System.String)">
      <summary>Writes a verbatim message without any additional context information to the file or stream.</summary>
      <param name="message">The message to write.</param>
    </member>
    <member name="M:System.Diagnostics.XmlWriterTraceListener.WriteLine(System.String)">
      <summary>Writes a verbatim message without any additional context information followed by the current line terminator to the file or stream.</summary>
      <param name="message">The message to write.</param>
    </member>
  </members>
</doc>