<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Net.Http.Headers</name>
    </assembly>
    <members>
        <member name="M:Microsoft.Net.Http.Headers.ContentDispositionHeaderValue.SetHttpFileName(Microsoft.Extensions.Primitives.StringSegment)">
            <summary>
            Sets both FileName and FileNameStar using encodings appropriate for HTTP headers.
            </summary>
            <param name="fileName"></param>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.ContentDispositionHeaderValue.SetMimeFileName(Microsoft.Extensions.Primitives.StringSegment)">
            <summary>
            Sets the FileName parameter using encodings appropriate for MIME headers.
            The FileNameStar parameter is removed.
            </summary>
            <param name="fileName"></param>
        </member>
        <member name="T:Microsoft.Net.Http.Headers.ContentDispositionHeaderValueIdentityExtensions">
            <summary>
            Various extension methods for <see cref="T:Microsoft.Net.Http.Headers.ContentDispositionHeaderValue"/> for identifying the type of the disposition header
            </summary>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.ContentDispositionHeaderValueIdentityExtensions.IsFileDisposition(Microsoft.Net.Http.Headers.ContentDispositionHeaderValue)">
            <summary>
            Checks if the content disposition header is a file disposition
            </summary>
            <param name="header">The header to check</param>
            <returns>True if the header is file disposition, false otherwise</returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.ContentDispositionHeaderValueIdentityExtensions.IsFormDisposition(Microsoft.Net.Http.Headers.ContentDispositionHeaderValue)">
            <summary>
            Checks if the content disposition header is a form disposition
            </summary>
            <param name="header">The header to check</param>
            <returns>True if the header is form disposition, false otherwise</returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.EntityTagHeaderValue.Equals(System.Object)">
            <summary>
            Check against another <see cref="T:Microsoft.Net.Http.Headers.EntityTagHeaderValue"/> for equality.
            This equality check should not be used to determine if two values match under the RFC specifications (https://tools.ietf.org/html/rfc7232#section-2.3.2).
            </summary>
            <param name="obj">The other value to check against for equality.</param>
            <returns>
            <c>true</c> if the strength and tag of the two values match,
            <c>false</c> if the other value is null, is not an <see cref="T:Microsoft.Net.Http.Headers.EntityTagHeaderValue"/>, or if there is a mismatch of strength or tag between the two values.
            </returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.EntityTagHeaderValue.Compare(Microsoft.Net.Http.Headers.EntityTagHeaderValue,System.Boolean)">
            <summary>
            Compares against another <see cref="T:Microsoft.Net.Http.Headers.EntityTagHeaderValue"/> to see if they match under the RFC specifications (https://tools.ietf.org/html/rfc7232#section-2.3.2).
            </summary>
            <param name="other">The other <see cref="T:Microsoft.Net.Http.Headers.EntityTagHeaderValue"/> to compare against.</param>
            <param name="useStrongComparison"><c>true</c> to use a strong comparison, <c>false</c> to use a weak comparison</param>
            <returns>
            <c>true</c> if the <see cref="T:Microsoft.Net.Http.Headers.EntityTagHeaderValue"/> match for the given comparison type,
            <c>false</c> if the other value is null or the comparison failed.
            </returns>
        </member>
        <member name="F:Microsoft.Net.Http.Headers.HeaderQuality.Match">
            <summary>
            Quality factor to indicate a perfect match.
            </summary>
        </member>
        <member name="F:Microsoft.Net.Http.Headers.HeaderQuality.NoMatch">
            <summary>
            Quality factor to indicate no match.
            </summary>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.HeaderUtilities.TryParseSeconds(Microsoft.Extensions.Primitives.StringValues,System.String,System.Nullable{System.TimeSpan}@)">
            <summary>
            Try to find a target header value among the set of given header values and parse it as a
            <see cref="T:System.TimeSpan"/>.
            </summary>
            <param name="headerValues">
            The <see cref="T:Microsoft.Extensions.Primitives.StringValues"/> containing the set of header values to search.
            </param>
            <param name="targetValue">
            The target header value to look for.
            </param>
            <param name="value">
            When this method returns, contains the parsed <see cref="T:System.TimeSpan"/>, if the parsing succeeded, or
            null if the parsing failed. The conversion fails if the <paramref name="targetValue"/> was not
            found or could not be parsed as a <see cref="T:System.TimeSpan"/>. This parameter is passed uninitialized;
            any value originally supplied in result will be overwritten.
            </param>
            <returns>
            <code>true</code> if <paramref name="targetValue"/> is found and successfully parsed; otherwise,
            <code>false</code>.
            </returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.HeaderUtilities.ContainsCacheDirective(Microsoft.Extensions.Primitives.StringValues,System.String)">
            <summary>
            Check if a target directive exists among the set of given cache control directives.
            </summary>
            <param name="cacheControlDirectives">
            The <see cref="T:Microsoft.Extensions.Primitives.StringValues"/> containing the set of cache control directives.
            </param>
            <param name="targetDirectives">
            The target cache control directives to look for.
            </param>
            <returns>
            <code>true</code> if <paramref name="targetDirectives"/> is contained in <paramref name="cacheControlDirectives"/>;
            otherwise, <code>false</code>.
            </returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.HeaderUtilities.TryParseNonNegativeInt32(Microsoft.Extensions.Primitives.StringSegment,System.Int32@)">
            <summary>
            Try to convert a string representation of a positive number to its 64-bit signed integer equivalent.
            A return value indicates whether the conversion succeeded or failed.
            </summary>
            <param name="value">
            A string containing a number to convert.
            </param>
            <param name="result">
            When this method returns, contains the 64-bit signed integer value equivalent of the number contained
            in the string, if the conversion succeeded, or zero if the conversion failed. The conversion fails if
            the string is null or String.Empty, is not of the correct format, is negative, or represents a number
            greater than Int64.MaxValue. This parameter is passed uninitialized; any value originally supplied in
            result will be overwritten.
            </param>
            <returns><code>true</code> if parsing succeeded; otherwise, <code>false</code>.</returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.HeaderUtilities.TryParseNonNegativeInt64(Microsoft.Extensions.Primitives.StringSegment,System.Int64@)">
            <summary>
            Try to convert a <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> representation of a positive number to its 64-bit signed
            integer equivalent. A return value indicates whether the conversion succeeded or failed.
            </summary>
            <param name="value">
            A <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> containing a number to convert.
            </param>
            <param name="result">
            When this method returns, contains the 64-bit signed integer value equivalent of the number contained
            in the string, if the conversion succeeded, or zero if the conversion failed. The conversion fails if
            the <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> is null or String.Empty, is not of the correct format, is negative, or
            represents a number greater than Int64.MaxValue. This parameter is passed uninitialized; any value
            originally supplied in result will be overwritten.
            </param>
            <returns><code>true</code> if parsing succeeded; otherwise, <code>false</code>.</returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.HeaderUtilities.FormatNonNegativeInt64(System.Int64)">
            <summary>
            Converts the non-negative 64-bit numeric value to its equivalent string representation.
            </summary>
            <param name="value">
            The number to convert.
            </param>
            <returns>
            The string representation of the value of this instance, consisting of a sequence of digits ranging from 0 to 9 with no leading zeroes.
            </returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.HeaderUtilities.UnescapeAsQuotedString(Microsoft.Extensions.Primitives.StringSegment)">
            <summary>
            Given a quoted-string as defined by <see href="https://tools.ietf.org/html/rfc7230#section-3.2.6">the RFC specification</see>,
            removes quotes and unescapes backslashes and quotes. This assumes that the input is a valid quoted-string.
            </summary>
            <param name="input">The quoted-string to be unescaped.</param>
            <returns>An unescaped version of the quoted-string.</returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.HeaderUtilities.EscapeAsQuotedString(Microsoft.Extensions.Primitives.StringSegment)">
            <summary>
            Escapes a <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> as a quoted-string, which is defined by
            <see href="https://tools.ietf.org/html/rfc7230#section-3.2.6">the RFC specification</see>.
            </summary>
            <remarks>
            This will add a backslash before each backslash and quote and add quotes
            around the input. Assumes that the input does not have quotes around it,
            as this method will add them. Throws if the input contains any invalid escape characters,
            as defined by rfc7230.
            </remarks>
            <param name="input">The input to be escaped.</param>
            <returns>An escaped version of the quoted-string.</returns>
        </member>
        <member name="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue">
            <summary>
            Representation of the media type header. See <see href="https://tools.ietf.org/html/rfc6838"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.#ctor(Microsoft.Extensions.Primitives.StringSegment)">
            <summary>
            Initializes a <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/> instance.
            </summary>
            <param name="mediaType">A <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> representation of a media type.
            The text provided must be a single media type without parameters. </param>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.#ctor(Microsoft.Extensions.Primitives.StringSegment,System.Double)">
            <summary>
            Initializes a <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/> instance.
            </summary>
            <param name="mediaType">A <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> representation of a media type.
            The text provided must be a single media type without parameters. </param>
            <param name="quality">The <see cref="T:System.Double"/> with the quality of the media type.</param>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.Charset">
            <summary>
            Gets or sets the value of the charset parameter. Returns <see cref="F:Microsoft.Extensions.Primitives.StringSegment.Empty"/>
            if there is no charset.
            </summary>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.Encoding">
            <summary>
            Gets or sets the value of the Encoding parameter. Setting the Encoding will set
            the <see cref="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.Charset"/> to <see cref="P:System.Text.Encoding.WebName"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.Boundary">
            <summary>
            Gets or sets the value of the boundary parameter. Returns <see cref="F:Microsoft.Extensions.Primitives.StringSegment.Empty"/>
            if there is no boundary.
            </summary>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.Parameters">
            <summary>
            Gets or sets the media type's parameters. Returns an empty <see cref="T:System.Collections.Generic.IList`1"/>
            if there are no parameters.
            </summary>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.Quality">
            <summary>
            Gets or sets the value of the quality parameter. Returns null
            if there is no quality.
            </summary>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.MediaType">
            <summary>
            Gets or sets the value of the media type. Returns <see cref="F:Microsoft.Extensions.Primitives.StringSegment.Empty"/>
            if there is no media type.
            </summary>
            <example>
            For the media type <c>"application/json"</c>, the property gives the value
            <c>"application/json"</c>.
            </example>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.Type">
            <summary>
            Gets the type of the <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/>.
            </summary>
            <example>
            For the media type <c>"application/json"</c>, the property gives the value <c>"application"</c>.
            </example>
            <remarks>See <see href="https://tools.ietf.org/html/rfc6838#section-4.2"/> for more details on the type.</remarks>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.SubType">
            <summary>
            Gets the subtype of the <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/>.
            </summary>
            <example>
            For the media type <c>"application/vnd.example+json"</c>, the property gives the value
            <c>"vnd.example+json"</c>.
            </example>
            <remarks>See <see href="https://tools.ietf.org/html/rfc6838#section-4.2"/> for more details on the subtype.</remarks>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.SubTypeWithoutSuffix">
            <summary>
            Gets subtype of the <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/>, excluding any structured syntax suffix. Returns <see cref="F:Microsoft.Extensions.Primitives.StringSegment.Empty"/>
            if there is no subtype without suffix.
            </summary>
            <example>
            For the media type <c>"application/vnd.example+json"</c>, the property gives the value
            <c>"vnd.example"</c>.
            </example>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.Suffix">
            <summary>
            Gets the structured syntax suffix of the <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/> if it has one.
            See <see href="https://tools.ietf.org/html/rfc6838#section-4.8">The RFC documentation on structured syntaxes.</see>
            </summary>
            <example>
            For the media type <c>"application/vnd.example+json"</c>, the property gives the value
            <c>"json"</c>.
            </example>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.Facets">
            <summary>
            Get a <see cref="T:System.Collections.Generic.IList`1"/> of facets of the <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/>. Facets are a
            period separated list of StringSegments in the <see cref="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.SubTypeWithoutSuffix"/>.
            See <see href="https://tools.ietf.org/html/rfc6838#section-3">The RFC documentation on facets.</see>
            </summary>
            <example>
            For the media type <c>"application/vnd.example+json"</c>, the property gives the value:
            <c>{"vnd", "example"}</c>
            </example>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.MatchesAllTypes">
            <summary>
            Gets whether this <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/> matches all types.
            </summary>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.MatchesAllSubTypes">
            <summary>
            Gets whether this <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/> matches all subtypes.
            </summary>
            <example>
            For the media type <c>"application/*"</c>, this property is <c>true</c>.
            </example>
            <example>
            For the media type <c>"application/json"</c>, this property is <c>false</c>.
            </example>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.MatchesAllSubTypesWithoutSuffix">
            <summary>
            Gets whether this <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/> matches all subtypes, ignoring any structured syntax suffix.
            </summary>
            <example>
            For the media type <c>"application/*+json"</c>, this property is <c>true</c>.
            </example>
            <example>
            For the media type <c>"application/vnd.example+json"</c>, this property is <c>false</c>.
            </example>
        </member>
        <member name="P:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.IsReadOnly">
            <summary>
            Gets whether the <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/> is readonly.
            </summary>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.IsSubsetOf(Microsoft.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Gets a value indicating whether this <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/> is a subset of
            <paramref name="otherMediaType"/>. A "subset" is defined as the same or a more specific media type
            according to the precedence described in https://www.ietf.org/rfc/rfc2068.txt section 14.1, Accept.
            </summary>
            <param name="otherMediaType">The <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/> to compare.</param>
            <returns>
            A value indicating whether this <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/> is a subset of
            <paramref name="otherMediaType"/>.
            </returns>
            <remarks>
            For example "multipart/mixed; boundary=1234" is a subset of "multipart/mixed; boundary=1234",
            "multipart/mixed", "multipart/*", and "*/*" but not "multipart/mixed; boundary=2345" or
            "multipart/message; boundary=1234".
            </remarks>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.Copy">
            <summary>
            Performs a deep copy of this object and all of it's NameValueHeaderValue sub components,
            while avoiding the cost of re-validating the components.
            </summary>
            <returns>A deep copy.</returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.CopyAsReadOnly">
            <summary>
            Performs a deep copy of this object and all of it's NameValueHeaderValue sub components,
            while avoiding the cost of re-validating the components. This copy is read-only.
            </summary>
            <returns>A deep, read-only, copy.</returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.Parse(Microsoft.Extensions.Primitives.StringSegment)">
            <summary>
            Takes a media type and parses it into the <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue" /> and its associated parameters.
            </summary>
            <param name="input">The <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> with the media type.</param>
            <returns>The parsed <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/>.</returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.TryParse(Microsoft.Extensions.Primitives.StringSegment,Microsoft.Net.Http.Headers.MediaTypeHeaderValue@)">
            <summary>
            Takes a media type, which can include parameters, and parses it into the <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue" /> and its associated parameters.
            </summary>
            <param name="input">The <see cref="T:Microsoft.Extensions.Primitives.StringSegment"/> with the media type. The media type constructed here must not have an y</param>
            <param name="parsedValue">The parsed <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/></param>
            <returns>True if the value was successfully parsed.</returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.ParseList(System.Collections.Generic.IList{System.String})">
            <summary>
            Takes an <see cref="T:System.Collections.Generic.IList`1"/> of <see cref="T:System.String"/> and parses it into the <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"></see> and its associated parameters.
            </summary>
            <param name="inputs">A list of media types</param>
            <returns>The parsed <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/>.</returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.ParseStrictList(System.Collections.Generic.IList{System.String})">
            <summary>
            Takes an <see cref="T:System.Collections.Generic.IList`1"/> of <see cref="T:System.String"/> and parses it into the <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"></see> and its associated parameters.
            Throws if there is invalid data in a string.
            </summary>
            <param name="inputs">A list of media types</param>
            <returns>The parsed <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/>.</returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.TryParseList(System.Collections.Generic.IList{System.String},System.Collections.Generic.IList{Microsoft.Net.Http.Headers.MediaTypeHeaderValue}@)">
            <summary>
            Takes an <see cref="T:System.Collections.Generic.IList`1"/> of <see cref="T:System.String"/> and parses it into the <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"></see> and its associated parameters.
            </summary>
            <param name="inputs">A list of media types</param>
            <param name="parsedValues">The parsed <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/>.</param>
            <returns>True if the value was successfully parsed.</returns>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.MediaTypeHeaderValue.TryParseStrictList(System.Collections.Generic.IList{System.String},System.Collections.Generic.IList{Microsoft.Net.Http.Headers.MediaTypeHeaderValue}@)">
            <summary>
            Takes an <see cref="T:System.Collections.Generic.IList`1"/> of <see cref="T:System.String"/> and parses it into the <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"></see> and its associated parameters.
            </summary>
            <param name="inputs">A list of media types</param>
            <param name="parsedValues">The parsed <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/>.</param>
            <returns>True if the value was successfully parsed.</returns>
        </member>
        <member name="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValueComparer">
            <summary>
            Implementation of <see cref="T:System.Collections.Generic.IComparer`1"/> that can compare accept media type header fields
            based on their quality values (a.k.a q-values).
            </summary>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.MediaTypeHeaderValueComparer.Compare(Microsoft.Net.Http.Headers.MediaTypeHeaderValue,Microsoft.Net.Http.Headers.MediaTypeHeaderValue)">
            <inheritdoc />
            <remarks>
            Performs comparisons based on the arguments' quality values
            (aka their "q-value"). Values with identical q-values are considered equal (i.e. the result is 0)
            with the exception that suffixed subtype wildcards are considered less than subtype wildcards, subtype wildcards
            are considered less than specific media types and full wildcards are considered less than
            subtype wildcards. This allows callers to sort a sequence of <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/> following
            their q-values in the order of specific media types, subtype wildcards, and last any full wildcards.
            </remarks>
            <example>
            If we had a list of media types (comma separated): { text/*;q=0.8, text/*+json;q=0.8, */*;q=1, */*;q=0.8, text/plain;q=0.8 }
            Sorting them using Compare would return: { */*;q=0.8, text/*;q=0.8, text/*+json;q=0.8, text/plain;q=0.8, */*;q=1 }
            </example>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.NameValueHeaderValue.Copy">
            <summary>
            Provides a copy of this object without the cost of re-validating the values.
            </summary>
            <returns>A copy.</returns>
        </member>
        <member name="T:Microsoft.Net.Http.Headers.SameSiteMode">
            <summary>
            Indicates if the client should include a cookie on "same-site" or "cross-site" requests.
            RFC Draft: https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-03#section-4.1.1
            </summary>
        </member>
        <member name="F:Microsoft.Net.Http.Headers.SameSiteMode.Unspecified">
            <summary>No SameSite field will be set, the client should follow its default cookie policy.</summary>
        </member>
        <member name="F:Microsoft.Net.Http.Headers.SameSiteMode.None">
            <summary>Indicates the client should disable same-site restrictions.</summary>
        </member>
        <member name="F:Microsoft.Net.Http.Headers.SameSiteMode.Lax">
            <summary>Indicates the client should send the cookie with "same-site" requests, and with "cross-site" top-level navigations.</summary>
        </member>
        <member name="F:Microsoft.Net.Http.Headers.SameSiteMode.Strict">
            <summary>Indicates the client should only send the cookie with "same-site" requests.</summary>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.SetCookieHeaderValue.AppendToStringBuilder(System.Text.StringBuilder)">
            <summary>
            Append string representation of this <see cref="T:Microsoft.Net.Http.Headers.SetCookieHeaderValue"/> to given
            <paramref name="builder"/>.
            </summary>
            <param name="builder">
            The <see cref="T:System.Text.StringBuilder"/> to receive the string representation of this
            <see cref="T:Microsoft.Net.Http.Headers.SetCookieHeaderValue"/>.
            </param>
        </member>
        <member name="T:Microsoft.Net.Http.Headers.StringWithQualityHeaderValueComparer">
            <summary>
            Implementation of <see cref="T:System.Collections.Generic.IComparer`1"/> that can compare content negotiation header fields
            based on their quality values (a.k.a q-values). This applies to values used in accept-charset,
            accept-encoding, accept-language and related header fields with similar syntax rules. See
            <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValueComparer"/> for a comparer for media type
            q-values.
            </summary>
        </member>
        <member name="M:Microsoft.Net.Http.Headers.StringWithQualityHeaderValueComparer.Compare(Microsoft.Net.Http.Headers.StringWithQualityHeaderValue,Microsoft.Net.Http.Headers.StringWithQualityHeaderValue)">
            <summary>
            Compares two <see cref="T:Microsoft.Net.Http.Headers.StringWithQualityHeaderValue"/> based on their quality value
            (a.k.a their "q-value").
            Values with identical q-values are considered equal (i.e the result is 0) with the exception of wild-card
            values (i.e. a value of "*") which are considered less than non-wild-card values. This allows to sort
            a sequence of <see cref="T:Microsoft.Net.Http.Headers.StringWithQualityHeaderValue"/> following their q-values ending up with any
            wild-cards at the end.
            </summary>
            <param name="stringWithQuality1">The first value to compare.</param>
            <param name="stringWithQuality2">The second value to compare</param>
            <returns>The result of the comparison.</returns>
        </member>
    </members>
</doc>
