<template>
  <a-modal :title="title" width="60%" :visible="visible" :confirmLoading="loading" @ok="handleSubmit"
    @cancel="()=>{this.visible=false}">
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="entity" :rules="rules" v-bind="layout">

        <!-- <a-form-model-item label="备注" prop="Remark">
          <a-input v-model="entity.Remark" autocomplete="off" />
        </a-form-model-item> -->
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="招聘名称" prop="F_RecruitName" :labelCol="{ span: 4 }" :wrapperCol="{ span: 19 }">
              <a-input v-model="entity.F_RecruitName" autocomplete="off" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>

          <a-col :span="12">
            <!-- <a-form-model-item label="招聘岗位" prop="F_Role" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-input v-model="entity.F_Role" autocomplete="off" />
            </a-form-model-item> -->
            <a-form-model-item label="招聘岗位" prop="F_Role" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-tree-select v-model="entity.F_Role" allowClear :treeData="ParentIdTreeData" placeholder="请选择招聘岗位"
                @select="postSet" treeDefaultExpandAll></a-tree-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="经验" prop="F_Experience" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-input v-model="entity.F_Experience" autocomplete="off" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <!-- <a-row>
          <a-col :span="12">
            <a-form-model-item label="招聘开始日期" prop="F_Start" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-date-picker v-model="entity.F_Start" :disabled-date="disabledStartDate" type="date" format="YYYY-MM-DD"
                placeholder="请选择开始时间" style="width: 100%;" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="招聘结束日期" prop="F_End" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-date-picker v-model="entity.F_End" :disabled-date="disabledEndDate" type="date" format="YYYY-MM-DD"
                placeholder="请选择结束时间" style="width: 100%;" />
            </a-form-model-item>
          </a-col>
        </a-row> -->
        <!-- <a-row>
          <a-col :span="12">
            <a-form-model-item label="访问地址" prop="F_Url" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-input v-model="entity.F_Url" autocomplete="off" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="城市" prop="F_City" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-input v-model="entity.F_City" autocomplete="off" />
            </a-form-model-item>
          </a-col>
        </a-row> -->
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="招聘人数" prop="F_Hiring" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-input v-model="entity.F_Hiring" autocomplete="off" prop="age" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="招聘需求期" prop="F_RecDemand" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <!-- <a-input v-model="entity.F_RecDemand" autocomplete="off" /> -->
              <SelectDiction ref="selectDiction" :Name="'招聘需求期'" @selectedvalue="isRecDemand" :Value="recDemand">
              </SelectDiction>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="学历" prop="F_Education" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-input v-model="entity.F_Education" autocomplete="off" />
            </a-form-model-item>
          </a-col>
          <!-- <a-col :span="12">
            <a-form-model-item label="薪酬待遇" prop="F_Salary" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-input v-model="entity.F_Salary" autocomplete="off" />
            </a-form-model-item>
          </a-col> -->

        </a-row>
        <!-- <a-row>
          <a-col :span="12">
            <a-form-model-item label="是否热门" prop="F_Hot" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <SelectDiction ref="selectDiction" :Name="'是否'" @selectedvalue="isHotFun" :Value="ishot">
              </SelectDiction>
            </a-form-model-item> 
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="员工类型" prop="F_Type" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-input v-model="entity.F_Type" autocomplete="off" />
            </a-form-model-item>
          </a-col>
        </a-row> -->

        <a-row>
          <a-col :span="24">
            <a-form-model-item label="工作地点" prop="F_Address" :labelCol="{ span: 4 }" :wrapperCol="{ span: 19 }">
              <a-input v-model="entity.F_Address" autocomplete="off" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="招聘介绍" prop="F_Content" :labelCol="{ span: 4 }" :wrapperCol="{ span: 19 }">
              <a-input v-model="entity.F_Content" autocomplete="off" type="textarea"
                :auto-size="{ minRows: 10, maxRows: 15 }" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="岗位要求" prop="F_Achieve" :labelCol="{ span: 4 }" :wrapperCol="{ span: 19 }">
              <a-input v-model="entity.F_Achieve" autocomplete="off" type="textarea"
                :auto-size="{ minRows: 10, maxRows: 15 }" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import moment from 'moment'
import SelectDiction from '@/components/SelectDictionaries/DictionariesList'
export default {
  components: {
    SelectDiction,
  },
  props: {
    parentObj: Object
  },
  data () {
    let checkAge = (rule, value, callback) => {
      clearTimeout(checkPending);
      if (!value) {
        return callback(new Error('Please input the age'));
      }
      checkPending = setTimeout(() => {
        if (!Number.isInteger(value)) {
          callback(new Error('Please input digits'));
        } else {
          if (value < 18) {
            callback(new Error('Age must be greater than 18'));
          } else {
            callback();
          }
        }
      }, 1000);
    };
    return {
      layout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 18 }
      },
      visible: false,
      loading: false,
      rules: {
        age: [{ validator: checkAge, trigger: 'change' }],
      },
      entity: {

      },
      rules: {
        F_RecruitName: [{ required: true, message: '招聘名称不能为空', trigger: 'blur' }],
        F_Role: [{ required: true, message: '请选择招聘岗位', trigger: 'blur' }],
        F_Hiring: [{ required: true, message: '招聘人数不能为空', trigger: 'blur' }],
        F_Achieve: [{ required: true, message: '岗位要求不能为空', trigger: 'blur' }],
        F_Education: [{ required: true, message: '学历不能为空', trigger: 'blur' }],
        F_Content: [{ required: true, message: '招聘介绍不能为空', trigger: 'blur' }],
        F_RecDemand: [{ required: true, message: '招聘需求期不能为空', trigger: 'blur' }]
      },
      title: '',
      ishot: '0',
      ParentIdTreeData: [],
      recDemand: "1",
    }
  },
  mounted () {
    this.entity.F_Hot = "0";
  },
  methods: {
    disabledStartDate (startValue) {
      const endValue = this.entity.F_End
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate (endValue) {
      const startValue = this.entity.F_Start
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    isRecDemand (value) {
      this.entity.F_RecDemand = parseInt(value)
    },
    init (id) {
      this.visible = true
      this.ishot = '0'
      this.entity = {
        F_RecruitName: '',
        F_Role: '',
        F_Experience: '',
        F_Start: null,
        F_End: null,
        F_Url: '重庆招商置地开发有限公司',
        F_City: '重庆市',
        F_Content: '',
        F_Achieve: '',
        F_Education: '',
        F_Salary: '面议',
        F_Type: '外资（非欧美）',
        F_Address: '重庆市渝北区',
        F_Hot: '0',
        F_Hiring: 0,
        F_RecDemand: 0,
      }
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })

      this.$http.post('/Base_Manage/Base_Post/GetTreeDataList', { CompanyId: this.entity.F_CompanyId }).then(resJson => {
        if (resJson.Success) {
          this.ParentIdTreeData = resJson.Data
          console.log(this.ParentIdTreeData)
          this.getData(id)
        }
      })

    },
    postSet (e) {
      this.$http.post('/Base_Manage/Base_Post/GetTheData', { id: e }).then(resJson => {
        console.log(resJson);
        this.entity.F_Achieve = resJson.Data.F_Responsibility
        this.entity.F_Content = resJson.Data.F_Qualifications
        this.entity.F_Education = resJson.Data.F_Education
        this.entity.F_Experience = resJson.Data.F_Experience
        this.entity.F_RecruitName = resJson.Data.F_Name
        //this.$forceUpdate()
      })

    },
    isHotFun (value, d) {
      if (value == '1') {
        this.entity.F_Hot = true
      } else {
        this.entity.F_Hot = false
      }
    },
    openForm (id, title) {
      this.title = '新建招聘需求'
      if (id) {
        this.title = '编辑招聘需求'
      }
      this.init(id)
    },
    getData (id) {
      if (id) {
        this.loading = true
        this.$http.post('/HR_Manage/HR_Recruit/GetTheData', { id: id }).then(resJson => {
          this.loading = false
          if (resJson.Data.F_Start) {
            resJson.Data.F_Start = moment(resJson.Data.F_Start)
          }
          if (resJson.Data.F_End) {
            resJson.Data.F_End = moment(resJson.Data.F_End)
          }
          this.entity = resJson.Data
          this.ishot = this.entity.F_Hot ? '1' : '0'
          this.recDemand = this.entity.F_RecDemand + ""
          console.log(this.entity)
        })
      }
    },
    handleSubmit () {
      if (this.entity.F_RecDemand == 0) {
        this.$message.info("招聘需求期不能为空");
        return;
      }
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.loading = true
        this.entity.F_Hot = false
        this.entity.F_BusState = 1
        this.$http.post('/HR_Manage/HR_Recruit/SaveData', this.entity).then(resJson => {
          this.loading = false

          if (resJson.Success) {
            this.$message.success('操作成功!')
            this.visible = false

            this.parentObj.getDataList()
          } else {
            this.$message.error(resJson.Msg)
          }
        })
      })
    }
  }
}
</script>
