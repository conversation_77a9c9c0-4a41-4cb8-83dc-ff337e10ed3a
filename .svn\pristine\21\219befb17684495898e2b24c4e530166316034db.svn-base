﻿using Coldairarrow.Entity.Plan_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Util.DataAccess;
using System;
using Newtonsoft.Json;
using System.Text;
using Coldairarrow.Util.Helper;

namespace Coldairarrow.Business.Plan_Manage
{
    public class TargetStatisticSalesBusiness : BaseBusiness<TargetStatisticSales>, ITargetStatisticSalesBusiness, ITransientDependency
    {
        public TargetStatisticSalesBusiness(IERPDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<TargetStatisticSales>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<TargetStatisticSales>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<TargetStatisticSales, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<TargetStatisticSales> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(TargetStatisticSales data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(TargetStatisticSales data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<TargetStatisticSales>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<TargetStatisticSales, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<A01_TargetStatisticSales, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 运营驾驶舱数据
        public async Task<TargetStatisticSalesDTO> GetTargetStatisticSales(DateTime? date)
        {
            date = date ?? DateTime.Now;
            var dateStr = date.Value.ToString("yyyy-MM-dd");
            TargetStatisticSalesDTO targetStatistic = new TargetStatisticSalesDTO();
            //获取各项目得数据
            targetStatistic.projectDatas = await this.Db.GetListBySqlAsync<TargetStatisticSales>(@$"  with 
datestr as (
	SELECT (case when  DATEDIFF (day, (select CONVERT(varchar,MAX(CreateDate),23)  from A01_TargetStatisticSales), '{dateStr}')<0 
	then  (case when (select count(1) from A01_TargetStatisticSales where 1=1  
	and CONVERT(varchar,[CreateDate],23)='{dateStr}' )>0 then '{dateStr}' 
	else (CONVERT(varchar,dateadd(day,-day(CAST('{dateStr}' as datetime)),dateadd(month,1,CAST('{dateStr}' as datetime))),23)  ) end)  
	else (select CONVERT(varchar,MAX(CreateDate),23)  from A01_TargetStatisticSales) 
	end ) maxtime
)
,
lasttime as (
    select eomonth(CAST((select maxtime from datestr) as datetime),-1) timestr
  ),
   lastyear as (
	 select CONVERT(varchar,DATEFROMPARTS(YEAR(CAST((select maxtime from datestr) as datetime))-1,12,31),23)  timestr
  ),
  todata as (
  SELECT TOP 3 *
  FROM [dbo].[A01_TargetStatisticSales]
WHERE ID IN (
 SELECT * FROM ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID =N'8baf9a96-1647-43cd-9bcd-067448bd10c9' 
and CONVERT(varchar, CreateDate,23)=(select max(CONVERT(varchar, CreateDate,23)) from [dbo].[A01_TargetStatisticSales] where 1=1   and CONVERT(varchar,[CreateDate],23)=(select maxtime from datestr) ) ORDER BY ID DESC) t1
  UNION ALL 
 SELECT * FROM  ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID =N'1980EA50-A241-4096-8057-18A97E605DA9' 
and CONVERT(varchar, CreateDate,23)=(select max(CONVERT(varchar, CreateDate,23)) from [dbo].[A01_TargetStatisticSales]  where 1=1   and CONVERT(varchar,[CreateDate],23)=(select maxtime from datestr)) ORDER BY ID DESC) t2
    UNION ALL
  SELECT * FROM  (  SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID =N'DACC9453-86DA-4D09-BA92-621B0EC33967' 
and CONVERT(varchar, CreateDate,23)=(select max(CONVERT(varchar, CreateDate,23)) from [dbo].[A01_TargetStatisticSales]  where 1=1   and CONVERT(varchar,[CreateDate],23)=(select maxtime from datestr) ) ORDER BY ID DESC) t3)
  ),
  last_data as  (
    SELECT TOP 3 *
  FROM [dbo].[A01_TargetStatisticSales]
WHERE ID IN (
 SELECT * FROM ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID =N'8baf9a96-1647-43cd-9bcd-067448bd10c9' and CONVERT(varchar,CreateDate,23)=(select timestr from lasttime )  ORDER BY ID DESC) t1
  UNION ALL 
 SELECT * FROM  ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID =N'1980EA50-A241-4096-8057-18A97E605DA9' and CONVERT(varchar,CreateDate,23)=(select timestr from lasttime ) ORDER BY ID DESC) t2
    UNION ALL
  SELECT * FROM  (  SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID =N'DACC9453-86DA-4D09-BA92-621B0EC33967' and CONVERT(varchar,CreateDate,23)=(select timestr from lasttime ) ORDER BY ID DESC) t3)
  ),
    lastyear_data as  (
    SELECT TOP 3 *
  FROM [dbo].[A01_TargetStatisticSales]
WHERE ID IN (
 SELECT * FROM ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID =N'8baf9a96-1647-43cd-9bcd-067448bd10c9' and CONVERT(varchar,CreateDate,23)=(select timestr from lastyear )  ORDER BY ID DESC) t1
  UNION ALL 
 SELECT * FROM  ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID =N'1980EA50-A241-4096-8057-18A97E605DA9' and CONVERT(varchar,CreateDate,23)=(select timestr from lastyear ) ORDER BY ID DESC) t2
    UNION ALL
  SELECT * FROM  (  SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID =N'DACC9453-86DA-4D09-BA92-621B0EC33967' and CONVERT(varchar,CreateDate,23)=(select timestr from lastyear ) ORDER BY ID DESC) t3)
  )
  select t.*,
 (select 本月销售成功率 from last_data l where t.TeamProject =l.TeamProject)上月销售成功率,
  (select 今年销售成功率 from last_data l where t.TeamProject =l.TeamProject)去年销售成功率
  from todata t");
            targetStatistic.projectDatas.ForEach(item =>
            {
                item.本年完成报事 = item.本年新增报事.HasValue ? (int)((item.本年报事完成率 * (decimal)0.01) * (decimal)((item.本年新增报事))) : 0;
                item.本月完成报事 = item.本月新增报事.HasValue ? (int)((item.本月报事完成率 * (decimal)0.01) * (decimal)((item.本月新增报事))) : 0;
                item.月环比 = item.上月销售成功率.HasValue && item.上月销售成功率 != 0 ? Math.Round((decimal)(item.本月销售成功率 - item.上月销售成功率) / (decimal)(item.上月销售成功率) * (decimal)100.0, 2) : 100;
                item.年环比 = item.去年销售成功率.HasValue && item.去年销售成功率 != 0 ? Math.Round((decimal)(item.今年销售成功率 - item.去年销售成功率) / (decimal)(item.去年销售成功率) * (decimal)100.0, 2) : 100;
            });
            //获取全项目得数据
            var targets = await this.Db.GetListBySqlAsync<TargetStatisticSales>(@$" with  
datestr as (
	SELECT (case when  DATEDIFF (day, (select CONVERT(varchar,MAX(CreateDate),23)  from A01_TargetStatisticSales), '{dateStr}')<0 
	then  (case when (select count(1) from A01_TargetStatisticSales where 1=1  
	and CONVERT(varchar,[CreateDate],23)='{dateStr}' )>0 then '{dateStr}' 
	else (CONVERT(varchar,dateadd(day,-day(CAST('{dateStr}' as datetime)),dateadd(month,1,CAST('{dateStr}' as datetime))),23)  ) end)  
	else (select CONVERT(varchar,MAX(CreateDate),23)  from A01_TargetStatisticSales) 
	end ) maxtime
)
,
bsdata as(SELECT  max(CreateDate) CreateDate,
isnull( sum([本月新增报事]),0) [本月新增报事]
      ,isnull( sum([本年新增报事]),0) [本年新增报事]
      ,isnull( sum([本月完成回访]),0) [本月完成回访]
      ,isnull( sum([本年完成回访]),0) [本年完成回访]
      ,isnull( sum([本年线上接房]),0) [本年线上接房] FROM [dbo].[A01_TargetStatisticSales]
WHERE ID IN (
 SELECT * FROM ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID =N'8baf9a96-1647-43cd-9bcd-067448bd10c9' 
and  CONVERT(varchar, CreateDate,23)=(select max(CONVERT(varchar, CreateDate,23)) from [dbo].[A01_TargetStatisticSales] where TeamProject is null  and CONVERT(varchar,[CreateDate],23)=(select maxtime from datestr)) ORDER BY ID DESC) t1
  UNION ALL 
 SELECT * FROM  ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID =N'1980EA50-A241-4096-8057-18A97E605DA9' 
and  CONVERT(varchar, CreateDate,23)=(select max(CONVERT(varchar, CreateDate,23)) from [dbo].[A01_TargetStatisticSales] where TeamProject is null  and CONVERT(varchar,[CreateDate],23)=(select maxtime from datestr)) ORDER BY ID DESC) t2
    UNION ALL
  SELECT * FROM  (  SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID =N'DACC9453-86DA-4D09-BA92-621B0EC33967' 
and  CONVERT(varchar, CreateDate,23)=(select max(CONVERT(varchar, CreateDate,23)) from [dbo].[A01_TargetStatisticSales] where TeamProject is null  and CONVERT(varchar,[CreateDate],23)=(select maxtime from datestr)) ORDER BY ID DESC) t3)
  ),
  xsdata as(SELECT  CreateDate CreateDate,
 isnull( [昨日接访客户量],0) 昨日接访客户量
      ,isnull([本月接访客户量],0) 本月接访客户量
      ,isnull([今年接访客户量],0) 今年接访客户量
      ,isnull([本月销售成功率],0) 本月销售成功率
      ,isnull([今年销售成功率],0) 今年销售成功率
      ,isnull([本月签约成功率],0) 本月签约成功率
      ,isnull([今年签约成功率],0) 今年签约成功率
 FROM [dbo].[A01_TargetStatisticSales]
WHERE TeamProject is null and  CONVERT(varchar,[CreateDate],23)=(select maxtime from datestr))
  select x.*,b.* 
  from xsdata x
  left join bsdata b on CONVERT(varchar, x.CreateDate,23)=CONVERT(varchar, b.CreateDate,23)");
            targetStatistic.allProjectData = targets.FirstOrDefault();
            targetStatistic.allProjectData.今日到访 = targetStatistic.projectDatas.Count > 0 ? targetStatistic.projectDatas.Sum(x => x.今日到访):0;
            targetStatistic.allProjectData.今日复访 = targetStatistic.projectDatas.Count > 0 ? targetStatistic.projectDatas.Sum(x => x.今日复访) : 0;
            targetStatistic.allProjectData.本周到访 = targetStatistic.projectDatas.Count > 0 ? targetStatistic.projectDatas.Sum(x => x.本周到访) : 0;
            targetStatistic.allProjectData.本周复访 = targetStatistic.projectDatas.Count > 0 ? targetStatistic.projectDatas.Sum(x => x.本周复访) : 0;
            targetStatistic.allProjectData.本月到访 = targetStatistic.projectDatas.Count > 0 ? targetStatistic.projectDatas.Sum(x => x.本月到访) : 0;
            targetStatistic.allProjectData.本月复访 = targetStatistic.projectDatas.Count > 0 ? targetStatistic.projectDatas.Sum(x => x.本月复访) : 0;
            targetStatistic.allProjectData.本季到访 = targetStatistic.projectDatas.Count > 0 ? targetStatistic.projectDatas.Sum(x => x.本季到访) : 0;
            targetStatistic.allProjectData.本季复访 = targetStatistic.projectDatas.Count > 0 ? targetStatistic.projectDatas.Sum(x => x.本季复访) : 0;
            targetStatistic.allProjectData.本年到访 = targetStatistic.projectDatas.Count > 0 ? targetStatistic.projectDatas.Sum(x => x.本年到访) : 0;
            targetStatistic.allProjectData.本年复访 = targetStatistic.projectDatas.Count > 0 ? targetStatistic.projectDatas.Sum(x => x.本年复访) : 0;
            var latYearDataList = await this.Db.GetListBySqlAsync<TargetStatisticSales>(@$"    with 
datestr as (
	SELECT (case when  DATEDIFF (day, (select CONVERT(varchar,MAX(CreateDate),23)  from A01_TargetStatisticSales), '{dateStr}')<0 
	then  (case when (select count(1) from A01_TargetStatisticSales where 1=1  
	and CONVERT(varchar,[CreateDate],23)='{dateStr}' )>0 then '{dateStr}' 
	else (CONVERT(varchar,dateadd(day,-day(CAST('{dateStr}' as datetime)),dateadd(month,1,CAST('{dateStr}' as datetime))),23)  ) end)  
	else (select CONVERT(varchar,MAX(CreateDate),23)  from A01_TargetStatisticSales) 
	end ) maxtime
)
,
bsdata as(SELECT  max(CreateDate) CreateDate,
isnull( sum([本月新增报事]),0) [本月新增报事]
      ,isnull( sum([本年新增报事]),0) [本年新增报事]
      ,isnull( sum([本月完成回访]),0) [本月完成回访]
      ,isnull( sum([本年完成回访]),0) [本年完成回访]
      ,isnull( sum([本年线上接房]),0) [本年线上接房] FROM [dbo].[A01_TargetStatisticSales]
WHERE ID IN (
 SELECT * FROM ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE CONVERT(varchar,CreateDate,23)=( select CONVERT(varchar,DATEFROMPARTS(YEAR(CAST((select maxtime from datestr) as datetime))-1,12,31),23)) and TeamProjGUID =N'8baf9a96-1647-43cd-9bcd-067448bd10c9' ORDER BY ID DESC) t1
  UNION ALL 
 SELECT * FROM  ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE CONVERT(varchar,CreateDate,23)=( select CONVERT(varchar,DATEFROMPARTS(YEAR(CAST((select maxtime from datestr) as datetime))-1,12,31),23)) and TeamProjGUID =N'1980EA50-A241-4096-8057-18A97E605DA9' ORDER BY ID DESC) t2
    UNION ALL
  SELECT * FROM  (  SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE CONVERT(varchar,CreateDate,23)=( select CONVERT(varchar,DATEFROMPARTS(YEAR(CAST((select maxtime from datestr) as datetime))-1,12,31),23)) and TeamProjGUID =N'DACC9453-86DA-4D09-BA92-621B0EC33967' ORDER BY ID DESC) t3)
  ),
  xsdata as(SELECT  CreateDate CreateDate,
 isnull( [昨日接访客户量],0) 昨日接访客户量
      ,isnull([本月接访客户量],0) 本月接访客户量
      ,isnull([今年接访客户量],0) 今年接访客户量
      ,isnull([本月销售成功率],0) 本月销售成功率
      ,isnull([今年销售成功率],0) 今年销售成功率
      ,isnull([本月签约成功率],0) 本月签约成功率
      ,isnull([今年签约成功率],0) 今年签约成功率
 FROM [dbo].[A01_TargetStatisticSales]
WHERE TeamProject is null and  
CreateDate=(select max(CreateDate) from [dbo].[A01_TargetStatisticSales] where TeamProject is null and  
CONVERT(varchar,CreateDate,23)=( select CONVERT(varchar,DATEFROMPARTS(YEAR(CAST((select maxtime from datestr) as datetime))-1,12,31),23))))
  select x.*,b.* 
  from xsdata x
  left join bsdata b on CONVERT(varchar, x.CreateDate,23)=CONVERT(varchar, b.CreateDate,23)");
            var latYearData = latYearDataList.FirstOrDefault();
            if (latYearData != null)
            {
                targetStatistic.allProjectData.月环比 = targetStatistic.allProjectData!=null&& latYearData.本月销售成功率.HasValue && latYearData.本月销售成功率 != 0 ? Math.Round((decimal)(targetStatistic.allProjectData.本月销售成功率 - latYearData.本月销售成功率) / (decimal)(latYearData.本月销售成功率) * (decimal)100.0, 2) : 100;
                targetStatistic.allProjectData.年环比 = targetStatistic.allProjectData != null && latYearData.今年销售成功率.HasValue && latYearData.今年销售成功率 != 0 ? Math.Round((decimal)(targetStatistic.allProjectData.今年销售成功率 - latYearData.今年销售成功率) / (decimal)(latYearData.今年销售成功率) * (decimal)100.0, 2) : 100;
            }
            targetStatistic.allProjectData.本年完成报事 = targetStatistic.projectDatas.Sum(x => x.本年完成报事);
            targetStatistic.allProjectData.本年报事完成率 = targetStatistic.allProjectData != null ?Math.Round((decimal)(targetStatistic.allProjectData.本年完成报事) / (decimal)(targetStatistic.allProjectData.本年新增报事 * 0.01), 2):0;
            targetStatistic.allProjectData.本月完成报事 = targetStatistic.projectDatas.Sum(x => x.本月完成报事);
            targetStatistic.allProjectData.本月报事完成率 = targetStatistic.allProjectData != null ? Math.Round((decimal)(targetStatistic.allProjectData.本月完成报事) / (decimal)(targetStatistic.allProjectData.本月新增报事 * 0.01), 2):0;
            return targetStatistic;
        }

        /// <summary>
        /// 获取接访趋势
        /// </summary>
        /// <returns></returns>
        public async Task<List<TargetStatisticSales>> GetJFTargetTrend(YunYingInput Input) {
            List<TargetStatisticSales> targetStatisticSales = new List<TargetStatisticSales>();
            var queryParam = Input.queryJson.ToJObject();
            //判断时间类型(day,week,month)
            var timeType = queryParam["timeType"]?.ToString();
            if (!timeType.IsNullOrEmpty())
            {
                //var redisStr = await RedisHelper.GetAsync("ERP_SalesTargetTrend_" + timeType + "_" + Input.project + DateTime.Now.ToString("yyyy-MM-dd"));
                var redisStr = "";
                if (!string.IsNullOrWhiteSpace(redisStr))
                    targetStatisticSales = JsonConvert.DeserializeObject<List<TargetStatisticSales>>(redisStr);
                else
                {
                    var strSql = new StringBuilder();
                    //判断项目
                    if (Input.project.IsNullOrEmpty())
                    {
                        strSql.Append(@$"SELECT  CreateDate CreateDate,
 isnull( [昨日接访客户量],0) 昨日接访客户量
      ,isnull([本月接访客户量],0) 本月接访客户量
      ,isnull([今年接访客户量],0) 今年接访客户量
      ,isnull([本月销售成功率],0) 本月销售成功率
      ,isnull([今年销售成功率],0) 今年销售成功率
      ,isnull([本月签约成功率],0) 本月签约成功率
      ,isnull([今年签约成功率],0) 今年签约成功率
 FROM [dbo].[A01_TargetStatisticSales]
WHERE TeamProject is null and  
CreateDate=(select max(CreateDate) from [dbo].[A01_TargetStatisticSales] where TeamProject is null ");
                        if (timeType == "day") {
                            strSql.Append(" and  CONVERT(varchar, CreateDate, 23) = @time) ");
                        }
                        else if(timeType== "month") {
                            strSql.Append(" and  convert(char(7) ,CreateDate , 120) = @time) ");
                        }
                    }
                    else {
                        strSql.Append(@$"   SELECT TOP 3 *
  FROM [dbo].[A01_TargetStatisticSales]
WHERE ID IN (
 SELECT * FROM ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID =N'8baf9a96-1647-43cd-9bcd-067448bd10c9' 
 ");
                        if (timeType == "day")
                        {
                            strSql.Append(" and  CONVERT(varchar, CreateDate, 23) = @time ");
                        }
                        else if (timeType == "month")
                        {
                            strSql.Append(" and  convert(char(7) ,CreateDate , 120) = @time ");
                        }
                        if (!Input.project.IsNullOrEmpty())
                        {
                            strSql.Append(" and [TeamProject]='" + Input.project + "' ");
                        }
                        strSql.Append(" ORDER BY ID DESC) t1  ");
                        strSql.Append(" UNION ALL  ");
                        strSql.Append(@$" SELECT * FROM  ( SELECT TOP 1 ID 
  FROM[dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID = N'1980EA50-A241-4096-8057-18A97E605DA9'
 ");
                        if (timeType == "day")
                        {
                            strSql.Append(" and  CONVERT(varchar, CreateDate, 23) = @time ");
                        }
                        else if (timeType == "month")
                        {
                            strSql.Append(" and  convert(char(7) ,CreateDate , 120) = @time ");
                        }
                        if (!Input.project.IsNullOrEmpty())
                        {
                            strSql.Append(" and [TeamProject]='" + Input.project + "' ");
                        }
                        strSql.Append(" ORDER BY ID DESC) t1  ");
                        strSql.Append(@$"   SELECT * FROM  (  SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticSales] WHERE TeamProjGUID =N'DACC9453-86DA-4D09-BA92-621B0EC33967' 
 ");
                        if (timeType == "day")
                        {
                            strSql.Append(" and  CONVERT(varchar, CreateDate, 23) = @time ");
                        }
                        else if (timeType == "month")
                        {
                            strSql.Append(" and  convert(char(7) ,CreateDate , 120) = @time ");
                        }
                        if (!Input.project.IsNullOrEmpty())
                        {
                            strSql.Append(" and [TeamProject]='" + Input.project + "' ");
                        }
                        strSql.Append(" ORDER BY ID DESC) t1  ");
                    }

                    var threndTime = TimeStampsHelper.GetTrendTime(timeType, "sales");
                    threndTime.ForEach((threndtime) =>
                    {
                        var time= timeType == "day" ? threndtime.time : threndtime.time.ToDateTime().ToString("yyyy-MM");
                        //获取全项目得数据
                        var targets = this.Db.GetListBySql<TargetStatisticSales>(strSql.ToString(), ("time", time));
                        TargetStatisticSales model = targets?.FirstOrDefault()==null?new TargetStatisticSales(): targets?.FirstOrDefault();
                        model.timeStr = threndtime.timeStr;
                        targetStatisticSales.Add(model);
                    });
                }
                //注入缓存
                await RedisHelper.SetAsync("ERP_SalesTargetTrend_" + timeType + "_" + Input.project + DateTime.Now.ToString("yyyy-MM-dd"),
                                            JsonConvert.SerializeObject(targetStatisticSales), TimeSpan.FromMinutes(30));
            }
            return targetStatisticSales;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}