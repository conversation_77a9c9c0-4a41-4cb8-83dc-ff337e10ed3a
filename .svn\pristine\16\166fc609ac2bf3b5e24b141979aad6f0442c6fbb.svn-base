﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System;
using System.Linq.Expressions;
using Coldairarrow.Entity.Base_Manage;
using AutoMapper;
using Coldairarrow.Business.HolidayManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.IBusiness;
using static Coldairarrow.Entity.Enum.RecruitType;
using Coldairarrow.Entity.Enum;
using Coldairarrow.Entity;
using Coldairarrow.Entity.HR_Manage.Extensions;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_RecruitmentCandidatesBusiness : BaseBusiness<HR_RecruitmentCandidates>, IHR_RecruitmentCandidatesBusiness, ITransientDependency
    {
        readonly IMapper _mapper;
        IHR_InterviewPlanBusiness _InterviewPlanBusiness;
        IHR_ContentOperationLogBusiness _ContentOperationLogBusiness;
        public HR_RecruitmentCandidatesBusiness(IDbAccessor db, IMapper mapper, IHR_InterviewPlanBusiness InterviewPlanBusiness, IHR_ContentOperationLogBusiness ContentOperationLogBusiness)
            : base(db)
        {
            _mapper = mapper;
            _InterviewPlanBusiness = InterviewPlanBusiness;
            _ContentOperationLogBusiness = ContentOperationLogBusiness;
        }

        #region 外部接口

        /// <summary>
        /// 简历信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PageResult<HR_RecruitmentCandidatesDTO>> GetResumeListAsync(PageInput<ConditionDTO> input, IOperator op = null)
        {
            try
            {
                Expression<Func<HR_RecruitmentCandidates, HR_Entry, HR_Recruit, Base_Post, Base_FileInfo, HR_RecruitmentCandidatesDTO>> select = (c, e, r, p, ff) => new HR_RecruitmentCandidatesDTO
                {
                    Name = e.NameUser,
                    Sex = e.Sex,
                    CompanyName = e.F_CompanyName,
                    //PostName = p.F_Name,//获取岗位名称
                    PostName = r.F_RecruitName,//获取招聘信息名称
                    DirthDate = e.DirthDate,
                    PostId = r.F_Role,
                    IsFileModel = e.F_IsFileModel.HasValue ? (e.F_IsFileModel.Value == 1 ? 1 : 0) : 0,
                    FileModel = ff == null ? "" : ff.F_FilePath,
                    F_SeveralInterview = e.F_SeveralInterview.HasValue ? e.F_SeveralInterview.Value : 0,
                };
                select = select.BuildExtendSelectExpre();
                var where = LinqHelper.True<HR_RecruitmentCandidatesDTO>();
                var search = input.Search;
                IQueryable<HR_RecruitmentCandidatesDTO> q = null;
                if (search.EmployRelStatus == "所有应聘者")
                {
                    q = from c in this.Db.GetIQueryable<HR_RecruitmentCandidates>().AsExpandable()
                        join e in this.Db.GetIQueryable<HR_Entry>() on c.F_UserId equals e.F_Id
                        join r in this.Db.GetIQueryable<HR_Recruit>() on c.F_RecruitId equals r.F_Id
                        join p in this.Db.GetIQueryable<Base_Post>() on r.F_Role equals p.F_Id
                        join ff in this.Db.GetIQueryable<Base_FileInfo>() on e.F_FileId equals ff.F_FileFolderId
                        into fileId
                        from ff in fileId.DefaultIfEmpty()
                        select @select.Invoke(c, e, r, p, ff);
                }
                else
                {
                    q = from c in this.Db.GetIQueryable<HR_RecruitmentCandidates>().AsExpandable()
                        join e in this.Db.GetIQueryable<HR_Entry>() on c.F_UserId equals e.F_Id
                        join r in this.Db.GetIQueryable<HR_Recruit>() on c.F_RecruitId equals r.F_Id
                        join p in this.Db.GetIQueryable<Base_Post>() on r.F_Role equals p.F_Id
                        join ff in this.Db.GetIQueryable<Base_FileInfo>() on e.F_FileId equals ff.F_FileFolderId
                        //into fileId
                        //from ff in fileId.DefaultIfEmpty()
                        select @select.Invoke(c, e, r, p, ff);
                }
                //筛选
                if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
                {
                    var newWhere = DynamicExpressionParser.ParseLambda<HR_RecruitmentCandidatesDTO, bool>(
                        ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                    where = where.And(newWhere);
                }
                where = where.AndIf(search.BusState.HasValue && search.BusState != 99, x => x.F_BusState == search.BusState);

                if (!op.IsAdmin())
                {
                    var sqlstr = "select * from [dbo].[Base_UserRole] where RoleId=(select top 1  Id from Base_Role where userId='" + op.UserId + "' and  RoleName like '%招聘管理员%')";
                    var entity = Db.GetListBySql<Base_UserRole>(sqlstr).ToList();
                    if (entity.Count == 0)
                    {
                        var userInfo = Db.GetIQueryable<HR_FormalEmployees>().Where(i => i.BaseUserId == op.UserId).FirstOrDefault();
                        if (userInfo == null)
                        {
                            throw new Exception("当前用户未作用户绑定");
                        }
                        var postList = Db.GetIQueryable<Base_Post>().FirstOrDefault(i => i.F_Id == userInfo.F_PositionId && i.F_IsViewUserDetails == 1);
                        if (postList != null)
                        {
                            List<Base_Post> list = new List<Base_Post>();
                            list.Add(postList);
                            var listAll = Db.GetIQueryable<Base_Post>().ToList();
                            var postS = list;
                            while (true)
                            {

                                var getList = getPost(postS, listAll);
                                if (getList.Count == 0)
                                {
                                    break;
                                }
                                foreach (var item in getList)
                                {
                                    if (list.Count(i => i.F_Id == item.F_Id) == 0)
                                    {
                                        list.Add(item);
                                    }
                                }
                                postS = getList;
                            }
                            var ids = list.Select(s => s.F_Id).ToList();
                            q = q.Where(i => ids.Contains(i.PostId));
                        }
                        else
                        {
                            return new PageResult<HR_RecruitmentCandidatesDTO>();
                        }
                    }
                }

                if (input.IsInterview?.Count > 0)
                {
                    var interList = input.IsInterview.Select(s => s == "是" ? 1 : 0).ToList();
                    var ids = interList.FirstOrDefault();
                    if (ids == 1)
                    {
                        q = q.Where(i => i.F_BusState == 1);
                    }
                    else
                    {
                        q = q.Where(i => i.F_BusState == 0 || !i.F_BusState.HasValue);
                    }

                }
                q = q.Where(i => i.F_IsDelete == 0);
                //查询标签
                var selectCause = input.Search.selectIds;
                if (selectCause?.Count > 0)
                {
                    var causeWhere = LinqHelper.True<HR_RecruitmentCandidatesDTO>();
                    string linqStr = "i =>";
                    int a = 0;
                    foreach (var item in selectCause)
                    {
                        var d = item;
                        if (a == 0)
                        {
                            causeWhere = causeWhere.And(i => i.F_Label.Contains(item));
                            //linqStr += $" i.F_Label.Contains(item)";
                        }
                        else
                        {
                            causeWhere = causeWhere.Or(i => i.F_Label.Contains(item));
                            //linqStr += $"|| i.F_Label.Contains(item)";
                        }
                        a++;
                    }
                    q = q.Where(causeWhere);
                }
                var temp = await q.Where(where).GetPageResultAsync(input);

                return temp;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public List<Base_Post> getPost(List<Base_Post> list, List<Base_Post> ListAll)
        {
            var listPId = list.Where(i => !string.IsNullOrEmpty(i.F_ParentId)).Select(s => s.F_ParentId).Distinct().ToList();
            return ListAll.Where(i => listPId.Contains(i.F_Id)).ToList();
        }
        /// <summary>
        /// 招聘入职
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PageResult<HR_RecruitmentCandidatesDTO>> GetRecruitmentJobAsync(PageInput<ConditionDTO> input, IOperator op = null)
        {
            Expression<Func<HR_RecruitmentCandidates, HR_Entry, HR_Recruit, Base_Post, HR_RecruitmentCandidatesDTO>> select = (c, e, r, p) => new HR_RecruitmentCandidatesDTO
            {
                Name = e.NameUser,
                Sex = e.Sex,
                CompanyName = e.F_CompanyName,
                PostId = c.F_PostId,
                //PostName = p.F_Name,
                PostName = r.F_RecruitName,
                DirthDate = e.DirthDate,
                F_IsGiveUpInterview = c.F_IsGiveUpInterview.HasValue ? c.F_IsGiveUpInterview.Value : 0,
                F_IsInvitation = c.F_IsInvitation.HasValue ? c.F_IsInvitation.Value : 0
            };
            select = select.BuildExtendSelectExpre();
            var q = from c in this.Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => i.F_IsDelete != 1).AsExpandable()
                    join e in this.Db.GetIQueryable<HR_Entry>() on c.F_UserId equals e.F_Id
                    join r in this.Db.GetIQueryable<HR_Recruit>() on c.F_RecruitId equals r.F_Id
                    join p in this.Db.GetIQueryable<Base_Post>() on r.F_Role equals p.F_Id
                    select @select.Invoke(c, e, r, p);
            var where = LinqHelper.True<HR_RecruitmentCandidatesDTO>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RecruitmentCandidatesDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            //判断是否是项目总经理查询
            //if (!op.IsAdmin())
            //{
            //    var sqlstr = "select * from [dbo].[Base_UserRole] where RoleId=(select top 1  Id from Base_Role where userId='" + op.UserId + "' and  RoleName like '%项目总%')";
            //    var entity = Db.GetListBySql<Base_UserRole>(sqlstr).FirstOrDefault();
            //    if (entity != null)
            //    {
            //        search.F_Through = 1;
            //    }
            //}


            if (search.F_Through == 99)
            {
                where = where.And(x => x.F_Through == 1 || x.F_Through == 2);
            }
            else
            {
                where = where.AndIf(search.F_Through.HasValue && search.F_Through != 99, x => x.F_Through == search.F_Through);
            }


            return await q.Where(where).GetPageResultAsync(input);
        }
        /// <summary>
        /// 根据投递者ID获取招聘信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<HR_Recruit>> GetRecruitListByUserId(ConditionDTO input)
        {
            var q = from rc in GetIQueryable()
                    join r in Db.GetIQueryable<HR_Recruit>() on rc.F_RecruitId equals r.F_Id
                    where rc.F_UserId == input.UserId
                    select r;

            return await q.ToListAsync();
        }
        public async Task<PageResult<HR_RecruitmentCandidates>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RecruitmentCandidates>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RecruitmentCandidates, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }
        /// <summary>
        /// 获取简历详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<RecruitmentDTO> GetFormTheDataAsnc(string id)
        {
            try
            {
                RecruitmentDTO dto = new RecruitmentDTO();
                var model = await GetEntityAsync(id);
                if (model != null)
                {
                    dto.RecruitmentCandidates = model;
                    dto.Recruit = await this.Db.GetIQueryable<HR_Recruit>().Where(i => i.F_Id == model.F_RecruitId).FirstOrDefaultAsync();
                    if (dto.Recruit != null)
                    {
                        var post = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == dto.Recruit.F_Role);
                        dto.Recruit.RoleName = post?.F_Name;
                    }
                    dto.interviewEntity = await this.Db.GetIQueryable<HR_InterviewRecord>().Where(i => i.F_Id == model.F_UserId).FirstOrDefaultAsync();

                    dto.hR_Entry = this.Db.GetIQueryable<HR_Entry>().Where(i => i.F_Id == model.F_UserId).Select(item => new HR_Entry()
                    {
                        F_Id = item.F_Id,
                        NameUser = item.NameUser,
                        HeadPortrait = item.HeadPortrait,
                        EmployeesCode = item.EmployeesCode,
                        IdCardNumber = item.IdCardNumber,
                        PassportNo = item.PassportNo,
                        DirthDate = item.DirthDate,
                        EmployRelStatus = item.EmployRelStatus,
                        Sex = item.Sex,
                        IdCardAddress = item.IdCardAddress,
                        ND = item.ND,
                        ND2 = item.ND2,
                        ND3 = item.ND2,
                        ND4 = item.ND3,
                        MobilePhone = item.MobilePhone,
                        Email = item.Email,
                        MaritalStatus = item.MaritalStatus,
                        PoliticalLandscape = item.PoliticalLandscape,
                        ProfessionalCategory = item.ProfessionalCategory,
                        Nationality = item.Nationality,
                        NativePlace = item.NativePlace,
                        AccountType = item.AccountType,
                        HomePhone = item.HomePhone,
                        HomeAddress = item.HomeAddress,
                        EmergencyContact = item.EmergencyContact,
                        EmergencyContactNumber = item.EmergencyContactNumber,
                        F_IsFileModel = item.F_IsFileModel,
                        F_FileId = item.F_FileId,
                        F_InterestsHobbies = item.F_InterestsHobbies,
                        F_Weight = item.F_Weight,
                        F_SourcesPersonnel = item.F_SourcesPersonnel,
                        F_Sources = item.F_Sources,
                        F_Interview = item.F_Interview,
                        F_FolderId = item.F_FolderId,
                        F_Introduce = item.F_Introduce,
                        F_WeChatUserId = item.F_WeChatUserId,
                        EmergencyContactNumber2 = item.EmergencyContactNumber2,
                        EmergencyContact2 = item.EmergencyContact2,
                        F_ProfessionalQualification = item.F_ProfessionalQualification,
                        F_ForeignLevel = item.F_ForeignLevel,
                        F_HealthCondition = item.F_HealthCondition,
                        F_BloodType = item.F_BloodType,
                        F_Stature = item.F_Stature,
                        F_RecordFormalschooling = item.F_RecordFormalschooling,
                        F_CompanyName = item.F_CompanyName,
                        F_CompanyId = item.F_CompanyId,
                        F_Rank = item.F_Rank,
                        F_DepartmentId = item.F_DepartmentId,
                        IsWhetherCar = item.IsWhetherCar,
                        FertilityStatus = item.FertilityStatus,
                        F_FileBase64Img = item.F_FileBase64Img
                    }).FirstOrDefault();
                    dto.hR_Entry.DecryptFormal();
                    dto.hR_RecruitEduBacks = await this.Db.GetIQueryable<HR_RecruitEduBack>().Where(i => i.UserId == model.F_UserId).ToListAsync();
                    dto.hR_RecruitWorkExpes = await this.Db.GetIQueryable<HR_RecruitWorkExpe>().Where(i => i.F_UserId == model.F_UserId).OrderByDescending(i => i.F_StartTime).ToListAsync();
                    dto.hR_RecruitCertificates = await this.Db.GetIQueryable<HR_RecruitCertificate>().Where(i => i.F_UserId == model.F_UserId).ToListAsync();
                    //简历附件
                    string fileid = dto.hR_Entry.F_FileId;
                    var file = this.Db.GetIQueryable<Base_FileInfo>().Where(i => i.F_FileFolderId == fileid).FirstOrDefault();
                    if (file != null)
                    {
                        dto.hR_Entry.F_FileId = file.F_FilePath;
                    }
                    else
                    {
                        dto.hR_Entry.F_FileId = null;

                    }
                    var temp = await this.Db.GetIQueryable<HR_InterviewEvaluation>().Where(i => i.F_ApplicantId == dto.hR_Entry.F_Id).ToListAsync();
                    var planTemp = Db.GetIQueryable<HR_InterviewPlan>().Where(i => i.F_ApplicantId == dto.hR_Entry.F_Id).ToList();
                    var hr_Cand = Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => i.F_UserId == model.F_UserId).OrderBy(i => i.F_CreateDate);
                    //获取他的岗位
                    //var postIds = hr_Cand.Select(i => i.F_PostId).ToList();
                    //var postIds = planTemp.Select(s => s.F_PostId).Distinct().ToList();
                    dto.ApplyInfos = (from a in hr_Cand.AsExpandable()
                                      join b in Db.GetIQueryable<HR_Recruit>() on a.F_RecruitId equals b.F_Id into abJoin
                                      from ab in abJoin.DefaultIfEmpty()
                                      select new ApplyInfo()
                                      {
                                          RecruitId = a.F_Id,
                                          Id = ab == null ? "" : ab.F_Role,
                                          PostName = ab == null ? "" : (ab.F_RecruitName + (string.IsNullOrEmpty(a.F_Label) ? "" : "（" + a.F_Label.Substring(0, a.F_Label.Length - 1) + "）")),
                                          F_HiringTime = a.F_HiringTime,
                                          F_InvitationTime = a.F_InvitationTime,
                                          F_Label = a.F_Label,
                                          F_LabelOperationTime = a.F_LabelOperationTime,
                                          F_Content = a.F_Cause,
                                          //F_ModifyDate=a.F_ModifyDate
                                      }).ToList();
                    var porssIds = temp.Select(i => i.F_InterviewerId).Distinct().ToList();
                    var userIds = temp.Select(i => i.F_InterviewerIdAll).Distinct().ToList();
                    List<string> userIdList = new List<string>();
                    foreach (var item in userIds)
                    {
                        var ids = item.Split(new Char[] { ',', '，' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                        userIdList.AddRange(ids);
                    }
                    var userEntity = Db.GetIQueryable<HR_FormalEmployees>().Where(i => userIdList.Contains(i.F_Id)).AsNoTracking().ToList();
                    var porssList = Db.GetIQueryable<HR_InterviewProcess>().Where(i => porssIds.Contains(i.F_Id)).ToList();
                    var BaseUserList = Db.GetIQueryable<Base_User>().AsNoTracking().ToList();
                    foreach (var item in dto.ApplyInfos)
                    {
                        var planIds = planTemp.Where(i => i.F_RecCanId == item.RecruitId).Select(s => s.F_Id).ToList();
                        item.hRInterviewEvaluation = temp.Where(i => planIds.Contains(i.F_PlanId)).OrderBy(i => i.F_CreateDate).ToList();
                        foreach (var process in item.hRInterviewEvaluation)
                        {
                            process.ProcessName = porssList.FirstOrDefault(i => i.F_Id == process.F_InterviewerId)?.F_InterviewName;
                            var userListId = process.F_InterviewerIdAll.Split(new Char[] { ',', '，' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                            var userList = userEntity.FirstOrDefault(i => userListId.Contains(i.F_Id));
                            if (userList != null)
                            {
                                if (process.F_ModifyUserId == userList.BaseUserId)
                                {
                                    process.InterviewerName = userList.NameUser;
                                }
                                else
                                {
                                    var base_User = BaseUserList.FirstOrDefault(i => i.Id == process.F_ModifyUserId);
                                    if (base_User != null)
                                    {
                                        process.InterviewerName = $"由{base_User.RealName}代替{userList.NameUser}";
                                    }
                                    else
                                    {
                                        process.InterviewerName = userList.NameUser;
                                    }
                                }
                            }
                            //var listName = userEntity.Where(i => userListId.Contains(i.F_Id)).Select(s => s.NameUser).ToList();
                            //process.InterviewerName = string.Join(",", listName.ToArray());
                        }
                        if (item.F_HiringTime.HasValue)
                        {
                            HR_InterviewEvaluation rz = new HR_InterviewEvaluation()
                            {
                                ProcessName = "聘用",
                                //InterviewerName="HR",
                                F_IsThrough = 1,
                                F_ModifyDate = item.F_HiringTime,
                                F_Evaluation = "面试成功，聘用时间安排",
                            };
                            item.hRInterviewEvaluation.Add(rz);
                        }
                        if (item.F_InvitationTime.HasValue)
                        {
                            HR_InterviewEvaluation rz = new HR_InterviewEvaluation()
                            {
                                ProcessName = "邀请入职",
                                // InterviewerName = "HR",
                                F_IsThrough = 1,
                                F_ModifyDate = item.F_InvitationTime,
                                F_Evaluation = "入职信息通过，入职时间安排",
                            };
                            item.hRInterviewEvaluation.Add(rz);
                        }
                        if (!string.IsNullOrEmpty(item.F_Label))
                        {
                            var labelList = item.F_Label.Split(new Char[] { ';', '；' }, StringSplitOptions.RemoveEmptyEntries).Select(s => s).ToList();
                            foreach (var label in labelList)
                            {
                                if (label == "招聘专员不同意" || label == "放弃面试" || label == "暂停面试" || label == "应聘者放弃入职" || label == "应聘者确认入职")
                                {
                                    HR_InterviewEvaluation rz = new HR_InterviewEvaluation()
                                    {
                                        ProcessName = label,
                                        // InterviewerName = "HR",
                                        F_IsThrough = 3,
                                        F_ModifyDate = item.F_LabelOperationTime,
                                        //F_ModifyDate = item.F_InvitationTime,
                                        F_Evaluation = label == "招聘专员不同意" ? item.F_Content : label,
                                    };
                                    item.hRInterviewEvaluation.Add(rz);
                                }
                            }
                        }
                    }
                }
                return dto;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }
        }
        /// <summary>
        /// 获取日历面试人员信息
        /// </summary>
        /// <param name="month"></param>
        /// <returns></returns>
        public List<CalendarRecruitDTO> GetCalendarRecruitListAsnc(SystemYearInput input)
        {
            var query = from p in Db.GetIQueryable<HR_InterviewPlan>()
                        join c in Db.GetIQueryable<HR_RecruitmentCandidates>() on p.F_RecCanId equals c.F_Id
                        join e in Db.GetIQueryable<HR_Entry>() on p.F_ApplicantId equals e.F_Id
                        where p.F_InterTime.HasValue && p.F_InterTime.Value.Year == input.Year.ToInt() && p.F_InterTime.Value.Month == input.Month.ToInt()
                        select new CalendarRecruitDTO()
                        {
                            Id = p.F_RecCanId,
                            Name = e.NameUser,
                            InterTime = p.F_InterTime.Value
                        };
            return query.ToList();
        }
        public async Task<HR_RecruitmentCandidates> GetTheDataAsync(string id)
        {
            var hR_Recruitment = await GetEntityAsync(id);
            if (hR_Recruitment != null)
            {
                //Db.GetIQueryable<HR_Recruit>().Where()
                var temp = await this.Db.GetEntityAsync<HR_Recruit>(hR_Recruitment.F_RecruitId);
                if (temp != null && !string.IsNullOrEmpty(temp.F_Role))
                {
                    hR_Recruitment.F_PostId = temp.F_Role;
                    //获取部门id
                    var base_Post = await this.Db.GetEntityAsync<Base_Post>(temp.F_Role);
                    if (base_Post != null)
                    {
                        hR_Recruitment.F_DepartmentId = base_Post.F_DepartmentId;
                    }
                }
            }
            return hR_Recruitment;
        }
        [DataAddJsonLog(UserLogType.招聘管理, "F_Id", "投简历简历")]
        public async Task AddDataAsync(HR_RecruitmentCandidates data)
        {
            await InsertAsync(data);
        }
        [DataEditLog(UserLogType.招聘管理, "F_Id", "筛选简历")]
        public async Task UpdateDataAsync(HR_RecruitmentCandidates data, int state = 0, IOperator op = null)
        {
            var sqlStr = "update HR_Entry set F_BusState=1 where F_Id='" + data.F_UserId + "' ";
            await Db.ExecuteSqlAsync(sqlStr);
            //查找该用户是否存在面试计划
            //var interviewPlans = this.Db.GetIQueryable<HR_InterviewPlan>()
            //          .Where(i => i.F_ApplicantId == data.F_UserId).ToList();
            //if (interviewPlans.Count > 0)
            //{ 
            //}
            if (state == 1)//通过自动发起审核流程
            {
                var entity = Db.GetIQueryable<HR_InterviewProcess>().Where(i => i.F_PostId == data.F_PostId && string.IsNullOrEmpty(i.F_ParentId)).ToList();
                if (entity.Count != 1)
                {
                    throw new Exception("流程配置异常，请检查一下该招聘岗位流程设置！");
                }
                HR_InterviewPlan plan = new HR_InterviewPlan()
                {
                    F_ApplicantId = data.F_UserId,
                    F_InterTime = DateTime.Now.AddDays(1).Date,
                    F_InterviewName = entity.FirstOrDefault()?.F_InterviewName,
                    F_InterviewPId = entity.FirstOrDefault()?.F_Id,
                    F_PostId = data.F_PostId,
                    F_RecCanId = data.F_Id,
                    F_RoundInterview = 1,
                    F_IsPassingInter = 0,
                    F_PId = "",
                };
                InitEntity(plan, op);
                HR_InterviewPlanBusiness hR_InterviewPlan = new HR_InterviewPlanBusiness(Db, _mapper, _ContentOperationLogBusiness);
                await hR_InterviewPlan.AddDataAsync(plan);
                sqlStr = "update HR_Entry set F_SeveralInterview = F_SeveralInterview + 1 where F_Id in ( '" + data.F_UserId + "') ";
                await Db.ExecuteSqlAsync(sqlStr);
            }
            await Db.UpdateAsync(data);
        }
        /// <summary>
        /// 重新安排面试
        /// </summary>
        /// <param name="input"></param>
        /// <param name="op"></param>
        /// <returns></returns>
        [DataEditJsonCLog(UserLogType.招聘管理, "id", "重新安排面试")]
        public async Task SaveDataRecruit(PlanInputDTO input, IOperator op = null)
        {
            if (input.Ids.Count > 0)
            {
                var entityList = Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => input.Ids.Contains(i.F_Id)).ToList();
                List<HR_RecruitmentCandidates> list = new List<HR_RecruitmentCandidates>();
                var userIds = entityList.Select(s => s.F_UserId).Distinct().ToList();
                List<HR_ContentOperationLog> hR_Contents = new List<HR_ContentOperationLog>();
                foreach (var item in entityList)
                {
                    item.F_IsDelete = 1;
                    //如果正常流程则转换成放弃面试
                    //if (item.F_IsGiveUpInterview == (int)RecruitType.IsGiveUpInterviewType.Normal)
                    //{
                    item.F_IsGiveUpInterview = (int)RecruitType.IsGiveUpInterviewType.RescheduleInterview;
                    item.F_Label = (!string.IsNullOrEmpty(item.F_Label)) ? item.F_Label + EnumHelper.GetEnumDescription(RecruitType.TagType.重新安排面试) : EnumHelper.GetEnumDescription(RecruitType.TagType.重新安排面试);
                    //保存标签
                    HR_ContentOperationLog hR_ContentOperation = new HR_ContentOperationLog()
                    {
                        F_BusState = (int)ASKBusState.正常,
                        F_UserId = item.F_UserId,
                        F_RelationalID = item.F_Id,
                        F_RelationalType = (int)RecruitType.RelationalType.正常,
                        F_TagType = (int)RecruitType.TagType.重新安排面试,
                        F_TagContent = EnumHelper.GetEnumDescription(RecruitType.TagType.重新安排面试)
                    };
                    hR_Contents.Add(hR_ContentOperation);
                    //}
                    var pricModel = Db.GetIQueryable<HR_Recruit>().FirstOrDefault(i => i.F_Role == input.PositionId);
                    HR_RecruitmentCandidates data = new HR_RecruitmentCandidates()
                    {
                        F_BusState = 1,
                        F_CompanyId = input.CompanyId,
                        F_RecruitId = pricModel.F_Id,
                        F_PostId = input.PositionId,
                        F_UserId = item.F_UserId,
                        F_IsInvitedInt = 0,
                        F_Through = 0,
                        F_ThroughTime=DateTime.Now,
                        F_Label = EnumHelper.GetEnumDescription(RecruitType.TagType.多次面试)
                    };
                    //保存标签
                    HR_ContentOperationLog dchR_ContentOperation = new HR_ContentOperationLog()
                    {
                        F_BusState = (int)ASKBusState.正常,
                        F_UserId = item.F_UserId,
                        F_RelationalID = item.F_Id,
                        F_RelationalType = (int)RecruitType.RelationalType.正常,
                        F_TagType = (int)RecruitType.TagType.多次面试,
                        F_TagContent = EnumHelper.GetEnumDescription(RecruitType.TagType.多次面试)
                    };
                    hR_Contents.Add(dchR_ContentOperation);
                    InitEntity(data, op);
                    UpdateEntity(data, op);
                    list.Add(data);
                    var entity = Db.GetIQueryable<HR_InterviewProcess>().Where(i => i.F_PostId == data.F_PostId && string.IsNullOrEmpty(i.F_ParentId)).ToList();
                    if (entity.Count != 1)
                    {
                        throw new Exception("流程配置异常，请检查一下该招聘岗位流程设置！");
                    }
                    HR_InterviewPlan plan = new HR_InterviewPlan()
                    {
                        F_ApplicantId = data.F_UserId,
                        F_InterTime = DateTime.Now.AddDays(1).Date,
                        F_InterviewName = entity.FirstOrDefault()?.F_InterviewName,
                        F_InterviewPId = entity.FirstOrDefault()?.F_Id,
                        F_PostId = data.F_PostId,
                        F_RecCanId = data.F_Id,
                        F_RoundInterview = 1,
                        F_IsPassingInter = 0,
                        F_PId = "",
                    };
                    InitEntity(plan, op);
                    HR_InterviewPlanBusiness hR_InterviewPlan = new HR_InterviewPlanBusiness(Db, _mapper, _ContentOperationLogBusiness);
                    await hR_InterviewPlan.AddDataAsync(plan);
                }
                var userStr = string.Join("','", userIds.ToArray());
                var sqlStr = "update HR_Entry set F_SeveralInterview = F_SeveralInterview + 1 where F_Id in ( '" + userStr + "') ";
                await Db.ExecuteSqlAsync(sqlStr);
                await Db.UpdateAsync(entityList);

                HR_ContentOperationLogBusiness hR_ContentOperation1 = new HR_ContentOperationLogBusiness(Db, op);
                await hR_ContentOperation1.SaveDataAsync(hR_Contents);
                await Db.InsertAsync(list);
            }

        }
        [DataDeleteJsonLog(UserLogType.招聘管理, "F_UserId", "收集简历")]
        public async Task DeleteDataAsync(List<string> ids, int type = 0)
        {
            var recritCEntity = Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => ids.Contains(i.F_Id)).ToList();
            var userId = recritCEntity.Select(s => s.F_UserId).Distinct().ToList();
            var fileId = Db.GetIQueryable<HR_Entry>().Where(i => userId.Contains(i.F_Id)).Select(s => s.F_FileId).Distinct().ToList();
            if (type == 0)
            {
                if (fileId.Count > 0)
                {
                    var fileEntity = Db.GetIQueryable<Base_FileInfo>().Where(i => fileId.Contains(i.F_FileFolderId)).ToList();
                    foreach (var item in fileEntity)
                    {
                        item.F_LocationType = 0;
                    }
                    Db.Update(fileEntity);
                    Db.SaveChanges();
                }
            }
            foreach (var item in recritCEntity)
            {
                item.F_IsDelete = 1;
            }
            await UpdateAsync(recritCEntity);
            //await DeleteAsync(ids);
        }
        /// <summary>
        /// 投简历
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task SubmiResume(SubmiResumeInput input)
        {
            if (input == null)
            {
                throw new BusException("参数错误");
            }
            var entry = Db.GetIQueryable<HR_Entry>().FirstOrDefault(x => x.F_WeChatUserId == input.UserId);
            if (entry == null)
            {
                throw new BusException("用户不存在");
            }
            var q = GetIQueryable().Where(x => x.F_UserId == entry.F_Id && x.F_RecruitId == input.RecruitId);
            if (q.Count() > 0)
            {
                throw new BusException("已投过该简历，不能重复投递");
            }
            HR_RecruitmentCandidates model = new HR_RecruitmentCandidates()
            {
                F_BusState = 0,
                F_CreateDate = DateTime.Now,
                F_CreateUserName = "小程序",
                F_CreateUserId = "小程序",
                F_RecruitId = input.RecruitId,
                F_UserId = entry.F_Id,
                F_Id = Guid.NewGuid().ToString("N"),
                F_Through = 0,
                F_IsInvitedInt = 0,
            };

            await InsertAsync(model);
        }


        /// <summary>
        /// 获取项目进度
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public List<PostProgress> GetPostProgress(PageInput<ConditionDTO> input)
        {
            var listIds = string.Join("','", input.IsInterview.ToArray());
            var sqlStr = "select lo.F_RecruitName as PostName,aa.F_RoundInterview as FirstTry from  " +
                "(select distinct k.F_Id, k.F_Name,p.F_RecruitName  from[dbo].[HR_RecruitmentCandidates] a " +
                "left join[dbo].[HR_Recruit] p on a.F_RecruitId = p.F_Id " +
                " left join[dbo].[Base_Post] k on p.F_Role = k.F_Id " +
                "where ";
            if (input.IsInterview.Count > 0)
            {
                sqlStr += "  a.F_Id in ('" + listIds + "') and  ";
            }
            sqlStr += " (a.F_Through !=1 or a.F_Through!=2)) lo  left join(select * from[HR_InterviewPlan] where  F_IsPassingInter is null or F_IsPassingInter != 2) aa on aa.F_PostId = lo.F_Id";
            // a.F_Id in ('" + listIds + "') and
            //var sqlStr = "select r.F_Name as PostName,a.F_RoundInterview as FirstTry from  [HR_InterviewPlan] a   left join  Base_Post r on a.F_PostId = r.F_Id " +
            //    "where a.F_PostId in(select c.F_PostId from[dbo].[HR_InterviewPlan] c " +
            //    "left join[dbo].[HR_RecruitmentCandidates] d on d.F_Id = c.F_RecCanId where " +
            //    "d.F_Id in('"+ listIds + "') and(c.F_IsPassingInter is null or c.F_IsPassingInter != 2))";
            var temp = Db.GetListBySql<PostProgress>(sqlStr);
            var postInfo = temp.GroupBy(g => g.PostName).Select(s => new PostProgress()
            {
                PostName = s.Key,
                FirstTry = s.Count(i => i.FirstTry == 1),
                SecondInter = s.Count(i => i.FirstTry == 2),
                WillTry = s.Count(i => i.FirstTry == 3),
            }).ToList();
            return postInfo;
        }
        public PostApplicant GetPostApplicant(IdInputDTO input)
        {
            var postModel = Db.GetIQueryable<HR_Recruit>().FirstOrDefault(x => x.F_Role == input.id);
            //var postModel = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == input.id);
            var sqlStr = "select* from [dbo].[HR_RecruitmentCandidates] where F_RecruitId in( select F_Id from[dbo].[HR_Recruit] where F_Role = '" + input.id + "')";
            var temp = Db.GetListBySql<HR_RecruitmentCandidates>(sqlStr);
            PostApplicant postApplicant = new PostApplicant()
            {
                PostName = postModel.F_RecruitName,
                AgreedInterview = temp.Count(i => i.F_BusState == 1),
                Applicant = temp.Count(),
                InterviewFailure = temp.Count(i => i.F_Through == 2),
                PassedInterview = temp.Count(i => i.F_Through == 1),
                Untreated = temp.Count(i => i.F_BusState == 2),
            };
            return postApplicant;
        }
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RecruitmentCandidates>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RecruitmentCandidates, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_RecruitmentCandidates, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}