﻿using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using System.Net.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Coldairarrow.Util.Helper;
using Microsoft.Extensions.Configuration;
using Coldairarrow.Business.Wechat_CostDept;

namespace Coldairarrow.Api.Controllers.HR_Manage
{
    [Route("/HR_Manage/[controller]/[action]")]
    public class HR_WechatUserController : BaseApiController
    {
        #region DI

        public HR_WechatUserController(IHR_WechatUserBusiness hR_WechatUserBus, IHR_EntryBusiness hR_EntryBus, IConfiguration configuration, IWechat_UserBusiness wechat_UserBusiness)
        {
            _hR_WechatUserBus = hR_WechatUserBus;
            _hR_EntryBus = hR_EntryBus;
            _configuration = configuration;
            _wechat_UserBusiness = wechat_UserBusiness;
        }

        IHR_WechatUserBusiness _hR_WechatUserBus { get; }
        IHR_EntryBusiness _hR_EntryBus { get; }
        IConfiguration _configuration { get; }
        IWechat_UserBusiness _wechat_UserBusiness { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_WechatUser>> GetDataList(PageInput<WechatUserInputDTO> input)
        {
            return await _hR_WechatUserBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_WechatUser> GetTheData(IdInputDTO input)
        {
            return await _hR_WechatUserBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_WechatUser data)
        {
            if (data.W_OpenId.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_WechatUserBus.AddDataAsync(data);
            }
            else
            {
                await _hR_WechatUserBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_WechatUserBus.DeleteDataAsync(ids);
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_WechatUserBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<HR_WechatUser>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _hR_WechatUserBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion

        #region 二次开发
        //获取openId
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetOpenid(string code)
        {
            try
            {


                string appid = "wxc9e55a72a791b989";
                string secret = "6224790189bebe119b8c12a05a2b2de1";
                string grant_type = "authorization_code";
                using (var httpClient = new HttpClient())
                {
                    //post
                    var url = new System.Uri("https://api.weixin.qq.com/sns/jscode2session");
                    var body = new FormUrlEncodedContent(new Dictionary<string, string>
                {
                    { "appid", appid},
                    { "secret", secret },
                    { "js_code", code},
                    { "grant_type",grant_type}

                });
                    // response
                    var response = httpClient.PostAsync(url, body).Result;
                    var data = response.Content.ReadAsStringAsync().Result;
                    JObject jo = (JObject)JsonConvert.DeserializeObject(data);
                    var pm = new
                    {
                        openid = jo["openid"].ToString(),
                        session_key = jo["session_key"].ToString()
                    };
                    return Success(pm);
                }
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }

        }
        //获取微信的openId
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetWxOpenid(WxType wxType, string code)
        {
            try
            {
                string appid = "";
                string secret = "";
                switch (wxType)
                {
                    case WxType.Plan:
                        appid = _configuration["WxConfig:plan:appId"];
                        secret = _configuration["WxConfig:plan:secret"];
                        break;
                    case WxType.Clock:
                        appid = "wx3bd5f22d2ad7d44a";
                        secret = "803b9ddaa29eda8447b936fe8de82167";
                        break;
                    default:
                        break;
                }
                string grant_type = "authorization_code";
                using (var httpClient = new HttpClient())
                {
                    //post
                    var url = new System.Uri("https://api.weixin.qq.com/sns/jscode2session");
                    var body = new FormUrlEncodedContent(new Dictionary<string, string>
                {
                    { "appid", appid},
                    { "secret", secret },
                    { "js_code", code},
                    { "grant_type",grant_type}

                });
                    // response
                    var response = httpClient.PostAsync(url, body).Result;
                    var data = response.Content.ReadAsStringAsync().Result;
                    JObject jo = (JObject)JsonConvert.DeserializeObject(data);
                    var pm = new
                    {
                        openid = jo["openid"].ToString(),
                        session_key = jo["session_key"].ToString()
                    };
                    return Success(pm);
                }
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }

        }

        //根据openId查询用户信息
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetUserInfoById(string id)
        {
            try
            {
                var user = _hR_WechatUserBus.GetTheDataAsync(id).Result;
                if (user != null)
                {
                    user.W_LastLogin = DateTime.Now;
                    var result = _hR_EntryBus.IsInterviewers(user.W_Phone);
                    if (result)
                    {                    //是
                        user.W_UserType = 2;
                    }
                    _hR_WechatUserBus.UpdateDataAsync(user).Wait();
                    return Success(user);
                }
                else
                {
                    var newUser = new HR_WechatUser();
                    newUser.W_OpenId = id;
                    newUser.W_UserType = 0;
                    newUser.W_IsRegister = 0;
                    newUser.W_FirstLogin = DateTime.Now;
                    newUser.W_LastLogin = DateTime.Now;
                    _hR_WechatUserBus.AddDataAsync(newUser).Wait();
                    return Success(newUser);
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }


        //根据openId更新用户手机号
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult UpdataUserPhone(string id, string mobile)
        {
            try
            {
                var user = _hR_WechatUserBus.GetTheDataAsync(id).Result;
                if (user != null)
                {
                    //同时判断身份,
                    var result = _hR_EntryBus.IsInterviewers(mobile);
                    if (result)
                    {
                        //是
                        user.W_UserType = 2;
                        user.W_Phone = mobile;
                        _hR_WechatUserBus.UpdateDataAsync(user).Wait();
                    }
                    else
                    {
                        //否
                        user.W_UserType = 1;
                        user.W_Phone = mobile;
                        _hR_WechatUserBus.UpdateDataAsync(user).Wait();
                    }

                    return Success(user);
                }
                else
                {
                    return Error("无此用户");
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 更新打卡手机号
        /// </summary>
        /// <param name="id"></param>
        /// <param name="mobile"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult UpdataClockUserPhone(string id, string mobile)
        {
            try
            {
                var user = _wechat_UserBusiness.GetTheDataAsync(id).Result;
                if (user != null)
                {
                    //同时判断身份,
                    var result = _hR_EntryBus.GetInterviewers(mobile);
                    if (!result.IsNullOrEmpty())
                    {
                        //是
                        if (result.NameUser == "刘栋" || result.NameUser == "冉江华" || result.NameUser == "李睿" || result.NameUser == "纪殿凯"
                            || result.F_PositionId == "E0A67419-224E-4178-AB08-6E685BA7DD77" ||
                            mobile == "***********" || mobile == "***********")
                        {
                            user.W_UserType = 2;
                        }
                        else
                        {
                            user.W_UserType = 1;
                        }
                        user.W_Phone = mobile;
                        user.W_Truename = result.NameUser;
                        user.Id = result.F_Id;
                        _wechat_UserBusiness.UpdateDataAsync(user).Wait();
                    }
                    else
                    {
                        //无信息
                        user.W_UserType = 0;
                        user.W_Phone = mobile;
                        _wechat_UserBusiness.UpdateDataAsync(user).Wait();
                    }

                    return Success(user);
                }
                else
                {
                    return Error("无此用户");
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        //根据openId更新用户信息
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult UpdataUserInfo()
        {
            try
            {
                string id = HttpContext.Request.Form["id"].ToString();
                string nickName = HttpContext.Request.Form["nickName"].ToString();
                string language = HttpContext.Request.Form["language"].ToString();
                string city = HttpContext.Request.Form["city"].ToString();
                string province = HttpContext.Request.Form["province"].ToString();
                string country = HttpContext.Request.Form["country"].ToString();
                string avatarUrl = HttpContext.Request.Form["avatarUrl"].ToString();
                int gender = HttpContext.Request.Form["gender"].ToString().ToInt();
                var user = _hR_WechatUserBus.GetTheDataAsync(id).Result;
                if (user != null)
                {
                    user.W_NickName = nickName;
                    user.W_Language = language;
                    user.W_City = city;
                    user.W_Province = province;
                    user.W_Country = country;
                    user.W_Gender = gender;
                    user.W_Icon = avatarUrl;
                    user.W_IsRegister = 1;
                    _hR_WechatUserBus.UpdateDataAsync(user).Wait();
                    return Success(user);
                }
                else
                {
                    return Error("无此用户");
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        //获取openId
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetExamOpenid()
        {
            try
            {
                string code = HttpContext.Request.Form["code"].ToString();
                if (code.IsNullOrEmpty())
                {
                    return Error("微信服务器波动");
                }
                string appid = "wxa9d16a5e8f83d61a";
                string secret = "f4c68e2eeb5f26d9a5a702abd4e5a498";
                string grant_type = "authorization_code";
                using (var httpClient = new HttpClient())
                {
                    //post
                    var url = new System.Uri("https://api.weixin.qq.com/sns/jscode2session");
                    var body = new FormUrlEncodedContent(new Dictionary<string, string>
     {
         { "appid", appid},
         { "secret", secret },
         { "js_code", code},
         { "grant_type",grant_type}

     });
                    // response
                    var response = httpClient.PostAsync(url, body).Result;
                    var data = response.Content.ReadAsStringAsync().Result;
                    if (data!=null)
                    {
                        var pm = JsonConvert.DeserializeObject<Code2Session>(data);
                        return Success(pm);
                    }
                    else
                    {
                        return Error("请求微信服务器失败");
                    }
                }
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }

        }
        #endregion
    }
    public class gOpenIdModel
    {
        public WxType wxType { get; set; }
        public string code
        { get; set; }
    }
    public enum WxType
    {
        /// <summary>
        /// 计划小程序
        /// </summary>
        Plan = 522,
        /// <summary>
        /// 打卡小程序
        /// </summary>
        Clock = 422
    }
}