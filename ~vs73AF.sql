--�������λ����
SELECT DISTINCT
t.F_Id,
Employ<PERSON><PERSON><PERSON>,
t.NameUser as UserName,
t1.F_InductionDate,
t2.Name AS DepartmentName,
t3.F_FullName as CompanyName,
t4.F_Name as Position,
t1.F_InductionDate,
t5.ContractEffectDate,
t5.ContractEndDate,
t.<PERSON>d<PERSON>ard<PERSON>,
t.EmployRel<PERSON>tatus,
depart.F_TrueDepartureDate,
t.F_Rank as 'ְ��',
dict.F_SortCode as 'ְ������',
CONVERT(varchar(100),com1.F_StartTime, 23) as '����ʱ��',
com1.F_PositionInfo as '����ְλ',
com1.F_Rank as '����ְ��',
(CONVERT(varchar(100),com1.F_StartTime, 23)+'��Ҫ��ְ'+com1.F_PositionInfo) as '������¼',
stuff((select  ','+CONVERT(varchar(100),com2.F_StartTime, 23)+com2.F_PositionInfo+com2.F_Rank from HR_CompanyEmploy com2 where com2.F_UserId=t.F_Id order by com2.F_StartTime desc  for xml 
path('')),1,1,'')  as '��λ�䶯��¼',
case t.sex when 1 then '��' when 0 then 'Ů' else '' end as '�Ա�'
,
CONVERT(varchar(100),poti.F_PositiveDate, 23) as 'ת��ʱ��'
FROM
HR_FormalEmployees t
LEFT JOIN HR_Induction t1 ON t.F_Id = t1.F_UserId
LEFT JOIN Base_Department t2 ON t.F_DepartmentId = t2.Id
LEFT JOIN Base_Company t3 ON t.F_CompanyId = t3.F_Id
LEFT JOIN Base_Post t4 ON t.F_PositionId = t4.F_Id
LEFT JOIN HR_LaborContractInfo t5 on t.F_Id = t5.UserId and ContractEndDate = (select max(ContractEndDate) from HR_LaborContractInfo lab where lab.UserId=t5.UserId)
LEFT JOIN HR_Departure depart on t.F_Id = depart.F_UserId
left join HR_DataDictionaryDetails dict on dict.F_ItemValue=t.F_Rank and dict.F_ItemId='1321100210314678272'
left join   HR_Positive poti ON t.F_Id = poti.F_UserId
left join HR_CompanyEmploy com1 on com1.F_UserId=t.F_Id  and com1.F_StartTime =(select max(F_StartTime) from HR_CompanyEmploy oldcom1 where com1.F_UserId=oldcom1.F_UserId and oldcom1.F_ChangesReason='����' and datediff(yyyy,com1.F_StartTime,GETDATE())<3)
where 1=1 
order by dict.F_SortCode desc




--�������Ƹ�������
select sum(Ͷ�ݼ�������) Ͷ�ݼ�������,sum(ͬ����������) as ͬ����������,sum(�ƻ��μ���������) as �ƻ��μ���������,
sum(ʵ�ʲμ���������) as ʵ�ʲμ���������,sum(δ�μ���������) as δ�μ���������,sum(ͨ����������) as ͨ����������,sum(ͨ��) as ¼������  from (
SELECT  CONVERT(varchar(100) , rc.F_CreateDate, 23) ����,
count(1) Ͷ�ݼ�������,
sum(case when  itp.F_CreateDate is not null then 1 else 0 end) as 'ͬ����������',
(select count(1) from HR_InterviewPlan where CONVERT(varchar(100), F_InterTime, 23) = CONVERT(varchar(100), rc.F_CreateDate, 23)) as '�ƻ��μ���������',
(select count(1) from HR_InterviewEvaluation where CONVERT(varchar(100), F_ModifyDate, 23) = CONVERT(varchar(100), rc.F_CreateDate, 23)) as 'ʵ�ʲμ���������',
(select count(1) from HR_InterviewPlan where CONVERT(varchar(100), F_InterTime, 23) = CONVERT(varchar(100), rc.F_CreateDate, 23) and F_IsInterview=0) as 'δ�μ���������',
(select count(1) from HR_InterviewEvaluation where CONVERT(varchar(100), F_ModifyDate, 23) = CONVERT(varchar(100), rc.F_CreateDate, 23) and F_IsThrough=1) as 'ͨ����������',
sum(case when F_Through =1 and F_IsInvitedInt=1 then 1 else 0 end) ͨ��
FROM HR_RecruitmentCandidates rc
left join HR_InterviewPlan  itp on rc.F_PostId=itp.F_PostId and rc.F_UserId=itp.F_ApplicantId and itp.F_RoundInterview=1 
and CONVERT(varchar(100), itp.F_CreateDate, 23)=CONVERT(varchar(100), rc.F_CreateDate, 23)
where 1=1 and rc.F_IsDelete=0
and CONVERT(varchar(100), rc.F_CreateDate, 23)>='2021-01-01' 
and  CONVERT(varchar(100), rc.F_CreateDate, 23)<='2021-12-31'
group by  CONVERT(varchar(100), rc.F_CreateDate, 23)
) d
--����ĸ�������Ƹ�������
--select sum(Ͷ�ݼ�������) Ͷ�ݼ�������,sum(ͬ����������) as ͬ����������,sum(�ƻ��μ���������) as �ƻ��μ���������,
--sum(ʵ�ʲμ���������) as ʵ�ʲμ���������,sum(δ�μ���������) as δ�μ���������,sum(ͨ����������) as ͨ����������,sum(ͨ��) as ¼������  from (
--SELECT  CONVERT(varchar(100) , rc.F_CreateDate, 23) ����,
--count(1) Ͷ�ݼ�������,
--sum(case when  itp.F_CreateDate is not null then 1 else 0 end) as 'ͬ����������',
--(select count(1) from HR_InterviewPlan where CONVERT(varchar(100), F_InterTime, 23) = CONVERT(varchar(100), rc.F_CreateDate, 23)) as '�ƻ��μ���������',
--(select count(1) from HR_InterviewEvaluation where CONVERT(varchar(100), F_ModifyDate, 23) = CONVERT(varchar(100), rc.F_CreateDate, 23)) as 'ʵ�ʲμ���������',
--(select count(1) from HR_InterviewPlan where CONVERT(varchar(100), F_InterTime, 23) = CONVERT(varchar(100), rc.F_CreateDate, 23) and F_IsInterview=0) as 'δ�μ���������',
--(select count(1) from HR_InterviewEvaluation where CONVERT(varchar(100), F_ModifyDate, 23) = CONVERT(varchar(100), rc.F_CreateDate, 23) and F_IsThrough=1) as 'ͨ����������',
--sum(case when F_Through =1 and F_IsInvitedInt=1 then 1 else 0 end) ͨ��
--FROM HR_RecruitmentCandidates rc
--left join HR_InterviewPlan  itp on rc.F_PostId=itp.F_PostId and rc.F_UserId=itp.F_ApplicantId and itp.F_RoundInterview=1 
--and CONVERT(varchar(100), itp.F_CreateDate, 23)=CONVERT(varchar(100), rc.F_CreateDate, 23)
--where 1=1 and rc.F_IsDelete=0
--and CONVERT(varchar(100), rc.F_CreateDate, 23)>='2021-01-01' 
--and  CONVERT(varchar(100), rc.F_CreateDate, 23)<='2021-12-31'
--group by  CONVERT(varchar(100), rc.F_CreateDate, 23)
--) d


SELECT * FROM HR_InterviewPlan WHERE F_ApplicantId='039FD66302EB0-61069F1D-EE50-45FD-A4AE-68B41056EA3B'


SELECT F_RecruitName,sum(Ͷ�ݼ�������)as Ͷ�ݼ�������,sum(����) as ����,sum(����) ����,sum(ͨ��) ͨ��,sum(������ְ) as ������ְ FROM (
SELECT  recruit.F_RecruitName,
count(1) Ͷ�ݼ�������,
(select count(1) from( SELECT COUNT(1) as c FROM HR_InterviewPlan WHERE F_RoundInterview=1  AND F_RecCanId=rc.F_Id and F_IsInterview=1 group by F_InterviewName)de ) as '����',
(select count(1) from( SELECT COUNT(1) as c FROM HR_InterviewPlan WHERE F_RoundInterview=2  AND F_RecCanId=rc.F_Id and F_IsInterview=1 group by F_InterviewName)de) as '����',
sum(case when F_Through =1 and F_IsInvitedInt=1 then 1 else 0 end) ͨ��,
sum(case when F_Through =1 and F_IsGiveUpInterview=3 then 1 else 0 end) ������ְ
FROM HR_RecruitmentCandidates rc
left join HR_Recruit recruit on rc.F_RecruitId=recruit.F_Id 
where 1=1 and rc.F_IsDelete=0
and CONVERT(varchar(100), rc.F_CreateDate, 23)>='2021-01-01' 
and  CONVERT(varchar(100), rc.F_CreateDate, 23)<='2022-01-11'
group by  recruit.F_RecruitName,rc.F_Id
) D 
where F_RecruitName='�ʼ칤��ʦ'
GROUP BY F_RecruitName


 SELECT * FROM HR_InterviewPlan WHERE F_RoundInterview=2 AND F_IsPassingInter=1 and F_PostId='B779343A-45B3-4A59-BF26-5A8B39825DF5'

SELECT * FROM HR_RecruitmentCandidates WHERE F_RecruitId='fe8ccbb6486b423daa94f4b65f23798c'

sum(case when F_Through =1 and F_IsInvitedInt=1 then 1 else 0 end) ͨ������,
sum(case when F_RoundInterview=1 and F_IsPassingInter=1 then 1 else 0 end ) ����,
sum(case when F_RoundInterview=2 and F_IsPassingInter=1 then 1 else 0 end ) ����
--
	SELECT
	(select count(1) from  [HRSystem].[dbo].[HR_Entry]  where 1=1 and F_CreateDate > convert(char(4),DATEPART(yyyy, getdate()))+'-01-01' and F_CreateDate< convert(char(4),DATEPART(yyyy, getdate()))+'-12-31' AND F_FileId IS NOT NULL) AS ��ְ,
	(select count(1) from  HR_RecruitmentCandidates  where  F_IsInvitedInt=1 and F_IsDelete=0 and F_CreateDate > convert(char(4),DATEPART(yyyy, getdate()))+'-01-01' and F_CreateDate<convert(char(4),DATEPART(yyyy, getdate()))+'-12-31') AS ����,
	(select count(1) from  HR_RecruitmentCandidates  where  F_Through=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-01-01' and F_ThroughTime<convert(char(4),DATEPART(yyyy, getdate()))+'-12-31') AS ����ͨ��,
	(select count(1) from  HR_RecruitmentCandidates  where  F_Through=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-01-01' and F_ThroughTime<convert(char(4),DATEPART(yyyy, getdate()))+'-12-31')*1.0 / 
	(select count(1) from  HR_RecruitmentCandidates  where  F_IsInvitedInt=1 and F_IsDelete=0 and F_CreateDate > convert(char(4),DATEPART(yyyy, getdate()))+'-01-01' and F_CreateDate<convert(char(4),DATEPART(yyyy, getdate()))+'-12-31')*1.0 AS ����ͨ����

