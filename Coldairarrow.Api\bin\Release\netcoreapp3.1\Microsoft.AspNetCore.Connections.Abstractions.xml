<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Connections.Abstractions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Internal.ActivatorUtilities">
            <summary>
            Helper code for the various activator services.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Internal.ActivatorUtilities.CreateInstance(System.IServiceProvider,System.Type,System.Object[])">
            <summary>
            Instantiate a type with constructor arguments provided directly and/or from an <see cref="T:System.IServiceProvider"/>.
            </summary>
            <param name="provider">The service provider used to resolve dependencies</param>
            <param name="instanceType">The type to activate</param>
            <param name="parameters">Constructor arguments not provided by the <paramref name="provider"/>.</param>
            <returns>An activated object of type instanceType</returns>
        </member>
        <member name="M:Microsoft.Extensions.Internal.ActivatorUtilities.CreateFactory(System.Type,System.Type[])">
            <summary>
            Create a delegate that will instantiate a type with constructor arguments provided directly
            and/or from an <see cref="T:System.IServiceProvider"/>.
            </summary>
            <param name="instanceType">The type to activate</param>
            <param name="argumentTypes">
            The types of objects, in order, that will be passed to the returned function as its second parameter
            </param>
            <returns>
            A factory that will instantiate instanceType using an <see cref="T:System.IServiceProvider"/>
            and an argument array containing objects matching the types defined in argumentTypes
            </returns>
        </member>
        <member name="M:Microsoft.Extensions.Internal.ActivatorUtilities.CreateInstance``1(System.IServiceProvider,System.Object[])">
            <summary>
            Instantiate a type with constructor arguments provided directly and/or from an <see cref="T:System.IServiceProvider"/>.
            </summary>
            <typeparam name="T">The type to activate</typeparam>
            <param name="provider">The service provider used to resolve dependencies</param>
            <param name="parameters">Constructor arguments not provided by the <paramref name="provider"/>.</param>
            <returns>An activated object of type T</returns>
        </member>
        <member name="M:Microsoft.Extensions.Internal.ActivatorUtilities.GetServiceOrCreateInstance``1(System.IServiceProvider)">
            <summary>
            Retrieve an instance of the given type from the service provider. If one is not found then instantiate it directly.
            </summary>
            <typeparam name="T">The type of the service</typeparam>
            <param name="provider">The service provider used to resolve dependencies</param>
            <returns>The resolved service or created instance</returns>
        </member>
        <member name="M:Microsoft.Extensions.Internal.ActivatorUtilities.GetServiceOrCreateInstance(System.IServiceProvider,System.Type)">
            <summary>
            Retrieve an instance of the given type from the service provider. If one is not found then instantiate it directly.
            </summary>
            <param name="provider">The service provider</param>
            <param name="type">The type of the service</param>
            <returns>The resolved service or created instance</returns>
        </member>
        <member name="T:Microsoft.Extensions.Internal.ActivatorUtilitiesConstructorAttribute">
            <summary>
            Marks the constructor to be used when activating type using <see cref="T:Microsoft.Extensions.Internal.ActivatorUtilities"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Internal.ObjectFactory">
            <summary>
            The result of <see cref="M:Microsoft.Extensions.Internal.ActivatorUtilities.CreateFactory(System.Type,System.Type[])"/>.
            </summary>
            <param name="serviceProvider">The <see cref="T:System.IServiceProvider"/> to get service arguments from.</param>
            <param name="arguments">Additional constructor arguments.</param>
            <returns>The instantiated type.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Connections.ConnectionContext">
            <summary>
            Encapsulates all information about an individual connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Connections.ConnectionContext.ConnectionId">
            <summary>
            Gets or sets a unique identifier to represent this connection in trace logs.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Connections.ConnectionContext.Features">
            <summary>
            Gets the collection of features provided by the server and middleware available on this connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Connections.ConnectionContext.Items">
            <summary>
            Gets or sets a key/value collection that can be used to share data within the scope of this connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Connections.ConnectionContext.Transport">
            <summary>
            Gets or sets the <see cref="T:System.IO.Pipelines.IDuplexPipe"/> that can be used to read or write data on this connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Connections.ConnectionContext.ConnectionClosed">
            <summary>
            Triggered when the client connection is closed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Connections.ConnectionContext.LocalEndPoint">
            <summary>
            Gets or sets the local endpoint for this connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Connections.ConnectionContext.RemoteEndPoint">
            <summary>
            Gets or sets the remote endpoint for this connection.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Connections.ConnectionContext.Abort(Microsoft.AspNetCore.Connections.ConnectionAbortedException)">
            <summary>
            Aborts the underlying connection.
            </summary>
            <param name="abortReason">An optional <see cref="T:Microsoft.AspNetCore.Connections.ConnectionAbortedException"/> describing the reason the connection is being terminated.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Connections.ConnectionContext.Abort">
            <summary>
            Aborts the underlying connection.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Connections.ConnectionContext.DisposeAsync">
            <summary>
            Releases resources for the underlying connection.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.ValueTask"/> that completes when resources have been released.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Connections.ConnectionDelegate">
            <summary>
            A function that can process a connection.
            </summary>
            <param name="connection">A <see cref="T:Microsoft.AspNetCore.Connections.ConnectionContext" /> representing the connection.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the connection lifetime. When the task completes, the connection will be closed.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Connections.ConnectionHandler">
            <summary>
            Represents an end point that multiple connections connect to. For HTTP, endpoints are URLs, for non HTTP it can be a TCP listener (or similar)
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Connections.ConnectionHandler.OnConnectedAsync(Microsoft.AspNetCore.Connections.ConnectionContext)">
            <summary>
            Called when a new connection is accepted to the endpoint
            </summary>
            <param name="connection">The new <see cref="T:Microsoft.AspNetCore.Connections.ConnectionContext"/></param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the connection lifetime. When the task completes, the connection is complete.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Connections.DefaultConnectionContext.#ctor(System.String)">
            <summary>
            Creates the DefaultConnectionContext without Pipes to avoid upfront allocations.
            The caller is expected to set the <see cref="P:Microsoft.AspNetCore.Connections.DefaultConnectionContext.Transport"/> and <see cref="P:Microsoft.AspNetCore.Connections.DefaultConnectionContext.Application"/> pipes manually.
            </summary>
            <param name="id"></param>
        </member>
        <member name="T:Microsoft.AspNetCore.Connections.Features.IConnectionCompleteFeature">
            <summary>
            Represents the completion action for a connection.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Connections.Features.IConnectionCompleteFeature.OnCompleted(System.Func{System.Object,System.Threading.Tasks.Task},System.Object)">
            <summary>
            Registers a callback to be invoked after a connection has fully completed processing. This is
            intended for resource cleanup.
            </summary>
            <param name="callback">The callback to invoke after the connection has completed processing.</param>
            <param name="state">The state to pass into the callback.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Connections.Features.IConnectionInherentKeepAliveFeature">
            <summary>
            Indicates if the connection transport has an "inherent keep-alive", which means that the transport will automatically
            inform the client that it is still present.
            </summary>
            <remarks>
            The most common example of this feature is the Long Polling HTTP transport, which must (due to HTTP limitations) terminate
            each poll within a particular interval and return a signal indicating "the server is still here, but there is no data yet".
            This feature allows applications to add keep-alive functionality, but limit it only to transports that don't have some kind
            of inherent keep-alive.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Connections.FileHandleType">
            <summary>
            Enumerates the <see cref="T:Microsoft.AspNetCore.Connections.FileHandleEndPoint"/> types.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Connections.IConnectionBuilder">
            <summary>
            Defines an interface that provides the mechanisms to configure a connection pipeline.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Connections.IConnectionBuilder.ApplicationServices">
            <summary>
            Gets the <see cref="T:System.IServiceProvider"/> that provides access to the application's service container.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Connections.IConnectionBuilder.Use(System.Func{Microsoft.AspNetCore.Connections.ConnectionDelegate,Microsoft.AspNetCore.Connections.ConnectionDelegate})">
            <summary>
            Adds a middleware delegate to the application's connection pipeline.
            </summary>
            <param name="middleware">The middleware delegate.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Connections.IConnectionBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Connections.IConnectionBuilder.Build">
            <summary>
            Builds the delegate used by this application to process connections.
            </summary>
            <returns>The connection handling delegate.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Connections.IConnectionFactory">
            <summary>
            A factory abstraction for creating connections to an endpoint.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Connections.IConnectionFactory.ConnectAsync(System.Net.EndPoint,System.Threading.CancellationToken)">
            <summary>
            Creates a new connection to an endpoint.
            </summary>
            <param name="endpoint">The <see cref="T:System.Net.EndPoint"/> to connect to.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.ValueTask`1" /> that represents the asynchronous connect, yielding the <see cref="T:Microsoft.AspNetCore.Connections.ConnectionContext" /> for the new connection when completed.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Connections.IConnectionListener">
            <summary>
            Defines an interface that represents a listener bound to a specific <see cref="P:Microsoft.AspNetCore.Connections.IConnectionListener.EndPoint"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Connections.IConnectionListener.EndPoint">
            <summary>
            The endpoint that was bound. This may differ from the requested endpoint, such as when the caller requested that any free port be selected.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Connections.IConnectionListener.AcceptAsync(System.Threading.CancellationToken)">
            <summary>
            Begins an asynchronous operation to accept an incoming connection.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A <see cref="T:System.Threading.Tasks.ValueTask`1"/> that completes when a connection is accepted, yielding the <see cref="T:Microsoft.AspNetCore.Connections.ConnectionContext" /> representing the connection.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Connections.IConnectionListener.UnbindAsync(System.Threading.CancellationToken)">
            <summary>
            Stops listening for incoming connections.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A <see cref="T:System.Threading.Tasks.ValueTask"/> that represents the un-bind operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Connections.IConnectionListenerFactory">
            <summary>
            Defines an interface that provides the mechanisms for binding to various types of <see cref="T:System.Net.EndPoint"/>s.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Connections.IConnectionListenerFactory.BindAsync(System.Net.EndPoint,System.Threading.CancellationToken)">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Connections.IConnectionListener"/> bound to the specified <see cref="T:System.Net.EndPoint"/>.
            </summary>
            <param name="endpoint">The <see cref="T:System.Net.EndPoint" /> to bind to.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A <see cref="T:System.Threading.Tasks.ValueTask`1"/> that completes when the listener has been bound, yielding a <see cref="T:Microsoft.AspNetCore.Connections.IConnectionListener" /> representing the new listener.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Connections.UriEndPoint">
            <summary>
            An <see cref="T:System.Net.EndPoint"/> defined by a <see cref="T:System.Uri"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Connections.UriEndPoint.#ctor(System.Uri)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Connections.UriEndPoint"/> class.
            </summary>
            <param name="uri">The <see cref="T:System.Uri"/> defining the <see cref="T:System.Net.EndPoint"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Connections.UriEndPoint.Uri">
            <summary>
            The <see cref="T:System.Uri"/> defining the <see cref="T:System.Net.EndPoint"/>.
            </summary>
        </member>
    </members>
</doc>
