﻿using Coldairarrow.Entity.S_School;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.IBusiness;

namespace Coldairarrow.Business.S_School
{
    public class S_CoureCommentBusiness : BaseBusiness<S_CoureComment>, IS_CoureCommentBusiness, ITransientDependency
    {
        public S_CoureCommentBusiness(IDbAccessor db, IOperator @operator)
            : base(db)
        {
            _operator = @operator;
        }
        private IOperator _operator;
        #region 外部接口

        public async Task<PageResult<S_CoureComment>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<S_CoureComment>();
            var search = input.Search;
            input.SortField = "F_CreateDate";
            input.SortType = "desc";
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<S_CoureComment, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            var CommentList = await q.Where(where).GetPageResultAsync(input);
            if (CommentList.Data.Count > 0)
            {
                var ContIds = CommentList.Data.Where(i => !string.IsNullOrEmpty(i.F_ContentId))
                            .Select(i => i.F_ContentId).ToList();
                List<S_ContentPage> s_Contents = new List<S_ContentPage>();
                if (ContIds.Count > 0)
                {
                    foreach (var item in ContIds)
                    {
                        var s_Content = await this.Db.GetEntityAsync<S_ContentPage>(item);
                        if (s_Content != null)
                        {
                            s_Contents.Add(s_Content);
                        }
                    }
                }
                ///获取用户信息
                foreach (var item in CommentList.Data)
                {
                    item.IsHits = 0;
                    item.IsFinger = 0;
                    //显示课程名称
                    if (!string.IsNullOrEmpty(item.F_ContentId))
                    {
                        item.F_ContentName = s_Contents.Find(i => i.F_Id == item.F_ContentId)?.F_ShortTitle;
                        if (string.IsNullOrEmpty(item.F_ContentName))
                        {
                            item.F_ContentName = s_Contents.Find(i => i.F_Id == item.F_ContentId)?.F_Title;
                        }
                    }
                    if (!string.IsNullOrEmpty(item.F_UserId))
                    {
                        if (item.F_UserId == "Admin")
                        {
                            item.UserName = "超级管理员";
                        }
                        else
                        {
                            var hR_Formal = await this.Db.GetIQueryable<HR_FormalEmployees>()
                                         .Where(i => i.BaseUserId == item.F_UserId).FirstOrDefaultAsync();
                            if (hR_Formal != null)
                            {
                                item.UserName = hR_Formal.NameUser;
                            }
                        }
                    }
                    //获取当前用户是否点赞
                    var Cont = await this.Db.GetIQueryable<S_ContentRead>()
                                    .Where(i => i.F_CreateUserId == _operator.UserId && i.F_ContentId == item.F_Id)
                                    .ToListAsync();
                    if (Cont.Count > 0)
                    {
                        item.IsHits = Cont.Find(i => i.F_Action == "CUp") != null ? 1 : 0;
                        item.IsFinger = Cont.Find(i => i.F_Action == "CFinger") != null ? 1 : 0;
                    }
                }
            }
            return CommentList;
        }

        public async Task<S_CoureComment> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(S_CoureComment data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(S_CoureComment data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            if (ids.Count > 0)
            {
                List<S_ContentPage> contentPages = new List<S_ContentPage>();
                //删除评论
                foreach (var item in ids)
                {
                    var s_Coure = await GetEntityAsync(item);
                    if (s_Coure != null)
                    {
                        var contentPage = await this.Db.GetEntityAsync<S_ContentPage>(s_Coure.F_ContentId);
                        if (contentPage != null)
                        {
                            if (contentPage.F_IsDiscuss.HasValue && contentPage.F_IsDiscuss >= 0)
                            {
                                contentPage.F_IsDiscuss--;
                            }
                        }
                        contentPages.Add(contentPage);
                    }
                }
                await DeleteAsync(ids);
                if (contentPages.Count > 0)
                {
                    await this.Db.UpdateAsync(contentPages);
                }

            }
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<S_CoureComment>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<S_CoureComment, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<S_CoureComment, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}