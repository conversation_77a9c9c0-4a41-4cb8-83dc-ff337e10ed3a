{"version": 2, "dgSpecHash": "6RuxnPs01YI=", "success": true, "projectFilePath": "E:\\Code\\HRSystem\\Coldairarrow.Api\\05.Coldairarrow.Api.csproj", "expectedPackageFiles": ["d:\\Users\\lijf\\.nuget\\packages\\alibabacloud.endpointutil\\0.1.1\\alibabacloud.endpointutil.0.1.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\alibabacloud.gatewayspi\\0.0.2\\alibabacloud.gatewayspi.0.0.2.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\alibabacloud.openapiclient\\0.1.10\\alibabacloud.openapiclient.0.1.10.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\alibabacloud.openapiutil\\1.1.1\\alibabacloud.openapiutil.1.1.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\alibabacloud.sdk.waf-openapi20211001\\4.4.0\\alibabacloud.sdk.waf-openapi20211001.4.4.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\alibabacloud.teautil\\0.1.18\\alibabacloud.teautil.0.1.18.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\alibabacloud.teaxml\\0.0.5\\alibabacloud.teaxml.0.0.5.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\aliyun-net-sdk-core\\1.5.10\\aliyun-net-sdk-core.1.5.10.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\aliyun-net-sdk-nlp-automl\\0.0.9\\aliyun-net-sdk-nlp-automl.0.0.9.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\aliyun.credentials\\1.3.2\\aliyun.credentials.1.3.2.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\aliyun.oss.sdk.netcore\\2.13.0\\aliyun.oss.sdk.netcore.2.13.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\automapper\\10.0.0\\automapper.10.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\caching.csredis\\3.6.5\\caching.csredis.3.6.5.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\castle.core\\4.4.1\\castle.core.4.4.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\castle.core.asyncinterceptor\\1.7.0\\castle.core.asyncinterceptor.1.7.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\colder.logging.abstractions\\1.0.2\\colder.logging.abstractions.1.0.2.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\colder.logging.serilog\\1.0.2\\colder.logging.serilog.1.0.2.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\csrediscore\\3.6.5\\csrediscore.3.6.5.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\documentformat.openxml\\2.13.1\\documentformat.openxml.2.13.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\dynamitey\\2.0.10.189\\dynamitey.2.0.10.189.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\efcore.sharding\\3.1.8.4\\efcore.sharding.3.1.8.4.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\efcore.sharding.sqlserver\\3.1.8.4\\efcore.sharding.sqlserver.3.1.8.4.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\elasticsearch.net\\7.8.1\\elasticsearch.net.7.8.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\idhelper\\1.4.1\\idhelper.1.4.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\idhelper.zookeeper\\1.5.1\\idhelper.zookeeper.1.5.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\ikvm\\8.2.0\\ikvm.8.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\linqkit.microsoft.entityframeworkcore\\3.0.0\\linqkit.microsoft.entityframeworkcore.3.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore\\2.2.0\\microsoft.aspnetcore.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.authentication.abstractions\\2.2.0\\microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.authentication.core\\2.2.0\\microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.authorization\\2.2.0\\microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.authorization.policy\\2.2.0\\microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.connections.abstractions\\2.2.0\\microsoft.aspnetcore.connections.abstractions.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.diagnostics\\2.2.0\\microsoft.aspnetcore.diagnostics.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.diagnostics.abstractions\\2.2.0\\microsoft.aspnetcore.diagnostics.abstractions.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.hostfiltering\\2.2.0\\microsoft.aspnetcore.hostfiltering.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.hosting\\2.2.0\\microsoft.aspnetcore.hosting.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.hosting.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.hosting.server.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.http\\2.2.0\\microsoft.aspnetcore.http.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.http.abstractions\\2.2.0\\microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.http.extensions\\2.2.0\\microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.http.features\\2.2.0\\microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.httpoverrides\\2.2.0\\microsoft.aspnetcore.httpoverrides.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.jsonpatch\\3.1.8\\microsoft.aspnetcore.jsonpatch.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.mvc.abstractions\\2.2.0\\microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.mvc.apiexplorer\\1.0.4\\microsoft.aspnetcore.mvc.apiexplorer.1.0.4.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.mvc.core\\2.2.5\\microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.mvc.formatters.json\\1.0.4\\microsoft.aspnetcore.mvc.formatters.json.1.0.4.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.mvc.newtonsoftjson\\3.1.8\\microsoft.aspnetcore.mvc.newtonsoftjson.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.responsecaching.abstractions\\2.2.0\\microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.routing\\2.2.0\\microsoft.aspnetcore.routing.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.routing.abstractions\\2.2.0\\microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.server.iis\\2.2.0\\microsoft.aspnetcore.server.iis.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.server.iisintegration\\2.2.0\\microsoft.aspnetcore.server.iisintegration.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.server.kestrel\\2.2.0\\microsoft.aspnetcore.server.kestrel.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.server.kestrel.core\\2.2.0\\microsoft.aspnetcore.server.kestrel.core.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.server.kestrel.https\\2.2.0\\microsoft.aspnetcore.server.kestrel.https.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.server.kestrel.transport.abstractions\\2.2.0\\microsoft.aspnetcore.server.kestrel.transport.abstractions.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.server.kestrel.transport.sockets\\2.2.0\\microsoft.aspnetcore.server.kestrel.transport.sockets.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.staticfiles\\1.0.4\\microsoft.aspnetcore.staticfiles.1.0.4.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.aspnetcore.webutilities\\2.2.0\\microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\1.1.1\\microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.bcl.hashcode\\1.1.0\\microsoft.bcl.hashcode.1.1.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.data.sqlclient\\2.0.0\\microsoft.data.sqlclient.2.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.data.sqlclient.sni.runtime\\2.0.0\\microsoft.data.sqlclient.sni.runtime.2.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.entityframeworkcore\\3.1.8\\microsoft.entityframeworkcore.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\3.1.8\\microsoft.entityframeworkcore.abstractions.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.entityframeworkcore.analyzers\\3.1.8\\microsoft.entityframeworkcore.analyzers.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.entityframeworkcore.relational\\3.1.8\\microsoft.entityframeworkcore.relational.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.entityframeworkcore.sqlserver\\3.1.8\\microsoft.entityframeworkcore.sqlserver.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.entityframeworkcore.sqlserver.nettopologysuite\\3.1.8\\microsoft.entityframeworkcore.sqlserver.nettopologysuite.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.apidescription.server\\3.0.0\\microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\3.1.8\\microsoft.extensions.caching.abstractions.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.caching.memory\\3.1.8\\microsoft.extensions.caching.memory.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.configuration\\3.1.8\\microsoft.extensions.configuration.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\3.1.8\\microsoft.extensions.configuration.abstractions.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.configuration.binder\\3.1.8\\microsoft.extensions.configuration.binder.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\2.2.0\\microsoft.extensions.configuration.commandline.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\2.2.0\\microsoft.extensions.configuration.environmentvariables.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\2.2.0\\microsoft.extensions.configuration.fileextensions.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.configuration.json\\2.2.0\\microsoft.extensions.configuration.json.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\2.2.0\\microsoft.extensions.configuration.usersecrets.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\3.1.8\\microsoft.extensions.dependencyinjection.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\3.1.8\\microsoft.extensions.dependencyinjection.abstractions.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.dependencymodel\\6.0.0\\microsoft.extensions.dependencymodel.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\3.1.8\\microsoft.extensions.fileproviders.abstractions.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.fileproviders.embedded\\1.0.1\\microsoft.extensions.fileproviders.embedded.1.0.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\2.2.0\\microsoft.extensions.fileproviders.physical.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\2.2.0\\microsoft.extensions.filesystemglobbing.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\3.1.8\\microsoft.extensions.hosting.abstractions.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.http\\3.1.8\\microsoft.extensions.http.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.logging\\3.1.8\\microsoft.extensions.logging.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\6.0.0\\microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.logging.configuration\\2.2.0\\microsoft.extensions.logging.configuration.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.logging.console\\2.2.0\\microsoft.extensions.logging.console.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.logging.debug\\2.2.0\\microsoft.extensions.logging.debug.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.logging.eventsource\\2.2.0\\microsoft.extensions.logging.eventsource.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.objectpool\\2.2.0\\microsoft.extensions.objectpool.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.options\\3.1.8\\microsoft.extensions.options.3.1.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\2.2.0\\microsoft.extensions.options.configurationextensions.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.primitives\\3.1.32\\microsoft.extensions.primitives.3.1.32.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.extensions.webencoders\\1.0.3\\microsoft.extensions.webencoders.1.0.3.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.identity.client\\4.14.0\\microsoft.identity.client.4.14.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\5.6.0\\microsoft.identitymodel.jsonwebtokens.5.6.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.identitymodel.logging\\5.6.0\\microsoft.identitymodel.logging.5.6.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.identitymodel.protocols\\5.6.0\\microsoft.identitymodel.protocols.5.6.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\5.6.0\\microsoft.identitymodel.protocols.openidconnect.5.6.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.identitymodel.tokens\\5.6.0\\microsoft.identitymodel.tokens.5.6.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.net.http.headers\\2.2.0\\microsoft.net.http.headers.2.2.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.1\\microsoft.netcore.platforms.1.1.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.netcore.targets\\1.1.3\\microsoft.netcore.targets.1.1.3.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\microsoft.win32.systemevents\\6.0.0\\microsoft.win32.systemevents.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\mysqlconnector\\0.69.9\\mysqlconnector.0.69.9.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\namotion.reflection\\1.0.13\\namotion.reflection.1.0.13.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\netcore.encrypt\\2.0.9\\netcore.encrypt.2.0.9.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\nettopologysuite\\2.0.0\\nettopologysuite.2.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\nettopologysuite.io.sqlserverbytes\\2.0.0\\nettopologysuite.io.sqlserverbytes.2.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\newtonsoft.json.bson\\1.0.2\\newtonsoft.json.bson.1.0.2.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\njsonschema\\10.1.26\\njsonschema.10.1.26.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\nodatime\\3.0.0\\nodatime.3.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\npgsql\\4.0.16\\npgsql.4.0.16.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\npoi\\2.5.1\\npoi.2.5.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\nswag.annotations\\13.7.4\\nswag.annotations.13.7.4.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\nswag.aspnetcore\\13.7.4\\nswag.aspnetcore.13.7.4.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\nswag.core\\13.7.4\\nswag.core.13.7.4.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\nswag.generation\\13.7.4\\nswag.generation.13.7.4.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\nswag.generation.aspnetcore\\13.7.4\\nswag.generation.aspnetcore.13.7.4.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\oracle.manageddataaccess.core\\2.19.180\\oracle.manageddataaccess.core.2.19.180.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\pipelines.sockets.unofficial\\2.2.8\\pipelines.sockets.unofficial.2.2.8.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\portable.bouncycastle\\1.8.6\\portable.bouncycastle.1.8.6.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\qrcoder\\1.3.9\\qrcoder.1.3.9.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\quartz\\3.1.0\\quartz.3.1.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\runtime.native.system.data.sqlclient.sni\\4.7.0\\runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\runtime.win-x64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\runtime.win-x86.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\serilog\\2.9.0\\serilog.2.9.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\serilog.aspnetcore\\3.4.0\\serilog.aspnetcore.3.4.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\serilog.extensions.hosting\\3.1.0\\serilog.extensions.hosting.3.1.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\serilog.extensions.logging\\3.0.1\\serilog.extensions.logging.3.0.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\serilog.formatting.compact\\1.1.0\\serilog.formatting.compact.1.1.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\serilog.formatting.elasticsearch\\8.4.1\\serilog.formatting.elasticsearch.8.4.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\serilog.settings.configuration\\3.1.0\\serilog.settings.configuration.3.1.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\serilog.sinks.console\\3.1.1\\serilog.sinks.console.3.1.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\serilog.sinks.debug\\1.0.1\\serilog.sinks.debug.1.0.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\serilog.sinks.elasticsearch\\8.4.1\\serilog.sinks.elasticsearch.8.4.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\serilog.sinks.file\\4.1.0\\serilog.sinks.file.4.1.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\serilog.sinks.periodicbatching\\2.1.1\\serilog.sinks.periodicbatching.2.1.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\sharpziplib\\1.3.3\\sharpziplib.1.3.3.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\stackexchange.redis\\2.8.16\\stackexchange.redis.2.8.16.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.collections.immutable\\1.7.1\\system.collections.immutable.1.7.1.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.collections.nongeneric\\4.3.0\\system.collections.nongeneric.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.collections.specialized\\4.3.0\\system.collections.specialized.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.componentmodel.annotations\\4.7.0\\system.componentmodel.annotations.4.7.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.componentmodel.primitives\\4.3.0\\system.componentmodel.primitives.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.componentmodel.typeconverter\\4.3.0\\system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.configuration.configurationmanager\\6.0.0\\system.configuration.configurationmanager.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.data.odbc\\6.0.0\\system.data.odbc.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.data.sqlclient\\4.8.6\\system.data.sqlclient.4.8.6.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.diagnostics.diagnosticsource\\4.7.1\\system.diagnostics.diagnosticsource.4.7.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.diagnostics.tools\\4.3.0\\system.diagnostics.tools.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.diagnostics.tracesource\\4.3.0\\system.diagnostics.tracesource.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.drawing.common\\6.0.0\\system.drawing.common.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.dynamic.runtime\\4.3.0\\system.dynamic.runtime.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.formats.asn1\\6.0.0\\system.formats.asn1.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.identitymodel.tokens.jwt\\5.6.0\\system.identitymodel.tokens.jwt.5.6.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.io.filesystem.accesscontrol\\5.0.0\\system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.io.packaging\\4.7.0\\system.io.packaging.4.7.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.io.pipelines\\5.0.1\\system.io.pipelines.5.0.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.linq.dynamic.core\\1.3.14\\system.linq.dynamic.core.1.3.14.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.net.nameresolution\\4.3.0\\system.net.nameresolution.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.private.datacontractserialization\\4.3.0\\system.private.datacontractserialization.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.private.servicemodel\\4.7.0\\system.private.servicemodel.4.7.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.private.uri\\4.3.2\\system.private.uri.4.3.2.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.reflection.dispatchproxy\\4.5.0\\system.reflection.dispatchproxy.4.5.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.reflection.emit\\4.7.0\\system.reflection.emit.4.7.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.reflection.metadata\\1.6.0\\system.reflection.metadata.1.6.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.runtime\\4.3.1\\system.runtime.4.3.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.runtime.caching\\4.7.0\\system.runtime.caching.4.7.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.runtime.loader\\4.3.0\\system.runtime.loader.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.runtime.serialization.formatters\\4.3.0\\system.runtime.serialization.formatters.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.runtime.serialization.json\\4.3.0\\system.runtime.serialization.json.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.runtime.serialization.primitives\\4.3.0\\system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.security.cryptography.cng\\5.0.0\\system.security.cryptography.cng.5.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.security.cryptography.pkcs\\6.0.1\\system.security.cryptography.pkcs.6.0.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.security.cryptography.protecteddata\\6.0.0\\system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.security.cryptography.xml\\4.5.0\\system.security.cryptography.xml.4.5.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.security.securestring\\4.3.0\\system.security.securestring.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.servicemodel.duplex\\4.4.4\\system.servicemodel.duplex.4.4.4.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.servicemodel.http\\4.4.4\\system.servicemodel.http.4.4.4.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.servicemodel.nettcp\\4.4.4\\system.servicemodel.nettcp.4.4.4.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.servicemodel.primitives\\4.7.0\\system.servicemodel.primitives.4.7.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.servicemodel.security\\4.4.4\\system.servicemodel.security.4.4.4.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.text.encoding.codepages\\6.0.0\\system.text.encoding.codepages.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.text.encodings.web\\6.0.0\\system.text.encodings.web.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.text.json\\6.0.0\\system.text.json.6.0.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.text.regularexpressions\\4.3.0\\system.text.regularexpressions.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.threading.accesscontrol\\6.0.0\\system.threading.accesscontrol.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.2\\system.threading.tasks.extensions.4.5.2.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.threading.timer\\4.0.1\\system.threading.timer.4.0.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.valuetuple\\4.5.0\\system.valuetuple.4.5.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.xml.xdocument\\4.3.0\\system.xml.xdocument.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.xml.xmldocument\\4.3.0\\system.xml.xmldocument.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.xml.xmlserializer\\4.3.0\\system.xml.xmlserializer.4.3.0.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.xml.xpath\\4.0.1\\system.xml.xpath.4.0.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\system.xml.xpath.xdocument\\4.0.1\\system.xml.xpath.xdocument.4.0.1.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\tea\\1.1.2\\tea.1.1.2.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\uaparser\\3.1.47\\uaparser.3.1.47.nupkg.sha512", "d:\\Users\\lijf\\.nuget\\packages\\zookeepernetex\\3.4.12.4\\zookeepernetex.3.4.12.4.nupkg.sha512"], "logs": []}