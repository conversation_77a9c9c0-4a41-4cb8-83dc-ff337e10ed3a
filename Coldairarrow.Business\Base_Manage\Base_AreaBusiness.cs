﻿using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

namespace Coldairarrow.Business.Base_Manage
{
    public class Base_AreaBusiness : BaseBusiness<Base_Area>, IBase_AreaBusiness, ITransientDependency
    {
        public Base_AreaBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口
        public async Task<List<Base_AreaTreeDTO>> GetTreeDataListAsync(AreaTreeInputDTO input)
        {
            var where = LinqHelper.True<Base_Area>();
            if (!input.parentId.IsNullOrEmpty())
                where = where.And(x => x.F_ParentId == input.parentId);

            var list = await GetIQueryable().Where(where).ToListAsync();
            var treeList = list
                .Select(x => new Base_AreaTreeDTO
                {
                    Id = x.F_Id,
                    ParentId = x.F_ParentId,
                    Text = x.F_AreaName,
                    Value = x.F_Id,
                    F_AreaCode = x.F_AreaCode,
                    F_AreaName = x.F_AreaName,
                    F_Description = x.F_Description,
                    F_Layer = x.F_Layer.HasValue.ToString(),
                    F_SimpleSpelling = x.F_SimpleSpelling,
                }).ToList();

            return TreeHelper.BuildTree(treeList);
        }
        public async Task<PageResult<Base_Area>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Base_Area>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Base_Area, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Base_Area> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(Base_Area data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Base_Area data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Base_Area>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Base_Area, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<Base_Area, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        DataTable IBase_AreaBusiness.GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            throw new System.NotImplementedException();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}