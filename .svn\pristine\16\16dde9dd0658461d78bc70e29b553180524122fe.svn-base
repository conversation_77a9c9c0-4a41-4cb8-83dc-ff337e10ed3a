<template>
  <a-modal :title="title" width="60%" :visible="visible" :confirmLoading="loading" @ok="handleSubmit" @cancel="
      () => {
        this.visible = false
      }
    ">
    <a-spin :spinning="loading">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="10">
            <a-col :md="6" :sm="12">
              <a-form-item label="查询类别">
                <a-select allowClear v-model="queryParam.condition">
                  <a-select-option key="F_Name"> 岗位名称 </a-select-option>
                  <a-select-option key="F_EnCode"> 岗位编号 </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item>
                <a-input v-model="queryParam.keyword" placeholder="关键字" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-button type="primary" icon="search" @click="
                  () => {
                    this.pagination.current = 1
                    this.getDataList()
                  }
                ">查询
              </a-button>
              <a-button style="margin-left: 8px" icon="sync" @click="() => (queryParam = {})">重置</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <a-table ref="table" :columns="columns" :rowKey="row => row.F_Id" :dataSource="data" :pagination="pagination"
        :loading="loading" @change="handleTableChange" :scroll="{ x: 1300, y: 500 }"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true">
      </a-table>
    </a-spin>
  </a-modal>
</template>

<script>
const columns = [
  { title: '岗位编号', dataIndex: 'F_EnCode', width: '120' },
  { title: '岗位名称', dataIndex: 'F_Name', width: '120' },
  { title: '部门名称', dataIndex: 'DepartmentName', width: '120' },
  { title: '上级部门', dataIndex: 'DepartmentName2', width: '120' },
  { title: '公司名称', dataIndex: 'CompanyName', width: '120' },
  { title: '上级岗位', dataIndex: 'ParentName', width: '120' }
]
export default {
  props: {
    //回调方法，返回选中的员工
    callBack: Function,
    //是否多选
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 10,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      sorter: { field: 'F_CreateDate', order: 'desc' },
      columns,
      queryParam: {},
      visible: false,
      loading: false,
      selectedRowKeys: [],
      selectedPosts: [], //选择用户
      entity: {},
      rules: {},
      title: ''
    }
  },
  methods: {
    init () {
      this.visible = true
      this.selectUser = []
      this.data = []
      this.queryParam = {}
    },
    openForm (title) {
      this.title = title
      this.init()
      //查询员工信息
      this.getDataList()
    },
    handleTableChange (pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    //查询员工信息
    getDataList () {
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/Base_Manage/Base_Post/GetDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'F_Id',
          SortType: this.sorter.order,
          Search: this.queryParam
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      console.log(selectedRows)
      this.selectedRowKeys = selectedRowKeys
      this.selectedPosts = selectedRows
    },
    handleSubmit () {
      if (this.selectedPosts.length == 0) {
        this.$message.warning('必须选中一行')
        return
      }
      if (!this.multiple && this.selectedPosts.length > 1) {
        this.$message.warning('只能选择一条数据')
        return
      }
      if (this.callBack) {
        this.visible = false
        this.callBack(this.selectedPosts)
      }
    }
  }
}
</script>
