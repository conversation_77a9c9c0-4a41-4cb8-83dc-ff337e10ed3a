<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Routing</name>
    </assembly>
    <members>
        <member name="M:Microsoft.Extensions.Internal.PropertyHelper.#ctor(System.Reflection.PropertyInfo)">
            <summary>
            Initializes a fast <see cref="T:Microsoft.Extensions.Internal.PropertyHelper"/>.
            This constructor does not cache the helper. For caching, use <see cref="M:Microsoft.Extensions.Internal.PropertyHelper.GetProperties(System.Type)"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Internal.PropertyHelper.Property">
            <summary>
            Gets the backing <see cref="T:System.Reflection.PropertyInfo"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Internal.PropertyHelper.Name">
            <summary>
            Gets (or sets in derived types) the property name.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Internal.PropertyHelper.ValueGetter">
            <summary>
            Gets the property value getter.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Internal.PropertyHelper.ValueSetter">
            <summary>
            Gets the property value setter.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Internal.PropertyHelper.GetValue(System.Object)">
            <summary>
            Returns the property value for the specified <paramref name="instance"/>.
            </summary>
            <param name="instance">The object whose property value will be returned.</param>
            <returns>The property value.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Internal.PropertyHelper.SetValue(System.Object,System.Object)">
            <summary>
            Sets the property value for the specified <paramref name="instance" />.
            </summary>
            <param name="instance">The object whose property value will be set.</param>
            <param name="value">The property value.</param>
        </member>
        <member name="M:Microsoft.Extensions.Internal.PropertyHelper.GetProperties(System.Reflection.TypeInfo)">
            <summary>
            Creates and caches fast property helpers that expose getters for every public get property on the
            underlying type.
            </summary>
            <param name="typeInfo">The type info to extract property accessors for.</param>
            <returns>A cached array of all public properties of the specified type.
            </returns>
        </member>
        <member name="M:Microsoft.Extensions.Internal.PropertyHelper.GetProperties(System.Type)">
            <summary>
            Creates and caches fast property helpers that expose getters for every public get property on the
            specified type.
            </summary>
            <param name="type">The type to extract property accessors for.</param>
            <returns>A cached array of all public properties of the specified type.
            </returns>
        </member>
        <member name="M:Microsoft.Extensions.Internal.PropertyHelper.GetVisibleProperties(System.Reflection.TypeInfo)">
            <summary>
            <para>
            Creates and caches fast property helpers that expose getters for every non-hidden get property
            on the specified type.
            </para>
            <para>
            <see cref="M:GetVisibleProperties"/> excludes properties defined on base types that have been
            hidden by definitions using the <c>new</c> keyword.
            </para>
            </summary>
            <param name="typeInfo">The type info to extract property accessors for.</param>
            <returns>
            A cached array of all public properties of the specified type.
            </returns>
        </member>
        <member name="M:Microsoft.Extensions.Internal.PropertyHelper.GetVisibleProperties(System.Type)">
            <summary>
            <para>
            Creates and caches fast property helpers that expose getters for every non-hidden get property
            on the specified type.
            </para>
            <para>
            <see cref="M:GetVisibleProperties"/> excludes properties defined on base types that have been
            hidden by definitions using the <c>new</c> keyword.
            </para>
            </summary>
            <param name="type">The type to extract property accessors for.</param>
            <returns>
            A cached array of all public properties of the specified type.
            </returns>
        </member>
        <member name="M:Microsoft.Extensions.Internal.PropertyHelper.MakeFastPropertyGetter(System.Reflection.PropertyInfo)">
            <summary>
            Creates a single fast property getter. The result is not cached.
            </summary>
            <param name="propertyInfo">propertyInfo to extract the getter for.</param>
            <returns>a fast getter.</returns>
            <remarks>
            This method is more memory efficient than a dynamically compiled lambda, and about the
            same speed.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.Internal.PropertyHelper.MakeNullSafeFastPropertyGetter(System.Reflection.PropertyInfo)">
            <summary>
            Creates a single fast property getter which is safe for a null input object. The result is not cached.
            </summary>
            <param name="propertyInfo">propertyInfo to extract the getter for.</param>
            <returns>a fast getter.</returns>
            <remarks>
            This method is more memory efficient than a dynamically compiled lambda, and about the
            same speed.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.Internal.PropertyHelper.MakeFastPropertySetter(System.Reflection.PropertyInfo)">
            <summary>
            Creates a single fast property setter for reference types. The result is not cached.
            </summary>
            <param name="propertyInfo">propertyInfo to extract the setter for.</param>
            <returns>a fast getter.</returns>
            <remarks>
            This method is more memory efficient than a dynamically compiled lambda, and about the
            same speed. This only works for reference types.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.Internal.PropertyHelper.ObjectToDictionary(System.Object)">
             <summary>
             Given an object, adds each instance property with a public get method as a key and its
             associated value to a dictionary.
            
             If the object is already an <see cref="T:System.Collections.Generic.IDictionary`2"/> instance, then a copy
             is returned.
             </summary>
             <remarks>
             The implementation of PropertyHelper will cache the property accessors per-type. This is
             faster when the same type is used multiple times with ObjectToDictionary.
             </remarks>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.RoutingServiceCollectionExtensions">
            <summary>
            Contains extension methods to <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.RoutingServiceCollectionExtensions.AddRouting(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds services required for routing requests.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the services to.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.RoutingServiceCollectionExtensions.AddRouting(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Microsoft.AspNetCore.Routing.RouteOptions})">
            <summary>
            Adds services required for routing requests.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the services to.</param>
            <param name="configureOptions">The routing options to configure the middleware with.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.ArrayBuilder`1">
            <summary>
            Helper type for avoiding allocations while building arrays.
            </summary>
            <typeparam name="T">The element type.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.ArrayBuilder`1.#ctor(System.Int32)">
            <summary>
            Initializes the <see cref="T:Microsoft.AspNetCore.Routing.ArrayBuilder`1"/> with a specified capacity.
            </summary>
            <param name="capacity">The capacity of the array to allocate.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.ArrayBuilder`1.Capacity">
            <summary>
            Gets the number of items this instance can store without re-allocating,
            or 0 if the backing array is <c>null</c>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.ArrayBuilder`1.Buffer">
            <summary>Gets the current underlying array.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.ArrayBuilder`1.Count">
            <summary>
            Gets the number of items in the array currently in use.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.ArrayBuilder`1.Item(System.Int32)">
            <summary>
            Gets or sets the item at a certain index in the array.
            </summary>
            <param name="index">The index into the array.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.ArrayBuilder`1.Add(`0)">
            <summary>
            Adds an item to the backing array, resizing it if necessary.
            </summary>
            <param name="item">The item to add.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.ArrayBuilder`1.First">
            <summary>
            Gets the first item in this builder.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.ArrayBuilder`1.Last">
            <summary>
            Gets the last item in this builder.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.ArrayBuilder`1.ToArray">
            <summary>
            Creates an array from the contents of this builder.
            </summary>
            <remarks>
            Do not call this method twice on the same builder.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.ArrayBuilder`1.UncheckedAdd(`0)">
            <summary>
            Adds an item to the backing array, without checking if there is room.
            </summary>
            <param name="item">The item to add.</param>
            <remarks>
            Use this method if you know there is enough space in the <see cref="T:Microsoft.AspNetCore.Routing.ArrayBuilder`1"/>
            for another item, and you are writing performance-sensitive code.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.CompositeEndpointDataSource">
            <summary>
            Represents an <see cref="T:Microsoft.AspNetCore.Routing.EndpointDataSource"/> whose values come from a collection of <see cref="T:Microsoft.AspNetCore.Routing.EndpointDataSource"/> instances.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.GetChangeToken">
            <summary>
            Gets a <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> used to signal invalidation of cached <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/>
            instances.
            </summary>
            <returns>The <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/>.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.Endpoints">
            <summary>
            Returns a read-only collection of <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> instances.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.AlphaRouteConstraint">
            <summary>
            Constrains a route parameter to contain only lowercase or uppercase letters A through Z in the English alphabet.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.AlphaRouteConstraint.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.Constraints.AlphaRouteConstraint" /> class.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.BoolRouteConstraint">
            <summary>
            Constrains a route parameter to represent only Boolean values.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.BoolRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.CompositeRouteConstraint">
            <summary>
            Constrains a route by several child constraints.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.CompositeRouteConstraint.#ctor(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Routing.IRouteConstraint})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.Constraints.CompositeRouteConstraint" /> class.
            </summary>
            <param name="constraints">The child constraints that must match for this constraint to match.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Constraints.CompositeRouteConstraint.Constraints">
            <summary>
            Gets the child constraints that must match for this constraint to match.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.CompositeRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.DateTimeRouteConstraint">
            <summary>
            Constrains a route parameter to represent only <see cref="T:System.DateTime"/> values.
            </summary>
            <remarks>
            This constraint tries to parse strings by using all of the formats returned by the
            CultureInfo.InvariantCulture.DateTimeFormat.GetAllDateTimePatterns() method.
            For a sample on how to list all formats which are considered, please visit
            http://msdn.microsoft.com/en-us/library/aszyst2c(v=vs.110).aspx
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.DateTimeRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.DecimalRouteConstraint">
            <summary>
            Constrains a route parameter to represent only decimal values.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.DecimalRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.DoubleRouteConstraint">
            <summary>
            Constrains a route parameter to represent only 64-bit floating-point values.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.DoubleRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.FileNameRouteConstraint">
            <summary>
            Constrains a route parameter to represent only file name values. Does not validate that
            the route value contains valid file system characters, or that the value represents
            an actual file on disk.
            </summary>
            <remarks>
            <para>
            This constraint can be used to disambiguate requests for static files versus dynamic
            content served from the application.
            </para>
            <para>
            This constraint determines whether a route value represents a file name by examining
            the last URL Path segment of the value (delimited by <c>/</c>). The last segment
            must contain the dot (<c>.</c>) character followed by one or more non-(<c>.</c>) characters.
            </para>
            <para>
            If the route value does not contain a <c>/</c> then the entire value will be interpreted
            as the last segment.
            </para>
            <para>
            The <see cref="T:Microsoft.AspNetCore.Routing.Constraints.FileNameRouteConstraint"/> does not attempt to validate that the value contains
            a legal file name for the current operating system.
            </para>
            <para>
            The <see cref="T:Microsoft.AspNetCore.Routing.Constraints.FileNameRouteConstraint"/> does not attempt to validate that the value represents
            an actual file on disk.
            </para>
            <para>
            <list type="bullet">  
                <listheader>  
                    <term>Examples of route values that will be matched as file names</term>  
                    <description>description</description>  
                </listheader>  
                <item>  
                    <term><c>/a/b/c.txt</c></term>  
                    <description>Final segment contains a <c>.</c> followed by other characters.</description>  
                </item>
                <item>  
                    <term><c>/hello.world.txt</c></term>  
                    <description>Final segment contains a <c>.</c> followed by other characters.</description>  
                </item>
                <item>  
                    <term><c>hello.world.txt</c></term>  
                    <description>Final segment contains a <c>.</c> followed by other characters.</description>  
                </item>
                <item>  
                    <term><c>.gitignore</c></term>  
                    <description>Final segment contains a <c>.</c> followed by other characters.</description>  
                </item> 
            </list>
            <list type="bullet">  
                <listheader>  
                    <term>Examples of route values that will be rejected as non-file-names</term>  
                    <description>description</description>  
                </listheader>  
                <item>  
                    <term><c>/a/b/c</c></term>  
                    <description>Final segment does not contain a <c>.</c>.</description>  
                </item>
                <item>  
                    <term><c>/a/b.d/c</c></term>  
                    <description>Final segment does not contain a <c>.</c>.</description>  
                </item>
                <item>  
                    <term><c>/a/b.d/c/</c></term>  
                    <description>Final segment is empty.</description>  
                </item>
                <item>  
                    <term><c></c></term>  
                    <description>Value is empty</description>  
                </item>
            </list>  
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.FileNameRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.FloatRouteConstraint">
            <summary>
            Constrains a route parameter to represent only 32-bit floating-point values.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.FloatRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.GuidRouteConstraint">
            <summary>
            Constrains a route parameter to represent only <see cref="T:System.Guid"/> values.
            Matches values specified in any of the five formats "N", "D", "B", "P", or "X",
            supported by Guid.ToString(string) and Guid.ToString(String, IFormatProvider) methods.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.GuidRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.HttpMethodRouteConstraint">
            <summary>
            Constrains the HTTP method of request or a route.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.HttpMethodRouteConstraint.#ctor(System.String[])">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Routing.Constraints.HttpMethodRouteConstraint"/> that accepts the HTTP methods specified
            by <paramref name="allowedMethods"/>.
            </summary>
            <param name="allowedMethods">The allowed HTTP methods.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Constraints.HttpMethodRouteConstraint.AllowedMethods">
            <summary>
            Gets the HTTP methods allowed by the constraint.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.HttpMethodRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.IntRouteConstraint">
            <summary>
            Constrains a route parameter to represent only 32-bit integer values.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.IntRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.LengthRouteConstraint">
            <summary>
            Constrains a route parameter to be a string of a given length or within a given range of lengths.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.LengthRouteConstraint.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.Constraints.LengthRouteConstraint" /> class that constrains
            a route parameter to be a string of a given length.
            </summary>
            <param name="length">The length of the route parameter.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.LengthRouteConstraint.#ctor(System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.Constraints.LengthRouteConstraint" /> class that constrains
            a route parameter to be a string of a given length.
            </summary>
            <param name="minLength">The minimum length allowed for the route parameter.</param>
            <param name="maxLength">The maximum length allowed for the route parameter.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Constraints.LengthRouteConstraint.MinLength">
            <summary>
            Gets the minimum length allowed for the route parameter.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Constraints.LengthRouteConstraint.MaxLength">
            <summary>
            Gets the maximum length allowed for the route parameter.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.LengthRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.LongRouteConstraint">
            <summary>
            Constrains a route parameter to represent only 64-bit integer values.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.LongRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.MaxLengthRouteConstraint">
            <summary>
            Constrains a route parameter to be a string with a maximum length.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.MaxLengthRouteConstraint.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.Constraints.MaxLengthRouteConstraint" /> class.
            </summary>
            <param name="maxLength">The maximum length allowed for the route parameter.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Constraints.MaxLengthRouteConstraint.MaxLength">
            <summary>
            Gets the maximum length allowed for the route parameter.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.MaxLengthRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.MaxRouteConstraint">
            <summary>
            Constrains a route parameter to be an integer with a maximum value.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.MaxRouteConstraint.#ctor(System.Int64)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.Constraints.MaxRouteConstraint" /> class.
            </summary>
            <param name="max">The maximum value allowed for the route parameter.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Constraints.MaxRouteConstraint.Max">
            <summary>
            Gets the maximum allowed value of the route parameter.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.MaxRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.MinLengthRouteConstraint">
            <summary>
            Constrains a route parameter to be a string with a minimum length.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.MinLengthRouteConstraint.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.Constraints.MinLengthRouteConstraint" /> class.
            </summary>
            <param name="minLength">The minimum length allowed for the route parameter.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Constraints.MinLengthRouteConstraint.MinLength">
            <summary>
            Gets the minimum length allowed for the route parameter.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.MinLengthRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.MinRouteConstraint">
            <summary>
            Constrains a route parameter to be a long with a minimum value.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.MinRouteConstraint.#ctor(System.Int64)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.Constraints.MinRouteConstraint" /> class.
            </summary>
            <param name="min">The minimum value allowed for the route parameter.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Constraints.MinRouteConstraint.Min">
            <summary>
            Gets the minimum allowed value of the route parameter.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.MinRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.NonFileNameRouteConstraint">
            <summary>
            Constrains a route parameter to represent only non-file-name values. Does not validate that
            the route value contains valid file system characters, or that the value represents
            an actual file on disk.
            </summary>
            <remarks>
            <para>
            This constraint can be used to disambiguate requests for dynamic content versus
            static files served from the application.
            </para>
            <para>
            This constraint determines whether a route value represents a file name by examining
            the last URL Path segment of the value (delimited by <c>/</c>). The last segment
            must contain the dot (<c>.</c>) character followed by one or more non-(<c>.</c>) characters.
            </para>
            <para>
            If the route value does not contain a <c>/</c> then the entire value will be interpreted
            as a the last segment.
            </para>
            <para>
            The <see cref="T:Microsoft.AspNetCore.Routing.Constraints.NonFileNameRouteConstraint"/> does not attempt to validate that the value contains
            a legal file name for the current operating system.
            </para>
            <para>
            <list type="bullet">  
                <listheader>  
                    <term>Examples of route values that will be matched as non-file-names</term>  
                    <description>description</description>  
                </listheader>  
                <item>  
                    <term><c>/a/b/c</c></term>  
                    <description>Final segment does not contain a <c>.</c>.</description>  
                </item>
                <item>  
                    <term><c>/a/b.d/c</c></term>  
                    <description>Final segment does not contain a <c>.</c>.</description>  
                </item>
                <item>  
                    <term><c>/a/b.d/c/</c></term>  
                    <description>Final segment is empty.</description>  
                </item>
                <item>  
                    <term><c></c></term>  
                    <description>Value is empty</description>  
                </item>
            </list>
            <list type="bullet">  
                <listheader>  
                    <term>Examples of route values that will be rejected as file names</term>  
                    <description>description</description>  
                </listheader>  
                <item>  
                    <term><c>/a/b/c.txt</c></term>  
                    <description>Final segment contains a <c>.</c> followed by other characters.</description>  
                </item>
                <item>  
                    <term><c>/hello.world.txt</c></term>  
                    <description>Final segment contains a <c>.</c> followed by other characters.</description>  
                </item>
                <item>  
                    <term><c>hello.world.txt</c></term>  
                    <description>Final segment contains a <c>.</c> followed by other characters.</description>  
                </item>
                <item>  
                    <term><c>.gitignore</c></term>  
                    <description>Final segment contains a <c>.</c> followed by other characters.</description>  
                </item> 
            </list>
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.NonFileNameRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.OptionalRouteConstraint">
            <summary>
            Defines a constraint on an optional parameter. If the parameter is present, then it is constrained by InnerConstraint. 
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.RangeRouteConstraint">
            <summary>
            Constraints a route parameter to be an integer within a given range of values.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.RangeRouteConstraint.#ctor(System.Int64,System.Int64)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.Constraints.RangeRouteConstraint" /> class.
            </summary>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <remarks>The minimum value should be less than or equal to the maximum value.</remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Constraints.RangeRouteConstraint.Min">
            <summary>
            Gets the minimum allowed value of the route parameter.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Constraints.RangeRouteConstraint.Max">
            <summary>
            Gets the maximum allowed value of the route parameter.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.RangeRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.RegexInlineRouteConstraint">
            <summary>
            Represents a regex constraint which can be used as an inlineConstraint.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.RegexInlineRouteConstraint.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.Constraints.RegexInlineRouteConstraint" /> class.
            </summary>
            <param name="regexPattern">The regular expression pattern to match.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.RequiredRouteConstraint">
            <summary>
            Constraints a route parameter that must have a value.
            </summary>
            <remarks>
            This constraint is primarily used to enforce that a non-parameter value is present during
            URL generation.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.RequiredRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Constraints.StringRouteConstraint">
            <summary>
            Constrains a route parameter to contain only a specified string.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.StringRouteConstraint.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.Constraints.StringRouteConstraint"/> class.
            </summary>
            <param name="value">The constraint value to match.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Constraints.StringRouteConstraint.Match(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.IRouter,System.String,Microsoft.AspNetCore.Routing.RouteValueDictionary,Microsoft.AspNetCore.Routing.RouteDirection)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.DataTokensMetadata">
            <summary>
            Metadata that defines data tokens for an <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/>. This metadata
            type provides data tokens value for <see cref="P:Microsoft.AspNetCore.Routing.RouteData.DataTokens"/> associated
            with an endpoint.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.DataTokensMetadata.DataTokens">
            <summary>
            Get the data tokens.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.DefaultEndpointDataSource">
            <summary>
            Provides a collection of <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> instances.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.DefaultEndpointDataSource.#ctor(Microsoft.AspNetCore.Http.Endpoint[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.DefaultEndpointDataSource" /> class.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> instances that the data source will return.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.DefaultEndpointDataSource.#ctor(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Http.Endpoint})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.DefaultEndpointDataSource" /> class.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> instances that the data source will return.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.DefaultEndpointDataSource.GetChangeToken">
            <summary>
            Gets a <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> used to signal invalidation of cached <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/>
            instances.
            </summary>
            <returns>The <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/>.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.DefaultEndpointDataSource.Endpoints">
            <summary>
            Returns a read-only collection of <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> instances.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.DefaultInlineConstraintResolver">
            <summary>
            The default implementation of <see cref="T:Microsoft.AspNetCore.Routing.IInlineConstraintResolver"/>. Resolves constraints by parsing
            a constraint key and constraint arguments, using a map to resolve the constraint type, and calling an
            appropriate constructor for the constraint type.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.DefaultInlineConstraintResolver.#ctor(Microsoft.Extensions.Options.IOptions{Microsoft.AspNetCore.Routing.RouteOptions},System.IServiceProvider)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.DefaultInlineConstraintResolver"/> class.
            </summary>
            <param name="routeOptions">Accessor for <see cref="T:Microsoft.AspNetCore.Routing.RouteOptions"/> containing the constraints of interest.</param>
            <param name="serviceProvider">The <see cref="T:System.IServiceProvider"/> to get service arguments from.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.DefaultInlineConstraintResolver.ResolveConstraint(System.String)">
            <inheritdoc />
            <example>
            A typical constraint looks like the following
            "exampleConstraint(arg1, arg2, 12)".
            Here if the type registered for exampleConstraint has a single constructor with one argument,
            The entire string "arg1, arg2, 12" will be treated as a single argument.
            In all other cases arguments are split at comma.
            </example>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.EndpointDataSource">
            <summary>
            Provides a collection of <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> instances.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.EndpointDataSource.GetChangeToken">
            <summary>
            Gets a <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> used to signal invalidation of cached <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/>
            instances.
            </summary>
            <returns>The <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/>.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.EndpointDataSource.Endpoints">
            <summary>
            Returns a read-only collection of <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> instances.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.EndpointNameMetadata">
            <summary>
            Specifies an endpoint name in <see cref="P:Microsoft.AspNetCore.Http.Endpoint.Metadata"/>.
            </summary>
            <remarks>
            Endpoint names must be unique within an application, and can be used to unambiguously
            identify a desired endpoint for URI generation using <see cref="T:Microsoft.AspNetCore.Routing.LinkGenerator"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.EndpointNameMetadata.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Routing.EndpointNameMetadata"/> with the provided endpoint name.
            </summary>
            <param name="endpointName">The endpoint name.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.EndpointNameMetadata.EndpointName">
            <summary>
            Gets the endpoint name.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.HostAttribute">
            <summary>
            Attribute for providing host metdata that is used during routing.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.HostAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.HostAttribute" /> class.
            </summary>
            <param name="host">
            The host used during routing.
            Host should be Unicode rather than punycode, and may have a port.
            </param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.HostAttribute.#ctor(System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.HostAttribute" /> class.
            </summary>
            <param name="hosts">
            The hosts used during routing.
            Hosts should be Unicode rather than punycode, and may have a port.
            An empty collection means any host will be accepted.
            </param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.HostAttribute.Hosts">
            <summary>
            Returns a read-only collection of hosts used during routing.
            Hosts will be Unicode rather than punycode, and may have a port.
            An empty collection means any host will be accepted.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.HttpMethodMetadata">
            <summary>
            Represents HTTP method metadata used during routing.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.HttpMethodMetadata.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.HttpMethodMetadata" /> class.
            </summary>
            <param name="httpMethods">
            The HTTP methods used during routing.
            An empty collection means any HTTP method will be accepted.
            </param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.HttpMethodMetadata.#ctor(System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.HttpMethodMetadata" /> class.
            </summary>
            <param name="httpMethods">
            The HTTP methods used during routing.
            An empty collection means any HTTP method will be accepted.
            </param>
            <param name="acceptCorsPreflight">A value indicating whether routing accepts CORS preflight requests.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.HttpMethodMetadata.AcceptCorsPreflight">
            <summary>
            Returns a value indicating whether the associated endpoint should accept CORS preflight requests.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.HttpMethodMetadata.HttpMethods">
            <summary>
            Returns a read-only collection of HTTP methods used during routing.
            An empty collection means any HTTP method will be accepted.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.IDataTokensMetadata">
            <summary>
            Metadata that defines data tokens for an <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/>. This metadata
            type provides data tokens value for <see cref="P:Microsoft.AspNetCore.Routing.RouteData.DataTokens"/> associated
            with an endpoint.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.IDataTokensMetadata.DataTokens">
            <summary>
            Get the data tokens.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.IDynamicEndpointMetadata">
            <summary>
            A metadata interface that can be used to specify that the associated <see cref="T:Microsoft.AspNetCore.Http.Endpoint" />
            will be dynamically replaced during matching. 
            </summary>
            <remarks>
            <para>
            <see cref="T:Microsoft.AspNetCore.Routing.IDynamicEndpointMetadata"/> and related derived interfaces signal to
            <see cref="T:Microsoft.AspNetCore.Routing.MatcherPolicy"/> implementations that an <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> has dynamic behavior
            and thus cannot have its characteristics cached.
            </para>
            <para>
            Using dynamic endpoints can be useful because the default matcher implementation does not
            supply extensibility for how URLs are processed. Routing implementations that have dynamic
            behavior can apply their dynamic logic after URL processing, by replacing a endpoints as
            part of a <see cref="T:Microsoft.AspNetCore.Routing.Matching.CandidateSet"/>.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.IDynamicEndpointMetadata.IsDynamic">
            <summary>
            Returns a value that indicates whether the associated endpoint has dynamic matching
            behavior.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.IEndpointAddressScheme`1">
            <summary>
            Defines a contract to find endpoints based on the provided address.
            </summary>
            <typeparam name="TAddress">The address type to look up endpoints.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.IEndpointAddressScheme`1.FindEndpoints(`0)">
            <summary>
            Finds endpoints based on the provided <paramref name="address"/>.
            </summary>
            <param name="address">The information used to look up endpoints.</param>
            <returns>A collection of <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.IEndpointNameMetadata">
            <summary>
            Defines a contract use to specify an endpoint name in <see cref="P:Microsoft.AspNetCore.Http.Endpoint.Metadata"/>.
            </summary>
            <remarks>
            Endpoint names must be unique within an application, and can be used to unambiguously
            identify a desired endpoint for URI generation using <see cref="T:Microsoft.AspNetCore.Routing.LinkGenerator"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.IEndpointNameMetadata.EndpointName">
            <summary>
            Gets the endpoint name.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder">
            <summary>
            Defines a contract for a route builder in an application. A route builder specifies the routes for
            an application.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder.CreateApplicationBuilder">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.
            </summary>
            <returns>The new <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder.ServiceProvider">
            <summary>
            Gets the sets the <see cref="T:System.IServiceProvider"/> used to resolve services for routes.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder.DataSources">
            <summary>
            Gets the endpoint data sources configured in the builder.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.IHostMetadata">
            <summary>
            Represents host metadata used during routing.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.IHostMetadata.Hosts">
            <summary>
            Returns a read-only collection of hosts used during routing.
            Hosts will be Unicode rather than punycode, and may have a port.
            An empty collection means any host will be accepted.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.IHttpMethodMetadata">
            <summary>
            Represents HTTP method metadata used during routing.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.IHttpMethodMetadata.AcceptCorsPreflight">
            <summary>
            Returns a value indicating whether the associated endpoint should accept CORS preflight requests.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.IHttpMethodMetadata.HttpMethods">
            <summary>
            Returns a read-only collection of HTTP methods used during routing.
            An empty collection means any HTTP method will be accepted.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.IInlineConstraintResolver">
            <summary>
            Defines an abstraction for resolving inline constraints as instances of <see cref="T:Microsoft.AspNetCore.Routing.IRouteConstraint"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.IInlineConstraintResolver.ResolveConstraint(System.String)">
            <summary>
            Resolves the inline constraint.
            </summary>
            <param name="inlineConstraint">The inline constraint to resolve.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.IRouteConstraint"/> the inline constraint was resolved to.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Internal.DfaGraphWriter">
            <summary>
            <para>
            A singleton service that can be used to write the route table as a state machine
            in GraphViz DOT language https://www.graphviz.org/doc/info/lang.html
            </para>
            <para>
            You can use http://www.webgraphviz.com/ to visualize the results.
            </para>
            <para>
            This type has no support contract, and may be removed or changed at any time in
            a future release.
            </para>
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.IRouteBuilder">
            <summary>
            Defines a contract for a route builder in an application. A route builder specifies the routes for
            an application.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.IRouteBuilder.ApplicationBuilder">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.IRouteBuilder.DefaultHandler">
            <summary>
            Gets or sets the default <see cref="T:Microsoft.AspNetCore.Routing.IRouter"/> that is used as a handler if an <see cref="T:Microsoft.AspNetCore.Routing.IRouter"/>
            is added to the list of routes but does not specify its own.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.IRouteBuilder.ServiceProvider">
            <summary>
            Gets the sets the <see cref="T:System.IServiceProvider"/> used to resolve services for routes.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.IRouteBuilder.Routes">
            <summary>
            Gets the routes configured in the builder.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.IRouteBuilder.Build">
            <summary>
            Builds an <see cref="T:Microsoft.AspNetCore.Routing.IRouter"/> that routes the routes specified in the <see cref="P:Microsoft.AspNetCore.Routing.IRouteBuilder.Routes"/> property.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.IRouteNameMetadata">
            <summary>
            Represents metadata used during link generation to find
            the associated endpoint using route name.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.IRouteNameMetadata.RouteName">
            <summary>
            Gets the route name. Can be null.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.ISuppressLinkGenerationMetadata">
            <summary>
            Represents metadata used during link generation. If <see cref="P:Microsoft.AspNetCore.Routing.ISuppressLinkGenerationMetadata.SuppressLinkGeneration"/> is <c>true</c> 
            the associated endpoint will not be used for link generation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.ISuppressLinkGenerationMetadata.SuppressLinkGeneration">
            <summary>
            Gets a value indicating whether the assocated endpoint should be used for link generation.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.ISuppressMatchingMetadata">
            <summary>
            Metadata used to prevent URL matching. If <see cref="P:Microsoft.AspNetCore.Routing.ISuppressMatchingMetadata.SuppressMatching"/> is <c>true</c> the
            associated endpoint will not be considered for URL matching.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.ISuppressMatchingMetadata.SuppressMatching">
            <summary>
            Gets a value indicating whether the assocated endpoint should be used for URL matching.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.LinkGeneratorEndpointNameAddressExtensions">
            <summary>
            Extension methods for using <see cref="T:Microsoft.AspNetCore.Routing.LinkGenerator"/> with and endpoint name.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.LinkGeneratorEndpointNameAddressExtensions.GetPathByName(Microsoft.AspNetCore.Routing.LinkGenerator,Microsoft.AspNetCore.Http.HttpContext,System.String,System.Object,System.Nullable{Microsoft.AspNetCore.Http.PathString},Microsoft.AspNetCore.Http.FragmentString,Microsoft.AspNetCore.Routing.LinkOptions)">
            <summary>
            Generates a URI with an absolute path based on the provided values.
            </summary>
            <param name="generator">The <see cref="T:Microsoft.AspNetCore.Routing.LinkGenerator"/>.</param>
            <param name="httpContext">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> associated with the current request.</param>
            <param name="endpointName">The endpoint name. Used to resolve endpoints.</param>
            <param name="values">The route values. Used to expand parameters in the route template. Optional.</param>
            <param name="pathBase">
            An optional URI path base. Prepended to the path in the resulting URI. If not provided, the value of <see cref="P:Microsoft.AspNetCore.Http.HttpRequest.PathBase"/> will be used.
            </param>
            <param name="fragment">An optional URI fragment. Appended to the resulting URI.</param>
            <param name="options">
            An optional <see cref="T:Microsoft.AspNetCore.Routing.LinkOptions"/>. Settings on provided object override the settings with matching
            names from <c>RouteOptions</c>.
            </param>
            <returns>A URI with an absolute path, or <c>null</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.LinkGeneratorEndpointNameAddressExtensions.GetPathByName(Microsoft.AspNetCore.Routing.LinkGenerator,System.String,System.Object,Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.FragmentString,Microsoft.AspNetCore.Routing.LinkOptions)">
            <summary>
            Generates a URI with an absolute path based on the provided values.
            </summary>
            <param name="generator">The <see cref="T:Microsoft.AspNetCore.Routing.LinkGenerator"/>.</param>
            <param name="endpointName">The endpoint name. Used to resolve endpoints.</param>
            <param name="values">The route values. Used to expand parameters in the route template. Optional.</param>
            <param name="pathBase">An optional URI path base. Prepended to the path in the resulting URI.</param>
            <param name="fragment">An optional URI fragment. Appended to the resulting URI.</param>
            <param name="options">
            An optional <see cref="T:Microsoft.AspNetCore.Routing.LinkOptions"/>. Settings on provided object override the settings with matching
            names from <c>RouteOptions</c>.
            </param>
            <returns>A URI with an absolute path, or <c>null</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.LinkGeneratorEndpointNameAddressExtensions.GetUriByName(Microsoft.AspNetCore.Routing.LinkGenerator,Microsoft.AspNetCore.Http.HttpContext,System.String,System.Object,System.String,System.Nullable{Microsoft.AspNetCore.Http.HostString},System.Nullable{Microsoft.AspNetCore.Http.PathString},Microsoft.AspNetCore.Http.FragmentString,Microsoft.AspNetCore.Routing.LinkOptions)">
            <summary>
            Generates an absolute URI based on the provided values.
            </summary>
            <param name="generator">The <see cref="T:Microsoft.AspNetCore.Routing.LinkGenerator"/>.</param>
            <param name="httpContext">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> associated with the current request.</param>
            <param name="endpointName">The endpoint name. Used to resolve endpoints.</param>
            <param name="values">The route values. Used to expand parameters in the route template. Optional.</param>
            <param name="scheme">
            The URI scheme, applied to the resulting URI. Optional. If not provided, the value of <see cref="P:Microsoft.AspNetCore.Http.HttpRequest.Scheme"/> will be used.
            </param>
            <param name="host">
            The URI host/authority, applied to the resulting URI. Optional. If not provided, the value <see cref="P:Microsoft.AspNetCore.Http.HttpRequest.Host"/> will be used.
            See the remarks section for details about the security implications of the <paramref name="host"/>.
            </param>
            <param name="pathBase">
            An optional URI path base. Prepended to the path in the resulting URI. If not provided, the value of <see cref="P:Microsoft.AspNetCore.Http.HttpRequest.PathBase"/> will be used.
            </param>
            <param name="fragment">An optional URI fragment. Appended to the resulting URI.</param>
            <param name="options">
            An optional <see cref="T:Microsoft.AspNetCore.Routing.LinkOptions"/>. Settings on provided object override the settings with matching
            names from <c>RouteOptions</c>.
            </param>
            <returns>A URI with an absolute path, or <c>null</c>.</returns>
            <remarks>
            <para>
            The value of <paramref name="host" /> should be a trusted value. Relying on the value of the current request
            can allow untrusted input to influence the resulting URI unless the <c>Host</c> header has been validated.
            See the deployment documentation for instructions on how to properly validate the <c>Host</c> header in
            your deployment environment.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.LinkGeneratorEndpointNameAddressExtensions.GetUriByName(Microsoft.AspNetCore.Routing.LinkGenerator,System.String,System.Object,System.String,Microsoft.AspNetCore.Http.HostString,Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.FragmentString,Microsoft.AspNetCore.Routing.LinkOptions)">
            <summary>
            Generates an absolute URI based on the provided values.
            </summary>
            <param name="generator">The <see cref="T:Microsoft.AspNetCore.Routing.LinkGenerator"/>.</param>
            <param name="endpointName">The endpoint name. Used to resolve endpoints.</param>
            <param name="values">The route values. Used to expand parameters in the route template. Optional.</param>
            <param name="scheme">The URI scheme, applied to the resulting URI.</param>
            <param name="host">
            The URI host/authority, applied to the resulting URI.
            See the remarks section for details about the security implications of the <paramref name="host"/>.
            </param>
            <param name="pathBase">An optional URI path base. Prepended to the path in the resulting URI.</param>
            <param name="fragment">An optional URI fragment. Appended to the resulting URI.</param>
            <param name="options">
            An optional <see cref="T:Microsoft.AspNetCore.Routing.LinkOptions"/>. Settings on provided object override the settings with matching
            names from <c>RouteOptions</c>.
            </param>
            <returns>An absolute URI, or <c>null</c>.</returns>
            <remarks>
            <para>
            The value of <paramref name="host" /> should be a trusted value. Relying on the value of the current request
            can allow untrusted input to influence the resulting URI unless the <c>Host</c> header has been validated.
            See the deployment documentation for instructions on how to properly validate the <c>Host</c> header in
            your deployment environment.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.LinkGeneratorRouteValuesAddressExtensions">
            <summary>
            Extension methods for using <see cref="T:Microsoft.AspNetCore.Routing.LinkGenerator"/> with <see cref="T:Microsoft.AspNetCore.Routing.RouteValuesAddress"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.LinkGeneratorRouteValuesAddressExtensions.GetPathByRouteValues(Microsoft.AspNetCore.Routing.LinkGenerator,Microsoft.AspNetCore.Http.HttpContext,System.String,System.Object,System.Nullable{Microsoft.AspNetCore.Http.PathString},Microsoft.AspNetCore.Http.FragmentString,Microsoft.AspNetCore.Routing.LinkOptions)">
            <summary>
            Generates a URI with an absolute path based on the provided values.
            </summary>
            <param name="generator">The <see cref="T:Microsoft.AspNetCore.Routing.LinkGenerator"/>.</param>
            <param name="httpContext">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> associated with the current request.</param>
            <param name="routeName">The route name. Used to resolve endpoints. Optional.</param>
            <param name="values">The route values. Used to resolve endpoints and expand parameters in the route template. Optional.</param>
            <param name="pathBase">
            An optional URI path base. Prepended to the path in the resulting URI. If not provided, the value of <see cref="P:Microsoft.AspNetCore.Http.HttpRequest.PathBase"/> will be used.
            </param>
            <param name="fragment">An optional URI fragment. Appended to the resulting URI.</param>
            <param name="options">
            An optional <see cref="T:Microsoft.AspNetCore.Routing.LinkOptions"/>. Settings on provided object override the settings with matching
            names from <c>RouteOptions</c>.
            </param>
            <returns>A URI with an absolute path, or <c>null</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.LinkGeneratorRouteValuesAddressExtensions.GetPathByRouteValues(Microsoft.AspNetCore.Routing.LinkGenerator,System.String,System.Object,Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.FragmentString,Microsoft.AspNetCore.Routing.LinkOptions)">
            <summary>
            Generates a URI with an absolute path based on the provided values.
            </summary>
            <param name="generator">The <see cref="T:Microsoft.AspNetCore.Routing.LinkGenerator"/>.</param>
            <param name="routeName">The route name. Used to resolve endpoints. Optional.</param>
            <param name="values">The route values. Used to resolve endpoints and expand parameters in the route template. Optional.</param>
            <param name="pathBase">An optional URI path base. Prepended to the path in the resulting URI.</param>
            <param name="fragment">An optional URI fragment. Appended to the resulting URI.</param>
            <param name="options">
            An optional <see cref="T:Microsoft.AspNetCore.Routing.LinkOptions"/>. Settings on provided object override the settings with matching
            names from <c>RouteOptions</c>.
            </param>
            <returns>A URI with an absolute path, or <c>null</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.LinkGeneratorRouteValuesAddressExtensions.GetUriByRouteValues(Microsoft.AspNetCore.Routing.LinkGenerator,Microsoft.AspNetCore.Http.HttpContext,System.String,System.Object,System.String,System.Nullable{Microsoft.AspNetCore.Http.HostString},System.Nullable{Microsoft.AspNetCore.Http.PathString},Microsoft.AspNetCore.Http.FragmentString,Microsoft.AspNetCore.Routing.LinkOptions)">
            <summary>
            Generates an absolute URI based on the provided values.
            </summary>
            <param name="generator">The <see cref="T:Microsoft.AspNetCore.Routing.LinkGenerator"/>.</param>
            <param name="httpContext">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> associated with the current request.</param>
            <param name="routeName">The route name. Used to resolve endpoints. Optional.</param>
            <param name="values">The route values. Used to resolve endpoints and expand parameters in the route template. Optional.</param>
            <param name="scheme">
            The URI scheme, applied to the resulting URI. Optional. If not provided, the value of <see cref="P:Microsoft.AspNetCore.Http.HttpRequest.Scheme"/> will be used.
            </param>
            <param name="host">
            The URI host/authority, applied to the resulting URI. Optional. If not provided, the value <see cref="P:Microsoft.AspNetCore.Http.HttpRequest.Host"/> will be used.
            See the remarks section for details about the security implications of the <paramref name="host"/>.
            </param>
            <param name="pathBase">
            An optional URI path base. Prepended to the path in the resulting URI. If not provided, the value of <see cref="P:Microsoft.AspNetCore.Http.HttpRequest.PathBase"/> will be used.
            </param>
            <param name="fragment">An optional URI fragment. Appended to the resulting URI.</param>
            <param name="options">
            An optional <see cref="T:Microsoft.AspNetCore.Routing.LinkOptions"/>. Settings on provided object override the settings with matching
            names from <c>RouteOptions</c>.
            </param>
            <returns>A URI with an absolute path, or <c>null</c>.</returns>
            <remarks>
            <para>
            The value of <paramref name="host" /> should be a trusted value. Relying on the value of the current request
            can allow untrusted input to influence the resulting URI unless the <c>Host</c> header has been validated.
            See the deployment documentation for instructions on how to properly validate the <c>Host</c> header in
            your deployment environment.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.LinkGeneratorRouteValuesAddressExtensions.GetUriByRouteValues(Microsoft.AspNetCore.Routing.LinkGenerator,System.String,System.Object,System.String,Microsoft.AspNetCore.Http.HostString,Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.FragmentString,Microsoft.AspNetCore.Routing.LinkOptions)">
            <summary>
            Generates an absolute URI based on the provided values.
            </summary>
            <param name="generator">The <see cref="T:Microsoft.AspNetCore.Routing.LinkGenerator"/>.</param>
            <param name="routeName">The route name. Used to resolve endpoints. Optional.</param>
            <param name="values">The route values. Used to resolve endpoints and expand parameters in the route template. Optional.</param>
            <param name="scheme">The URI scheme, applied to the resulting URI.</param>
            <param name="host">
            The URI host/authority, applied to the resulting URI.
            See the remarks section for details about the security implications of the <paramref name="host"/>.
            </param>
            <param name="pathBase">An optional URI path base. Prepended to the path in the resulting URI.</param>
            <param name="fragment">An optional URI fragment. Appended to the resulting URI.</param>
            <param name="options">
            An optional <see cref="T:Microsoft.AspNetCore.Routing.LinkOptions"/>. Settings on provided object override the settings with matching
            names from <c>RouteOptions</c>.
            </param>
            <returns>An absolute URI, or <c>null</c>.</returns>
            <remarks>
            <para>
            The value of <paramref name="host" /> should be a trusted value. Relying on the value of the current request
            can allow untrusted input to influence the resulting URI unless the <c>Host</c> header has been validated.
            See the deployment documentation for instructions on how to properly validate the <c>Host</c> header in
            your deployment environment.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.LinkParser">
            <summary>
            Defines a contract to parse URIs using information from routing.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.LinkParser.ParsePathByAddress``1(``0,Microsoft.AspNetCore.Http.PathString)">
            <summary>
            Attempts to parse the provided <paramref name="path"/> using the route pattern
            specified by the <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> matching <paramref name="address"/>.
            </summary>
            <typeparam name="TAddress">The address type.</typeparam>
            <param name="address">The address value. Used to resolve endpoints.</param>
            <param name="path">The URI path to parse.</param>
            <returns>
            A <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/> with the parsed values if parsing is successful; 
            otherwise <c>null</c>.
            </returns>
            <remarks>
            <para>
            <see cref="M:Microsoft.AspNetCore.Routing.LinkParser.ParsePathByAddress``1(``0,Microsoft.AspNetCore.Http.PathString)"/> will attempt to first resolve
            <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> instances that match <paramref name="address"/> and then use the route
            pattern associated with each endpoint to parse the URL path. 
            </para>
            <para>
            The parsing operation will fail and return <c>null</c> if either no endpoints are found or none
            of the route patterns match the provided URI path.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.LinkParserEndpointNameAddressExtensions">
            <summary>
            Extension methods for using <see cref="T:Microsoft.AspNetCore.Routing.LinkParser"/> with an endpoint name.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.LinkParserEndpointNameAddressExtensions.ParsePathByEndpointName(Microsoft.AspNetCore.Routing.LinkParser,System.String,Microsoft.AspNetCore.Http.PathString)">
            <summary>
            Attempts to parse the provided <paramref name="path"/> using the route pattern
            specified by the <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> matching <paramref name="endpointName"/>.
            </summary>
            <param name="parser">The <see cref="T:Microsoft.AspNetCore.Routing.LinkParser"/>.</param>
            <param name="endpointName">The endpoint name. Used to resolve endpoints.</param>
            <param name="path">The URI path to parse.</param>
            <returns>
            A <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/> with the parsed values if parsing is successful; 
            otherwise <c>null</c>.
            </returns>
            <remarks>
            <para>
            <see cref="M:Microsoft.AspNetCore.Routing.LinkParserEndpointNameAddressExtensions.ParsePathByEndpointName(Microsoft.AspNetCore.Routing.LinkParser,System.String,Microsoft.AspNetCore.Http.PathString)"/> will attempt to first resolve
            <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> instances that match <paramref name="endpointName"/> and then use the route
            pattern associated with each endpoint to parse the URL path. 
            </para>
            <para>
            The parsing operation will fail and return <c>null</c> if either no endpoints are found or none
            of the route patterns match the provided URI path.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException">
            <summary>
            An exception which indicates multiple matches in endpoint selection.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Matching.CandidateSet">
            <summary>
            Represents a set of <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> candidates that have been matched
            by the routing system. Used by implementations of <see cref="T:Microsoft.AspNetCore.Routing.Matching.EndpointSelector"/>
            and <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointSelectorPolicy"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.CandidateSet.#ctor(Microsoft.AspNetCore.Http.Endpoint[],Microsoft.AspNetCore.Routing.RouteValueDictionary[],System.Int32[])">
            <summary>
            <para>
            Initializes a new instances of the <see cref="T:Microsoft.AspNetCore.Routing.Matching.CandidateSet"/> class with the provided <paramref name="endpoints"/>,
            <paramref name="values"/>, and <paramref name="scores"/>.
            </para>
            <para>
            The constructor is provided to enable unit tests of implementations of <see cref="T:Microsoft.AspNetCore.Routing.Matching.EndpointSelector"/>
            and <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointSelectorPolicy"/>.
            </para>
            </summary>
            <param name="endpoints">The list of endpoints, sorted in descending priority order.</param>
            <param name="values">The list of <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/> instances.</param>
            <param name="scores">The list of endpoint scores. <see cref="P:Microsoft.AspNetCore.Routing.Matching.CandidateState.Score"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.CandidateSet.Count">
            <summary>
            Gets the count of candidates in the set.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.CandidateSet.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Routing.Matching.CandidateState"/> associated with the candidate <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/>
            at <paramref name="index"/>.
            </summary>
            <param name="index">The candidate index.</param>
            <returns>
            A reference to the <see cref="T:Microsoft.AspNetCore.Routing.Matching.CandidateState"/>. The result is returned by reference.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.CandidateSet.IsValidCandidate(System.Int32)">
            <summary>
            Gets a value which indicates where the <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> is considered
            a valid candiate for the current request.
            </summary>
            <param name="index">The candidate index.</param>
            <returns>
            <c>true</c> if the candidate at position <paramref name="index"/> is considered valid
            for the current request, otherwise <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.CandidateSet.SetValidity(System.Int32,System.Boolean)">
            <summary>
            Sets the validitity of the candidate at the provided index.
            </summary>
            <param name="index">The candidate index.</param>
            <param name="value">
            The value to set. If <c>true</c> the candidate is considered valid for the current request.
            </param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.CandidateSet.ReplaceEndpoint(System.Int32,Microsoft.AspNetCore.Http.Endpoint,Microsoft.AspNetCore.Routing.RouteValueDictionary)">
            <summary>
            Replaces the <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> at the provided <paramref name="index"/> with the
            provided <paramref name="endpoint"/>.
            </summary>
            <param name="index">The candidate index.</param>
            <param name="endpoint">
            The <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> to replace the original <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> at
            the <paramref name="index"/>. If <paramref name="endpoint"/> is <c>null</c>. the candidate will be marked
            as invalid.
            </param>
            <param name="values">
            The <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/> to replace the original <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/> at
            the <paramref name="index"/>.
            </param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(System.Int32,System.Collections.Generic.IReadOnlyList{Microsoft.AspNetCore.Http.Endpoint},System.Collections.Generic.IComparer{Microsoft.AspNetCore.Http.Endpoint})">
            <summary>
            Replaces the <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> at the provided <paramref name="index"/> with the
            provided <paramref name="endpoints"/>.
            </summary>
            <param name="index">The candidate index.</param>
            <param name="endpoints">
            The list of endpoints <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> to replace the original <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> at
            the <paramref name="index"/>. If <paramref name="endpoints"/> is empty, the candidate will be marked
            as invalid.
            </param>
            <param name="comparer">
            The endpoint comparer used to order the endpoints. Can be retrieved from the service provider as
            type <see cref="T:Microsoft.AspNetCore.Routing.Matching.EndpointMetadataComparer"/>.
            </param>
            <remarks>
            <para>
            This method supports replacing a dynamic endpoint with a collection of endpoints, and relying on
            <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointSelectorPolicy"/> implementations to disambiguate further.
            </para>
            <para>
            The endpoint being replace should have a unique score value. The score is the combination of route
            patter precedence, order, and policy metadata evaluation. A dynamic endpoint will not function
            correctly if other endpoints exist with the same score.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Matching.CandidateState">
            <summary>
            The state associated with a candidate in a <see cref="T:Microsoft.AspNetCore.Routing.Matching.CandidateSet"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.CandidateState.Endpoint">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.CandidateState.Score">
            <summary>
            Gets the score of the <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> within the current
            <see cref="T:Microsoft.AspNetCore.Routing.Matching.CandidateSet"/>.
            </summary>
            <remarks>
            <para>
            Candidates within a set are ordered in priority order and then assigned a
            sequential score value based on that ordering. Candiates with the same
            score are considered to have equal priority.
            </para>
            <para>
            The score values are used in the <see cref="T:Microsoft.AspNetCore.Routing.Matching.EndpointSelector"/> to determine
            whether a set of matching candidates is an ambiguous match.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.CandidateState.Values">
            <summary>
            Gets <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/> associated with the
            <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> and the current request.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Matching.EndpointMetadataComparer">
            <summary>
            A comparer that can order <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> instances based on implementations of
            <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointComparerPolicy" />. The implementation can be retrieved from the service
            provider and provided to <see cref="M:Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(System.Int32,System.Collections.Generic.IReadOnlyList{Microsoft.AspNetCore.Http.Endpoint},System.Collections.Generic.IComparer{Microsoft.AspNetCore.Http.Endpoint})"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Matching.EndpointMetadataComparer`1">
            <summary>
            A base class for <see cref="T:System.Collections.Generic.IComparer`1"/> implementations that use 
            a specific type of metadata from <see cref="P:Microsoft.AspNetCore.Http.Endpoint.Metadata"/> for comparison.
            Useful for implementing <see cref="P:Microsoft.AspNetCore.Routing.Matching.IEndpointComparerPolicy.Comparer"/>.
            </summary>
            <typeparam name="TMetadata">
            The type of metadata to compare. Typically this is a type of metadata related
            to the application concern being handled.
            </typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.EndpointMetadataComparer`1.Compare(Microsoft.AspNetCore.Http.Endpoint,Microsoft.AspNetCore.Http.Endpoint)">
            <summary>
            Compares two objects and returns a value indicating whether one is less than, equal to, 
            or greater than the other.
            </summary>
            <param name="x">The first object to compare.</param>
            <param name="y">The second object to compare.</param>
            <returns>
            An implementation of this method must return a value less than zero if 
            x is less than y, zero if x is equal to y, or a value greater than zero if x is 
            greater than y.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.EndpointMetadataComparer`1.GetMetadata(Microsoft.AspNetCore.Http.Endpoint)">
            <summary>
            Gets the metadata of type <typeparamref name="TMetadata"/> from the provided endpoint.
            </summary>
            <param name="endpoint">The <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/>.</param>
            <returns>The <typeparamref name="TMetadata"/> instance or <c>null</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.EndpointMetadataComparer`1.CompareMetadata(`0,`0)">
            <summary>
            Compares two <typeparamref name="TMetadata"/> instances.
            </summary>
            <param name="x">The first object to compare.</param>
            <param name="y">The second object to compare.</param>
            <returns>
            An implementation of this method must return a value less than zero if 
            x is less than y, zero if x is equal to y, or a value greater than zero if x is 
            greater than y.
            </returns>
            <remarks>
            The base-class implementation of this method will compare metadata based on whether
            or not they are <c>null</c>. The effect of this is that when endpoints are being
            compared, the endpoint that defines an instance of <typeparamref name="TMetadata"/>
            will be considered higher priority.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Matching.EndpointSelector">
            <summary>
            A service that is responsible for the final <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> selection
            decision. To use a custom <see cref="T:Microsoft.AspNetCore.Routing.Matching.EndpointSelector"/> register an implementation
            of <see cref="T:Microsoft.AspNetCore.Routing.Matching.EndpointSelector"/> in the dependency injection container as a singleton.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.EndpointSelector.SelectAsync(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.Matching.CandidateSet)">
            <summary>
            Asynchronously selects an <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> from the <see cref="T:Microsoft.AspNetCore.Routing.Matching.CandidateSet"/>.
            </summary>
            <param name="httpContext">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> associated with the current request.</param>
            <param name="candidates">The <see cref="T:Microsoft.AspNetCore.Routing.Matching.CandidateSet"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that completes asynchronously once endpoint selection is complete.</returns>
            <remarks>
            An <see cref="T:Microsoft.AspNetCore.Routing.Matching.EndpointSelector"/> should assign the endpoint by calling
            <see cref="M:Microsoft.AspNetCore.Http.EndpointHttpContextExtensions.SetEndpoint(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Http.Endpoint)"/>
            and setting <see cref="P:Microsoft.AspNetCore.Http.HttpRequest.RouteValues"/> once an endpoint is selected.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Matching.HostMatcherPolicy">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Routing.MatcherPolicy"/> that implements filtering and selection by
            the host header of a request.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Matching.HttpMethodMatcherPolicy">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Routing.MatcherPolicy"/> that implements filtering and selection by
            the HTTP method of a request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.HttpMethodMatcherPolicy.Comparer">
            <summary>
            For framework use only.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.HttpMethodMatcherPolicy.Order">
            <summary>
            For framework use only.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.HttpMethodMatcherPolicy.ApplyAsync(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.Matching.CandidateSet)">
            <summary>
            For framework use only.
            </summary>
            <param name="httpContext"></param>
            <param name="candidates"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.HttpMethodMatcherPolicy.GetEdges(System.Collections.Generic.IReadOnlyList{Microsoft.AspNetCore.Http.Endpoint})">
            <summary>
            For framework use only.
            </summary>
            <param name="endpoints"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.HttpMethodMatcherPolicy.BuildJumpTable(System.Int32,System.Collections.Generic.IReadOnlyList{Microsoft.AspNetCore.Routing.Matching.PolicyJumpTableEdge})">
            <summary>
            For framework use only.
            </summary>
            <param name="exitDestination"></param>
            <param name="edges"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Matching.IEndpointComparerPolicy">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Routing.MatcherPolicy"/> interface that can be implemented to sort
            endpoints. Implementations of <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointComparerPolicy"/> must
            inherit from <see cref="T:Microsoft.AspNetCore.Routing.MatcherPolicy"/> and should be registered in
            the dependency injection container as singleton services of type <see cref="T:Microsoft.AspNetCore.Routing.MatcherPolicy"/>.
            </summary>
            <remarks>
            <para>
            Candidates in a <see cref="T:Microsoft.AspNetCore.Routing.Matching.CandidateSet"/> are sorted based on their priority. Defining
            a <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointComparerPolicy"/> adds an additional criterion to the sorting
            operation used to order candidates.
            </para>
            <para>
            As an example, the implementation of <see cref="T:Microsoft.AspNetCore.Routing.Matching.HttpMethodMatcherPolicy"/> implements
            <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointComparerPolicy"/> to ensure that endpoints matching specific HTTP
            methods are sorted with a higher priority than endpoints without a specific HTTP method
            requirement.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.IEndpointComparerPolicy.Comparer">
            <summary>
            Gets an <see cref="T:System.Collections.Generic.IComparer`1"/> that will be used to sort the endpoints.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Matching.IEndpointSelectorPolicy">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Routing.MatcherPolicy"/> interface that can implemented to filter endpoints
            in a <see cref="T:Microsoft.AspNetCore.Routing.Matching.CandidateSet"/>. Implementations of <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointSelectorPolicy"/> must
            inherit from <see cref="T:Microsoft.AspNetCore.Routing.MatcherPolicy"/> and should be registered in
            the dependency injection container as singleton services of type <see cref="T:Microsoft.AspNetCore.Routing.MatcherPolicy"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.IEndpointSelectorPolicy.AppliesToEndpoints(System.Collections.Generic.IReadOnlyList{Microsoft.AspNetCore.Http.Endpoint})">
            <summary>
            Returns a value that indicates whether the <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointSelectorPolicy"/> applies
            to any endpoint in <paramref name="endpoints"/>.
            </summary>
            <param name="endpoints">The set of candidate <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> values.</param>
            <returns>
            <c>true</c> if the policy applies to any endpoint in <paramref name="endpoints"/>, otherwise <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.IEndpointSelectorPolicy.ApplyAsync(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.Matching.CandidateSet)">
            <summary>
            Applies the policy to the <see cref="T:Microsoft.AspNetCore.Routing.Matching.CandidateSet"/>.
            </summary>
            <param name="httpContext">
            The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> associated with the current request.
            </param>
            <param name="candidates">The <see cref="T:Microsoft.AspNetCore.Routing.Matching.CandidateSet"/>.</param>
            <remarks>
            <para>
            Implementations of <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointSelectorPolicy"/> should implement this method
            and filter the set of candidates in the <paramref name="candidates"/> by setting
            <see cref="M:Microsoft.AspNetCore.Routing.Matching.CandidateSet.SetValidity(System.Int32,System.Boolean)"/> to <c>false</c> where desired.
            </para>
            <para>
            To signal an error condition, the <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointSelectorPolicy"/> should assign the endpoint by
            calling <see cref="M:Microsoft.AspNetCore.Http.EndpointHttpContextExtensions.SetEndpoint(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Http.Endpoint)"/>
            and setting <see cref="P:Microsoft.AspNetCore.Http.HttpRequest.RouteValues"/> to an
            <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> value that will produce the desired error when executed.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.ILEmitTrieFactory.Locals.UInt16Value">
            <summary>
            Holds current character when processing a character at a time.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.ILEmitTrieFactory.Locals.UInt64Value">
            <summary>
            Holds current character when processing 4 characters at a time.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.ILEmitTrieFactory.Locals.UInt64LowerIndicator">
            <summary>
            Used to covert casing. See comments where it's used.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.ILEmitTrieFactory.Locals.UInt64UpperIndicator">
            <summary>
            Used to covert casing. See comments where it's used.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.ILEmitTrieFactory.Locals.P">
            <summary>
            Holds a 'ref byte' reference to the current character (in bytes).
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.ILEmitTrieFactory.Locals.Span">
            <summary>
            Holds the relevant portion of the path as a Span[byte].
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.ILEmitTrieFactory.Labels.ReturnDefault">
            <summary>
            Label to goto that will return the default destination (not a match).
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.ILEmitTrieFactory.Labels.ReturnNotAscii">
            <summary>
            Label to goto that will return a sentinel value for non-ascii text.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.ILEmitTrieFactory.Methods.Add">
            <summary>
            <see cref="M:System.Runtime.CompilerServices.Unsafe.Add``1(``0@,System.Int32)"/> - Add[ref byte]
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.ILEmitTrieFactory.Methods.As">
            <summary>
            <see cref="M:System.Runtime.CompilerServices.Unsafe.As``2(``0@)"/> - As[char, byte]
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.ILEmitTrieFactory.Methods.AsSpan">
            <summary>
            <see cref="M:System.MemoryExtensions.AsSpan(System.String,System.Int32,System.Int32)"/>
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.ILEmitTrieFactory.Methods.GetReference">
            <summary>
            <see cref="M:System.Runtime.InteropServices.MemoryMarshal.GetReference``1(System.ReadOnlySpan{``0})"/> - GetReference[char]
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.ILEmitTrieFactory.Methods.ReadUnalignedUInt64">
            <summary>
            <see cref="M:System.Runtime.CompilerServices.Unsafe.ReadUnaligned``1(System.Byte@)"/> - ReadUnaligned[ulong]
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Matching.ILEmitTrieFactory.Methods.ReadUnalignedUInt16">
            <summary>
            <see cref="M:System.Runtime.CompilerServices.Unsafe.ReadUnaligned``1(System.Byte@)"/> - ReadUnaligned[ushort]
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Matching.Matcher">
            <summary>
            An interface for components that can select an <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> given the current request, as part
            of the execution of <see cref="T:Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Matching.Matcher.MatchAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Attempts to asynchronously select an <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> for the current request.
            </summary>
            <param name="httpContext">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> associated with the current request.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which represents the asynchronous completion of the operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.MatcherPolicy">
            <summary>
            Defines a policy that applies behaviors to the URL matcher. Implementations
            of <see cref="T:Microsoft.AspNetCore.Routing.MatcherPolicy"/> and related interfaces must be registered
            in the dependency injection container as singleton services of type
            <see cref="T:Microsoft.AspNetCore.Routing.MatcherPolicy"/>.
            </summary>
            <remarks>
            <see cref="T:Microsoft.AspNetCore.Routing.MatcherPolicy"/> implementations can implement the following
            interfaces <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointComparerPolicy"/>, <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointSelectorPolicy"/>,
            and <see cref="T:Microsoft.AspNetCore.Routing.Matching.INodeBuilderPolicy"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.MatcherPolicy.Order">
            <summary>
            Gets a value that determines the order the <see cref="T:Microsoft.AspNetCore.Routing.MatcherPolicy"/> should
            be applied. Policies are applied in ascending numeric value of the <see cref="P:Microsoft.AspNetCore.Routing.MatcherPolicy.Order"/>
            property.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.MatcherPolicy.ContainsDynamicEndpoints(System.Collections.Generic.IReadOnlyList{Microsoft.AspNetCore.Http.Endpoint})">
            <summary>
            Returns a value that indicates whether the provided <paramref name="endpoints"/> contains
            one or more dynamic endpoints.
            </summary>
            <param name="endpoints">The set of endpoints.</param>
            <returns><c>true</c> if a dynamic endpoint is found; otherwise returns <c>false</c>.</returns>
            <remarks>
            <para>
            The presence of <see cref="T:Microsoft.AspNetCore.Routing.IDynamicEndpointMetadata"/> signifies that an endpoint that may be replaced 
            during processing by an <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointSelectorPolicy"/>.
            </para>
            <para>
            An implementation of <see cref="T:Microsoft.AspNetCore.Routing.Matching.INodeBuilderPolicy"/> should also implement <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointSelectorPolicy"/>
            and use its <see cref="T:Microsoft.AspNetCore.Routing.Matching.IEndpointSelectorPolicy"/> implementation when a node contains a dynamic endpoint.
            <see cref="T:Microsoft.AspNetCore.Routing.Matching.INodeBuilderPolicy"/> implementations rely on caching of data based on a static set of endpoints. This
            is not possible when endpoints are replaced dynamically.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.ParameterPolicyFactory">
            <summary>
            Defines an abstraction for resolving inline parameter policies as instances of <see cref="T:Microsoft.AspNetCore.Routing.IParameterPolicy"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.ParameterPolicyFactory.Create(Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart,System.String)">
            <summary>
            Creates a parameter policy.
            </summary>
            <param name="parameter">The parameter the parameter policy is being created for.</param>
            <param name="inlineText">The inline text to resolve.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.IParameterPolicy"/> for the parameter.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.ParameterPolicyFactory.Create(Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart,Microsoft.AspNetCore.Routing.IParameterPolicy)">
            <summary>
            Creates a parameter policy.
            </summary>
            <param name="parameter">The parameter the parameter policy is being created for.</param>
            <param name="parameterPolicy">An existing parameter policy.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.IParameterPolicy"/> for the parameter.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.ParameterPolicyFactory.Create(Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart,Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference)">
            <summary>
            Creates a parameter policy.
            </summary>
            <param name="parameter">The parameter the parameter policy is being created for.</param>
            <param name="reference">The reference to resolve.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.IParameterPolicy"/> for the parameter.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern">
            <summary>
            Represents a parsed route template with default values and constraints.
            Use <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory"/> to create <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>
            instances. Instances of <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> are immutable.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.RequiredValueAny">
            <summary>
            A marker object that can be used in <see cref="P:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.RequiredValues"/> to designate that
            any non-null or non-empty value is required.
            </summary>
            <remarks>
            <see cref="F:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.RequiredValueAny"/> is only use in routing is in <see cref="P:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.RequiredValues"/>.
            <see cref="F:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.RequiredValueAny"/> is not valid as a route value, and will convert to the null/empty string.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.Defaults">
            <summary>
            Gets the set of default values for the route pattern.
            The keys of <see cref="P:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.Defaults"/> are the route parameter names.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.ParameterPolicies">
            <summary>
            Gets the set of parameter policy references for the route pattern.
            The keys of <see cref="P:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.ParameterPolicies"/> are the route parameter names.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.RequiredValues">
            <summary>
            Gets a collection of route values that must be provided for this route pattern to be considered
            applicable.
            </summary>
            <remarks>
            <para>
            <see cref="P:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.RequiredValues"/> allows a framework to substitute route values into a parameterized template
            so that the same route template specification can be used to create multiple route patterns.
            <example>
            This example shows how a route template can be used with required values to substitute known
            route values for parameters.
            <code>
            Route Template: "{controller=Home}/{action=Index}/{id?}"
            Route Values: { controller = "Store", action = "Index" }
            </code>
            
            A route pattern produced in this way will match and generate URL paths like: <c>/Store</c>, 
            <c>/Store/Index</c>, and <c>/Store/Index/17</c>.
            </example>
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.InboundPrecedence">
            <summary>
            Gets the precedence value of the route pattern for URL matching.
            </summary>
            <remarks>
            Precedence is a computed value based on the structure of the route pattern
            used for building URL matching data structures.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.OutboundPrecedence">
            <summary>
            Gets the precedence value of the route pattern for URL generation.
            </summary>
            <remarks>
            Precedence is a computed value based on the structure of the route pattern
            used for building URL generation data structures.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.RawText">
            <summary>
            Gets the raw text supplied when parsing the route pattern. May be null.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.Parameters">
            <summary>
            Gets the list of route parameters.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.PathSegments">
            <summary>
            Gets the list of path segments.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.GetParameter(System.String)">
            <summary>
            Gets the parameter matching the given name.
            </summary>
            <param name="name">The name of the parameter to match.</param>
            <returns>The matching parameter or <c>null</c> if no parameter matches the given name.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternException">
            <summary>
            An exception that is thrown for error constructing a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternException.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternException"/>.
            </summary>
            <param name="pattern">The route pattern as raw text.</param>
            <param name="message">The exception message.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternException.Pattern">
            <summary>
            Gets the route pattern associated with this exception.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo"/> with the data needed to serialize the target object.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> to populate with data.</param>
            <param name="context">The destination (<see cref="T:System.Runtime.Serialization.StreamingContext" />) for this serialization.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory">
            <summary>
            Contains factory methods for creating <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> and related types.
            Use <see cref="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Parse(System.String)"/> to parse a route pattern in
            string format.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Parse(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> from its string representation.
            </summary>
            <param name="pattern">The route pattern string to parse.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Parse(System.String,System.Object,System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> from its string representation along
            with provided default values and parameter policies.
            </summary>
            <param name="pattern">The route pattern string to parse.</param>
            <param name="defaults">
            Additional default values to associated with the route pattern. May be null.
            The provided object will be converted to key-value pairs using <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/>
            and then merged into the parsed route pattern.
            </param>
            <param name="parameterPolicies">
            Additional parameter policies to associated with the route pattern. May be null.
            The provided object will be converted to key-value pairs using <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/>
            and then merged into the parsed route pattern.
            Multiple policies can be specified for a key by providing a collection as the value.
            </param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Parse(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> from its string representation along
            with provided default values and parameter policies.
            </summary>
            <param name="pattern">The route pattern string to parse.</param>
            <param name="defaults">
            Additional default values to associated with the route pattern. May be null.
            The provided object will be converted to key-value pairs using <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/>
            and then merged into the parsed route pattern.
            </param>
            <param name="parameterPolicies">
            Additional parameter policies to associated with the route pattern. May be null.
            The provided object will be converted to key-value pairs using <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/>
            and then merged into the parsed route pattern.
            Multiple policies can be specified for a key by providing a collection as the value.
            </param>
            <param name="requiredValues">
            Route values that can be substituted for parameters in the route pattern. See remarks on <see cref="P:Microsoft.AspNetCore.Routing.Patterns.RoutePattern.RequiredValues"/>.
            </param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Pattern(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> from a collection of segments.
            </summary>
            <param name="segments">The collection of segments.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Pattern(System.String,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> from a collection of segments.
            </summary>
            <param name="rawText">The raw text to associate with the route pattern. May be null.</param>
            <param name="segments">The collection of segments.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Pattern(System.Object,System.Object,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment})">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> from a collection of segments along
            with provided default values and parameter policies.
            </summary>
            <param name="defaults">
            Additional default values to associated with the route pattern. May be null.
            The provided object will be converted to key-value pairs using <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/>
            and then merged into the route pattern.
            </param>
            <param name="parameterPolicies">
            Additional parameter policies to associated with the route pattern. May be null.
            The provided object will be converted to key-value pairs using <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/>
            and then merged into the route pattern.
            Multiple policies can be specified for a key by providing a collection as the value.
            </param>
            <param name="segments">The collection of segments.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Pattern(System.String,System.Object,System.Object,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment})">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> from a collection of segments along
            with provided default values and parameter policies.
            </summary>
            <param name="rawText">The raw text to associate with the route pattern. May be null.</param>
            <param name="defaults">
            Additional default values to associated with the route pattern. May be null.
            The provided object will be converted to key-value pairs using <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/>
            and then merged into the route pattern.
            </param>
            <param name="parameterPolicies">
            Additional parameter policies to associated with the route pattern. May be null.
            The provided object will be converted to key-value pairs using <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/>
            and then merged into the route pattern.
            Multiple policies can be specified for a key by providing a collection as the value.
            </param>
            <param name="segments">The collection of segments.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Pattern(Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment[])">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> from a collection of segments.
            </summary>
            <param name="segments">The collection of segments.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Pattern(System.String,Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment[])">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> from a collection of segments.
            </summary>
            <param name="rawText">The raw text to associate with the route pattern. May be null.</param>
            <param name="segments">The collection of segments.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Pattern(System.Object,System.Object,Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> from a collection of segments along
            with provided default values and parameter policies.
            </summary>
            <param name="defaults">
            Additional default values to associated with the route pattern. May be null.
            The provided object will be converted to key-value pairs using <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/>
            and then merged into the route pattern.
            </param>
            <param name="parameterPolicies">
            Additional parameter policies to associated with the route pattern. May be null.
            The provided object will be converted to key-value pairs using <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/>
            and then merged into the route pattern.
            Multiple policies can be specified for a key by providing a collection as the value.
            </param>
            <param name="segments">The collection of segments.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Pattern(System.String,System.Object,System.Object,Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> from a collection of segments along
            with provided default values and parameter policies.
            </summary>
            <param name="rawText">The raw text to associate with the route pattern.</param>
            <param name="defaults">
            Additional default values to associated with the route pattern. May be null.
            The provided object will be converted to key-value pairs using <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/>
            and then merged into the route pattern.
            </param>
            <param name="parameterPolicies">
            Additional parameter policies to associated with the route pattern. May be null.
            The provided object will be converted to key-value pairs using <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/>
            and then merged into the route pattern.
            Multiple policies can be specified for a key by providing a collection as the value.
            </param>
            <param name="segments">The collection of segments.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Segment(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Routing.Patterns.RoutePatternPart})">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment"/> from the provided collection
            of parts.
            </summary>
            <param name="parts">The collection of parts.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Segment(Microsoft.AspNetCore.Routing.Patterns.RoutePatternPart[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment"/> from the provided collection
            of parts.
            </summary>
            <param name="parts">The collection of parts.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.LiteralPart(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternLiteralPart"/> from the provided text
            content.
            </summary>
            <param name="content">The text content.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternLiteralPart"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.SeparatorPart(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternSeparatorPart"/> from the provided text
            content.
            </summary>
            <param name="content">The text content.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternSeparatorPart"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.ParameterPart(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart"/> from the provided parameter name.
            </summary>
            <param name="parameterName">The parameter name.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.ParameterPart(System.String,System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart"/> from the provided parameter name
            and default value.
            </summary>
            <param name="parameterName">The parameter name.</param>
            <param name="default">The parameter default value. May be <c>null</c>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.ParameterPart(System.String,System.Object,Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterKind)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart"/> from the provided parameter name
            and default value, and parameter kind.
            </summary>
            <param name="parameterName">The parameter name.</param>
            <param name="default">The parameter default value. May be <c>null</c>.</param>
            <param name="parameterKind">The parameter kind.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.ParameterPart(System.String,System.Object,Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterKind,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference})">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart"/> from the provided parameter name
            and default value, parameter kind, and parameter policies.
            </summary>
            <param name="parameterName">The parameter name.</param>
            <param name="default">The parameter default value. May be <c>null</c>.</param>
            <param name="parameterKind">The parameter kind.</param>
            <param name="parameterPolicies">The parameter policies to associated with the parameter.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.ParameterPart(System.String,System.Object,Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterKind,Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart"/> from the provided parameter name
            and default value, parameter kind, and parameter policies.
            </summary>
            <param name="parameterName">The parameter name.</param>
            <param name="default">The parameter default value. May be <c>null</c>.</param>
            <param name="parameterKind">The parameter kind.</param>
            <param name="parameterPolicies">The parameter policies to associated with the parameter.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Constraint(System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference"/> from the provided contraint.
            </summary>
            <param name="constraint">
            The constraint object, which must be of type <see cref="T:Microsoft.AspNetCore.Routing.IRouteConstraint"/>
            or <see cref="T:System.String"/>. If the constraint object is a <see cref="T:System.String"/>
            then it will be tranformed into an instance of <see cref="T:Microsoft.AspNetCore.Routing.Constraints.RegexRouteConstraint"/>.
            </param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Constraint(Microsoft.AspNetCore.Routing.IRouteConstraint)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference"/> from the provided constraint.
            </summary>
            <param name="constraint">
            The constraint object.
            </param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Constraint(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference"/> from the provided constraint.
            </summary>
            <param name="constraint">
            The constraint text, which will be resolved by <see cref="T:Microsoft.AspNetCore.Routing.ParameterPolicyFactory"/>.
            </param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.ParameterPolicy(Microsoft.AspNetCore.Routing.IParameterPolicy)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference"/> from the provided object.
            </summary>
            <param name="parameterPolicy">
            The parameter policy object.
            </param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.ParameterPolicy(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference"/> from the provided object.
            </summary>
            <param name="parameterPolicy">
            The parameter policy text, which will be resolved by <see cref="T:Microsoft.AspNetCore.Routing.ParameterPolicyFactory"/>.
            </param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternLiteralPart">
            <summary>
            Resprents a literal text part of a route pattern. Instances of <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternLiteralPart"/>
            are immutable.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternLiteralPart.Content">
            <summary>
            Gets the text content.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterKind">
            <summary>
            Defines the kinds of <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart"/> instances.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterKind.Standard">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterKind"/> of a standard parameter
            without optional or catch all behavior.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterKind.Optional">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterKind"/> of an optional parameter.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterKind.CatchAll">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterKind"/> of a catch-all parameter.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart">
            <summary>
            Represents a parameter part in a route pattern. Instances of <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart"/>
            are immutable.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart.ParameterPolicies">
            <summary>
            Gets the list of parameter policies associated with this parameter.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart.EncodeSlashes">
            <summary>
            Gets the value indicating if slashes in current parameter's value should be encoded.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart.Default">
            <summary>
            Gets the default value of this route parameter. May be null.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart.IsCatchAll">
            <summary>
            Returns <c>true</c> if this part is a catch-all parameter.
            Otherwise returns <c>false</c>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart.IsOptional">
            <summary>
            Returns <c>true</c> if this part is an optional parameter.
            Otherwise returns <c>false</c>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart.ParameterKind">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterKind"/> of this parameter.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart.Name">
            <summary>
            Gets the parameter name.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference">
            <summary>
            The parsed representation of a policy in a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> parameter. Instances
            of <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference"/> are immutable.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference.Content">
            <summary>
            Gets the constraint text.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPolicyReference.ParameterPolicy">
            <summary>
            Gets a pre-existing <see cref="T:Microsoft.AspNetCore.Routing.IParameterPolicy"/> that was used to construct this reference.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPart">
            <summary>
            Represents a part of a route pattern.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPart.PartKind">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPartKind"/> of this part.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPart.IsLiteral">
            <summary>
            Returns <c>true</c> if this part is literal text. Otherwise returns <c>false</c>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPart.IsParameter">
            <summary>
            Returns <c>true</c> if this part is a route parameter. Otherwise returns <c>false</c>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPart.IsSeparator">
            <summary>
            Returns <c>true</c> if this part is an optional separator. Otherwise returns <c>false</c>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPartKind">
            <summary>
            Defines the kinds of <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPart"/> instances.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPartKind.Literal">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPartKind"/> of a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternLiteralPart"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPartKind.Parameter">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPartKind"/> of a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternParameterPart"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPartKind.Separator">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPartKind"/> of a <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternSeparatorPart"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment">
            <summary>
            Represents a path segment in a route pattern. Instances of <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment"/> are
            immutable.
            </summary>
            <remarks>
            Route patterns are made up of URL path segments, delimited by <c>/</c>. A
            <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment"/> contains a group of 
            <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPart"/> that represent the structure of a segment
            in a route pattern.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment.IsSimple">
            <summary>
            Returns <c>true</c> if the segment contains a single part;
            otherwise returns <c>false</c>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternPathSegment.Parts">
            <summary>
            Gets the list of parts in this segment.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternSeparatorPart">
            <summary>
            Represents an optional separator part of a route pattern. Instances of <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternSeparatorPart"/>
            are immutable.
            </summary>
            <remarks>
            <para>
            An optional separator is a literal text delimiter that appears between
            two parameter parts in the last segment of a route pattern. The only separator
            that is recognized is <c>.</c>.
            </para>
            <para>
            <example>
            In the route pattern <c>/{controller}/{action}/{id?}.{extension?}</c>
            the <c>.</c> character is an optional separator.
            </example>
            </para>
            <para>
            An optional separator character does not need to present in the URL path
            of a request for the route pattern to match.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Patterns.RoutePatternSeparatorPart.Content">
            <summary>
            Gets the text content of the part.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Patterns.RoutePatternTransformer">
            <summary>
            A singleton service that provides transformations on <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternTransformer.SubstituteRequiredValues(Microsoft.AspNetCore.Routing.Patterns.RoutePattern,System.Object)">
            <summary>
            Attempts to substitute the provided <paramref name="requiredValues"/> into the provided
            <paramref name="original"/>.
            </summary>
            <param name="original">The original <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.</param>
            <param name="requiredValues">The required values to substitute.</param>
            <returns>
            A new <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> if substitution succeeds, otherwise <c>null</c>.
            </returns>
            <remarks>
            <para>
            Substituting required values into a route pattern is intended for us with a general-purpose
            parameterize route specification that can match many logical endpoints. Calling 
            <see cref="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternTransformer.SubstituteRequiredValues(Microsoft.AspNetCore.Routing.Patterns.RoutePattern,System.Object)"/> can produce a derived route pattern
            for each set of route values that corresponds to an endpoint.
            </para>
            <para>
            The substitution process considers default values and <see cref="T:Microsoft.AspNetCore.Routing.IRouteConstraint"/> implementations
            when examining a required value. <see cref="M:Microsoft.AspNetCore.Routing.Patterns.RoutePatternTransformer.SubstituteRequiredValues(Microsoft.AspNetCore.Routing.Patterns.RoutePattern,System.Object)"/> will
            return <c>null</c> if any required value cannot be substituted.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapRoute(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> for the given <paramref name="template"/>, and
            <paramref name="handler"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="template">The route template.</param>
            <param name="handler">The <see cref="T:Microsoft.AspNetCore.Http.RequestDelegate"/> route handler.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapMiddlewareRoute(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.Action{Microsoft.AspNetCore.Builder.IApplicationBuilder})">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> for the given <paramref name="template"/>, and
            <paramref name="action"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="template">The route template.</param>
            <param name="action">The action to apply to the <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapDelete(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP DELETE requests for the given
            <paramref name="template"/>, and <paramref name="handler"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="template">The route template.</param>
            <param name="handler">The <see cref="T:Microsoft.AspNetCore.Http.RequestDelegate"/> route handler.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapMiddlewareDelete(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.Action{Microsoft.AspNetCore.Builder.IApplicationBuilder})">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP DELETE requests for the given
            <paramref name="template"/>, and <paramref name="action"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="template">The route template.</param>
            <param name="action">The action to apply to the <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapDelete(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.Func{Microsoft.AspNetCore.Http.HttpRequest,Microsoft.AspNetCore.Http.HttpResponse,Microsoft.AspNetCore.Routing.RouteData,System.Threading.Tasks.Task})">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP DELETE requests for the given
            <paramref name="template"/>, and <paramref name="handler"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="template">The route template.</param>
            <param name="handler">The route handler.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapGet(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP GET requests for the given
            <paramref name="template"/>, and <paramref name="handler"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="template">The route template.</param>
            <param name="handler">The <see cref="T:Microsoft.AspNetCore.Http.RequestDelegate"/> route handler.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapMiddlewareGet(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.Action{Microsoft.AspNetCore.Builder.IApplicationBuilder})">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP GET requests for the given
            <paramref name="template"/>, and <paramref name="action"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="template">The route template.</param>
            <param name="action">The action to apply to the <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapGet(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.Func{Microsoft.AspNetCore.Http.HttpRequest,Microsoft.AspNetCore.Http.HttpResponse,Microsoft.AspNetCore.Routing.RouteData,System.Threading.Tasks.Task})">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP GET requests for the given
            <paramref name="template"/>, and <paramref name="handler"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="template">The route template.</param>
            <param name="handler">The route handler.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapPost(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP POST requests for the given
            <paramref name="template"/>, and <paramref name="handler"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="template">The route template.</param>
            <param name="handler">The <see cref="T:Microsoft.AspNetCore.Http.RequestDelegate"/> route handler.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapMiddlewarePost(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.Action{Microsoft.AspNetCore.Builder.IApplicationBuilder})">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP POST requests for the given
            <paramref name="template"/>, and <paramref name="action"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="template">The route template.</param>
            <param name="action">The action to apply to the <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapPost(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.Func{Microsoft.AspNetCore.Http.HttpRequest,Microsoft.AspNetCore.Http.HttpResponse,Microsoft.AspNetCore.Routing.RouteData,System.Threading.Tasks.Task})">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP POST requests for the given
            <paramref name="template"/>, and <paramref name="handler"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="template">The route template.</param>
            <param name="handler">The route handler.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapPut(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP PUT requests for the given
            <paramref name="template"/>, and <paramref name="handler"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="template">The route template.</param>
            <param name="handler">The <see cref="T:Microsoft.AspNetCore.Http.RequestDelegate"/> route handler.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapMiddlewarePut(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.Action{Microsoft.AspNetCore.Builder.IApplicationBuilder})">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP PUT requests for the given
            <paramref name="template"/>, and <paramref name="action"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="template">The route template.</param>
            <param name="action">The action to apply to the <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapPut(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.Func{Microsoft.AspNetCore.Http.HttpRequest,Microsoft.AspNetCore.Http.HttpResponse,Microsoft.AspNetCore.Routing.RouteData,System.Threading.Tasks.Task})">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP PUT requests for the given
            <paramref name="template"/>, and <paramref name="handler"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="template">The route template.</param>
            <param name="handler">The route handler.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapVerb(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.String,System.Func{Microsoft.AspNetCore.Http.HttpRequest,Microsoft.AspNetCore.Http.HttpResponse,Microsoft.AspNetCore.Routing.RouteData,System.Threading.Tasks.Task})">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP requests for the given
            <paramref name="verb"/>, <paramref name="template"/>, and <paramref name="handler"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="verb">The HTTP verb allowed by the route.</param>
            <param name="template">The route template.</param>
            <param name="handler">The route handler.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapVerb(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.String,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP requests for the given
            <paramref name="verb"/>, <paramref name="template"/>, and <paramref name="handler"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="verb">The HTTP verb allowed by the route.</param>
            <param name="template">The route template.</param>
            <param name="handler">The <see cref="T:Microsoft.AspNetCore.Http.RequestDelegate"/> route handler.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RequestDelegateRouteBuilderExtensions.MapMiddlewareVerb(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.String,System.Action{Microsoft.AspNetCore.Builder.IApplicationBuilder})">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> that only matches HTTP requests for the given
            <paramref name="verb"/>, <paramref name="template"/>, and <paramref name="action"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <param name="verb">The HTTP verb allowed by the route.</param>
            <param name="template">The route template.</param>
            <param name="action">The action to apply to the <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.</param>
            <returns>A reference to the <paramref name="builder"/> after this operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RouteBase.RouteAsync(Microsoft.AspNetCore.Routing.RouteContext)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RouteBase.GetVirtualPath(Microsoft.AspNetCore.Routing.VirtualPathContext)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.RouteConstraintBuilder">
            <summary>
            A builder for produding a mapping of keys to see <see cref="T:Microsoft.AspNetCore.Routing.IRouteConstraint"/>.
            </summary>
            <remarks>
            <see cref="T:Microsoft.AspNetCore.Routing.RouteConstraintBuilder"/> allows iterative building a set of route constraints, and will
            merge multiple entries for the same key.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RouteConstraintBuilder.#ctor(Microsoft.AspNetCore.Routing.IInlineConstraintResolver,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Routing.RouteConstraintBuilder"/> instance.
            </summary>
            <param name="inlineConstraintResolver">The <see cref="T:Microsoft.AspNetCore.Routing.IInlineConstraintResolver"/>.</param>
            <param name="displayName">The display name (for use in error messages).</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RouteConstraintBuilder.Build">
            <summary>
            Builds a mapping of constraints.
            </summary>
            <returns>An <see cref="T:System.Collections.Generic.IDictionary`2"/> of the constraints.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RouteConstraintBuilder.AddConstraint(System.String,System.Object)">
             <summary>
             Adds a constraint instance for the given key.
             </summary>
             <param name="key">The key.</param>
             <param name="value">
             The constraint instance. Must either be a string or an instance of <see cref="T:Microsoft.AspNetCore.Routing.IRouteConstraint"/>.
             </param>
             <remarks>
             If the <paramref name="value"/> is a string, it will be converted to a <see cref="T:Microsoft.AspNetCore.Routing.Constraints.RegexRouteConstraint"/>.
            
             For example, the string <code>Product[0-9]+</code> will be converted to the regular expression
             <code>^(Product[0-9]+)</code>. See <see cref="T:System.Text.RegularExpressions.Regex"/> for more details.
             </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RouteConstraintBuilder.AddResolvedConstraint(System.String,System.String)">
            <summary>
            Adds a constraint for the given key, resolved by the <see cref="T:Microsoft.AspNetCore.Routing.IInlineConstraintResolver"/>.
            </summary>
            <param name="key">The key.</param>
            <param name="constraintText">The text to be resolved by <see cref="T:Microsoft.AspNetCore.Routing.IInlineConstraintResolver"/>.</param>
            <remarks>
            The <see cref="T:Microsoft.AspNetCore.Routing.IInlineConstraintResolver"/> can create <see cref="T:Microsoft.AspNetCore.Routing.IRouteConstraint"/> instances
            based on <paramref name="constraintText"/>. See <see cref="P:Microsoft.AspNetCore.Routing.RouteOptions.ConstraintMap"/> to register
            custom constraint types.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RouteConstraintBuilder.SetOptional(System.String)">
            <summary>
            Sets the given key as optional.
            </summary>
            <param name="key">The key.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.RouteCreationException">
            <summary>
            The exception that is thrown for invalid routes or constraints.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RouteCreationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.RouteCreationException"/> class with a specified error message.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RouteCreationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.RouteCreationException"/> class with a specified error message
            and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.RouteEndpoint">
            <summary>
            Represents an <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> that can be used in URL matching or URL generation.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RouteEndpoint.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.AspNetCore.Routing.Patterns.RoutePattern,System.Int32,Microsoft.AspNetCore.Http.EndpointMetadataCollection,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> class.
            </summary>
            <param name="requestDelegate">The delegate used to process requests for the endpoint.</param>
            <param name="routePattern">The <see cref="P:Microsoft.AspNetCore.Routing.RouteEndpoint.RoutePattern"/> to use in URL matching.</param>
            <param name="order">The order assigned to the endpoint.</param>
            <param name="metadata">
            The <see cref="T:Microsoft.AspNetCore.Http.EndpointMetadataCollection"/> or metadata associated with the endpoint.
            </param>
            <param name="displayName">The informational display name of the endpoint.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.RouteEndpoint.Order">
            <summary>
            Gets the order value of endpoint.
            </summary>
            <remarks>
            The order value provides absolute control over the priority
            of an endpoint. Endpoints with a lower numeric value of order have higher priority.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.RouteEndpoint.RoutePattern">
            <summary>
            Gets the <see cref="P:Microsoft.AspNetCore.Routing.RouteEndpoint.RoutePattern"/> associated with the endpoint.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.RouteNameMetadata">
            <summary>
            Metadata used during link generation to find the associated endpoint using route name.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RouteNameMetadata.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Routing.RouteNameMetadata"/> with the provided route name.
            </summary>
            <param name="routeName">The route name. Can be null.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.RouteNameMetadata.RouteName">
            <summary>
            Gets the route name. Can be null.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.RouteOptions.EndpointDataSources">
            <summary>
            Gets a collection of <see cref="T:Microsoft.AspNetCore.Routing.EndpointDataSource"/> instances configured with routing.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.RouteOptions.LowercaseUrls">
            <summary>
            Gets or sets a value indicating whether all generated paths URLs are lower-case. 
            Use <see cref="P:Microsoft.AspNetCore.Routing.RouteOptions.LowercaseQueryStrings" /> to configure the behavior for query strings.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.RouteOptions.LowercaseQueryStrings">
            <summary>
            Gets or sets a value indicating whether a generated query strings are lower-case.
            This property will not be used unless <see cref="P:Microsoft.AspNetCore.Routing.RouteOptions.LowercaseUrls" /> is also <c>true</c>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.RouteOptions.AppendTrailingSlash">
            <summary>
            Gets or sets a value indicating whether a trailing slash should be appended to the generated URLs.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.RouteOptions.SuppressCheckForUnhandledSecurityMetadata">
            <summary>
            Gets or sets a value that indicates if the check for unhandled security endpoint metadata is suppressed.
            <para>
            Endpoints can be associated with metadata such as authorization, or CORS, that needs to be
            handled by a specific middleware to be actionable. If the middleware is not configured, such
            metadata will go unhandled.
            </para>
            <para>
            When <see langword="false"/>, prior to the execution of the endpoint, routing will verify that
            all known security-specific metadata has been handled.
            Setting this property to <see langword="true"/> suppresses this check.
            </para>
            </summary>
            <value>Defaults to <see langword="false"/>.</value>
            <remarks>
            This check exists as a safeguard against accidental insecure configuration. You may suppress
            this check if it does not match your application's requirements.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.RouteValueEqualityComparer">
             <summary>
             An <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> implementation that compares objects as-if
             they were route value strings.
             </summary>
             <remarks>
             Values that are are not strings are converted to strings using
             <c>Convert.ToString(x, CultureInfo.InvariantCulture)</c>. <c>null</c> values are converted
             to the empty string.
            
             strings are compared using <see cref="F:System.StringComparison.OrdinalIgnoreCase"/>.
             </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RouteValueEqualityComparer.Equals(System.Object,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.RouteValueEqualityComparer.GetHashCode(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.RouteValuesAddress">
            <summary>
            An address of route name and values.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.RouteValuesAddress.RouteName">
            <summary>
            Gets or sets the route name.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.RouteValuesAddress.ExplicitValues">
            <summary>
            Gets or sets the route values that are explicitly specified.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.RouteValuesAddress.AmbientValues">
            <summary>
            Gets or sets ambient route values from the current HTTP request.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.RoutingMarkerService">
            <summary>
            A marker class used to determine if all the routing services were added
            to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> before routing is configured.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.SuppressLinkGenerationMetadata">
            <summary>
            Represents metadata used during link generation. If <see cref="P:Microsoft.AspNetCore.Routing.SuppressLinkGenerationMetadata.SuppressLinkGeneration"/> is <c>true</c> 
            the associated endpoint will not be used for link generation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.SuppressLinkGenerationMetadata.SuppressLinkGeneration">
            <summary>
            Gets a value indicating whether the assocated endpoint should be used for link generation.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.SuppressMatchingMetadata">
            <summary>
            Metadata used to prevent URL matching. If <see cref="P:Microsoft.AspNetCore.Routing.SuppressMatchingMetadata.SuppressMatching"/> is <c>true</c> the
            associated endpoint will not be considered for URL matching.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.SuppressMatchingMetadata.SuppressMatching">
            <summary>
            Gets a value indicating whether the assocated endpoint should be used for URL matching.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Template.InlineConstraint">
            <summary>
            The parsed representation of an inline constraint in a route parameter.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Template.InlineConstraint.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Routing.Template.InlineConstraint"/>.
            </summary>
            <param name="constraint">The constraint text.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Template.InlineConstraint.Constraint">
            <summary>
            Gets the constraint text.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Template.RoutePrecedence">
            <summary>
            Computes precedence for a route template.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Template.RouteTemplate.GetParameter(System.String)">
            <summary>
            Gets the parameter matching the given name.
            </summary>
            <param name="name">The name of the parameter to match.</param>
            <returns>The matching parameter or <c>null</c> if no parameter matches the given name.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Template.RouteTemplate.ToRoutePattern">
            <summary>
            Converts the <see cref="T:Microsoft.AspNetCore.Routing.Template.RouteTemplate"/> to the equivalent 
            <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>
            </summary>
            <returns>A <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Template.TemplateBinder.#ctor(System.Text.Encodings.Web.UrlEncoder,Microsoft.Extensions.ObjectPool.ObjectPool{Microsoft.AspNetCore.Routing.UriBuildingContext},Microsoft.AspNetCore.Routing.Template.RouteTemplate,Microsoft.AspNetCore.Routing.RouteValueDictionary)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Routing.Template.TemplateBinder"/>.
            </summary>
            <param name="urlEncoder">The <see cref="T:System.Text.Encodings.Web.UrlEncoder"/>.</param>
            <param name="pool">The <see cref="T:Microsoft.Extensions.ObjectPool.ObjectPool`1"/>.</param>
            <param name="template">The <see cref="T:Microsoft.AspNetCore.Routing.Template.RouteTemplate"/> to bind values to.</param>
            <param name="defaults">The default values for <paramref name="template"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Template.TemplateBinder.#ctor(System.Text.Encodings.Web.UrlEncoder,Microsoft.Extensions.ObjectPool.ObjectPool{Microsoft.AspNetCore.Routing.UriBuildingContext},Microsoft.AspNetCore.Routing.Patterns.RoutePattern,Microsoft.AspNetCore.Routing.RouteValueDictionary,System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.ValueTuple{System.String,Microsoft.AspNetCore.Routing.IParameterPolicy}})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Routing.Template.TemplateBinder"/>.
            </summary>
            <param name="urlEncoder">The <see cref="T:System.Text.Encodings.Web.UrlEncoder"/>.</param>
            <param name="pool">The <see cref="T:Microsoft.Extensions.ObjectPool.ObjectPool`1"/>.</param>
            <param name="pattern">The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/> to bind values to.</param>
            <param name="defaults">The default values for <paramref name="pattern"/>. Optional.</param>
            <param name="requiredKeys">Keys used to determine if the ambient values apply. Optional.</param>
            <param name="parameterPolicies">
            A list of (<see cref="T:System.String"/>, <see cref="T:Microsoft.AspNetCore.Routing.IParameterPolicy"/>) pairs to evalute when producing a URI.
            </param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Template.TemplateBinder.RoutePartsEqual(System.Object,System.Object)">
            <summary>
            Compares two objects for equality as parts of a case-insensitive path.
            </summary>
            <param name="a">An object to compare.</param>
            <param name="b">An object to compare.</param>
            <returns>True if the object are equal, otherwise false.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Template.TemplateBinderFactory">
            <summary>
            A factory used to create <see cref="T:Microsoft.AspNetCore.Routing.Template.TemplateBinder"/> instances.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Template.TemplateBinderFactory.Create(Microsoft.AspNetCore.Routing.Template.RouteTemplate,Microsoft.AspNetCore.Routing.RouteValueDictionary)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Routing.Template.TemplateBinder"/> from the provided <paramref name="template"/> and
            <paramref name="defaults"/>.
            </summary>
            <param name="template">The route template.</param>
            <param name="defaults">A collection of extra default values that do not appear in the route template.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Routing.Template.TemplateBinder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Template.TemplateBinderFactory.Create(Microsoft.AspNetCore.Routing.Patterns.RoutePattern)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Routing.Template.TemplateBinder"/> from the provided <paramref name="pattern"/>.
            </summary>
            <param name="pattern">The <see cref="T:Microsoft.AspNetCore.Routing.Patterns.RoutePattern"/>.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Routing.Template.TemplateBinder"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Template.TemplateValuesResult">
            <summary>
            The values used as inputs for constraints and link generation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Template.TemplateValuesResult.AcceptedValues">
            <summary>
            The set of values that will appear in the URL.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Template.TemplateValuesResult.CombinedValues">
             <summary>
             The set of values that that were supplied for URL generation.
             </summary>
             <remarks>
             This combines implicit (ambient) values from the <see cref="T:Microsoft.AspNetCore.Routing.RouteData"/> of the current request
             (if applicable), explictly provided values, and default values for parameters that appear in
             the route template.
            
             Implicit (ambient) values which are invalidated due to changes in values lexically earlier in the
             route template are excluded from this set.
             </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Tree.InboundMatch">
            <summary>
            A candidate route to match incoming URLs in a <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.InboundMatch.Entry">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.InboundMatch.TemplateMatcher">
            <summary>
            Gets or sets the <see cref="P:Microsoft.AspNetCore.Routing.Tree.InboundMatch.TemplateMatcher"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry">
            <summary>
            Used to build an <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>. Represents a URL template tha will be used to match incoming
            request URLs.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry.Constraints">
            <summary>
            Gets or sets the route constraints.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry.Defaults">
            <summary>
            Gets or sets the route defaults.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry.Handler">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Routing.IRouter"/> to invoke when this entry matches.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry.Order">
            <summary>
            Gets or sets the order of the entry.
            </summary>
            <remarks>
            Entries are ordered first by <see cref="P:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry.Order"/> (ascending) then by <see cref="P:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry.Precedence"/> (descending).
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry.Precedence">
            <summary>
            Gets or sets the precedence of the entry.
            </summary>
            <remarks>
            Entries are ordered first by <see cref="P:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry.Order"/> (ascending) then by <see cref="P:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry.Precedence"/> (descending).
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry.RouteName">
            <summary>
            Gets or sets the name of the route.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry.RouteTemplate">
            <summary>
            Gets or sets the <see cref="P:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry.RouteTemplate"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Tree.OutboundMatch">
            <summary>
            A candidate match for link generation in a <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.OutboundMatch.Entry">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.OutboundMatch.TemplateBinder">
            <summary>
            Gets or sets the <see cref="P:Microsoft.AspNetCore.Routing.Tree.OutboundMatch.TemplateBinder"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry">
            <summary>
            Used to build a <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>. Represents a URL template that will be used to generate
            outgoing URLs.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.Constraints">
            <summary>
            Gets or sets the route constraints.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.Defaults">
            <summary>
            Gets or sets the route defaults.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.Handler">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Routing.IRouter"/> to invoke when this entry matches.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.Order">
            <summary>
            Gets or sets the order of the entry.
            </summary>
            <remarks>
            Entries are ordered first by <see cref="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.Order"/> (ascending) then by <see cref="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.Precedence"/> (descending).
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.Precedence">
            <summary>
            Gets or sets the precedence of the template for link generation. A greater value of
            <see cref="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.Precedence"/> means that an entry is considered first.
            </summary>
            <remarks>
            Entries are ordered first by <see cref="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.Order"/> (ascending) then by <see cref="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.Precedence"/> (descending).
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.RouteName">
            <summary>
            Gets or sets the name of the route.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.RequiredLinkValues">
            <summary>
            Gets or sets the set of values that must be present for link genration.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.RouteTemplate">
            <summary>
            Gets or sets the <see cref="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.RouteTemplate"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry.Data">
            <summary>
            Gets or sets the data that is associated with this entry.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder">
            <summary>
            Builder for <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/> instances.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder.#ctor(Microsoft.Extensions.Logging.ILoggerFactory,Microsoft.Extensions.ObjectPool.ObjectPool{Microsoft.AspNetCore.Routing.UriBuildingContext},Microsoft.AspNetCore.Routing.IInlineConstraintResolver)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder"/>.
            </summary>
            <param name="loggerFactory">The <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/>.</param>
            <param name="objectPool">The <see cref="T:Microsoft.Extensions.ObjectPool.ObjectPool`1"/>.</param>
            <param name="constraintResolver">The <see cref="T:Microsoft.AspNetCore.Routing.IInlineConstraintResolver"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder.MapInbound(Microsoft.AspNetCore.Routing.IRouter,Microsoft.AspNetCore.Routing.Template.RouteTemplate,System.String,System.Int32)">
            <summary>
            Adds a new inbound route to the <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>.
            </summary>
            <param name="handler">The <see cref="T:Microsoft.AspNetCore.Routing.IRouter"/> for handling the route.</param>
            <param name="routeTemplate">The <see cref="T:Microsoft.AspNetCore.Routing.Template.RouteTemplate"/> of the route.</param>
            <param name="routeName">The route name.</param>
            <param name="order">The route order.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder.MapOutbound(Microsoft.AspNetCore.Routing.IRouter,Microsoft.AspNetCore.Routing.Template.RouteTemplate,Microsoft.AspNetCore.Routing.RouteValueDictionary,System.String,System.Int32)">
            <summary>
            Adds a new outbound route to the <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>.
            </summary>
            <param name="handler">The <see cref="T:Microsoft.AspNetCore.Routing.IRouter"/> for handling the link generation.</param>
            <param name="routeTemplate">The <see cref="T:Microsoft.AspNetCore.Routing.Template.RouteTemplate"/> of the route.</param>
            <param name="requiredLinkValues">The <see cref="T:Microsoft.AspNetCore.Routing.RouteValueDictionary"/> containing the route values.</param>
            <param name="routeName">The route name.</param>
            <param name="order">The route order.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry"/>.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder.InboundEntries">
            <summary>
            Gets the list of <see cref="T:Microsoft.AspNetCore.Routing.Tree.InboundRouteEntry"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder.OutboundEntries">
            <summary>
            Gets the list of <see cref="T:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder.Build">
            <summary>
            Builds a <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/> with the <see cref="P:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder.InboundEntries"/>
            and <see cref="P:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder.OutboundEntries"/> defined in this <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder"/>.
            </summary>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder.Build(System.Int32)">
            <summary>
            Builds a <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/> with the <see cref="P:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder.InboundEntries"/>
            and <see cref="P:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder.OutboundEntries"/> defined in this <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder"/>.
            </summary>
            <param name="version">The version of the <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder.Clear">
            <summary>
            Removes all <see cref="P:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder.InboundEntries"/> and <see cref="P:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder.OutboundEntries"/> from this
            <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouteBuilder"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Routing.IRouter"/> implementation for attribute routing.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Tree.TreeRouter.#ctor(Microsoft.AspNetCore.Routing.Tree.UrlMatchingTree[],System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry},System.Text.Encodings.Web.UrlEncoder,Microsoft.Extensions.ObjectPool.ObjectPool{Microsoft.AspNetCore.Routing.UriBuildingContext},Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.ILogger,System.Int32)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>.
            </summary>
            <param name="trees">The list of <see cref="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingTree"/> that contains the route entries.</param>
            <param name="linkGenerationEntries">The set of <see cref="T:Microsoft.AspNetCore.Routing.Tree.OutboundRouteEntry"/>.</param>
            <param name="urlEncoder">The <see cref="T:System.Text.Encodings.Web.UrlEncoder"/>.</param>
            <param name="objectPool">The <see cref="T:Microsoft.Extensions.ObjectPool.ObjectPool`1"/>.</param>
            <param name="routeLogger">The <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instance.</param>
            <param name="constraintLogger">The <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instance used
            in <see cref="T:Microsoft.AspNetCore.Routing.RouteConstraintMatcher"/>.</param>
            <param name="version">The version of this route.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.TreeRouter.Version">
            <summary>
            Gets the version of this route.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Tree.TreeRouter.GetVirtualPath(Microsoft.AspNetCore.Routing.VirtualPathContext)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Tree.TreeRouter.RouteAsync(Microsoft.AspNetCore.Routing.RouteContext)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode">
            <summary>
            A node in a <see cref="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingTree"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode"/>.
            </summary>
            <param name="length">The length of the path to this node in the <see cref="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingTree"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode.Depth">
            <summary>
            Gets the length of the path to this node in the <see cref="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingTree"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode.IsCatchAll">
            <summary>
            Gets or sets a value indicating whether this node represents a catch all segment.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode.Matches">
            <summary>
            Gets the list of matching route entries associated with this node.
            </summary>
            <remarks>
            These entries are sorted by precedence then template.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode.Literals">
            <summary>
            Gets the literal segments following this segment.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode.ConstrainedParameters">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode"/> representing
            parameter segments with constraints following this segment in the <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode.Parameters">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode"/> representing
            parameter segments following this segment in the <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode.ConstrainedCatchAlls">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode"/> representing
            catch all parameter segments with constraints following this segment in the <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode.CatchAlls">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingNode"/> representing
            catch all parameter segments following this segment in the <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingTree">
            <summary>
            A tree part of a <see cref="T:Microsoft.AspNetCore.Routing.Tree.TreeRouter"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Tree.UrlMatchingTree.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingTree"/>.
            </summary>
            <param name="order">The order associated with routes in this <see cref="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingTree"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.UrlMatchingTree.Order">
            <summary>
            Gets the order of the routes associated with this <see cref="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingTree"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Tree.UrlMatchingTree.Root">
            <summary>
            Gets the root of the <see cref="T:Microsoft.AspNetCore.Routing.Tree.UrlMatchingTree"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.ArgumentMustBeGreaterThanOrEqualTo">
            <summary>Value must be greater than or equal to {0}.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatArgumentMustBeGreaterThanOrEqualTo(System.Object)">
            <summary>Value must be greater than or equal to {0}.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.RangeConstraint_MinShouldBeLessThanOrEqualToMax">
            <summary>The value for argument '{0}' should be less than or equal to the value for the argument '{1}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatRangeConstraint_MinShouldBeLessThanOrEqualToMax(System.Object,System.Object)">
            <summary>The value for argument '{0}' should be less than or equal to the value for the argument '{1}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.PropertyOfTypeCannotBeNull">
            <summary>The '{0}' property of '{1}' must not be null.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatPropertyOfTypeCannotBeNull(System.Object,System.Object)">
            <summary>The '{0}' property of '{1}' must not be null.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.NamedRoutes_AmbiguousRoutesFound">
            <summary>The supplied route name '{0}' is ambiguous and matched more than one route.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatNamedRoutes_AmbiguousRoutesFound(System.Object)">
            <summary>The supplied route name '{0}' is ambiguous and matched more than one route.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.DefaultHandler_MustBeSet">
            <summary>A default handler must be set on the {0}.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatDefaultHandler_MustBeSet(System.Object)">
            <summary>A default handler must be set on the {0}.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.DefaultInlineConstraintResolver_AmbiguousCtors">
            <summary>The constructor to use for activating the constraint type '{0}' is ambiguous. Multiple constructors were found with the following number of parameters: {1}.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatDefaultInlineConstraintResolver_AmbiguousCtors(System.Object,System.Object)">
            <summary>The constructor to use for activating the constraint type '{0}' is ambiguous. Multiple constructors were found with the following number of parameters: {1}.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.DefaultInlineConstraintResolver_CouldNotFindCtor">
            <summary>Could not find a constructor for constraint type '{0}' with the following number of parameters: {1}.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatDefaultInlineConstraintResolver_CouldNotFindCtor(System.Object,System.Object)">
            <summary>Could not find a constructor for constraint type '{0}' with the following number of parameters: {1}.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.DefaultInlineConstraintResolver_TypeNotConstraint">
            <summary>The constraint type '{0}' which is mapped to constraint key '{1}' must implement the '{2}' interface.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatDefaultInlineConstraintResolver_TypeNotConstraint(System.Object,System.Object,System.Object)">
            <summary>The constraint type '{0}' which is mapped to constraint key '{1}' must implement the '{2}' interface.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_CannotHaveCatchAllInMultiSegment">
            <summary>A path segment that contains more than one section, such as a literal section or a parameter, cannot contain a catch-all parameter.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_CannotHaveDefaultValueSpecifiedInlineAndExplicitly">
            <summary>The route parameter '{0}' has both an inline default value and an explicit default value specified. A route parameter cannot contain an inline default value when a default value is specified explicitly. Consider removing one of them.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatTemplateRoute_CannotHaveDefaultValueSpecifiedInlineAndExplicitly(System.Object)">
            <summary>The route parameter '{0}' has both an inline default value and an explicit default value specified. A route parameter cannot contain an inline default value when a default value is specified explicitly. Consider removing one of them.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_CannotHaveConsecutiveParameters">
            <summary>A path segment cannot contain two consecutive parameters. They must be separated by a '/' or by a literal string.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_CannotHaveConsecutiveSeparators">
            <summary>The route template separator character '/' cannot appear consecutively. It must be separated by either a parameter or a literal value.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_CatchAllCannotBeOptional">
            <summary>A catch-all parameter cannot be marked optional.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_OptionalCannotHaveDefaultValue">
            <summary>An optional parameter cannot have default value.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_CatchAllMustBeLast">
            <summary>A catch-all parameter can only appear as the last segment of the route template.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_InvalidLiteral">
            <summary>The literal section '{0}' is invalid. Literal sections cannot contain the '?' character.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatTemplateRoute_InvalidLiteral(System.Object)">
            <summary>The literal section '{0}' is invalid. Literal sections cannot contain the '?' character.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_InvalidParameterName">
            <summary>The route parameter name '{0}' is invalid. Route parameter names must be non-empty and cannot contain these characters: '{{', '}}', '/'. The '?' character marks a parameter as optional, and can occur only at the end of the parameter. The '*' character mark ...</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatTemplateRoute_InvalidParameterName(System.Object)">
            <summary>The route parameter name '{0}' is invalid. Route parameter names must be non-empty and cannot contain these characters: '{{', '}}', '/'. The '?' character marks a parameter as optional, and can occur only at the end of the parameter. The '*' character mark ...</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_InvalidRouteTemplate">
            <summary>The route template cannot start with a '~' character unless followed by a '/'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_MismatchedParameter">
            <summary>There is an incomplete parameter in the route template. Check that each '{' character has a matching '}' character.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_RepeatedParameter">
            <summary>The route parameter name '{0}' appears more than one time in the route template.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatTemplateRoute_RepeatedParameter(System.Object)">
            <summary>The route parameter name '{0}' appears more than one time in the route template.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.RouteConstraintBuilder_ValidationMustBeStringOrCustomConstraint">
            <summary>The constraint entry '{0}' - '{1}' on the route '{2}' must have a string value or be of a type which implements '{3}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatRouteConstraintBuilder_ValidationMustBeStringOrCustomConstraint(System.Object,System.Object,System.Object,System.Object)">
            <summary>The constraint entry '{0}' - '{1}' on the route '{2}' must have a string value or be of a type which implements '{3}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.RouteConstraintBuilder_CouldNotResolveConstraint">
            <summary>The constraint entry '{0}' - '{1}' on the route '{2}' could not be resolved by the constraint resolver of type '{3}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatRouteConstraintBuilder_CouldNotResolveConstraint(System.Object,System.Object,System.Object,System.Object)">
            <summary>The constraint entry '{0}' - '{1}' on the route '{2}' could not be resolved by the constraint resolver of type '{3}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_UnescapedBrace">
            <summary>In a route parameter, '{' and '}' must be escaped with '{{' and '}}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_OptionalParameterCanbBePrecededByPeriod">
            <summary>In the segment '{0}', the optional parameter '{1}' is preceded by an invalid segment '{2}'. Only a period (.) can precede an optional parameter.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatTemplateRoute_OptionalParameterCanbBePrecededByPeriod(System.Object,System.Object,System.Object)">
            <summary>In the segment '{0}', the optional parameter '{1}' is preceded by an invalid segment '{2}'. Only a period (.) can precede an optional parameter.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_OptionalParameterHasTobeTheLast">
            <summary>An optional parameter must be at the end of the segment. In the segment '{0}', optional parameter '{1}' is followed by '{2}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatTemplateRoute_OptionalParameterHasTobeTheLast(System.Object,System.Object,System.Object)">
            <summary>An optional parameter must be at the end of the segment. In the segment '{0}', optional parameter '{1}' is followed by '{2}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.AttributeRoute_DifferentLinkGenerationEntries_SameName">
            <summary>Two or more routes named '{0}' have different templates.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatAttributeRoute_DifferentLinkGenerationEntries_SameName(System.Object)">
            <summary>Two or more routes named '{0}' have different templates.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.UnableToFindServices">
            <summary>Unable to find the required services. Please add all the required services by calling '{0}.{1}' inside the call to '{2}' in the application startup code.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatUnableToFindServices(System.Object,System.Object,System.Object)">
            <summary>Unable to find the required services. Please add all the required services by calling '{0}.{1}' inside the call to '{2}' in the application startup code.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.TemplateRoute_Exception">
            <summary>An error occurred while creating the route with name '{0}' and template '{1}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatTemplateRoute_Exception(System.Object,System.Object)">
            <summary>An error occurred while creating the route with name '{0}' and template '{1}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.AmbiguousEndpoints">
            <summary>The request matched multiple endpoints. Matches: {0}{0}{1}</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatAmbiguousEndpoints(System.Object,System.Object)">
            <summary>The request matched multiple endpoints. Matches: {0}{0}{1}</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.Argument_NullOrEmpty">
            <summary>Value cannot be null or empty.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.RoutePatternBuilder_CollectionCannotBeEmpty">
            <summary>The collection cannot be empty.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.ConstraintMustBeStringOrConstraint">
            <summary>The constraint entry '{0}' - '{1}' must have a string value or be of a type which implements '{2}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatConstraintMustBeStringOrConstraint(System.Object,System.Object,System.Object)">
            <summary>The constraint entry '{0}' - '{1}' must have a string value or be of a type which implements '{2}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.RoutePattern_InvalidConstraintReference">
            <summary>Invalid constraint '{0}'. A constraint must be of type 'string' or '{1}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatRoutePattern_InvalidConstraintReference(System.Object,System.Object)">
            <summary>Invalid constraint '{0}'. A constraint must be of type 'string' or '{1}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.RoutePattern_InvalidParameterConstraintReference">
            <summary>Invalid constraint '{0}' for parameter '{1}'. A constraint must be of type 'string', '{2}', or '{3}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatRoutePattern_InvalidParameterConstraintReference(System.Object,System.Object,System.Object,System.Object)">
            <summary>Invalid constraint '{0}' for parameter '{1}'. A constraint must be of type 'string', '{2}', or '{3}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.RoutePattern_ConstraintReferenceNotFound">
            <summary>The constraint reference '{0}' could not be resolved to a type. Register the constraint type with '{1}.{2}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatRoutePattern_ConstraintReferenceNotFound(System.Object,System.Object,System.Object)">
            <summary>The constraint reference '{0}' could not be resolved to a type. Register the constraint type with '{1}.{2}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.RoutePattern_InvalidStringConstraintReference">
            <summary>Invalid constraint type '{0}' registered as '{1}'. A constraint  type must either implement '{2}', or inherit from '{3}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatRoutePattern_InvalidStringConstraintReference(System.Object,System.Object,System.Object,System.Object)">
            <summary>Invalid constraint type '{0}' registered as '{1}'. A constraint  type must either implement '{2}', or inherit from '{3}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.DuplicateEndpointNameEntry">
            <summary>Endpoints with endpoint name '{0}':</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Routing.Resources.FormatDuplicateEndpointNameEntry(System.Object)">
            <summary>Endpoints with endpoint name '{0}':</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Routing.Resources.DuplicateEndpointNameHeader">
            <summary>The following endpoints with a duplicate endpoint name were found.</summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.EndpointRouteBuilderExtensions">
            <summary>
            Provides extension methods for <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add endpoints.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.EndpointRouteBuilderExtensions.MapGet(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> that matches HTTP GET requests
            for the specified pattern.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add the route to.</param>
            <param name="pattern">The route pattern.</param>
            <param name="requestDelegate">The delegate executed when the endpoint is matched.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/> that can be used to further customize the endpoint.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.EndpointRouteBuilderExtensions.MapPost(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> that matches HTTP POST requests
            for the specified pattern.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add the route to.</param>
            <param name="pattern">The route pattern.</param>
            <param name="requestDelegate">The delegate executed when the endpoint is matched.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/> that can be used to further customize the endpoint.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.EndpointRouteBuilderExtensions.MapPut(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> that matches HTTP PUT requests
            for the specified pattern.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add the route to.</param>
            <param name="pattern">The route pattern.</param>
            <param name="requestDelegate">The delegate executed when the endpoint is matched.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/> that can be used to further customize the endpoint.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.EndpointRouteBuilderExtensions.MapDelete(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> that matches HTTP DELETE requests
            for the specified pattern.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add the route to.</param>
            <param name="pattern">The route pattern.</param>
            <param name="requestDelegate">The delegate executed when the endpoint is matched.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/> that can be used to further customize the endpoint.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.EndpointRouteBuilderExtensions.MapMethods(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,System.Collections.Generic.IEnumerable{System.String},Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> that matches HTTP requests
            for the specified HTTP methods and pattern.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add the route to.</param>
            <param name="pattern">The route pattern.</param>
            <param name="requestDelegate">The delegate executed when the endpoint is matched.</param>
            <param name="httpMethods">HTTP methods that the endpoint will match.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/> that can be used to further customize the endpoint.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.EndpointRouteBuilderExtensions.Map(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> that matches HTTP requests
            for the specified pattern.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add the route to.</param>
            <param name="pattern">The route pattern.</param>
            <param name="requestDelegate">The delegate executed when the endpoint is matched.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/> that can be used to further customize the endpoint.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.EndpointRouteBuilderExtensions.Map(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,Microsoft.AspNetCore.Routing.Patterns.RoutePattern,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> that matches HTTP requests
            for the specified pattern.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add the route to.</param>
            <param name="pattern">The route pattern.</param>
            <param name="requestDelegate">The delegate executed when the endpoint is matched.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/> that can be used to further customize the endpoint.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.EndpointRoutingApplicationBuilderExtensions.UseRouting(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware"/> middleware to the specified <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> to add the middleware to.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
            <remarks>
            <para>
            A call to <see cref="M:Microsoft.AspNetCore.Builder.EndpointRoutingApplicationBuilderExtensions.UseRouting(Microsoft.AspNetCore.Builder.IApplicationBuilder)"/> must be followed by a call to
            <see cref="M:Microsoft.AspNetCore.Builder.EndpointRoutingApplicationBuilderExtensions.UseEndpoints(Microsoft.AspNetCore.Builder.IApplicationBuilder,System.Action{Microsoft.AspNetCore.Routing.IEndpointRouteBuilder})"/> for the same <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>
            instance.
            </para>
            <para>
            The <see cref="T:Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware"/> defines a point in the middleware pipeline where routing decisions are
            made, and an <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> is associated with the <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>. The <see cref="T:Microsoft.AspNetCore.Routing.EndpointMiddleware"/>
            defines a point in the middleware pipeline where the current <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> is executed. Middleware between
            the <see cref="T:Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware"/> and <see cref="T:Microsoft.AspNetCore.Routing.EndpointMiddleware"/> may observe or change the
            <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> associated with the <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.EndpointRoutingApplicationBuilderExtensions.UseEndpoints(Microsoft.AspNetCore.Builder.IApplicationBuilder,System.Action{Microsoft.AspNetCore.Routing.IEndpointRouteBuilder})">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Routing.EndpointMiddleware"/> middleware to the specified <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>
            with the <see cref="T:Microsoft.AspNetCore.Routing.EndpointDataSource"/> instances built from configured <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/>.
            The <see cref="T:Microsoft.AspNetCore.Routing.EndpointMiddleware"/> will execute the <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> associated with the current
            request.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> to add the middleware to.</param>
            <param name="configure">An <see cref="T:System.Action`1"/> to configure the provided <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/>.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
            <remarks>
            <para>
            A call to <see cref="M:Microsoft.AspNetCore.Builder.EndpointRoutingApplicationBuilderExtensions.UseEndpoints(Microsoft.AspNetCore.Builder.IApplicationBuilder,System.Action{Microsoft.AspNetCore.Routing.IEndpointRouteBuilder})"/> must be preceded by a call to
            <see cref="M:Microsoft.AspNetCore.Builder.EndpointRoutingApplicationBuilderExtensions.UseRouting(Microsoft.AspNetCore.Builder.IApplicationBuilder)"/> for the same <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>
            instance.
            </para>
            <para>
            The <see cref="T:Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware"/> defines a point in the middleware pipeline where routing decisions are
            made, and an <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> is associated with the <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>. The <see cref="T:Microsoft.AspNetCore.Routing.EndpointMiddleware"/>
            defines a point in the middleware pipeline where the current <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> is executed. Middleware between
            the <see cref="T:Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware"/> and <see cref="T:Microsoft.AspNetCore.Routing.EndpointMiddleware"/> may observe or change the
            <see cref="T:Microsoft.AspNetCore.Http.Endpoint"/> associated with the <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.FallbackEndpointRouteBuilderExtensions">
            <summary>
            Contains extension methods for <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Builder.FallbackEndpointRouteBuilderExtensions.DefaultPattern">
            <summary>
            The default route pattern used by fallback routing. <c>{*path:nonfile}</c>
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.FallbackEndpointRouteBuilderExtensions.MapFallback(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a specialized <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> that will match
            requests for non-file-names with the lowest possible priority.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add the route to.</param>
            <param name="requestDelegate">The delegate executed when the endpoint is matched.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/> that can be used to further customize the endpoint.</returns>
            <remarks>
            <para>
            <see cref="M:Microsoft.AspNetCore.Builder.FallbackEndpointRouteBuilderExtensions.MapFallback(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,Microsoft.AspNetCore.Http.RequestDelegate)"/> is intended to handle cases where URL path of
            the request does not contain a file name, and no other endpoint has matched. This is convenient for routing
            requests for dynamic content to a SPA framework, while also allowing requests for non-existent files to
            result in an HTTP 404.
            </para>
            <para>
            <see cref="M:Microsoft.AspNetCore.Builder.FallbackEndpointRouteBuilderExtensions.MapFallback(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,Microsoft.AspNetCore.Http.RequestDelegate)"/> registers an endpoint using the pattern
            <c>{*path:nonfile}</c>. The order of the registered endpoint will be <c>int.MaxValue</c>.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.FallbackEndpointRouteBuilderExtensions.MapFallback(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a specialized <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> that will match
            the provided pattern with the lowest possible priority.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add the route to.</param>
            <param name="pattern">The route pattern.</param>
            <param name="requestDelegate">The delegate executed when the endpoint is matched.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/> that can be used to further customize the endpoint.</returns>
            <remarks>
            <para>
            <see cref="M:Microsoft.AspNetCore.Builder.FallbackEndpointRouteBuilderExtensions.MapFallback(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,Microsoft.AspNetCore.Http.RequestDelegate)"/> is intended to handle cases where no
            other endpoint has matched. This is convenient for routing requests to a SPA framework.
            </para>
            <para>
            The order of the registered endpoint will be <c>int.MaxValue</c>.
            </para>
            <para>
            This overload will use the provided <paramref name="pattern"/> verbatim. Use the <c>:nonfile</c> route contraint
            to exclude requests for static files.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.RoutingBuilderExtensions">
            <summary>
            Extension methods for adding the <see cref="T:Microsoft.AspNetCore.Builder.RouterMiddleware"/> middleware to an <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.RoutingBuilderExtensions.UseRouter(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Routing.IRouter)">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Builder.RouterMiddleware"/> middleware to the specified <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> with the specified <see cref="T:Microsoft.AspNetCore.Routing.IRouter"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> to add the middleware to.</param>
            <param name="router">The <see cref="T:Microsoft.AspNetCore.Routing.IRouter"/> to use for routing requests.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.RoutingBuilderExtensions.UseRouter(Microsoft.AspNetCore.Builder.IApplicationBuilder,System.Action{Microsoft.AspNetCore.Routing.IRouteBuilder})">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Builder.RouterMiddleware"/> middleware to the specified <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>
            with the <see cref="T:Microsoft.AspNetCore.Routing.IRouter"/> built from configured <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> to add the middleware to.</param>
            <param name="action">An <see cref="T:System.Action`1"/> to configure the provided <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/>.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.RoutingEndpointConventionBuilderExtensions">
            <summary>
            Extension methods for adding routing metadata to endpoint instances using <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.RoutingEndpointConventionBuilderExtensions.RequireHost``1(``0,System.String[])">
            <summary>
            Requires that endpoints match one of the specified hosts during routing.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/> to add the metadata to.</param>
            <param name="hosts">
            The hosts used during routing.
            Hosts should be Unicode rather than punycode, and may have a port.
            An empty collection means any host will be accepted.
            </param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.RoutingEndpointConventionBuilderExtensions.WithDisplayName``1(``0,System.String)">
            <summary>
            Sets the <see cref="P:Microsoft.AspNetCore.Builder.EndpointBuilder.DisplayName"/> to the provided <paramref name="displayName"/> for all
            builders created by <paramref name="builder"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/>.</param>
            <param name="displayName">The display name.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.RoutingEndpointConventionBuilderExtensions.WithDisplayName``1(``0,System.Func{Microsoft.AspNetCore.Builder.EndpointBuilder,System.String})">
            <summary>
            Sets the <see cref="P:Microsoft.AspNetCore.Builder.EndpointBuilder.DisplayName"/> using the provided <paramref name="func"/> for all
            builders created by <paramref name="builder"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/>.</param>
            <param name="func">A delegate that produces the display name for each <see cref="T:Microsoft.AspNetCore.Builder.EndpointBuilder"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.RoutingEndpointConventionBuilderExtensions.WithMetadata``1(``0,System.Object[])">
            <summary>
            Adds the provided metadata <paramref name="items"/> to <see cref="P:Microsoft.AspNetCore.Builder.EndpointBuilder.Metadata"/> for all builders
            produced by <paramref name="builder"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/>.</param>
            <param name="items">A collection of metadata items.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.MapRouteRouteBuilderExtensions">
            <summary>
            Provides extension methods for <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> to add routes.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.MapRouteRouteBuilderExtensions.MapRoute(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.String)">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> with the specified name and template.
            </summary>
            <param name="routeBuilder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> to add the route to.</param>
            <param name="name">The name of the route.</param>
            <param name="template">The URL pattern of the route.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.MapRouteRouteBuilderExtensions.MapRoute(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.String,System.Object)">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> with the specified name, template, and default values.
            </summary>
            <param name="routeBuilder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> to add the route to.</param>
            <param name="name">The name of the route.</param>
            <param name="template">The URL pattern of the route.</param>
            <param name="defaults">
            An object that contains default values for route parameters. The object's properties represent the names
            and values of the default values.
            </param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.MapRouteRouteBuilderExtensions.MapRoute(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.String,System.Object,System.Object)">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> with the specified name, template, default values, and
            constraints.
            </summary>
            <param name="routeBuilder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> to add the route to.</param>
            <param name="name">The name of the route.</param>
            <param name="template">The URL pattern of the route.</param>
            <param name="defaults">
            An object that contains default values for route parameters. The object's properties represent the names
            and values of the default values.
            </param>
            <param name="constraints">
            An object that contains constraints for the route. The object's properties represent the names and values
            of the constraints.
            </param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.MapRouteRouteBuilderExtensions.MapRoute(Microsoft.AspNetCore.Routing.IRouteBuilder,System.String,System.String,System.Object,System.Object,System.Object)">
            <summary>
            Adds a route to the <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> with the specified name, template, default values, and
            data tokens.
            </summary>
            <param name="routeBuilder">The <see cref="T:Microsoft.AspNetCore.Routing.IRouteBuilder"/> to add the route to.</param>
            <param name="name">The name of the route.</param>
            <param name="template">The URL pattern of the route.</param>
            <param name="defaults">
            An object that contains default values for route parameters. The object's properties represent the names
            and values of the default values.
            </param>
            <param name="constraints">
            An object that contains constraints for the route. The object's properties represent the names and values
            of the constraints.
            </param>
            <param name="dataTokens">
            An object that contains data tokens for the route. The object's properties represent the names and values
            of the data tokens.
            </param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
    </members>
</doc>
