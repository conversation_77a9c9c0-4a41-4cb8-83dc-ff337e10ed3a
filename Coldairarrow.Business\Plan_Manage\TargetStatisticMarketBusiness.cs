﻿using Coldairarrow.Entity.Plan_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Util.DataAccess;
using System;

namespace Coldairarrow.Business.Plan_Manage
{
    public class TargetStatisticMarketBusiness : BaseBusiness<TargetStatisticMarket>, ITargetStatisticMarketBusiness, ITransientDependency
    {
        public TargetStatisticMarketBusiness(IERPDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<TargetStatisticMarket>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<TargetStatisticMarket>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<TargetStatisticMarket, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<TargetStatisticMarket> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(TargetStatisticMarket data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(TargetStatisticMarket data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<TargetStatisticMarket>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<TargetStatisticMarket, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<A01_TargetStatisticMarket, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 运营驾驶舱数据
        public async Task<TargetStatisticMarketDTO> GetTargetStatisticMarket(DateTime? date)
        {
            date = date ?? DateTime.Now;
            var dateStr = date.Value.ToString("yyyy-MM-dd");
            TargetStatisticMarketDTO targetStatistic = new TargetStatisticMarketDTO();
            //获取各项目得数据
            targetStatistic.projectDatas = await this.Db.GetListBySqlAsync<TargetStatisticMarket>(@$"SELECT TOP 3 *
              FROM [dbo].[A01_TargetStatisticMarket]
            WHERE ID IN (
             SELECT * FROM ( SELECT TOP 1 ID 
              FROM [dbo].[A01_TargetStatisticMarket] 
            WHERE 
            TeamProjGUID =N'8baf9a96-1647-43cd-9bcd-067448bd10c9' 
            and CONVERT(varchar,[CreateDate],23)='{dateStr}'
            ORDER BY ID DESC) t1
              UNION ALL 
             SELECT * FROM  ( SELECT TOP 1 ID 
              FROM [dbo].[A01_TargetStatisticMarket] 
            WHERE TeamProjGUID =N'1980EA50-A241-4096-8057-18A97E605DA9'
            and CONVERT(varchar,[CreateDate],23)='{dateStr}'
            ORDER BY ID DESC) t2
                UNION ALL
              SELECT * FROM  (  SELECT TOP 1 ID 
              FROM [dbo].[A01_TargetStatisticMarket] 
            WHERE TeamProjGUID =N'DACC9453-86DA-4D09-BA92-621B0EC33967' 
            and CONVERT(varchar,[CreateDate],23)='{dateStr}'
            ORDER BY ID DESC) t3)");
            //获取全项目得数据
            var targets = await this.Db.GetListBySqlAsync<TargetStatisticMarket>(@$"
  SELECT  [CreateDate]
      ,[TeamProjGUID]
      ,[TeamProject]
      ,[签约周期]
      ,[业务周期]
      ,[回款周期]
      ,[签约回款率]
      ,[签约分期占比]
      ,[签约按揭占比]
      ,[签约公积金占比] FROM [dbo].[A01_TargetStatisticMarket]
WHERE (TeamProject is null or  TeamProject ='')
and CreateDate=(select MAX(CreateDate) from [dbo].[A01_TargetStatisticMarket] 
WHERE 1=1
and CONVERT(varchar,[CreateDate],23)='{dateStr}' and (
TeamProject is null or  TeamProject =''))");
            targetStatistic.allProjectData = targets.FirstOrDefault();
            return targetStatistic;
        }
        #endregion


        #region 私有成员

        #endregion
    }
}