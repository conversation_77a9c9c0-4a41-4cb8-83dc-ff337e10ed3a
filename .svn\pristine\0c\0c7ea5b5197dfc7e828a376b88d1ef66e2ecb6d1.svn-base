﻿using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Org.BouncyCastle.Crypto;
using System;
using static Coldairarrow.Entity.Shop_Manage.Model.AliJDResultModel;
using System.Drawing;
using Coldairarrow.Util.Helper;
using Coldairarrow.Util.UEditor;
using Coldairarrow.Entity.Enum;
using static Coldairarrow.Entity.Enum.HWEnum;
using Microsoft.Extensions.Logging;
using NodaTime;
using static Coldairarrow.Util.Helper.WechatJsonHelper;
using System.Text;
using Dynamitey.DynamicObjects;
using Coldairarrow.Util.DataAccess;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity;
using Newtonsoft.Json;
using Microsoft.Extensions.Primitives;
using NetTopologySuite.Geometries;
using System.Net;
using Microsoft.Extensions.DependencyInjection;
using static Microsoft.AspNetCore.Hosting.Internal.HostingApplication;
using System.Threading;
using System.Collections.Concurrent;
using System.Threading.Channels;

namespace Coldairarrow.Business.Base_Manage
{
    public class Base_IpLockBusiness : BaseBusiness<Base_IpLock>, IBase_IpLockBusiness, ITransientDependency
    {
        public IHWDbAccessor _db { get; set; }
        public IDbAccessor _basedb { get; set; }
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private static readonly ConcurrentDictionary<string, DateTime> _processingIps = new ConcurrentDictionary<string, DateTime>();
        private static readonly ConcurrentDictionary<string, string> _activeTokens = new ConcurrentDictionary<string, string>();
        private static readonly ChannelReader<string> _tokenReleaseChannel = Channel.CreateUnbounded<string>().Reader;
        public Base_IpLockBusiness(IHWDbAccessor db, IDbAccessor basedb, IServiceScopeFactory serviceScopeFactory)
            : base(db)
        {
            _db = db;
            _basedb = basedb;
            _serviceScopeFactory = serviceScopeFactory;
        }
        #region 外部接口

        public async Task<PageResult<Base_IpLock>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Base_IpLock>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Base_IpLock, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Base_IpLock> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        /// <summary>
        /// 获取恶意ip
        /// </summary>
        /// <returns></returns>
        public async Task GetMaliciousIpAsync()
        {
            //获取恶意ip接口
            string str = @"with  
HRLOG as (
SELECT TOP 1000
	'HR系统' as type,
    dt AS DateTimeGroup, 
    ip,
    COUNT(1) AS RecordCount
FROM (
    SELECT 
        CONVERT(varchar(16), OpTime, 121) AS dt, 
        ip,url
    FROM HRSystem.[dbo].[Base_HistoryLogRecord]
    WHERE OpTime >= DATEADD(minute, -30, GETDATE())-1 AND OpTime <= GETDATE()  
and Url <>'/Base_Manage/Base_HWIpDetail/SaveData' and Url <>'/Base_Manage/Base_IpLock/GetMaliciousIp'
and Url <>'/WorkWx_Robot/WorkWx_Message/SendOPMMessage' and Url <>'/Base_Manage/Base_HWIpDetail/SaveDataByExcel' and url <>'/Base_Manage/Base_HWLogDetail/SaveDataByNum'
) AS DerivedTable
GROUP BY dt, ip
having COUNT(1)>100
ORDER BY dt, ip
),
 PayLog as (
SELECT TOP 1000
	'支付系统' as type,
    dt AS DateTimeGroup, 
    ip,
    COUNT(1) AS RecordCount
FROM (
    SELECT 
        CONVERT(varchar(16), OpTime, 121) AS dt, 
        ip,url
    FROM PayDB.[dbo].sys_log_op
    WHERE OpTime >= DATEADD(minute, -30, GETDATE())-1 AND OpTime <= GETDATE()
	and Url <>'/PaySystem/GetPay'
) AS DerivedTable
GROUP BY dt, ip
having COUNT(1)>100
ORDER BY dt, ip
),
 PlanLog as (
SELECT TOP 1000
	'计划系统' as type,
    dt AS DateTimeGroup, 
    ip,
    COUNT(1) AS RecordCount
FROM (
    SELECT 
        CONVERT(varchar(16), AccessDate, 121) AS dt, 
        LoginIP ip,URL
    FROM PlanSystemDB.[dbo].UserHistoryRecord
    WHERE AccessDate >= DATEADD(minute, -30, GETDATE())-1 AND AccessDate <= GETDATE()
and [URL] not like '%/WeChatAPP/GetIpModel%'
) AS DerivedTable
GROUP BY dt, ip
having COUNT(1)>100
ORDER BY dt, ip
),
ERPLog1 as (
SELECT TOP 1000
	'ERP系统' as type,
    dt AS DateTimeGroup, 
    ip,
    COUNT(1) AS RecordCount
FROM (
    SELECT 
        CONVERT(varchar(16), AccessDate, 121) AS dt, 
        LoginIP ip,URL
    FROM [Mysoft_ERP25].[dbo].UserHistoryRecord
    WHERE 

	AccessDate >= DATEADD(minute, -30, GETDATE())-1 AND AccessDate <= GETDATE()
) AS DerivedTable
GROUP BY dt, ip
having COUNT(1)>250
ORDER BY dt, ip
),
ERPLog2 as (
SELECT TOP 1000
	'ERP系统' as type,
    dt AS DateTimeGroup, 
    ip,
    COUNT(1) AS RecordCount
FROM (
    SELECT 
        CONVERT(varchar(16), AccessDate, 121) AS dt, 
        LoginIP ip,URL
    FROM [Mysoft_ERP25].[dbo].UserWeixinHistoryRecord
    WHERE AccessDate >= DATEADD(minute, -30, GETDATE())-1 AND AccessDate <= GETDATE()
) AS DerivedTable
GROUP BY dt, ip
having COUNT(1)>250
ORDER BY dt, ip
),
--费用系统访问日志
CmsLog as(
(
SELECT TOP 1000
	'费用系统' as type,
    dt AS DateTimeGroup, 
    ip,
    COUNT(1) AS RecordCount
FROM (
    SELECT 
        CONVERT(varchar(16), op.op_time, 121) AS dt, 
        op.ip ip,URL
    from OPMDB.[dbo].sys_op_log op
    WHERE op.op_time >= DATEADD(minute, -30, GETDATE())-1 AND op.op_time <= GETDATE()
) AS DerivedTable
GROUP BY dt, ip
having COUNT(1)>100
ORDER BY dt, ip
)
union all 
(
SELECT TOP 1000
	'客服系统' as type,
    dt AS DateTimeGroup, 
    ip,
    COUNT(1) AS RecordCount
FROM (
    SELECT 
        CONVERT(varchar(16), op.opTime, 121) AS dt, 
        op.ip ip,URL
     from KFcrm.dbo.crm_op_log op
    WHERE op.opTime >= DATEADD(minute, -30, GETDATE())-1 AND op.opTime <= GETDATE()
) AS DerivedTable
GROUP BY dt, ip
having COUNT(1)>100
ORDER BY dt, ip
)
),
lockips as (
SELECT 
      [F_IP] IP
  FROM [HRSystem].[dbo].[Base_IpLock]
)
select * from (
select  ip,type,
 ROW_NUMBER() OVER(PARTITION BY IP ORDER BY Type ASC) AS RowNum from (
select
*
from HRLOG
union all select * from PayLog
union all select * from PlanLog
union all select * from ERPLog1
union all select * from ERPLog2
union all select * from CmsLog
) dat
where 1=1 and   ( not exists (select ip from lockips b where b.IP=dat.ip)
and   not exists (select F_Ip from Base_HWWhiteIp b where b.F_Ip=dat.ip))
) dd
where 1=1
and RowNum = 1
            ";
            var ips = await this._basedb.GetListBySqlAsync<ipDto>(str);

            if (ips.Any())
            {
                var ipList = this.GetIQueryable();

                // 创建新记录
                var ipLocks = ips.Where(ip => !ipList.Any(x => x.F_IP == ip.ip))
                                 .Select(ip => new Base_IpLock
                                 {
                                     F_Id = IdHelper.GetId(),
                                     F_CreateDate = DateTime.Now,
                                     F_IP = ip.ip,
                                     F_IsLock = 0,
                                     F_BusState = HWStatusEnum.生效,
                                     F_LockNum = 1,
                                     F_LockTime = null,
                                     F_Type = HWTypeEnum.系统日志,
                                     F_System = ip.type + "强爆破攻击"
                                 }).ToList();

                // 更新已存在的记录
                var updateIpLocks = ips.Where(ip => ipList.Any(x => x.F_BusState == HWStatusEnum.未生效 && x.F_IP == ip.ip))
                                       .Select(ip =>
                                       {
                                           var existingIp = ipList.FirstOrDefault(x => x.F_BusState == HWStatusEnum.未生效 && x.F_IP == ip.ip);
                                           return new Base_IpLock
                                           {
                                               F_Id = existingIp.F_Id,
                                               F_CreateDate = existingIp.F_CreateDate,
                                               F_ModifyDate = DateTime.Now,
                                               F_IP = ip.ip,
                                               F_IsLock = 0,
                                               F_BusState = HWStatusEnum.生效,
                                               F_LockNum = existingIp.F_LockNum + 1,
                                               F_LockTime = null,
                                               F_Type = HWTypeEnum.系统日志,
                                               F_System = ip.type + "强爆破攻击"
                                           };
                                       }).ToList();

                // 合并所有记录
                var allIpLocks = ipLocks.Concat(updateIpLocks).ToList();

                // 如果有新的或更新的记录，则批量插入或更新
                if (allIpLocks.Any())
                {
                    // 批量插入新记录
                    await this.Db.InsertAsync(ipLocks).ConfigureAwait(false);

                    // 批量更新现有记录
                    await this.Db.UpdateAsync(updateIpLocks).ConfigureAwait(false);
                }

                // 创建日志记录
                var base_IpLockRecords = allIpLocks.Select(ipLock => new Base_IpLockRecord
                {
                    F_Id = IdHelper.GetId(),
                    F_CreateDate = DateTime.Now,
                    F_IP = ipLock.F_IP,
                    F_Remark = $"{ipLock.F_System}黑名单封禁",
                    F_Type = HWLogRecordTypeEnum.封禁,
                    F_LockNum = ipLock.F_LockNum,
                    F_LockTime = DateTime.Now
                }).ToList();

                // 批量插入日志记录
                if (base_IpLockRecords.Any())
                {
                    await this.Db.InsertAsync(base_IpLockRecords).ConfigureAwait(false);
                }
            }
        }
        /// <summary>
        /// 更新深信服Ip组
        /// </summary>
        /// <returns></returns>
        public async Task UpdateSXFIpGroups()
        {
            //获取登陆Token
            var (token, errorMessage) = await GetTokenAsync("**************");
            await UpdateSXFIpGroups("", token);
        }
        #region 深信服封禁ip方法
        /// <summary>
        /// IP 与名称的映射
        /// </summary>
        private static readonly Dictionary<string, string> ipNameMap = new Dictionary<string, string>
        {
            { "**************", "地产-光  里" },
            { "**************", "商业-长嘉汇" },
            { "************", "商业-光 花" }
        };
        /// <summary>
        /// 封禁恶意ip
        /// </summary>
        /// <returns></returns>
        public async Task BlockAllIpAsync()
        {
            // 查询所有未封禁的 IP
            var base_Ips = await this.Db.GetIQueryable<Base_IpLock>()
                .Where(x => x.F_IsLock == 0)
                .Select(x => new { x.F_IP, x.F_System })
                .Take(5)
                .ToListAsync();

            if (!base_Ips.Any())
            {
                return;
            }

            var tokensToLogout = new List<(string targetIp, string token)>();

            foreach (var item in base_Ips)
            {
                // 创建一个新的作用域
                using (var scope = _serviceScopeFactory.CreateScope())
                {
                    var dbContext = scope.ServiceProvider.GetRequiredService<IHWDbAccessor>(); // 替换为你的 DbContext 类型
                    var ipLockBusiness = scope.ServiceProvider.GetRequiredService<Base_IpLockBusiness>();

                    // 调用 BlockIpAsync 并传递 dbContext
                    var (success, token) = await ipLockBusiness.BlockIpAsync(item.F_IP, item.F_System, "BLACK", dbContext);
                    if (success && !string.IsNullOrEmpty(token))
                    {
                        tokensToLogout.Add((item.F_IP, token));
                    }
                }

                // 每个 IP 封禁操作之间延迟 30 秒
                await Task.Delay(TimeSpan.FromSeconds(5));
            }

            // 统一注销 Token
            foreach (var (targetIp, token) in tokensToLogout)
            {
                await logout(targetIp, token);
            }
        }
        /// <summary>
        /// 封禁单个 IP
        /// </summary>
        /// <param name="url">IP 地址</param>
        /// <param name="description">描述</param>
        /// <param name="type">类型</param>
        /// <returns></returns>
        public async Task<(bool success, string token)> BlockIpAsync(string url, string description, string type, IHWDbAccessor dbAccessor)
        {
            var targetIps = new List<string> { "**************", "**************", "************" };
            var results = new Dictionary<string, string>();
            var base_IpLock = new Base_IpLock { F_IP = url };

            var tasks = targetIps.Select(async targetIp =>
            {
                try
                {
                    var (success, errorMessage, F_Ctity) = await BlockIpForTargetAsync(targetIp, url, description, type);
                    var targetName = ipNameMap.ContainsKey(targetIp) ? ipNameMap[targetIp] : "深信服";
                    base_IpLock.F_Ctity = F_Ctity;
                    results[targetIp] = success
                        ? $"{targetName}:<font color=\"green\"> 封禁成功</font>"
                        : $"{targetName}:<font color=\"red\"> 封禁失败，原因：{errorMessage}</font>";

                    UpdateBaseIpLock(base_IpLock, targetIp, success, results[targetIp]);
                }
                catch (Exception ex)
                {
                    results[targetIp] = $"{targetIp}: 封禁失败，原因：{ex.Message}";
                    UpdateBaseIpLock(base_IpLock, targetIp, false, results[targetIp]);
                }
            });

            await Task.WhenAll(tasks);

            // 保存 Base_IpLock 对象到数据库并推送企微
            await UpdateMaliciousIpState(base_IpLock, dbAccessor);

            // 返回成功状态和 Token（如果有）
            var token = await GetValidTokenAsync(targetIps.First());
            return (results.All(x => x.Value.Contains("成功")), token);
        }
        /// <summary>
        /// 检查 Redis 中的 Token 是否有效，如果无效则重新获取。
        /// </summary>
        /// <param name="targetIp"></param>
        /// <returns></returns>
        private async Task<string> GetValidTokenAsync(string targetIp)
        {
            // 从 Redis 中获取 Token
            var token = await RedisHelper.GetAsync<string>($"token:{targetIp}");

            // 如果 Token 不存在或已失效，重新获取
            if (string.IsNullOrEmpty(token))
            {
                var (newToken, errorMessage) = await GetTokenAsync(targetIp);
                if (!string.IsNullOrEmpty(newToken))
                {
                    token = newToken;
                }
            }

            return token;
        }
        /// <summary>
        /// 更新 Base_IpLock 对象
        /// </summary>
        /// <param name="baseIpLock">Base_IpLock 对象</param>
        /// <param name="targetIp">目标 IP</param>
        /// <param name="success">是否成功</param>
        /// <param name="resultMessage">结果信息</param>
        private void UpdateBaseIpLock(Base_IpLock baseIpLock, string targetIp, bool success, string resultMessage)
        {
            switch (targetIp)
            {
                case "**************":
                    baseIpLock.BlockState1 = success;
                    baseIpLock.BlockStr1 = resultMessage;
                    break;
                case "**************":
                    baseIpLock.BlockState2 = success;
                    baseIpLock.BlockStr2 = resultMessage;
                    break;
                case "************":
                    baseIpLock.BlockState3 = success;
                    baseIpLock.BlockStr3 = resultMessage;
                    break;
                default:
                    break;
            }
        }


        /// <summary>
        /// 针对单个目标 IP 执行封禁操作
        /// </summary>
        /// <param name="targetIp">目标 IP</param>
        /// <param name="url">需要封禁的 URL</param>
        /// <param name="description">封禁原因描述</param>
        /// <param name="type">封禁类型（如 BLACK）</param>
        /// <returns>是否成功及错误信息</returns>
        private async Task<(bool success, string errorMessage, string F_Ctity)> BlockIpForTargetAsync(string targetIp, string url, string description, string type)
        {
            // 获取 Token
            var (token, errorMessage) = await GetTokenAsync(targetIp);

            // 检查是否有错误信息
            if (!string.IsNullOrEmpty(errorMessage))
            {
                throw new Exception($"获取 Token 失败: {errorMessage}");
            }

            // 使用 token 调用 GetLocationAsync
            var F_Ctity = await GetLocationAsync(url, token);
            if (string.IsNullOrEmpty(token))
            {
                return (false, errorMessage, F_Ctity); // 返回错误信息
            }
            // 添加黑名单
            var (success, blacklistErrorMessage) = await AddToBlacklistAsync(targetIp, token, url, description, type);
            return (success, blacklistErrorMessage, F_Ctity);
        }
        private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(30); // 设置缓存过期时间
        /// <summary>
        /// 获取 Token
        /// </summary>
        /// <param name="targetIp">目标 IP</param>
        /// <returns>Token 及错误信息</returns>
        private async Task<(string token, string errorMessage)> GetTokenAsync(string targetIp)
        {
            // 先从 Redis 中获取 Token
            var cachedToken = await RedisHelper.GetAsync<string>($"token:{targetIp}");

            if (!string.IsNullOrEmpty(cachedToken))
            {
                //让token存活
                //await keepalive(targetIp, cachedToken);
                return (cachedToken, null); // 如果缓存中存在 Token，直接返回
            }

            // 如果缓存中没有 Token，则调用 API 获取
            var getTokenUrl = $"https://{targetIp}/api/v1/namespaces/@namespace/login";
            var parameters = new Dictionary<string, object>
        {
            { "name", "apiaddmin" },
            { "password", "ZD@cq0509" }
        };

            var response = HttpHelper.PostData(getTokenUrl, parameters, null, ContentType.Json);
            var retObj = JsonConvert.DeserializeObject<SXLoginResponse>(response);

            if (retObj != null && retObj.code == "0")
            {
                var token = retObj.data.loginResult.token;

                // 将 Token 缓存到 Redis 中
                await RedisHelper.SetAsync($"token:{targetIp}", token, (int)_cacheExpiration.TotalSeconds);

                return (token, null); // 成功，返回 Token
            }
            else
            {
                // 返回错误信息
                return (null, retObj?.message ?? "获取 Token 失败");
            }
        }
        /// <summary>
        /// Token 保活
        /// </summary>
        /// <param name="targetIp"></param>
        /// <param name="cachedToken"></param>
        /// <returns></returns>
        private async Task UpdateSXFIpGroups(string ip, string cachedToken)
        {
            // 先从 Redis 中获取 Token
            var blackUrl = $"https://**************/api/v1/namespaces/pubilc/ipgroups/允许访问组";
            var blackHeaders = new Dictionary<string, string>
            {
                { "Cookie", $"token={cachedToken}" },
                { "Accept", $"*/*" },
                { "Accept-Encoding", $"gzip, deflate, br" },
                { "Connection", $"keep-alive" }
            };
            var response = HttpHelper.GetData(blackUrl, null, blackHeaders);
            Console.WriteLine(response);
        }
        /// <summary>
        /// Token 保活
        /// </summary>
        /// <param name="targetIp"></param>
        /// <param name="cachedToken"></param>
        /// <returns></returns>
        private async Task keepalive(string targetIp, string cachedToken)
        {
            // 先从 Redis 中获取 Token


            var blackUrl = $"https://{targetIp}/api/v1/namespaces/public/keepalive";


            var blackHeaders = new Dictionary<string, string>
        {
            { "Cookie", $"token={cachedToken}" },
                    { "User-Agent", $"Apifox/1.0.0 (https://www.apifox.cn)" },
                    { "Accept", $"*/*" },
                    { "Accept-Encoding", $"gzip, deflate, br" },
                    { "Connection", $"keep-alive" }
        };
            var response = HttpHelper.GetData(blackUrl, null, blackHeaders);
            Console.WriteLine(response);
        }
        /// <summary>
        /// 获取 地区
        /// </summary>
        /// <param name="targetIp">目标 IP</param>
        /// <returns>Token 及错误信息</returns>
        private async Task<string> GetLocationAsync(string IP, string ttoken)
        {
            var targetIp = "**************";
            var cachedToken = ttoken;
            if (string.IsNullOrWhiteSpace(ttoken))
            {
                // 先从 Redis 中获取 Token
                cachedToken = await RedisHelper.GetAsync<string>($"token:{targetIp}");

                if (string.IsNullOrEmpty(cachedToken))
                {
                    var (token, errorMessage) = await GetTokenAsync(targetIp);
                    if (!string.IsNullOrWhiteSpace(token))
                    {
                        cachedToken = token;
                    }
                    else
                    {
                        return "";
                    }
                }
            }


            var blackUrl = $"https://{targetIp}/api/v1/namespaces/public/iplocation?_ip={IP}";


            var blackHeaders = new Dictionary<string, string>
            {
                { "Cookie", $"token={cachedToken}" },
                { "User-Agent", $"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/106.0.1370.47" },
                { "Accept", $"Accept" },
                { "Accept-Encoding", $"gzip, deflate, br" },
                { "Accept-Language", $"zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6" }
            };

            var response = HttpHelper.GetData(blackUrl, null, blackHeaders);
            try
            {
                var blockRetObj = JsonConvert.DeserializeObject<SXLocationResponse>(response);

                if (blockRetObj != null && (blockRetObj.code == 0))
                {
                    return blockRetObj.data.location; // 成功
                }
                else
                {
                    // 返回错误信息
                    return "";
                }
            }
            catch (Exception ex)
            {
                return "";
            }
        }
        /// <summary>
        /// 添加黑名单
        /// </summary>
        /// <param name="targetIp">目标 IP</param>
        /// <param name="token">Token</param>
        /// <param name="url">需要封禁的 URL</param>
        /// <param name="description">封禁原因描述</param>
        /// <param name="type">封禁类型（如 BLACK）</param>
        /// <returns>是否成功及错误信息</returns>
        private async Task<(bool success, string errorMessage)> AddToBlacklistAsync(string targetIp, string token, string url, string description, string type)
        {
            var blackUrl = $"https://{targetIp}/api/v1/namespaces/public/whiteblacklist";
            var blackParameters = new Dictionary<string, object>
        {
            { "type", type }, // 使用传入的 type
            { "enable", true },
            { "url", url }, // 使用传入的 url
            { "description", description } // 使用传入的 description
        };

            var blackHeaders = new Dictionary<string, string>
        {
            { "Cookie", $"token={token}" }
        };

            var response = HttpHelper.PostData(blackUrl, blackParameters, blackHeaders, ContentType.Json);
            var blockRetObj = JsonConvert.DeserializeObject<BlockResponse>(response);

            if (blockRetObj != null && (blockRetObj.code == 0 || blockRetObj.code == 17))
            {
                //删除缓存
                await RedisHelper.DelAsync($"token:{targetIp}");
                await logout(targetIp, token);
                //注销账号
                return (true, null); // 成功
            }
            else
            {
                if (blockRetObj?.message == "token is invalid")
                {
                    //重新刷新token
                    await RedisHelper.DelAsync($"token:{targetIp}");
                    await GetTokenAsync(targetIp);

                    await AddToBlacklistAsync(targetIp, token, url, description, type);
                }
                else
                {
                    //删除缓存
                    await RedisHelper.DelAsync($"token:{targetIp}");
                    await logout(targetIp, token);
                }
                // 返回错误信息
                return (false, blockRetObj?.message ?? "未知错误");
            }
        }
        /// <summary>
        /// 注销账号
        /// </summary>
        /// <param name="targetIp"></param>
        /// <param name="cachedToken"></param>
        /// <returns></returns>
        private async Task logout(string targetIp, string cachedToken)
        {
            // 先从 Redis 中获取 Token


            var blackUrl = $"https://{targetIp}/api/v1/namespaces/public/logout";


            var blackHeaders = new Dictionary<string, string>
            {
                { "Cookie", $"token={cachedToken}" },
            };
            var json = $@"
            {{
                ""loginResult"": {{
                    ""token"": ""{cachedToken}""
                }}
            }}";

            // 将 JSON 字符串转换为 Dictionary<string, object>
            var dictionary = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);
            var response = HttpHelper.PostData(blackUrl, dictionary, blackHeaders, ContentType.Json);
            Console.WriteLine(response);
        }
        #endregion
        /// <summary>
        /// 新增恶意ip
        /// </summary>
        /// <returns></returns>
        public async Task AddMaliciousIpAsync(Base_IpLock input)
        {
            if (input != null && !string.IsNullOrEmpty(input.F_IP))
            {
                // 获取现有的 IP 列表和白名单 IP 列表
                var baseIps = await this.GetIQueryable().ToListAsync().ConfigureAwait(false);
                var hWWhiteIps = await this.Db.GetIQueryable<Base_HWWhiteIp>().ToListAsync().ConfigureAwait(false);

                // 检查是否已经存在
                var existsInBaseIps = baseIps.Any(item => item.F_IP == input.F_IP);
                var existsInWhiteIps = hWWhiteIps.Any(hw => hw.F_Ip == input.F_IP);

                if (!existsInBaseIps && !existsInWhiteIps)
                {
                    // 新增记录
                    input.F_Id = IdHelper.GetId();
                    input.F_CreateDate = DateTime.Now; // 使用 UTC 时间
                    input.F_IsLock = 0;
                    input.F_BusState = HWStatusEnum.生效;
                    input.F_LockNum = 1;
                    input.F_LockTime = null;

                    await InsertAsync(input);

                    // 创建日志记录
                    var base_IpLockRecord = new Base_IpLockRecord
                    {
                        F_Id = IdHelper.GetId(),
                        F_CreateDate = DateTime.Now,
                        F_IP = input.F_IP,
                        F_Remark = input.F_System ?? "黑名单封禁",
                        F_Type = HWLogRecordTypeEnum.封禁,
                        F_LockNum = input.F_LockNum,
                        F_LockTime = DateTime.Now,
                    };

                    await this.Db.InsertAsync(base_IpLockRecord);
                }
                else
                {
                    // 查找未生效的记录
                    var base_IpLock = baseIps.FirstOrDefault(x => x.F_IP == input.F_IP && x.F_BusState == HWStatusEnum.未生效);

                    if (base_IpLock != null)
                    {
                        // 更新记录
                        base_IpLock.F_ModifyDate = DateTime.Now;
                        base_IpLock.F_BusState = HWStatusEnum.生效;
                        base_IpLock.F_LockNum++;

                        await this.Db.UpdateAsync(base_IpLock);

                        // 创建日志记录
                        var base_IpLockRecord = new Base_IpLockRecord
                        {
                            F_Id = IdHelper.GetId(),
                            F_CreateDate = DateTime.Now,
                            F_IP = base_IpLock.F_IP,
                            F_Remark = input.F_System ?? "黑名单封禁",
                            F_Type = HWLogRecordTypeEnum.封禁,
                            F_LockNum = base_IpLock.F_LockNum,
                            F_LockTime = DateTime.Now,
                        };

                        await this.Db.InsertAsync(base_IpLockRecord);
                    }
                }
            }
        }

        /// <summary>
        /// 判断是否是恶意ip
        /// </summary>
        /// <param name="ip"></param>
        /// <returns></returns>
        public async Task<dynamic> IsMaliciousIp(string ip)
        {
            return await this.GetIQueryable().AnyAsync(x => x.F_IP == ip);
        }
        /// <summary>
        /// 获取恶意ip的信息
        /// </summary>
        /// <param name="ip"></param>
        /// <returns></returns>
        public async Task<dynamic> GetMaliciousIpInfoAsync(string ip)
        {
            var ipLockInfoDto = new IpLockInfoDto
            {
                IsExistsIp = await this.IsMaliciousIp(ip),
                isExistsRecord = await this.Db.GetIQueryable<Base_HWIpDetail>()
                                             .AnyAsync(x => x.F_CreateDate >= DateTime.Now.AddDays(-2) && x.F_SA == ip),
                ALiYunFineSrc = $@"https://fine.cqlandmark.com/webroot/decision/view/report?viewlet=Report%252FIT%252FIp%25E8%25A1%258C%25E4%25B8%25BA%25E6%2597%25A5%25E5%25BF%2597_mobile.cpt&op=h5&ip={ip}",
                UserRecordFineSrc = $@"https://fine.cqlandmark.com/webroot/decision/view/report?viewlet=Report%252FIT%252F%25E5%2585%25A8%25E7%25B3%25BB%25E7%25BB%259F%25E8%25AE%25BF%25E9%2597%25AE%25E6%2593%258D%25E4%25BD%259C%25E6%2597%25A5%25E5%25BF%2597.cpt&ref_t=design&ref_c=e4bbbac5-a396-4a69-92c8-d92b9fee4c15&ip={ip}"
            };

            var ipAddress = await this.Db.GetIQueryable<Base_HWIpAddress>()
                                         .Select(x => new { x.F_IP, x.F_Ctity })
                                         .FirstOrDefaultAsync(x => x.F_IP == ip);

            if (ipAddress != null)
            {
                ipLockInfoDto.Ctity = ipAddress.F_Ctity;
            }
            else
            {
                var ipInfo = await IpHelper.GetCityModelByIpAsync(ip);
                if (ipInfo != null)
                {
                    ipLockInfoDto.Ctity = ipInfo.city;
                    //保存到ip库
                    await this.Db.InsertAsync(new Base_HWIpAddress
                    {
                        F_Id = Guid.NewGuid().ToString(),
                        F_IP = ip,
                        F_Ctity = ipInfo.city,
                        F_Lat = ipInfo.lat,
                        F_Lng = ipInfo.lng,
                        F_Time = DateTime.Now
                    });
                }
            }

            return ipLockInfoDto;
        }


        /// <summary>
        /// 获取阿里云防护列表（定时）
        /// </summary>
        /// <returns></returns>

        public async Task GetAliYunProtectionList()
        {
            // 获取阿里云防护列表（定时）
            var aliYunOutInputs = await AliYunHelper.DescribeRuleHitsTopClientIpAsync();

            // 如果有数据，继续处理
            if (aliYunOutInputs.Any())
            {
                // 获取现有的 IP 列表和白名单 IP 列表
                var baseIps = await this.GetIQueryable().ToListAsync().ConfigureAwait(false);
                var hWWhiteIps = await this.Db.GetIQueryable<Base_HWWhiteIp>().ToListAsync().ConfigureAwait(false);

                // 构建一个新的列表，只包含需要锁定的 IP
                var locks = aliYunOutInputs
                    .Where(ipdto =>
                        !baseIps.Any(item => item.F_IP == ipdto.ClientIp) &&
                        !hWWhiteIps.Any(hw => hw.F_Ip == ipdto.ClientIp)
                    )
                    .Select(ipdto => new Base_IpLock
                    {
                        F_Id = IdHelper.GetId(),
                        F_CreateDate = DateTime.Now, // 使用 UTC 时间
                        F_IP = ipdto.ClientIp,
                        F_IsLock = 0,
                        F_BusState = HWStatusEnum.生效,
                        F_LockNum = 1,
                        F_LockTime = null,
                        F_Type = HWTypeEnum.黑名单,
                        F_System = $"阿里云防护,攻击类型：{ipdto.RuleTypeName},攻击次数：{ipdto.Count}"
                    })
                    .ToList();

                // 构建需要更新的 IP 锁定记录
                var updateLocks = aliYunOutInputs
                    .Where(ipdto =>
                        baseIps.Any(item => item.F_BusState == HWStatusEnum.未生效 && item.F_IP == ipdto.ClientIp) &&
                        !hWWhiteIps.Any(hw => hw.F_Ip == ipdto.ClientIp)
                    )
                    .Select(ipdto =>
                    {
                        var existingIp = baseIps.FirstOrDefault(x => x.F_BusState == HWStatusEnum.未生效 && x.F_IP == ipdto.ClientIp);
                        return new Base_IpLock
                        {
                            F_Id = existingIp.F_Id,
                            F_CreateDate = existingIp.F_CreateDate,
                            F_ModifyDate = DateTime.Now,
                            F_IP = ipdto.ClientIp,
                            F_IsLock = 0,
                            F_BusState = HWStatusEnum.生效,
                            F_LockNum = existingIp.F_LockNum + 1,
                            F_LockTime = null,
                            F_Type = HWTypeEnum.系统日志,
                            F_System = "阿里云防护黑名单封禁"
                        };
                    })
                    .ToList();

                // 合并所有需要插入和更新的记录
                var allLocks = locks.Concat(updateLocks).ToList();

                // 如果有新的或需要更新的记录，则批量插入或更新
                if (allLocks.Any())
                {
                    await this.InsertOrUpdateAsync(allLocks).ConfigureAwait(false); // 假设 Db 上下文支持 InsertOrUpdateAsync 方法
                }

                // 创建日志记录
                var lockRecords = allLocks.Select(ipLock => new Base_IpLockRecord
                {
                    F_Id = IdHelper.GetId(),
                    F_CreateDate = DateTime.Now,
                    F_IP = ipLock.F_IP,
                    F_Remark = "阿里云防护黑名单封禁",
                    F_Type = HWLogRecordTypeEnum.封禁,
                    F_LockNum = ipLock.F_LockNum,
                    F_LockTime = DateTime.Now,
                }).ToList();

                // 批量插入日志记录
                if (lockRecords.Any())
                {
                    await this.Db.InsertAsync(lockRecords).ConfigureAwait(false);
                }
            }
        }
        /// <summary>
        /// 更新恶意ip状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task UpdateMaliciousIpState(Base_IpLock input, IHWDbAccessor dbContext = null)
        {
            if (string.IsNullOrWhiteSpace(input.F_IP))
            {
                throw new BusException("IP不正确");
            }
            dbContext ??= _db;

            // 合并查询 Base_IpLock 和 Base_HWIpAddress
            var base_Ip = await dbContext.GetIQueryable<Base_IpLock>()
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.F_IP == input.F_IP && x.F_IsLock == 0);

            if (base_Ip == null)
            {
                throw new BusException("未找到相关IP");
            }
            base_Ip.F_Ctity = input.F_Ctity;
            var ipAddress = await dbContext.GetIQueryable<Base_HWIpAddress>()
                .FirstOrDefaultAsync(x => x.F_IP == input.F_IP);

            // 查询 Base_HWIpDetail
            var hWIpDetails = await dbContext.GetIQueryable<Base_HWIpDetail>()
                .Where(x => x.F_SA == input.F_IP)
                .ToListAsync();

            // 更新 Base_IpLock 状态
            base_Ip.F_IsLock = 1;
            base_Ip.F_LockTime = DateTime.Now;

            if (ipAddress == null)
            {
                string city = base_Ip.F_Ctity ?? await GetLocationAsync(input.F_IP, "");
                if (!string.IsNullOrWhiteSpace(city))
                {
                    ipAddress = new Base_HWIpAddress
                    {
                        F_Id = Guid.NewGuid().ToString(),
                        F_IP = input.F_IP,
                        F_Ctity = city,
                        F_Time = DateTime.Now
                    };
                    await dbContext.InsertAsync(ipAddress);
                }
            }

            // 更新 Base_IpLock 的城市信息
            if (ipAddress != null)
            {
                base_Ip.F_Ctity = ipAddress.F_Ctity;
            }

            await dbContext.UpdateAsync(base_Ip);

            // 发送企微通知
            await SendWeChatNotification(base_Ip, input, hWIpDetails, dbContext);
        }
        /// <summary>
        /// 发送企微通知
        /// </summary>
        /// <param name="base_IpLock">IP 封禁信息</param>
        /// <param name="input">输入参数</param>
        /// <param name="hWIpDetails">IP 活动详情</param>
        /// <returns></returns>
        private async Task SendWeChatNotification(Base_IpLock base_IpLock, Base_IpLock input, List<Base_HWIpDetail> hWIpDetails, IHWDbAccessor dbContext = null)
        {
            try
            {
                var url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=519d3bca-f11b-4044-b7e8-59ed4a5058e1"; // 正式
                // 测试环境 URL
                //var url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c806f851-105a-4166-8f79-fcad40fecd1d";

                var parameters = new Dictionary<string, object>
                {
                    { "msgtype", "markdown" }
                };
                var ipStr = string.IsNullOrWhiteSpace(base_IpLock.F_Ctity)
                    ? input.F_IP
                    : $"{base_IpLock.F_IP}（来自：{base_IpLock.F_Ctity}）";

                var strBuilder = new StringBuilder();
                strBuilder.Append($"发现恶意IP：<font color=\"warning\">{ipStr}</font>\n原因：{base_IpLock.F_System}");

                // 添加 IP 活动详情
                if (hWIpDetails != null && hWIpDetails.Any())
                {
                    strBuilder.Append($"\n已进行封禁处理！\n近期活动次数:<font color=\"red\">{hWIpDetails.Count(x => x.F_CreateDate >= DateTime.Now.AddHours(-1))}</font>");
                    strBuilder.Append($"\n[点击查询活动轨迹](https://fine.cqlandmark.com:443/webroot/decision/view/report?viewlet=Report%252FIT%252FIp%25E8%25A1%258C%25E4%25B8%25BA%25E6%2597%25A5%25E5%25BF%2597_mobile.cpt&op=h5&ip={input.F_IP})");

                    if (!base_IpLock.F_System.Contains("手动上传封禁"))
                    {
                        if (base_IpLock.F_System != "阿里云规则触发")
                        {
                            var safeDetail = hWIpDetails.OrderByDescending(x => x.F_CreateDate).FirstOrDefault();
                            if (safeDetail != null)
                            {
                                var name = await GetIPBusinessAsync(safeDetail.F_TA, dbContext);
                                var ipInfo = !string.IsNullOrWhiteSpace(name) ? $"({name})" : "";
                                strBuilder.Append($"\n<font color=\"warning\">目标ip：{safeDetail.F_TA}{ipInfo}</font>");
                            }
                        }
                        else
                        {
                            var lockRuleIds = await dbContext.GetIQueryable<Base_HWAliYunRule>()
                                .Where(x => x.F_RuleLock == "拉黑")
                                .Select(x => x.F_RuleID)
                                .ToListAsync();

                            var taIpList = hWIpDetails
                                .Where(x => x.F_Region == "阿里云规则触发" && lockRuleIds.Contains(x.F_Action) && x.F_SA == base_IpLock.F_IP)
                                .Select(x => x.F_TA)
                                .Distinct()
                                .ToList();

                            if (taIpList.Any())
                            {
                                var ipInfoList = await Task.WhenAll(taIpList.Select(ip => GetIPBusinessAsync(ip, dbContext)))
                                    .ContinueWith(task => task.Result.Select((name, index) => !string.IsNullOrWhiteSpace(name) ? $"{taIpList[index]}({name})" : taIpList[index]).ToList());

                                var ipInfo = string.Join(", ", ipInfoList);
                                strBuilder.Append($"\n<font color=\"warning\">目标ip：{ipInfo}</font>");
                            }
                        }
                    }
                }
                else
                {
                    strBuilder.Append($"\n已进行封禁处理！\n暂未发现此IP的网络活动轨迹！");
                }

                // 添加深信服的通知
                if (!string.IsNullOrWhiteSpace(input.BlockStr1))
                {
                    input.BlockStr1 = input.BlockState1
                        ? $"地产-光   里:<font color=\"green\"> 封禁成功</font>"
                        : $"地产-光   里:<font color=\"red\"> 封禁失败，原因：{input.BlockStr1}</font>";
                    input.BlockStr2 = input.BlockState2
                        ? $"商业-长嘉汇:<font color=\"green\"> 封禁成功</font>"
                        : $"商业-长嘉汇:<font color=\"red\"> 封禁失败，原因：{input.BlockStr2}</font>";
                    //input.BlockStr3 = input.BlockState3
                    //    ? $"商业-光   花:<font color=\"green\"> 封禁成功</font>"
                    //    : $"商业-光   花:<font color=\"red\"> 封禁失败，原因：{input.BlockStr3}</font>";

                    strBuilder.Append($"\n{input.BlockStr1}\n{input.BlockStr2}\n{input.BlockStr3}");
                }

                var contents = new Dictionary<string, object>
                {
                    { "content", strBuilder.ToString() }
                };

                parameters.Add("markdown", contents);

                var response = HttpHelper.PostData(url, parameters, null, ContentType.Json);

                if (string.IsNullOrEmpty(response) || response.Contains("\"errcode\":0"))
                {
                    Console.WriteLine("企微通知发送成功。");
                }
                else
                {
                    Console.WriteLine($"企微通知发送失败，响应内容：{response}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发送企微通知时发生错误。{ex}");
            }
        }

        public async Task<string> GetIPBusinessAsync(string ip, IHWDbAccessor dbContext = null)
        {
            dbContext ??= _db;
            var base_HWIP = await dbContext.GetIQueryable<Base_HWIPBusiness>().FirstOrDefaultAsync(x => x.F_IP == ip);
            return base_HWIP?.F_Name ?? string.Empty;
        }
        public async Task InsertOrUpdateAsync(List<Base_IpLock> locks)
        {
            var existingIds = await this.Db.GetIQueryable<Base_IpLock>().Select(x => x.F_Id).ToListAsync().ConfigureAwait(false);

            var inserts = locks.Where(ipLock => !existingIds.Contains(ipLock.F_Id)).ToList();
            var updates = locks.Where(ipLock => existingIds.Contains(ipLock.F_Id)).ToList();

            if (inserts.Any())
            {
                await this.Db.InsertAsync(inserts).ConfigureAwait(false);
            }

            if (updates.Any())
            {
                await this.Db.UpdateAsync(updates).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// 批量新增ip
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> SaveBatchLockIpAsync(string ips, HWTypeEnum hWType = HWTypeEnum.黑名单)
        {
            var ipList = ips.Split(',')
                            .Select(ip => ip.Trim())
                            .Distinct()
                            .ToList();

            try
            {
                // 查询现有的IP和白名单IP
                var existingIps = await GetIQueryable().Select(x => x.F_IP).ToListAsync();
                var whiteIps = await Db.GetIQueryable<Base_HWWhiteIp>().Select(x => x.F_Ip).ToListAsync();

                // 筛选出需要新增的IP
                var newlyLockedIps = ipList.Except(existingIps.Concat(whiteIps)).ToList();

                if (newlyLockedIps.Any())
                {
                    var ipLockTasks = newlyLockedIps.Select(async ip =>
                    {
                        var ipLock = new Base_IpLock()
                        {
                            F_Id = GuidHelper.GenerateKey(),
                            F_CreateDate = DateTime.Now,
                            F_IP = ip,
                            F_IsLock = hWType == HWTypeEnum.蜜罐 ? 0 : 1,
                            F_LockTime = DateTime.Now,
                            F_System = hWType == HWTypeEnum.蜜罐 ? "蜜罐手动上传封禁" : "黑名单手动上传封禁"
                        };

                        if (hWType != HWTypeEnum.蜜罐)
                        {
                            ipLock.F_Ctity = await IpHelper.GetCityByIpAsync(ip);
                            if (!ipLock.F_Ctity.IsNullOrWhiteSpace() && ipLock.F_Ctity == "重庆")
                            {
                                ipLock.F_IsLock = 0;
                                ipLock.F_LockTime = null;
                            }
                        }
                        else
                        {
                            ipLock.F_LockTime = null;
                        }

                        return ipLock;
                    });

                    var newIpLocks = await Task.WhenAll(ipLockTasks).ConfigureAwait(false);
                    this.Db.BulkInsert(newIpLocks.ToList());

                    // 如果是蜜罐类型，处理蜜罐的逻辑
                    if (hWType == HWTypeEnum.蜜罐)
                    {
                        var mggIps = await this.Db.GetIQueryable<Base_MGIpLock>().Select(x => x.F_IP).ToListAsync().ConfigureAwait(false);
                        var newMgIps = newlyLockedIps.Except(mggIps).ToList();

                        if (newMgIps.Any())
                        {
                            var mgIpLocks = newMgIps.Select(ip => new Base_MGIpLock()
                            {
                                F_Id = GuidHelper.GenerateKey(),
                                F_CreateDate = DateTime.Now,
                                F_IP = ip,
                                F_IsLock = 1,
                                F_LockTime = DateTime.Now,
                                F_System = "蜜罐手动上传封禁"
                            }).ToList();
                            this.Db.BulkInsert(mgIpLocks);
                        }
                    }

                    return newlyLockedIps;
                }

                return new List<string>();
            }
            catch (Exception ex)
            {
                throw new Exception("手动上传封禁失败！", ex);
            }
        }
        public async Task AddDataAsync(Base_IpLock data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Base_IpLock data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Base_IpLock>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Base_IpLock, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<Base_IpLock, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 私有成员

        #endregion
    }
    public class IpLockInfoDto
    {
        /// <summary>
        /// 是否存在黑名单
        /// </summary>
        public bool IsExistsIp { get; set; }
        /// <summary>
        /// 是否存在记录（近2天)
        /// </summary>
        public bool isExistsRecord { get; set; }
        /// <summary>
        /// ip归属地
        /// </summary>
        public string Ctity { get; set; }
        /// <summary>
        /// 访问记录帆软地址
        /// </summary>
        public string ALiYunFineSrc { get; set; }
        /// <summary>
        /// 系统记录帆软地址
        /// </summary>
        public string UserRecordFineSrc { get; set; }
    }
    /// <summary>
    /// 深信服登录返回
    /// </summary>
    public class SXLoginResult
    {
        public string token { get; set; }
    }
    /// <summary>
    /// 深信服登录返回
    /// </summary>
    public class SXLoginData
    {
        public SXLoginResult loginResult { get; set; }
    }
    /// <summary>
    /// 深信服登录返回
    /// </summary>
    public class SXLoginResponse
    {
        public string code { get; set; }
        public string message { get; set; }
        public SXLoginData data { get; set; }
    }
    public class BlockResponse
    {
        public int code { get; set; } // 响应码
        public string message { get; set; } // 响应消息
        public BlockDataInfo data { get; set; } // 数据部分
    }

    public class BlockDataInfo
    {
        public int domain { get; set; } // 域名
        public string type { get; set; } // 类型
        public bool isDefault { get; set; } // 是否默认
        public string url { get; set; } // URL
        public bool enable { get; set; } // 是否启用
        public string description { get; set; } // 描述
        public DateTime createTime { get; set; } // 创建时间
    }
    /// <summary>
    /// 深信服获取地区返回
    /// </summary>
    public class SXLocationResponse
    {
        public int code { get; set; }
        public string message { get; set; }
        public locationInfo data { get; set; }
    }
    public class locationInfo
    {
        public string ip { get; set; }
        public string location { get; set; }
    }
}