﻿{
  "AllowedHosts": "*",
  "log": { //日志配置
    "minlevel": "Trace", //定义详见Microsoft.Extensions.Logging.LogLevel
    "console": {
      "enabled": true
    },
    "debug": {
      "enabled": true
    },
    "file": {
      "enabled": true
    },
    "elasticsearch": {
      "enabled": false,
      "nodes": [ "http://localhost:9200/" ],
      "indexformat": "colder.{0:yyyyMM}"
    },
    "overrides": [ //重写日志输出级别
      {
        "source": "Microsoft.AspNetCore",
        "minlevel": "Information"
      },
      {
        "source": "Microsoft.EntityFrameworkCore",
        "minlevel": "Information"
      },
      {
        "source": "Microsoft.EntityFrameworkCore.Infrastructure",
        "minlevel": "Warning"
      }
    ]
  },
  "Cache": {
    "CacheType": "Redis",
    "Endpoints": [
      {
        "Name": "Redis1",
        "Type": "Redis",
        "Endpoint": "T7bz8bGG38HmEM0QUIZZ2w=="
        //"Endpoint": "Ek3l6o0XcE9hXVyDuAGjHcH1C8ieYXsuboHoQY/WHycEWoyUJqyKMg8PTFa+YnOWJ8XoLsMq9aIRQezVwRGgYUx6b7oH+u/ig7hXjpDOR4Kgv0rTLTxe6sUvBZypW320"
      },
      {
        "Name": "Redis2",
        "Type": "Redis",
        "Endpoint": ""
      }
    ]
  },
  "Database": {
    "BaseDb": {
      "DatabaseType": "SqlServer", //定义详见EFCore.Sharding.DatabaseType
      //"ConnectionString": "Data Source=************;Initial Catalog=HRSystem_Test;User Id=saback;Password=********"
      //"ConnectionString": "2jzQ+QCExy3kqB/W0Z3aVfxw89h1PSlkxCyHUweBkOj/wg4ydFZeJNslE+PwfeuXpA49wE+7HhL/S7g5bMR9LL/s5qb1GngjuI7ex2c0IO3fm3cNXBNUYwzTRrIIWLpSvyLxHuyv6kGNpr5DryPH9g==",
      //"ConnectionString": "Data Source=172.30.101.42;Initial Catalog=HRSystem_Test;User Id=saback;Password=****************"
      //"ConnectionString": "UnWGVWYeGYHGyEugkNuKXM8vqHaLAqDPwmgzwdXZh+eq7rGX2I1aT5f6RUb7NZi6pJbO0IWCJICtD5eqYHfVwiD+ILAQyxY6+CesMN1YLl47aqUURtqEsAGczf/RXuP/plJXfYFxQZj4IkDjKEW5JA=="
      //"ConnectionString": "Data Source=172.30.101.42;Initial Catalog=HRSystem;User Id=saback;Password=****************"
    "ConnectionString": "UnWGVWYeGYHGyEugkNuKXM8vqHaLAqDPwmgzwdXZh+eq7rGX2I1aT5f6RUb7NZi687PkdeiJsLfegWm8EbFAIJ6mftYkbNmnoTR3RhX/++XOBtBpmXduLo51q9o2XBMtvaE+g++eynFIDhZzQy06ueYTLGB8dUMr46+WFra/Uz0="
    },
    "ERPDb": {
      "DatabaseType": "SqlServer", //定义详见EFCore.Sharding.DatabaseType
      //"ConnectionString": "Data Source=172.30.101.42;Initial Catalog=Mysoft_ERP25;Integrated Security=False;Pooling=true;user id=saback;password=****************"
      "ConnectionString": "UnWGVWYeGYHGyEugkNuKXM8vqHaLAqDPwmgzwdXZh+da0IOC05LPm10vEDC01fs28qnwflFINiyJoXZ1DvkDd0BtoCRiXFcVJm4XxLSKiVZYg7sd1FPbWU6uK+7DP1FUA4jqfUU7fOuIDyj7WpH7HXuKoYK4KKS56TN7P0IDI5VhsXJrHCsCfoX7OUAanvC+"
    },
    "GoDb": {
      "DatabaseType": "SqlServer", //定义详见EFCore.Sharding.DatabaseType
      //"ConnectionString": "Data Source=************;Initial Catalog=GoDB;Integrated Security=False;Pooling=true;user id=CqHouse;password=**********;",
      "ConnectionString": "2jzQ+QCExy3kqB/W0Z3aVfxw89h1PSlkxCyHUweBkOjvsWifhzp4AYgfUuzK/dA3rlnccYL280Mua8NXjd0u7yenlKxlxYOzI3hKXRYWwuKpObwtuwS9gBY7/jdriuAfmBIrzf8uBg0RA4FS7CVHc/+6stoVb+SQ0VDnoT0EkJc="

    },
    "BusinessDb": {
      "DatabaseType": "SqlServer", //定义详见EFCore.Sharding.DatabaseType
      //"ConnectionString": "Data Source=************;Initial Catalog=BusinessDb;Integrated Security=False;Pooling=true;user id=CqHouse;password=**********;"
      "ConnectionString": "2jzQ+QCExy3kqB/W0Z3aVfxw89h1PSlkxCyHUweBkOiQw5YFN3nwv7Bdja0c1b7UsCafFKgix4sd7YlGiYQMpz64Z8U/EZwht1u/0XER2YLaO1Ldwb6OqgY/GNUW9KJzubQ7ePjE2LdTyXMNxR5Rt+0kx2TSfpD5XT6FTgyMcJM="

    },
    "ShekouDb": {
      "DatabaseType": "SqlServer", //定义详见EFCore.Sharding.DatabaseType
      //"ConnectionString": "Data Source=************;Initial Catalog=Shekou;Integrated Security=False;Pooling=true;user id=CqHouse;password=**********;",
      "ConnectionString": "2jzQ+QCExy3kqB/W0Z3aVfxw89h1PSlkxCyHUweBkOi0ax0oMklXsPghOVpQ//TW3B4nW47LJ05FK3Q+pLjEMT+4Mj7fmHXIz/fTp9ZvqeWqD8sXyZ+r/MkXTSE2n72+FolsQh3I8cEMacuFjR83QsBf7/0JpNYEC9g+IpEwM/c="

    },
    "HWDb": {
      "DatabaseType": "SqlServer", //定义详见EFCore.Sharding.DatabaseType
      //"ConnectionString": "Data Source=***********;Initial Catalog=HWDB;Integrated Security=False;Pooling=true;user id=HwUser;password=****************;",
      "ConnectionString": "UnWGVWYeGYHGyEugkNuKXGKYEjvm7NRTtdqZRs001MamDJKMt3twX8Qpkq1d7lcUo6tn0wOPLZ31qesGzC0xG+IFFxJxONc87+4fU5cNAmdim3oN9B/sIqlEBM/p+sw/qE8NdnLz6sRPFCSBLKSTxSw9sH2DPtQSAMHX+KbGnXI="
    }
  },
  //雪花Id,,以进程为单位,不同进程数据中心Id和机器Id不能同时重复
  "IsEnableAD": false,
  "WorkerId": 1, //机器Id
  "JWTSecret": "CpUTEdG30LPAQXxQ",
  "WebRootUrl": "https://costapi.cqlandmark.com", //接口根地址
  //"WebRootUrl": "https://hrapi.cqlandmark.com", //接口根地址
  //"OAUrl": "https://**************/", //OA流程接口地址
 "OAUrl": "https://www.cqlandmark.com/", //OA流程接口地址
  //"OAUrl": "https://*************/", //OA流程接口地址
  "OAUrls": {
    "BacklogProcess": "rest/archives/getHrDBWorkflow", //待办流程获取
    "HasProcess": "rest/archives/getHrYBWorkflow", //已办流程数量获取
    "GetToken": "ssologin/getToken" //获取token值
  },
  "SMSUrls": {
    "Url": "http://localhost:5566/", //地址
    "aliSendMsg": "AliDysmsapi/SendMsg", //阿里发送短信
    "carpApiMsg": "CarpApiMsg/sendMsg" //亿美发送短信
  },
  //微信配置文件
  "WxConfig": {
    "plan": {
      "appId": "wx63acb7c68f057646",
      "secret": "8ee739c0693a146c87389d4da753f23c"
    },
    "plancockpit": {
      "corpid": "",
      "corpsecret": "",
      "baseurl": "https://qyapi.weixin.qq.com",
      "getUserByCode": "/cgi-bin/user/getuserinfo?access_token={0}&code={1}",
      "getToken": "/cgi-bin/gettoken?corpid={0}&corpsecret={1}",
      "getUserByUserId": "/cgi-bin/user/get?access_token={0}&userid={1}"
    }
  },
  //"PunchCardBssId": [
  //  "PunchCard_Manage"
  //],
  "PaySystem": {
    "Url": "https://payapi.cqlandmark.com/" //地址
  },
  //地产流程
  "OAFormId": {
    "AskLeaveFlow": "-409", //请假流程 测试:403 正式：409
    "TermLeaveFlow": "-419", //销假流程 测试:405 正式：419
    "PositiveFlow": "-415", //转正流程 测试:404 正式：415
    "DepartureFlow": "-410", //离职流程 测试:406 正式：410
    "InductionFlow": "-417", //入职流程
    "TransferInfoFlow": "-418", //员工调动流程 测试:411 正式：418
    "LaborContractNewFlow": "-411", //劳动合同新签流程
    "LaborContractSignedFlow": "-412", //劳动合同续签流程 测试:410 正式：412
    "LaborContractBGFlow": "-413", //劳动合同变更流程
    "WorkOvertimeFlow": "-414", //加班申请流程 测试:412 正式：414
    "NewEmploInductionFlow": "-21", //新员工入职申请流程
    "AttendanceFlow": "-579" //员工出勤流程
  },
  //商业流程
  "OASYFormId": {
    "AskLeaveFlow": "-574", //请假流程 测试:577 正式：574
    "TermLeaveFlow": "-571", //销假流程 测试:405 正式：419
    "PositiveFlow": "-574", //转正流程 测试:404 正式：415
    "DepartureFlow": "-575", //离职流程 测试:406 正式：410
    "InductionFlow": "-417", //入职流程
    "TransferInfoFlow": "-418", //员工调动流程 测试:411 正式：418
    "LaborContractNewFlow": "-411", //劳动合同新签流程
    "LaborContractSignedFlow": "-573", //劳动合同续签流程 测试:410 正式：412
    "LaborContractBGFlow": "-413", //劳动合同变更流程
    "WorkOvertimeFlow": "-414", //加班申请流程 测试:412 正式：414
    "NewEmploInductionFlow": "-21" //新员工入职申请流程
  },
  "NaturalHoliday": "婚假,陪产假,产假",
  "fileTypes": ".zip,.jpg,.png,.gif,.xlsx,.xls,.jpeg,.pptx,.docx,.pdf,.doc,.ppt,.docm,.txt,.mp4",
  //京东万邦配置文件
  "JDWB": {
    "apiKey": "25630443",
    "apiSecret": "7a6d6053939e0e7a3efcda53650db9b2",
    "appcode": "984ffbb869004fa78cfe0cf0401fe910",
    "callBackUrl": "https://markapi.cqlandmark.com/Shop_Manage/Z_Products/JDProductInfoCallBack"
  }
}
