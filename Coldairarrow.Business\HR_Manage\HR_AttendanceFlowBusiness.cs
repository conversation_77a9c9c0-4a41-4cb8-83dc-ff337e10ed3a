﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.IBusiness;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using static Coldairarrow.Entity.Shop_Manage.Model.AliJDResultModel;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Microsoft.Extensions.Configuration;
using Coldairarrow.Util.Helper;
using NPOI.POIFS.FileSystem;
using System.Xml;
using Coldairarrow.Entity;
using System;
using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Entity.HR_AttendanceManage;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_AttendanceFlowBusiness : BaseBusiness<HR_AttendanceFlow>, IHR_AttendanceFlowBusiness, ITransientDependency
    {
        private IConfiguration _configuration;
        public HR_AttendanceFlowBusiness(IDbAccessor db, IConfiguration configuration)
            : base(db)
        {
            _configuration = configuration;
        }

        #region 外部接口

        public async Task<PageResult<HR_AttendanceFlow>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_AttendanceFlow>();
            var search = input.Search;

            if (!search.month.IsNullOrEmpty())
            {
                if (DateTime.TryParse(search.month, out DateTime monthDate))
                {
                    where = where.And(x => x.F_DateStr == monthDate.ToString("yyyy-MM"));
                }
            }
            if (search.wfState.HasValue)
            {
                where = where.And(x => x.F_WFState == search.wfState.Value);
            }
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_AttendanceFlow, bool>(
                    ParsingConfig.Default,
                    false,
                    $"{search.Condition}.Contains(@0)",
                    search.Keyword
                );
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_AttendanceFlow> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_AttendanceFlow data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_AttendanceFlow data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_AttendanceFlow>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_AttendanceFlow, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_AttendanceFlow, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        /// <summary>
        /// 流程回调方法
        /// </summary>
        /// <param name="input"></param>
        public void FlowCallBack(FlowInputDTO input)
        {
            if (input == null)
            {
                throw new System.Exception("参数错误");
            }
            var entity = this.Db.GetIQueryable<HR_AttendanceFlow>().Where(i => i.F_Id == input.id).FirstOrDefault();
            if (entity == null)
            {
                throw new System.Exception("未找到对象");
            }
            entity.F_WFState = input.status;
            this.Db.Update(entity);
        }
        /// <summary>
        /// 导入并保存数据
        /// </summary>
        /// <param name="physicPath">物理路径</param>
        /// <returns></returns>
        public async Task<bool> SaveAndCreateFlow(string physicPath, IOperator op, string month, string Remark)
        {
            List<string> departments = new List<string>
            {
                "IT部",
                "财务部",
                "成本部",
                "发展部",
                "工程部",
                "景观部",
                "客服部",
                "人力资源部",
                "商业商务中心",
                "设计部",
                "行政部",
                "营销部",
                "总经理室"
            };
            var isComplete = true;
            List<HR_AttendanceFlow> hR_AttendanceFlows = new List<HR_AttendanceFlow>();
            Base_User user = null;
            var hR_Attendances = Db.GetIQueryable<HR_AttendanceFlow>().Where(x => x.F_DateStr == month);
            if (hR_Attendances.Count() > 0)
            {
                throw new System.Exception("该月份已发起过相关流程，请勿重复发起!");
            }
            var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.BaseUserId == op.UserId);
            ProjectDept projectDept = new ProjectDept();
            if (formalEmployees != null)
            {
                user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == formalEmployees.BaseUserId);

            }
            //var user = db.getiqueryable<base_user>().firstordefault(x => x.id == data.f_createuserid);
            //string token = jWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
            var userName = user != null ? user.UserName.Replace("@cqlandmark.com", "") : "";
            departments.ForEach((dep) =>
            {
                HR_AttendanceFlow data = new HR_AttendanceFlow();
                InitEntity(data, op);
                string token = string.Empty;
                token = JWTHelper.GetBusinessToken(data.F_Id, user != null ? user.UserName.Replace("@cqlandmark.com", "") : "");
                Dictionary<string, object> paramters = new Dictionary<string, object>();
                paramters.Add("reqCode", token);
                paramters.Add("reqNo", data.F_Id);
                paramters.Add("applyPerson", userName);
                paramters.Add("applyDate", DateTime.Now);
                var companyName = projectDept?.company;
                List<FileInfoDTO> files = new List<FileInfoDTO>()
                {
                    new FileInfoDTO
                    {
                        FilePath = physicPath,
                        FileName = physicPath.Substring(physicPath.LastIndexOf('/') + 1)
                    }
                };
                paramters.Add("formId", _configuration["OAFormId:AttendanceFlow"]);
                Dictionary<string, object> hrData = new Dictionary<string, object>();
                hrData.Add("dept", dep);
                hrData.Add("month", month);
                hrData.Add("remark", Remark);
                hrData.Add("files", files);
                hrData.Add("project", "长嘉汇");
                hrData.Add("oaCompany", "重庆招商置地开发有限公司");
                paramters.Add("hrData", hrData);
                string action = "create";
                string str = HttpHelper.PostData(_configuration["OAUrl"] + "rest/archives/" + action, paramters, null, ContentType.Json);

                var retObj = str.ToObject<Dictionary<string, string>>();
                if (retObj != null)
                {
                    if (retObj["errCode"] == "0000")
                    {
                        data.F_WFId = retObj["oaReqId"];
                        data.F_WFState = (int)WFStates.审核中;
                        data.F_DateStr = month;
                        data.F_Remark = Remark;
                        data.F_DepartmentName = dep;
                        data.F_Month = Convert.ToInt32(month.Split('-')[1]);
                        data.F_Year = Convert.ToInt32(month.Split('-')[0]);
                        data.F_Tiltle = $"{data.F_Year}年{data.F_Month}月部门员工考勤确认";
                        hR_AttendanceFlows.Add(data);
                    }
                }
            });
            if (hR_AttendanceFlows.Count > 0)
            {
                await this.Db.InsertAsync(hR_AttendanceFlows);
                return isComplete;
            }
            else
            {
                throw new System.Exception("创建流程失败!");
            }
        }
        #endregion

        #region 私有成员

        #endregion
    }
}