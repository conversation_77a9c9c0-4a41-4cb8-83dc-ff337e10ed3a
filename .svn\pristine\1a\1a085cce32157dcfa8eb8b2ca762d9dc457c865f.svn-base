﻿<template>
  <a-card :bordered="false">
    <div class="table-operator">
      <!-- <a-button type="primary" icon="plus" @click="hanldleAdd()">新建</a-button>
      <a-button type="primary" icon="minus" @click="handleDelete(selectedRowKeys)" :disabled="!hasSelected()"
        :loading="loading">删除</a-button> -->
      <a-button type="primary" icon="retweet" @click="hanldleTime()">
        通讯费开放开关</a-button>
      <a-button type="primary" icon="redo" @click="getDataList()">刷新</a-button>
    </div>

    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="10">
          <a-col :md="4" :sm="24">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="queryParam.condition">
                <a-select-option key="F_UserName">员工姓名</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item>
              <a-input v-model="queryParam.keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-button type="primary" @click="() => {this.pagination.current = 1; this.getDataList()}">查询</a-button>
            <a-button style="margin-left: 8px" @click="() => (queryParam = {})">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table" :columns="columns" :rowKey="row => row.F_Id" :dataSource="data" :pagination="pagination"
      :loading="loading" @change="handleTableChange"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true" size="small">
      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record.F_Id)">编辑</a>
          <a-divider type="vertical" />
          <a @click="handlePointList(record.F_UserId)">额度明细</a>
          <!-- <a-divider type="vertical" />
          <a @click="handleDelete([record.F_Id])">删除</a> -->
        </template>
      </span>
    </a-table>

    <edit-form ref="editForm" :parentObj="this"></edit-form>
    <CList ref="cList" :parentObj="this"></CList>
    <Time ref="time" :parentObj="this"></Time>
  </a-card>
</template>

<script>
import EditForm from './EditForm'
import CList from '../HR_FeeUseDetail/components/CList.vue'
import Time from './Time'
const columns = [
  { title: '年份', dataIndex: 'F_Iyear', width: '10%' },
  { title: '员工姓名', dataIndex: 'F_UserName', width: '10%' },
  { title: '标准额度', dataIndex: 'F_StandardNumber', width: '10%' },
  { title: '增减额度', dataIndex: 'F_LncDecNumber', width: '10%' },
  { title: '剩余额度', dataIndex: 'F_ResNumber', width: '10%' },
  { title: '使用额度', dataIndex: 'F_UsedNumber', width: '10%' },
  { title: '操作', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
]

export default {
  components: {
    EditForm,
    CList,
    Time
  },
  mounted () {
    this.getDataList()
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 20,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      filters: {},
      sorter: { field: 'F_CreateDate', order: 'desc' },
      loading: false,
      columns,
      queryParam: {
      },
      selectedRowKeys: []
    }
  },
  methods: {
    handleTableChange (pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.filters = { ...filters }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    exportExcel () {
    },
    getDataList () {
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/HR_Manage/HR_FeePointEntity/GetDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'F_Id',
          SortType: this.sorter.order,
          Search: this.queryParam,
          ...this.filters
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange (selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    hasSelected () {
      return this.selectedRowKeys.length > 0
    },
    handlePointList (F_UserId) {
      console.log(this.$refs.cList)
      this.$refs.cList.openForm(F_UserId)
    },
    hanldleAdd () {
      this.$refs.editForm.openForm()
    },
    hanldleTime () {
      this.$refs.time.openForm()
    },
    handleEdit (id) {
      this.$refs.editForm.openForm(id)
    },
    handleDelete (ids) {
      var thisObj = this
      this.$confirm({
        title: '确认删除吗?',
        onOk () {
          return new Promise((resolve, reject) => {
            thisObj.$http.post('/HR_Manage/HR_PointEntity/DeleteData', ids).then(resJson => {
              resolve()

              if (resJson.Success) {
                thisObj.$message.success('操作成功!')

                thisObj.getDataList()
              } else {
                thisObj.$message.error(resJson.Msg)
              }
            })
          })
        }
      })
    }
  }
}
</script>