﻿using Coldairarrow.Business.HolidayManage;
using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Microsoft.Extensions.Caching.Distributed;

namespace Coldairarrow.Api.Controllers.HolidayManage
{
    [Route("/HolidayManage/[controller]/[action]")]
    public class HR_HolidayLineController : BaseApiController
    {
        #region DI

        public HR_HolidayLineController(IHR_HolidayLineBusiness hR_HolidayLineBus, IDistributedCache cache)
        {
            _hR_HolidayLineBus = hR_HolidayLineBus;
            _cache = cache;
        }

        private readonly IDistributedCache _cache;
        IHR_HolidayLineBusiness _hR_HolidayLineBus { get; }

        #endregion

        #region 获取

        /// <summary>
        /// 获取法定年假剩余天数
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult GetRemainingAmount(PreReInputDTO input)
        {
            var rev = _hR_HolidayLineBus.GetRemainingAmount(input.id,input.time);
            return Success(rev);
        }
        [HttpPost]
        public async Task<PageResult<HR_HolidayLineDTO>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_HolidayLineBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_HolidayLine> GetTheData(IdInputDTO input)
        {
            return await _hR_HolidayLineBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_HolidayLine data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_HolidayLineBus.AddDataAsync(data);
            }
            else
            {
                await _hR_HolidayLineBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_HolidayLineBus.DeleteDataAsync(ids);
        }
        [HttpPost]
        public void GenerateLeaveAllowance(TypeInputDTO inputDTO)
        {
            var usermodel = GetOperator();
            _hR_HolidayLineBus.GenerateLeaveAllowance("", inputDTO.itemCode,usermodel);
        }
        #endregion


        #region 导出Excel

        /// <summary>
        /// 模板下载
        /// </summary>
        [HttpPost]
        public FileContentResult DownloadTemplate()
        {
            string filePath = Directory.GetCurrentDirectory() + "/BusinessTemplate/假期额度模板.xls";
            if (FileHelper.Exists(filePath))
            {
                var bys = System.IO.File.ReadAllBytes(filePath);//excel表转换成流
                return File(bys, "application/vnd.ms-excel");
            }
            else
            {
                throw new BusException("找不到模板");
            }

        }
        /// <summary>
        /// 下载错误Excel
        /// </summary>
        [HttpPost]
        public FileContentResult DownloadErrorExcel(IdInputDTO input)
        {
            DataTable dt = _cache.GetObject<DataTable>(input.id);
            if (dt != null)
            {
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "错误清单";
                excelconfig.Title = "错误清单";
                excelconfig.IsAllSizeColumn = true;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                int sort = 0;
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "员工", ExcelColumn = "员工", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "生效时间", ExcelColumn = "生效时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "结束时间", ExcelColumn = "结束时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "假期类型", ExcelColumn = "假期类型", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "假期单位", ExcelColumn = "假期单位", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "标准额度", ExcelColumn = "标准额度", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "实际额度", ExcelColumn = "实际额度", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "已用额度", ExcelColumn = "已用额度", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "增减额度", ExcelColumn = "增减额度", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "导入错误", ExcelColumn = "导入错误", Alignment = "left", Sort = sort++ });

                var t = ExcelHelper.ExportMemoryStream(dt, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");
                _cache.Remove(input.id);

                return file;
            }
            else
            {
                throw new BusException("找不到文件");
            }

        }

        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_HolidayLineBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "假期额度导出.xlsx";
                excelconfig.Title = "假期额度导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                var sort = 1;
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "empcode", ExcelColumn = "员工编码", Alignment = "left",Sort= sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "empname", ExcelColumn = "姓名", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_holidaytypes", ExcelColumn = "假期类型", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_effecttime", ExcelColumn = "生效时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_endtime", ExcelColumn = "结束时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_vacationunit", ExcelColumn = "假期单位", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_standardline", ExcelColumn = "标准额度", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_lncdecline", ExcelColumn = "增减额度", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_actualamount", ExcelColumn = "实际额度", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_usedline", ExcelColumn = "已用额度", Alignment = "left", Sort = sort++ });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入人员变动
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult UploadFileByForm()
        {
            var year = Request.Form["year"];
            if (string.IsNullOrEmpty(year))
            {
                throw new BusException("请选择年份");
            }
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return Error("上传文件不能为空");

            if (Request.Form.Files.Count > 1)
                return Error("只能上传一个文件");

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            var res = _hR_HolidayLineBus.ImportSaveData(physicPath, this.GetOperator(),year);
            if (res.Data != null)
            {
                string id = GuidHelper.GenerateKey();
                _cache.SetObject(id, res.Data);

                AjaxResult<string> ajaxResult = new AjaxResult<string>();
                ajaxResult.Success = false;
                ajaxResult.Msg = "保存失败";
                ajaxResult.ErrorCode = 3;
                ajaxResult.Data = id;
                return ajaxResult;
            }
            return res;
        }

        #endregion
    }
}