﻿using Coldairarrow.Business.Wechat_Go;
using Coldairarrow.Entity.Wechat_Go;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Newtonsoft.Json;
using Coldairarrow.Util.Helper;

namespace Coldairarrow.Api.Controllers.Wechat_Go
{
    [Route("/Wechat_Go/[controller]/[action]")]
    public class Go_GroupController : BaseApiController
    {
        #region DI

        public Go_GroupController(IGo_GroupBusiness go_GroupBus, IGo_GroupUserBusiness goGroupUserBus)
        {
            _go_GroupBus = go_GroupBus;
            _go_GroupUserBus = goGroupUserBus;
        }

        IGo_GroupBusiness _go_GroupBus { get; }
        IGo_GroupUserBusiness _go_GroupUserBus { get;}

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Go_Group>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _go_GroupBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Go_Group> GetTheData(IdInputDTO input)
        {
            return await _go_GroupBus.GetTheDataAsync(input.id);
        }

        /// <summary>
        /// 创建团队
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult CreartTeam()
        {
            try
            {
                string JsonString = HttpContext.Request.Form["info"].ToString();
                string openId = HttpContext.Request.Form["openId"].ToString();
                var data = JsonConvert.DeserializeObject<Go_Group>(JsonString);
                if (data.Id.IsNullOrEmpty())
                {
                    var result = _go_GroupUserBus.GetDataByOpenId(openId);
                    if (result.IsNullOrEmpty())
                    {
                        data.F_IsAble = 1;
                        data.Id = Guid.NewGuid().ToString("N");
                        data.CreateTime = DateTime.Now;
                        data.CreatorId = openId;
                        data.F_Leader = openId;
                        _go_GroupBus.AddDataAsync(data).Wait();
                        //新增管理员
                        var user = new Go_GroupUser();
                        user.F_CreateTime = DateTime.Now;
                        user.F_Id = Guid.NewGuid().ToString("N");
                        user.F_OpenId = openId;
                        user.F_TeamId = data.Id;
                        user.F_IsAble = 1;
                        user.F_UserType = 2;
                        _go_GroupUserBus.AddDataAsync(user).Wait();
                        return Success("新建圈子成功");
                    }
                    else
                    {
                        return Success("您已创建过圈子");
                    }

                }
                else
                {
                    data.F_IsAble = 1;
                    data.CreatorId = openId;
                    data.F_Leader = openId;
                    _go_GroupBus.UpdateDataAsync(data).Wait();
                    return Success("修改圈子成功");
                }

            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        /// <summary>
        /// 小程序，获取团队详情
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetData()
        {
            try
            {
                string Id = HttpContext.Request.Form["Id"].ToString();
                var data = _go_GroupBus.GetTheDataAsync(Id);
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        /// <summary>
        /// 获取团队成员
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult GetTeamUser()
        {
            try
            {
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var list = _go_GroupBus.GetTeamUser(teamId);
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        /// <summary>
        /// 获取我的团队，用于圈子
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult GetMyTeam()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                var data = _go_GroupBus.GetMyTeam(openId);
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        /// <summary>
        /// 获取某人的团队
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult GetUserTeam()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                var data = _go_GroupBus.GetTeamByOpenId(openId);
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }

        /// <summary>
        /// 获取排行
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult GetUserRank()
        {
            try
            {
                string type = HttpContext.Request.Form["type"].ToString();
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var data = new List<Go_MiniUserDTO>();
                switch (type)
                {
                    case "week": data = _go_GroupBus.getUserRank(teamId, 1); break;
                    case "month": data = _go_GroupBus.getUserRank(teamId, 3); break;
                    case "lastmonth": data = _go_GroupBus.getUserRank(teamId, 4); break;
                    default: data = _go_GroupBus.getUserRank(teamId, 2); break;
                }

                return Success(data);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }

        /// <summary>
        /// 获取带是否缴款的排行榜
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult GetUserRankByDate()
        {
            try
            {
              
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var queryBeginDate = TimeStampsHelper.GoToDateTime(HttpContext.Request.Form["beginDate"].ToString());
                var queryEndDate = TimeStampsHelper.GoToDateTime(HttpContext.Request.Form["endDate"].ToString());
                var list = _go_GroupBus.getUserRankByDate(teamId, queryBeginDate, queryEndDate);
                foreach (var i in list)
                {
                    if (i.PayState==true)
                    {
                        //再查询判断是否该时间段内需要缴纳
                        var result = _go_GroupBus.GetMoneyByDate(i.F_Id, teamId, queryBeginDate, queryEndDate);
                        if (!result.IsNullOrEmpty())
                        {
                            i.PayState = false;
                        }
                    }
                }
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Go_Group data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _go_GroupBus.AddDataAsync(data);
            }
            else
            {
                await _go_GroupBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _go_GroupBus.DeleteDataAsync(ids);
        }

        #endregion
        

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _go_GroupBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Go_Group>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _go_GroupBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion

        #region
        #endregion
    }
}