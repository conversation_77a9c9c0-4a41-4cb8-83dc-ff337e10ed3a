﻿using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using AutoMapper;
using System;
using Coldairarrow.IBusiness;
using Coldairarrow.Entity.Enum;
using Coldairarrow.Entity.HR_Manage;
using Aliyun.Base.xiaoxizn;
using Newtonsoft.Json;
using Coldairarrow.Entity;
using Microsoft.Extensions.Options;
using Coldairarrow.Entity.HR_Manage.Extensions;
namespace Coldairarrow.Business.Base_Manage
{
    public class Base_FileInfoBusiness : BaseBusiness<Base_FileInfo>, IBase_FileInfoBusiness, ITransientDependency
    {
        readonly IMapper _mapper;
        IOperator _operator;
        public Base_FileInfoBusiness(IDbAccessor db, IMapper mapper, IOperator operators)
            : base(db)
        {
            _operator = operators;
            _mapper = mapper;
        }

        #region 外部接口

        public async Task<PageResult<Base_FileInfo>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Base_FileInfo>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Base_FileInfo, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Base_FileInfo> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(Base_FileInfo data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Base_FileInfo data)
        {
            await UpdateAsync(data);
        }
        //删除文件 
        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Base_FileInfo>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Base_FileInfo, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<Base_FileInfo, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        //上传文件
        public FileModel UploadFiles(FileModel data, IOperator userInfo)
        {

             DateTime nowDate = DateTime.Now;
            bool isAdd = false;
            var isRem = data.F_IsRecruitment;
            if (string.IsNullOrWhiteSpace(data.F_Id))
            {
                data.F_Id = GuidHelper.GenerateKey();
                data.F_CreateDate = nowDate;
                data.F_CreateUserId = "";
                data.F_CreateUserName = "";
                data.F_EnabledMark = 1;
                isAdd = true;
            }
            foreach (var item in data.Files)
            {
                item.F_CreateUserId = "";
                item.F_CreateUserName = "";
                item.F_CreateDate = nowDate;
                item.F_EnabledMark = 1;
                item.F_FileFolderId = data.F_Id;
                item.F_Id = GuidHelper.GenerateKey();
            }
            Base_FileFolder fileFolder = new Base_FileFolder()
            {
                F_BusState = data.F_BusState,
                F_CreateDate = data.F_CreateDate,
                F_CreateUserId = data.F_CreateUserId,
                F_CreateUserName = data.F_CreateUserName,
                F_DeleteMark = data.F_DeleteMark,
                F_EnabledMark = data.F_EnabledMark,
                F_FolderName = data.F_FolderName,
                F_FolderType = data.F_FolderType,
                F_Id = data.F_Id,
                F_Remark = data.F_Remark
            };
            if (isAdd)
            {
                Db.Insert<Base_FileFolder>(fileFolder);
            }
            else
            {
                var entity = Db.GetIQueryable<Base_FileInfo>().Where(i => i.F_FileFolderId == data.F_Id).ToList();
                foreach (var item in entity)
                {
                    if (isRem.HasValue)
                        item.F_DeleteMark = 1;
                    else
                        Db.Delete(item);
                }
                if (isRem.HasValue)
                    Db.Update(entity);
            }
            BulkInsert(data.Files);
            Db.SaveChanges();
            return data;
        }
        //简历上传文件
        public List<FileModels<HR_Entry>> appresumeUploadFiles(FileModel data, string id, IOperator userInfo)
        {
            DateTime nowDate = DateTime.Now;
            bool isAdd = false;
            List<Base_FileFolder> base_FileFolders = new List<Base_FileFolder>();
            List<Base_FileInfo> fileInfoList = new List<Base_FileInfo>();
            List<HR_Entry> hR_Entries = new List<HR_Entry>();
            List<FileModels<HR_Entry>> fileModels = new List<FileModels<HR_Entry>>();
            foreach (var item in data.Files)
            {
                //循环保存
                //一个文件一个文件夹
                if (!id.IsNullOrEmpty() || !isExist(item.F_FileHash, FileTypes.招聘简历))
                {
                    FileModels<HR_Entry> fileModel = new FileModels<HR_Entry>();
                    var FolderId = GuidHelper.GenerateKey();
                    Base_FileFolder fileFolder = new Base_FileFolder()
                    {
                        F_BusState = item.F_BusState,
                        F_CreateDate = item.F_CreateDate,
                        F_CreateUserId = item.F_CreateUserId,
                        F_CreateUserName = item.F_CreateUserName,
                        F_DeleteMark = item.F_DeleteMark,
                        F_EnabledMark = item.F_EnabledMark,
                        F_FolderName = data.F_FolderName,
                        F_FolderType = data.F_FolderType,
                        F_Id = FolderId,
                        F_Remark = data.F_Remark
                    };
                    fileModel.base_FileFolder = fileFolder;
                    base_FileFolders.Add(fileFolder);
                    //item.F_CreateUserId = "";
                    //item.F_CreateUserName = "";
                    item.F_CreateDate = nowDate;
                    item.F_EnabledMark = 1;
                    item.F_FileFolderId = FolderId;
                    item.F_Id = GuidHelper.GenerateKey();
                    fileModel.Files = item;
                    fileInfoList.Add(item);
                    HR_Entry hR_Entry = new HR_Entry()
                    {
                        F_CreateDate = item.F_CreateDate,
                        F_CreateUserId = item.F_CreateUserId,
                        F_Id = GuidHelper.GenerateKey(),
                        F_CreateUserName = item.F_CreateUserName,
                        F_BusState = (int)InductionBusState.未入职,
                        F_FileId = FolderId
                    };
                    if (!string.IsNullOrEmpty(id))
                    {
                        hR_Entry = Db.GetIQueryable<HR_Entry>().FirstOrDefault(i => i.F_Id == id);
                        if (hR_Entry == null)
                        {
                            throw new Exception("用户不存在");
                        }
                        hR_Entry.F_FileId = FolderId;
                    }
                    fileModel.Data = hR_Entry;
                    fileModels.Add(fileModel);
                    hR_Entries.Add(hR_Entry);
                }
            }
            if (base_FileFolders.Count > 0)
            {
                this.Db.BulkInsert(base_FileFolders);
            }
            if (fileInfoList.Count > 0)
            {
                BulkInsert(fileInfoList);
            }
            if (hR_Entries.Count > 0)
            {
                if (string.IsNullOrEmpty(id))
                {
                    this.Db.BulkInsert(hR_Entries);
                }
                else
                {
                    this.Db.Update(hR_Entries);
                }
            }
            Db.SaveChanges();
            return fileModels;
        }

        //简历上传文件
        [Transactional]
        public List<FileModels<HR_Entry>> resumeUploadFiles(FileModel data, string id, IOperator userInfo)
        {
            DateTime nowDate = DateTime.Now;
            bool isAdd = false;
            List<Base_FileFolder> base_FileFolders = new List<Base_FileFolder>();
            List<Base_FileInfo> fileInfoList = new List<Base_FileInfo>();
            List<HR_Entry> hR_Entries = new List<HR_Entry>();
            List<FileModels<HR_Entry>> fileModels = new List<FileModels<HR_Entry>>();
            foreach (var item in data.Files)
            {
                //循环保存
                //一个文件一个文件夹
                if (!id.IsNullOrEmpty() || !isExist(item.F_FileHash, FileTypes.招聘简历))
                {
                    FileModels<HR_Entry> fileModel = new FileModels<HR_Entry>();
                    var FolderId = GuidHelper.GenerateKey();
                    Base_FileFolder fileFolder = new Base_FileFolder()
                    {
                        F_BusState = item.F_BusState,
                        F_CreateDate = item.F_CreateDate,
                        F_CreateUserId = item.F_CreateUserId,
                        F_CreateUserName = item.F_CreateUserName,
                        F_DeleteMark = item.F_DeleteMark,
                        F_EnabledMark = item.F_EnabledMark,
                        F_FolderName = data.F_FolderName,
                        F_FolderType = data.F_FolderType,
                        F_Id = FolderId,
                        F_Remark = data.F_Remark
                    };
                    fileModel.base_FileFolder = fileFolder;
                    base_FileFolders.Add(fileFolder);
                    //item.F_CreateUserId = "";
                    //item.F_CreateUserName = "";
                    item.F_CreateDate = nowDate;
                    item.F_EnabledMark = 1;
                    item.F_FileFolderId = FolderId;
                    item.F_Id = GuidHelper.GenerateKey();
                    fileModel.Files = item;
                    fileInfoList.Add(item);
                    HR_Entry hR_Entry = new HR_Entry();
                    //if (!string.IsNullOrEmpty(id))
                    //{
                    //    //查询对应的简历
                    //    hR_Entry = this.Db.GetEntity<HR_Entry>(id);
                    //    if (hR_Entry != null)
                    //    {
                    //        hR_Entry.F_FileId = FolderId;
                    //        hR_Entry.F_SeveralInterview = this.Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => i.F_UserId == hR_Entry.F_Id && i.F_BusState == 1).Count();
                    //    }
                    //}
                    //else
                    //{
                    hR_Entry.F_CreateDate = item.F_CreateDate;
                    hR_Entry.F_CreateUserId = item.F_CreateUserId;
                    hR_Entry.F_Id = GuidHelper.GenerateKey();
                    hR_Entry.F_CreateUserName = item.F_CreateUserName;
                    hR_Entry.F_BusState = (int)InductionBusState.未入职;
                    hR_Entry.F_FileId = FolderId;
                    hR_Entry.F_SeveralInterview = 0;
                    //}
                    //if (!string.IsNullOrEmpty(id))
                    //{
                    //    hR_Entry = Db.GetIQueryable<HR_Entry>().FirstOrDefault(i => i.F_Id == id);
                    //    if (hR_Entry == null)
                    //    {
                    //        throw new Exception("用户不存在");
                    //    }
                    //    hR_Entry.F_FileId = FolderId;
                    //}
                    fileModel.Data = hR_Entry;
                    fileModels.Add(fileModel);
                    hR_Entries.Add(hR_Entry);
                }
            }
            if (base_FileFolders.Count > 0)
            {
                this.Db.Insert(base_FileFolders);
                //this.Db.BulkInsert(base_FileFolders);
            }
            if (fileInfoList.Count > 0)
            {
                BulkInsert(fileInfoList);
            }
            if (hR_Entries.Count > 0)
            {
                if (string.IsNullOrEmpty(id))
                {
                    this.Db.BulkInsert(hR_Entries);
                }
                else
                {
                    //this.Db.Update(hR_Entries);
                }
            }
            Db.SaveChanges();
            return fileModels;
        }
        /// <summary>
        /// 简历解析保存应聘者数据
        /// </summary>
        /// <param name="id">文件夹ID</param>
        /// <returns></returns>

        public string appSaveEntry(PlanInputDTO planInputDTO, PersonModel personModel)
        {
            ///应聘者ID
            string userId = "";
            //通过文件夹找到相关应聘者
            var hR_Entry = new HR_Entry();
            //if (!string.IsNullOrEmpty(planInputDTO.userId))
            //{
            //    hR_Entry = this.Db.GetIQueryable<HR_Entry>().AsNoTracking()
            //                      .FirstOrDefault(i => i.F_Id == planInputDTO.userId);
            //}
            //else
            //{
            //    hR_Entry = this.Db.GetIQueryable<HR_Entry>().AsNoTracking()
            //                          .FirstOrDefault(i => i.F_FileId == planInputDTO.id);
            //}
            hR_Entry = this.Db.GetIQueryable<HR_Entry>().AsNoTracking()
                            .FirstOrDefault(i => i.F_FileId == planInputDTO.id);
            if (hR_Entry != null)
            {
                //保存
                hR_Entry.F_FileModel = JsonConvert.SerializeObject(personModel);
                hR_Entry.F_CompanyId = planInputDTO.CompanyId;
                hR_Entry.F_IsFileModel = 1;
                //保存应聘者基本数据
                if (personModel.parsing_result != null && personModel.parsing_result.basic_info != null)
                {
                    var basicInfo = personModel.parsing_result.basic_info;
                    hR_Entry.NameUser = basicInfo.name;
                    hR_Entry.Sex = basicInfo.gender == "女" ? 0 : 1;
                    hR_Entry.F_FileBase64Img = personModel.avatar_data;
                    hR_Entry.IdCardNumber = basicInfo.national_identity_number;
                    //政治面貌
                    hR_Entry.PoliticalLandscape = basicInfo.political_status;
                    //婚姻状况
                    hR_Entry.MaritalStatus = basicInfo.marital_status == "已婚" ? 1 : 0;
                    try
                    {
                        if (!string.IsNullOrEmpty(basicInfo.date_of_birth))
                        {
                            hR_Entry.DirthDate = basicInfo.date_of_birth.ToDateTime();
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                    hR_Entry.IdCardAddress = basicInfo.detailed_location;
                    hR_Entry.NationalInfo = basicInfo.ethnic;
                    if (personModel.parsing_result.contact_info != null)
                    {
                        var contactInfo = personModel.parsing_result.contact_info;
                        hR_Entry.MobilePhone = contactInfo.phone_number;
                        hR_Entry.Email = contactInfo.email;
                        hR_Entry.OfficePhone = contactInfo.home_phone_number;
                    }
                    hR_Entry.HomeAddress = basicInfo.detailed_location;
                    hR_Entry.F_RecordFormalschooling = basicInfo.degree;
                    hR_Entry.EncryptForm();
                    this.Db.Update(hR_Entry);

                    //保存应聘者工作经历
                    List<HR_RecruitWorkExpe> hR_RecruitWorkExpes = new List<HR_RecruitWorkExpe>();
                    if (personModel.parsing_result.work_experience != null && personModel.parsing_result.work_experience.Length > 0)
                    {
                        personModel.parsing_result.work_experience.ForEach((item) =>
                        {
                            if (item.start_time_year == "")
                            {
                                item.start_time_year = DateTime.Now.Year + "";
                            }
                            if (item.start_time_month == "")
                            {
                                item.start_time_month = DateTime.Now.Month + "";
                            }
                            if (item.end_time_year == "")
                            {
                                item.end_time_year = DateTime.Now.Year + "";
                            }
                            if (item.end_time_month == "")
                            {
                                item.end_time_month = DateTime.Now.Month + "";
                            }
                            HR_RecruitWorkExpe hR_RecruitWorkExpe = new HR_RecruitWorkExpe()
                            {
                                F_CompanyName = item.company_name,
                                F_Position = item.job_title,
                                F_Department = item.department,
                                F_StartTime = new DateTime(item.start_time_year.ToInt(), item.start_time_month.ToInt(), 1),
                                F_EndTime = new DateTime(item.end_time_year.ToInt(), item.end_time_month.ToInt(), 1),
                                F_WorkYear = new DateTime(item.start_time_year.ToInt(), item.start_time_month.ToInt(), 1).ToString(),
                                F_UserId = hR_Entry.F_Id,
                                F_BusState = (int)ASKBusState.正常,
                                F_ContactNumber = hR_Entry.MobilePhone,

                                F_Performance = item.description,
                                F_WorkAddress = item.location,
                                F_Responsibilities = item.job_function,
                            };
                            if (_operator != null && !string.IsNullOrEmpty(_operator.UserId))
                            {
                                this.InitEntity(hR_RecruitWorkExpe, _operator);
                            }
                            else
                            {
                                hR_RecruitWorkExpe.F_Id = GuidHelper.GenerateKey();
                                hR_RecruitWorkExpe.F_CreateUserId = "小程序";
                                hR_RecruitWorkExpe.F_CreateDate = DateTime.Now;
                                hR_RecruitWorkExpe.F_CreateUserName = "小程序";
                            }
                            hR_RecruitWorkExpes.Add(hR_RecruitWorkExpe);
                        });
                        if (hR_RecruitWorkExpes.Count > 0)
                        {
                            this.Db.BulkInsert(hR_RecruitWorkExpes);
                        }
                    }
                    //保存应聘者教育背景
                    List<HR_RecruitEduBack> hR_RecruitEduBacks = new List<HR_RecruitEduBack>();
                    if (personModel.parsing_result.education_experience != null && personModel.parsing_result.education_experience.Length > 0)
                    {
                        personModel.parsing_result.education_experience.ForEach((item) =>
                        {
                            HR_RecruitEduBack hR_RecruitEdu = new HR_RecruitEduBack()
                            {
                                GraduatedSchool = item.school_name,
                                Professional = item.major,
                                RecordSchooling = item.degree,
                                GraduationYear = item.end_time_year,
                                F_BusState = (int)ASKBusState.正常,
                                F_StartDate = new DateTime(item.start_time_year.ToInt(), item.start_time_month.ToInt(), 1),
                                F_EndDate = new DateTime(item.end_time_year.ToInt(), item.end_time_month.ToInt(), 1),
                                UserId = hR_Entry.F_Id,
                            };
                            if (_operator != null && !string.IsNullOrEmpty(_operator.UserId))
                            {
                                this.InitEntity(hR_RecruitEdu, _operator);
                            }
                            else
                            {
                                hR_RecruitEdu.F_Id = GuidHelper.GenerateKey();
                                hR_RecruitEdu.F_CreateDate = DateTime.Now;
                                hR_RecruitEdu.F_CreateUserId = "小程序";
                                hR_RecruitEdu.F_CreateUserName = "小程序";
                            }
                            hR_RecruitEduBacks.Add(hR_RecruitEdu);
                        });
                        if (hR_RecruitEduBacks.Count > 0)
                        {
                            this.Db.BulkInsert(hR_RecruitEduBacks);
                        }
                    }

                    //岗位
                    if (!string.IsNullOrEmpty(planInputDTO.PositionId))
                    {
                        var entity = Db.GetIQueryable<HR_Recruit>().Where(i => i.F_Role == planInputDTO.PositionId).FirstOrDefault();
                        if (entity == null)
                        {
                            var postModel = Db.GetIQueryable<Base_Post>().Where(i => i.F_Id == planInputDTO.PositionId).FirstOrDefault();
                            HR_Recruit hR_Recruit = new HR_Recruit()
                            {
                                F_Content = "",
                                F_City = "",
                                F_Address = "",
                                F_CreateDate = DateTime.Now,
                                F_Detail = "",
                                F_Experience = "",
                                F_Role = planInputDTO.PositionId,
                                F_RecruitName = postModel.F_Name,
                                F_Education = postModel.F_Education,
                                F_Achieve = postModel.F_Qualifications,
                                F_BusState = 1,//招聘需求
                            };
                            if (_operator != null && !string.IsNullOrEmpty(_operator.UserId))
                            {
                                this.InitEntity(hR_Recruit, _operator);
                            }
                            else
                            {
                                hR_Recruit.F_Id = GuidHelper.GenerateKey();
                                hR_Recruit.F_CreateDate = DateTime.Now;
                                hR_Recruit.F_CreateUserId = "小程序";
                                hR_Recruit.F_CreateUserName = "小程序";
                            }
                            this.Db.Insert(hR_Recruit);
                            entity = hR_Recruit;
                        }
                        HR_RecruitmentCandidates hR_RecruitmentCandidates = new HR_RecruitmentCandidates()
                        {
                            F_BusState = 0,
                            F_CreateDate = DateTime.Now,
                            F_Id = Guid.NewGuid().ToString("N"),
                            F_RecruitId = entity.F_Id,
                            F_UserId = hR_Entry.F_Id,
                        };
                        if (_operator != null && !string.IsNullOrEmpty(_operator.UserId))
                        {
                            this.InitEntity(hR_RecruitmentCandidates, _operator);
                        }
                        else
                        {
                            hR_RecruitmentCandidates.F_Id = GuidHelper.GenerateKey();
                            hR_RecruitmentCandidates.F_CreateDate = DateTime.Now;
                            hR_RecruitmentCandidates.F_CreateUserId = "小程序";
                            hR_RecruitmentCandidates.F_CreateUserName = "小程序";
                        }
                        this.Db.Insert(hR_RecruitmentCandidates);
                    }

                }

                userId = hR_Entry.F_Id;
            }
            else
            {
                throw new Exception("未找到相关数据！");
            }
            return userId;
        }
        /// <summary>
        /// 简历解析保存应聘者数据
        /// </summary>
        /// <param name="id">文件夹ID</param>
        /// <returns></returns>
        /// 
        [DataAddLog(UserLogType.招聘管理, "id", "上次简历")]
        [Transactional]
        public string SaveEntry(PlanInputDTO planInputDTO, PersonModel personModel, bool isNew = true)
        {
            ///应聘者ID
            string userId = "";
            //通过文件夹找到相关应聘者
            var hR_Entry = new HR_Entry();
            //判断重复提交 
            var entries = this.Db.GetIQueryable<HR_Entry>();
            if (!string.IsNullOrEmpty(planInputDTO.userId))
            {
                hR_Entry = entries.AsNoTracking()
                                  .FirstOrDefault(i => i.F_Id == planInputDTO.userId);
            }
            else
            {
                hR_Entry = entries.AsNoTracking()
                                      .FirstOrDefault(i => i.F_FileId == planInputDTO.id);
                if (hR_Entry.F_IsFileModel.HasValue && hR_Entry.F_IsFileModel == 1)
                {
                    throw new Exception("该简历已解析,请刷新至列表查看！");
                }
            }
            if (hR_Entry != null)
            {
                hR_Entry.F_FileId = planInputDTO.id;
                //保存
                hR_Entry.F_FileModel = JsonConvert.SerializeObject(personModel);
                hR_Entry.F_IsFileModel = 1;
                //保存应聘者基本数据
                if (personModel.parsing_result != null && personModel.parsing_result.basic_info != null)
                {
                    var basicInfo = personModel.parsing_result.basic_info;
                    hR_Entry.NameUser = basicInfo.name;
                    hR_Entry.Sex = basicInfo.gender == "女" ? 0 : 1;
                    hR_Entry.F_FileBase64Img = personModel.avatar_data;
                    hR_Entry.HeadPortrait = ImgHelper.SaveBase64Image(hR_Entry.F_FileBase64Img, hR_Entry.F_Id);
                    hR_Entry.IdCardNumber = basicInfo.national_identity_number;
                    //政治面貌
                    hR_Entry.PoliticalLandscape = basicInfo.political_status;
                    //婚姻状况
                    hR_Entry.MaritalStatus = basicInfo.marital_status == "已婚" ? 1 : 0;
                    try
                    {
                        if (!string.IsNullOrEmpty(basicInfo.date_of_birth))
                        {
                            hR_Entry.DirthDate = basicInfo.date_of_birth.ToDateTime();
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                    hR_Entry.IdCardAddress = basicInfo.detailed_location;
                    hR_Entry.NationalInfo = basicInfo.ethnic;
                    if (personModel.parsing_result.contact_info != null)
                    {
                        var contactInfo = personModel.parsing_result.contact_info;
                        hR_Entry.MobilePhone = contactInfo.phone_number;
                        hR_Entry.Email = contactInfo.email;
                        hR_Entry.OfficePhone = contactInfo.home_phone_number;
                    }
                    hR_Entry.HomeAddress = basicInfo.detailed_location;
                    hR_Entry.F_RecordFormalschooling = basicInfo.degree;
                    if (_operator != null && !string.IsNullOrEmpty(_operator.UserId))
                    {
                        this.UpdateEntity(hR_Entry, _operator);
                    }
                    else
                    {
                        hR_Entry.F_ModifyDate = DateTime.Now;
                        hR_Entry.F_ModifyUserId = "小程序";
                        hR_Entry.F_ModifyUserName = "小程序";
                    };
                    hR_Entry.EncryptForm();
                    this.Db.Update(hR_Entry);

                    //如果是旧简历，则删除后重新添加
                    if (!isNew)
                    {
                        if (!string.IsNullOrEmpty(hR_Entry.F_Id))
                        {
                            Db.Delete<HR_RecruitWorkExpe>(t => t.F_UserId == hR_Entry.F_Id);
                            Db.Delete<HR_RecruitEduBack>(t => t.UserId == hR_Entry.F_Id);
                        }
                    }
                    //保存应聘者工作经历
                    List<HR_RecruitWorkExpe> hR_RecruitWorkExpes = new List<HR_RecruitWorkExpe>();
                    if (personModel.parsing_result.work_experience != null && personModel.parsing_result.work_experience.Length > 0)
                    {
                        personModel.parsing_result.work_experience.ForEach((item) =>
                        {
                            if (item.start_time_year == "")
                            {
                                item.start_time_year = DateTime.Now.Year + "";
                            }
                            if (item.start_time_month == "")
                            {
                                item.start_time_month = DateTime.Now.Month + "";
                            }
                            if (item.end_time_year == "")
                            {
                                item.end_time_year = DateTime.Now.Year + "";
                            }
                            if (item.end_time_month == "")
                            {
                                item.end_time_month = DateTime.Now.Month + "";
                            }
                            HR_RecruitWorkExpe hR_RecruitWorkExpe = new HR_RecruitWorkExpe()
                            {
                                F_CompanyName = item.company_name,
                                F_Position = item.job_title,
                                F_Performance = item.description,
                                F_WorkAddress = item.location,
                                F_Responsibilities = item.job_function,
                                F_Department = item.department,
                                F_UserId = hR_Entry.F_Id,
                                F_BusState = (int)ASKBusState.正常,
                                F_ContactNumber = hR_Entry.MobilePhone,
                            };
                            if (!string.IsNullOrEmpty(item.start_time_year) && !string.IsNullOrEmpty(item.start_time_month))
                            {
                                hR_RecruitWorkExpe.F_StartTime = new DateTime(item.start_time_year.ToInt(), item.start_time_month.ToInt(), 1);
                            }
                            if (!string.IsNullOrEmpty(item.end_time_year) && !string.IsNullOrEmpty(item.end_time_month))
                            {
                                hR_RecruitWorkExpe.F_EndTime = new DateTime(item.end_time_year.ToInt(), item.end_time_month.ToInt(), 1);
                            }
                            if (!string.IsNullOrEmpty(item.start_time_year) && !string.IsNullOrEmpty(item.start_time_month))
                            {
                                hR_RecruitWorkExpe.F_WorkYear = new DateTime(item.start_time_year.ToInt(), item.start_time_month.ToInt(), 1).ToString();
                            }
                            if (_operator != null && !string.IsNullOrEmpty(_operator.UserId))
                            {
                                this.InitEntity(hR_RecruitWorkExpe, _operator);
                            }
                            else
                            {
                                hR_RecruitWorkExpe.F_Id = GuidHelper.GenerateKey();
                                hR_RecruitWorkExpe.F_CreateUserId = "小程序";
                                hR_RecruitWorkExpe.F_CreateDate = DateTime.Now;
                                hR_RecruitWorkExpe.F_CreateUserName = "小程序";
                            }
                            hR_RecruitWorkExpes.Add(hR_RecruitWorkExpe);
                        });
                        if (hR_RecruitWorkExpes.Count > 0)
                        {
                            this.Db.BulkInsert(hR_RecruitWorkExpes);
                        }
                    }
                    //保存应聘者教育背景
                    List<HR_RecruitEduBack> hR_RecruitEduBacks = new List<HR_RecruitEduBack>();
                    if (personModel.parsing_result.education_experience != null && personModel.parsing_result.education_experience.Length > 0)
                    {
                        personModel.parsing_result.education_experience.ForEach((item) =>
                        {
                            if (item.start_time_year == "")
                            {
                                item.start_time_year = DateTime.Now.Year + "";
                            }
                            if (item.start_time_month == "")
                            {
                                item.start_time_month = DateTime.Now.Month + "";
                            }
                            if (item.end_time_year == "")
                            {
                                item.end_time_year = DateTime.Now.Year + "";
                            }
                            if (item.end_time_month == "")
                            {
                                item.end_time_month = DateTime.Now.Month + "";
                            }
                            HR_RecruitEduBack hR_RecruitEdu = new HR_RecruitEduBack()
                            {
                                GraduatedSchool = item.school_name,
                                Professional = item.major,
                                RecordSchooling = item.degree,
                                GraduationYear = item.end_time_year,
                                F_BusState = (int)ASKBusState.正常,
                                UserId = hR_Entry.F_Id,
                            };
                            if (!string.IsNullOrEmpty(item.start_time_year) && !string.IsNullOrEmpty(item.start_time_month))
                            {
                                hR_RecruitEdu.F_StartDate = new DateTime(item.start_time_year.ToInt(), item.start_time_month.ToInt(), 1);
                            }
                            if (!string.IsNullOrEmpty(item.end_time_year) && !string.IsNullOrEmpty(item.end_time_month))
                            {
                                hR_RecruitEdu.F_EndDate = new DateTime(item.end_time_year.ToInt(), item.end_time_month.ToInt(), 1);
                            }
                            if (_operator != null && !string.IsNullOrEmpty(_operator.UserId))
                            {
                                this.InitEntity(hR_RecruitEdu, _operator);
                            }
                            else
                            {
                                hR_RecruitEdu.F_Id = GuidHelper.GenerateKey();
                                hR_RecruitEdu.F_CreateDate = DateTime.Now;
                                hR_RecruitEdu.F_CreateUserId = "小程序";
                                hR_RecruitEdu.F_CreateUserName = "小程序";
                            }
                            hR_RecruitEduBacks.Add(hR_RecruitEdu);
                        });
                        if (hR_RecruitEduBacks.Count > 0)
                        {
                            this.Db.BulkInsert(hR_RecruitEduBacks);
                        }
                    }


                    //岗位
                    if (!string.IsNullOrEmpty(planInputDTO.PositionId))
                    {
                        var entity = Db.GetIQueryable<HR_Recruit>().Where(i => i.F_Role == planInputDTO.PositionId || i.F_Id == planInputDTO.PositionId).FirstOrDefault();
                        if (entity == null)
                        {
                            var postModel = Db.GetIQueryable<Base_Post>().Where(i => i.F_Id == planInputDTO.PositionId).FirstOrDefault();
                            HR_Recruit hR_Recruit = new HR_Recruit()
                            {
                                F_Content = "",
                                F_City = "",
                                F_Address = "",
                                F_CreateDate = DateTime.Now,
                                F_Detail = "",
                                F_Experience = "",
                                F_Role = planInputDTO.PositionId,
                                F_RecruitName = postModel.F_Name,
                                F_Education = postModel.F_Education,
                                F_Achieve = postModel.F_Qualifications,
                                F_BusState = 1,//招聘需求
                            };
                            if (_operator != null && !string.IsNullOrEmpty(_operator.UserId))
                            {
                                this.InitEntity(hR_Recruit, _operator);
                            }
                            else
                            {
                                hR_Recruit.F_Id = GuidHelper.GenerateKey();
                                hR_Recruit.F_CreateDate = DateTime.Now;
                                hR_Recruit.F_CreateUserId = "小程序";
                                hR_Recruit.F_CreateUserName = "小程序";
                            }
                            this.Db.Insert(hR_Recruit);
                            entity = hR_Recruit;
                        }
                        HR_RecruitmentCandidates hR_RecruitmentCandidates = new HR_RecruitmentCandidates()
                        {
                            F_BusState = 0,
                            F_CreateDate = DateTime.Now,
                            F_Id = Guid.NewGuid().ToString("N"),
                            F_RecruitId = entity.F_Id,
                            F_UserId = hR_Entry.F_Id,
                            F_IsInvitedInt = 0,
                            F_Through = 0,
                        };
                        if (_operator != null && !string.IsNullOrEmpty(_operator.UserId))
                        {
                            this.InitEntity(hR_RecruitmentCandidates, _operator);
                        }
                        else
                        {
                            hR_RecruitmentCandidates.F_Id = GuidHelper.GenerateKey();
                            hR_RecruitmentCandidates.F_CreateDate = DateTime.Now;
                            hR_RecruitmentCandidates.F_CreateUserId = "小程序";
                            hR_RecruitmentCandidates.F_CreateUserName = "小程序";
                        }
                        this.Db.Insert(hR_RecruitmentCandidates);
                    }

                }

                userId = hR_Entry.F_Id;
                ///改变文件的状态
                if (!string.IsNullOrEmpty(planInputDTO.fileid))
                {
                    var fileInfo = this.GetEntity(planInputDTO.fileid);
                    if (fileInfo != null)
                    {
                        fileInfo.F_BusState = 0;
                    }
                    this.Db.Update(fileInfo);
                }

            }
            else
            {
                throw new Exception("未找到相关数据！");
            }
            return userId;
        }

        //获取文件
        public FileModel GetFiles(string fileFolderId, string fileId)
        {
            var fileList = new FileModel();
            if (!string.IsNullOrWhiteSpace(fileId))
            {
                fileList.Files = GetIQueryable().Where(i => i.F_Id == fileId&&i.F_DeleteMark!=1).ToList();
                return fileList;
            }
            else
            {
                if (!string.IsNullOrWhiteSpace(fileFolderId))
                {
                    fileList.F_Id = fileFolderId;
                    fileList.Files = GetIQueryable().Where(i => i.F_FileFolderId == fileFolderId && i.F_DeleteMark != 1).ToList();
                    return fileList;
                }
            }
            return fileList;
        }
        //下载文件
        public FileModel DownLoadFilesAsync(string fileId)
        {
            var fileList = new FileModel();
            if (!string.IsNullOrWhiteSpace(fileId))
            {
                fileList.Files = GetIQueryable().Where(i => i.F_Id == fileId).ToList();
                return fileList;
            }
            return fileList;
        }
        /// <summary>
        /// 获取文件信息
        /// </summary>
        /// <param name="fileFolderId"></param>
        /// <returns></returns>
        public string GetFileInfo(string fileFolderId)
        {
            var fileFolderInfo = Db.GetIQueryable<Base_FileFolder>().FirstOrDefault(i => i.F_Id == fileFolderId);
            if (fileFolderInfo != null)
            {
                var fileInfo = Db.GetIQueryable<Base_FileInfo>().FirstOrDefault(i => i.F_FileFolderId == fileFolderInfo.F_Id);
                if (fileInfo != null)
                {
                    return fileInfo.F_FilePath;
                }
            }
            return "";
        }
        /// <summary>
        /// 判断是否存在该文件
        /// </summary>
        /// <param name="Md5Text">哈希Md5字符</param>
        /// <returns></returns>
        public bool isExist(string Md5Text, FileTypes? fileTypes = null)
        {
            var existFile = false;
            var base_FileInfos = this.GetIQueryable().Where(i => i.F_FileHash == Md5Text && i.F_BusState == 0).ToList();
            if (base_FileInfos.Count > 0 && fileTypes.HasValue)
            {
                var fileInfo = base_FileInfos.Find(i => i.F_LocationType == (int)fileTypes.Value);
                existFile = fileInfo != null ? true : false;
            }
            return existFile;
        }

        #endregion

        #region 私有成员

        #endregion
    }
}