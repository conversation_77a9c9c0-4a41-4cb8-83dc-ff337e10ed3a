﻿using Coldairarrow.Business.WorkWx_Robot;
using Coldairarrow.Entity.WorkWx_Robot;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using System.Net;
using System.Text;
using Newtonsoft.Json;
using Coldairarrow.Util.Helper;
using System.Net.Http;
using Newtonsoft.Json.Linq;

namespace Coldairarrow.Api.Controllers.WorkWx_Robot
{
    [Route("/WorkWx_Robot/[controller]/[action]")]
    public class WorkWx_MessageController : BaseApiController
    {
        #region DI

        public WorkWx_MessageController(IWorkWx_MessageBusiness workWx_MessageBus, IHttpClientFactory clientFactory)
        {
            _workWx_MessageBus = workWx_MessageBus;
            _httpClientFactory = clientFactory;
        }

        IWorkWx_MessageBusiness _workWx_MessageBus { get; }
        IHttpClientFactory _httpClientFactory;

        /// <summary>
        /// ACCESS_TOKEN最后一次更新时间,初始值为当前2小时前
        /// </summary>
        static DateTime _lastGetTimeOfAccessToken = DateTime.Now.AddSeconds(-7200);

        /// <summary>
        /// 存储微信访问凭证
        /// </summary>
        static string _AccessToken;

       
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<WorkWx_Message>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _workWx_MessageBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<WorkWx_Message> GetTheData(IdInputDTO input)
        {
            return await _workWx_MessageBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(WorkWx_Message data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _workWx_MessageBus.AddDataAsync(data);
            }
            else
            {
                await _workWx_MessageBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _workWx_MessageBus.DeleteDataAsync(ids);
        }

        #endregion
        

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _workWx_MessageBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<WorkWx_Message>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _workWx_MessageBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }


        ///1.获取access_token
        private string GetAccessToken()
        {
            try
            {
                var time = _lastGetTimeOfAccessToken.AddSeconds(7200);
                if (time <= DateTime.Now)
                {
                    //当前时间比上次获取时间超了7200s后执行
                    _lastGetTimeOfAccessToken = DateTime.Now;
                    string corpid = "wwe9e87379e762b7fa"; //企业id，是固定的
                    string secret = "jOJGsDc0YQmfLCDuG_ePi1rX-CQ24Lt-HR8x2RgeMXE";  //每个项目不一样
                    //string corpid = "ww768a758c0fddf27b"; //企业id，是固定的
                    //string secret = "DzLD4-JnOOZ0cUHq-igH7tGWbi0611jg4DVt3eDJbQ4";  //每个项目不一样
                    string strUrl = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=" + corpid + "&corpsecret=" + secret;
                    /*
                                       正确的Json返回示例:
                       {
                         "errcode": 0,
                         "errmsg": "ok",
                         "access_token": "accesstoken000001",
                          "expires_in": 7200
                       }
                    */
                    HttpWebRequest req = (HttpWebRequest)HttpWebRequest.Create(strUrl);  //用GET形式请求指定的地址
                    req.Method = "GET";
                    using (WebResponse wr = req.GetResponse())
                    {
                        //HttpWebResponse myResponse = (HttpWebResponse)req.GetResponse();  
                        StreamReader reader = new StreamReader(wr.GetResponseStream(), Encoding.UTF8);
                        string content = reader.ReadToEnd();
                        reader.Close();
                        reader.Dispose();
                        //在这里对Access_token 赋值  
                        Access_token token = new Access_token();
                        token = JsonConvert.DeserializeObject<Access_token>(content);
                        _AccessToken = token.access_token;
                    }

                }
                return _AccessToken;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        
        ///获取token
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetToken(string id)
        {
            try
            {
                var accessToken = String.Empty;
                if (id=="lilz")
                {
                     accessToken = GetAccessToken();
                }
                return Success(accessToken);

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        //2.发送并存储发送记录
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult SendMessage(string user, string content,string formsystem)
        {
            try
            {
                var data = new WorkWx_Message { 
                    F_Content = content,
                    F_User  = user,
                    F_Msgtype = "text",
                    F_CreateUserName = formsystem
                };
                InitEntity(data);
                var accessToken = GetAccessToken();
                var result = sendTextMsg(accessToken, user, content);
                data.F_JsonStr = result;
                var resultObj = JsonConvert.DeserializeObject<WorkWx_RobotResult>(result);
                if (resultObj.errcode == 0)
                {
                    data.F_Result = 1;
                    data.F_ResultJson = resultObj.errmsg;
                    _workWx_MessageBus.AddDataAsync(data).Wait();
                    return Success("发送成功");
                }
                else
                {
                    data.F_Result = 0;
                    data.F_ResultJson = resultObj.errmsg;
                    _workWx_MessageBus.AddDataAsync(data).Wait();
                    return Error("发送失败");
                }
              
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 提供给OPM的发送
        /// </summary>
        /// <param name="user"></param>
        /// <param name="title"></param>
        /// <param name="content"></param>
        /// <param name="url"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult SendOPMMessage(string user, string title, string content,string url,string party)
        {
            try
            {
                var data = new WorkWx_Message
                {
                    F_Content = content,
                    F_Title = title,
                    F_User = user,
                    F_Msgtype = "textcard",
                };
                if (url.IsNullOrEmpty())
                {
                    url = "https://cms.cqlandmark.com";
                }
                InitEntity(data);
                var accessToken = GetAccessToken();
                var result = sendTextcardMsg(accessToken, user, title, content, url, party);
                data.F_JsonStr = result;
                var resultObj = JsonConvert.DeserializeObject<WorkWx_RobotResult>(result);
                if (resultObj.errcode == 0)
                {
                    data.F_Result = 1;
                    data.F_ResultJson = resultObj.errmsg;
                    _workWx_MessageBus.AddDataAsync(data).Wait();
                    return Success("发送成功");
                }
                else
                {
                    data.F_Result = 0;
                    data.F_ResultJson = resultObj.errmsg;
                    _workWx_MessageBus.AddDataAsync(data).Wait();
                    return Error("发送失败");
                }

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 新的接收方式
        /// </summary>
        /// <param name="user"></param>
        /// <param name="title"></param>
        /// <param name="content"></param>
        /// <param name="url"></param>
        /// <param name="party"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult SendOPMMessageNew()
        {
            try
            {
                string user = HttpContext.Request.Form["user"].ToString();
                string title = HttpContext.Request.Form["title"].ToString();
                string content = HttpContext.Request.Form["content"].ToString();
                string url = HttpContext.Request.Form["url"].ToString();
                string party = HttpContext.Request.Form["party"].ToString();
                var data = new WorkWx_Message
                {
                    F_Content = content,
                    F_Title = title,
                    F_User = user,
                    F_Msgtype = "textcard",
                };
                if (url.IsNullOrEmpty())
                {
                    url = "https://cms.cqlandmark.com";
                }
                InitEntity(data);
                var accessToken = GetAccessToken();
                var result = sendTextcardMsg(accessToken, user, title, content, url, party);
                data.F_JsonStr = result;
                var resultObj = JsonConvert.DeserializeObject<WorkWx_RobotResult>(result);
                if (resultObj.errcode == 0)
                {
                    data.F_Result = 1;
                    data.F_ResultJson = resultObj.errmsg;
                    _workWx_MessageBus.AddDataAsync(data).Wait();
                    return Success("发送成功");
                }
                else
                {
                    data.F_Result = 0;
                    data.F_ResultJson = resultObj.errmsg;
                    _workWx_MessageBus.AddDataAsync(data).Wait();
                    return Error("发送失败");
                }

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 修改群聊名称
        /// </summary>
        /// <param name="user"></param>
        /// <param name="title"></param>
        /// <param name="content"></param>
        /// <param name="url"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult UpdateName(string chatid, string name)
        {
            try
            {
                var accessToken = GetAccessToken();
                LogHelper.WriteLog_LocalTxt(chatid+"和"+name);
                var parm = new
                {
                    chatid = chatid,
                    name = name
                };
                var data = JsonConvert.SerializeObject(parm);
                var myUrl = $"https://qyapi.weixin.qq.com/cgi-bin/appchat/update?access_token=" + accessToken;
                //var myUrl = robotUrl;
                string gethtml = MyHttpHelper.HttpPost(myUrl, data);
                return Success(gethtml);

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private string sendTextMsg(string ACCESS_TOKEN, string user, string content)
        {
            var workwxText = new WorkWxText
            {
                content = content
            };
            var parm = new 
            {
                text = workwxText,
                msgtype = "text",
                agentid = "1000076",
                touser = user
            };
            var data = JsonConvert.SerializeObject(parm);
            var myUrl = $"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token="+ ACCESS_TOKEN;
            //var myUrl = robotUrl;
            string gethtml = MyHttpHelper.HttpPost(myUrl, data);
            return gethtml;
        }
        private string sendTextcardMsg(string ACCESS_TOKEN, string user, string title,string content ,string url,string party)
        {
            var workwxText = new WrokArticles
            {
                description = content,
                title = title,
                url = url,
            };
            var parm = new
            {
                textcard = workwxText,
                msgtype = "textcard",
                agentid = "1000076",
                touser = user,
                toparty = party
            };
            var data = JsonConvert.SerializeObject(parm);
            var myUrl = $"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + ACCESS_TOKEN;
            //var myUrl = robotUrl;
            string gethtml = MyHttpHelper.HttpPost(myUrl, data);
            return gethtml;
        }
        /// <summary>
        /// 发送图片地址
        /// </summary>
        /// <param name="user"></param>
        /// <param name="title"></param>
        /// <param name="url"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult SendPhotoMessage(string user, string title, string fileurl)
        {
            try
            {
                //1.初始化
                var data = new WorkWx_Message
                {
                    F_Content = fileurl,
                    F_Title = title,
                    F_User = user,
                    F_Msgtype = "image",
                };
                InitEntity(data);
                var ACCESS_TOKEN = GetAccessToken();
                //2.调用上传临时素材接口
                string result = HttpClientPostPhoto(ACCESS_TOKEN, fileurl, title);
                var resultjSON = JsonConvert.DeserializeObject<JObject>(result);
                var media_id = resultjSON["media_id"].ToString();
                ///3.构造发送企业微信
                var parm = new
                {
                    msgtype = "image",
                    agentid = "1000076",
                    touser = user,
                    image = new
                    {
                        media_id = media_id
                    },
                    safe = 0,
                    enable_duplicate_check= 0 ,
                    duplicate_check_interval = 1800
                };
                var parmdata = JsonConvert.SerializeObject(parm);
                var myUrl = $"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + ACCESS_TOKEN;
                //var myUrl = robotUrl;
                string gethtml = MyHttpHelper.HttpPost(myUrl, parmdata);
                data.F_JsonStr = gethtml;
                var resultObj = JsonConvert.DeserializeObject<WorkWx_RobotResult>(gethtml);
                if (resultObj.errcode == 0)
                {
                    data.F_Result = 1;
                    data.F_ResultJson = resultObj.errmsg;
                    _workWx_MessageBus.AddDataAsync(data).Wait();
                    return Success("发送成功");
                }
                else
                {
                    data.F_Result = 0;
                    data.F_ResultJson = resultObj.errmsg;
                    _workWx_MessageBus.AddDataAsync(data).Wait();
                    return Error("发送失败");
                }

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// 发送图文消息
        /// </summary>
        /// <param name="user"></param>
        /// <param name="title"></param>
        /// <param name="fileurl"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult SendMPNewsMessage(string user, string title, string content,string fileurl)
        {
            try
            {
                //1.初始化
                var data = new WorkWx_Message
                {
                    F_Content = fileurl,
                    F_Title = title,
                    F_User = user,
                    F_Msgtype = "news",
                };
                InitEntity(data);
                var ACCESS_TOKEN = GetAccessToken();
                //2.调用上传临时素材接口
                string result = HttpClientPostPhoto(ACCESS_TOKEN, fileurl, title);
                var resultjSON = JsonConvert.DeserializeObject<JObject>(result);
                var media_id = resultjSON["media_id"].ToString();
                // 构造参数
                var article = new WrokMpNews{
                    title = title,
                    content = content,
                    thumb_media_id = media_id,
                    digest = content
                };
                WrokMpNews[] articles = { article };
                ///3.构造发送企业微信
                var parm = new
                {
                    msgtype = "mpnews",
                    agentid = "1000076",
                    touser = user,
                    mpnews = new {
                       articles = articles
                    },
                    safe = 0,
                    enable_duplicate_check = 0,
                    duplicate_check_interval = 1800
                };
                var parmdata = JsonConvert.SerializeObject(parm);
                var myUrl = $"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + ACCESS_TOKEN;
                //var myUrl = robotUrl;
                string gethtml = MyHttpHelper.HttpPost(myUrl, parmdata);
                data.F_JsonStr = gethtml;
                var resultObj = JsonConvert.DeserializeObject<WorkWx_RobotResult>(gethtml);
                if (resultObj.errcode == 0)
                {
                    data.F_Result = 1;
                    data.F_ResultJson = resultObj.errmsg;
                    _workWx_MessageBus.AddDataAsync(data).Wait();
                    return Success("发送成功");
                }
                else
                {
                    data.F_Result = 0;
                    data.F_ResultJson = resultObj.errmsg;
                    _workWx_MessageBus.AddDataAsync(data).Wait();
                    return Error("发送失败");
                }

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// 向企业微信临时素材提交图片数据
        /// </summary>
        /// <param name="requestUrl">企微的请求地址</param>
        /// <param name="bmpBytes">图片字节流</param>
        /// <param name="fileName">图片名称,下载的时候就展示的名字</param>
        /// <returns>返回mediaid</returns>
        private string HttpClientPostPhoto(string ACCESS_TOKEN, string fileurl, string fileName)
        {
            string requestUrl = "https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token=" + ACCESS_TOKEN + "&type=image";
            byte[] bmpBytes = ImgToByt(fileurl);
            var _httpClient = _httpClientFactory.CreateClient();
            var boundary = DateTime.Now.Ticks.ToString("X");
            _httpClient.DefaultRequestHeaders.Remove("Expect");
            _httpClient.DefaultRequestHeaders.Remove("Connection");
            using (var content = new MultipartFormDataContent(boundary))
            {
                content.Headers.Remove("Content-Type");
                content.Headers.TryAddWithoutValidation("Content-Type", "multipart/form-data; boundary=" + boundary);
                using (var contentByte = new ByteArrayContent(bmpBytes))
                {

                    content.Add(contentByte);
                    contentByte.Headers.Remove("Content-Disposition");
                    contentByte.Headers.TryAddWithoutValidation("Content-Disposition", $"form-data; name=\"media\";filename=\"{fileName}\"" + "");
                    contentByte.Headers.Remove("Content-Type");
                    contentByte.Headers.TryAddWithoutValidation("Content-Type", "image/png");
                    try
                    {
                        return _httpClient.PostAsync(requestUrl, content).Result.Content.ReadAsStringAsync().Result;
                    }
                    catch (Exception ex)
                    {
                        return ex.Message;
                    }
                }
            }
        }


        /// <summary>
        /// 向企业微信临时素材提交图片数据
        /// </summary>
        /// <param name="requestUrl">企微的请求地址</param>
        /// <param name="bmpBytes">图片字节流</param>
        /// <param name="fileName">图片名称,下载的时候就展示的名字</param>
        /// <returns>返回mediaid</returns>
        private string HttpClientPostFile(string ACCESS_TOKEN, string fileurl, string fileName)
        {
            string requestUrl = "https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token=" + ACCESS_TOKEN + "&type=file";
            byte[] bmpBytes = ImgToByt(fileurl);
            var _httpClient = _httpClientFactory.CreateClient();
            var boundary = DateTime.Now.Ticks.ToString("X");
            _httpClient.DefaultRequestHeaders.Remove("Expect");
            _httpClient.DefaultRequestHeaders.Remove("Connection");
            using (var content = new MultipartFormDataContent(boundary))
            {
                content.Headers.Remove("Content-Type");
                content.Headers.TryAddWithoutValidation("Content-Type", "multipart/form-data; boundary=" + boundary);
                using (var contentByte = new ByteArrayContent(bmpBytes))
                {

                    content.Add(contentByte);
                    contentByte.Headers.Remove("Content-Disposition");
                    contentByte.Headers.TryAddWithoutValidation("Content-Disposition", $"form-data; name=\"media\";filename=\"{fileName}\"" + "");
                    contentByte.Headers.Remove("Content-Type");
                    contentByte.Headers.TryAddWithoutValidation("Content-Type", "image/png");
                    try
                    {
                        return _httpClient.PostAsync(requestUrl, content).Result.Content.ReadAsStringAsync().Result;
                    }
                    catch (Exception ex)
                    {
                        return ex.Message;
                    }
                }
            }
        }

        /// <summary>
        /// 图片转换成字节流
        /// </summary>
        /// <param name="img">要转换的Image对象</param>
        /// <returns>转换后返回的字节流</returns>
        public static byte[] ImgToByt(string imagePath )
        {
            FileStream files = new FileStream(imagePath, FileMode.Open);
            byte[] imgByte = new byte[files.Length];
            files.Read(imgByte, 0, imgByte.Length);
            files.Close();
            return imgByte;
        }
        #endregion
    }
}