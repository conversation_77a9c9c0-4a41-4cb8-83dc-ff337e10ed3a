﻿<template>
  <a-modal
    :title="title"
    width="40%"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="()=>{this.visible=false}"
  >
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="entity" :rules="rules" v-bind="layout">
        <a-form-model-item label="F_Id" prop="F_Id">
          <a-input v-model="entity.F_Id" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="创建时间" prop="F_CreateDate">
          <a-input v-model="entity.F_CreateDate" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="创建人Id" prop="F_CreateUserId">
          <a-input v-model="entity.F_CreateUserId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="创建人" prop="F_CreateUserName">
          <a-input v-model="entity.F_CreateUserName" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_ContentId" prop="F_ContentId">
          <a-input v-model="entity.F_ContentId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="F_Action" prop="F_Action">
          <a-input v-model="entity.F_Action" autocomplete="off" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      layout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 18 }
      },
      visible: false,
      loading: false,
      entity: {},
      rules: {},
      title: ''
    }
  },
  methods: {
    init() {
      this.visible = true
      this.entity = {}
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    openForm(id, title) {
      this.init()

      if (id) {
        this.loading = true
        this.$http.post('/S_School/S_ContentRead/GetTheData', { id: id }).then(resJson => {
          this.loading = false

          this.entity = resJson.Data
        })
      }
    },
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.loading = true
        this.$http.post('/S_School/S_ContentRead/SaveData', this.entity).then(resJson => {
          this.loading = false

          if (resJson.Success) {
            this.$message.success('操作成功!')
            this.visible = false

            this.parentObj.getDataList()
          } else {
            this.$message.error(resJson.Msg)
          }
        })
      })
    }
  }
}
</script>
