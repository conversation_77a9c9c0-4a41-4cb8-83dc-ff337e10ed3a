﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System.Linq.Expressions;
using System;
using AutoMapper;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.Enum;
using Coldairarrow.Entity;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_InterviewPlanBusiness : BaseBusiness<HR_InterviewPlan>, IHR_InterviewPlanBusiness, ITransientDependency
    {
        readonly IMapper _mapper;
        IHR_ContentOperationLogBusiness _ContentOperationLogBusiness;
        public HR_InterviewPlanBusiness(IDbAccessor db, IMapper mapper, IHR_ContentOperationLogBusiness ContentOperationLogBusiness)
            : base(db)
        {
            _mapper = mapper;
            _ContentOperationLogBusiness = ContentOperationLogBusiness;
        }

        #region 外部接口

        public async Task<PageResult<HR_InterviewPlanDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            try
            {


                Expression<Func<HR_InterviewPlan, HR_RecruitmentCandidates, HR_Entry, Base_Post, HR_Recruit, HR_InterviewPlanDTO>> select = (t, c, a, b, g) => new HR_InterviewPlanDTO
                {
                    ApplicantName = a.NameUser,
                    //Entry = a,
                    Entry_Id = a.F_Id,
                    Post = b,
                    //PostName = b.F_Name,//获取岗位名称
                    PostName = g.F_RecruitName,//获取招聘信息名称
                    PhoneNumber = a.MobilePhoneStr,
                };
                select = select.BuildExtendSelectExpre();
                var q = from t in this.Db.GetIQueryable<HR_InterviewPlan>().AsExpandable()
                        join c in this.Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => i.F_Through == 0 && i.F_IsGiveUpInterview == 0&&i.F_IsDelete!=1) on t.F_RecCanId equals c.F_Id
                        join a in this.Db.GetIQueryable<HR_Entry>() on t.F_ApplicantId equals a.F_Id into rec
                        from a in rec.DefaultIfEmpty()
                        join b in this.Db.GetIQueryable<Base_Post>() on t.F_PostId equals b.F_Id into retb
                        from b in retb.DefaultIfEmpty()
                        join d in this.Db.GetIQueryable<HR_Recruit>() on b.F_Id equals d.F_Role into recrd
                        from g in recrd.DefaultIfEmpty()
                        select @select.Invoke(t, c, a, b, g);
                var where = LinqHelper.True<HR_InterviewPlanDTO>();
                var search = input.Search;

                q = q.Where(i => i.F_IsPassingInter == 0 && i.F_IsGiveUpInterview == 0);
                q = q.Where(i => !i.F_IsDelete.HasValue || i.F_IsDelete == 0);
                //筛选
                if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
                {
                    var newWhere = DynamicExpressionParser.ParseLambda<HR_InterviewPlanDTO, bool>(
                        ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                    where = where.And(newWhere);
                }
                if (input.PostId?.Count > 0)
                {
                    q = q.Where(i => input.PostId.Contains(i.F_PostId));
                }
                if (input.RoundInterview?.Count > 0)
                {
                    var interList = input.RoundInterview.Select(s => Convert.ToInt32(s)).ToList();
                    q = q.Where(i => interList.Contains(i.F_RoundInterview.Value));
                }
                if (input.IsInterview?.Count > 0)
                {
                    var interList = input.IsInterview.Select(s => s == "是" ? 1 : 0).ToList();
                    q = q.Where(i => interList.Contains(i.F_IsInterview.HasValue ? i.F_IsInterview.Value : 0));
                }
                var qToList = q.ToList();
                var tempx = (from d in qToList
                             group d by d.F_ApplicantId into g
                             select new
                             {
                                 F_RoundInterview = g.Max(x => x.F_RoundInterview),
                                 ApplicantId = g.Key,
                             }).ToList();
                var tempids = (from a in qToList
                               join aq in tempx on a.F_ApplicantId equals aq.ApplicantId
                               where aq.F_RoundInterview == a.F_RoundInterview
                               select new
                               {
                                   Id = a.F_Id
                               }).ToList();
                var groupids = tempids.Select(s => s.Id);
                q = q.Where(q => groupids.Contains(q.F_Id));
                var temp = await q.Where(where).GetPageResultAsync(input);

                var planIds = temp.Data.Select(s => s.F_Id);
                var evaluation = Db.GetIQueryable<HR_InterviewEvaluation>().Where(i => planIds.Contains(i.F_PlanId)).ToList();
                List<String> userIds = new List<string>();
                foreach (var item in evaluation)
                {
                    var listIds = item.F_InterviewerIdAll.Split(new Char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                    userIds.AddRange(listIds);
                }
                userIds = userIds.Distinct().ToList();
                var userList = Db.GetIQueryable<HR_FormalEmployees>().Where(i => userIds.Contains(i.F_Id)).ToList();
                foreach (var plan in temp.Data)
                {
                    var eval = evaluation.Where(i => i.F_PlanId == plan.F_Id);
                    List<string> listIds = new List<string>();
                    foreach (var item in eval)
                    {
                        var ids = item.F_InterviewerIdAll.Split(new Char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                        listIds.AddRange(ids);
                    }
                    var users = userList.Where(i => listIds.Contains(i.F_Id)).Select(s => s.NameUser).ToList();
                    if (users.Count > 0)
                    {
                        plan.Interviewer = string.Join(",", users.ToArray());
                    }
                }
                return temp;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public HR_InterviewPlanDTO GetTheData(string id)
        {
            HR_InterviewPlanDTO retValue = null;

            HR_InterviewPlan entity = GetEntity(id);
            if (entity != null)
            {
                retValue = _mapper.Map<HR_InterviewPlanDTO>(entity);
                HR_Entry entry = Db.GetIQueryable<HR_Entry>().Where(x => x.F_Id == entity.F_ApplicantId).Select(p => new HR_Entry() { F_Id = p.F_Id, NameUser = p.NameUser }).FirstOrDefault();
                if (entry != null)
                {
                    retValue.Entry_Id = entry.F_Id;
                    retValue.ApplicantName = entry.NameUser;
                }

                var model = Db.GetIQueryable<HR_Interviewer>().Where(i => i.F_InterviewPId == entity.F_InterviewPId);
                if (model.Count() > 0)
                {

                    var list = model.Select(s => s.F_UserId);
                    var temp = Db.GetIQueryable<HR_FormalEmployees>().Where(i => list.Contains(i.F_Id)).Select(i => i.NameUser);
                    retValue.Interviewer = string.Join(",", temp.ToArray());
                }

            }

            return retValue;
        }
        public async Task<HR_InterviewPlan> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        [DataAddLog(UserLogType.招聘管理, "F_InterviewName", "安排面试")]
        [Transactional]
        public async Task AddDataAsync(HR_InterviewPlan data)
        {
            data.F_IsDelete = 0;
            if (data.F_RoundInterview == 1)
            {
                var lind = Db.GetIQueryable<HR_InterviewProcess>().FirstOrDefault(i => i.F_PostId == data.F_PostId && string.IsNullOrEmpty(i.F_ParentId));
                if (lind == null)
                {
                    throw new BusException("该岗位还没有设置面试流程");
                }
                else
                {
                    data.F_InterviewPId = lind.F_Id;
                }
            }
            if (!string.IsNullOrEmpty(data.F_InterviewPId) && data.F_RoundInterview > 1)
            {
                var lmodel = Db.GetIQueryable<HR_InterviewProcess>().FirstOrDefault(i => i.F_ParentId == data.F_InterviewPId);
                if (lmodel == null)
                {
                    throw new BusException("该岗位流程没有第 " + data.F_RoundInterview + " 轮面试");
                }
                else
                {
                    data.F_InterviewPId = lmodel.F_Id;
                }
            }
            var model = Db.GetIQueryable<HR_RecruitmentCandidates>().FirstOrDefault(i => i.F_Id == data.F_RecCanId);
            if (model != null)
            {
                model.F_IsInvitedInt = 1;
                model.F_Through = 0;
                Db.Update<HR_RecruitmentCandidates>(model);
            }
            var planModel = Db.GetIQueryable<HR_InterviewPlan>().FirstOrDefault(i => i.F_Id == data.F_PId);
            if (planModel != null)
            {
                planModel.F_IsPassingInter = 3;
                Db.Update<HR_InterviewPlan>(planModel);
            }

            var linList = Db.GetIQueryable<HR_Interviewer>().Where(i => i.F_InterviewPId == data.F_InterviewPId).Select(s => s.F_UserId).ToList();
            var interviewerIdAll = "";
            //if (linList.Count()>0)
            //{
            //    interviewerIdAll= string.Join(",", linList.ToArray()); 
            //}
            List<HR_InterviewEvaluation> hR_Interviews = new List<HR_InterviewEvaluation>();
            foreach (var item in linList)
            {
                HR_InterviewEvaluation evaluation = new HR_InterviewEvaluation()
                {
                    F_ApplicantId = data.F_ApplicantId,
                    F_BusState = 0,
                    F_CreateDate = data.F_CreateDate,
                    F_CreateUserId = data.F_CreateUserId,
                    F_CreateUserName = data.F_CreateUserName,
                    F_Id = Guid.NewGuid().ToString("N"),
                    F_InterviewerId = data.F_InterviewPId,
                    F_InterviewerIdAll = item,
                    F_IsThrough = 0,
                    F_PlanId = data.F_Id,
                    F_WFState = 0,
                };
                hR_Interviews.Add(evaluation);
            }

            Db.Insert<HR_InterviewEvaluation>(hR_Interviews);
            data.F_IsInterview = 0;
            await Db.InsertAsync(data);
        }
        [DataEditLog(UserLogType.招聘管理, "F_InterviewName", "安排面试")]
        public async Task UpdateDataAsync(HR_InterviewPlan data)
        {
            await UpdateAsync(data);
        }
        [DataDeleteJsonLog(UserLogType.招聘管理, "F_InterviewName", "安排面试")]
        public async Task DeleteDataAsync(List<string> ids)
        {
            var data = Db.GetIQueryable<HR_InterviewPlan>().Where(i => ids.Contains(i.F_Id)).ToList();
            List<HR_RecruitmentCandidates> hR_Recruitments = new List<HR_RecruitmentCandidates>();
            foreach (var item in data)
            {
                item.F_IsDelete = 1;
                hR_Recruitments.AddRange(Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => i.F_UserId == item.F_ApplicantId).ToList());
            }
            if (hR_Recruitments.Count > 0)
            {
                foreach (var item in hR_Recruitments)
                {
                    item.F_BusState = 1;
                    item.F_IsInvitedInt = 0;
                }
                this.Db.Update(hR_Recruitments);
            }

            var planIds = data.Select(i => i.F_Id).ToList();

            var dataEva = Db.GetIQueryable<HR_InterviewEvaluation>().Where(i => planIds.Contains(i.F_PlanId)).ToList();
            foreach (var item in dataEva)
            {
                item.F_IsDelete = 1;
            }
            await Db.UpdateAsync<HR_InterviewEvaluation>(dataEva);
            //await UpdateAsync(data);
            await DeleteAsync(ids);
            //await DeleteAsync(ids);
        }
        [DataEditJsonLog(UserLogType.招聘管理, "ids", "安排面试->放弃面试")]
        public async Task GiveUpInterview(List<string> ids)
        {
            var data = Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => ids.Contains(i.F_Id)).ToList();
            //var postId = data.Select(i => i.F_PostId).ToList();
            //var PostList = Db.GetIQueryable<HR_Recruit>().Where(i => postId.Contains(i.F_Role)).ToList();
            List<HR_ContentOperationLog> hR_ContentOperationLogs = new List<HR_ContentOperationLog>();
            if (data.Count > 0)
            {
                foreach (var item in data)
                {
                    item.F_IsGiveUpInterview = (int)RecruitType.IsGiveUpInterviewType.GUinterview;
                    //var hR_Recruit = PostList.FirstOrDefault(i => i.F_Role == item.F_PostId);
                    //var name = hR_Recruit != null ? hR_Recruit.F_RecruitName : "";
                    item.F_Label += EnumHelper.GetEnumDescription(RecruitType.TagType.应聘者放弃面试);
                    item.F_LabelOperationTime = DateTime.Now;
                    var model = Db.GetIQueryable<HR_InterviewPlan>().Where(i => i.F_IsDelete == 0 && i.F_ApplicantId == item.F_UserId).OrderByDescending(i => i.F_RoundInterview).FirstOrDefault();
                    if (model != null)
                    {
                        model.F_IsGiveUpInterview = (int)RecruitType.IsGiveUpInterviewType.GUinterview;
                        await Db.UpdateAsync<HR_InterviewPlan>(model);
                    }
                    //保存标签
                    HR_ContentOperationLog hR_ContentOperation = new HR_ContentOperationLog()
                    {
                        F_BusState = (int)ASKBusState.正常,
                        F_UserId = item.F_UserId,
                        F_RelationalID = item.F_Id,
                        F_RelationalType = (int)RecruitType.RelationalType.正常,
                        F_TagContent = EnumHelper.GetEnumDescription(RecruitType.TagType.应聘者放弃面试),
                        F_TagType = (int)RecruitType.TagType.应聘者放弃面试
                    };
                    hR_ContentOperationLogs.Add(hR_ContentOperation);
                }
                await _ContentOperationLogBusiness.SaveDataAsync(hR_ContentOperationLogs);
                await Db.UpdateAsync<HR_RecruitmentCandidates>(data);
            }
        }
        /// <summary>
        /// 暂停面试
        /// </summary>
        /// <param name="recruitId">应聘关系表id</param>
        /// <param name="f_id">计划id</param>
        /// <returns></returns>
        [DataEditJsonLog(UserLogType.招聘管理, "ids", "安排面试->暂停面试")]
        public async Task PauseInterview(List<string> ids)
        {
            var data = Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => ids.Contains(i.F_Id)).ToList();
            //var postId = data.Select(i => i.F_PostId).ToList();
            //var PostList = Db.GetIQueryable<HR_Recruit>().Where(i => postId.Contains(i.F_Role)).ToList();
            List<HR_ContentOperationLog> hR_ContentOperationLogs = new List<HR_ContentOperationLog>();
            if (data.Count > 0)
            {
                foreach (var item in data)
                {
                    item.F_IsGiveUpInterview = (int)RecruitType.IsGiveUpInterviewType.ABinterview;
                    //var hR_Recruit = PostList.FirstOrDefault(i => i.F_Role == item.F_PostId);
                    //var name = hR_Recruit != null ? hR_Recruit.F_RecruitName : "";
                    item.F_Label = item.F_Label + EnumHelper.GetEnumDescription(RecruitType.TagType.暂停面试);
                    item.F_LabelOperationTime = DateTime.Now;
                    var model = Db.GetIQueryable<HR_InterviewPlan>().Where(i => i.F_IsDelete == 0 && i.F_ApplicantId == item.F_UserId).OrderByDescending(i => i.F_RoundInterview).FirstOrDefault();
                    if (model != null)
                    {
                        model.F_IsGiveUpInterview = (int)RecruitType.IsGiveUpInterviewType.ABinterview;
                        await Db.UpdateAsync<HR_InterviewPlan>(model);
                    }
                    //保存标签
                    HR_ContentOperationLog hR_ContentOperation = new HR_ContentOperationLog()
                    {
                        F_BusState = (int)ASKBusState.正常,
                        F_UserId = item.F_UserId,
                        F_RelationalID = item.F_Id,
                        F_RelationalType = (int)RecruitType.RelationalType.正常,
                        F_TagContent = EnumHelper.GetEnumDescription(RecruitType.TagType.暂停面试),
                        F_TagType = (int)RecruitType.TagType.暂停面试
                    };
                    hR_ContentOperationLogs.Add(hR_ContentOperation);
                }
                await _ContentOperationLogBusiness.SaveDataAsync(hR_ContentOperationLogs);
                await Db.UpdateAsync<HR_RecruitmentCandidates>(data);
            }
        }
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_InterviewPlan>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_InterviewPlan, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_InterviewPlan, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }


        public List<GetInterviewerDTO> GetInterviewer(InterviewDTO input)
        {
            List<GetInterviewerDTO> getInterviewers = new List<GetInterviewerDTO>();
            if (input.type == 0)
            {
                getInterviewers = (from a in Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => i.F_BusState == 1 && i.F_IsInvitedInt.Value == 0 && i.F_Through == 0 && i.F_IsGiveUpInterview == 0)
                                   join b in Db.GetIQueryable<HR_Entry>().Where(i => i.F_BusState == 1) on a.F_UserId equals b.F_Id
                                   //from b in abEn.DefaultIfEmpty()
                                   join c in Db.GetIQueryable<HR_Recruit>() on a.F_RecruitId equals c.F_Id into acEn
                                   from c in acEn.DefaultIfEmpty()
                                   join e in Db.GetIQueryable<Base_Post>() on c.F_Role equals e.F_Id into ceEn
                                   from e in ceEn.DefaultIfEmpty()
                                   join q in Db.GetIQueryable<HR_InterviewProcess>().Where(i => string.IsNullOrEmpty(i.F_ParentId)) on c.F_Role equals q.F_PostId into cqEn
                                   from q in cqEn.DefaultIfEmpty()
                                   select new GetInterviewerDTO()
                                   {
                                       ByTime = a.F_ModifyDate.Value,
                                       InterviewPId = q.F_Id,
                                       PostId = e.F_Id,
                                       PostName = e.F_Name,
                                       ResumeId = a.F_Id,
                                       StateInterview = "同意面试",
                                       UserId = b.F_Id,
                                       UserName = b.NameUser,
                                       F_Id = c.F_Id,
                                   }).ToList();
            }
            else
            {
                getInterviewers = (from a in Db.GetIQueryable<HR_InterviewPlan>().Where(i => i.F_RoundInterview == input.type && i.F_IsPassingInter == 1 && i.F_IsDelete == 0 && i.F_IsGiveUpInterview == 0)
                                   join c in Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => i.F_Through == 0 && i.F_IsGiveUpInterview == 0) on a.F_RecCanId equals c.F_Id
                                   join b in Db.GetIQueryable<HR_Entry>().Where(i => i.F_BusState == 1) on a.F_ApplicantId equals b.F_Id
                                   //from b in abEn.DefaultIfEmpty()
                                   join e in Db.GetIQueryable<Base_Post>() on a.F_PostId equals e.F_Id into ceEn
                                   from e in ceEn.DefaultIfEmpty()
                                   select new GetInterviewerDTO()
                                   {
                                       ByTime = a.F_PassingTime.Value,
                                       InterviewPId = a.F_InterviewPId,
                                       PostId = e.F_Id,
                                       PostName = e.F_Name,
                                       ResumeId = a.F_RecCanId,
                                       StateInterview = "第 " + a.F_RoundInterview + " 面试",
                                       UserId = b.F_Id,
                                       UserName = b.NameUser,
                                       F_Id = a.F_Id,
                                   }).ToList();
            }
            return getInterviewers.OrderByDescending(i => i.ByTime).ToList();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}