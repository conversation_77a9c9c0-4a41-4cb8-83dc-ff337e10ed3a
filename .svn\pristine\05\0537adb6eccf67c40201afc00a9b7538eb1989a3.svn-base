{"_from": "tinymce@^5.1.0", "_id": "tinymce@5.7.1", "_inBundle": false, "_integrity": "sha1-ZYpvtMfVOoSWzAD42jP0uCkNoG0=", "_location": "/tinymce", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "tinymce@^5.1.0", "name": "<PERSON><PERSON><PERSON>", "escapedName": "<PERSON><PERSON><PERSON>", "rawSpec": "^5.1.0", "saveSpec": null, "fetchSpec": "^5.1.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npm.taobao.org/tinymce/download/tinymce-5.7.1.tgz?cache=0&sync_timestamp=1615946363291&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftinymce%2Fdownload%2Ftinymce-5.7.1.tgz", "_shasum": "658a6fb4c7d53a8496cc00f8da33f4b8290da06d", "_spec": "tinymce@^5.1.0", "_where": "E:\\HRSystem\\Coldairarrow.Exam", "author": {"name": "Tiny Technologies, Inc"}, "bugs": {"url": "https://github.com/tinymce/tinymce/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Web based JavaScript HTML WYSIWYG editor control.", "homepage": "https://github.com/tinymce/tinymce-dist#readme", "keywords": ["editor", "wysiwyg", "<PERSON><PERSON><PERSON>", "richtext", "javascript", "html"], "license": "LGPL-2.1", "main": "tinymce.js", "name": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/tinymce/tinymce-dist.git"}, "types": "tinymce.d.ts", "version": "5.7.1"}