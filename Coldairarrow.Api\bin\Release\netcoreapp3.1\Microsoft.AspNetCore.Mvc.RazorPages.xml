<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Mvc.RazorPages</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.CompiledPageActionDescriptorBuilder">
            <summary>
            Constructs a <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/> from an <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.CompiledPageActionDescriptorBuilder.Build(Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel,Microsoft.AspNetCore.Mvc.Filters.FilterCollection)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/> from the specified <paramref name="applicationModel"/>.
            </summary>
            <param name="applicationModel">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/>.</param>
            <param name="globalFilters">Global filters to apply to the page.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.DefaultPageApplicationModelPartsProvider.CreateHandlerModel(System.Reflection.MethodInfo)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel"/> for the specified <paramref name="method"/>.s
            </summary>
            <param name="method">The <see cref="T:System.Reflection.MethodInfo"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.DefaultPageApplicationModelPartsProvider.CreateParameterModel(System.Reflection.ParameterInfo)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageParameterModel"/> for the specified <paramref name="parameter"/>.
            </summary>
            <param name="parameter">The <see cref="T:System.Reflection.ParameterInfo"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageParameterModel"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.DefaultPageApplicationModelPartsProvider.CreatePropertyModel(System.Reflection.PropertyInfo)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel"/> for the <paramref name="property"/>.
            </summary>
            <param name="property">The <see cref="T:System.Reflection.PropertyInfo"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.DefaultPageApplicationModelPartsProvider.IsHandler(System.Reflection.MethodInfo)">
            <summary>
            Determines if the specified <paramref name="methodInfo"/> is a handler.
            </summary>
            <param name="methodInfo">The <see cref="T:System.Reflection.MethodInfo"/>.</param>
            <returns><c>true</c> if the <paramref name="methodInfo"/> is a handler. Otherwise <c>false</c>.</returns>
            <remarks>
            Override this method to provide custom logic to determine which methods are considered handlers.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.DefaultPageApplicationModelProvider.Order">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.DefaultPageApplicationModelProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.DefaultPageApplicationModelProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.DefaultPageApplicationModelProvider.CreateModel(Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor,System.Reflection.TypeInfo)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/> for the given <paramref name="pageTypeInfo"/>.
            </summary>
            <param name="actionDescriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/>.</param>
            <param name="pageTypeInfo">The <see cref="T:System.Reflection.TypeInfo"/>.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/> for the given <see cref="T:System.Reflection.TypeInfo"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelConvention">
            <summary>
            Allows customization of the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelConvention.Apply(Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel)">
            <summary>
            Called to apply the convention to the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/>.
            </summary>
            <param name="model">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelPartsProvider">
            <summary>
            Provides parts that are used to construct a <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel" /> instance
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelPartsProvider.CreateHandlerModel(System.Reflection.MethodInfo)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel"/> for the specified <paramref name="method"/>.s
            </summary>
            <param name="method">The <see cref="T:System.Reflection.MethodInfo"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelPartsProvider.CreateParameterModel(System.Reflection.ParameterInfo)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageParameterModel"/> for the specified <paramref name="parameter"/>.
            </summary>
            <param name="parameter">The <see cref="T:System.Reflection.ParameterInfo"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageParameterModel"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelPartsProvider.CreatePropertyModel(System.Reflection.PropertyInfo)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel"/> for the <paramref name="property"/>.
            </summary>
            <param name="property">The <see cref="T:System.Reflection.PropertyInfo"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelPartsProvider.IsHandler(System.Reflection.MethodInfo)">
            <summary>
            Determines if the specified <paramref name="methodInfo"/> is a handler.
            </summary>
            <param name="methodInfo">The <see cref="T:System.Reflection.MethodInfo"/>.</param>
            <returns><c>true</c> if the <paramref name="methodInfo"/> is a handler. Otherwise <c>false</c>.</returns>
            <remarks>
            Override this method to provide custom logic to determine which methods are considered handlers.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider">
            <summary>
            Builds or modifies an <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext"/> for Razor Page discovery.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.Order">
            <summary>
            Gets the order value for determining the order of execution of providers. Providers execute in
            ascending numeric value of the <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.Order"/> property.
            </summary>
            <remarks>
            <para>
            Providers are executed in an ordering determined by an ascending sort of the <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.Order"/> property.
            A provider with a lower numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.Order"/> will have its
            <see cref="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext)"/> called before that of a provider with a higher numeric value of
            <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.Order"/>. The <see cref="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext)"/> method is called in the reverse ordering after
            all calls to <see cref="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext)"/>. A provider with a lower numeric value of
            <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.Order"/> will have its <see cref="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext)"/> method called after that of a provider
            with a higher numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.Order"/>.
            </para>
            <para>
            If two providers have the same numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.Order"/>, then their relative execution order
            is undefined.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext)">
            <summary>
            Executed for the first pass of building <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/> instances. See <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.Order"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext)">
            <summary>
            Executed for the second pass of building <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/> instances. See <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider.Order"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageConvention">
            <summary>
            Common interface for route and application model conventions that apply to Razor Pages.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageHandlerModelConvention">
            <summary>
            Allows customization of the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageHandlerModelConvention.Apply(Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel)">
            <summary>
            Called to apply the convention to the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel"/>.
            </summary>
            <param name="model">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelConvention">
            <summary>
            Allows customization of the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelConvention.Apply(Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel)">
            <summary>
            Called to apply the convention to the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/>.
            </summary>
            <param name="model">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider">
            <summary>
            Builds or modifies an <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModelProviderContext"/> for Razor Page routing.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.Order">
            <summary>
            Gets the order value for determining the order of execution of providers. Providers execute in
            ascending numeric value of the <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.Order"/> property.
            </summary>
            <remarks>
            <para>
            Providers are executed in an ordering determined by an ascending sort of the <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.Order"/> property.
            A provider with a lower numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.Order"/> will have its
            <see cref="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModelProviderContext)"/> called before that of a provider with a higher numeric value of
            <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.Order"/>. The <see cref="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModelProviderContext)"/> method is called in the reverse ordering after
            all calls to <see cref="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModelProviderContext)"/>. A provider with a lower numeric value of
            <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.Order"/> will have its <see cref="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModelProviderContext)"/> method called after that of a provider
            with a higher numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.Order"/>.
            </para>
            <para>
            If two providers have the same numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.Order"/>, then their relative execution order
            is undefined.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModelProviderContext)">
            <summary>
            Executed for the first pass of building <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/> instances. See <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.Order"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModelProviderContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModelProviderContext)">
            <summary>
            Executed for the second pass of building <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/> instances. See <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider.Order"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModelProviderContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel">
            <summary>
            Application model component for RazorPages.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.#ctor(Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor,System.Reflection.TypeInfo,System.Collections.Generic.IReadOnlyList{System.Object})">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.#ctor(Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor,System.Reflection.TypeInfo,System.Reflection.TypeInfo,System.Collections.Generic.IReadOnlyList{System.Object})">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.#ctor(Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel)">
            <summary>
            A copy constructor for <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/>.
            </summary>
            <param name="other">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/> to copy from.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.ActionDescriptor">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.RelativePath">
            <summary>
            Gets the application root relative path for the page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.ViewEnginePath">
            <summary>
            Gets the path relative to the base path for page discovery.
            <para>
            This value is the path of the file without extension, relative to the pages root directory.
            e.g. the <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.ViewEnginePath"/> for the file /Pages/Catalog/Antiques.cshtml is <c>/Catalog/Antiques</c>
            </para>
            <para>
            In an area, this value is the path of the file without extension, relative to the pages root directory for the specified area.
            e.g. the <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.ViewEnginePath"/>  for the file Areas/Identity/Pages/Manage/Accounts.cshtml, is <c>/Manage/Accounts</c>.
            </para>
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.AreaName">
            <summary>
            Gets the area name.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.RouteTemplate">
            <summary>
            Gets the route template for the page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.Filters">
            <summary>
            Gets the applicable <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> instances.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.Properties">
            <summary>
            Stores arbitrary metadata properties associated with the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.PageType">
            <summary>
            Gets or sets the <see cref="T:System.Reflection.TypeInfo"/> of the Razor page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.DeclaredModelType">
            <summary>
            Gets the declared model <see cref="T:System.Reflection.TypeInfo"/> of the model for the page.
            Typically this <see cref="T:System.Reflection.TypeInfo"/> will be the type specified by the @model directive
            in the razor page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.ModelType">
            <summary>
            Gets or sets the runtime model <see cref="T:System.Reflection.TypeInfo"/> of the model for the razor page.
            This is the <see cref="T:System.Reflection.TypeInfo"/> that will be used at runtime to instantiate and populate
            the model property of the page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.HandlerType">
            <summary>
            Gets the <see cref="T:System.Reflection.TypeInfo"/> of the handler.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.HandlerTypeAttributes">
            <summary>
            Gets the sequence of attributes declared on <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.HandlerType"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.HandlerMethods">
            <summary>
            Gets the sequence of <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel"/> instances.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.HandlerProperties">
            <summary>
            Gets the sequence of <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel"/> instances on <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel.EndpointMetadata">
            <summary>
            Gets the endpoint metadata for this action.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext">
            <summary>
            A context object for <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelProvider"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext.ActionDescriptor">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext.PageType">
            <summary>
            Gets the page <see cref="T:System.Reflection.TypeInfo"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext.PageApplicationModel">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> class that is empty.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection.#ctor(System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.ApplicationModels.IPageConvention})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> class
            as a wrapper for the specified list.
            </summary>
            <param name="conventions">The list that is wrapped by the new collection.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection.AddPageApplicationModelConvention(System.String,System.Action{Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel})">
            <summary>
            Creates and adds an <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelConvention"/> that invokes an action on the
            <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/> for the page with the specified name.
            </summary>
            <param name="pageName">The name of the page e.g. <c>/Users/<USER>/c></param>
            <param name="action">The <see cref="T:System.Action"/>.</param>
            <returns>The added <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelConvention"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection.AddAreaPageApplicationModelConvention(System.String,System.String,System.Action{Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel})">
            <summary>
            Creates and adds an <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelConvention"/> that invokes an action on the
            <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/> for the page with the specified name located in the specified area.
            </summary>
            <param name="areaName">The name of area.</param>
            <param name="pageName">
            The page name e.g. <c>/Users/<USER>/c>
            <para>
            The page name is the path of the file without extension, relative to the pages root directory for the specified area.
            e.g. the page name for the file Areas/Identity/Pages/Manage/Accounts.cshtml, is <c>/Manage/Accounts</c>.
            </para>
            </param>
            <param name="action">The <see cref="T:System.Action"/>.</param>
            <returns>The added <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelConvention"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection.AddFolderApplicationModelConvention(System.String,System.Action{Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel})">
            <summary>
            Creates and adds an <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelConvention"/> that invokes an action on
            <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/> instances for all page under the specified folder.
            </summary>
            <param name="folderPath">The path of the folder relative to the Razor Pages root. e.g. <c>/Users/<USER>/c></param>
            <param name="action">The <see cref="T:System.Action"/>.</param>
            <returns>The added <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelConvention"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection.AddAreaFolderApplicationModelConvention(System.String,System.String,System.Action{Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel})">
            <summary>
            Creates and adds an <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelConvention"/> that invokes an action on
            <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/> instances for all pages under the specified area folder.
            </summary>
            <param name="areaName">The name of area.</param>
            <param name="folderPath">
            The folder path e.g. <c>/Manage/</c>
            <para>
            The folder path is the path of the folder, relative to the pages root directory for the specified area.
            e.g. the folder path for the file Areas/Identity/Pages/Manage/Accounts.cshtml, is <c>/Manage</c>.
            </para>
            </param>
            <param name="action">The <see cref="T:System.Action"/>.</param>
            <returns>The added <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelConvention"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection.AddPageRouteModelConvention(System.String,System.Action{Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel})">
            <summary>
            Creates and adds an <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelConvention"/> that invokes an action on the
            <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/> for the page with the specified name.
            </summary>
            <param name="pageName">The name of the page e.g. <c>/Users/<USER>/c></param>
            <param name="action">The <see cref="T:System.Action"/>.</param>
            <returns>The added <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelConvention"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection.AddAreaPageRouteModelConvention(System.String,System.String,System.Action{Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel})">
            <summary>
            Creates and adds an <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelConvention"/> that invokes an action on the
            <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/> for the page with the specified name located in the specified area.
            </summary>
            <param name="areaName">The area name.</param>
            <param name="pageName">
            The page name e.g. <c>/Users/<USER>/c>
            <para>
            The page name is the path of the file without extension, relative to the pages root directory for the specified area.
            e.g. the page name for the file Areas/Identity/Pages/Manage/Accounts.cshtml, is <c>/Manage/Accounts</c>.
            </para>
            </param>
            <param name="action">The <see cref="T:System.Action"/>.</param>
            <returns>The added <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelConvention"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection.AddFolderRouteModelConvention(System.String,System.Action{Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel})">
            <summary>
            Creates and adds an <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelConvention"/> that invokes an action on
            <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/> instances for all page under the specified folder.
            </summary>
            <param name="folderPath">The path of the folder relative to the Razor Pages root. e.g. <c>/Users/<USER>/c></param>
            <param name="action">The <see cref="T:System.Action"/>.</param>
            <returns>The added <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelConvention"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection.AddAreaFolderRouteModelConvention(System.String,System.String,System.Action{Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel})">
            <summary>
            Creates and adds an <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelConvention"/> that invokes an action on
            <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/> instances for all page under the specified area folder.
            </summary>
            <param name="areaName">The area name.</param>
            <param name="folderPath">
            The folder path e.g. <c>/Manage/</c>
            <para>
            The folder path is the path of the folder, relative to the pages root directory for the specified area.
            e.g. the folder path for the file Areas/Identity/Pages/Manage/Accounts.cshtml, is <c>/Manage</c>.
            </para>
            </param>
            <param name="action">The <see cref="T:System.Action"/>.</param>
            <returns>The added <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageApplicationModelConvention"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection.RemoveType``1">
            <summary>
            Removes all <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageConvention"/> instances of the specified type.
            </summary>
            <typeparam name="TPageConvention">The type to remove.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection.RemoveType(System.Type)">
            <summary>
            Removes all <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageConvention"/> instances of the specified type.
            </summary>
            <param name="pageConventionType">The type to remove.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel">
            <summary>
            Represents a handler in a <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel.#ctor(System.Reflection.MethodInfo,System.Collections.Generic.IReadOnlyList{System.Object})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel"/>.
            </summary>
            <param name="handlerMethod">The <see cref="T:System.Reflection.MethodInfo"/> for the handler.</param>
            <param name="attributes">Any attributes annotated on the handler method.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel.#ctor(Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel"/> from a given <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel"/>.
            </summary>
            <param name="other">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel"/> which needs to be copied.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel.MethodInfo">
            <summary>
            Gets the <see cref="T:System.Reflection.MethodInfo"/> for the handler.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel.HttpMethod">
            <summary>
            Gets or sets the HTTP method supported by this handler.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel.HandlerName">
            <summary>
            Gets or sets the handler method name.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel.Name">
            <summary>
            Gets or sets a descriptive name for the handler.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel.Parameters">
            <summary>
            Gets the sequence of <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageParameterModel"/> instances.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel.Page">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel.Attributes">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageHandlerModel.Properties">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel">
            <summary>
            Represents a property in a <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel.#ctor(System.Reflection.PropertyInfo,System.Collections.Generic.IReadOnlyList{System.Object})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel"/>.
            </summary>
            <param name="propertyInfo">The <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel.PropertyInfo"/> for the underlying property.</param>
            <param name="attributes">Any attributes which are annotated on the property.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel.#ctor(Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel"/> from a given <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel"/>.
            </summary>
            <param name="other">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel"/> which needs to be copied.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel.Page">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"/> this <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PagePropertyModel"/> is associated with.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteMetadata">
            <summary>
            Metadata used to construct an endpoint route to the page.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteMetadata.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteMetadata"/>.
            </summary>
            <param name="pageRoute">The page route.</param>
            <param name="routeTemplate">The route template specified by the page.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteMetadata.PageRoute">
            <summary>
            Gets the page route.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteMetadata.RouteTemplate">
            <summary>
            Gets the route template specified by the page.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel">
            <summary>
            A model component for routing RazorPages.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/>.
            </summary>
            <param name="relativePath">The application relative path of the page.</param>
            <param name="viewEnginePath">The path relative to the base path for page discovery.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/>.
            </summary>
            <param name="relativePath">The application relative path of the page.</param>
            <param name="viewEnginePath">The path relative to the base path for page discovery.</param>
            <param name="areaName">The area name.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.#ctor(Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel)">
            <summary>
            A copy constructor for <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/>.
            </summary>
            <param name="other">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/> to copy from.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.RelativePath">
            <summary>
            Gets the application root relative path for the page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.ViewEnginePath">
            <summary>
            Gets the path relative to the base path for page discovery.
            <para>
            This value is the path of the file without extension, relative to the pages root directory.
            e.g. the <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.ViewEnginePath"/> for the file /Pages/Catalog/Antiques.cshtml is <c>/Catalog/Antiques</c>
            </para>
            <para>
            In an area, this value is the path of the file without extension, relative to the pages root directory for the specified area.
            e.g. the <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.ViewEnginePath"/>  for the file Areas/Identity/Pages/Manage/Accounts.cshtml, is <c>/Manage/Accounts</c>.
            </para>
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.AreaName">
            <summary>
            Gets the area name. Will be <c>null</c> for non-area pages.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.Properties">
            <summary>
            Stores arbitrary metadata properties associated with the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.Selectors">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.SelectorModel"/> instances.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.RouteValues">
            <summary>
            Gets a collection of route values that must be present in the <see cref="P:Microsoft.AspNetCore.Routing.RouteData.Values"/>
            for the corresponding page to be selected.
            </summary>
            <remarks>
            <para>
            The value of <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.ViewEnginePath"/> is considered an implicit route value corresponding
            to the key <c>page</c>.
            </para>
            <para>
            The value of <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.AreaName"/> is considered an implicit route value corresponding
            to the key <c>area</c> when <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.AreaName"/> is not <c>null</c>.
            </para>
            <para>
            These entries will be implicitly added to <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor.RouteValues"/>
            when the action descriptor is created, but will not be visible in <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.RouteValues"/>.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.RouteParameterTransformer">
            <summary>
            Gets or sets an <see cref="T:Microsoft.AspNetCore.Routing.IOutboundParameterTransformer"/> that will be used to transform 
            built-in route parameters such as <c>action</c>, <c>controller</c>, and <c>area</c> as well as
            additional parameters specified by <see cref="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel.RouteValues"/> into static segments in the route template.
            </summary>
            <remarks>
            <para>
            This feature only applies when using endpoint routing.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModelProviderContext">
            <summary>
            A context object for <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelProvider"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModelProviderContext.RouteModels">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/> instances.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteTransformerConvention">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageRouteModelConvention"/> that sets page route resolution
            to use the specified <see cref="T:Microsoft.AspNetCore.Routing.IOutboundParameterTransformer"/> on <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteModel"/>.
            This convention does not effect controller action routes.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteTransformerConvention.#ctor(Microsoft.AspNetCore.Routing.IOutboundParameterTransformer)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageRouteTransformerConvention"/> with the specified <see cref="T:Microsoft.AspNetCore.Routing.IOutboundParameterTransformer"/>.
            </summary>
            <param name="parameterTransformer">The <see cref="T:Microsoft.AspNetCore.Routing.IOutboundParameterTransformer"/> to use resolve page routes.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApplicationModels.ViewDataAttributePageApplicationModelProvider.Order">
            <inheritdoc />
            <remarks>This order ensures that <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.ViewDataAttributePageApplicationModelProvider"/> runs after the <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.DefaultPageApplicationModelProvider"/>.</remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.ViewDataAttributePageApplicationModelProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApplicationModels.ViewDataAttributePageApplicationModelProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModelProviderContext)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/> for a compiled Razor page.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor.#ctor">
            <summary>
            Initializes an empty <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor.#ctor(Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/>
            from the specified <paramref name="actionDescriptor"/> instance.
            </summary>
            <param name="actionDescriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor.HandlerMethods">
            <summary>
            Gets the list of handler methods for the page. 
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor.HandlerTypeInfo">
            <summary>
            Gets or sets the <see cref="T:System.Reflection.TypeInfo"/> of the type that defines handler methods for the page. This can be
            the same as <see cref="P:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor.PageTypeInfo"/> and <see cref="P:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor.ModelTypeInfo"/> if the page does not have an
            explicit model type defined.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor.DeclaredModelTypeInfo">
            <summary>
            Gets or sets the declared model <see cref="T:System.Reflection.TypeInfo"/> of the model for the page.
            Typically this <see cref="T:System.Reflection.TypeInfo"/> will be the type specified by the @model directive
            in the razor page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor.ModelTypeInfo">
            <summary>
            Gets or sets the runtime model <see cref="T:System.Reflection.TypeInfo"/> of the model for the razor page.
            This is the <see cref="T:System.Reflection.TypeInfo"/> that will be used at runtime to instantiate and populate
            the model property of the page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor.PageTypeInfo">
            <summary>
            Gets or sets the <see cref="T:System.Reflection.TypeInfo"/> of the page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor.Endpoint">
            <summary>
            Gets or sets the associated <see cref="P:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor.Endpoint"/> of this page.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DefaultPageActivatorProvider">
            <summary>
            <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.IPageActivatorProvider"/> that uses type activation to create Pages.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DefaultPageActivatorProvider.CreateActivator(Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DefaultPageModelActivatorProvider">
            <summary>
            <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.IPageActivatorProvider"/> that uses type activation to create Razor Page instances.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DefaultPageModelActivatorProvider.CreateActivator(Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.HandleOptionsRequestsPageFilter">
            <summary>
            A filter that handles OPTIONS requests page when no handler method is available.
            <para>
            a) MVC treats no handler being selected no differently than a page having no handler, both execute the
            page.
            b) A common model for programming Razor Pages is to initialize content required by a page in the
            <c>OnGet</c> handler. Executing a page without running the handler may result in runtime exceptions -
            e.g. null ref or out of bounds exception if you expected a property or collection to be initialized.
            </para>
            <para>
            Some web crawlers use OPTIONS request when probing servers. In the absence of an uncommon <c>OnOptions</c>
            handler, executing the page will likely result in runtime errors as described in earlier. This filter
            attempts to avoid this pit of failure by handling OPTIONS requests and returning a 200 if no handler is selected.
            </para>
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.HandleOptionsRequestsPageFilter.Order">
            <summary>
            Ordered to run after filters with default order.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.HandlerParameterDescriptor.ParameterInfo">
            <summary>
            Gets or sets the <see cref="T:System.Reflection.ParameterInfo"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.IPageLoader">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/> from a <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.IPageLoader.Load(Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor)">
            <summary>
            Produces a <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/> given a <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/>.
            </summary>
            <param name="actionDescriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.PageActionInvoker.InvokeInnerFilterAsync">
            <remarks>
            <see cref="T:Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker"/> for details on what the variables in this method represent.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.PageActionInvokerCacheEntry.ReleasePage">
            <summary>
            The action invoked to release a page. This may be <c>null</c>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.PageActionInvokerCacheEntry.ReleaseModel">
            <summary>
            The delegate invoked to release a model. This may be <c>null</c>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.PageActionInvokerCacheEntry.PropertyBinder">
            <summary>
            The delegate invoked to bind either the handler type (page or model).
            This may be <c>null</c>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.PageActionInvokerCacheEntry.ViewStartFactories">
            <summary>
            Gets the applicable ViewStart pages.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.PageBoundPropertyDescriptor.Property">
            <summary>
            Gets or sets the <see cref="T:System.Reflection.PropertyInfo"/> for this property.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.PageLoader">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/> from a <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.PageLoader.LoadAsync(Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor)">
            <summary>
            Produces a <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/> given a <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/>.
            </summary>
            <param name="actionDescriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns a <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.PageModelAttribute">
            <summary>
            An attribute for base classes for page models. Applying this attribute to a type
            marks all subclasses of that type as page model types.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.PageResultExecutor">
            <summary>
            Executes a Razor Page.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.PageResultExecutor.#ctor(Microsoft.AspNetCore.Mvc.Infrastructure.IHttpResponseStreamWriterFactory,Microsoft.AspNetCore.Mvc.ViewEngines.ICompositeViewEngine,Microsoft.AspNetCore.Mvc.Razor.IRazorViewEngine,Microsoft.AspNetCore.Mvc.Razor.IRazorPageActivator,System.Diagnostics.DiagnosticListener,System.Text.Encodings.Web.HtmlEncoder)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.PageResultExecutor"/>.
            </summary>
            <param name="writerFactory">The <see cref="T:Microsoft.AspNetCore.Mvc.Infrastructure.IHttpResponseStreamWriterFactory"/>.</param>
            <param name="compositeViewEngine">The <see cref="T:Microsoft.AspNetCore.Mvc.ViewEngines.ICompositeViewEngine"/>.</param>
            <param name="razorViewEngine">The <see cref="T:Microsoft.AspNetCore.Mvc.Razor.IRazorViewEngine"/>.</param>
            <param name="razorPageActivator">The <see cref="T:Microsoft.AspNetCore.Mvc.Razor.IRazorPageActivator"/>.</param>
            <param name="diagnosticListener">The <see cref="T:System.Diagnostics.DiagnosticListener"/>.</param>
            <param name="htmlEncoder">The <see cref="T:System.Text.Encodings.Web.HtmlEncoder"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.PageResultExecutor.ExecuteAsync(Microsoft.AspNetCore.Mvc.RazorPages.PageContext,Microsoft.AspNetCore.Mvc.RazorPages.PageResult)">
            <summary>
            Executes a Razor Page asynchronously.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.ServiceBasedPageModelActivatorProvider">
            <summary>
            <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.IPageActivatorProvider"/> that uses type activation to create Razor Page instances.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.IPageActivatorProvider">
            <summary>
            Provides methods to create a Razor page.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.IPageActivatorProvider.CreateActivator(Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor)">
            <summary>
            Creates a Razor page activator.
            </summary>
            <param name="descriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/>.</param>
            <returns>The delegate used to activate the page.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.IPageActivatorProvider.CreateReleaser(Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor)">
            <summary>
            Releases a Razor page.
            </summary>
            <param name="descriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/>.</param>
            <returns>The delegate used to dispose the activated page.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.IPageFactoryProvider">
            <summary>
            Provides methods for creation and disposal of Razor pages.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.IPageFactoryProvider.CreatePageFactory(Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor)">
            <summary>
            Creates a factory for producing Razor pages for the specified <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageContext"/>.
            </summary>
            <param name="descriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/>.</param>
            <returns>The Razor page factory.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.IPageFactoryProvider.CreatePageDisposer(Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor)">
            <summary>
            Releases a Razor page.
            </summary>
            <param name="descriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/>.</param>
            <returns>The delegate used to release the created page.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.IPageModelActivatorProvider">
            <summary>
            Provides methods to create a Razor Page model.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.IPageModelActivatorProvider.CreateActivator(Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor)">
            <summary>
            Creates a Razor Page model activator.
            </summary>
            <param name="descriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/>.</param>
            <returns>The delegate used to activate the page model.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.IPageModelActivatorProvider.CreateReleaser(Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor)">
            <summary>
            Releases a Razor Page model.
            </summary>
            <param name="descriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/>.</param>
            <returns>The delegate used to dispose the activated Razor Page model.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.IPageModelFactoryProvider">
            <summary>
            Provides methods for creation and disposal of Razor Page models.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.IPageModelFactoryProvider.CreateModelFactory(Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor)">
            <summary>
            Creates a factory for producing models for Razor Pages given the specified <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageContext"/>.
            </summary>
            <param name="descriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/>.</param>
            <returns>The Razor Page model factory.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.IPageModelFactoryProvider.CreateModelDisposer(Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor)">
            <summary>
            Releases a Razor Page model.
            </summary>
            <param name="descriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.CompiledPageActionDescriptor"/>.</param>
            <returns>The delegate used to release the created Razor Page model.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.NonHandlerAttribute">
            <summary>
            Specifies that the targeted method is not a page handler method.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.Page">
            <summary>
            A base class for a Razor page.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor.#ctor(Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor)">
            <summary>
            A copy constructor for <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/>.
            </summary>
            <param name="other">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/> to copy from.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor.RelativePath">
            <summary>
            Gets or sets the application root relative path for the page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor.ViewEnginePath">
            <summary>
            Gets or sets the path relative to the base path for page discovery.
            <para>
            This value is the path of the file without extension, relative to the pages root directory.
            e.g. the <see cref="P:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor.ViewEnginePath"/> for the file /Pages/Catalog/Antiques.cshtml is <c>/Catalog/Antiques</c>
            </para>
            <para>
            In an area, this value is the path of the file without extension, relative to the pages root directory for the specified area.
            e.g. the <see cref="P:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor.ViewEnginePath"/>  for the file Areas/Identity/Pages/Manage/Accounts.cshtml, is <c>/Manage/Accounts</c>.
            </para>
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor.AreaName">
            <summary>
            Gets or sets the area name for this page.
            This value will be <c>null</c> for non-area pages.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor.DisplayName">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.PageBase">
            <summary>
            A base class for a Razor page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.PageContext">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageContext"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.ViewContext">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.HttpContext">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Request">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Http.HttpRequest"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Response">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Http.HttpResponse"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RouteData">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Routing.RouteData"/> for the executing action.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.ModelState">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.MetadataProvider">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.EnsureRenderedBodyOrSections">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.BeginContext(System.Int32,System.Int32,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.EndContext">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.BadRequest">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.BadRequestResult"/> that produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest"/> response.
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.BadRequestResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.BadRequest(System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.BadRequestObjectResult"/> that produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest"/> response.
            </summary>
            <param name="error">An error object to be returned to the client.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.BadRequestObjectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.BadRequest(Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.BadRequestObjectResult"/> that produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest"/> response.
            </summary>
            <param name="modelState">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary" /> containing errors to be returned to the client.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.BadRequestObjectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Challenge">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/>.
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/> for the response.</returns>
            <remarks>
            The behavior of this method depends on the <see cref="T:Microsoft.AspNetCore.Authentication.IAuthenticationService"/> in use.
            <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized"/> and <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/>
            are among likely status results.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Challenge(System.String[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/> with the specified authentication schemes.
            </summary>
            <param name="authenticationSchemes">The authentication schemes to challenge.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/> for the response.</returns>
            <remarks>
            The behavior of this method depends on the <see cref="T:Microsoft.AspNetCore.Authentication.IAuthenticationService"/> in use.
            <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized"/> and <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/>
            are among likely status results.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Challenge(Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/> with the specified <paramref name="properties" />.
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/> for the response.</returns>
            <remarks>
            The behavior of this method depends on the <see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationService"/> in use.
            <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized"/> and <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/>
            are among likely status results.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Challenge(Microsoft.AspNetCore.Authentication.AuthenticationProperties,System.String[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/> with the specified authentication schemes and
            <paramref name="properties" />.
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
            <param name="authenticationSchemes">The authentication schemes to challenge.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/> for the response.</returns>
            <remarks>
            The behavior of this method depends on the <see cref="T:Microsoft.AspNetCore.Authentication.IAuthenticationService"/> in use.
            <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized"/> and <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/>
            are among likely status results.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Content(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object with <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/> by specifying a
            <paramref name="content"/> string.
            </summary>
            <param name="content">The content to write to the response.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Content(System.String,System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object with <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/> by specifying a
            <paramref name="content"/> string and a content type.
            </summary>
            <param name="content">The content to write to the response.</param>
            <param name="contentType">The content type (MIME type).</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Content(System.String,System.String,System.Text.Encoding)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object with <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/> by specifying a
            <paramref name="content"/> string, a <paramref name="contentType"/>, and <paramref name="contentEncoding"/>.
            </summary>
            <param name="content">The content to write to the response.</param>
            <param name="contentType">The content type (MIME type).</param>
            <param name="contentEncoding">The content encoding.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object for the response.</returns>
            <remarks>
            If encoding is provided by both the 'charset' and the <paramref name="contentEncoding"/> parameters, then
            the <paramref name="contentEncoding"/> parameter is chosen as the final encoding.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Content(System.String,Microsoft.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object with <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/> by specifying a
            <paramref name="content"/> string and a <paramref name="contentType"/>.
            </summary>
            <param name="content">The content to write to the response.</param>
            <param name="contentType">The content type (MIME type).</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Forbid">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> by default).
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> for the response.</returns>
            <remarks>
            Some authentication schemes, such as cookies, will convert <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> to
            a redirect to show a login page.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Forbid(System.String[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> by default) with the
            specified authentication schemes.
            </summary>
            <param name="authenticationSchemes">The authentication schemes to challenge.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> for the response.</returns>
            <remarks>
            Some authentication schemes, such as cookies, will convert <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> to
            a redirect to show a login page.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Forbid(Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> by default) with the
            specified <paramref name="properties" />.
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> for the response.</returns>
            <remarks>
            Some authentication schemes, such as cookies, will convert <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> to
            a redirect to show a login page.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Forbid(Microsoft.AspNetCore.Authentication.AuthenticationProperties,System.String[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> by default) with the
            specified authentication schemes and <paramref name="properties" />.
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
            <param name="authenticationSchemes">The authentication schemes to challenge.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> for the response.</returns>
            <remarks>
            Some authentication schemes, such as cookies, will convert <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> to
            a redirect to show a login page.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.File(System.Byte[],System.String)">
            <summary>
            Returns a file with the specified <paramref name="fileContents" /> as content
            (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>) and the specified <paramref name="contentType" /> as the Content-Type.
            </summary>
            <param name="fileContents">The file contents.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.FileContentResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.File(System.Byte[],System.String,System.String)">
            <summary>
            Returns a file with the specified <paramref name="fileContents" /> as content (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>), the
            specified <paramref name="contentType" /> as the Content-Type and the
            specified <paramref name="fileDownloadName" /> as the suggested file name.
            </summary>
            <param name="fileContents">The file contents.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <param name="fileDownloadName">The suggested file name.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.FileContentResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.File(System.IO.Stream,System.String)">
            <summary>
            Returns a file in the specified <paramref name="fileStream" /> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>)
            with the specified <paramref name="contentType" /> as the Content-Type.
            </summary>
            <param name="fileStream">The <see cref="T:System.IO.Stream"/> with the contents of the file.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.FileStreamResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.File(System.IO.Stream,System.String,System.String)">
            <summary>
            Returns a file in the specified <paramref name="fileStream" /> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>) with the
            specified <paramref name="contentType" /> as the Content-Type and the
            specified <paramref name="fileDownloadName" /> as the suggested file name.
            </summary>
            <param name="fileStream">The <see cref="T:System.IO.Stream"/> with the contents of the file.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <param name="fileDownloadName">The suggested file name.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.FileStreamResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.File(System.String,System.String)">
            <summary>
            Returns the file specified by <paramref name="virtualPath" /> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>) with the
            specified <paramref name="contentType" /> as the Content-Type.
            </summary>
            <param name="virtualPath">The virtual path of the file to be returned.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.VirtualFileResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.File(System.String,System.String,System.String)">
            <summary>
            Returns the file specified by <paramref name="virtualPath" /> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>) with the
            specified <paramref name="contentType" /> as the Content-Type and the
            specified <paramref name="fileDownloadName" /> as the suggested file name.
            </summary>
            <param name="virtualPath">The virtual path of the file to be returned.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <param name="fileDownloadName">The suggested file name.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.VirtualFileResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.PhysicalFile(System.String,System.String)">
            <summary>
            Returns the file specified by <paramref name="physicalPath" /> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>) with the
            specified <paramref name="contentType" /> as the Content-Type.
            </summary>
            <param name="physicalPath">The physical path of the file to be returned.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.PhysicalFileResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.PhysicalFile(System.String,System.String,System.String)">
            <summary>
            Returns the file specified by <paramref name="physicalPath" /> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>) with the
            specified <paramref name="contentType" /> as the Content-Type and the
            specified <paramref name="fileDownloadName" /> as the suggested file name.
            </summary>
            <param name="physicalPath">The physical path of the file to be returned.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <param name="fileDownloadName">The suggested file name.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.PhysicalFileResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.LocalRedirect(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> object that redirects
            (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified local <paramref name="localUrl"/>.
            </summary>
            <param name="localUrl">The local URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.LocalRedirectPermanent(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> object with <see cref="P:Microsoft.AspNetCore.Mvc.LocalRedirectResult.Permanent"/> set to
            true (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) using the specified <paramref name="localUrl"/>.
            </summary>
            <param name="localUrl">The local URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.LocalRedirectPreserveMethod(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> object with <see cref="P:Microsoft.AspNetCore.Mvc.LocalRedirectResult.Permanent"/> set to
            false and <see cref="P:Microsoft.AspNetCore.Mvc.LocalRedirectResult.PreserveMethod"/> set to true
            (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status307TemporaryRedirect"/>) using the specified <paramref name="localUrl"/>.
            </summary>
            <param name="localUrl">The local URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.LocalRedirectPermanentPreserveMethod(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> object with <see cref="P:Microsoft.AspNetCore.Mvc.LocalRedirectResult.Permanent"/> set to
            true and <see cref="P:Microsoft.AspNetCore.Mvc.LocalRedirectResult.PreserveMethod"/> set to true
            (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status308PermanentRedirect"/>) using the specified <paramref name="localUrl"/>.
            </summary>
            <param name="localUrl">The local URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.NotFound">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Mvc.NotFoundResult"/> that produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status404NotFound"/> response.
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.NotFoundResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.NotFound(System.Object)">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Mvc.NotFoundObjectResult"/> that produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status404NotFound"/> response.
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.NotFoundObjectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Page">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageResult"/> object that renders this page as a view to the response.
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageResult"/> object for the response.</returns>
            <remarks>
            Returning a <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageResult"/> from a page handler method is equivalent to returning void.
            The view associated with the page will be executed.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Redirect(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> object that redirects to the specified <paramref name="url"/>.
            </summary>
            <param name="url">The URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectPermanent(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> object with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectResult.Permanent"/> set to true
            (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) using the specified <paramref name="url"/>.
            </summary>
            <param name="url">The URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectPreserveMethod(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> object with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectResult.Permanent"/> set to false
            and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectResult.PreserveMethod"/> set to true (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status307TemporaryRedirect"/>)
            using the specified <paramref name="url"/>.
            </summary>
            <param name="url">The URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectPermanentPreserveMethod(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> object with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectResult.Permanent"/> set to true
            and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectResult.PreserveMethod"/> set to true (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status308PermanentRedirect"/>)
            using the specified <paramref name="url"/>.
            </summary>
            <param name="url">The URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToAction(System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified action using the <paramref name="actionName"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToAction(System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified action using the
            <paramref name="actionName"/> and <paramref name="routeValues"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToAction(System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified action using the
            <paramref name="actionName"/> and the <paramref name="controllerName"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the controller.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToAction(System.String,System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified action using the specified
            <paramref name="actionName"/>, <paramref name="controllerName"/>, and <paramref name="routeValues"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the controller.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToAction(System.String,System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified action using the specified
            <paramref name="actionName"/>, <paramref name="controllerName"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the controller.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToAction(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified action using the specified <paramref name="actionName"/>,
            <paramref name="controllerName"/>, <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the controller.</param>
            <param name="routeValues">The parameters for a route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToActionPreserveMethod(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status307TemporaryRedirect"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to false and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.PreserveMethod"/>
            set to true, using the specified <paramref name="actionName"/>, <paramref name="controllerName"/>,
            <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the controller.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToActionPermanent(System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to true using the specified <paramref name="actionName"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToActionPermanent(System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to true using the specified <paramref name="actionName"/>
            and <paramref name="routeValues"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToActionPermanent(System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to true using the specified <paramref name="actionName"/>
            and <paramref name="controllerName"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the controller.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToActionPermanent(System.String,System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to true using the specified <paramref name="actionName"/>,
            <paramref name="controllerName"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the controller.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToActionPermanent(System.String,System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to true using the specified <paramref name="actionName"/>,
            <paramref name="controllerName"/>, and <paramref name="routeValues"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the controller.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToActionPermanent(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to true using the specified <paramref name="actionName"/>,
            <paramref name="controllerName"/>, <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the controller.</param>
            <param name="routeValues">The parameters for a route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToActionPermanentPreserveMethod(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status308PermanentRedirect"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to true and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.PreserveMethod"/>
            set to true, using the specified <paramref name="actionName"/>, <paramref name="controllerName"/>,
            <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the controller.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToRoute(System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified route using the specified <paramref name="routeName"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToRoute(System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified route using the specified <paramref name="routeValues"/>.
            </summary>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToRoute(System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified route using the specified
            <paramref name="routeName"/> and <paramref name="routeValues"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToRoute(System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified route using the specified
            <paramref name="routeName"/> and <paramref name="fragment"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToRoute(System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified route using the specified
            <paramref name="routeName"/>, <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for a route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToRoutePreserveMethod(System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status307TemporaryRedirect"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to false and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.PreserveMethod"/>
            set to true, using the specified <paramref name="routeName"/>, <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToRoutePermanent(System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to true using the specified <paramref name="routeName"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToRoutePermanent(System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to true using the specified <paramref name="routeValues"/>.
            </summary>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToRoutePermanent(System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to true using the specified <paramref name="routeName"/>
            and <paramref name="routeValues"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToRoutePermanent(System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to true using the specified <paramref name="routeName"/>
            and <paramref name="fragment"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToRoutePermanent(System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to true using the specified <paramref name="routeName"/>,
            <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for a route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToRoutePermanentPreserveMethod(System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status308PermanentRedirect"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to true and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.PreserveMethod"/>
            set to true, using the specified <paramref name="routeName"/>, <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPage">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the current page.
            </summary>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPage(System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the current page with the specified <paramref name="routeValues"/>.
            </summary>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPage(System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified <paramref name="pageName"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPage(System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="pageHandler"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPage(System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="routeValues"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPage(System.String,System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="fragment"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPage(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="routeValues"/> and <paramref name="fragment"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="routeValues">The parameters for a route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPagePermanent(System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified <paramref name="pageName"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/> with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToPageResult.Permanent"/> set.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPagePermanent(System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="routeValues"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/> with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToPageResult.Permanent"/> set.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPagePermanent(System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="pageHandler"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/> with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToPageResult.Permanent"/> set.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPagePermanent(System.String,System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="routeValues"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/> with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToPageResult.Permanent"/> set.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPagePermanent(System.String,System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="fragment"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/> with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToPageResult.Permanent"/> set.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPagePermanent(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="routeValues"/> and <paramref name="fragment"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="routeValues">The parameters for a route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/> with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToPageResult.Permanent"/> set.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPagePreserveMethod(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status307TemporaryRedirect"/>) to the specified page with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to false and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.PreserveMethod"/>
            set to true, using the specified <paramref name="pageName"/>, <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.RedirectToPagePermanentPreserveMethod(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status308PermanentRedirect"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to true and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.PreserveMethod"/>
            set to true, using the specified <paramref name="pageName"/>, <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.SignIn(System.Security.Claims.ClaimsPrincipal,System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.SignInResult"/> with the specified authentication scheme.
            </summary>
            <param name="principal">The <see cref="T:System.Security.Claims.ClaimsPrincipal"/> containing the user claims.</param>
            <param name="authenticationScheme">The authentication scheme to use for the sign-in operation.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.SignInResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.SignIn(System.Security.Claims.ClaimsPrincipal,Microsoft.AspNetCore.Authentication.AuthenticationProperties,System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.SignInResult"/> with the specified authentication scheme and
            <paramref name="properties" />.
            </summary>
            <param name="principal">The <see cref="T:System.Security.Claims.ClaimsPrincipal"/> containing the user claims.</param>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the sign-in operation.</param>
            <param name="authenticationScheme">The authentication scheme to use for the sign-in operation.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.SignInResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.SignOut(System.String[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.SignOutResult"/> with the specified authentication schemes.
            </summary>
            <param name="authenticationSchemes">The authentication schemes to use for the sign-out operation.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.SignOutResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.SignOut(Microsoft.AspNetCore.Authentication.AuthenticationProperties,System.String[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.SignOutResult"/> with the specified authentication schemes and
            <paramref name="properties" />.
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the sign-out operation.</param>
            <param name="authenticationSchemes">The authentication scheme to use for the sign-out operation.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.SignOutResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.StatusCode(System.Int32)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.StatusCodeResult"/> object by specifying a <paramref name="statusCode"/>.
            </summary>
            <param name="statusCode">The status code to set on the response.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.StatusCodeResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.StatusCode(System.Int32,System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ObjectResult"/> object by specifying a <paramref name="statusCode"/> and <paramref name="value"/>
            </summary>
            <param name="statusCode">The status code to set on the response.</param>
            <param name="value">The value to set on the <see cref="T:Microsoft.AspNetCore.Mvc.ObjectResult"/>.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ObjectResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Unauthorized">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Mvc.UnauthorizedResult"/> that produces an <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized"/> response.
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.UnauthorizedResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Partial(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.PartialViewResult"/> by specifying the name of a partial to render.
            </summary>
            <param name="viewName">The partial name.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.PartialViewResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.Partial(System.String,System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.PartialViewResult"/> by specifying the name of a partial to render and the model object.
            </summary>
            <param name="viewName">The partial name.</param>
            <param name="model">The model to be passed into the partial.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.PartialViewResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.ViewComponent(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> by specifying the name of a view component to render.
            </summary>
            <param name="componentName">
            The view component name. Can be a view component
            <see cref="P:Microsoft.AspNetCore.Mvc.ViewComponents.ViewComponentDescriptor.ShortName"/> or
            <see cref="P:Microsoft.AspNetCore.Mvc.ViewComponents.ViewComponentDescriptor.FullName"/>.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.ViewComponent(System.Type)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> by specifying the <see cref="T:System.Type"/> of a view component to
            render.
            </summary>
            <param name="componentType">The view component <see cref="T:System.Type"/>.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.ViewComponent(System.String,System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> by specifying the name of a view component to render.
            </summary>
            <param name="componentName">
            The view component name. Can be a view component
            <see cref="P:Microsoft.AspNetCore.Mvc.ViewComponents.ViewComponentDescriptor.ShortName"/> or
            <see cref="P:Microsoft.AspNetCore.Mvc.ViewComponents.ViewComponentDescriptor.FullName"/>.</param>
            <param name="arguments">
            An <see cref="T:System.Object"/> with properties representing arguments to be passed to the invoked view component
            method. Alternatively, an <see cref="T:System.Collections.Generic.IDictionary`2"/> instance
            containing the invocation arguments.
            </param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.ViewComponent(System.Type,System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> by specifying the <see cref="T:System.Type"/> of a view component to
            render.
            </summary>
            <param name="componentType">The view component <see cref="T:System.Type"/>.</param>
            <param name="arguments">
            An <see cref="T:System.Object"/> with properties representing arguments to be passed to the invoked view component
            method. Alternatively, an <see cref="T:System.Collections.Generic.IDictionary`2"/> instance
            containing the invocation arguments.
            </param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.TryUpdateModelAsync``1(``0)">
            <summary>
            Updates the specified <paramref name="model"/> instance using values from the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.Page"/>'s current
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/>.
            </summary>
            <typeparam name="TModel">The type of the model object.</typeparam>
            <param name="model">The model instance to update.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.TryUpdateModelAsync``1(``0,System.String)">
            <summary>
            Updates the specified <paramref name="model"/> instance using values from the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.Page"/>'s current
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> and a <paramref name="prefix"/>.
            </summary>
            <typeparam name="TModel">The type of the model object.</typeparam>
            <param name="model">The model instance to update.</param>
            <param name="prefix">The prefix to use when looking up values in the current <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/>.
            </param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.TryUpdateModelAsync``1(``0,System.String,Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider)">
            <summary>
            Updates the specified <paramref name="model"/> instance using the <paramref name="valueProvider"/> and a
            <paramref name="prefix"/>.
            </summary>
            <typeparam name="TModel">The type of the model object.</typeparam>
            <param name="model">The model instance to update.</param>
            <param name="prefix">The prefix to use when looking up values in the <paramref name="valueProvider"/>.
            </param>
            <param name="valueProvider">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> used for looking up values.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.TryUpdateModelAsync``1(``0,System.String,System.Linq.Expressions.Expression{System.Func{``0,System.Object}}[])">
            <summary>
            Updates the specified <paramref name="model"/> instance using values from the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.Page"/>'s current
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> and a <paramref name="prefix"/>.
            </summary>
            <typeparam name="TModel">The type of the model object.</typeparam>
            <param name="model">The model instance to update.</param>
            <param name="prefix">The prefix to use when looking up values in the current <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/>.
            </param>
            <param name="includeExpressions"> <see cref="T:System.Linq.Expressions.Expression"/>(s) which represent top-level properties
            which need to be included for the current model.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.TryUpdateModelAsync``1(``0,System.String,System.Func{Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.Boolean})">
            <summary>
            Updates the specified <paramref name="model"/> instance using values from the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.Page"/>'s current
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> and a <paramref name="prefix"/>.
            </summary>
            <typeparam name="TModel">The type of the model object.</typeparam>
            <param name="model">The model instance to update.</param>
            <param name="prefix">The prefix to use when looking up values in the current <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/>.
            </param>
            <param name="propertyFilter">A predicate which can be used to filter properties at runtime.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.TryUpdateModelAsync``1(``0,System.String,Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider,System.Linq.Expressions.Expression{System.Func{``0,System.Object}}[])">
            <summary>
            Updates the specified <paramref name="model"/> instance using the <paramref name="valueProvider"/> and a
            <paramref name="prefix"/>.
            </summary>
            <typeparam name="TModel">The type of the model object.</typeparam>
            <param name="model">The model instance to update.</param>
            <param name="prefix">The prefix to use when looking up values in the <paramref name="valueProvider"/>.
            </param>
            <param name="valueProvider">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> used for looking up values.</param>
            <param name="includeExpressions"> <see cref="T:System.Linq.Expressions.Expression"/>(s) which represent top-level properties
            which need to be included for the current model.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.TryUpdateModelAsync``1(``0,System.String,Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider,System.Func{Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.Boolean})">
            <summary>
            Updates the specified <paramref name="model"/> instance using the <paramref name="valueProvider"/> and a
            <paramref name="prefix"/>.
            </summary>
            <typeparam name="TModel">The type of the model object.</typeparam>
            <param name="model">The model instance to update.</param>
            <param name="prefix">The prefix to use when looking up values in the <paramref name="valueProvider"/>.
            </param>
            <param name="valueProvider">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> used for looking up values.</param>
            <param name="propertyFilter">A predicate which can be used to filter properties at runtime.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.TryUpdateModelAsync(System.Object,System.Type,System.String)">
            <summary>
            Updates the specified <paramref name="model"/> instance using values from the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.Page"/>'s current
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> and a <paramref name="prefix"/>.
            </summary>
            <param name="model">The model instance to update.</param>
            <param name="modelType">The type of model instance to update.</param>
            <param name="prefix">The prefix to use when looking up values in the current <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/>.
            </param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.TryUpdateModelAsync(System.Object,System.Type,System.String,Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider,System.Func{Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.Boolean})">
            <summary>
            Updates the specified <paramref name="model"/> instance using the <paramref name="valueProvider"/> and a
            <paramref name="prefix"/>.
            </summary>
            <param name="model">The model instance to update.</param>
            <param name="modelType">The type of model instance to update.</param>
            <param name="prefix">The prefix to use when looking up values in the <paramref name="valueProvider"/>.
            </param>
            <param name="valueProvider">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> used for looking up values.</param>
            <param name="propertyFilter">A predicate which can be used to filter properties at runtime.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.TryValidateModel(System.Object)">
            <summary>
            Validates the specified <paramref name="model"/> instance.
            </summary>
            <param name="model">The model to validate.</param>
            <returns><c>true</c> if the <see cref="P:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.ModelState"/> is valid; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.TryValidateModel(System.Object,System.String)">
            <summary>
            Validates the specified <paramref name="model"/> instance.
            </summary>
            <param name="model">The model to validate.</param>
            <param name="prefix">The key to use when looking up information in <see cref="P:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.ModelState"/>.
            </param>
            <returns><c>true</c> if the <see cref="P:Microsoft.AspNetCore.Mvc.RazorPages.PageBase.ModelState"/> is valid;<c>false</c> otherwise.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.PageContext">
            <summary>
            The context associated with the current request for a Razor page.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageContext.#ctor">
            <summary>
            Creates an empty <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageContext"/>.
            </summary>
            <remarks>
            The default constructor is provided for unit test purposes only.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageContext"/>.
            </summary>
            <param name="actionContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageContext.ActionDescriptor">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageActionDescriptor"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageContext.ValueProviderFactories">
            <summary>
            Gets or sets the list of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProviderFactory"/> instances for the current request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageContext.ViewData">
            <summary>
            Gets or sets <see cref="T:Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageContext.ViewStartFactories">
            <summary>
            Gets or sets the applicable _ViewStart instances.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.PageContextAttribute">
            <summary>
            Specifies that a Razor Page model property should be set with the current <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageContext"/> when creating
            the model instance. The property must have a public set method.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.PageContext">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageContext"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.HttpContext">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Request">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Http.HttpRequest"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Response">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Http.HttpResponse"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RouteData">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Routing.RouteData"/> for the executing action.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.ModelState">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.User">
            <summary>
            Gets the <see cref="T:System.Security.Claims.ClaimsPrincipal"/> for user associated with the executing action.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.TempData">
            <summary>
            Gets or sets <see cref="T:Microsoft.AspNetCore.Mvc.ViewFeatures.ITempDataDictionary"/> used by <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Url">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.IUrlHelper"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.MetadataProvider">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.ViewData">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.TryUpdateModelAsync``1(``0)">
            <summary>
            Updates the specified <paramref name="model"/> instance using values from the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageModel"/>'s current
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/>.
            </summary>
            <typeparam name="TModel">The type of the model object.</typeparam>
            <param name="model">The model instance to update.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.TryUpdateModelAsync``1(``0,System.String)">
            <summary>
            Updates the specified <paramref name="model"/> instance using values from the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageModel"/>'s current
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/>.
            </summary>
            <typeparam name="TModel">The type of the model object.</typeparam>
            <param name="model">The model instance to update.</param>
            <param name="name">The model name.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.TryUpdateModelAsync``1(``0,System.String,Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider)">
            <summary>
            Updates the specified <paramref name="model"/> instance using the <paramref name="valueProvider"/> and a
            <paramref name="name"/>.
            </summary>
            <typeparam name="TModel">The type of the model object.</typeparam>
            <param name="model">The model instance to update.</param>
            <param name="name">The name to use when looking up values in the <paramref name="valueProvider"/>.
            </param>
            <param name="valueProvider">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> used for looking up values.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.TryUpdateModelAsync``1(``0,System.String,System.Linq.Expressions.Expression{System.Func{``0,System.Object}}[])">
            <summary>
            Updates the specified <paramref name="model"/> instance using values from the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageModel"/>'s current
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> and a <paramref name="name"/>.
            </summary>
            <typeparam name="TModel">The type of the model object.</typeparam>
            <param name="model">The model instance to update.</param>
            <param name="name">The name to use when looking up values in the current <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/>.
            </param>
            <param name="includeExpressions"> <see cref="T:System.Linq.Expressions.Expression"/>(s) which represent top-level properties
            which need to be included for the current model.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.TryUpdateModelAsync``1(``0,System.String,System.Func{Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.Boolean})">
            <summary>
            Updates the specified <paramref name="model"/> instance using values from the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageModel"/>'s current
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> and a <paramref name="name"/>.
            </summary>
            <typeparam name="TModel">The type of the model object.</typeparam>
            <param name="model">The model instance to update.</param>
            <param name="name">The name to use when looking up values in the current <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/>.
            </param>
            <param name="propertyFilter">A predicate which can be used to filter properties at runtime.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.TryUpdateModelAsync``1(``0,System.String,Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider,System.Linq.Expressions.Expression{System.Func{``0,System.Object}}[])">
            <summary>
            Updates the specified <paramref name="model"/> instance using the <paramref name="valueProvider"/> and a
            <paramref name="name"/>.
            </summary>
            <typeparam name="TModel">The type of the model object.</typeparam>
            <param name="model">The model instance to update.</param>
            <param name="name">The name to use when looking up values in the <paramref name="valueProvider"/>.
            </param>
            <param name="valueProvider">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> used for looking up values.</param>
            <param name="includeExpressions"> <see cref="T:System.Linq.Expressions.Expression"/>(s) which represent top-level properties
            which need to be included for the current model.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.TryUpdateModelAsync``1(``0,System.String,Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider,System.Func{Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.Boolean})">
            <summary>
            Updates the specified <paramref name="model"/> instance using the <paramref name="valueProvider"/> and a
            <paramref name="name"/>.
            </summary>
            <typeparam name="TModel">The type of the model object.</typeparam>
            <param name="model">The model instance to update.</param>
            <param name="name">The name to use when looking up values in the <paramref name="valueProvider"/>.
            </param>
            <param name="valueProvider">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> used for looking up values.</param>
            <param name="propertyFilter">A predicate which can be used to filter properties at runtime.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.TryUpdateModelAsync(System.Object,System.Type,System.String)">
            <summary>
            Updates the specified <paramref name="model"/> instance using values from the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageModel"/>'s current
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> and a <paramref name="name"/>.
            </summary>
            <param name="model">The model instance to update.</param>
            <param name="modelType">The type of model instance to update.</param>
            <param name="name">The name to use when looking up values in the current <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/>.
            </param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.TryUpdateModelAsync(System.Object,System.Type,System.String,Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider,System.Func{Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.Boolean})">
            <summary>
            Updates the specified <paramref name="model"/> instance using the <paramref name="valueProvider"/> and a
            <paramref name="name"/>.
            </summary>
            <param name="model">The model instance to update.</param>
            <param name="modelType">The type of model instance to update.</param>
            <param name="name">The name to use when looking up values in the <paramref name="valueProvider"/>.
            </param>
            <param name="valueProvider">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> used for looking up values.</param>
            <param name="propertyFilter">A predicate which can be used to filter properties at runtime.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns <c>true</c> if the update is successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.BadRequest">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.BadRequestResult"/> that produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest"/> response.
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.BadRequestResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.BadRequest(System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.BadRequestObjectResult"/> that produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest"/> response.
            </summary>
            <param name="error">An error object to be returned to the client.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.BadRequestObjectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.BadRequest(Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.BadRequestObjectResult"/> that produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest"/> response.
            </summary>
            <param name="modelState">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary" /> containing errors to be returned to the client.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.BadRequestObjectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Challenge">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/>.
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/> for the response.</returns>
            <remarks>
            The behavior of this method depends on the <see cref="T:Microsoft.AspNetCore.Authentication.IAuthenticationService"/> in use.
            <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized"/> and <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/>
            are among likely status results.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Challenge(System.String[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/> with the specified authentication schemes.
            </summary>
            <param name="authenticationSchemes">The authentication schemes to challenge.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/> for the response.</returns>
            <remarks>
            The behavior of this method depends on the <see cref="T:Microsoft.AspNetCore.Authentication.IAuthenticationService"/> in use.
            <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized"/> and <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/>
            are among likely status results.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Challenge(Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/> with the specified <paramref name="properties" />.
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/> for the response.</returns>
            <remarks>
            The behavior of this method depends on the <see cref="T:Microsoft.AspNetCore.Authentication.IAuthenticationService"/> in use.
            <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized"/> and <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/>
            are among likely status results.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Challenge(Microsoft.AspNetCore.Authentication.AuthenticationProperties,System.String[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/> with the specified authentication schemes and
            <paramref name="properties" />.
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
            <param name="authenticationSchemes">The authentication schemes to challenge.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ChallengeResult"/> for the response.</returns>
            <remarks>
            The behavior of this method depends on the <see cref="T:Microsoft.AspNetCore.Authentication.IAuthenticationService"/> in use.
            <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized"/> and <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/>
            are among likely status results.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Content(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object with <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/> by specifying a
            <paramref name="content"/> string.
            </summary>
            <param name="content">The content to write to the response.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Content(System.String,System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object with <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/> by specifying a
            <paramref name="content"/> string and a content type.
            </summary>
            <param name="content">The content to write to the response.</param>
            <param name="contentType">The content type (MIME type).</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Content(System.String,System.String,System.Text.Encoding)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object with <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/> by specifying a
            <paramref name="content"/> string, a <paramref name="contentType"/>, and <paramref name="contentEncoding"/>.
            </summary>
            <param name="content">The content to write to the response.</param>
            <param name="contentType">The content type (MIME type).</param>
            <param name="contentEncoding">The content encoding.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object for the response.</returns>
            <remarks>
            If encoding is provided by both the 'charset' and the <paramref name="contentEncoding"/> parameters, then
            the <paramref name="contentEncoding"/> parameter is chosen as the final encoding.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Content(System.String,Microsoft.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object with <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/> by specifying a
            <paramref name="content"/> string and a <paramref name="contentType"/>.
            </summary>
            <param name="content">The content to write to the response.</param>
            <param name="contentType">The content type (MIME type).</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ContentResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Forbid">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> by default).
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> for the response.</returns>
            <remarks>
            Some authentication schemes, such as cookies, will convert <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> to
            a redirect to show a login page.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Forbid(System.String[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> by default) with the
            specified authentication schemes.
            </summary>
            <param name="authenticationSchemes">The authentication schemes to challenge.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> for the response.</returns>
            <remarks>
            Some authentication schemes, such as cookies, will convert <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> to
            a redirect to show a login page.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Forbid(Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> by default) with the
            specified <paramref name="properties" />.
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> for the response.</returns>
            <remarks>
            Some authentication schemes, such as cookies, will convert <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> to
            a redirect to show a login page.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Forbid(Microsoft.AspNetCore.Authentication.AuthenticationProperties,System.String[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> by default) with the
            specified authentication schemes and <paramref name="properties" />.
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
            <param name="authenticationSchemes">The authentication schemes to challenge.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ForbidResult"/> for the response.</returns>
            <remarks>
            Some authentication schemes, such as cookies, will convert <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> to
            a redirect to show a login page.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.File(System.Byte[],System.String)">
            <summary>
            Returns a file with the specified <paramref name="fileContents" /> as content
            (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>) and the specified <paramref name="contentType" /> as the Content-Type.
            </summary>
            <param name="fileContents">The file contents.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.FileContentResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.File(System.Byte[],System.String,System.String)">
            <summary>
            Returns a file with the specified <paramref name="fileContents" /> as content (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>), the
            specified <paramref name="contentType" /> as the Content-Type and the
            specified <paramref name="fileDownloadName" /> as the suggested file name.
            </summary>
            <param name="fileContents">The file contents.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <param name="fileDownloadName">The suggested file name.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.FileContentResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.File(System.IO.Stream,System.String)">
            <summary>
            Returns a file in the specified <paramref name="fileStream" /> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>)
            with the specified <paramref name="contentType" /> as the Content-Type.
            </summary>
            <param name="fileStream">The <see cref="T:System.IO.Stream"/> with the contents of the file.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.FileStreamResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.File(System.IO.Stream,System.String,System.String)">
            <summary>
            Returns a file in the specified <paramref name="fileStream" /> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>) with the
            specified <paramref name="contentType" /> as the Content-Type and the
            specified <paramref name="fileDownloadName" /> as the suggested file name.
            </summary>
            <param name="fileStream">The <see cref="T:System.IO.Stream"/> with the contents of the file.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <param name="fileDownloadName">The suggested file name.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.FileStreamResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.File(System.String,System.String)">
            <summary>
            Returns the file specified by <paramref name="virtualPath" /> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>) with the
            specified <paramref name="contentType" /> as the Content-Type.
            </summary>
            <param name="virtualPath">The virtual path of the file to be returned.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.VirtualFileResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.File(System.String,System.String,System.String)">
            <summary>
            Returns the file specified by <paramref name="virtualPath" /> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>) with the
            specified <paramref name="contentType" /> as the Content-Type and the
            specified <paramref name="fileDownloadName" /> as the suggested file name.
            </summary>
            <param name="virtualPath">The virtual path of the file to be returned.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <param name="fileDownloadName">The suggested file name.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.VirtualFileResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.LocalRedirect(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> object that redirects
            (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified local <paramref name="localUrl"/>.
            </summary>
            <param name="localUrl">The local URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.LocalRedirectPermanent(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> object with <see cref="P:Microsoft.AspNetCore.Mvc.LocalRedirectResult.Permanent"/> set to
            true (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) using the specified <paramref name="localUrl"/>.
            </summary>
            <param name="localUrl">The local URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.LocalRedirectPreserveMethod(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> object with <see cref="P:Microsoft.AspNetCore.Mvc.LocalRedirectResult.Permanent"/> set to
            false and <see cref="P:Microsoft.AspNetCore.Mvc.LocalRedirectResult.PreserveMethod"/> set to true
            (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status307TemporaryRedirect"/>) using the specified <paramref name="localUrl"/>.
            </summary>
            <param name="localUrl">The local URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.LocalRedirectPermanentPreserveMethod(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> object with <see cref="P:Microsoft.AspNetCore.Mvc.LocalRedirectResult.Permanent"/> set to
            true and <see cref="P:Microsoft.AspNetCore.Mvc.LocalRedirectResult.PreserveMethod"/> set to true
            (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status308PermanentRedirect"/>) using the specified <paramref name="localUrl"/>.
            </summary>
            <param name="localUrl">The local URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.LocalRedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.NotFound">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Mvc.NotFoundResult"/> that produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status404NotFound"/> response.
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.NotFoundResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.NotFound(System.Object)">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Mvc.NotFoundObjectResult"/> that produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status404NotFound"/> response.
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.NotFoundObjectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Page">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageResult"/> object that renders the page.
            </summary>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.PhysicalFile(System.String,System.String)">
            <summary>
            Returns the file specified by <paramref name="physicalPath" /> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>) with the
            specified <paramref name="contentType" /> as the Content-Type.
            </summary>
            <param name="physicalPath">The physical path of the file to be returned.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.PhysicalFileResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.PhysicalFile(System.String,System.String,System.String)">
            <summary>
            Returns the file specified by <paramref name="physicalPath" /> (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/>) with the
            specified <paramref name="contentType" /> as the Content-Type and the
            specified <paramref name="fileDownloadName" /> as the suggested file name.
            </summary>
            <param name="physicalPath">The physical path of the file to be returned.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <param name="fileDownloadName">The suggested file name.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.PhysicalFileResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Redirect(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> object that redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>)
            to the specified <paramref name="url"/>.
            </summary>
            <param name="url">The URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectPermanent(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> object with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectResult.Permanent"/> set to true
            (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) using the specified <paramref name="url"/>.
            </summary>
            <param name="url">The URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectPreserveMethod(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> object with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectResult.Permanent"/> set to false
            and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectResult.PreserveMethod"/> set to true (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status307TemporaryRedirect"/>)
            using the specified <paramref name="url"/>.
            </summary>
            <param name="url">The URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectPermanentPreserveMethod(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> object with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectResult.Permanent"/> set to true
            and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectResult.PreserveMethod"/> set to true (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status308PermanentRedirect"/>)
            using the specified <paramref name="url"/>.
            </summary>
            <param name="url">The URL to redirect to.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToAction(System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified action using the <paramref name="actionName"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToAction(System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified action using the
            <paramref name="actionName"/> and <paramref name="routeValues"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToAction(System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified action using the
            <paramref name="actionName"/> and the <paramref name="controllerName"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the pageModel.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToAction(System.String,System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified action using the specified
            <paramref name="actionName"/>, <paramref name="controllerName"/>, and <paramref name="routeValues"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the pageModel.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToAction(System.String,System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified action using the specified
            <paramref name="actionName"/>, <paramref name="controllerName"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the pageModel.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToAction(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified action using the specified <paramref name="actionName"/>,
            <paramref name="controllerName"/>, <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the pageModel.</param>
            <param name="routeValues">The parameters for a route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToActionPreserveMethod(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status307TemporaryRedirect"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to false and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.PreserveMethod"/>
            set to true, using the specified <paramref name="actionName"/>, <paramref name="controllerName"/>,
            <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the pageModel.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToActionPermanent(System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to true using the specified <paramref name="actionName"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToActionPermanent(System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to true using the specified <paramref name="actionName"/>
            and <paramref name="routeValues"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToActionPermanent(System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to true using the specified <paramref name="actionName"/>
            and <paramref name="controllerName"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the pageModel.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToActionPermanent(System.String,System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to true using the specified <paramref name="actionName"/>,
            <paramref name="controllerName"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the pageModel.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToActionPermanent(System.String,System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to true using the specified <paramref name="actionName"/>,
            <paramref name="controllerName"/>, and <paramref name="routeValues"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the pageModel.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToActionPermanent(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to true using the specified <paramref name="actionName"/>,
            <paramref name="controllerName"/>, <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the pageModel.</param>
            <param name="routeValues">The parameters for a route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToActionPermanentPreserveMethod(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status308PermanentRedirect"/>) to the specified action with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.Permanent"/> set to true and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToActionResult.PreserveMethod"/>
            set to true, using the specified <paramref name="actionName"/>, <paramref name="controllerName"/>,
            <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="actionName">The name of the action.</param>
            <param name="controllerName">The name of the pageModel.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToActionResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToRoute(System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified route using the specified <paramref name="routeName"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToRoute(System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified route using the specified <paramref name="routeValues"/>.
            </summary>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToRoute(System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified route using the specified
            <paramref name="routeName"/> and <paramref name="routeValues"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToRoute(System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified route using the specified
            <paramref name="routeName"/> and <paramref name="fragment"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToRoute(System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified route using the specified
            <paramref name="routeName"/>, <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for a route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToRoutePreserveMethod(System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status307TemporaryRedirect"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to false and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.PreserveMethod"/>
            set to true, using the specified <paramref name="routeName"/>, <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToRoutePermanent(System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to true using the specified <paramref name="routeName"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToRoutePermanent(System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to true using the specified <paramref name="routeValues"/>.
            </summary>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToRoutePermanent(System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to true using the specified <paramref name="routeName"/>
            and <paramref name="routeValues"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToRoutePermanent(System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to true using the specified <paramref name="routeName"/>
            and <paramref name="fragment"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToRoutePermanent(System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to true using the specified <paramref name="routeName"/>,
            <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for a route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToRoutePermanentPreserveMethod(System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status308PermanentRedirect"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to true and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.PreserveMethod"/>
            set to true, using the specified <paramref name="routeName"/>, <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPage">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the current page.
            </summary>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPage(System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the current page with the specified <paramref name="routeValues"/>.
            </summary>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPage(System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified <paramref name="pageName"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPage(System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="pageHandler"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPage(System.String,System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="pageHandler"/> and <paramref name="routeValues"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPage(System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="routeValues"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPage(System.String,System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="fragment"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPage(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="routeValues"/> and <paramref name="fragment"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="routeValues">The parameters for a route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPagePermanent(System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified <paramref name="pageName"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/> with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToPageResult.Permanent"/> set.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPagePermanent(System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="routeValues"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/> with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToPageResult.Permanent"/> set.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPagePermanent(System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified <paramref name="pageName"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/> with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToPageResult.Permanent"/> set.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPagePermanent(System.String,System.String,System.Object)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="routeValues"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="routeValues">The parameters for a route.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/> with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToPageResult.Permanent"/> set.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPagePermanent(System.String,System.String,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="fragment"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/> with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToPageResult.Permanent"/> set.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPagePermanent(System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="fragment"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="routeValues">The parameters for a route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/> with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToPageResult.Permanent"/> set.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPagePermanent(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/>) to the specified <paramref name="pageName"/>
            using the specified <paramref name="routeValues"/> and <paramref name="fragment"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="routeValues">The parameters for a route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToPageResult"/> with <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToPageResult.Permanent"/> set.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPagePreserveMethod(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status307TemporaryRedirect"/>) to the specified page with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to false and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.PreserveMethod"/>
            set to true, using the specified <paramref name="pageName"/>, <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.RedirectToPagePermanentPreserveMethod(System.String,System.String,System.Object,System.String)">
            <summary>
            Redirects (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status308PermanentRedirect"/>) to the specified route with
            <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.Permanent"/> set to true and <see cref="P:Microsoft.AspNetCore.Mvc.RedirectToRouteResult.PreserveMethod"/>
            set to true, using the specified <paramref name="pageName"/>, <paramref name="routeValues"/>, and <paramref name="fragment"/>.
            </summary>
            <param name="pageName">The name of the page.</param>
            <param name="pageHandler">The page handler to redirect to.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.RedirectToRouteResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.SignIn(System.Security.Claims.ClaimsPrincipal,System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.SignInResult"/> with the specified authentication scheme.
            </summary>
            <param name="principal">The <see cref="T:System.Security.Claims.ClaimsPrincipal"/> containing the user claims.</param>
            <param name="authenticationScheme">The authentication scheme to use for the sign-in operation.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.SignInResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.SignIn(System.Security.Claims.ClaimsPrincipal,Microsoft.AspNetCore.Authentication.AuthenticationProperties,System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.SignInResult"/> with the specified authentication scheme and
            <paramref name="properties" />.
            </summary>
            <param name="principal">The <see cref="T:System.Security.Claims.ClaimsPrincipal"/> containing the user claims.</param>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the sign-in operation.</param>
            <param name="authenticationScheme">The authentication scheme to use for the sign-in operation.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.SignInResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.SignOut(System.String[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.SignOutResult"/> with the specified authentication schemes.
            </summary>
            <param name="authenticationSchemes">The authentication schemes to use for the sign-out operation.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.SignOutResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.SignOut(Microsoft.AspNetCore.Authentication.AuthenticationProperties,System.String[])">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.SignOutResult"/> with the specified authentication schemes and
            <paramref name="properties" />.
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the sign-out operation.</param>
            <param name="authenticationSchemes">The authentication scheme to use for the sign-out operation.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.SignOutResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.StatusCode(System.Int32)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.StatusCodeResult"/> object by specifying a <paramref name="statusCode"/>.
            </summary>
            <param name="statusCode">The status code to set on the response.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.StatusCodeResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.StatusCode(System.Int32,System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ObjectResult"/> object by specifying a <paramref name="statusCode"/> and <paramref name="value"/>
            </summary>
            <param name="statusCode">The status code to set on the response.</param>
            <param name="value">The value to set on the <see cref="T:Microsoft.AspNetCore.Mvc.ObjectResult"/>.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ObjectResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Unauthorized">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Mvc.UnauthorizedResult"/> that produces an <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized"/> response.
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.UnauthorizedResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Partial(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.PartialViewResult"/> by specifying the name of a partial to render.
            </summary>
            <param name="viewName">The partial name.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.PartialViewResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.Partial(System.String,System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.PartialViewResult"/> by specifying the name of a partial to render and the model object.
            </summary>
            <param name="viewName">The partial name.</param>
            <param name="model">The model to be passed into the partial.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.PartialViewResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.ViewComponent(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> by specifying the name of a view component to render.
            </summary>
            <param name="componentName">
            The view component name. Can be a view component
            <see cref="P:Microsoft.AspNetCore.Mvc.ViewComponents.ViewComponentDescriptor.ShortName"/> or
            <see cref="P:Microsoft.AspNetCore.Mvc.ViewComponents.ViewComponentDescriptor.FullName"/>.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.ViewComponent(System.Type)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> by specifying the <see cref="T:System.Type"/> of a view component to
            render.
            </summary>
            <param name="componentType">The view component <see cref="T:System.Type"/>.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.ViewComponent(System.String,System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> by specifying the name of a view component to render.
            </summary>
            <param name="componentName">
            The view component name. Can be a view component
            <see cref="P:Microsoft.AspNetCore.Mvc.ViewComponents.ViewComponentDescriptor.ShortName"/> or
            <see cref="P:Microsoft.AspNetCore.Mvc.ViewComponents.ViewComponentDescriptor.FullName"/>.</param>
            <param name="arguments">
            An <see cref="T:System.Object"/> with properties representing arguments to be passed to the invoked view component
            method. Alternatively, an <see cref="T:System.Collections.Generic.IDictionary`2"/> instance
            containing the invocation arguments.
            </param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.ViewComponent(System.Type,System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> by specifying the <see cref="T:System.Type"/> of a view component to
            render.
            </summary>
            <param name="componentType">The view component <see cref="T:System.Type"/>.</param>
            <param name="arguments">
            An <see cref="T:System.Object"/> with properties representing arguments to be passed to the invoked view component
            method. Alternatively, an <see cref="T:System.Collections.Generic.IDictionary`2"/> instance
            containing the invocation arguments.
            </param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Mvc.ViewComponentResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.TryValidateModel(System.Object)">
            <summary>
            Validates the specified <paramref name="model"/> instance.
            </summary>
            <param name="model">The model to validate.</param>
            <returns><c>true</c> if the <see cref="P:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.ModelState"/> is valid; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.TryValidateModel(System.Object,System.String)">
            <summary>
            Validates the specified <paramref name="model"/> instance.
            </summary>
            <param name="model">The model to validate.</param>
            <param name="name">The key to use when looking up information in <see cref="P:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.ModelState"/>.
            </param>
            <returns><c>true</c> if the <see cref="P:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.ModelState"/> is valid;<c>false</c> otherwise.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.OnPageHandlerSelected(Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext)">
            <summary>
            Called after a handler method has been selected, but before model binding occurs.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.OnPageHandlerExecuting(Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext)">
            <summary>
            Called before the handler method executes, after model binding is complete.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.OnPageHandlerExecuted(Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext)">
            <summary>
            Called after the handler method executes, before the action result executes.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.OnPageHandlerSelectionAsync(Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext)">
            <summary>
            Called asynchronously after the handler method has been selected, but before model binding occurs.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion indicates the filter has executed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageModel.OnPageHandlerExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext,Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutionDelegate)">
            <summary>
            Called asynchronously before the handler method is invoked, after model binding is complete.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext"/>.</param>
            <param name="next">
            The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutionDelegate"/>. Invoked to execute the next page filter or the handler method itself.
            </param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion indicates the filter has executed.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.PageResult">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Mvc.ActionResult"/> that renders a Razor Page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageResult.ContentType">
            <summary>
            Gets or sets the Content-Type header for the response.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageResult.Model">
            <summary>
            Gets the page model.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageResult.Page">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageBase"/> to be executed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageResult.ViewData">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary"/> for the page to be executed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.PageResult.StatusCode">
            <summary>
            Gets or sets the HTTP status code.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.PageResult.ExecuteResultAsync(Microsoft.AspNetCore.Mvc.ActionContext)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.RazorPages.RazorPagesOptions">
            <summary>
            Provides configuration for Razor Pages.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.RazorPagesOptions.Conventions">
            <summary>
            Gets a collection of <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IPageConvention"/> instances that are applied during
            route and page model construction.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.RazorPagesOptions.RootDirectory">
            <summary>
            Application relative path used as the root of discovery for Razor Page files.
            Defaults to the <c>/Pages</c> directory under application root.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Resources.PropertyOfTypeCannotBeNull">
            <summary>The '{0}' property of '{1}' must not be null.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Resources.FormatPropertyOfTypeCannotBeNull(System.Object,System.Object)">
            <summary>The '{0}' property of '{1}' must not be null.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Resources.ActivatedInstance_MustBeAnInstanceOf">
            <summary>Page created by '{0}' must be an instance of '{1}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Resources.FormatActivatedInstance_MustBeAnInstanceOf(System.Object,System.Object)">
            <summary>Page created by '{0}' must be an instance of '{1}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Resources.PageViewResult_ContextIsInvalid">
            <summary>The context used to execute '{0}' must be an instance of '{1}'. Returning a '{2}' from a controller is a not supported.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Resources.FormatPageViewResult_ContextIsInvalid(System.Object,System.Object,System.Object)">
            <summary>The context used to execute '{0}' must be an instance of '{1}'. Returning a '{2}' from a controller is a not supported.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Resources.ArgumentCannotBeNullOrEmpty">
            <summary>Value cannot be null or empty.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Resources.UnsupportedHandlerMethodType">
            <summary>Unsupported handler method return type '{0}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Resources.FormatUnsupportedHandlerMethodType(System.Object)">
            <summary>Unsupported handler method return type '{0}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Resources.AmbiguousHandler">
            <summary>Multiple handlers matched. The following handlers matched route data and had all constraints satisfied:{0}{0}{1}</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Resources.FormatAmbiguousHandler(System.Object,System.Object)">
            <summary>Multiple handlers matched. The following handlers matched route data and had all constraints satisfied:{0}{0}{1}</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Resources.PathMustBeRootRelativePath">
            <summary>Path must be a root relative path that starts with a forward slash '/'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Resources.AsyncPageFilter_InvalidShortCircuit">
            <summary>If an {0} provides a result value by setting the {1} property of {2} to a non-null value, then it cannot call the next filter by invoking {3}.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Resources.FormatAsyncPageFilter_InvalidShortCircuit(System.Object,System.Object,System.Object,System.Object)">
            <summary>If an {0} provides a result value by setting the {1} property of {2} to a non-null value, then it cannot call the next filter by invoking {3}.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Resources.InvalidPageType_WrongBase">
            <summary>The type '{0}' is not a valid page. A page must inherit from '{1}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Resources.FormatInvalidPageType_WrongBase(System.Object,System.Object)">
            <summary>The type '{0}' is not a valid page. A page must inherit from '{1}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Resources.InvalidPageType_NoModelProperty">
            <summary>The type '{0}' is not a valid page. A page must define a public, non-static '{1}' property.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Resources.FormatInvalidPageType_NoModelProperty(System.Object,System.Object)">
            <summary>The type '{0}' is not a valid page. A page must define a public, non-static '{1}' property.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Resources.InvalidValidPageName">
            <summary>'{0}' is not a valid page name. A page name is path relative to the Razor Pages root directory that starts with a leading forward slash ('/') and does not contain the file extension e.g "/Users/<USER>".</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Resources.FormatInvalidValidPageName(System.Object)">
            <summary>'{0}' is not a valid page name. A page name is path relative to the Razor Pages root directory that starts with a leading forward slash ('/') and does not contain the file extension e.g "/Users/<USER>".</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.RazorPages.Resources.InvalidActionDescriptorModelType">
            <summary>The model type for '{0}' is of type '{1}' which is not assignable to its declared model type '{2}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.RazorPages.Resources.FormatInvalidActionDescriptorModelType(System.Object,System.Object,System.Object)">
            <summary>The model type for '{0}' is of type '{1}' which is not assignable to its declared model type '{2}'.</summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncPageFilter">
            <summary>
            A filter that asynchronously surrounds execution of a page handler method. This filter is executed only when
            decorated on a handler's type and not on individual handler methods.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IAsyncPageFilter.OnPageHandlerSelectionAsync(Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext)">
            <summary>
            Called asynchronously after the handler method has been selected, but before model binding occurs.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion indicates the filter has executed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IAsyncPageFilter.OnPageHandlerExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext,Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutionDelegate)">
            <summary>
            Called asynchronously before the handler method is invoked, after model binding is complete.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext"/>.</param>
            <param name="next">
            The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutionDelegate"/>. Invoked to execute the next page filter or the handler method itself.
            </param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion indicates the filter has executed.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IPageFilter">
            <summary>
            A filter that surrounds execution of a page handler method. This filter is executed only when decorated on a
            handler's type and not on individual handler methods.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IPageFilter.OnPageHandlerSelected(Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext)">
            <summary>
            Called after a handler method has been selected, but before model binding occurs.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IPageFilter.OnPageHandlerExecuting(Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext)">
            <summary>
            Called before the handler method executes, after model binding is complete.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IPageFilter.OnPageHandlerExecuted(Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext)">
            <summary>
            Called after the handler method executes, before the action result executes.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext">
            <summary>
            A context for page filters, used specifically in 
            <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IPageFilter.OnPageHandlerExecuted(Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext)"/> and 
            <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IAsyncPageFilter.OnPageHandlerExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext,Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutionDelegate)"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext.#ctor(Microsoft.AspNetCore.Mvc.RazorPages.PageContext,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata},Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.HandlerMethodDescriptor,System.Object)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext"/>.
            </summary>
            <param name="pageContext">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageContext"/> associated with the current request.</param>
            <param name="filters">The set of filters associated with the page.</param>
            <param name="handlerMethod">The handler method to be invoked, may be null.</param>
            <param name="handlerInstance">The handler instance associated with the page.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext.ActionDescriptor">
            <summary>
            Gets the descriptor associated with the current page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext.Canceled">
            <summary>
            Gets or sets an indication that an page filter short-circuited the action and the page filter pipeline.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext.HandlerInstance">
            <summary>
            Gets the handler instance containing the handler method.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext.HandlerMethod">
            <summary>
            Gets the descriptor for the handler method that was invoked.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext.Exception">
            <summary>
            Gets or sets the <see cref="T:System.Exception"/> caught while executing the action or action filters, if
            any.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext.ExceptionDispatchInfo">
            <summary>
            Gets or sets the <see cref="T:System.Runtime.ExceptionServices.ExceptionDispatchInfo"/> for the
            <see cref="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext.Exception"/>, if an <see cref="T:System.Exception"/> was caught and this information captured.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext.ExceptionHandled">
            <summary>
            Gets or sets an indication that the <see cref="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext.Exception"/> has been handled.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext.Result">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.IActionResult"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext">
            <summary>
            A context for page filters, used specifically in 
            <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IPageFilter.OnPageHandlerExecuting(Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext)"/> and 
            <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IAsyncPageFilter.OnPageHandlerExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext,Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutionDelegate)"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext.#ctor(Microsoft.AspNetCore.Mvc.RazorPages.PageContext,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata},Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.HandlerMethodDescriptor,System.Collections.Generic.IDictionary{System.String,System.Object},System.Object)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext"/>.
            </summary>
            <param name="pageContext">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageContext"/> associated with the current request.</param>
            <param name="filters">The set of filters associated with the page.</param>
            <param name="handlerMethod">The handler method to be invoked, may be null.</param>
            <param name="handlerArguments">The arguments to provide to the handler method.</param>
            <param name="handlerInstance">The handler instance associated with the page.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext.ActionDescriptor">
            <summary>
            Gets the descriptor associated with the current page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext.Result">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.IActionResult"/> to execute. Setting <see cref="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext.Result"/> to a non-<c>null</c>
            value inside a page filter will short-circuit the page and any remaining page filters.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext.HandlerArguments">
            <summary>
            Gets the arguments to pass when invoking the handler method. Keys are parameter names.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext.HandlerMethod">
            <summary>
            Gets the descriptor for the handler method about to be invoked.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext.HandlerInstance">
            <summary>
            Gets the object instance containing the handler method.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutionDelegate">
            <summary>
            A delegate that asynchronously returns a <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext"/> indicating the page or the next
            page filter has executed.
            </summary>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns an <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext"/>.
            </returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerPageFilter.Order">
            <remarks>
            Filters on handlers run furthest from the action.
            </remarks>t
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerResultFilter.Order">
            <remarks>
            Filters on handlers run furthest from the action.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext">
            <summary>
            A context for page filters, used specifically in 
            <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IPageFilter.OnPageHandlerSelected(Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext)"/> and 
            <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IAsyncPageFilter.OnPageHandlerSelectionAsync(Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext)"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext.#ctor(Microsoft.AspNetCore.Mvc.RazorPages.PageContext,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata},System.Object)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutedContext"/>.
            </summary>
            <param name="pageContext">The <see cref="T:Microsoft.AspNetCore.Mvc.RazorPages.PageContext"/> associated with the current request.</param>
            <param name="filters">The set of filters associated with the page.</param>
            <param name="handlerInstance">The handler instance associated with the page.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext.ActionDescriptor">
            <summary>
            Gets the descriptor associated with the current page.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext.HandlerMethod">
            <summary>
            Gets or sets the descriptor for the handler method about to be invoked.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext.HandlerInstance">
            <summary>
            Gets the object instance containing the handler method.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.PageResponseCacheFilter">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IPageFilter"/> which sets the appropriate headers related to response caching.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.PageResponseCacheFilter.#ctor(Microsoft.AspNetCore.Mvc.CacheProfile,Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageResponseCacheFilter"/>
            </summary>
            <param name="cacheProfile">The profile which contains the settings for
            <see cref="T:Microsoft.AspNetCore.Mvc.Filters.PageResponseCacheFilter"/>.</param>
            <param name="loggerFactory">The <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageResponseCacheFilter.Duration">
            <summary>
            Gets or sets the duration in seconds for which the response is cached.
            This is a required parameter.
            This sets "max-age" in "Cache-control" header.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageResponseCacheFilter.Location">
            <summary>
            Gets or sets the location where the data from a particular URL must be cached.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageResponseCacheFilter.NoStore">
            <summary>
            Gets or sets the value which determines whether the data should be stored or not.
            When set to <see langword="true"/>, it sets "Cache-control" header to "no-store".
            Ignores the "Location" parameter for values other than "None".
            Ignores the "duration" parameter.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageResponseCacheFilter.VaryByHeader">
            <summary>
            Gets or sets the value for the Vary response header.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.PageResponseCacheFilter.VaryByQueryKeys">
            <summary>
            Gets or sets the query keys to vary by.
            </summary>
            <remarks>
            <see cref="P:Microsoft.AspNetCore.Mvc.Filters.PageResponseCacheFilter.VaryByQueryKeys"/> requires the response cache middleware.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.PageActionEndpointConventionBuilder">
            <summary>
            Builds conventions that will be used for customization of <see cref="T:Microsoft.AspNetCore.Builder.EndpointBuilder"/> instances.
            </summary>
            <remarks>
            This interface is used at application startup to customize endpoints for the application.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.PageActionEndpointConventionBuilder.Add(System.Action{Microsoft.AspNetCore.Builder.EndpointBuilder})">
            <summary>
            Adds the specified convention to the builder. Conventions are used to customize <see cref="T:Microsoft.AspNetCore.Builder.EndpointBuilder"/> instances.
            </summary>
            <param name="convention">The convention to add to the builder.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions">
            <summary>
            Contains extension methods for using Razor Pages with <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapRazorPages(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder)">
            <summary>
            Adds endpoints for Razor Pages to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/>.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/>.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Builder.PageActionEndpointConventionBuilder"/> for endpoints associated with Razor Pages.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapFallbackToPage(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String)">
            <summary>
            Adds a specialized <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> that will match
            requests for non-file-names with the lowest possible priority. The request will be routed to a page endpoint that
            matches <paramref name="page"/>.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add the route to.</param>
            <param name="page">The page name.</param>
            <remarks>
            <para>
            <see cref="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapFallbackToPage(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String)"/> is intended to handle cases where URL path of
            the request does not contain a file name, and no other endpoint has matched. This is convenient for routing
            requests for dynamic content to a SPA framework, while also allowing requests for non-existent files to
            result in an HTTP 404.
            </para>
            <para>
            <see cref="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapFallbackToPage(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String)"/> registers an endpoint using the pattern
            <c>{*path:nonfile}</c>. The order of the registered endpoint will be <c>int.MaxValue</c>.
            </para>
            <para>
            <see cref="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapFallbackToPage(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String)"/> does not re-execute routing, and will
            not generate route values based on routes defined elsewhere. When using this overload, the <c>path</c> route value
            will be available.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapFallbackToPage(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,System.String)">
            <summary>
            Adds a specialized <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> that will match
            requests for non-file-names with the lowest possible priority. The request will be routed to a page endpoint that
            matches <paramref name="page"/>.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add the route to.</param>
            <param name="pattern">The route pattern.</param>
            <param name="page">The action name.</param>
            <remarks>
            <para>
            <see cref="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapFallbackToPage(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,System.String)"/> is intended to handle cases where URL path of
            the request does not contain a file name, and no other endpoint has matched. This is convenient for routing
            requests for dynamic content to a SPA framework, while also allowing requests for non-existent files to
            result in an HTTP 404.
            </para>
            <para>
            The order of the registered endpoint will be <c>int.MaxValue</c>.
            </para>
            <para>
            This overload will use the provided <paramref name="pattern"/> verbatim. Use the <c>:nonfile</c> route contraint
            to exclude requests for static files.
            </para>
            <para>
            <see cref="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapFallbackToPage(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,System.String)"/> does not re-execute routing, and will
            not generate route values based on routes defined elsewhere. When using this overload, the route values provided by matching
            <paramref name="pattern"/> will be available.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapFallbackToAreaPage(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,System.String)">
            <summary>
            Adds a specialized <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> that will match
            requests for non-file-names with the lowest possible priority. The request will be routed to a page endpoint that
            matches <paramref name="page"/>, and <paramref name="area"/>.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add the route to.</param>
            <param name="page">The action name.</param>
            <param name="area">The area name.</param>
            <remarks>
            <para>
            <see cref="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapFallbackToAreaPage(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,System.String)"/> is intended to handle cases where URL path of
            the request does not contain a file name, and no other endpoint has matched. This is convenient for routing
            requests for dynamic content to a SPA framework, while also allowing requests for non-existent files to
            result in an HTTP 404.
            </para>
            <para>
            <see cref="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapFallbackToAreaPage(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,System.String)"/> registers an endpoint using the pattern
            <c>{*path:nonfile}</c>. The order of the registered endpoint will be <c>int.MaxValue</c>.
            </para>
            <para>
            <see cref="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapFallbackToAreaPage(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,System.String)"/> does not re-execute routing, and will
            not generate route values based on routes defined elsewhere. When using this overload, the <c>path</c> route value
            will be available.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapFallbackToAreaPage(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,System.String,System.String)">
            <summary>
            Adds a specialized <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> that will match
            requests for non-file-names with the lowest possible priority. The request will be routed to a page endpoint that
            matches <paramref name="page"/>, and <paramref name="area"/>.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add the route to.</param>
            <param name="pattern">The route pattern.</param>
            <param name="page">The action name.</param>
            <param name="area">The area name.</param>
            <remarks>
            <para>
            <see cref="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapFallbackToAreaPage(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,System.String,System.String)"/> is intended to handle cases where URL path of
            the request does not contain a file name, and no other endpoint has matched. This is convenient for routing
            requests for dynamic content to a SPA framework, while also allowing requests for non-existent files to
            result in an HTTP 404.
            </para>
            <para>
            The order of the registered endpoint will be <c>int.MaxValue</c>.
            </para>
            <para>
            This overload will use the provided <paramref name="pattern"/> verbatim. Use the <c>:nonfile</c> route contraint
            to exclude requests for static files.
            </para>
            <para>
            <see cref="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapFallbackToAreaPage(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String,System.String,System.String)"/> does not re-execute routing, and will
            not generate route values based on routes defined elsewhere. When using this overload, the route values provided by matching
            <paramref name="pattern"/> will be available.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.RazorPagesEndpointRouteBuilderExtensions.MapDynamicPageRoute``1(Microsoft.AspNetCore.Routing.IEndpointRouteBuilder,System.String)">
            <summary>
            Adds a specialized <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> to the <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> that will
            attempt to select a page using the route values produced by <typeparamref name="TTransformer"/>.
            </summary>
            <param name="endpoints">The <see cref="T:Microsoft.AspNetCore.Routing.IEndpointRouteBuilder"/> to add the route to.</param>
            <param name="pattern">The URL pattern of the route.</param>
            <typeparam name="TTransformer">The type of a <see cref="T:Microsoft.AspNetCore.Mvc.Routing.DynamicRouteValueTransformer"/>.</typeparam>
            <remarks>
            <para>
            This method allows the registration of a <see cref="T:Microsoft.AspNetCore.Routing.RouteEndpoint"/> and <see cref="T:Microsoft.AspNetCore.Mvc.Routing.DynamicRouteValueTransformer"/>
            that combine to dynamically select a page using custom logic.
            </para>
            <para>
            The instance of <typeparamref name="TTransformer"/> will be retrieved from the dependency injection container.
            Register <typeparamref name="TTransformer"/> with the desired service lifetime in <c>ConfigureServices</c>.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.MvcRazorPagesMvcBuilderExtensions">
            <summary>
            Extensions methods for configuring Razor Pages via an <see cref="T:Microsoft.Extensions.DependencyInjection.IMvcBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.MvcRazorPagesMvcBuilderExtensions.AddRazorPagesOptions(Microsoft.Extensions.DependencyInjection.IMvcBuilder,System.Action{Microsoft.AspNetCore.Mvc.RazorPages.RazorPagesOptions})">
            <summary>
            Configures a set of <see cref="T:Microsoft.AspNetCore.Mvc.Razor.RazorViewEngineOptions"/> for the application.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IMvcBuilder"/>.</param>
            <param name="setupAction">An action to configure the <see cref="T:Microsoft.AspNetCore.Mvc.Razor.RazorViewEngineOptions"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IMvcBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.MvcRazorPagesMvcBuilderExtensions.WithRazorPagesRoot(Microsoft.Extensions.DependencyInjection.IMvcBuilder,System.String)">
            <summary>
            Configures Razor Pages to use the specified <paramref name="rootDirectory"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IMvcCoreBuilder"/>.</param>
            <param name="rootDirectory">The application relative path to use as the root directory.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IMvcBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.MvcRazorPagesMvcBuilderExtensions.WithRazorPagesAtContentRoot(Microsoft.Extensions.DependencyInjection.IMvcBuilder)">
            <summary>
            Configures Razor Pages to be rooted at the content root (<see cref="P:Microsoft.AspNetCore.Hosting.IHostingEnvironment.ContentRootPath"/>).
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IMvcCoreBuilder"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IMvcBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.MvcRazorPagesMvcCoreBuilderExtensions.WithRazorPagesRoot(Microsoft.Extensions.DependencyInjection.IMvcCoreBuilder,System.String)">
            <summary>
            Configures Razor Pages to use the specified <paramref name="rootDirectory"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IMvcCoreBuilder"/>.</param>
            <param name="rootDirectory">The application relative path to use as the root directory.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions">
            <summary>
            Extensions for <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.ConfigureFilter(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.Func{Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel,Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata})">
            <summary>
            Configures the specified <paramref name="factory"/> to apply filters to all Razor Pages.
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
            <param name="factory">The factory to create filters.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.ConfigureFilter(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata)">
            <summary>
            Configures the specified <paramref name="filter"/> to apply to all Razor Pages.
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
            <param name="filter">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> to add.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.Add(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,Microsoft.AspNetCore.Mvc.ApplicationModels.IParameterModelBaseConvention)">
            <summary>
            Adds the specified <paramref name="convention"/> to <paramref name="conventions"/>.
            The added convention will apply to all handler properties and parameters on handler methods.
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
            <param name="convention">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.IParameterModelBaseConvention"/> to apply.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.AllowAnonymousToPage(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.String)">
            <summary>
            Allows anonymous access to the page with the specified name.
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
            <param name="pageName">The page name.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.AllowAnonymousToAreaPage(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.String,System.String)">
            <summary>
            Allows anonymous access to the page with the specified name located in the specified area.
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
            <param name="areaName">The area name.</param>
            <param name="pageName">
            The page name e.g. <c>/Users/<USER>/c>
            <para>
            The page name is the path of the file without extension, relative to the pages root directory for the specified area.
            e.g. the page name for the file Areas/Identity/Pages/Manage/Accounts.cshtml, is <c>/Manage/Accounts</c>.
            </para>
            </param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.AllowAnonymousToFolder(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.String)">
            <summary>
            Allows anonymous access to all pages under the specified folder.
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
            <param name="folderPath">The folder path.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.AllowAnonymousToAreaFolder(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.String,System.String)">
             <summary>
             Allows anonymous access to all pages under the specified area folder.
             </summary>
             <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
             <param name="areaName">The area name.</param>
             <param name="folderPath">
             The folder path e.g. <c>/Manage/</c>
             <para>
             The folder path is the path of the folder, relative to the pages root directory for the specified area.
             e.g. the folder path for the file Areas/Identity/Pages/Manage/Accounts.cshtml, is <c>/Manage</c>.
             </para>
            .</param>
             <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.AuthorizePage(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.String,System.String)">
            <summary>
            Requires authorization with the specified policy for the page with the specified name.
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
            <param name="pageName">The page name.</param>
            <param name="policy">The authorization policy.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.AuthorizePage(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.String)">
            <summary>
            Requires authorization for the specified page.
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
            <param name="pageName">The page name.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.AuthorizeAreaPage(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.String,System.String)">
            <summary>
            Requires authorization for the specified area page.
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
            <param name="areaName">The area name.</param>
            <param name="pageName">
            The page name e.g. <c>/Users/<USER>/c>
            <para>
            The page name is the path of the file without extension, relative to the pages root directory for the specified area.
            e.g. the page name for the file Areas/Identity/Pages/Manage/Accounts.cshtml, is <c>/Manage/Accounts</c>.
            </para>
            </param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.AuthorizeAreaPage(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.String,System.String,System.String)">
            <summary>
            Requires authorization for the specified area page with the specified policy.
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
            <param name="areaName">The area name.</param>
            <param name="pageName">
            The page name e.g. <c>/Users/<USER>/c>
            <para>
            The page name is the path of the file without extension, relative to the pages root directory for the specified area.
            e.g. the page name for the file Areas/Identity/Pages/Manage/Accounts.cshtml, is <c>/Manage/Accounts</c>.
            </para>
            </param>
            <param name="policy">The authorization policy.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.AuthorizeFolder(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.String,System.String)">
            <summary>
            Requires authorization for all pages under the specified folder.
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
            <param name="folderPath">The folder path.</param>
            <param name="policy">The authorization policy.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.AuthorizeFolder(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.String)">
            <summary>
            Requires authorization for all pages under the specified folder.
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
            <param name="folderPath">The folder path.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.AuthorizeAreaFolder(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.String,System.String)">
            <summary>
            Requires authorization with the default policy for all pages under the specified folder.
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
            <param name="areaName">The area name.</param>
            <param name="folderPath">
            The folder path e.g. <c>/Manage/</c>
            <para>
            The folder path is the path of the folder, relative to the pages root directory for the specified area.
            e.g. the folder path for the file Areas/Identity/Pages/Manage/Accounts.cshtml, is <c>/Manage</c>.
            </para>
            </param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.AuthorizeAreaFolder(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.String,System.String,System.String)">
            <summary>
            Requires authorization with the specified policy for all pages under the specified folder.
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/> to configure.</param>
            <param name="areaName">The area name.</param>
            <param name="folderPath">
            The folder path e.g. <c>/Manage/</c>
            <para>
            The folder path is the path of the folder, relative to the pages root directory for the specified area.
            e.g. the folder path for the file Areas/Identity/Pages/Manage/Accounts.cshtml, is <c>/Manage</c>.
            </para>
            </param>
            <param name="policy">The authorization policy.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.AddPageRoute(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.String,System.String)">
            <summary>
            Adds the specified <paramref name="route"/> to the page at the specified <paramref name="pageName"/>.
            <para>
            The page can be routed via <paramref name="route"/> in addition to the default set of path based routes.
            All links generated for this page will use the specified route.
            </para>
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</param>
            <param name="pageName">The page name.</param>
            <param name="route">The route to associate with the page.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PageConventionCollectionExtensions.AddAreaPageRoute(Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection,System.String,System.String,System.String)">
            <summary>
            Adds the specified <paramref name="route"/> to the page at the specified <paramref name="pageName"/> located in the specified
            area.
            <para>
            The page can be routed via <paramref name="route"/> in addition to the default set of path based routes.
            All links generated for this page will use the specified route.
            </para>
            </summary>
            <param name="conventions">The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</param>
            <param name="areaName">The area name.</param>
            <param name="pageName">
            The page name e.g. <c>/Users/<USER>/c>
            <para>
            The page name is the path of the file without extension, relative to the pages root directory for the specified area.
            e.g. the page name for the file Areas/Identity/Pages/Manage/Accounts.cshtml, is <c>/Manage/Accounts</c>.
            </para>
            </param>
            <param name="route">The route to associate with the page.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ApplicationModels.PageConventionCollection"/>.</returns>
        </member>
    </members>
</doc>
