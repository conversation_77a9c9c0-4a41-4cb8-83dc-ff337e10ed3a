﻿using Coldairarrow.Business.Wechat_CostDept;
using Coldairarrow.Entity.Wechat_CostDept;
using Coldairarrow.Util;
using Coldairarrow.Util.DTO;
using EFCore.Sharding;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Wechat_CostDept
{
    [Route("/Wechat_CostDept/[controller]/[action]")]
    public class Wechat_GuideController : BaseApiController
    {
        #region DI

        public Wechat_GuideController(IWechat_GuideBusiness wechat_GuideBus, IWechat_FileBusiness wechat_FileBus)
        {
            _wechat_GuideBus = wechat_GuideBus;
            _wechat_FileBus = wechat_FileBus;
        }

        IWechat_GuideBusiness _wechat_GuideBus { get; }
        IWechat_FileBusiness _wechat_FileBus { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Wechat_Guide>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _wechat_GuideBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<PageResult<Wechat_GuideDTO>> GetDataList_(PageInput<GuideInputDTO> input)
        {
            return await _wechat_GuideBus.GetDataListAsync_(input);
        }

        [HttpPost]
        public async Task<Wechat_Guide> GetTheData(IdInputDTO input)
        {
            return await _wechat_GuideBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Wechat_Guide data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _wechat_GuideBus.AddDataAsync(data);
            }
            else
            {
                await _wechat_GuideBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task SaveData_(Wechat_GuideDTO data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);
                var fileString = _wechat_FileBus.UpdateFils(data.Id,data.oldFiles,data.newFiles);
                data.W_File = fileString;
                await _wechat_GuideBus.AddDataAsync(data);
            }
            else
            {
                var fileString =  _wechat_FileBus.UpdateFils(data.Id, data.oldFiles, data.newFiles);
                data.W_File = fileString;
                await _wechat_GuideBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _wechat_GuideBus.DeleteDataAsync(ids);
        }

        #endregion

        #region 二次开发
        //根据大类id查询下面的id
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetListByPointId(string index)
        {
            try
            {
                var list = _wechat_GuideBus.GetListByPointId(index);
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error("失败"+ex.Message);
            }
        }
        //根据id查询具体信息
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetDataById(string index)
        {
            try
            {
                var guide = _wechat_GuideBus.GetTheDataAsync(index).Result;
                return Success(guide);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        #endregion
    }
}