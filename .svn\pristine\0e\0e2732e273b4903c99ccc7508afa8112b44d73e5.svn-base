import { Axios } from "@/utils/plugin/axios-plugin"

let permissions = []
let inited = false

let OperatorCache = {
  info: {},
  EmployeesInfo: {},
  inited () {
    return inited
  },
  init (callBack) {
    if (inited)
      callBack()
    else {
      Axios.post('/Base_Manage/Home/GetOperatorInfo').then(resJson => {
        if (resJson.Data) {
          this.info = resJson.Data.UserInfo
          permissions = resJson.Data.Permissions
          this.EmployeesInfo = resJson.Data.EmployeesInfo
          inited = true
          callBack()
        } else {
          // console.log(this.$router);
          //this.$router.replace('/Home/Login')
          // this.$router.push({ path: '/Home/Login' })
        }
      })
    }
  },
  hasPermission (thePermission) {
    return permissions.includes(thePermission)
  },
  clear () {
    inited = false
    permissions = []
    this.info = {}
  }
}

export default OperatorCache