﻿<template>
  <a-modal
    :title="title"
    width="40%"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="()=>{this.visible=false}"
  >
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="entity" :rules="rules" v-bind="layout">
        <a-form-model-item label="发起人的ID" prop="W_OpenId">
          <a-input v-model="entity.W_OpenId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="对应指引主键" prop="W_GuideId">
          <a-input v-model="entity.W_GuideId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="发起内容" prop="W_Content">
          <a-input v-model="entity.W_Content" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="附件编号" prop="W_File">
          <a-input v-model="entity.W_File" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="更新时间" prop="W_UpdateTime">
          <a-input v-model="entity.W_UpdateTime" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="名称" prop="W_Name">
          <a-input v-model="entity.W_Name" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="关联的ERPID" prop="W_ERPID">
          <a-input v-model="entity.W_ERPID" autocomplete="off" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      layout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 18 }
      },
      visible: false,
      loading: false,
      entity: {},
      rules: {},
      title: ''
    }
  },
  methods: {
    init() {
      this.visible = true
      this.entity = {}
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    openForm(id, title) {
      this.init()

      if (id) {
        this.loading = true
        this.$http.post('/Wechat_CostDept/Wechat_Commit/GetTheData', { id: id }).then(resJson => {
          this.loading = false

          this.entity = resJson.Data
        })
      }
    },
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.loading = true
        this.$http.post('/Wechat_CostDept/Wechat_Commit/SaveData', this.entity).then(resJson => {
          this.loading = false

          if (resJson.Success) {
            this.$message.success('操作成功!')
            this.visible = false

            this.parentObj.getDataList()
          } else {
            this.$message.error(resJson.Msg)
          }
        })
      })
    }
  }
}
</script>
