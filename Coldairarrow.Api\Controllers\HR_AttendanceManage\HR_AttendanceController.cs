﻿using Coldairarrow.Business.HR_AttendanceManage;
using Coldairarrow.Entity.HR_AttendanceManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Coldairarrow.Util.Excel.Model;

namespace Coldairarrow.Api.Controllers.HR_AttendanceManage
{
    [Route("/HR_AttendanceManage/[controller]/[action]")]
    public class HR_AttendanceController : BaseApiController
    {
        #region DI

        public HR_AttendanceController(IHR_AttendanceBusiness hR_AttendanceBus)
        {
            _hR_AttendanceBus = hR_AttendanceBus;
        }

        IHR_AttendanceBusiness _hR_AttendanceBus { get; }

        #endregion

        #region 获取

        /// <summary>
        /// 获得考勤报表数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public PageResult<CheckWorkAttendanceDTO> GetAttendanceReport(PageInput<ReportConditionDTO> input)
        {
            //var op = GetOperator();
            return _hR_AttendanceBus.GetAttendanceReport(input);
        }

        [HttpPost]
        public async Task<PageResult<HR_AttendanceDTO>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_AttendanceBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_AttendanceDTO> GetTheData(IdInputDTO input)
        {
            return await _hR_AttendanceBus.GetFormDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_Attendance data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_AttendanceBus.AddDataAsync(data);
            }
            else
            {
                await _hR_AttendanceBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_AttendanceBus.DeleteDataAsync(ids);
        }

        #endregion
        

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ReportConditionDTO> input)
        {
            try
            {
                input.PageRows = 10000;
                //取出数据源
                DataTable exportTable = _hR_AttendanceBus.GetAttendanceReport(input).Data.ToDataTable();
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                //excelconfig.Title = "人员异动详情";
                //excelconfig.TitleFont = "微软雅黑";
                //excelconfig.TitlePoint = 20;
                excelconfig.FileName = "考勤报表.xls";
                excelconfig.IsAllSizeColumn = true;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>
                {
                    new ColumnModel() { Column = "name", ExcelColumn = "姓名", Sort = 1 },
                    new ColumnModel() { Column = "departmentname", ExcelColumn = "部门", Sort = 2 },
                    new ColumnModel() { Column = "postname", ExcelColumn = "岗位", Sort = 3 },
                    new ColumnModel() { Column = "employeestatus", ExcelColumn = "员工状态", Sort = 4 },
                    new ColumnModel() { Column = "daysonduty", ExcelColumn = "应出勤天数", Sort = 5 },
                    new ColumnModel() { Column = "actualattendancedays", ExcelColumn = "实际出勤天数", Sort = 6 },
                    new ColumnModel() { Column = "welfareannualleave", ExcelColumn = "福利年假", Sort = 7 },
                    new ColumnModel() { Column = "statutoryannualleave", ExcelColumn = "法定年假", Sort = 9 },
                    new ColumnModel() { Column = "sickleave", ExcelColumn = "病假", Sort = 10 },
                    new ColumnModel() { Column = "marriageleave", ExcelColumn = "婚假", Sort = 11 },
                    new ColumnModel() { Column = "productioninspectionfraud", ExcelColumn = "产检假", Sort = 12 },
                    new ColumnModel() { Column = "maternityleave", ExcelColumn = "产假", Sort = 13 },
                    new ColumnModel() { Column = "escortleave", ExcelColumn = "陪护假", Sort = 14 },
                    new ColumnModel() { Column = "absenteeism", ExcelColumn = "旷工", Sort = 15 },
                    new ColumnModel() { Column = "compassionateleave", ExcelColumn = "事假", Sort = 16 },
                    new ColumnModel() { Column = "funeralleave", ExcelColumn = "丧假", Sort = 17 },
                    new ColumnModel() { Column = "other", ExcelColumn = "其他", Sort = 18 },
                    new ColumnModel() { Column = "totalovertimehours", ExcelColumn = "加班总时长", Sort = 19 },
                    new ColumnModel() { Column = "workingovertime", ExcelColumn = "工作日加班", Sort = 20 },
                    new ColumnModel() { Column = "overtimeonrestdays", ExcelColumn = "休息日加班", Sort = 21 },
                    new ColumnModel() { Column = "holidayovertime", ExcelColumn = "节日加班", Sort = 22 },
                    new ColumnModel() { Column = "remarks", ExcelColumn = "备注", Sort = 23 },
                };

                List<HeaderMultiMergeModel> headerMultis = new List<HeaderMultiMergeModel>();
                HeaderMultiMergeModel headerMulti = new HeaderMultiMergeModel
                {
                    Row = 0
                };
                List<HeaderMultiMergeDetails> mergeDetails = new List<HeaderMultiMergeDetails>();
                for (int a = 0; a < 9; a++)
                {
                    switch (a)
                    {
                        case 0:
                            mergeDetails.Add(AssignmentInfo("姓名", 1, 2, 0, 0));
                            break;
                        case 1:
                            mergeDetails.Add(AssignmentInfo("部门", 1, 2, 1, 1));
                            break;
                        case 2:
                            mergeDetails.Add(AssignmentInfo("岗位", 1, 2, 2, 2));
                            break;
                        case 3:
                            mergeDetails.Add(AssignmentInfo("员工状态", 1, 2, 3, 3));
                            break;
                        case 4:
                            mergeDetails.Add(AssignmentInfo("应出勤天数", 1, 2, 4, 4));
                            break;
                        case 5:
                            mergeDetails.Add(AssignmentInfo("实际出勤天数", 1, 2, 5, 5));
                            break;
                        case 6:
                            mergeDetails.Add(AssignmentInfo("缺勤情况", 1, 1, 6, 16));
                            break;
                        case 7:
                            mergeDetails.Add(AssignmentInfo("加班(小时)", 1, 1, 17, 20));
                            break;
                        case 8:
                            mergeDetails.Add(AssignmentInfo("备注", 1, 2, 21, 21));
                            break;
                        default:
                            break;
                    }
                }
                headerMulti.List = mergeDetails;
                headerMultis.Add(headerMulti);
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, headerMultis).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }

        /// <summary>
        /// 装载表头数据
        /// </summary>
        /// <param name="name">表头列名</param>
        /// <param name="fRow">开始行</param>
        /// <param name="lRow">最后行</param>
        /// <param name="fCol">开始列</param>
        /// <param name="lCol">最后列</param>
        /// <returns></returns>
        public HeaderMultiMergeDetails AssignmentInfo(string name, int fRow, int lRow, int fCol, int lCol)
        {
            return new HeaderMultiMergeDetails()
            {
                HeaderTitle = name,
                FirstCol = fCol,
                FirstRow = fRow,
                LastCol = lCol,
                LastRow = lRow,
            };
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<HR_Attendance>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _hR_AttendanceBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}