﻿using Coldairarrow.Business.Wechat_CostDept;
using Coldairarrow.Entity.Wechat_CostDept;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Wechat_CostDept
{
    [Route("/Wechat_CostDept/[controller]/[action]")]
    public class Wechat_FileController : BaseApiController
    {
        #region DI

        private static IConfiguration _configuration;
        private static IHostingEnvironment _hostingEnvironment;

        public Wechat_FileController(IWechat_FileBusiness wechat_FileBus, IConfiguration configuration, IHostingEnvironment hostingEnvironment)
        {
            _wechat_FileBus = wechat_FileBus;
            _configuration = configuration;
            _hostingEnvironment = hostingEnvironment;
        }

        IWechat_FileBusiness _wechat_FileBus { get; }


        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Wechat_File>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _wechat_FileBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Wechat_File> GetTheData(IdInputDTO input)
        {
            return await _wechat_FileBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Wechat_File data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _wechat_FileBus.AddDataAsync(data);
            }
            else
            {
                await _wechat_FileBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _wechat_FileBus.DeleteDataAsync(ids);
        }

        #endregion
        #region 二次开发
        /// <summary>
        /// 根据ID获取文件信息
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetDataById(string index)
        {
            try
            {
                var data = _wechat_FileBus.GetDataById(index);
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        //根据指引id查询存在的文件
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetListByGuideId(string index)
        {
            try
            {
                var list = _wechat_FileBus.GetListByGuideId(index);
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        //成本部后台文件上传
        [NoCheckJWT]
        [HttpPost]
        public IActionResult UploadFileByForm_(string ID)
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());
            var currentDate = DateTime.Now;
            string path = $"/Upload/{currentDate:yyyyMMdd}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            string url = $"{_configuration["WebRootUrl"]}{path}";
           // string url = _hostingEnvironment.ContentRootPath + path;
            var res = new
            {
                name = file.FileName,
                status = "done",
                thumbUrl = url,
                url = url
            };
            Wechat_File data = new Wechat_File();
            data.W_GuideId = ID;
            data.W_Name = res.name;
            data.W_ThumbUrl = res.thumbUrl;
            data.W_Url = res.url;
            data.W_Status = res.status;
            data.W_Isable = 1;
            data.W_Module = 0;
            InitEntity(data);
            var theFile = _wechat_FileBus.AddDataAsync(data);
            return JsonContent(res.ToJson());
        }


        //存储用户提交的文件
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult UploadCommitFile(string openId,string guildeId,string fileName,string fileUrl)
        {
            try
            {
                string url = $"{_configuration["WebRootUrl"]}{fileUrl}";
                Wechat_File data = new Wechat_File
                {
                    W_Name = fileName,
                    W_Url = url,
                    W_ThumbUrl = url,
                    W_Status = "done",
                    W_Isable = 1,
                    W_Module = 1,
                    W_GuideId = guildeId,
                    CreatorId = openId,
                    CreateTime = DateTime.Now,
                    Id = IdHelper.GetId()
                };
                _wechat_FileBus.AddDataAsync(data);
                return Success(data.Id);
            }
            catch (Exception ex)
            {
                return Error("失败:"+ex.Message);
            }
        }

        //获取系统上传的文件列表
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetSystemList()
        {
            try
            {
                var list = _wechat_FileBus.GetSystemList();
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 微信小程序上传图片
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult SingleFileUpload()
        {
            var formFile = Request.Form.Files["file"];//获取请求发送过来的文件
            var currentDate = DateTime.Now;
            var webRootPath = _hostingEnvironment.WebRootPath;//>>>相当于HttpContext.Current.Server.MapPath("") 

            try
            {
                var filePath = $"/UploadFile/{currentDate:yyyyMMdd}/";

                //创建每日存储文件夹
                if (!Directory.Exists(webRootPath + filePath))
                {
                    Directory.CreateDirectory(webRootPath + filePath);
                }

                if (formFile != null)
                {
                    //文件后缀
                    var fileExtension = Path.GetExtension(formFile.FileName);//获取文件格式，拓展名
                    if (fileExtension != ".xls" && fileExtension != ".xlsx" && fileExtension != ".pdf" && fileExtension != ".jpg" && fileExtension != ".png" && fileExtension != ".doc" && fileExtension != ".docx")
                    {
                        return Error("只支持上传execl、pdf、word、图片文件");
                    }
                    //判断文件大小
                    var fileSize = formFile.Length;

                    if (fileSize > 1024 * 1024 * 10) //10M TODO:(1mb=1024X1024b)
                    {
                        //return new JsonResult(new { isSuccess = false, resultMsg = "上传的文件不能大于10M" });
                        return Error("上传的文件不能大于10M");
                    }

                    //保存的文件名称(以名称和保存时间命名)
                    var saveName = formFile.FileName.Substring(0, formFile.FileName.LastIndexOf('.')) + "_" + currentDate.ToString("HHmmss") + fileExtension;

                    //文件保存
                    using (var fs = System.IO.File.Create(webRootPath + filePath + saveName))
                    {
                        formFile.CopyTo(fs);
                        fs.Flush();
                    }

                    //完整的文件路径
                    var completeFilePath = Path.Combine(filePath, saveName);

                    //return new JsonResult(new { isSuccess = true, returnMsg = "上传成功", completeFilePath = completeFilePath });
                    return Success(completeFilePath);
                }
                else
                {
                    //return new JsonResult(new { isSuccess = false, resultMsg = "上传失败，未检测上传的文件信息~" });
                    return Error("上传失败，未检测上传的文件信息~");
                }

            }
            catch (Exception ex)
            {
                //return new JsonResult(new { isSuccess = false, resultMsg = "文件保存失败，异常信息为：" + ex.Message });
                return Error("文件保存失败，异常信息为：" + ex.Message);
            }
        }
        #endregion
    }
}