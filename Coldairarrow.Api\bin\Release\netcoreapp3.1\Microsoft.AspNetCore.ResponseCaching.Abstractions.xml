<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.ResponseCaching.Abstractions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.ResponseCaching.IResponseCachingFeature">
            <summary>
            A feature for configuring additional response cache options on the HTTP response.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.ResponseCaching.IResponseCachingFeature.VaryByQueryKeys">
            <summary>
            Gets or sets the query keys used by the response cache middleware for calculating secondary vary keys.
            </summary>
        </member>
    </members>
</doc>
