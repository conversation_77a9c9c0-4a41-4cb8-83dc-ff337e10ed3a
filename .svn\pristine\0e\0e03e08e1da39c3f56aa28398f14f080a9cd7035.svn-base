﻿using Coldairarrow.Entity.WorkWx_Robot;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.WorkWx_Robot
{
    public interface IWorkWx_LogsBusiness
    {
        Task<PageResult<WorkWx_Logs>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<WorkWx_Logs> GetTheDataAsync(string id);
        Task AddDataAsync(WorkWx_Logs data);
        Task UpdateDataAsync(WorkWx_Logs data);
        Task DeleteDataAsync(List<string> ids);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);

        string sendRobotTextMsg(string robotUrl, string content, string user);

        string sendRobotImageMsg(string robotUrl, string content);
    }
}