﻿using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Coldairarrow.Business.HolidayManage;
using Newtonsoft.Json;
using Coldairarrow.Entity.Enum;
using Coldairarrow.Entity;

namespace Coldairarrow.Api.Controllers.HR_Manage
{
    [Route("/HR_Manage/[controller]/[action]")]
    public class HR_RecruitmentCandidatesController : BaseApiController
    {
        #region DI

        public HR_RecruitmentCandidatesController(IHR_RecruitmentCandidatesBusiness hR_RecruitmentCandidatesBus,
            IHR_EntryBusiness hR_Entry,
            IHR_RecruitBusiness hR_Recruit, IHR_ContentOperationLogBusiness hR_ContentOperationLog)
        {
            _hR_RecruitmentCandidatesBus = hR_RecruitmentCandidatesBus;
            _hR_Entry = hR_Entry;
            _hR_Recruit = hR_Recruit;
            _hR_ContentOperationLog = hR_ContentOperationLog;
        }
        IHR_ContentOperationLogBusiness _hR_ContentOperationLog { get; }
        IHR_RecruitmentCandidatesBusiness _hR_RecruitmentCandidatesBus { get; }
        IHR_EntryBusiness _hR_Entry { get; }
        IHR_RecruitBusiness _hR_Recruit { get; }

        #endregion

        #region 获取
        /// <summary>
        /// 简历信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PageResult<HR_RecruitmentCandidatesDTO>> GetResumeList(PageInput<ConditionDTO> input)
        {
            var op = GetOperator();
            return await _hR_RecruitmentCandidatesBus.GetResumeListAsync(input, op);
        }
        /// <summary>
        /// 获取项目进度
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public List<PostProgress> GetPostProgress(PageInput<ConditionDTO> input)
        {

            return _hR_RecruitmentCandidatesBus.GetPostProgress(input);
        }

        /// <summary>
        /// 简历信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PageResult<HR_RecruitmentCandidatesDTO>> GetRecruitmentJob(PageInput<ConditionDTO> input)
        {
            var op = GetOperator();
            return await _hR_RecruitmentCandidatesBus.GetRecruitmentJobAsync(input, op);
        }
        [HttpPost]
        public async Task<PageResult<HR_RecruitmentCandidates>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_RecruitmentCandidatesBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_RecruitmentCandidates> GetTheData(IdInputDTO input)
        {
            return await _hR_RecruitmentCandidatesBus.GetTheDataAsync(input.id);
        }
        /// <summary>
        /// 获取简历详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<RecruitmentDTO> GetFormTheData(IdInputDTO input)
        {
            return await _hR_RecruitmentCandidatesBus.GetFormTheDataAsnc(input.id);
        }

        /// <summary>
        /// 获取日历面试人员信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public List<CalendarRecruitDTO> GetCalendarRecruitList(SystemYearInput input)
        {
            return _hR_RecruitmentCandidatesBus.GetCalendarRecruitListAsnc(input);
        }
        /// <summary>
        /// 获取岗位信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public PostApplicant GetPostApplicant(IdInputDTO input)
        {
            var data = _hR_RecruitmentCandidatesBus.GetPostApplicant(input);
            return data;
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_RecruitmentCandidates data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_RecruitmentCandidatesBus.AddDataAsync(data);
            }
            else
            {
                await _hR_RecruitmentCandidatesBus.UpdateDataAsync(data);
            }
        }
        /// <summary>
        /// 重新安排面试
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task SaveDataRecruit(PlanInputDTO input)
        {
            if (input.Ids.Count > 0)
            {
                var op = GetOperator();
                await _hR_RecruitmentCandidatesBus.SaveDataRecruit(input, op);
            }
        }

        /// <summary>
        /// 修改面试状态
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task MianShi(MianShiInput data)
        {
            if (data == null)
            {
                throw new BusException("参数不能为空");
            }
            if (data.id.IsNullOrEmpty())
            {
                throw new BusException("参数错误");
            }
            HR_RecruitmentCandidates model = await _hR_RecruitmentCandidatesBus.GetTheDataAsync(data.id);
            if (model == null)
            {
                throw new BusException("未找到数据");
            }
            this.UpdateEntity(model);
            model.F_BusState = data.state;
            string userId = model.F_UserId.ToString();
            //保存不同意原因
            if (data.state == 2)
            {
                model.F_Cause = data.F_Cause;
                model.F_Label = EnumHelper.GetEnumDescription(RecruitType.TagType.招聘专员不同意);
                model.F_LabelOperationTime = DateTime.Now;
                //保存标签
                HR_ContentOperationLog hR_ContentOperation = new HR_ContentOperationLog()
                {
                    F_BusState = (int)ASKBusState.正常,
                    F_UserId = model.F_UserId,
                    F_RelationalID = model.F_Id,
                    F_RelationalType = (int)RecruitType.RelationalType.正常,
                    F_TagContent = EnumHelper.GetEnumDescription(RecruitType.TagType.招聘专员不同意),
                    F_TagType = (int)RecruitType.TagType.招聘专员不同意,
                    F_Evaluation = model.F_Cause
                };
                await _hR_ContentOperationLog.SaveDataAsync(new List<HR_ContentOperationLog> { hR_ContentOperation });
            }
            else
                model.F_Cause = ""; 
            //todo，以下代码无意义？ 20210317
            var entryModel = await _hR_Entry.GetTheDataAsync(userId);
            entryModel.F_FolderId = data.FileId;
            entryModel.F_Sources = data.F_Sources;
            entryModel.F_SourcesPersonnel = data.F_SourcesPersonnel;
            entryModel.F_Interview = data.F_Interview;
            _hR_Entry.UpdateData(entryModel);
            var op = GetOperator();
            await _hR_RecruitmentCandidatesBus.UpdateDataAsync(model, data.state, op);
        }
        /// <summary>
        /// 保存附件
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task SaveAttachment(MianShiInput data)
        {
            HR_RecruitmentCandidates model = await _hR_RecruitmentCandidatesBus.GetTheDataAsync(data.id);
            var entryModel = await _hR_Entry.GetTheDataAsync(model.F_UserId);
            entryModel.F_FolderId = data.FileId; 
            _hR_Entry.UpdateData(entryModel);
        }
        [HttpPost]
        public async Task DeleteData(List<string> ids, int type = 0)
        {
            await _hR_RecruitmentCandidatesBus.DeleteDataAsync(ids, type);
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_RecruitmentCandidatesBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<HR_RecruitmentCandidates>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _hR_RecruitmentCandidatesBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}