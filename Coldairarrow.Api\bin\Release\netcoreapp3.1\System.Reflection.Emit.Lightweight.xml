﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Reflection.Emit.Lightweight</name>
  </assembly>
  <members>
    <member name="T:System.Reflection.Emit.DynamicILInfo">
      <summary>Provides support for alternative ways to generate the Microsoft intermediate language (MSIL) and metadata for a dynamic method, including methods for creating tokens and for inserting the code, exception handling, and local variable signature blobs.</summary>
    </member>
    <member name="P:System.Reflection.Emit.DynamicILInfo.DynamicMethod">
      <summary>Gets the dynamic method whose body is generated by the current instance.</summary>
      <returns>A <see cref="T:System.Reflection.Emit.DynamicMethod" /> object representing the dynamic method for which the current <see cref="T:System.Reflection.Emit.DynamicILInfo" /> object is generating code.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicILInfo.GetTokenFor(System.Byte[])">
      <summary>Gets a token, valid in the scope of the current <see cref="T:System.Reflection.Emit.DynamicILInfo" />, representing the signature for the associated dynamic method.</summary>
      <param name="signature">An array that contains the signature.</param>
      <returns>A token that can be embedded in the metadata and the MSIL stream for the associated dynamic method.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicILInfo.GetTokenFor(System.Reflection.Emit.DynamicMethod)">
      <summary>Gets a token, valid in the scope of the current <see cref="T:System.Reflection.Emit.DynamicILInfo" />, representing a dynamic method to be called from the associated method.</summary>
      <param name="method">The dynamic method to call.</param>
      <returns>A token that can be embedded in the MSIL stream for the associated dynamic method, as the target of an MSIL instruction.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicILInfo.GetTokenFor(System.RuntimeFieldHandle)">
      <summary>Gets a token, valid in the scope of the current <see cref="T:System.Reflection.Emit.DynamicILInfo" />, representing a field to be accessed from the associated dynamic method.</summary>
      <param name="field">The field to be accessed.</param>
      <returns>A token that can be used as the operand of an MSIL instruction that accesses fields, in the scope of the current <see cref="T:System.Reflection.Emit.DynamicILInfo" /> object.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicILInfo.GetTokenFor(System.RuntimeFieldHandle,System.RuntimeTypeHandle)">
      <summary>Gets a token, valid in the scope of the current <see cref="T:System.Reflection.Emit.DynamicILInfo" />, representing a field to be accessed from the associated dynamic method; the field is on the specified generic type.</summary>
      <param name="field">The field to be accessed.</param>
      <param name="contextType">The generic type the field belongs to.</param>
      <returns>A token that can be used as the operand of an MSIL instruction that accesses fields in the scope of the current <see cref="T:System.Reflection.Emit.DynamicILInfo" /> object.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicILInfo.GetTokenFor(System.RuntimeMethodHandle)">
      <summary>Gets a token, valid in the scope of the current <see cref="T:System.Reflection.Emit.DynamicILInfo" />, representing a method to be accessed from the associated dynamic method.</summary>
      <param name="method">The method to be accessed.</param>
      <returns>A token that can be used as the operand of an MSIL instruction that accesses methods, such as <see cref="F:System.Reflection.Emit.OpCodes.Call" /> or <see cref="F:System.Reflection.Emit.OpCodes.Ldtoken" />, in the scope of the current <see cref="T:System.Reflection.Emit.DynamicILInfo" /> object.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicILInfo.GetTokenFor(System.RuntimeMethodHandle,System.RuntimeTypeHandle)">
      <summary>Gets a token, valid in the scope of the current <see cref="T:System.Reflection.Emit.DynamicILInfo" />, representing a method on a generic type.</summary>
      <param name="method">The method.</param>
      <param name="contextType">The generic type the method belongs to.</param>
      <returns>A token that can be used as the operand of an MSIL instruction that accesses methods, such as <see cref="F:System.Reflection.Emit.OpCodes.Call" /> or <see cref="F:System.Reflection.Emit.OpCodes.Ldtoken" />, in the scope of the current <see cref="T:System.Reflection.Emit.DynamicILInfo" /> object.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicILInfo.GetTokenFor(System.RuntimeTypeHandle)">
      <summary>Gets a token, valid in the scope of the current <see cref="T:System.Reflection.Emit.DynamicILInfo" />, representing a type to be used in the associated dynamic method.</summary>
      <param name="type">The type to be used.</param>
      <returns>A token that can be used as the operand of an MSIL instruction that requires a type, in the scope of the current <see cref="T:System.Reflection.Emit.DynamicILInfo" /> object.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicILInfo.GetTokenFor(System.String)">
      <summary>Gets a token, valid in the scope of the current <see cref="T:System.Reflection.Emit.DynamicILInfo" />, representing a string literal to be used in the associated dynamic method.</summary>
      <param name="literal">The string to be used.</param>
      <returns>A token that can be used as the operand of an MSIL instruction that requires a string, in the scope of the current <see cref="T:System.Reflection.Emit.DynamicILInfo" /> object.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicILInfo.SetCode(System.Byte*,System.Int32,System.Int32)">
      <summary>Sets the code body of the associated dynamic method.</summary>
      <param name="code">A pointer to a byte array containing the MSIL stream.</param>
      <param name="codeSize">The number of bytes in the MSIL stream.</param>
      <param name="maxStackSize">The maximum number of items on the operand stack when the method is executing.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="code" /> is <see langword="null" /> and <paramref name="codeSize" /> is greater than 0.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="codeSize" /> is less than 0.</exception>
    </member>
    <member name="M:System.Reflection.Emit.DynamicILInfo.SetCode(System.Byte[],System.Int32)">
      <summary>Sets the code body of the associated dynamic method.</summary>
      <param name="code">An array that contains the MSIL stream.</param>
      <param name="maxStackSize">The maximum number of items on the operand stack when the method is executing.</param>
    </member>
    <member name="M:System.Reflection.Emit.DynamicILInfo.SetExceptions(System.Byte*,System.Int32)">
      <summary>Sets the exception metadata for the associated dynamic method.</summary>
      <param name="exceptions">A pointer to a byte array containing the exception metadata.</param>
      <param name="exceptionsSize">The number of bytes of exception metadata.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exceptions" /> is <see langword="null" /> and <paramref name="exceptionSize" /> is greater than 0.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="exceptionSize" /> is less than 0.</exception>
    </member>
    <member name="M:System.Reflection.Emit.DynamicILInfo.SetExceptions(System.Byte[])">
      <summary>Sets the exception metadata for the associated dynamic method.</summary>
      <param name="exceptions">An array that contains the exception metadata.</param>
    </member>
    <member name="M:System.Reflection.Emit.DynamicILInfo.SetLocalSignature(System.Byte*,System.Int32)">
      <summary>Sets the local variable signature that describes the layout of local variables for the associated dynamic method.</summary>
      <param name="localSignature">An array that contains the layout of local variables for the associated <see cref="T:System.Reflection.Emit.DynamicMethod" />.</param>
      <param name="signatureSize">The number of bytes in the signature.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="localSignature" /> is <see langword="null" /> and <paramref name="signatureSize" /> is greater than 0.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureSize" /> is less than 0.</exception>
    </member>
    <member name="M:System.Reflection.Emit.DynamicILInfo.SetLocalSignature(System.Byte[])">
      <summary>Sets the local variable signature that describes the layout of local variables for the associated dynamic method.</summary>
      <param name="localSignature">An array that contains the layout of local variables for the associated <see cref="T:System.Reflection.Emit.DynamicMethod" />.</param>
    </member>
    <member name="T:System.Reflection.Emit.DynamicMethod">
      <summary>Defines and represents a dynamic method that can be compiled, executed, and discarded. Discarded methods are available for garbage collection.</summary>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.#ctor(System.String,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Type,System.Type[],System.Reflection.Module,System.Boolean)">
      <summary>Creates a dynamic method that is global to a module, specifying the method name, attributes, calling convention, return type, parameter types, module, and whether just-in-time (JIT) visibility checks should be skipped for types and members accessed by the Microsoft intermediate language (MSIL) of the dynamic method.</summary>
      <param name="name">The name of the dynamic method. This can be a zero-length string, but it cannot be <see langword="null" />.</param>
      <param name="attributes">A bitwise combination of <see cref="T:System.Reflection.MethodAttributes" /> values that specifies the attributes of the dynamic method. The only combination allowed is <see cref="F:System.Reflection.MethodAttributes.Public" /> and <see cref="F:System.Reflection.MethodAttributes.Static" />.</param>
      <param name="callingConvention">The calling convention for the dynamic method. Must be <see cref="F:System.Reflection.CallingConventions.Standard" />.</param>
      <param name="returnType">A <see cref="T:System.Type" /> object that specifies the return type of the dynamic method, or <see langword="null" /> if the method has no return type.</param>
      <param name="parameterTypes">An array of <see cref="T:System.Type" /> objects specifying the types of the parameters of the dynamic method, or <see langword="null" /> if the method has no parameters.</param>
      <param name="m">A <see cref="T:System.Reflection.Module" /> representing the module with which the dynamic method is to be logically associated.</param>
      <param name="skipVisibility">
        <see langword="true" /> to skip JIT visibility checks on types and members accessed by the MSIL of the dynamic method; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentException">An element of <paramref name="parameterTypes" /> is <see langword="null" /> or <see cref="T:System.Void" />.
-or-
<paramref name="m" /> is a module that provides anonymous hosting for dynamic methods.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.
-or-
<paramref name="m" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="attributes" /> is a combination of flags other than <see cref="F:System.Reflection.MethodAttributes.Public" /> and <see cref="F:System.Reflection.MethodAttributes.Static" />.
-or-
<paramref name="callingConvention" /> is not <see cref="F:System.Reflection.CallingConventions.Standard" />.
-or-
<paramref name="returnType" /> is a type for which <see cref="P:System.Type.IsByRef" /> returns <see langword="true" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.#ctor(System.String,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Type,System.Type[],System.Type,System.Boolean)">
      <summary>Creates a dynamic method, specifying the method name, attributes, calling convention, return type, parameter types, the type with which the dynamic method is logically associated, and whether just-in-time (JIT) visibility checks should be skipped for types and members accessed by the Microsoft intermediate language (MSIL) of the dynamic method.</summary>
      <param name="name">The name of the dynamic method. This can be a zero-length string, but it cannot be <see langword="null" />.</param>
      <param name="attributes">A bitwise combination of <see cref="T:System.Reflection.MethodAttributes" /> values that specifies the attributes of the dynamic method. The only combination allowed is <see cref="F:System.Reflection.MethodAttributes.Public" /> and <see cref="F:System.Reflection.MethodAttributes.Static" />.</param>
      <param name="callingConvention">The calling convention for the dynamic method. Must be <see cref="F:System.Reflection.CallingConventions.Standard" />.</param>
      <param name="returnType">A <see cref="T:System.Type" /> object that specifies the return type of the dynamic method, or <see langword="null" /> if the method has no return type.</param>
      <param name="parameterTypes">An array of <see cref="T:System.Type" /> objects specifying the types of the parameters of the dynamic method, or <see langword="null" /> if the method has no parameters.</param>
      <param name="owner">A <see cref="T:System.Type" /> with which the dynamic method is logically associated. The dynamic method has access to all members of the type.</param>
      <param name="skipVisibility">
        <see langword="true" /> to skip JIT visibility checks on types and members accessed by the MSIL of the dynamic method; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentException">An element of <paramref name="parameterTypes" /> is <see langword="null" /> or <see cref="T:System.Void" />.
-or-
<paramref name="owner" /> is an interface, an array, an open generic type, or a type parameter of a generic type or method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.
-or-
<paramref name="owner" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="attributes" /> is a combination of flags other than <see cref="F:System.Reflection.MethodAttributes.Public" /> and <see cref="F:System.Reflection.MethodAttributes.Static" />.
-or-
<paramref name="callingConvention" /> is not <see cref="F:System.Reflection.CallingConventions.Standard" />.
-or-
<paramref name="returnType" /> is a type for which <see cref="P:System.Type.IsByRef" /> returns <see langword="true" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.#ctor(System.String,System.Type,System.Type[])">
      <summary>Initializes an anonymously hosted dynamic method, specifying the method name, return type, and parameter types.</summary>
      <param name="name">The name of the dynamic method. This can be a zero-length string, but it cannot be <see langword="null" />.</param>
      <param name="returnType">A <see cref="T:System.Type" /> object that specifies the return type of the dynamic method, or <see langword="null" /> if the method has no return type.</param>
      <param name="parameterTypes">An array of <see cref="T:System.Type" /> objects specifying the types of the parameters of the dynamic method, or <see langword="null" /> if the method has no parameters.</param>
      <exception cref="T:System.ArgumentException">An element of <paramref name="parameterTypes" /> is <see langword="null" /> or <see cref="T:System.Void" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="returnType" /> is a type for which <see cref="P:System.Type.IsByRef" /> returns <see langword="true" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.#ctor(System.String,System.Type,System.Type[],System.Boolean)">
      <summary>Initializes an anonymously hosted dynamic method, specifying the method name, return type, parameter types, and whether just-in-time (JIT) visibility checks should be skipped for types and members accessed by the Microsoft intermediate language (MSIL) of the dynamic method.</summary>
      <param name="name">The name of the dynamic method. This can be a zero-length string, but it cannot be <see langword="null" />.</param>
      <param name="returnType">A <see cref="T:System.Type" /> object that specifies the return type of the dynamic method, or <see langword="null" /> if the method has no return type.</param>
      <param name="parameterTypes">An array of <see cref="T:System.Type" /> objects specifying the types of the parameters of the dynamic method, or <see langword="null" /> if the method has no parameters.</param>
      <param name="restrictedSkipVisibility">
        <see langword="true" /> to skip JIT visibility checks on types and members accessed by the MSIL of the dynamic method, with this restriction: the trust level of the assemblies that contain those types and members must be equal to or less than the trust level of the call stack that emits the dynamic method; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentException">An element of <paramref name="parameterTypes" /> is <see langword="null" /> or <see cref="T:System.Void" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="returnType" /> is a type for which <see cref="P:System.Type.IsByRef" /> returns <see langword="true" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.#ctor(System.String,System.Type,System.Type[],System.Reflection.Module)">
      <summary>Creates a dynamic method that is global to a module, specifying the method name, return type, parameter types, and module.</summary>
      <param name="name">The name of the dynamic method. This can be a zero-length string, but it cannot be <see langword="null" />.</param>
      <param name="returnType">A <see cref="T:System.Type" /> object that specifies the return type of the dynamic method, or <see langword="null" /> if the method has no return type.</param>
      <param name="parameterTypes">An array of <see cref="T:System.Type" /> objects specifying the types of the parameters of the dynamic method, or <see langword="null" /> if the method has no parameters.</param>
      <param name="m">A <see cref="T:System.Reflection.Module" /> representing the module with which the dynamic method is to be logically associated.</param>
      <exception cref="T:System.ArgumentException">An element of <paramref name="parameterTypes" /> is <see langword="null" /> or <see cref="T:System.Void" />.
-or-
<paramref name="m" /> is a module that provides anonymous hosting for dynamic methods.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.
-or-
<paramref name="m" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="returnType" /> is a type for which <see cref="P:System.Type.IsByRef" /> returns <see langword="true" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.#ctor(System.String,System.Type,System.Type[],System.Reflection.Module,System.Boolean)">
      <summary>Creates a dynamic method that is global to a module, specifying the method name, return type, parameter types, module, and whether just-in-time (JIT) visibility checks should be skipped for types and members accessed by the Microsoft intermediate language (MSIL) of the dynamic method.</summary>
      <param name="name">The name of the dynamic method. This can be a zero-length string, but it cannot be <see langword="null" />.</param>
      <param name="returnType">A <see cref="T:System.Type" /> object that specifies the return type of the dynamic method, or <see langword="null" /> if the method has no return type.</param>
      <param name="parameterTypes">An array of <see cref="T:System.Type" /> objects specifying the types of the parameters of the dynamic method, or <see langword="null" /> if the method has no parameters.</param>
      <param name="m">A <see cref="T:System.Reflection.Module" /> representing the module with which the dynamic method is to be logically associated.</param>
      <param name="skipVisibility">
        <see langword="true" /> to skip JIT visibility checks on types and members accessed by the MSIL of the dynamic method.</param>
      <exception cref="T:System.ArgumentException">An element of <paramref name="parameterTypes" /> is <see langword="null" /> or <see cref="T:System.Void" />.
-or-
<paramref name="m" /> is a module that provides anonymous hosting for dynamic methods.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.
-or-
<paramref name="m" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="returnType" /> is a type for which <see cref="P:System.Type.IsByRef" /> returns <see langword="true" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.#ctor(System.String,System.Type,System.Type[],System.Type)">
      <summary>Creates a dynamic method, specifying the method name, return type, parameter types, and the type with which the dynamic method is logically associated.</summary>
      <param name="name">The name of the dynamic method. This can be a zero-length string, but it cannot be <see langword="null" />.</param>
      <param name="returnType">A <see cref="T:System.Type" /> object that specifies the return type of the dynamic method, or <see langword="null" /> if the method has no return type.</param>
      <param name="parameterTypes">An array of <see cref="T:System.Type" /> objects specifying the types of the parameters of the dynamic method, or <see langword="null" /> if the method has no parameters.</param>
      <param name="owner">A <see cref="T:System.Type" /> with which the dynamic method is logically associated. The dynamic method has access to all members of the type.</param>
      <exception cref="T:System.ArgumentException">An element of <paramref name="parameterTypes" /> is <see langword="null" /> or <see cref="T:System.Void" />.
-or-
<paramref name="owner" /> is an interface, an array, an open generic type, or a type parameter of a generic type or method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.
-or-
<paramref name="owner" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="returnType" /> is <see langword="null" />, or is a type for which <see cref="P:System.Type.IsByRef" /> returns <see langword="true" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.#ctor(System.String,System.Type,System.Type[],System.Type,System.Boolean)">
      <summary>Creates a dynamic method, specifying the method name, return type, parameter types, the type with which the dynamic method is logically associated, and whether just-in-time (JIT) visibility checks should be skipped for types and members accessed by the Microsoft intermediate language (MSIL) of the dynamic method.</summary>
      <param name="name">The name of the dynamic method. This can be a zero-length string, but it cannot be <see langword="null" />.</param>
      <param name="returnType">A <see cref="T:System.Type" /> object that specifies the return type of the dynamic method, or <see langword="null" /> if the method has no return type.</param>
      <param name="parameterTypes">An array of <see cref="T:System.Type" /> objects specifying the types of the parameters of the dynamic method, or <see langword="null" /> if the method has no parameters.</param>
      <param name="owner">A <see cref="T:System.Type" /> with which the dynamic method is logically associated. The dynamic method has access to all members of the type.</param>
      <param name="skipVisibility">
        <see langword="true" /> to skip JIT visibility checks on types and members accessed by the MSIL of the dynamic method; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentException">An element of <paramref name="parameterTypes" /> is <see langword="null" /> or <see cref="T:System.Void" />.
-or-
<paramref name="owner" /> is an interface, an array, an open generic type, or a type parameter of a generic type or method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.
-or-
<paramref name="owner" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="returnType" /> is <see langword="null" />, or is a type for which <see cref="P:System.Type.IsByRef" /> returns <see langword="true" />.</exception>
    </member>
    <member name="P:System.Reflection.Emit.DynamicMethod.Attributes">
      <summary>Gets the attributes specified when the dynamic method was created.</summary>
      <returns>A bitwise combination of the <see cref="T:System.Reflection.MethodAttributes" /> values representing the attributes for the method.</returns>
    </member>
    <member name="P:System.Reflection.Emit.DynamicMethod.CallingConvention">
      <summary>Gets the calling convention specified when the dynamic method was created.</summary>
      <returns>One of the <see cref="T:System.Reflection.CallingConventions" /> values that indicates the calling convention of the method.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.CreateDelegate(System.Type)">
      <summary>Completes the dynamic method and creates a delegate that can be used to execute it.</summary>
      <param name="delegateType">A delegate type whose signature matches that of the dynamic method.</param>
      <returns>A delegate of the specified type, which can be used to execute the dynamic method.</returns>
      <exception cref="T:System.InvalidOperationException">The dynamic method has no method body.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="delegateType" /> has the wrong number of parameters or the wrong parameter types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.CreateDelegate(System.Type,System.Object)">
      <summary>Completes the dynamic method and creates a delegate that can be used to execute it, specifying the delegate type and an object the delegate is bound to.</summary>
      <param name="delegateType">A delegate type whose signature matches that of the dynamic method, minus the first parameter.</param>
      <param name="target">An object the delegate is bound to. Must be of the same type as the first parameter of the dynamic method.</param>
      <returns>A delegate of the specified type, which can be used to execute the dynamic method with the specified target object.</returns>
      <exception cref="T:System.InvalidOperationException">The dynamic method has no method body.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="target" /> is not the same type as the first parameter of the dynamic method, and is not assignable to that type.
-or-
<paramref name="delegateType" /> has the wrong number of parameters or the wrong parameter types.</exception>
    </member>
    <member name="P:System.Reflection.Emit.DynamicMethod.DeclaringType">
      <summary>Gets the type that declares the method, which is always <see langword="null" /> for dynamic methods.</summary>
      <returns>Always <see langword="null" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.DefineParameter(System.Int32,System.Reflection.ParameterAttributes,System.String)">
      <summary>Defines a parameter of the dynamic method.</summary>
      <param name="position">The position of the parameter in the parameter list. Parameters are indexed beginning with the number 1 for the first parameter.</param>
      <param name="attributes">A bitwise combination of <see cref="T:System.Reflection.ParameterAttributes" /> values that specifies the attributes of the parameter.</param>
      <param name="parameterName">The name of the parameter. The name can be a zero-length string.</param>
      <returns>Always returns <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The method has no parameters.
-or-
<paramref name="position" /> is less than 0.
-or-
<paramref name="position" /> is greater than the number of the method's parameters.</exception>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.GetBaseDefinition">
      <summary>Returns the base implementation for the method.</summary>
      <returns>The base implementation of the method.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.GetCustomAttributes(System.Boolean)">
      <summary>Returns all the custom attributes defined for the method.</summary>
      <param name="inherit">
        <see langword="true" /> to search the method's inheritance chain to find the custom attributes; <see langword="false" /> to check only the current method.</param>
      <returns>An array of objects representing all the custom attributes of the method.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.GetCustomAttributes(System.Type,System.Boolean)">
      <summary>Returns the custom attributes of the specified type that have been applied to the method.</summary>
      <param name="attributeType">A <see cref="T:System.Type" /> representing the type of custom attribute to return.</param>
      <param name="inherit">
        <see langword="true" /> to search the method's inheritance chain to find the custom attributes; <see langword="false" /> to check only the current method.</param>
      <returns>An array of objects representing the attributes of the method that are of type <paramref name="attributeType" /> or derive from type <paramref name="attributeType" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attributeType" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.GetDynamicILInfo">
      <summary>Returns a <see cref="T:System.Reflection.Emit.DynamicILInfo" /> object that can be used to generate a method body from metadata tokens, scopes, and Microsoft intermediate language (MSIL) streams.</summary>
      <returns>A <see cref="T:System.Reflection.Emit.DynamicILInfo" /> object that can be used to generate a method body from metadata tokens, scopes, and MSIL streams.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.GetILGenerator">
      <summary>Returns a Microsoft intermediate language (MSIL) generator for the method with a default MSIL stream size of 64 bytes.</summary>
      <returns>An <see cref="T:System.Reflection.Emit.ILGenerator" /> object for the method.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.GetILGenerator(System.Int32)">
      <summary>Returns a Microsoft intermediate language (MSIL) generator for the method with the specified MSIL stream size.</summary>
      <param name="streamSize">The size of the MSIL stream, in bytes.</param>
      <returns>An <see cref="T:System.Reflection.Emit.ILGenerator" /> object for the method, with the specified MSIL stream size.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.GetMethodImplementationFlags">
      <summary>Returns the implementation flags for the method.</summary>
      <returns>A bitwise combination of <see cref="T:System.Reflection.MethodImplAttributes" /> values representing the implementation flags for the method.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.GetParameters">
      <summary>Returns the parameters of the dynamic method.</summary>
      <returns>An array of <see cref="T:System.Reflection.ParameterInfo" /> objects that represent the parameters of the dynamic method.</returns>
    </member>
    <member name="P:System.Reflection.Emit.DynamicMethod.InitLocals">
      <summary>Gets or sets a value indicating whether the local variables in the method are zero-initialized.</summary>
      <returns>
        <see langword="true" /> if the local variables in the method are zero-initialized; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.Invoke(System.Object,System.Reflection.BindingFlags,System.Reflection.Binder,System.Object[],System.Globalization.CultureInfo)">
      <summary>Invokes the dynamic method using the specified parameters, under the constraints of the specified binder, with the specified culture information.</summary>
      <param name="obj">This parameter is ignored for dynamic methods, because they are static. Specify <see langword="null" />.</param>
      <param name="invokeAttr">A bitwise combination of <see cref="T:System.Reflection.BindingFlags" /> values.</param>
      <param name="binder">A <see cref="T:System.Reflection.Binder" /> object that enables the binding, coercion of argument types, invocation of members, and retrieval of <see cref="T:System.Reflection.MemberInfo" /> objects through reflection. If <paramref name="binder" /> is <see langword="null" />, the default binder is used. For more details, see <see cref="T:System.Reflection.Binder" />.</param>
      <param name="parameters">An argument list. This is an array of arguments with the same number, order, and type as the parameters of the method to be invoked. If there are no parameters this parameter should be <see langword="null" />.</param>
      <param name="culture">An instance of <see cref="T:System.Globalization.CultureInfo" /> used to govern the coercion of types. If this is <see langword="null" />, the <see cref="T:System.Globalization.CultureInfo" /> for the current thread is used. For example, this information is needed to correctly convert a <see cref="T:System.String" /> that represents 1000 to a <see cref="T:System.Double" /> value, because 1000 is represented differently by different cultures.</param>
      <returns>A <see cref="T:System.Object" /> containing the return value of the invoked method.</returns>
      <exception cref="T:System.NotSupportedException">The <see cref="F:System.Reflection.CallingConventions.VarArgs" /> calling convention is not supported.</exception>
      <exception cref="T:System.Reflection.TargetParameterCountException">The number of elements in <paramref name="parameters" /> does not match the number of parameters in the dynamic method.</exception>
      <exception cref="T:System.ArgumentException">The type of one or more elements of <paramref name="parameters" /> does not match the type of the corresponding parameter of the dynamic method.</exception>
      <exception cref="T:System.Reflection.TargetInvocationException">The dynamic method is associated with a module, is not anonymously hosted, and was constructed with <paramref name="skipVisibility" /> set to <see langword="false" />, but the dynamic method accesses members that are not <see langword="public" /> or <see langword="internal" /> (<see langword="Friend" /> in Visual Basic).
-or-
The dynamic method is anonymously hosted and was constructed with <paramref name="skipVisibility" /> set to <see langword="false" />, but it accesses members that are not <see langword="public" />.
-or-
The dynamic method contains unverifiable code. See the "Verification" section in Remarks for <see cref="T:System.Reflection.Emit.DynamicMethod" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.IsDefined(System.Type,System.Boolean)">
      <summary>Indicates whether the specified custom attribute type is defined.</summary>
      <param name="attributeType">A <see cref="T:System.Type" /> representing the type of custom attribute to search for.</param>
      <param name="inherit">
        <see langword="true" /> to search the method's inheritance chain to find the custom attributes; <see langword="false" /> to check only the current method.</param>
      <returns>
        <see langword="true" /> if the specified custom attribute type is defined; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.DynamicMethod.IsSecurityCritical">
      <summary>Gets a value that indicates whether the current dynamic method is security-critical or security-safe-critical, and therefore can perform critical operations.</summary>
      <returns>
        <see langword="true" /> if the current dynamic method is security-critical or security-safe-critical; <see langword="false" /> if it is transparent.</returns>
      <exception cref="T:System.InvalidOperationException">The dynamic method doesn't have a method body.</exception>
    </member>
    <member name="P:System.Reflection.Emit.DynamicMethod.IsSecuritySafeCritical">
      <summary>Gets a value that indicates whether the current dynamic method is security-safe-critical at the current trust level; that is, whether it can perform critical operations and can be accessed by transparent code.</summary>
      <returns>
        <see langword="true" /> if the dynamic method is security-safe-critical at the current trust level; <see langword="false" /> if it is security-critical or transparent.</returns>
      <exception cref="T:System.InvalidOperationException">The dynamic method doesn't have a method body.</exception>
    </member>
    <member name="P:System.Reflection.Emit.DynamicMethod.IsSecurityTransparent">
      <summary>Gets a value that indicates whether the current dynamic method is transparent at the current trust level, and therefore cannot perform critical operations.</summary>
      <returns>
        <see langword="true" /> if the dynamic method is security-transparent at the current trust level; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The dynamic method doesn't have a method body.</exception>
    </member>
    <member name="P:System.Reflection.Emit.DynamicMethod.MethodHandle">
      <summary>Not supported for dynamic methods.</summary>
      <returns>Not supported for dynamic methods.</returns>
      <exception cref="T:System.InvalidOperationException">Not allowed for dynamic methods.</exception>
    </member>
    <member name="P:System.Reflection.Emit.DynamicMethod.Module">
      <summary>Gets the module with which the dynamic method is logically associated.</summary>
      <returns>The <see cref="T:System.Reflection.Module" /> with which the current dynamic method is associated.</returns>
    </member>
    <member name="P:System.Reflection.Emit.DynamicMethod.Name">
      <summary>Gets the name of the dynamic method.</summary>
      <returns>The simple name of the method.</returns>
    </member>
    <member name="P:System.Reflection.Emit.DynamicMethod.ReflectedType">
      <summary>Gets the class that was used in reflection to obtain the method.</summary>
      <returns>Always <see langword="null" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.DynamicMethod.ReturnParameter">
      <summary>Gets the return parameter of the dynamic method.</summary>
      <returns>Always <see langword="null" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.DynamicMethod.ReturnType">
      <summary>Gets the type of return value for the dynamic method.</summary>
      <returns>A <see cref="T:System.Type" /> representing the type of the return value of the current method; <see cref="T:System.Void" /> if the method has no return type.</returns>
    </member>
    <member name="P:System.Reflection.Emit.DynamicMethod.ReturnTypeCustomAttributes">
      <summary>Gets the custom attributes of the return type for the dynamic method.</summary>
      <returns>An <see cref="T:System.Reflection.ICustomAttributeProvider" /> representing the custom attributes of the return type for the dynamic method.</returns>
    </member>
    <member name="M:System.Reflection.Emit.DynamicMethod.ToString">
      <summary>Returns the signature of the method, represented as a string.</summary>
      <returns>A string representing the method signature.</returns>
    </member>
  </members>
</doc>