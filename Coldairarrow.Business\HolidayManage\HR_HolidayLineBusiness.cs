﻿using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.IBusiness;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity;
using System;
using System.Linq.Expressions;

namespace Coldairarrow.Business.HolidayManage
{
    public class HR_HolidayLineBusiness : BaseBusiness<HR_HolidayLine>, IHR_HolidayLineBusiness, ITransientDependency
    {
        public List<string> rankList = new List<string>() { "1", "2", "3", "4", "5", "6", "7", "8", "9" };
        public HR_HolidayLineBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        /// <summary>
        /// 根据用户ID获取年假实际额度
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public string GetRemainingAmount(string userId, DateTime? time)
        {
            string remainingDays = "";
            var dataNow = DateTime.Now.Date;
            var hTime = dataNow;
            if (time.HasValue && time.Value.Date.Year < dataNow.Year)
            {
                hTime = time.Value;
            }
            var moldeLe = Db.GetIQueryable<HR_HolidayLine>().Where(x => x.F_UserId == userId && x.F_EffectTime <= hTime && x.F_EndTime >= hTime && x.F_HolidayTypes.Contains("年假")).ToList();
            if (moldeLe.Count() > 0)
            {
                remainingDays = (moldeLe.Sum(s => s.F_ActualAmount) - moldeLe.Sum(s => s.F_UsedLine)).Value.ToString("0.0");
            }

            return remainingDays;
        }
        public async Task<PageResult<HR_HolidayLineDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_HolidayLine, HR_FormalEmployees, HR_HolidayLineDTO>> select = (h, e) => new HR_HolidayLineDTO
            {
                EmpCode = e.EmployeesCode,
                EmpName = e.NameUser,
            };
            select = select.BuildExtendSelectExpre();
            var q = from h in this.Db.GetIQueryable<HR_HolidayLine>().AsExpandable()
                    join e in this.Db.GetIQueryable<HR_FormalEmployees>() on h.F_UserId equals e.F_Id into emp
                    from e in emp.DefaultIfEmpty()
                    select @select.Invoke(h, e);
            var where = LinqHelper.True<HR_HolidayLineDTO>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_HolidayLineDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_HolidayLine> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_HolidayLine data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_HolidayLine data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_HolidayLine, HR_FormalEmployees, HR_HolidayLineDTO>> select = (h, e) => new HR_HolidayLineDTO
            {
                EmpCode = e.EmployeesCode,
                EmpName = e.NameUser,
            };
            select = select.BuildExtendSelectExpre();
            var q = from h in this.Db.GetIQueryable<HR_HolidayLine>().AsExpandable()
                    join e in this.Db.GetIQueryable<HR_FormalEmployees>() on h.F_UserId equals e.F_Id into emp
                    from e in emp.DefaultIfEmpty()
                    select @select.Invoke(h, e);
            var where = LinqHelper.True<HR_HolidayLineDTO>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_HolidayLineDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return q.Where(where).ToDataTable();
        }

        /// <summary>
        /// 导入并保存数据
        /// </summary>
        /// <param name="physicPath">物理路径</param>
        /// <returns></returns>
        /// 

        public AjaxResult<DataTable> ImportSaveData(string physicPath, IOperator op, string year)
        {
            AjaxResult<DataTable> ajaxResult = new AjaxResult<DataTable>();

            DataTable dt = ExcelHelper.ExcelImport(physicPath);
            if (dt == null || dt.Rows.Count == 0)
            {
                ajaxResult.Success = false;
                ajaxResult.Msg = "上传数据错误或不能为空";
            }
            else
            {
                dt.Columns.Add("导入错误", typeof(string));
                bool isError = false;
                List<HR_HolidayLine> records = new List<HR_HolidayLine>();
                var userEntity = Db.GetIQueryable<HR_FormalEmployees>().ToList();
                var entity = Db.GetIQueryable<HR_HolidayLine>().Where(i => i.F_Year == year).ToList();
                List<HR_HolidayLine> hR_Holidays = new List<HR_HolidayLine>();
                foreach (DataRow dr in dt.Rows)
                {
                    HR_HolidayLine model = new HR_HolidayLine();
                    var emp = userEntity.FirstOrDefault(x => x.EmployeesCode == dr["员工编码"].ToString());
                    if (emp == null)
                    {
                        dr["导入错误"] += "员工不存在;";
                        isError = true;
                    }
                    else
                    {
                        model.F_BusState = (int)ASKBusState.正常;
                        model.F_UserId = emp.F_Id;
                    }
                    var holideEntity = entity.Where(i => i.F_UserId == emp.F_Id).ToList();
                    hR_Holidays.AddRange(holideEntity);
                    if (!isError)
                    {
                        model.F_Id = IdHelper.GetId();
                        model.F_CreateDate = DateTime.Now;
                        model.F_CreateUserId = op.UserId;
                        model.F_CreateUserName = op.RealName;
                        model.F_HolidayTypes = dr["假期类型"].ToString();
                        model.F_EffectTime = dr["开始/生效时间"].ToString().ToDateTime();
                        model.F_EndTime = dr["结束时间"].ToString().ToDateTime();
                        model.F_VacationUnit = dr["假期单位"].ToString();
                        model.F_StandardLine = Convert.ToDecimal(dr["标准额度"].ToString());
                        model.F_LncDecLine = Convert.ToDecimal(dr["增减额度"].ToString());
                        model.F_UsedLine = Convert.ToDecimal(dr["已用额度"].ToString());
                        model.F_ActualAmount = model.F_StandardLine + model.F_LncDecLine;
                        model.F_StandardLineLast = 0;
                        model.F_ActualAmountLast = 0;
                        model.F_Year = year;
                        records.Add(model);
                    }
                }
                if (isError)
                {
                    ajaxResult.Data = dt;
                    ajaxResult.Success = false;
                    ajaxResult.Msg = "导入失败";

                    return ajaxResult;
                }
                else
                {
                    Db.Delete(hR_Holidays);
                    Insert(records);
                    ajaxResult.Success = true;
                    ajaxResult.Msg = "导入成功";
                    ajaxResult.ErrorCode = 0;
                }
            }
            return ajaxResult;
        }

        /// <summary>
        /// 生成假期额度
        /// </summary>
        /// <returns></returns>
        public void GenerateLeaveAllowance(string userId, string year, IOperator op)
        {
            //userId = "27CE5688-D612-4AE7-97A1-6F0D45FCD7A5";
            //year = "2021";
            List<string> relStatus = new List<string>()
                {
                    "派驻",
                    "正式员工",
                    "试用员工",
                    "第三方用工",
                    "第三方员工",
                    "试用员工（延期转正）",
                    "正式员工（校招）",
                    "正式员工（销售）",
                    "顾问"
                };

            var model = Db.GetIQueryable<HR_FormalEmployees>().Where(i => relStatus.Contains(i.EmployRelStatus));
            var List = new List<HR_FormalEmployees>();
            if (!string.IsNullOrWhiteSpace(userId))
            {
                var entity = Db.GetIQueryable<HR_HolidayLine>().Where(i => i.F_Year == year && i.F_UserId == userId && i.F_HolidayTypes != "病假").ToList();
                if (entity.Count > 0)
                {
                    Db.Delete(entity);
                }
                List = model.Where(i => i.F_Id == userId).ToList();
            }
            else
            {
                var ids = Db.GetIQueryable<HR_HolidayLine>().Where(i => i.F_Year == year).Select(s => s.F_UserId).Distinct().ToList();
                List = model.Where(i => !ids.Contains(i.F_Id)).ToList();
                var ueser = List.Where(i => i.F_Id == "446BF456-2D52-41FF-A0FB-EBDAE880295E").FirstOrDefault();
            }
            var dateNow = Convert.ToDateTime(year + "-12-31");
            List<HR_HolidayLine> hR_Holidays = new List<HR_HolidayLine>();
            var legal = "法定年假";
            var welfare = "福利年假";
            var sickLeave = "病假";

            foreach (var item in List)
            {
                if (item.NameUser == "张波")
                {
                    Console.WriteLine("11");
                }
                if (!item.F_InTime.HasValue || !item.F_StartWorkTime.HasValue) { continue; }
                int ys = dateNow.Year - item.F_StartWorkTime.Value.Year;
                int ms = dateNow.Month - item.F_StartWorkTime.Value.Month;
                //span 开始工作至今有多少个月
                int span = ys * 12 + ms;
                int ysIn = dateNow.Year - item.F_InTime.Value.Year;
                int msIn = dateNow.Month - item.F_InTime.Value.Month;
                var workingYear = (int)Math.Floor(Convert.ToDecimal((ysIn * 12) + msIn) / 12);
                //if (span < 12)
                //{
                //    continue;
                //}
                decimal legalDay = 0;
                decimal welfareDay = 0;
                #region 初始化法定年假、福利年假、病假
                HR_HolidayLine LegalHoliday = new HR_HolidayLine()
                {
                    F_ActualAmount = 0,
                    F_BusState = 1,
                    F_CreateDate = DateTime.Now.Date,
                    F_CreateUserId = !op.UserId.IsNullOrEmpty() ? op.UserId : "Admin",
                    F_CreateUserName = !op.RealName.IsNullOrEmpty() ? op.RealName : "超级管理员",
                    F_EndTime = Convert.ToDateTime((Convert.ToInt32(year) + 1) + "-1-1").AddSeconds(-1),
                    F_HolidayTypes = legal,
                    F_Id = Guid.NewGuid().ToString(),
                    F_LncDecLine = 0,
                    F_StandardLine = 0,
                    F_VacationUnit = "天",
                    F_EffectTime = Convert.ToDateTime(year + "-1-1"),
                    F_UsedLine = 0,
                    F_UserId = item.F_Id,
                    F_Year = year,
                    F_StandardLineLast = 0,
                    F_ActualAmountLast = 0,
                };
                HR_HolidayLine WelfareHoliday = new HR_HolidayLine();
                if (!item.EmployRelStatus.Contains("第三方"))
                {
                    WelfareHoliday = new HR_HolidayLine()
                    {
                        F_ActualAmount = 5,
                        F_BusState = 1,
                        F_CreateDate = DateTime.Now.Date,
                        F_CreateUserId = !op.UserId.IsNullOrEmpty() ? op.UserId : "Admin",
                        F_CreateUserName = !op.RealName.IsNullOrEmpty() ? op.RealName : "超级管理员",
                        F_EndTime = Convert.ToDateTime((Convert.ToInt32(year) + 1) + "-1-1").AddSeconds(-1),
                        F_HolidayTypes = welfare,
                        F_Id = Guid.NewGuid().ToString(),
                        F_LncDecLine = 0,
                        F_StandardLine = 0,
                        F_VacationUnit = "天",
                        F_EffectTime = Convert.ToDateTime(year + "-1-1"),
                        F_UsedLine = 0,
                        F_UserId = item.F_Id,
                        F_Year = year,
                        F_StandardLineLast = 0,
                        F_ActualAmountLast = 0,
                    };
                }

                HR_HolidayLine sickLeaveHoliday = new HR_HolidayLine()
                {
                    F_ActualAmount = 6,
                    F_BusState = 1,
                    F_CreateDate = DateTime.Now.Date,
                    F_CreateUserId = !op.UserId.IsNullOrEmpty() ? op.UserId : "Admin",
                    F_CreateUserName = !op.RealName.IsNullOrEmpty() ? op.RealName : "超级管理员",
                    F_EndTime = Convert.ToDateTime((Convert.ToInt32(year) + 1) + "-1-1").AddSeconds(-1),
                    F_HolidayTypes = sickLeave,
                    F_Id = Guid.NewGuid().ToString(),
                    F_LncDecLine = 0,
                    F_StandardLine = 6,
                    F_VacationUnit = "天",
                    F_EffectTime = Convert.ToDateTime(year + "-1-1"),
                    F_UsedLine = 0,
                    F_UserId = item.F_Id,
                    F_Year = year,
                    F_StandardLineLast = 0,
                    F_ActualAmountLast = 0,
                };
                #endregion


                var Year = LessYear(span);
                if (dateNow.Year == item.F_InTime.Value.Year)//入职当年转正的员工
                {
                    if (span <= 12)//工作年限不满一年的员工
                    {
                        TimeSpan tss = dateNow - item.F_InTime.Value;
                        var days = tss.TotalDays;
                        welfareDay = LessYear((decimal)days, 10);
                        if (!item.EmployRelStatus.Contains("第三方"))
                        {
                            WelfareHoliday.F_ActualAmount = welfareDay;
                            WelfareHoliday.F_LncDecLine = welfareDay - 10;
                            WelfareHoliday.F_StandardLine = 10;
                            WelfareHoliday.F_UsedLine = 0;
                            WelfareHoliday.F_ActualAmountLast = 0;
                            WelfareHoliday.F_ActualAmountLast = 0;
                            WelfareHoliday.F_EffectTime = item.F_InTime.Value;
                            hR_Holidays.Add(WelfareHoliday);
                        }
                    }
                    else //工作年限一年以上的员工
                    {
                        var dataEnd = Convert.ToDateTime(year + "-12-31");
                        TimeSpan ts = dateNow - item.F_InTime.Value;
                        var day = ts.TotalDays;
                        legalDay = LessYear((decimal)day, Year);
                        LegalHoliday.F_ActualAmount = legalDay;
                        LegalHoliday.F_LncDecLine = legalDay - Year;
                        LegalHoliday.F_StandardLine = Year;
                        LegalHoliday.F_UsedLine = 0;
                        LegalHoliday.F_ActualAmountLast = 0;
                        LegalHoliday.F_ActualAmountLast = 0;
                        LegalHoliday.F_EffectTime = item.F_InTime.Value;
                        hR_Holidays.Add(LegalHoliday);
                        if (WelfareLegalYear(item.F_Rank) && !item.EmployRelStatus.Contains("第三方"))
                        {
                            if (item.F_Rank.Contains("5") || item.F_Rank.Contains("6") || item.F_Rank.Contains("7"))
                            {
                                welfareDay = LessYear((decimal)day, 5);
                                WelfareHoliday.F_ActualAmount = welfareDay;
                                WelfareHoliday.F_LncDecLine = welfareDay - 5;
                                WelfareHoliday.F_StandardLine = 5;
                            }
                            else
                            {
                                if (Year == 15)
                                {
                                    continue;
                                }
                                else
                                {
                                    welfareDay = LessYear((decimal)day, 5);
                                    WelfareHoliday.F_ActualAmount = welfareDay;
                                    WelfareHoliday.F_LncDecLine = welfareDay - 5;
                                    WelfareHoliday.F_StandardLine = 5;
                                }
                            }
                            WelfareHoliday.F_UsedLine = 0;
                            WelfareHoliday.F_ActualAmountLast = 0;
                            WelfareHoliday.F_ActualAmountLast = 0;
                            WelfareHoliday.F_EffectTime = item.F_InTime.Value;
                            hR_Holidays.Add(WelfareHoliday);
                        }
                    }
                }
                else
                {
                    var wokeY = 0;
                    LegalHoliday.F_ActualAmount = Year;
                    LegalHoliday.F_LncDecLine = 0;
                    LegalHoliday.F_StandardLine = Year;
                    LegalHoliday.F_UsedLine = 0;
                    LegalHoliday.F_ActualAmountLast = 0;
                    LegalHoliday.F_ActualAmountLast = 0;
                    hR_Holidays.Add(LegalHoliday);
                    if (WelfareLegalYear(item.F_Rank))
                    {
                        var enYear = span / 12;
                        if (enYear <= 10)
                        {
                            if (item.F_Rank.Contains("5") || item.F_Rank.Contains("6"))
                            {
                                WelfareHoliday.F_ActualAmount = 5 + workingYear;
                                WelfareHoliday.F_LncDecLine = 0;
                                WelfareHoliday.F_StandardLine = 5 + workingYear;
                            }
                            else
                            {
                                wokeY = 5 + workingYear;
                                if (wokeY > 10)
                                {
                                    wokeY = 10;
                                }
                                WelfareHoliday.F_ActualAmount = wokeY;
                                WelfareHoliday.F_LncDecLine = 0;
                                WelfareHoliday.F_StandardLine = wokeY;
                            }
                        }
                        else if (10 < enYear && enYear < 20)
                        {
                            if (item.F_Rank.Contains("5") || item.F_Rank.Contains("6"))
                            {
                                wokeY = 5 + workingYear;
                                if (wokeY > 10)
                                {
                                    wokeY = 10;
                                }
                                WelfareHoliday.F_ActualAmount = wokeY;
                                WelfareHoliday.F_LncDecLine = 0;
                                WelfareHoliday.F_StandardLine = wokeY;
                            }
                            else
                            {
                                WelfareHoliday.F_ActualAmount = 5;
                                WelfareHoliday.F_LncDecLine = 0;
                                WelfareHoliday.F_StandardLine = 5;
                            }
                        }
                        else
                        {
                            if (item.F_Rank.Contains("5") || item.F_Rank.Contains("6"))
                            {
                                WelfareHoliday.F_ActualAmount = 5;
                                WelfareHoliday.F_LncDecLine = 0;
                                WelfareHoliday.F_StandardLine = 5;
                            }
                            else
                            {
                                hR_Holidays.Add(sickLeaveHoliday);
                                continue;
                            }
                        }

                        if (item.F_Rank.Contains("5") || item.F_Rank.Contains("6"))
                        {
                            if (WelfareHoliday.F_ActualAmount + LegalHoliday.F_ActualAmount > 20)
                            {
                                WelfareHoliday.F_ActualAmount = 15 - LegalHoliday.F_ActualAmount;
                                WelfareHoliday.F_StandardLine = 15 - LegalHoliday.F_StandardLine;
                            }

                        }
                        else
                        {
                            if (WelfareHoliday.F_ActualAmount + LegalHoliday.F_ActualAmount > 15)
                            {
                                WelfareHoliday.F_ActualAmount = 15 - LegalHoliday.F_ActualAmount;
                                WelfareHoliday.F_StandardLine = 15 - LegalHoliday.F_StandardLine;
                            }
                        }
                        LegalHoliday.F_UsedLine = 0;
                        LegalHoliday.F_ActualAmountLast = 0;
                        LegalHoliday.F_ActualAmountLast = 0;
                        if (!item.EmployRelStatus.Contains("第三方"))
                        {
                            hR_Holidays.Add(WelfareHoliday);
                        }
                    }
                    hR_Holidays.Add(sickLeaveHoliday);
                }
            }
            if (hR_Holidays.Count() > 0)
            {
                hR_Holidays.ForEach(i =>
                {
                    i.F_CreateUserId = !op.UserId.IsNullOrEmpty() ? op.UserId : "Admin";
                    i.F_CreateUserName = !op.RealName.IsNullOrEmpty() ? op.RealName : "超级管理员";
                });
            }
            Db.BulkInsert(hR_Holidays);
            Db.SaveChanges();
        }
        /// <summary>
        /// 生成病假
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="year"></param>
        /// <param name="op"></param>
        public void GenerateLeaveSickLeave(string userId, string year, IOperator op)
        {
            var sickLeave = "病假";
            var List = new List<HR_FormalEmployees>();
            List<string> relStatus = new List<string>()
                {
                    "派驻",
                    "正式员工",
                    "试用员工",
                    "第三方用工",
                    "第三方员工",
                    "试用员工（延期转正）",
                    "正式员工（校招）",
                    "正式员工（销售）",
                    "顾问"
                };

            //var model = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(i => relStatus.Contains(i.EmployRelStatus)&& i.F_Id==userId);
            //if (model ==null)
            //{
            //    throw new BusException("用户不存在");
            //}
            var entity = Db.GetIQueryable<HR_HolidayLine>().Where(i => i.F_Year == year && i.F_UserId == userId).ToList();
            if (entity.Count > 0)
            {
                Db.Delete(entity);
            }
            HR_HolidayLine sickLeaveHoliday = new HR_HolidayLine()
            {
                F_ActualAmount = 6,
                F_BusState = 1,
                F_CreateDate = DateTime.Now.Date,
                F_CreateUserId = !op.UserId.IsNullOrEmpty() ? op.UserId : "Admin",
                F_CreateUserName = !op.RealName.IsNullOrEmpty() ? op.RealName : "超级管理员",
                F_EndTime = Convert.ToDateTime((Convert.ToInt32(year) + 1) + "-1-1").AddSeconds(-1),
                F_HolidayTypes = sickLeave,
                F_Id = Guid.NewGuid().ToString(),
                F_LncDecLine = 0,
                F_StandardLine = 6,
                F_VacationUnit = "天",
                F_EffectTime = DateTime.Now.Date,
                F_UsedLine = 0,
                F_UserId = userId,
                F_Year = year,
                F_StandardLineLast = 0,
                F_ActualAmountLast = 0,
            };
            Db.Insert(sickLeaveHoliday);
            Db.SaveChanges();
        }

        /// <summary>
        /// 获取法定假日（通过参加工作时间）
        /// </summary>
        /// <param name="month"></param>
        /// <returns></returns>
        public int LessYear(int month)
        {
            // var yearNum = (int)Math.Ceiling(Convert.ToDecimal(month) / 12);
            var yearNum = (int)(Convert.ToDecimal(month) / 12);
            if (0 < yearNum && yearNum < 10)
            {
                return 5;
            }
            else if (10 <= yearNum && yearNum < 20)
            {
                return 10;
            }
            else
            {
                return 15;
            }

        }
        /// <summary>
        /// 法定年假获取
        /// </summary>
        /// <param name="day"></param>
        /// <param name="holidayNum"></param>
        /// <returns></returns>
        public decimal LessYear(decimal day, int holidayNum)
        {
            var numberDays = (decimal)holidayNum / 365;
            var conut = numberDays * day;
            var numCon = (conut * 100) % 100;
            decimal reNum = 0;
            if (numCon > 0 && numCon <= 50)
            {
                reNum = (decimal)(0.5);
            }
            if (numCon > 50 && numCon < 100)
            {
                reNum = 1;
            }
            conut = (int)conut + reNum;
            return conut;
        }
        /// <summary>
        /// 福利和年假
        /// </summary>
        /// <returns></returns>
        public decimal LegalLessYear(decimal day, int holidayNum)
        {
            var decNum = (day / 365) * holidayNum;
            var num = (int)decNum;
            decimal leDay = 0;
            if ((decNum * 10) % 10 <= 5)
            {
                leDay = num + Convert.ToDecimal(0.5);
            }
            else if ((decNum * 10) % 10 <= 5)
            {
                leDay = num + 1;
            }
            return leDay;
        }
        /// <summary>
        /// 是否有福利年假
        /// </summary>
        /// <param name="months"></param>
        /// <param name="legYear"></param>
        /// <param name="rank"></param>
        /// <returns></returns>
        public bool WelfareLegalYear(string rank)
        {
            if (!string.IsNullOrWhiteSpace(rank) && rankList.Count(i => rank.Contains(i)) > 0)//福利年假开始计算
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 法定年假
        /// </summary>
        /// <param name="months"></param>
        /// <param name="year"></param>
        /// <returns></returns>
        public decimal LegalYear(int months, int year)
        {
            //15天年假 
            decimal day = months / 12;
            if (months % 12 <= 6)
            {
                day += Convert.ToDecimal(0.5);
            }
            else
            {
                day += 1;
            }
            if (day >= year)
            {
                day = year;
            }
            return day;
        }
        /// <summary>
        /// 福利年假
        /// </summary>
        /// <param name="months"></param>
        /// <param name="legYear"></param>
        /// <param name="rank"></param>
        /// <returns></returns>
        public decimal WelfareYear(int months, decimal legYear, string rank)
        {
            decimal day = 0;
            day = months / 12;
            if (months % 12 <= 6)
            {
                day += Convert.ToDecimal(0.5);
            }
            else
            {
                day += 1;
            }


            if (rank.Contains("5") || rank.Contains("6"))
            {

                if (day + legYear >= 20)
                {
                    day = 20 - legYear;
                }
            }
            else if (rank.Contains("1") || rank.Contains("2") || rank.Contains("3") || rank.Contains("4"))
            {

                if (day + legYear >= 15)
                {
                    day = 15 - legYear;
                }
            }
            else
            {
                day = 0;
            }
            //if (rankList.Count(i => i == rank) > 0)//福利年假开始计算
            //{
            //    day = months / 12;
            //    if (months % 12 <= 6)
            //    {
            //        day += Convert.ToDecimal(0.5);
            //    }
            //    else
            //    {
            //        day += 1;
            //    }

            //    if (Convert.ToInt32(rank) <= 4)//只有15假
            //    {
            //        if (day + legYear >= 15)
            //        {
            //            day = 15 - legYear;
            //        }

            //    }
            //    if (Convert.ToInt32(rank) >= 5)//只有20假
            //    {
            //        if (day + legYear >= 20)
            //        {
            //            day = 20 - legYear;
            //        }
            //    }
            //}
            return day;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}