﻿using Coldairarrow.Business.PunchCard_Manage;
using Coldairarrow.Entity.PunchCard_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Business;
using Coldairarrow.Entity.Base_Manage;
using EFCore.Sharding;

namespace Coldairarrow.Api.Controllers.PunchCard_Manage
{
    [Route("/PunchCard_Manage/[controller]/[action]")]
    [NoCheckJWT]
    public class PunchCard_InfoController : BaseApiController
    {
        #region DI

        public PunchCard_InfoController(IPunchCard_InfoBusiness punchCard_InfoBus, IBase_AppSecretBusiness appSecretBus,
            IHR_FormalEmployeesBusiness hR_FormalEmployeesBus, IPunchCard_Flow_ListBusiness punchCard_Flow_ListBus,
            IPunchCard_Flow_InfoBusiness punchCard_Flow_InfoBus, IBase_UserLogBusiness base_LogBusiness)
        {
            _punchCard_InfoBus = punchCard_InfoBus;
            _appSecretBus = appSecretBus;
            _hR_FormalEmployeesBus = hR_FormalEmployeesBus;
            _punchCard_Flow_ListBus = punchCard_Flow_ListBus;
            _punchCard_Flow_InfoBus = punchCard_Flow_InfoBus;
            _base_LogBusiness = base_LogBusiness;
        }
        IHR_FormalEmployeesBusiness _hR_FormalEmployeesBus { get; }
        IPunchCard_InfoBusiness _punchCard_InfoBus { get; }
        IBase_AppSecretBusiness _appSecretBus { get; }
        IPunchCard_Flow_ListBusiness _punchCard_Flow_ListBus { get; }
        IBase_UserLogBusiness _base_LogBusiness { get; }
        IPunchCard_Flow_InfoBusiness _punchCard_Flow_InfoBus { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<PunchCard_InfoDTO>> GetDataList(PageInput<PunchCardModel> input)
        {
            return await _punchCard_InfoBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<PunchCard_Info> GetTheData(IdInputDTO input)
        {
            return await _punchCard_InfoBus.GetTheDataAsync(input.id);
        }

        [HttpPost]
        public async Task<List<string>> GetBssId()
        {
            var secret = await _appSecretBus.GetAppSecretAsync("PunchCardBssId");
            return !string.IsNullOrWhiteSpace(secret) ? secret.Split(';').Where(i => !i.IsNullOrEmpty()).Select(i => i.Trim()).ToList() : new List<string>();
        }

        /// <summary>
        /// 获取报表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PageResult<PunchCard_ReportDto>> GetPuchCardReport(PageInput<PunchCardModel> input)
        {
            return await _punchCard_InfoBus.GetPuchCardReport(input);
        }
        #endregion

        #region 提交

        [HttpPost]
        [Transactional]
        public async Task SaveData(PunchCard_Info data)
        {
            if (data.F_Id.IsNullOrEmpty() && !data.F_UserId.IsNullOrEmpty())
            {
                data.F_Time = DateTime.Now;
                data.F_TimeShort = DateTime.Now.ToString("HH:mm:ss");
                InitEntity(data);
                var userName = _hR_FormalEmployeesBus.GetTheDataAsync(data.F_UserId)?.Result?.NameUser;
                //判断是否打卡完成
                var punchCard_Infos = await _punchCard_InfoBus.GetToDayIsComDAsync(data.F_UserId);
                if (punchCard_Infos.Count >= 0 && punchCard_Infos.FirstOrDefault(x => x.F_Type == data.F_Type) != null)
                {
                    throw new Exception(EnumHelper.GetEnumDescription(data.F_Type) + "已打过");
                }
                else
                {
                    //data.F_Title = userName + data.F_Title;
                    //如果不等于正常或迟到15分钟后,则发起相关流程
                    if (data.F_TimeType != TimeType.正常)
                    {
                        //迟到早退
                        if ((data.F_TimeType == TimeType.迟到 && data.F_Time >= new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 9, 15, 0))
                            || (data.F_TimeType == TimeType.早退 && data.F_Time < new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 18, 0, 0))
                            || data.F_TimeType == TimeType.外勤)
                        {
                            data.F_WFState = WfType.审核中;
                            //创建流程
                            //获取审批人
                            var flowUsers = await _hR_FormalEmployeesBus.GetPunchCarFlowUsers();
                            if (flowUsers.Count() > 0)
                            {
                                var @operator = GetOperator();
                                PunchCard_Flow_List punchCard_Flow_List = new PunchCard_Flow_List()
                                {
                                    F_WFState = WfType.审核中,
                                    F_StartTime = DateTime.Now,
                                    F_Title = data.F_Title,
                                    F_Id = GuidHelper.GenerateKey(),
                                    F_CreateDate = DateTime.Now,
                                    F_CreateUserId = data.F_UserId,
                                    F_UserId = data.F_UserId,
                                    F_CreateUserName = @operator?.RealName,
                                };
                                await _punchCard_Flow_ListBus.AddDataAsync(punchCard_Flow_List);
                                data.F_WFId = punchCard_Flow_List.F_Id;
                                List<PunchCard_Flow_Info> punchCard_Flow_Infos = new List<PunchCard_Flow_Info>();
                                foreach (var uitem in flowUsers)
                                {
                                    PunchCard_Flow_Info punchCard_Flow_Info = new PunchCard_Flow_Info()
                                    {
                                        F_WFState = WfType.审核中,
                                        F_WFId = punchCard_Flow_List.F_Id,
                                        F_initiator = data.F_UserId,
                                        F_approver = uitem.F_Id,
                                        F_StartTime = DateTime.Now,
                                        F_Title = data.F_Title,
                                        F_Time = data.F_Time,
                                        F_TimeShort = data.F_TimeShort,
                                        F_Type = data.F_Type,
                                        F_TimeType = data.F_TimeType,
                                        F_Id = GuidHelper.GenerateKey(),
                                        F_CreateDate = DateTime.Now,
                                        F_CreateUserId = data.F_UserId,
                                        F_CreateUserName = @operator?.RealName,
                                        F_FlowType = (FlowType)uitem.IsPunchCardType
                                    };
                                    punchCard_Flow_Infos.Add(punchCard_Flow_Info);
                                }
                                await _punchCard_Flow_InfoBus.AddDataAsync(punchCard_Flow_Infos);
                            }
                        }
                    }
                    else
                    {
                        //判断时间是否是9点到9点15
                        if (data.F_Type == CardType.上班打卡 && (data.F_Time >= new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 9, 1, 0) && data.F_Time <= new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 9, 15, 0)))
                        {
                            data.F_Title = "提交迟到打卡";
                            data.F_TimeType = TimeType.迟到;
                        }
                    }
                    await _punchCard_InfoBus.AddDataAsync(data);
                }
                var op = this.GetOperator();
                var title = data.F_Title + (data.F_TimeType != TimeType.正常 ? "(" + EnumHelper.GetEnumDescription(data.F_Type) + ")" : "");
                var log = new Base_UserLog
                {
                    Id = IdHelper.GetId(),
                    CreateTime = DateTime.Now,
                    CreatorId = data.F_UserId,
                    CreatorRealName = userName,
                    LogContent = $"{userName}{title}",
                    LogType = UserLogType.打卡小程序.ToString(),
                    JsonContent = "",
                };
                op.WriteUserLog(log);
            }
            else
            {
                await _punchCard_InfoBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _punchCard_InfoBus.DeleteDataAsync(ids);
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _punchCard_InfoBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }

        //[HttpPost]
        //public FileContentResult ExcelDownload(PageInput<PunchCardModel> input)
        //{
        //    try
        //    {
        //        var t = _punchCard_InfoBus.ExcelDownload(input);
        //        var file = File(t, "application/x-zip-compressed", "cccc.xls");
        //        return file;
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<PunchCard_Info>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _punchCard_InfoBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}