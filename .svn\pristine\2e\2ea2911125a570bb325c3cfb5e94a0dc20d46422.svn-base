﻿using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.HR_EmployeeInfoManage
{
    public interface IHR_SocialWorkExpBusiness
    {
        Task<PageResult<HR_SocialWorkExp>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<HR_SocialWorkExp> GetTheDataAsync(string id);
        Task AddDataAsync(HR_SocialWorkExp data);
        int AddData(HR_SocialWorkExp data);
        Task UpdateDataAsync(HR_SocialWorkExp data);
        int UpdateData(HR_SocialWorkExp data);
        int UpdateListData(List<HR_SocialWorkExp> data);
        Task DeleteDataAsync(List<string> ids);
        int DeleteData(List<string> ids);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
    }
    public class HR_SocialWorkExpDTO : HR_SocialWorkExp
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public string F_StartTimeText { get => F_StartTime?.ToString("yyyy-MM-dd"); }
        /// <summary>
        /// 开始时间
        /// </summary>
        public string F_EndTimeText { get => F_EndTime?.ToString("yyyy-MM-dd"); }
    }
}