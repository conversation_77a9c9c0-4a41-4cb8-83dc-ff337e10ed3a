<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Localization.Routing</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Localization.Routing.RouteDataRequestCultureProvider">
            <summary>
            Determines the culture information for a request via values in the route data.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Localization.Routing.RouteDataRequestCultureProvider.RouteDataStringKey">
            <summary>
            The key that contains the culture name.
            Defaults to "culture".
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Localization.Routing.RouteDataRequestCultureProvider.UIRouteDataStringKey">
            <summary>
            The key that contains the UI culture name. If not specified or no value is found,
            <see cref="P:Microsoft.AspNetCore.Localization.Routing.RouteDataRequestCultureProvider.RouteDataStringKey"/> will be used.
            Defaults to "ui-culture".
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Localization.Routing.RouteDataRequestCultureProvider.DetermineProviderCultureResult(Microsoft.AspNetCore.Http.HttpContext)">
            <inheritdoc />
        </member>
    </members>
</doc>
