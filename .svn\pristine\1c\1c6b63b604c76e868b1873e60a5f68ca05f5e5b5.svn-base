﻿using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Base_Manage
{
    [Route("/Base_Manage/[controller]/[action]")]
    public class Base_CompanyController : BaseApiController
    {
        #region DI

        public Base_CompanyController(IBase_CompanyBusiness base_CompanyBus)
        {
            _base_CompanyBus = base_CompanyBus;
        }

        IBase_CompanyBusiness _base_CompanyBus { get; }

        #endregion

        #region 获取
        [HttpPost]
        public async Task<List<Base_CompanyTreeDTO>> GetTreeDataList(CompanyTreeInputDTO input)
        {
            return await _base_CompanyBus.GetTreeDataListAsync(input);
        }
        [HttpPost]
        public async Task<PageResult<Base_Company>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _base_CompanyBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Base_Company> GetTheData(IdInputDTO input)
        {
            return await _base_CompanyBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Base_Company data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _base_CompanyBus.AddDataAsync(data);
            }
            else
            {
                await _base_CompanyBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _base_CompanyBus.DeleteDataAsync(ids);
        }

        #endregion
    }
}