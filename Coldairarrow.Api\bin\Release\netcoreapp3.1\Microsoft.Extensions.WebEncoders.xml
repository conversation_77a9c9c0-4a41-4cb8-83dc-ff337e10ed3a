<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.WebEncoders</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.DependencyInjection.EncoderServiceCollectionExtensions">
            <summary>
            Extension methods for setting up web encoding services in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.EncoderServiceCollectionExtensions.AddWebEncoders(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds <see cref="T:System.Text.Encodings.Web.HtmlEncoder"/>, <see cref="T:System.Text.Encodings.Web.JavaScriptEncoder"/> and <see cref="T:System.Text.Encodings.Web.UrlEncoder"/>
            to the specified <paramref name="services" />.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.EncoderServiceCollectionExtensions.AddWebEncoders(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Microsoft.Extensions.WebEncoders.WebEncoderOptions})">
            <summary>
            Adds <see cref="T:System.Text.Encodings.Web.HtmlEncoder"/>, <see cref="T:System.Text.Encodings.Web.JavaScriptEncoder"/> and <see cref="T:System.Text.Encodings.Web.UrlEncoder"/>
            to the specified <paramref name="services" />.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="setupAction">An <see cref="T:System.Action`1"/> to configure the provided <see cref="T:Microsoft.Extensions.WebEncoders.WebEncoderOptions"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="T:Microsoft.Extensions.WebEncoders.Testing.HtmlTestEncoder">
            <summary>
            Encoder used for unit testing.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.WebEncoders.Testing.JavaScriptTestEncoder">
            <summary>
            Encoder used for unit testing.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.WebEncoders.Testing.UrlTestEncoder">
            <summary>
            Encoder used for unit testing.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.WebEncoders.WebEncoderOptions">
            <summary>
            Specifies options common to all three encoders (HtmlEncode, JavaScriptEncode, UrlEncode).
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.WebEncoders.WebEncoderOptions.TextEncoderSettings">
            <summary>
            Specifies which code points are allowed to be represented unescaped by the encoders.
            </summary>
            <remarks>
            If this property is null, then the encoders will use their default allow lists.
            </remarks>
        </member>
    </members>
</doc>
