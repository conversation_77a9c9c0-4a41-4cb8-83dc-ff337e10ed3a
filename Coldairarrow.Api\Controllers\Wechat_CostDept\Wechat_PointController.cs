﻿using Coldairarrow.Business.Wechat_CostDept;
using Coldairarrow.Entity.Wechat_CostDept;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Wechat_CostDept
{
    [Route("/Wechat_CostDept/[controller]/[action]")]
    public class Wechat_PointController : BaseApiController
    {
        #region DI

        public Wechat_PointController(IWechat_PointBusiness wechat_PointBus)
        {
            _wechat_PointBus = wechat_PointBus;
        }

        IWechat_PointBusiness _wechat_PointBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Wechat_Point>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _wechat_PointBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Wechat_Point> GetTheData(IdInputDTO input)
        {
            return await _wechat_PointBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Wechat_Point data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);
                //设置type和顺序的默认值
                if (data.W_Type.IsNullOrEmpty())
                {
                    data.W_Type = 1;
                }
                if (data.W_Sort.IsNullOrEmpty())
                {
                    data.W_Sort = 1;
                }
                await _wechat_PointBus.AddDataAsync(data);
            }
            else
            {
                await _wechat_PointBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _wechat_PointBus.DeleteDataAsync(ids);
        }

        #endregion

        #region 二次开发
        //直接获取全部的数据
        [HttpPost]
        public async Task<List<Wechat_Point>> GetAllDataList()
        {
            return await _wechat_PointBus.GetAllDataList();
        }
        //根据type查询大类信息
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetListByType(int type)
        {
            try
            {
                var list = _wechat_PointBus.GetListByType(type);
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        #endregion
    }
}