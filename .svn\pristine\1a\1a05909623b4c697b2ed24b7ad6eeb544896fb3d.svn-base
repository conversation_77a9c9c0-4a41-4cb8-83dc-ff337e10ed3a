﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Coldairarrow.Entity.Wechat_CostDept
{
    /// <summary>
    /// 小程序用户表
    /// </summary>
    [Table("Wechat_User")]
    public class Wechat_User
    {

        /// <summary>
        /// 微信id
        /// </summary>
        [Key, Column(Order = 1)]
        public String W_OpenId { get; set; }

        /// <summary>
        /// 主键
        /// </summary>
        public String Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public String CreatorId { get; set; }

        /// <summary>
        /// 用户类型
        /// </summary>
        public Int32? W_UserType { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public String W_Name { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        public String W_Phone { get; set; }

        /// <summary>
        /// 真实姓名
        /// </summary>
        public String W_Truename { get; set; }

        /// <summary>
        /// 公司名
        /// </summary>
        public String W_Company { get; set; }

        /// <summary>
        /// 合同ID
        /// </summary>
        public String W_CompanyId { get; set; }

        /// <summary>
        /// 合同名称
        /// </summary>
        public String W_CompanyName { get; set; }

        /// <summary>
        /// 合同编号
        /// </summary>
        public String W_ContractNum { get; set; }

        /// <summary>
        /// 性别，0男，1女
        /// </summary>
        public Int32? U_Gender { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
        public String U_City { get; set; }

        /// <summary>
        /// 省份
        /// </summary>
        public String U_Province { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        public String U_Country { get; set; }

        /// <summary>
        /// 头像
        /// </summary>
        public String U_Icon { get; set; }

        /// <summary>
        /// 所属项目
        /// </summary>
        public Int32? W_Place { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public String W_Description { get; set; }

        /// <summary>
        /// 岗位
        /// </summary>
        public String W_Post { get; set; }

    }
}