<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Identity.Core</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Identity.AuthenticatorTokenProvider`1">
            <summary>
            Used for authenticator code verification.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.AuthenticatorTokenProvider`1.CanGenerateTwoFactorTokenAsync(Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Checks if a two factor authentication token can be generated for the specified <paramref name="user"/>.
            </summary>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> to retrieve the <paramref name="user"/> from.</param>
            <param name="user">The <typeparamref name="TUser"/> to check for the possibility of generating a two factor authentication token.</param>
            <returns>True if the user has an authenticator key set, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.AuthenticatorTokenProvider`1.GenerateAsync(System.String,Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Returns an empty string since no authenticator codes are sent.
            </summary>
            <param name="purpose">Ignored.</param>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> to retrieve the <paramref name="user"/> from.</param>
            <param name="user">The <typeparamref name="TUser"/>.</param>
            <returns>string.Empty.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.AuthenticatorTokenProvider`1.ValidateAsync(System.String,System.String,Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            
            </summary>
            <param name="purpose"></param>
            <param name="token"></param>
            <param name="manager"></param>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.ClaimsIdentityOptions">
            <summary>
            Options used to configure the claim types used for well known claims.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.ClaimsIdentityOptions.RoleClaimType">
            <summary>
            Gets or sets the ClaimType used for a Role claim. Defaults to <see cref="F:System.Security.Claims.ClaimTypes.Role"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.ClaimsIdentityOptions.UserNameClaimType">
            <summary>
            Gets or sets the ClaimType used for the user name claim. Defaults to <see cref="F:System.Security.Claims.ClaimTypes.Name"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.ClaimsIdentityOptions.UserIdClaimType">
            <summary>
            Gets or sets the ClaimType used for the user identifier claim. Defaults to <see cref="F:System.Security.Claims.ClaimTypes.NameIdentifier"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.ClaimsIdentityOptions.SecurityStampClaimType">
            <summary>
            Gets or sets the ClaimType used for the security stamp claim. Defaults to "AspNet.Identity.SecurityStamp".
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.DefaultPersonalDataProtector">
            <summary>
            Default implementation of <see cref="T:Microsoft.AspNetCore.Identity.IPersonalDataProtector"/> that uses <see cref="T:Microsoft.AspNetCore.Identity.ILookupProtectorKeyRing"/> 
            and <see cref="T:Microsoft.AspNetCore.Identity.ILookupProtector"/> to protect data with a payload format of {keyId}:{protectedData}
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.DefaultPersonalDataProtector.#ctor(Microsoft.AspNetCore.Identity.ILookupProtectorKeyRing,Microsoft.AspNetCore.Identity.ILookupProtector)">
            <summary>
            Constructor.
            </summary>
            <param name="keyRing"></param>
            <param name="protector"></param>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.DefaultPersonalDataProtector.Unprotect(System.String)">
            <summary>
            Unprotect the data.
            </summary>
            <param name="data">The data to unprotect.</param>
            <returns>The unprotected data.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.DefaultPersonalDataProtector.Protect(System.String)">
            <summary>
            Protect the data.
            </summary>
            <param name="data">The data to protect.</param>
            <returns>The protected data.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.DefaultUserConfirmation`1">
            <summary>
            Default implementation of <see cref="T:Microsoft.AspNetCore.Identity.IUserConfirmation`1"/>.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.DefaultUserConfirmation`1.IsConfirmedAsync(Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Determines whether the specified <paramref name="user"/> is confirmed.
            </summary>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> that can be used to retrieve user properties.</param>
            <param name="user">The user.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the confirmation operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.EmailTokenProvider`1">
            <summary>
            TokenProvider that generates tokens from the user's security stamp and notifies a user via email.
            </summary>
            <typeparam name="TUser">The type used to represent a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.EmailTokenProvider`1.CanGenerateTwoFactorTokenAsync(Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Checks if a two factor authentication token can be generated for the specified <paramref name="user"/>.
            </summary>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> to retrieve the <paramref name="user"/> from.</param>
            <param name="user">The <typeparamref name="TUser"/> to check for the possibility of generating a two factor authentication token.</param>
            <returns>True if the user has an email address set, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.EmailTokenProvider`1.GetUserModifierAsync(System.String,Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Returns the a value for the user used as entropy in the generated token.
            </summary>
            <param name="purpose">The purpose of the two factor authentication token.</param>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> to retrieve the <paramref name="user"/> from.</param>
            <param name="user">The <typeparamref name="TUser"/> to check for the possibility of generating a two factor authentication token.</param>
            <returns>A string suitable for use as entropy in token generation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IdentityBuilder">
            <summary>
            Helper functions for configuring identity services.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.#ctor(System.Type,Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/>.
            </summary>
            <param name="user">The <see cref="T:System.Type"/> to use for the users.</param>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to attach to.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.#ctor(System.Type,System.Type,Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/>.
            </summary>
            <param name="user">The <see cref="T:System.Type"/> to use for the users.</param>
            <param name="role">The <see cref="T:System.Type"/> to use for the roles.</param>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to attach to.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityBuilder.UserType">
            <summary>
            Gets the <see cref="T:System.Type"/> used for users.
            </summary>
            <value>
            The <see cref="T:System.Type"/> used for users.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityBuilder.RoleType">
            <summary>
            Gets the <see cref="T:System.Type"/> used for roles.
            </summary>
            <value>
            The <see cref="T:System.Type"/> used for roles.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityBuilder.Services">
            <summary>
            Gets the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> services are attached to.
            </summary>
            <value>
            The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> services are attached to.
            </value>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddUserValidator``1">
            <summary>
            Adds an <see cref="T:Microsoft.AspNetCore.Identity.IUserValidator`1"/> for the <seealso cref="P:Microsoft.AspNetCore.Identity.IdentityBuilder.UserType"/>.
            </summary>
            <typeparam name="TValidator">The user validator type.</typeparam>
            <returns>The current <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddClaimsPrincipalFactory``1">
            <summary>
            Adds an <see cref="T:Microsoft.AspNetCore.Identity.IUserClaimsPrincipalFactory`1"/> for the <seealso cref="P:Microsoft.AspNetCore.Identity.IdentityBuilder.UserType"/>.
            </summary>
            <typeparam name="TFactory">The type of the claims principal factory.</typeparam>
            <returns>The current <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddErrorDescriber``1">
            <summary>
            Adds an <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/>.
            </summary>
            <typeparam name="TDescriber">The type of the error describer.</typeparam>
            <returns>The current <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddPasswordValidator``1">
            <summary>
            Adds an <see cref="T:Microsoft.AspNetCore.Identity.IPasswordValidator`1"/> for the <seealso cref="P:Microsoft.AspNetCore.Identity.IdentityBuilder.UserType"/>.
            </summary>
            <typeparam name="TValidator">The validator type used to validate passwords.</typeparam>
            <returns>The current <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddUserStore``1">
            <summary>
            Adds an <see cref="T:Microsoft.AspNetCore.Identity.IUserStore`1"/> for the <seealso cref="P:Microsoft.AspNetCore.Identity.IdentityBuilder.UserType"/>.
            </summary>
            <typeparam name="TStore">The user store type.</typeparam>
            <returns>The current <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddTokenProvider``1(System.String)">
            <summary>
            Adds a token provider.
            </summary>
            <typeparam name="TProvider">The type of the token provider to add.</typeparam>
            <param name="providerName">The name of the provider to add.</param>
            <returns>The current <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddTokenProvider(System.String,System.Type)">
            <summary>
            Adds a token provider for the <seealso cref="P:Microsoft.AspNetCore.Identity.IdentityBuilder.UserType"/>.
            </summary>
            <param name="providerName">The name of the provider to add.</param>
            <param name="provider">The type of the <see cref="T:Microsoft.AspNetCore.Identity.IUserTwoFactorTokenProvider`1"/> to add.</param>
            <returns>The current <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddUserManager``1">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> for the <seealso cref="P:Microsoft.AspNetCore.Identity.IdentityBuilder.UserType"/>.
            </summary>
            <typeparam name="TUserManager">The type of the user manager to add.</typeparam>
            <returns>The current <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddRoles``1">
            <summary>
            Adds Role related services for TRole, including IRoleStore, IRoleValidator, and RoleManager.
            </summary>
            <typeparam name="TRole">The role type.</typeparam>
            <returns>The current <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddRoleValidator``1">
            <summary>
            Adds an <see cref="T:Microsoft.AspNetCore.Identity.IRoleValidator`1"/> for the <seealso cref="P:Microsoft.AspNetCore.Identity.IdentityBuilder.RoleType"/>.
            </summary>
            <typeparam name="TRole">The role validator type.</typeparam>
            <returns>The current <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddPersonalDataProtection``2">
            <summary>
            Adds an <see cref="T:Microsoft.AspNetCore.Identity.ILookupProtector"/> and <see cref="T:Microsoft.AspNetCore.Identity.ILookupProtectorKeyRing"/>.
            </summary>
            <typeparam name="TProtector">The personal data protector type.</typeparam>
            <typeparam name="TKeyRing">The personal data protector key ring type.</typeparam>
            <returns>The current <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddRoleStore``1">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Identity.IRoleStore`1"/> for the <seealso cref="P:Microsoft.AspNetCore.Identity.IdentityBuilder.RoleType"/>.
            </summary>
            <typeparam name="TStore">The role store.</typeparam>
            <returns>The current <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddRoleManager``1">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Identity.RoleManager`1"/> for the <seealso cref="P:Microsoft.AspNetCore.Identity.IdentityBuilder.RoleType"/>.
            </summary>
            <typeparam name="TRoleManager">The type of the role manager to add.</typeparam>
            <returns>The current <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> instance.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IdentityError">
            <summary>
            Encapsulates an error from the identity subsystem.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityError.Code">
            <summary>
            Gets or sets the code for this error.
            </summary>
            <value>
            The code for this error.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityError.Description">
            <summary>
            Gets or sets the description for this error.
            </summary>
            <value>
            The description for this error.
            </value>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber">
            <summary>
            Service to enable localization for application facing identity errors.
            </summary>
            <remarks>
            These errors are returned to controllers and are generally used as display messages to end users.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.DefaultError">
            <summary>
            Returns the default <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/>.
            </summary>
            <returns>The default <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.ConcurrencyFailure">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a concurrency failure.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a concurrency failure.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.PasswordMismatch">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a password mismatch.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a password mismatch.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.InvalidToken">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating an invalid token.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating an invalid token.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.RecoveryCodeRedemptionFailed">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a recovery code was not redeemed.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a recovery code was not redeemed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.LoginAlreadyAssociated">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating an external login is already associated with an account.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating an external login is already associated with an account.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.InvalidUserName(System.String)">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating the specified user <paramref name="userName"/> is invalid.
            </summary>
            <param name="userName">The user name that is invalid.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating the specified user <paramref name="userName"/> is invalid.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.InvalidEmail(System.String)">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating the specified <paramref name="email"/> is invalid.
            </summary>
            <param name="email">The email that is invalid.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating the specified <paramref name="email"/> is invalid.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.DuplicateUserName(System.String)">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating the specified <paramref name="userName"/> already exists.
            </summary>
            <param name="userName">The user name that already exists.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating the specified <paramref name="userName"/> already exists.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.DuplicateEmail(System.String)">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating the specified <paramref name="email"/> is already associated with an account.
            </summary>
            <param name="email">The email that is already associated with an account.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating the specified <paramref name="email"/> is already associated with an account.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.InvalidRoleName(System.String)">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating the specified <paramref name="role"/> name is invalid.
            </summary>
            <param name="role">The invalid role.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating the specific role <paramref name="role"/> name is invalid.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.DuplicateRoleName(System.String)">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating the specified <paramref name="role"/> name already exists.
            </summary>
            <param name="role">The duplicate role.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating the specific role <paramref name="role"/> name already exists.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.UserAlreadyHasPassword">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a user already has a password.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a user already has a password.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.UserLockoutNotEnabled">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating user lockout is not enabled.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating user lockout is not enabled.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.UserAlreadyInRole(System.String)">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a user is already in the specified <paramref name="role"/>.
            </summary>
            <param name="role">The duplicate role.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a user is already in the specified <paramref name="role"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.UserNotInRole(System.String)">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a user is not in the specified <paramref name="role"/>.
            </summary>
            <param name="role">The duplicate role.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a user is not in the specified <paramref name="role"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.PasswordTooShort(System.Int32)">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a password of the specified <paramref name="length"/> does not meet the minimum length requirements.
            </summary>
            <param name="length">The length that is not long enough.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a password of the specified <paramref name="length"/> does not meet the minimum length requirements.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.PasswordRequiresUniqueChars(System.Int32)">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a password does not meet the minimum number <paramref name="uniqueChars"/> of unique chars.
            </summary>
            <param name="uniqueChars">The number of different chars that must be used.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a password does not meet the minimum number <paramref name="uniqueChars"/> of unique chars.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.PasswordRequiresNonAlphanumeric">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a password entered does not contain a non-alphanumeric character, which is required by the password policy.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a password entered does not contain a non-alphanumeric character.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.PasswordRequiresDigit">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a password entered does not contain a numeric character, which is required by the password policy.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a password entered does not contain a numeric character.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.PasswordRequiresLower">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a password entered does not contain a lower case letter, which is required by the password policy.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a password entered does not contain a lower case letter.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityErrorDescriber.PasswordRequiresUpper">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a password entered does not contain an upper case letter, which is required by the password policy.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/> indicating a password entered does not contain an upper case letter.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IdentityOptions">
            <summary>
            Represents all the options you can use to configure the identity system.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityOptions.ClaimsIdentity">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Identity.ClaimsIdentityOptions"/> for the identity system.
            </summary>
            <value>
            The <see cref="T:Microsoft.AspNetCore.Identity.ClaimsIdentityOptions"/> for the identity system.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityOptions.User">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Identity.UserOptions"/> for the identity system.
            </summary>
            <value>
            The <see cref="T:Microsoft.AspNetCore.Identity.UserOptions"/> for the identity system.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityOptions.Password">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Identity.PasswordOptions"/> for the identity system.
            </summary>
            <value>
            The <see cref="T:Microsoft.AspNetCore.Identity.PasswordOptions"/> for the identity system.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityOptions.Lockout">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Identity.LockoutOptions"/> for the identity system.
            </summary>
            <value>
            The <see cref="T:Microsoft.AspNetCore.Identity.LockoutOptions"/> for the identity system.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityOptions.SignIn">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Identity.SignInOptions"/> for the identity system.
            </summary>
            <value>
            The <see cref="T:Microsoft.AspNetCore.Identity.SignInOptions"/> for the identity system.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityOptions.Tokens">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Identity.TokenOptions"/> for the identity system.
            </summary>
            <value>
            The <see cref="T:Microsoft.AspNetCore.Identity.TokenOptions"/> for the identity system.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityOptions.Stores">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Identity.StoreOptions"/> for the identity system.
            </summary>
            <value>
            The <see cref="T:Microsoft.AspNetCore.Identity.StoreOptions"/> for the identity system.
            </value>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IdentityResult">
            <summary>
            Represents the result of an identity operation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityResult.Succeeded">
            <summary>
            Flag indicating whether if the operation succeeded or not.
            </summary>
            <value>True if the operation succeeded, otherwise false.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityResult.Errors">
            <summary>
            An <see cref="T:System.Collections.Generic.IEnumerable`1"/> of <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/>s containing an errors
            that occurred during the identity operation.
            </summary>
            <value>An <see cref="T:System.Collections.Generic.IEnumerable`1"/> of <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/>s.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityResult.Success">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> indicating a successful identity operation.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> indicating a successful operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityResult.Failed(Microsoft.AspNetCore.Identity.IdentityError[])">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> indicating a failed identity operation, with a list of <paramref name="errors"/> if applicable.
            </summary>
            <param name="errors">An optional array of <see cref="T:Microsoft.AspNetCore.Identity.IdentityError"/>s which caused the operation to fail.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> indicating a failed identity operation, with a list of <paramref name="errors"/> if applicable.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityResult.ToString">
            <summary>
            Converts the value of the current <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> object to its equivalent string representation.
            </summary>
            <returns>A string representation of the current <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> object.</returns>
            <remarks>
            If the operation was successful the ToString() will return "Succeeded" otherwise it returned 
            "Failed : " followed by a comma delimited list of error codes from its <see cref="P:Microsoft.AspNetCore.Identity.IdentityResult.Errors"/> collection, if any.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.ILookupNormalizer">
            <summary>
            Provides an abstraction for normalizing keys (emails/names) for lookup purposes.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.ILookupNormalizer.NormalizeName(System.String)">
            <summary>
            Returns a normalized representation of the specified <paramref name="name"/>.
            </summary>
            <param name="name">The key to normalize.</param>
            <returns>A normalized representation of the specified <paramref name="name"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.ILookupNormalizer.NormalizeEmail(System.String)">
            <summary>
            Returns a normalized representation of the specified <paramref name="email"/>.
            </summary>
            <param name="email">The email to normalize.</param>
            <returns>A normalized representation of the specified <paramref name="email"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.ILookupProtector">
            <summary>
            Used to protect/unprotect lookups with a specific key.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.ILookupProtector.Protect(System.String,System.String)">
            <summary>
            Protect the data using the specified key.
            </summary>
            <param name="keyId">The key to use.</param>
            <param name="data">The data to protect.</param>
            <returns>The protected data.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.ILookupProtector.Unprotect(System.String,System.String)">
            <summary>
            Unprotect the data using the specified key.
            </summary>
            <param name="keyId">The key to use.</param>
            <param name="data">The data to unprotect.</param>
            <returns>The original data.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.ILookupProtectorKeyRing">
            <summary>
            Abstraction used to manage named keys used to protect lookups.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.ILookupProtectorKeyRing.CurrentKeyId">
            <summary>
            Get the current key id.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.ILookupProtectorKeyRing.Item(System.String)">
            <summary>
            Return a specific key.
            </summary>
            <param name="keyId">The id of the key to fetch.</param>
            <returns>The key ring.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.ILookupProtectorKeyRing.GetAllKeyIds">
            <summary>
            Return all of the key ids.
            </summary>
            <returns>All of the key ids.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IPasswordHasher`1">
            <summary>
            Provides an abstraction for hashing passwords.
            </summary>
            <typeparam name="TUser">The type used to represent a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IPasswordHasher`1.HashPassword(`0,System.String)">
            <summary>
            Returns a hashed representation of the supplied <paramref name="password"/> for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose password is to be hashed.</param>
            <param name="password">The password to hash.</param>
            <returns>A hashed representation of the supplied <paramref name="password"/> for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IPasswordHasher`1.VerifyHashedPassword(`0,System.String,System.String)">
            <summary>
            Returns a <see cref="T:Microsoft.AspNetCore.Identity.PasswordVerificationResult"/> indicating the result of a password hash comparison.
            </summary>
            <param name="user">The user whose password should be verified.</param>
            <param name="hashedPassword">The hash value for a user's stored password.</param>
            <param name="providedPassword">The password supplied for comparison.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Identity.PasswordVerificationResult"/> indicating the result of a password hash comparison.</returns>
            <remarks>Implementations of this method should be time consistent.</remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IPasswordValidator`1">
            <summary>
            Provides an abstraction for validating passwords.
            </summary>
            <typeparam name="TUser">The type that represents a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IPasswordValidator`1.ValidateAsync(Microsoft.AspNetCore.Identity.UserManager{`0},`0,System.String)">
            <summary>
            Validates a password as an asynchronous operation.
            </summary>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> to retrieve the <paramref name="user"/> properties from.</param>
            <param name="user">The user whose password should be validated.</param>
            <param name="password">The password supplied for validation</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IPersonalDataProtector">
            <summary>
            Provides an abstraction used for personal data encryption.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IPersonalDataProtector.Protect(System.String)">
            <summary>
            Protect the data.
            </summary>
            <param name="data">The data to protect.</param>
            <returns>The protected data.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IPersonalDataProtector.Unprotect(System.String)">
            <summary>
            Unprotect the data.
            </summary>
            <param name="data"></param>
            <returns>The unprotected data.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IProtectedUserStore`1">
            <summary>
            Marker interface used to signal that the store supports the <see cref="P:Microsoft.AspNetCore.Identity.StoreOptions.ProtectPersonalData"/> flag.
            </summary>
            <typeparam name="TUser">The type that represents a user.</typeparam>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IQueryableRoleStore`1">
            <summary>
            Provides an abstraction for querying roles in a Role store.
            </summary>
            <typeparam name="TRole">The type encapsulating a role.</typeparam>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IQueryableRoleStore`1.Roles">
            <summary>
            Returns an <see cref="T:System.Linq.IQueryable`1"/> collection of roles.
            </summary>
            <value>An <see cref="T:System.Linq.IQueryable`1"/> collection of roles.</value>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IQueryableUserStore`1">
            <summary>
            Provides an abstraction for querying users in a User store.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IQueryableUserStore`1.Users">
            <summary>
            Returns an <see cref="T:System.Linq.IQueryable`1"/> collection of users.
            </summary>
            <value>An <see cref="T:System.Linq.IQueryable`1"/> collection of users.</value>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IRoleClaimStore`1">
            <summary>
            Provides an abstraction for a store of role specific claims.
            </summary>
            <typeparam name="TRole">The type encapsulating a role.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IRoleClaimStore`1.GetClaimsAsync(`0,System.Threading.CancellationToken)">
            <summary>
             Gets a list of <see cref="T:System.Security.Claims.Claim"/>s to be belonging to the specified <paramref name="role"/> as an asynchronous operation.
            </summary>
            <param name="role">The role whose claims to retrieve.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the result of the asynchronous query, a list of <see cref="T:System.Security.Claims.Claim"/>s.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IRoleClaimStore`1.AddClaimAsync(`0,System.Security.Claims.Claim,System.Threading.CancellationToken)">
            <summary>
            Add a new claim to a role as an asynchronous operation.
            </summary>
            <param name="role">The role to add a claim to.</param>
            <param name="claim">The <see cref="T:System.Security.Claims.Claim"/> to add.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IRoleClaimStore`1.RemoveClaimAsync(`0,System.Security.Claims.Claim,System.Threading.CancellationToken)">
            <summary>
            Remove a claim from a role as an asynchronous operation.
            </summary>
            <param name="role">The role to remove the claim from.</param>
            <param name="claim">The <see cref="T:System.Security.Claims.Claim"/> to remove.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IRoleStore`1">
            <summary>
            Provides an abstraction for a storage and management of roles.
            </summary>
            <typeparam name="TRole">The type that represents a role.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IRoleStore`1.CreateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Creates a new role in a store as an asynchronous operation.
            </summary>
            <param name="role">The role to create in the store.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the asynchronous query.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IRoleStore`1.UpdateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Updates a role in a store as an asynchronous operation.
            </summary>
            <param name="role">The role to update in the store.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the asynchronous query.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IRoleStore`1.DeleteAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Deletes a role from the store as an asynchronous operation.
            </summary>
            <param name="role">The role to delete from the store.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the asynchronous query.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IRoleStore`1.GetRoleIdAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the ID for a role from the store as an asynchronous operation.
            </summary>
            <param name="role">The role whose ID should be returned.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that contains the ID of the role.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IRoleStore`1.GetRoleNameAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the name of a role from the store as an asynchronous operation.
            </summary>
            <param name="role">The role whose name should be returned.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that contains the name of the role.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IRoleStore`1.SetRoleNameAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the name of a role in the store as an asynchronous operation.
            </summary>
            <param name="role">The role whose name should be set.</param>
            <param name="roleName">The name of the role.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IRoleStore`1.GetNormalizedRoleNameAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Get a role's normalized name as an asynchronous operation.
            </summary>
            <param name="role">The role whose normalized name should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that contains the name of the role.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IRoleStore`1.SetNormalizedRoleNameAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Set a role's normalized name as an asynchronous operation.
            </summary>
            <param name="role">The role whose normalized name should be set.</param>
            <param name="normalizedName">The normalized name to set</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IRoleStore`1.FindByIdAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Finds the role who has the specified ID as an asynchronous operation.
            </summary>
            <param name="roleId">The role ID to look for.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that result of the look up.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IRoleStore`1.FindByNameAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Finds the role who has the specified normalized name as an asynchronous operation.
            </summary>
            <param name="normalizedRoleName">The normalized role name to look for.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that result of the look up.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IRoleValidator`1">
            <summary>
            Provides an abstraction for a validating a role.
            </summary>
            <typeparam name="TRole">The type encapsulating a role.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IRoleValidator`1.ValidateAsync(Microsoft.AspNetCore.Identity.RoleManager{`0},`0)">
            <summary>
            Validates a role as an asynchronous operation.
            </summary>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.RoleManager`1"/> managing the role store.</param>
            <param name="role">The role to validate.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the asynchronous validation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserAuthenticationTokenStore`1">
            <summary>
            Provides an abstraction to store a user's authentication tokens.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserAuthenticationTokenStore`1.SetTokenAsync(`0,System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the token value for a particular user.
            </summary>
            <param name="user">The user.</param>
            <param name="loginProvider">The authentication provider for the token.</param>
            <param name="name">The name of the token.</param>
            <param name="value">The value of the token.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserAuthenticationTokenStore`1.RemoveTokenAsync(`0,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Deletes a token for a user.
            </summary>
            <param name="user">The user.</param>
            <param name="loginProvider">The authentication provider for the token.</param>
            <param name="name">The name of the token.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserAuthenticationTokenStore`1.GetTokenAsync(`0,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Returns the token value.
            </summary>
            <param name="user">The user.</param>
            <param name="loginProvider">The authentication provider for the token.</param>
            <param name="name">The name of the token.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserAuthenticatorKeyStore`1">
            <summary>
            Provides an abstraction for a store which stores info about user's authenticator.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserAuthenticatorKeyStore`1.SetAuthenticatorKeyAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the authenticator key for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose authenticator key should be set.</param>
            <param name="key">The authenticator key to set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserAuthenticatorKeyStore`1.GetAuthenticatorKeyAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Get the authenticator key for the specified <paramref name="user" />.
            </summary>
            <param name="user">The user whose security stamp should be set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the security stamp for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserClaimsPrincipalFactory`1">
            <summary>
            Provides an abstraction for a factory to create a <see cref="T:System.Security.Claims.ClaimsPrincipal"/> from a user.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserClaimsPrincipalFactory`1.CreateAsync(`0)">
            <summary>
            Creates a <see cref="T:System.Security.Claims.ClaimsPrincipal"/> from an user asynchronously.
            </summary>
            <param name="user">The user to create a <see cref="T:System.Security.Claims.ClaimsPrincipal"/> from.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous creation operation, containing the created <see cref="T:System.Security.Claims.ClaimsPrincipal"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserClaimStore`1">
            <summary>
            Provides an abstraction for a store of claims for a user.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserClaimStore`1.GetClaimsAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets a list of <see cref="T:System.Security.Claims.Claim"/>s to be belonging to the specified <paramref name="user"/> as an asynchronous operation.
            </summary>
            <param name="user">The role whose claims to retrieve.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the result of the asynchronous query, a list of <see cref="T:System.Security.Claims.Claim"/>s.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserClaimStore`1.AddClaimsAsync(`0,System.Collections.Generic.IEnumerable{System.Security.Claims.Claim},System.Threading.CancellationToken)">
            <summary>
            Add claims to a user as an asynchronous operation.
            </summary>
            <param name="user">The user to add the claim to.</param>
            <param name="claims">The collection of <see cref="T:System.Security.Claims.Claim"/>s to add.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserClaimStore`1.ReplaceClaimAsync(`0,System.Security.Claims.Claim,System.Security.Claims.Claim,System.Threading.CancellationToken)">
            <summary>
            Replaces the given <paramref name="claim"/> on the specified <paramref name="user"/> with the <paramref name="newClaim"/>
            </summary>
            <param name="user">The user to replace the claim on.</param>
            <param name="claim">The claim to replace.</param>
            <param name="newClaim">The new claim to replace the existing <paramref name="claim"/> with.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserClaimStore`1.RemoveClaimsAsync(`0,System.Collections.Generic.IEnumerable{System.Security.Claims.Claim},System.Threading.CancellationToken)">
            <summary>
            Removes the specified <paramref name="claims"/> from the given <paramref name="user"/>.
            </summary>
            <param name="user">The user to remove the specified <paramref name="claims"/> from.</param>
            <param name="claims">A collection of <see cref="T:System.Security.Claims.Claim"/>s to remove.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserClaimStore`1.GetUsersForClaimAsync(System.Security.Claims.Claim,System.Threading.CancellationToken)">
            <summary>
            Returns a list of users who contain the specified <see cref="T:System.Security.Claims.Claim"/>.
            </summary>
            <param name="claim">The claim to look for.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the result of the asynchronous query, a list of <typeparamref name="TUser"/> who
            contain the specified claim.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserConfirmation`1">
            <summary>
            Provides an abstraction for confirmation of user accounts.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserConfirmation`1.IsConfirmedAsync(Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Determines whether the specified <paramref name="user"/> is confirmed.
            </summary>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> that can be used to retrieve user properties.</param>
            <param name="user">The user.</param>
            <returns>Whether the user is confirmed.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserEmailStore`1">
            <summary>
            Provides an abstraction for the storage and management of user email addresses.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserEmailStore`1.SetEmailAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the <paramref name="email"/> address for a <paramref name="user"/>.
            </summary>
            <param name="user">The user whose email should be set.</param>
            <param name="email">The email to set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserEmailStore`1.GetEmailAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the email address for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose email should be returned.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The task object containing the results of the asynchronous operation, the email address for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserEmailStore`1.GetEmailConfirmedAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets a flag indicating whether the email address for the specified <paramref name="user"/> has been verified, true if the email address is verified otherwise
            false.
            </summary>
            <param name="user">The user whose email confirmation status should be returned.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The task object containing the results of the asynchronous operation, a flag indicating whether the email address for the specified <paramref name="user"/>
            has been confirmed or not.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserEmailStore`1.SetEmailConfirmedAsync(`0,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Sets the flag indicating whether the specified <paramref name="user"/>'s email address has been confirmed or not.
            </summary>
            <param name="user">The user whose email confirmation status should be set.</param>
            <param name="confirmed">A flag indicating if the email address has been confirmed, true if the address is confirmed otherwise false.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserEmailStore`1.FindByEmailAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets the user, if any, associated with the specified, normalized email address.
            </summary>
            <param name="normalizedEmail">The normalized email address to return the user for.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The task object containing the results of the asynchronous lookup operation, the user if any associated with the specified normalized email address.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserEmailStore`1.GetNormalizedEmailAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Returns the normalized email for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose email address to retrieve.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The task object containing the results of the asynchronous lookup operation, the normalized email address if any associated with the specified user.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserEmailStore`1.SetNormalizedEmailAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the normalized email for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose email address to set.</param>
            <param name="normalizedEmail">The normalized email to set for the specified <paramref name="user"/>.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserLockoutStore`1">
            <summary>
            Provides an abstraction for a storing information which can be used to implement account lockout, 
            including access failures and lockout status
            </summary>
            <typeparam name="TUser">The type that represents a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserLockoutStore`1.GetLockoutEndDateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the last <see cref="T:System.DateTimeOffset"/> a user's last lockout expired, if any.
            Any time in the past should be indicates a user is not locked out.
            </summary>
            <param name="user">The user whose lockout date should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the result of the asynchronous query, a <see cref="T:System.DateTimeOffset"/> containing the last time
            a user's lockout expired, if any.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserLockoutStore`1.SetLockoutEndDateAsync(`0,System.Nullable{System.DateTimeOffset},System.Threading.CancellationToken)">
            <summary>
            Locks out a user until the specified end date has passed. Setting a end date in the past immediately unlocks a user.
            </summary>
            <param name="user">The user whose lockout date should be set.</param>
            <param name="lockoutEnd">The <see cref="T:System.DateTimeOffset"/> after which the <paramref name="user"/>'s lockout should end.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserLockoutStore`1.IncrementAccessFailedCountAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Records that a failed access has occurred, incrementing the failed access count.
            </summary>
            <param name="user">The user whose cancellation count should be incremented.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the incremented failed access count.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserLockoutStore`1.ResetAccessFailedCountAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Resets a user's failed access count.
            </summary>
            <param name="user">The user whose failed access count should be reset.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
            <remarks>This is typically called after the account is successfully accessed.</remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserLockoutStore`1.GetAccessFailedCountAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Retrieves the current failed access count for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose failed access count should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the failed access count.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserLockoutStore`1.GetLockoutEnabledAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Retrieves a flag indicating whether user lockout can enabled for the specified user.
            </summary>
            <param name="user">The user whose ability to be locked out should be returned.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, true if a user can be locked out, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserLockoutStore`1.SetLockoutEnabledAsync(`0,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Set the flag indicating if the specified <paramref name="user"/> can be locked out.
            </summary>
            <param name="user">The user whose ability to be locked out should be set.</param>
            <param name="enabled">A flag indicating if lock out can be enabled for the specified <paramref name="user"/>.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserLoginStore`1">
            <summary>
            Provides an abstraction for storing information that maps external login information provided
            by Microsoft Account, Facebook etc. to a user account.
            </summary>
            <typeparam name="TUser">The type that represents a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserLoginStore`1.AddLoginAsync(`0,Microsoft.AspNetCore.Identity.UserLoginInfo,System.Threading.CancellationToken)">
            <summary>
            Adds an external <see cref="T:Microsoft.AspNetCore.Identity.UserLoginInfo"/> to the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user to add the login to.</param>
            <param name="login">The external <see cref="T:Microsoft.AspNetCore.Identity.UserLoginInfo"/> to add to the specified <paramref name="user"/>.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserLoginStore`1.RemoveLoginAsync(`0,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Attempts to remove the provided login information from the specified <paramref name="user"/>.
            and returns a flag indicating whether the removal succeed or not.
            </summary>
            <param name="user">The user to remove the login information from.</param>
            <param name="loginProvider">The login provide whose information should be removed.</param>
            <param name="providerKey">The key given by the external login provider for the specified user.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserLoginStore`1.GetLoginsAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Retrieves the associated logins for the specified <param ref="user"/>.
            </summary>
            <param name="user">The user whose associated logins to retrieve.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> for the asynchronous operation, containing a list of <see cref="T:Microsoft.AspNetCore.Identity.UserLoginInfo"/> for the specified <paramref name="user"/>, if any.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserLoginStore`1.FindByLoginAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Retrieves the user associated with the specified login provider and login provider key.
            </summary>
            <param name="loginProvider">The login provider who provided the <paramref name="providerKey"/>.</param>
            <param name="providerKey">The key provided by the <paramref name="loginProvider"/> to identify a user.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> for the asynchronous operation, containing the user, if any which matched the specified login provider and key.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserPasswordStore`1">
            <summary>
            Provides an abstraction for a store containing users' password hashes.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserPasswordStore`1.SetPasswordHashAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the password hash for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose password hash to set.</param>
            <param name="passwordHash">The password hash to set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserPasswordStore`1.GetPasswordHashAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the password hash for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose password hash to retrieve.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, returning the password hash for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserPasswordStore`1.HasPasswordAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets a flag indicating whether the specified <paramref name="user"/> has a password.
            </summary>
            <param name="user">The user to return a flag for, indicating whether they have a password or not.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, returning true if the specified <paramref name="user"/> has a password
            otherwise false.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserPhoneNumberStore`1">
            <summary>
            Provides an abstraction for a store containing users' telephone numbers.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserPhoneNumberStore`1.SetPhoneNumberAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the telephone number for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose telephone number should be set.</param>
            <param name="phoneNumber">The telephone number to set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserPhoneNumberStore`1.GetPhoneNumberAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the telephone number, if any, for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose telephone number should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the user's telephone number, if any.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserPhoneNumberStore`1.GetPhoneNumberConfirmedAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets a flag indicating whether the specified <paramref name="user"/>'s telephone number has been confirmed.
            </summary>
            <param name="user">The user to return a flag for, indicating whether their telephone number is confirmed.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, returning true if the specified <paramref name="user"/> has a confirmed
            telephone number otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserPhoneNumberStore`1.SetPhoneNumberConfirmedAsync(`0,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Sets a flag indicating if the specified <paramref name="user"/>'s phone number has been confirmed.
            </summary>
            <param name="user">The user whose telephone number confirmation status should be set.</param>
            <param name="confirmed">A flag indicating whether the user's telephone number has been confirmed.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserRoleStore`1">
            <summary>
            Provides an abstraction for a store which maps users to roles.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserRoleStore`1.AddToRoleAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Add the specified <paramref name="user"/> to the named role.
            </summary>
            <param name="user">The user to add to the named role.</param>
            <param name="roleName">The name of the role to add the user to.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserRoleStore`1.RemoveFromRoleAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Remove the specified <paramref name="user"/> from the named role.
            </summary>
            <param name="user">The user to remove the named role from.</param>
            <param name="roleName">The name of the role to remove.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserRoleStore`1.GetRolesAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets a list of role names the specified <paramref name="user"/> belongs to.
            </summary>
            <param name="user">The user whose role names to retrieve.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing a list of role names.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserRoleStore`1.IsInRoleAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Returns a flag indicating whether the specified <paramref name="user"/> is a member of the given named role.
            </summary>
            <param name="user">The user whose role membership should be checked.</param>
            <param name="roleName">The name of the role to be checked.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing a flag indicating whether the specified <paramref name="user"/> is
            a member of the named role.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserRoleStore`1.GetUsersInRoleAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Returns a list of Users who are members of the named role.
            </summary>
            <param name="roleName">The name of the role whose membership should be returned.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing a list of users who are in the named role.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserSecurityStampStore`1">
            <summary>
            Provides an abstraction for a store which stores a user's security stamp.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserSecurityStampStore`1.SetSecurityStampAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the provided security <paramref name="stamp"/> for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose security stamp should be set.</param>
            <param name="stamp">The security stamp to set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserSecurityStampStore`1.GetSecurityStampAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Get the security stamp for the specified <paramref name="user" />.
            </summary>
            <param name="user">The user whose security stamp should be set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the security stamp for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserStore`1">
            <summary>
            Provides an abstraction for a store which manages user accounts.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserStore`1.GetUserIdAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the user identifier for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose identifier should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the identifier for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserStore`1.GetUserNameAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the user name for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose name should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the name for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserStore`1.SetUserNameAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the given <paramref name="userName" /> for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose name should be set.</param>
            <param name="userName">The user name to set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserStore`1.GetNormalizedUserNameAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the normalized user name for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose normalized name should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the normalized user name for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserStore`1.SetNormalizedUserNameAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the given normalized name for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose name should be set.</param>
            <param name="normalizedName">The normalized name to set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserStore`1.CreateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Creates the specified <paramref name="user"/> in the user store.
            </summary>
            <param name="user">The user to create.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the creation operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserStore`1.UpdateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Updates the specified <paramref name="user"/> in the user store.
            </summary>
            <param name="user">The user to update.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the update operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserStore`1.DeleteAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Deletes the specified <paramref name="user"/> from the user store.
            </summary>
            <param name="user">The user to delete.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the update operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserStore`1.FindByIdAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Finds and returns a user, if any, who has the specified <paramref name="userId"/>.
            </summary>
            <param name="userId">The user ID to search for.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the user matching the specified <paramref name="userId"/> if it exists.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserStore`1.FindByNameAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Finds and returns a user, if any, who has the specified normalized user name.
            </summary>
            <param name="normalizedUserName">The normalized user name to search for.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the user matching the specified <paramref name="normalizedUserName"/> if it exists.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserTwoFactorRecoveryCodeStore`1">
            <summary>
            Provides an abstraction for a store which stores a user's recovery codes.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserTwoFactorRecoveryCodeStore`1.ReplaceCodesAsync(`0,System.Collections.Generic.IEnumerable{System.String},System.Threading.CancellationToken)">
            <summary>
            Updates the recovery codes for the user while invalidating any previous recovery codes.
            </summary>
            <param name="user">The user to store new recovery codes for.</param>
            <param name="recoveryCodes">The new recovery codes for the user.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The new recovery codes for the user.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserTwoFactorRecoveryCodeStore`1.RedeemCodeAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Returns whether a recovery code is valid for a user. Note: recovery codes are only valid
            once, and will be invalid after use.
            </summary>
            <param name="user">The user who owns the recovery code.</param>
            <param name="code">The recovery code to use.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>True if the recovery code was found for the user.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserTwoFactorRecoveryCodeStore`1.CountCodesAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Returns how many recovery code are still valid for a user.
            </summary>
            <param name="user">The user who owns the recovery code.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The number of valid recovery codes for the user..</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserTwoFactorStore`1">
            <summary>
            Provides an abstraction to store a flag indicating whether a user has two factor authentication enabled.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserTwoFactorStore`1.SetTwoFactorEnabledAsync(`0,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Sets a flag indicating whether the specified <paramref name="user"/> has two factor authentication enabled or not,
            as an asynchronous operation.
            </summary>
            <param name="user">The user whose two factor authentication enabled status should be set.</param>
            <param name="enabled">A flag indicating whether the specified <paramref name="user"/> has two factor authentication enabled.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserTwoFactorStore`1.GetTwoFactorEnabledAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Returns a flag indicating whether the specified <paramref name="user"/> has two factor authentication enabled or not,
            as an asynchronous operation.
            </summary>
            <param name="user">The user whose two factor authentication enabled status should be set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing a flag indicating whether the specified 
            <paramref name="user"/> has two factor authentication enabled or not.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserTwoFactorTokenProvider`1">
            <summary>
            Provides an abstraction for two factor token generators.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserTwoFactorTokenProvider`1.GenerateAsync(System.String,Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Generates a token for the specified <paramref name="user"/> and <paramref name="purpose"/>.
            </summary>
            <param name="purpose">The purpose the token will be used for.</param>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> that can be used to retrieve user properties.</param>
            <param name="user">The user a token should be generated for.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the token for the specified 
            <paramref name="user"/> and <paramref name="purpose"/>.
            </returns>
            <remarks>
            The <paramref name="purpose"/> parameter allows a token generator to be used for multiple types of token whilst
            insuring a token for one purpose cannot be used for another. For example if you specified a purpose of "Email" 
            and validated it with the same purpose a token with the purpose of TOTP would not pass the check even if it was
            for the same user.
            
            Implementations of <see cref="T:Microsoft.AspNetCore.Identity.IUserTwoFactorTokenProvider`1"/> should validate that purpose is not null or empty to
            help with token separation.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserTwoFactorTokenProvider`1.ValidateAsync(System.String,System.String,Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Returns a flag indicating whether the specified <paramref name="token"/> is valid for the given
            <paramref name="user"/> and <paramref name="purpose"/>.
            </summary>
            <param name="purpose">The purpose the token will be used for.</param>
            <param name="token">The token to validate.</param>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> that can be used to retrieve user properties.</param>
            <param name="user">The user a token should be validated for.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the a flag indicating the result
            of validating the <paramref name="token"> for the specified </paramref><paramref name="user"/> and <paramref name="purpose"/>.
            The task will return true if the token is valid, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserTwoFactorTokenProvider`1.CanGenerateTwoFactorTokenAsync(Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Returns a flag indicating whether the token provider can generate a token suitable for two factor authentication token for
            the specified <paramref name="user"/>.
            </summary>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> that can be used to retrieve user properties.</param>
            <param name="user">The user a token could be generated for.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the a flag indicating if a two
            factor token could be generated by this provider for the specified <paramref name="user"/>.
            The task will return true if a two factor authentication token could be generated, otherwise false.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IUserValidator`1">
            <summary>
            Provides an abstraction for user validation.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IUserValidator`1.ValidateAsync(Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Validates the specified <paramref name="user"/> as an asynchronous operation.
            </summary>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> that can be used to retrieve user properties.</param>
            <param name="user">The user to validate.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the validation operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.LockoutOptions">
            <summary>
            Options for configuring user lockout.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.LockoutOptions.AllowedForNewUsers">
            <summary>
            Gets or sets a flag indicating whether a new user can be locked out. Defaults to true.
            </summary>
            <value>
            True if a newly created user can be locked out, otherwise false.  
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.LockoutOptions.MaxFailedAccessAttempts">
            <summary>
            Gets or sets the number of failed access attempts allowed before a user is locked out,
            assuming lock out is enabled. Defaults to 5.
            </summary>
            <value>
            The number of failed access attempts allowed before a user is locked out, if lockout is enabled.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.LockoutOptions.DefaultLockoutTimeSpan">
            <summary>
            Gets or sets the <see cref="T:System.TimeSpan"/> a user is locked out for when a lockout occurs. Defaults to 5 minutes.
            </summary>
            <value>The <see cref="T:System.TimeSpan"/> a user is locked out for when a lockout occurs.</value>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.PasswordHasher`1">
            <summary>
            Implements the standard Identity password hashing.
            </summary>
            <typeparam name="TUser">The type used to represent a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.PasswordHasher`1.#ctor(Microsoft.Extensions.Options.IOptions{Microsoft.AspNetCore.Identity.PasswordHasherOptions})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Identity.PasswordHasher`1"/>.
            </summary>
            <param name="optionsAccessor">The options for this instance.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.PasswordHasher`1.HashPassword(`0,System.String)">
            <summary>
            Returns a hashed representation of the supplied <paramref name="password"/> for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose password is to be hashed.</param>
            <param name="password">The password to hash.</param>
            <returns>A hashed representation of the supplied <paramref name="password"/> for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.PasswordHasher`1.VerifyHashedPassword(`0,System.String,System.String)">
            <summary>
            Returns a <see cref="T:Microsoft.AspNetCore.Identity.PasswordVerificationResult"/> indicating the result of a password hash comparison.
            </summary>
            <param name="user">The user whose password should be verified.</param>
            <param name="hashedPassword">The hash value for a user's stored password.</param>
            <param name="providedPassword">The password supplied for comparison.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Identity.PasswordVerificationResult"/> indicating the result of a password hash comparison.</returns>
            <remarks>Implementations of this method should be time consistent.</remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.PasswordHasherCompatibilityMode">
            <summary>
            Specifies the format used for hashing passwords.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Identity.PasswordHasherCompatibilityMode.IdentityV2">
            <summary>
            Indicates hashing passwords in a way that is compatible with ASP.NET Identity versions 1 and 2.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Identity.PasswordHasherCompatibilityMode.IdentityV3">
            <summary>
            Indicates hashing passwords in a way that is compatible with ASP.NET Identity version 3.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.PasswordHasherOptions">
            <summary>
            Specifies options for password hashing.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.PasswordHasherOptions.CompatibilityMode">
            <summary>
            Gets or sets the compatibility mode used when hashing passwords. Defaults to 'ASP.NET Identity version 3'.
            </summary>
            <value>
            The compatibility mode used when hashing passwords.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.PasswordHasherOptions.IterationCount">
            <summary>
            Gets or sets the number of iterations used when hashing passwords using PBKDF2. Default is 10,000.
            </summary>
            <value>
            The number of iterations used when hashing passwords using PBKDF2.
            </value>
            <remarks>
            This value is only used when the compatibility mode is set to 'V3'.
            The value must be a positive integer. 
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.PasswordOptions">
            <summary>
            Specifies options for password requirements.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.PasswordOptions.RequiredLength">
            <summary>
            Gets or sets the minimum length a password must be. Defaults to 6.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.PasswordOptions.RequiredUniqueChars">
            <summary>
            Gets or sets the minimum number of unique characters which a password must contain. Defaults to 1.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.PasswordOptions.RequireNonAlphanumeric">
            <summary>
            Gets or sets a flag indicating if passwords must contain a non-alphanumeric character. Defaults to true.
            </summary>
            <value>True if passwords must contain a non-alphanumeric character, otherwise false.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.PasswordOptions.RequireLowercase">
            <summary>
            Gets or sets a flag indicating if passwords must contain a lower case ASCII character. Defaults to true.
            </summary>
            <value>True if passwords must contain a lower case ASCII character.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.PasswordOptions.RequireUppercase">
            <summary>
            Gets or sets a flag indicating if passwords must contain a upper case ASCII character. Defaults to true.
            </summary>
            <value>True if passwords must contain a upper case ASCII character.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.PasswordOptions.RequireDigit">
            <summary>
            Gets or sets a flag indicating if passwords must contain a digit. Defaults to true.
            </summary>
            <value>True if passwords must contain a digit.</value>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.PasswordValidator`1">
            <summary>
            Provides the default password policy for Identity.
            </summary>
            <typeparam name="TUser">The type that represents a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.PasswordValidator`1.#ctor(Microsoft.AspNetCore.Identity.IdentityErrorDescriber)">
            <summary>
            Constructions a new instance of <see cref="T:Microsoft.AspNetCore.Identity.PasswordValidator`1"/>.
            </summary>
            <param name="errors">The <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> to retrieve error text from.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.PasswordValidator`1.Describer">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> used to supply error text.
            </summary>
            <value>The <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> used to supply error text.</value>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.PasswordValidator`1.ValidateAsync(Microsoft.AspNetCore.Identity.UserManager{`0},`0,System.String)">
            <summary>
            Validates a password as an asynchronous operation.
            </summary>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> to retrieve the <paramref name="user"/> properties from.</param>
            <param name="user">The user whose password should be validated.</param>
            <param name="password">The password supplied for validation</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.PasswordValidator`1.IsDigit(System.Char)">
            <summary>
            Returns a flag indicating whether the supplied character is a digit.
            </summary>
            <param name="c">The character to check if it is a digit.</param>
            <returns>True if the character is a digit, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.PasswordValidator`1.IsLower(System.Char)">
            <summary>
            Returns a flag indicating whether the supplied character is a lower case ASCII letter.
            </summary>
            <param name="c">The character to check if it is a lower case ASCII letter.</param>
            <returns>True if the character is a lower case ASCII letter, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.PasswordValidator`1.IsUpper(System.Char)">
            <summary>
            Returns a flag indicating whether the supplied character is an upper case ASCII letter.
            </summary>
            <param name="c">The character to check if it is an upper case ASCII letter.</param>
            <returns>True if the character is an upper case ASCII letter, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.PasswordValidator`1.IsLetterOrDigit(System.Char)">
            <summary>
            Returns a flag indicating whether the supplied character is an ASCII letter or digit.
            </summary>
            <param name="c">The character to check if it is an ASCII letter or digit.</param>
            <returns>True if the character is an ASCII letter or digit, otherwise false.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.PasswordVerificationResult">
            <summary>
            Specifies the results for password verification.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Identity.PasswordVerificationResult.Failed">
            <summary>
            Indicates password verification failed.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Identity.PasswordVerificationResult.Success">
            <summary>
            Indicates password verification was successful.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Identity.PasswordVerificationResult.SuccessRehashNeeded">
            <summary>
            Indicates password verification was successful however the password was encoded using a deprecated algorithm
            and should be rehashed and updated.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.PersonalDataAttribute">
            <summary>
            Used to indicate that a something is considered personal data.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.PhoneNumberTokenProvider`1">
            <summary>
            Represents a token provider that generates tokens from a user's security stamp and
            sends them to the user via their phone number.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.PhoneNumberTokenProvider`1.CanGenerateTwoFactorTokenAsync(Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Returns a flag indicating whether the token provider can generate a token suitable for two factor authentication token for
            the specified <paramref name="user"/>.
            </summary>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> that can be used to retrieve user properties.</param>
            <param name="user">The user a token could be generated for.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the a flag indicating if a two
            factor token could be generated by this provider for the specified <paramref name="user"/>.
            The task will return true if a two factor authentication token could be generated as the user has
            a telephone number, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.PhoneNumberTokenProvider`1.GetUserModifierAsync(System.String,Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Returns a constant, provider and user unique modifier used for entropy in generated tokens from user information.
            </summary>
            <param name="purpose">The purpose the token will be generated for.</param>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> that can be used to retrieve user properties.</param>
            <param name="user">The user a token should be generated for.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing a constant modifier for the specified 
            <paramref name="user"/> and <paramref name="purpose"/>.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.ProtectedPersonalDataAttribute">
            <summary>
            Used to indicate that a something is considered personal data and should be protected.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.RoleManager`1">
            <summary>
            Provides the APIs for managing roles in a persistence store.
            </summary>
            <typeparam name="TRole">The type encapsulating a role.</typeparam>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.RoleManager`1.CancellationToken">
            <summary>
            The cancellation token used to cancel operations.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.#ctor(Microsoft.AspNetCore.Identity.IRoleStore{`0},System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Identity.IRoleValidator{`0}},Microsoft.AspNetCore.Identity.ILookupNormalizer,Microsoft.AspNetCore.Identity.IdentityErrorDescriber,Microsoft.Extensions.Logging.ILogger{Microsoft.AspNetCore.Identity.RoleManager{`0}})">
            <summary>
            Constructs a new instance of <see cref="T:Microsoft.AspNetCore.Identity.RoleManager`1"/>.
            </summary>
            <param name="store">The persistence store the manager will operate over.</param>
            <param name="roleValidators">A collection of validators for roles.</param>
            <param name="keyNormalizer">The normalizer to use when normalizing role names to keys.</param>
            <param name="errors">The <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> used to provider error messages.</param>
            <param name="logger">The logger used to log messages, warnings and errors.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.RoleManager`1.Store">
            <summary>
            Gets the persistence store this instance operates over.
            </summary>
            <value>The persistence store this instance operates over.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.RoleManager`1.Logger">
            <summary>
            Gets the <see cref="T:Microsoft.Extensions.Logging.ILogger"/> used to log messages from the manager.
            </summary>
            <value>
            The <see cref="T:Microsoft.Extensions.Logging.ILogger"/> used to log messages from the manager.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.RoleManager`1.RoleValidators">
            <summary>
            Gets a list of validators for roles to call before persistence.
            </summary>
            <value>A list of validators for roles to call before persistence.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.RoleManager`1.ErrorDescriber">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> used to provider error messages.
            </summary>
            <value>
            The <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> used to provider error messages.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.RoleManager`1.KeyNormalizer">
            <summary>
            Gets the normalizer to use when normalizing role names to keys.
            </summary>
            <value>
            The normalizer to use when normalizing role names to keys.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.RoleManager`1.Roles">
            <summary>
            Gets an IQueryable collection of Roles if the persistence store is an <see cref="T:Microsoft.AspNetCore.Identity.IQueryableRoleStore`1"/>,
            otherwise throws a <see cref="T:System.NotSupportedException"/>.
            </summary>
            <value>An IQueryable collection of Roles if the persistence store is an <see cref="T:Microsoft.AspNetCore.Identity.IQueryableRoleStore`1"/>.</value>
            <exception cref="T:System.NotSupportedException">Thrown if the persistence store is not an <see cref="T:Microsoft.AspNetCore.Identity.IQueryableRoleStore`1"/>.</exception>
            <remarks>
            Callers to this property should use <see cref="P:Microsoft.AspNetCore.Identity.RoleManager`1.SupportsQueryableRoles"/> to ensure the backing role store supports 
            returning an IQueryable list of roles.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.RoleManager`1.SupportsQueryableRoles">
            <summary>
            Gets a flag indicating whether the underlying persistence store supports returning an <see cref="T:System.Linq.IQueryable"/> collection of roles.
            </summary>
            <value>
            true if the underlying persistence store supports returning an <see cref="T:System.Linq.IQueryable"/> collection of roles, otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.RoleManager`1.SupportsRoleClaims">
            <summary>
            Gets a flag indicating whether the underlying persistence store supports <see cref="T:System.Security.Claims.Claim"/>s for roles.
            </summary>
            <value>
            true if the underlying persistence store supports <see cref="T:System.Security.Claims.Claim"/>s for roles, otherwise false.
            </value>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.CreateAsync(`0)">
            <summary>
            Creates the specified <paramref name="role"/> in the persistence store.
            </summary>
            <param name="role">The role to create.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.UpdateNormalizedRoleNameAsync(`0)">
            <summary>
            Updates the normalized name for the specified <paramref name="role"/>.
            </summary>
            <param name="role">The role whose normalized name needs to be updated.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.UpdateAsync(`0)">
            <summary>
            Updates the specified <paramref name="role"/>.
            </summary>
            <param name="role">The role to updated.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> for the update.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.DeleteAsync(`0)">
            <summary>
            Deletes the specified <paramref name="role"/>.
            </summary>
            <param name="role">The role to delete.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> for the delete.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.RoleExistsAsync(System.String)">
            <summary>
            Gets a flag indicating whether the specified <paramref name="roleName"/> exists.
            </summary>
            <param name="roleName">The role name whose existence should be checked.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing true if the role name exists, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.NormalizeKey(System.String)">
            <summary>
            Gets a normalized representation of the specified <paramref name="key"/>.
            </summary>
            <param name="key">The value to normalize.</param>
            <returns>A normalized representation of the specified <paramref name="key"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.FindByIdAsync(System.String)">
            <summary>
            Finds the role associated with the specified <paramref name="roleId"/> if any.
            </summary>
            <param name="roleId">The role ID whose role should be returned.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the role 
            associated with the specified <paramref name="roleId"/>
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.GetRoleNameAsync(`0)">
            <summary>
            Gets the name of the specified <paramref name="role"/>.
            </summary>
            <param name="role">The role whose name should be retrieved.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the name of the 
            specified <paramref name="role"/>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.SetRoleNameAsync(`0,System.String)">
            <summary>
            Sets the name of the specified <paramref name="role"/>.
            </summary>
            <param name="role">The role whose name should be set.</param>
            <param name="name">The name to set.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.GetRoleIdAsync(`0)">
            <summary>
            Gets the ID of the specified <paramref name="role"/>.
            </summary>
            <param name="role">The role whose ID should be retrieved.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the ID of the 
            specified <paramref name="role"/>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.FindByNameAsync(System.String)">
            <summary>
            Finds the role associated with the specified <paramref name="roleName"/> if any.
            </summary>
            <param name="roleName">The name of the role to be returned.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the role 
            associated with the specified <paramref name="roleName"/>
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.AddClaimAsync(`0,System.Security.Claims.Claim)">
            <summary>
            Adds a claim to a role.
            </summary>
            <param name="role">The role to add the claim to.</param>
            <param name="claim">The claim to add.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.RemoveClaimAsync(`0,System.Security.Claims.Claim)">
            <summary>
            Removes a claim from a role.
            </summary>
            <param name="role">The role to remove the claim from.</param>
            <param name="claim">The claim to remove.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.GetClaimsAsync(`0)">
            <summary>
            Gets a list of claims associated with the specified <paramref name="role"/>.
            </summary>
            <param name="role">The role whose claims should be returned.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the list of <see cref="T:System.Security.Claims.Claim"/>s
            associated with the specified <paramref name="role"/>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.Dispose">
            <summary>
            Releases all resources used by the role manager.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the role manager and optionally releases the managed resources.
            </summary>
            <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.ValidateRoleAsync(`0)">
            <summary>
            Should return <see cref="P:Microsoft.AspNetCore.Identity.IdentityResult.Success"/> if validation is successful. This is
            called before saving the role via Create or Update.
            </summary>
            <param name="role">The role</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> representing whether validation was successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.UpdateRoleAsync(`0)">
            <summary>
            Called to update the role after validating and updating the normalized role name.
            </summary>
            <param name="role">The role.</param>
            <returns>Whether the operation was successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleManager`1.ThrowIfDisposed">
            <summary>
            Throws if this class has been disposed.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.RoleValidator`1">
            <summary>
            Provides the default validation of roles.
            </summary>
            <typeparam name="TRole">The type encapsulating a role.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleValidator`1.#ctor(Microsoft.AspNetCore.Identity.IdentityErrorDescriber)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Identity.RoleValidator`1"/>/
            </summary>
            <param name="errors">The <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> used to provider error messages.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleValidator`1.ValidateAsync(Microsoft.AspNetCore.Identity.RoleManager{`0},`0)">
            <summary>
            Validates a role as an asynchronous operation.
            </summary>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.RoleManager`1"/> managing the role store.</param>
            <param name="role">The role to validate.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the asynchronous validation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.SignInOptions">
            <summary>
            Options for configuring sign in.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.SignInOptions.RequireConfirmedEmail">
            <summary>
            Gets or sets a flag indicating whether a confirmed email address is required to sign in. Defaults to false.
            </summary>
            <value>True if a user must have a confirmed email address before they can sign in, otherwise false.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.SignInOptions.RequireConfirmedPhoneNumber">
            <summary>
            Gets or sets a flag indicating whether a confirmed telephone number is required to sign in. Defaults to false.
            </summary>
            <value>True if a user must have a confirmed telephone number before they can sign in, otherwise false.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.SignInOptions.RequireConfirmedAccount">
            <summary>
            Gets or sets a flag indicating whether a confirmed <see cref="T:Microsoft.AspNetCore.Identity.IUserConfirmation`1"/> account is required to sign in. Defaults to false.
            </summary>
            <value>True if a user must have a confirmed account before they can sign in, otherwise false.</value>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.SignInResult">
            <summary>
            Represents the result of a sign-in operation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.SignInResult.Succeeded">
            <summary>
            Returns a flag indication whether the sign-in was successful.
            </summary>
            <value>True if the sign-in was successful, otherwise false.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.SignInResult.IsLockedOut">
            <summary>
            Returns a flag indication whether the user attempting to sign-in is locked out.
            </summary>
            <value>True if the user attempting to sign-in is locked out, otherwise false.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.SignInResult.IsNotAllowed">
            <summary>
            Returns a flag indication whether the user attempting to sign-in is not allowed to sign-in.
            </summary>
            <value>True if the user attempting to sign-in is not allowed to sign-in, otherwise false.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.SignInResult.RequiresTwoFactor">
            <summary>
            Returns a flag indication whether the user attempting to sign-in requires two factor authentication.
            </summary>
            <value>True if the user attempting to sign-in requires two factor authentication, otherwise false.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.SignInResult.Success">
            <summary>
            Returns a <see cref="T:Microsoft.AspNetCore.Identity.SignInResult"/> that represents a successful sign-in.
            </summary>
            <returns>A <see cref="T:Microsoft.AspNetCore.Identity.SignInResult"/> that represents a successful sign-in.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.SignInResult.Failed">
            <summary>
            Returns a <see cref="T:Microsoft.AspNetCore.Identity.SignInResult"/> that represents a failed sign-in.
            </summary>
            <returns>A <see cref="T:Microsoft.AspNetCore.Identity.SignInResult"/> that represents a failed sign-in.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.SignInResult.LockedOut">
            <summary>
            Returns a <see cref="T:Microsoft.AspNetCore.Identity.SignInResult"/> that represents a sign-in attempt that failed because 
            the user was logged out.
            </summary>
            <returns>A <see cref="T:Microsoft.AspNetCore.Identity.SignInResult"/> that represents sign-in attempt that failed due to the
            user being locked out.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.SignInResult.NotAllowed">
            <summary>
            Returns a <see cref="T:Microsoft.AspNetCore.Identity.SignInResult"/> that represents a sign-in attempt that failed because 
            the user is not allowed to sign-in.
            </summary>
            <returns>A <see cref="T:Microsoft.AspNetCore.Identity.SignInResult"/> that represents sign-in attempt that failed due to the
            user is not allowed to sign-in.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.SignInResult.TwoFactorRequired">
            <summary>
            Returns a <see cref="T:Microsoft.AspNetCore.Identity.SignInResult"/> that represents a sign-in attempt that needs two-factor 
            authentication.
            </summary>
            <returns>A <see cref="T:Microsoft.AspNetCore.Identity.SignInResult"/> that represents sign-in attempt that needs two-factor
            authentication.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.SignInResult.ToString">
            <summary>
            Converts the value of the current <see cref="T:Microsoft.AspNetCore.Identity.SignInResult"/> object to its equivalent string representation.
            </summary>
            <returns>A string representation of value of the current <see cref="T:Microsoft.AspNetCore.Identity.SignInResult"/> object.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.StoreOptions">
            <summary>
            Used for store specific options
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.StoreOptions.MaxLengthForKeys">
            <summary>
            If set to a positive number, the default OnModelCreating will use this value as the max length for any 
            properties used as keys, i.e. UserId, LoginProvider, ProviderKey.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.StoreOptions.ProtectPersonalData">
            <summary>
            If set to true, the store must protect all personally identifying data for a user. 
            This will be enforced by requiring the store to implement <see cref="T:Microsoft.AspNetCore.Identity.IProtectedUserStore`1"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.TokenOptions">
            <summary>
            Options for user tokens.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Identity.TokenOptions.DefaultProvider">
            <summary>
            Default token provider name used by email confirmation, password reset, and change email.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Identity.TokenOptions.DefaultEmailProvider">
            <summary>
            Default token provider name used by the email provider. />.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Identity.TokenOptions.DefaultPhoneProvider">
            <summary>
            Default token provider name used by the phone provider. />.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Identity.TokenOptions.DefaultAuthenticatorProvider">
            <summary>
            Default token provider name used by the <see cref="P:Microsoft.AspNetCore.Identity.TokenOptions.AuthenticatorTokenProvider"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.TokenOptions.ProviderMap">
            <summary>
            Will be used to construct UserTokenProviders with the key used as the providerName.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.TokenOptions.EmailConfirmationTokenProvider">
            <summary>
            Gets or sets the token provider used to generate tokens used in account confirmation emails.
            </summary>
            <value>
            The <see cref="T:Microsoft.AspNetCore.Identity.IUserTwoFactorTokenProvider`1"/> used to generate tokens used in account confirmation emails.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.TokenOptions.PasswordResetTokenProvider">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Identity.IUserTwoFactorTokenProvider`1"/> used to generate tokens used in password reset emails.
            </summary>
            <value>
            The <see cref="T:Microsoft.AspNetCore.Identity.IUserTwoFactorTokenProvider`1"/> used to generate tokens used in password reset emails.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.TokenOptions.ChangeEmailTokenProvider">
            <summary>
            Gets or sets the <see cref="P:Microsoft.AspNetCore.Identity.TokenOptions.ChangeEmailTokenProvider"/> used to generate tokens used in email change confirmation emails.
            </summary>
            <value>
            The <see cref="P:Microsoft.AspNetCore.Identity.TokenOptions.ChangeEmailTokenProvider"/> used to generate tokens used in email change confirmation emails.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.TokenOptions.ChangePhoneNumberTokenProvider">
            <summary>
            Gets or sets the <see cref="P:Microsoft.AspNetCore.Identity.TokenOptions.ChangePhoneNumberTokenProvider"/> used to generate tokens used when changing phone numbers.
            </summary>
            <value>
            The <see cref="P:Microsoft.AspNetCore.Identity.TokenOptions.ChangePhoneNumberTokenProvider"/> used to generate tokens used when changing phone numbers.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.TokenOptions.AuthenticatorTokenProvider">
            <summary>
            Gets or sets the <see cref="P:Microsoft.AspNetCore.Identity.TokenOptions.AuthenticatorTokenProvider"/> used to validate two factor sign ins with an authenticator.
            </summary>
            <value>
            The <see cref="P:Microsoft.AspNetCore.Identity.TokenOptions.AuthenticatorTokenProvider"/> used to validate two factor sign ins with an authenticator.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.TokenOptions.AuthenticatorIssuer">
            <summary>
            Gets or sets the issuer used for the authenticator issuer.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.TokenProviderDescriptor">
            <summary>
            Used to represents a token provider in <see cref="T:Microsoft.AspNetCore.Identity.TokenOptions"/>'s TokenMap.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.TokenProviderDescriptor.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Identity.TokenProviderDescriptor"/> class.
            </summary>
            <param name="type">The concrete type for this token provider.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.TokenProviderDescriptor.ProviderType">
            <summary>
            The type that will be used for this token provider.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.TokenProviderDescriptor.ProviderInstance">
            <summary>
            If specified, the instance to be used for the token provider.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.TotpSecurityStampBasedTokenProvider`1">
            <summary>
            Represents a token provider that generates time based codes using the user's security stamp.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.TotpSecurityStampBasedTokenProvider`1.GenerateAsync(System.String,Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Generates a token for the specified <paramref name="user"/> and <paramref name="purpose"/>.
            </summary>
            <param name="purpose">The purpose the token will be used for.</param>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> that can be used to retrieve user properties.</param>
            <param name="user">The user a token should be generated for.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the token for the specified 
            <paramref name="user"/> and <paramref name="purpose"/>.
            </returns>
            <remarks>
            The <paramref name="purpose"/> parameter allows a token generator to be used for multiple types of token whilst
            insuring a token for one purpose cannot be used for another. For example if you specified a purpose of "Email" 
            and validated it with the same purpose a token with the purpose of TOTP would not pass the check even if it was
            for the same user.
            
            Implementations of <see cref="T:Microsoft.AspNetCore.Identity.IUserTwoFactorTokenProvider`1"/> should validate that purpose is not null or empty to
            help with token separation.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.TotpSecurityStampBasedTokenProvider`1.ValidateAsync(System.String,System.String,Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Returns a flag indicating whether the specified <paramref name="token"/> is valid for the given
            <paramref name="user"/> and <paramref name="purpose"/>.
            </summary>
            <param name="purpose">The purpose the token will be used for.</param>
            <param name="token">The token to validate.</param>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> that can be used to retrieve user properties.</param>
            <param name="user">The user a token should be validated for.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the a flag indicating the result
            of validating the <paramref name="token"> for the specified </paramref><paramref name="user"/> and <paramref name="purpose"/>.
            The task will return true if the token is valid, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.TotpSecurityStampBasedTokenProvider`1.GetUserModifierAsync(System.String,Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Returns a constant, provider and user unique modifier used for entropy in generated tokens from user information.
            </summary>
            <param name="purpose">The purpose the token will be generated for.</param>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> that can be used to retrieve user properties.</param>
            <param name="user">The user a token should be generated for.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing a constant modifier for the specified 
            <paramref name="user"/> and <paramref name="purpose"/>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.TotpSecurityStampBasedTokenProvider`1.CanGenerateTwoFactorTokenAsync(Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Returns a flag indicating whether the token provider can generate a token suitable for two factor authentication token for
            the specified <paramref name="user"/>.
            </summary>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> that can be used to retrieve user properties.</param>
            <param name="user">The user a token could be generated for.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the a flag indicating if a two
            factor token could be generated by this provider for the specified <paramref name="user"/>.
            The task will return true if a two factor authentication token could be generated, otherwise false.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.UpperInvariantLookupNormalizer">
            <summary>
            Implements <see cref="T:Microsoft.AspNetCore.Identity.ILookupNormalizer"/> by converting keys to their upper cased invariant culture representation.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UpperInvariantLookupNormalizer.NormalizeName(System.String)">
            <summary>
            Returns a normalized representation of the specified <paramref name="name"/>.
            </summary>
            <param name="name">The key to normalize.</param>
            <returns>A normalized representation of the specified <paramref name="name"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UpperInvariantLookupNormalizer.NormalizeEmail(System.String)">
            <summary>
            Returns a normalized representation of the specified <paramref name="email"/>.
            </summary>
            <param name="email">The email to normalize.</param>
            <returns>A normalized representation of the specified <paramref name="email"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.UserClaimsPrincipalFactory`1">
            <summary>
            Provides methods to create a claims principal for a given user.
            </summary>
            <typeparam name="TUser">The type used to represent a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserClaimsPrincipalFactory`1.#ctor(Microsoft.AspNetCore.Identity.UserManager{`0},Microsoft.Extensions.Options.IOptions{Microsoft.AspNetCore.Identity.IdentityOptions})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Identity.UserClaimsPrincipalFactory`1"/> class.
            </summary>
            <param name="userManager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> to retrieve user information from.</param>
            <param name="optionsAccessor">The configured <see cref="T:Microsoft.AspNetCore.Identity.IdentityOptions"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserClaimsPrincipalFactory`1.UserManager">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> for this factory.
            </summary>
            <value>
            The current <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> for this factory instance.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserClaimsPrincipalFactory`1.Options">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Identity.IdentityOptions"/> for this factory.
            </summary>
            <value>
            The current <see cref="T:Microsoft.AspNetCore.Identity.IdentityOptions"/> for this factory instance.
            </value>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserClaimsPrincipalFactory`1.CreateAsync(`0)">
            <summary>
            Creates a <see cref="T:System.Security.Claims.ClaimsPrincipal"/> from an user asynchronously.
            </summary>
            <param name="user">The user to create a <see cref="T:System.Security.Claims.ClaimsPrincipal"/> from.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous creation operation, containing the created <see cref="T:System.Security.Claims.ClaimsPrincipal"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserClaimsPrincipalFactory`1.GenerateClaimsAsync(`0)">
            <summary>
            Generate the claims for a user.
            </summary>
            <param name="user">The user to create a <see cref="T:System.Security.Claims.ClaimsIdentity"/> from.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous creation operation, containing the created <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.UserClaimsPrincipalFactory`2">
            <summary>
            Provides methods to create a claims principal for a given user.
            </summary>
            <typeparam name="TUser">The type used to represent a user.</typeparam>
            <typeparam name="TRole">The type used to represent a role.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserClaimsPrincipalFactory`2.#ctor(Microsoft.AspNetCore.Identity.UserManager{`0},Microsoft.AspNetCore.Identity.RoleManager{`1},Microsoft.Extensions.Options.IOptions{Microsoft.AspNetCore.Identity.IdentityOptions})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Identity.UserClaimsPrincipalFactory`2"/> class.
            </summary>
            <param name="userManager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> to retrieve user information from.</param>
            <param name="roleManager">The <see cref="T:Microsoft.AspNetCore.Identity.RoleManager`1"/> to retrieve a user's roles from.</param>
            <param name="options">The configured <see cref="T:Microsoft.AspNetCore.Identity.IdentityOptions"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserClaimsPrincipalFactory`2.RoleManager">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Identity.RoleManager`1"/> for this factory.
            </summary>
            <value>
            The current <see cref="T:Microsoft.AspNetCore.Identity.RoleManager`1"/> for this factory instance.
            </value>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserClaimsPrincipalFactory`2.GenerateClaimsAsync(`0)">
            <summary>
            Generate the claims for a user.
            </summary>
            <param name="user">The user to create a <see cref="T:System.Security.Claims.ClaimsIdentity"/> from.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous creation operation, containing the created <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.UserLoginInfo">
            <summary>
            Represents login information and source for a user record.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserLoginInfo.#ctor(System.String,System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Identity.UserLoginInfo"/>
            </summary>
            <param name="loginProvider">The provider associated with this login information.</param>
            <param name="providerKey">The unique identifier for this user provided by the login provider.</param>
            <param name="displayName">The display name for this user provided by the login provider.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserLoginInfo.LoginProvider">
            <summary>
            Gets or sets the provider for this instance of <see cref="T:Microsoft.AspNetCore.Identity.UserLoginInfo"/>.
            </summary>
            <value>The provider for the this instance of <see cref="T:Microsoft.AspNetCore.Identity.UserLoginInfo"/></value>
            <remarks>
            Examples of the provider may be Local, Facebook, Google, etc.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserLoginInfo.ProviderKey">
            <summary>
            Gets or sets the unique identifier for the user identity user provided by the login provider.
            </summary>
            <value>
            The unique identifier for the user identity user provided by the login provider.
            </value>
            <remarks>
            This would be unique per provider, examples may be @microsoft as a Twitter provider key.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserLoginInfo.ProviderDisplayName">
            <summary>
            Gets or sets the display name for the provider.
            </summary>
            <value>
            The display name for the provider.
            </value>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.UserManager`1">
            <summary>
            Provides the APIs for managing user in a persistence store.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="F:Microsoft.AspNetCore.Identity.UserManager`1.ResetPasswordTokenPurpose">
            <summary>
            The data protection purpose used for the reset password related methods.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Identity.UserManager`1.ChangePhoneNumberTokenPurpose">
            <summary>
            The data protection purpose used for the change phone number methods.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Identity.UserManager`1.ConfirmEmailTokenPurpose">
            <summary>
            The data protection purpose used for the email confirmation related methods.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.CancellationToken">
            <summary>
            The cancellation token used to cancel operations.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.#ctor(Microsoft.AspNetCore.Identity.IUserStore{`0},Microsoft.Extensions.Options.IOptions{Microsoft.AspNetCore.Identity.IdentityOptions},Microsoft.AspNetCore.Identity.IPasswordHasher{`0},System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Identity.IUserValidator{`0}},System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Identity.IPasswordValidator{`0}},Microsoft.AspNetCore.Identity.ILookupNormalizer,Microsoft.AspNetCore.Identity.IdentityErrorDescriber,System.IServiceProvider,Microsoft.Extensions.Logging.ILogger{Microsoft.AspNetCore.Identity.UserManager{`0}})">
            <summary>
            Constructs a new instance of <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/>.
            </summary>
            <param name="store">The persistence store the manager will operate over.</param>
            <param name="optionsAccessor">The accessor used to access the <see cref="T:Microsoft.AspNetCore.Identity.IdentityOptions"/>.</param>
            <param name="passwordHasher">The password hashing implementation to use when saving passwords.</param>
            <param name="userValidators">A collection of <see cref="T:Microsoft.AspNetCore.Identity.IUserValidator`1"/> to validate users against.</param>
            <param name="passwordValidators">A collection of <see cref="T:Microsoft.AspNetCore.Identity.IPasswordValidator`1"/> to validate passwords against.</param>
            <param name="keyNormalizer">The <see cref="T:Microsoft.AspNetCore.Identity.ILookupNormalizer"/> to use when generating index keys for users.</param>
            <param name="errors">The <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> used to provider error messages.</param>
            <param name="services">The <see cref="T:System.IServiceProvider"/> used to resolve services.</param>
            <param name="logger">The logger used to log messages, warnings and errors.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.Store">
            <summary>
            Gets or sets the persistence store the manager operates over.
            </summary>
            <value>The persistence store the manager operates over.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.Logger">
            <summary>
            The <see cref="T:Microsoft.Extensions.Logging.ILogger"/> used to log messages from the manager.
            </summary>
            <value>
            The <see cref="T:Microsoft.Extensions.Logging.ILogger"/> used to log messages from the manager.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.PasswordHasher">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Identity.IPasswordHasher`1"/> used to hash passwords.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.UserValidators">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Identity.IUserValidator`1"/> used to validate users.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.PasswordValidators">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Identity.IPasswordValidator`1"/> used to validate passwords.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.KeyNormalizer">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Identity.ILookupNormalizer"/> used to normalize things like user and role names.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.ErrorDescriber">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> used to generate error messages.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.Options">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Identity.IdentityOptions"/> used to configure Identity.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.SupportsUserAuthenticationTokens">
            <summary>
            Gets a flag indicating whether the backing user store supports authentication tokens.
            </summary>
            <value>
            true if the backing user store supports authentication tokens, otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.SupportsUserAuthenticatorKey">
            <summary>
            Gets a flag indicating whether the backing user store supports a user authenticator.
            </summary>
            <value>
            true if the backing user store supports a user authenticator, otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.SupportsUserTwoFactorRecoveryCodes">
            <summary>
            Gets a flag indicating whether the backing user store supports recovery codes.
            </summary>
            <value>
            true if the backing user store supports a user authenticator, otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.SupportsUserTwoFactor">
            <summary>
            Gets a flag indicating whether the backing user store supports two factor authentication.
            </summary>
            <value>
            true if the backing user store supports user two factor authentication, otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.SupportsUserPassword">
            <summary>
            Gets a flag indicating whether the backing user store supports user passwords.
            </summary>
            <value>
            true if the backing user store supports user passwords, otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.SupportsUserSecurityStamp">
            <summary>
            Gets a flag indicating whether the backing user store supports security stamps.
            </summary>
            <value>
            true if the backing user store supports user security stamps, otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.SupportsUserRole">
            <summary>
            Gets a flag indicating whether the backing user store supports user roles.
            </summary>
            <value>
            true if the backing user store supports user roles, otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.SupportsUserLogin">
            <summary>
            Gets a flag indicating whether the backing user store supports external logins.
            </summary>
            <value>
            true if the backing user store supports external logins, otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.SupportsUserEmail">
            <summary>
            Gets a flag indicating whether the backing user store supports user emails.
            </summary>
            <value>
            true if the backing user store supports user emails, otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.SupportsUserPhoneNumber">
            <summary>
            Gets a flag indicating whether the backing user store supports user telephone numbers.
            </summary>
            <value>
            true if the backing user store supports user telephone numbers, otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.SupportsUserClaim">
            <summary>
            Gets a flag indicating whether the backing user store supports user claims.
            </summary>
            <value>
            true if the backing user store supports user claims, otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.SupportsUserLockout">
            <summary>
            Gets a flag indicating whether the backing user store supports user lock-outs.
            </summary>
            <value>
            true if the backing user store supports user lock-outs, otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.SupportsQueryableUsers">
            <summary>
            Gets a flag indicating whether the backing user store supports returning
            <see cref="T:System.Linq.IQueryable"/> collections of information.
            </summary>
            <value>
            true if the backing user store supports returning <see cref="T:System.Linq.IQueryable"/> collections of
            information, otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserManager`1.Users">
            <summary>
                Returns an IQueryable of users if the store is an IQueryableUserStore
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.Dispose">
            <summary>
            Releases all resources used by the user manager.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetUserName(System.Security.Claims.ClaimsPrincipal)">
            <summary>
            Returns the Name claim value if present otherwise returns null.
            </summary>
            <param name="principal">The <see cref="T:System.Security.Claims.ClaimsPrincipal"/> instance.</param>
            <returns>The Name claim value, or null if the claim is not present.</returns>
            <remarks>The Name claim is identified by <see cref="F:System.Security.Claims.ClaimsIdentity.DefaultNameClaimType"/>.</remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetUserId(System.Security.Claims.ClaimsPrincipal)">
            <summary>
            Returns the User ID claim value if present otherwise returns null.
            </summary>
            <param name="principal">The <see cref="T:System.Security.Claims.ClaimsPrincipal"/> instance.</param>
            <returns>The User ID claim value, or null if the claim is not present.</returns>
            <remarks>The User ID claim is identified by <see cref="F:System.Security.Claims.ClaimTypes.NameIdentifier"/>.</remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetUserAsync(System.Security.Claims.ClaimsPrincipal)">
            <summary>
            Returns the user corresponding to the IdentityOptions.ClaimsIdentity.UserIdClaimType claim in
            the principal or null.
            </summary>
            <param name="principal">The principal which contains the user id claim.</param>
            <returns>The user corresponding to the IdentityOptions.ClaimsIdentity.UserIdClaimType claim in
            the principal or null</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GenerateConcurrencyStampAsync(`0)">
            <summary>
            Generates a value suitable for use in concurrency tracking.
            </summary>
            <param name="user">The user to generate the stamp for.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the security
            stamp for the specified <paramref name="user"/>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.CreateAsync(`0)">
            <summary>
            Creates the specified <paramref name="user"/> in the backing store with no password,
            as an asynchronous operation.
            </summary>
            <param name="user">The user to create.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.UpdateAsync(`0)">
            <summary>
            Updates the specified <paramref name="user"/> in the backing store.
            </summary>
            <param name="user">The user to update.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.DeleteAsync(`0)">
            <summary>
            Deletes the specified <paramref name="user"/> from the backing store.
            </summary>
            <param name="user">The user to delete.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.FindByIdAsync(System.String)">
            <summary>
            Finds and returns a user, if any, who has the specified <paramref name="userId"/>.
            </summary>
            <param name="userId">The user ID to search for.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the user matching the specified <paramref name="userId"/> if it exists.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.FindByNameAsync(System.String)">
            <summary>
            Finds and returns a user, if any, who has the specified user name.
            </summary>
            <param name="userName">The user name to search for.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the user matching the specified <paramref name="userName"/> if it exists.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.CreateAsync(`0,System.String)">
            <summary>
            Creates the specified <paramref name="user"/> in the backing store with given password,
            as an asynchronous operation.
            </summary>
            <param name="user">The user to create.</param>
            <param name="password">The password for the user to hash and store.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.NormalizeName(System.String)">
            <summary>
            Normalize user or role name for consistent comparisons.
            </summary>
            <param name="name">The name to normalize.</param>
            <returns>A normalized value representing the specified <paramref name="name"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.NormalizeEmail(System.String)">
            <summary>
            Normalize email for consistent comparisons.
            </summary>
            <param name="email">The email to normalize.</param>
            <returns>A normalized value representing the specified <paramref name="email"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.UpdateNormalizedUserNameAsync(`0)">
            <summary>
            Updates the normalized user name for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose user name should be normalized and updated.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetUserNameAsync(`0)">
            <summary>
            Gets the user name for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose name should be retrieved.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the name for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.SetUserNameAsync(`0,System.String)">
            <summary>
            Sets the given <paramref name="userName" /> for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose name should be set.</param>
            <param name="userName">The user name to set.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetUserIdAsync(`0)">
            <summary>
            Gets the user identifier for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose identifier should be retrieved.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the identifier for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.CheckPasswordAsync(`0,System.String)">
            <summary>
            Returns a flag indicating whether the given <paramref name="password"/> is valid for the
            specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose password should be validated.</param>
            <param name="password">The password to validate</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing true if
            the specified <paramref name="password" /> matches the one store for the <paramref name="user"/>,
            otherwise false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.HasPasswordAsync(`0)">
            <summary>
            Gets a flag indicating whether the specified <paramref name="user"/> has a password.
            </summary>
            <param name="user">The user to return a flag for, indicating whether they have a password or not.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, returning true if the specified <paramref name="user"/> has a password
            otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.AddPasswordAsync(`0,System.String)">
            <summary>
            Adds the <paramref name="password"/> to the specified <paramref name="user"/> only if the user
            does not already have a password.
            </summary>
            <param name="user">The user whose password should be set.</param>
            <param name="password">The password to set.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.ChangePasswordAsync(`0,System.String,System.String)">
            <summary>
            Changes a user's password after confirming the specified <paramref name="currentPassword"/> is correct,
            as an asynchronous operation.
            </summary>
            <param name="user">The user whose password should be set.</param>
            <param name="currentPassword">The current password to validate before changing.</param>
            <param name="newPassword">The new password to set for the specified <paramref name="user"/>.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.RemovePasswordAsync(`0)">
            <summary>
            Removes a user's password.
            </summary>
            <param name="user">The user whose password should be removed.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.VerifyPasswordAsync(Microsoft.AspNetCore.Identity.IUserPasswordStore{`0},`0,System.String)">
            <summary>
            Returns a <see cref="T:Microsoft.AspNetCore.Identity.PasswordVerificationResult"/> indicating the result of a password hash comparison.
            </summary>
            <param name="store">The store containing a user's password.</param>
            <param name="user">The user whose password should be verified.</param>
            <param name="password">The password to verify.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.PasswordVerificationResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetSecurityStampAsync(`0)">
            <summary>
            Get the security stamp for the specified <paramref name="user" />.
            </summary>
            <param name="user">The user whose security stamp should be set.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the security stamp for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.UpdateSecurityStampAsync(`0)">
            <summary>
            Regenerates the security stamp for the specified <paramref name="user" />.
            </summary>
            <param name="user">The user whose security stamp should be regenerated.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
            <remarks>
            Regenerating a security stamp will sign out any saved login for the user.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GeneratePasswordResetTokenAsync(`0)">
            <summary>
            Generates a password reset token for the specified <paramref name="user"/>, using
            the configured password reset token provider.
            </summary>
            <param name="user">The user to generate a password reset token for.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation,
            containing a password reset token for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.ResetPasswordAsync(`0,System.String,System.String)">
            <summary>
            Resets the <paramref name="user"/>'s password to the specified <paramref name="newPassword"/> after
            validating the given password reset <paramref name="token"/>.
            </summary>
            <param name="user">The user whose password should be reset.</param>
            <param name="token">The password reset token to verify.</param>
            <param name="newPassword">The new password to set if reset token verification succeeds.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.FindByLoginAsync(System.String,System.String)">
            <summary>
            Retrieves the user associated with the specified external login provider and login provider key.
            </summary>
            <param name="loginProvider">The login provider who provided the <paramref name="providerKey"/>.</param>
            <param name="providerKey">The key provided by the <paramref name="loginProvider"/> to identify a user.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> for the asynchronous operation, containing the user, if any which matched the specified login provider and key.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.RemoveLoginAsync(`0,System.String,System.String)">
            <summary>
            Attempts to remove the provided external login information from the specified <paramref name="user"/>.
            and returns a flag indicating whether the removal succeed or not.
            </summary>
            <param name="user">The user to remove the login information from.</param>
            <param name="loginProvider">The login provide whose information should be removed.</param>
            <param name="providerKey">The key given by the external login provider for the specified user.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.AddLoginAsync(`0,Microsoft.AspNetCore.Identity.UserLoginInfo)">
            <summary>
            Adds an external <see cref="T:Microsoft.AspNetCore.Identity.UserLoginInfo"/> to the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user to add the login to.</param>
            <param name="login">The external <see cref="T:Microsoft.AspNetCore.Identity.UserLoginInfo"/> to add to the specified <paramref name="user"/>.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetLoginsAsync(`0)">
            <summary>
            Retrieves the associated logins for the specified <param ref="user"/>.
            </summary>
            <param name="user">The user whose associated logins to retrieve.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> for the asynchronous operation, containing a list of <see cref="T:Microsoft.AspNetCore.Identity.UserLoginInfo"/> for the specified <paramref name="user"/>, if any.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.AddClaimAsync(`0,System.Security.Claims.Claim)">
            <summary>
            Adds the specified <paramref name="claim"/> to the <paramref name="user"/>.
            </summary>
            <param name="user">The user to add the claim to.</param>
            <param name="claim">The claim to add.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.AddClaimsAsync(`0,System.Collections.Generic.IEnumerable{System.Security.Claims.Claim})">
            <summary>
            Adds the specified <paramref name="claims"/> to the <paramref name="user"/>.
            </summary>
            <param name="user">The user to add the claim to.</param>
            <param name="claims">The claims to add.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.ReplaceClaimAsync(`0,System.Security.Claims.Claim,System.Security.Claims.Claim)">
            <summary>
            Replaces the given <paramref name="claim"/> on the specified <paramref name="user"/> with the <paramref name="newClaim"/>
            </summary>
            <param name="user">The user to replace the claim on.</param>
            <param name="claim">The claim to replace.</param>
            <param name="newClaim">The new claim to replace the existing <paramref name="claim"/> with.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.RemoveClaimAsync(`0,System.Security.Claims.Claim)">
            <summary>
            Removes the specified <paramref name="claim"/> from the given <paramref name="user"/>.
            </summary>
            <param name="user">The user to remove the specified <paramref name="claim"/> from.</param>
            <param name="claim">The <see cref="T:System.Security.Claims.Claim"/> to remove.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.RemoveClaimsAsync(`0,System.Collections.Generic.IEnumerable{System.Security.Claims.Claim})">
            <summary>
            Removes the specified <paramref name="claims"/> from the given <paramref name="user"/>.
            </summary>
            <param name="user">The user to remove the specified <paramref name="claims"/> from.</param>
            <param name="claims">A collection of <see cref="T:System.Security.Claims.Claim"/>s to remove.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetClaimsAsync(`0)">
            <summary>
            Gets a list of <see cref="T:System.Security.Claims.Claim"/>s to be belonging to the specified <paramref name="user"/> as an asynchronous operation.
            </summary>
            <param name="user">The user whose claims to retrieve.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the result of the asynchronous query, a list of <see cref="T:System.Security.Claims.Claim"/>s.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.AddToRoleAsync(`0,System.String)">
            <summary>
            Add the specified <paramref name="user"/> to the named role.
            </summary>
            <param name="user">The user to add to the named role.</param>
            <param name="role">The name of the role to add the user to.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.AddToRolesAsync(`0,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Add the specified <paramref name="user"/> to the named roles.
            </summary>
            <param name="user">The user to add to the named roles.</param>
            <param name="roles">The name of the roles to add the user to.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.RemoveFromRoleAsync(`0,System.String)">
            <summary>
            Removes the specified <paramref name="user"/> from the named role.
            </summary>
            <param name="user">The user to remove from the named role.</param>
            <param name="role">The name of the role to remove the user from.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.RemoveFromRolesAsync(`0,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Removes the specified <paramref name="user"/> from the named roles.
            </summary>
            <param name="user">The user to remove from the named roles.</param>
            <param name="roles">The name of the roles to remove the user from.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetRolesAsync(`0)">
            <summary>
            Gets a list of role names the specified <paramref name="user"/> belongs to.
            </summary>
            <param name="user">The user whose role names to retrieve.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing a list of role names.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.IsInRoleAsync(`0,System.String)">
            <summary>
            Returns a flag indicating whether the specified <paramref name="user"/> is a member of the given named role.
            </summary>
            <param name="user">The user whose role membership should be checked.</param>
            <param name="role">The name of the role to be checked.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing a flag indicating whether the specified <paramref name="user"/> is
            a member of the named role.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetEmailAsync(`0)">
            <summary>
            Gets the email address for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose email should be returned.</param>
            <returns>The task object containing the results of the asynchronous operation, the email address for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.SetEmailAsync(`0,System.String)">
            <summary>
            Sets the <paramref name="email"/> address for a <paramref name="user"/>.
            </summary>
            <param name="user">The user whose email should be set.</param>
            <param name="email">The email to set.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.FindByEmailAsync(System.String)">
            <summary>
            Gets the user, if any, associated with the normalized value of the specified email address.
            Note: Its recommended that identityOptions.User.RequireUniqueEmail be set to true when using this method, otherwise
            the store may throw if there are users with duplicate emails.
            </summary>
            <param name="email">The email address to return the user for.</param>
            <returns>
            The task object containing the results of the asynchronous lookup operation, the user, if any, associated with a normalized value of the specified email address.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.UpdateNormalizedEmailAsync(`0)">
            <summary>
            Updates the normalized email for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose email address should be normalized and updated.</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GenerateEmailConfirmationTokenAsync(`0)">
            <summary>
            Generates an email confirmation token for the specified user.
            </summary>
            <param name="user">The user to generate an email confirmation token for.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, an email confirmation token.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.ConfirmEmailAsync(`0,System.String)">
            <summary>
            Validates that an email confirmation token matches the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user to validate the token against.</param>
            <param name="token">The email confirmation token to validate.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.IsEmailConfirmedAsync(`0)">
            <summary>
            Gets a flag indicating whether the email address for the specified <paramref name="user"/> has been verified, true if the email address is verified otherwise
            false.
            </summary>
            <param name="user">The user whose email confirmation status should be returned.</param>
            <returns>
            The task object containing the results of the asynchronous operation, a flag indicating whether the email address for the specified <paramref name="user"/>
            has been confirmed or not.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GenerateChangeEmailTokenAsync(`0,System.String)">
            <summary>
            Generates an email change token for the specified user.
            </summary>
            <param name="user">The user to generate an email change token for.</param>
            <param name="newEmail">The new email address.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, an email change token.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.ChangeEmailAsync(`0,System.String,System.String)">
            <summary>
            Updates a users emails if the specified email change <paramref name="token"/> is valid for the user.
            </summary>
            <param name="user">The user whose email should be updated.</param>
            <param name="newEmail">The new email address.</param>
            <param name="token">The change email token to be verified.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetPhoneNumberAsync(`0)">
            <summary>
            Gets the telephone number, if any, for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose telephone number should be retrieved.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the user's telephone number, if any.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.SetPhoneNumberAsync(`0,System.String)">
            <summary>
            Sets the phone number for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose phone number to set.</param>
            <param name="phoneNumber">The phone number to set.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.ChangePhoneNumberAsync(`0,System.String,System.String)">
            <summary>
            Sets the phone number for the specified <paramref name="user"/> if the specified
            change <paramref name="token"/> is valid.
            </summary>
            <param name="user">The user whose phone number to set.</param>
            <param name="phoneNumber">The phone number to set.</param>
            <param name="token">The phone number confirmation token to validate.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/>
            of the operation.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.IsPhoneNumberConfirmedAsync(`0)">
            <summary>
            Gets a flag indicating whether the specified <paramref name="user"/>'s telephone number has been confirmed.
            </summary>
            <param name="user">The user to return a flag for, indicating whether their telephone number is confirmed.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, returning true if the specified <paramref name="user"/> has a confirmed
            telephone number otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GenerateChangePhoneNumberTokenAsync(`0,System.String)">
            <summary>
            Generates a telephone number change token for the specified user.
            </summary>
            <param name="user">The user to generate a telephone number token for.</param>
            <param name="phoneNumber">The new phone number the validation token should be sent to.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the telephone change number token.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.VerifyChangePhoneNumberTokenAsync(`0,System.String,System.String)">
            <summary>
            Returns a flag indicating whether the specified <paramref name="user"/>'s phone number change verification
            token is valid for the given <paramref name="phoneNumber"/>.
            </summary>
            <param name="user">The user to validate the token against.</param>
            <param name="token">The telephone number change token to validate.</param>
            <param name="phoneNumber">The telephone number the token was generated for.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, returning true if the <paramref name="token"/>
            is valid, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.VerifyUserTokenAsync(`0,System.String,System.String,System.String)">
            <summary>
            Returns a flag indicating whether the specified <paramref name="token"/> is valid for
            the given <paramref name="user"/> and <paramref name="purpose"/>.
            </summary>
            <param name="user">The user to validate the token against.</param>
            <param name="tokenProvider">The token provider used to generate the token.</param>
            <param name="purpose">The purpose the token should be generated for.</param>
            <param name="token">The token to validate</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, returning true if the <paramref name="token"/>
            is valid, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GenerateUserTokenAsync(`0,System.String,System.String)">
            <summary>
            Generates a token for the given <paramref name="user"/> and <paramref name="purpose"/>.
            </summary>
            <param name="purpose">The purpose the token will be for.</param>
            <param name="user">The user the token will be for.</param>
            <param name="tokenProvider">The provider which will generate the token.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents result of the asynchronous operation, a token for
            the given user and purpose.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.RegisterTokenProvider(System.String,Microsoft.AspNetCore.Identity.IUserTwoFactorTokenProvider{`0})">
            <summary>
            Registers a token provider.
            </summary>
            <param name="providerName">The name of the provider to register.</param>
            <param name="provider">The provider to register.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetValidTwoFactorProvidersAsync(`0)">
            <summary>
            Gets a list of valid two factor token providers for the specified <paramref name="user"/>,
            as an asynchronous operation.
            </summary>
            <param name="user">The user the whose two factor authentication providers will be returned.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents result of the asynchronous operation, a list of two
            factor authentication providers for the specified user.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.VerifyTwoFactorTokenAsync(`0,System.String,System.String)">
            <summary>
            Verifies the specified two factor authentication <paramref name="token" /> against the <paramref name="user"/>.
            </summary>
            <param name="user">The user the token is supposed to be for.</param>
            <param name="tokenProvider">The provider which will verify the token.</param>
            <param name="token">The token to verify.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents result of the asynchronous operation, true if the token is valid,
            otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GenerateTwoFactorTokenAsync(`0,System.String)">
            <summary>
            Gets a two factor authentication token for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user the token is for.</param>
            <param name="tokenProvider">The provider which will generate the token.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents result of the asynchronous operation, a two factor authentication token
            for the user.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetTwoFactorEnabledAsync(`0)">
            <summary>
            Returns a flag indicating whether the specified <paramref name="user"/> has two factor authentication enabled or not,
            as an asynchronous operation.
            </summary>
            <param name="user">The user whose two factor authentication enabled status should be retrieved.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, true if the specified <paramref name="user "/>
            has two factor authentication enabled, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.SetTwoFactorEnabledAsync(`0,System.Boolean)">
            <summary>
            Sets a flag indicating whether the specified <paramref name="user"/> has two factor authentication enabled or not,
            as an asynchronous operation.
            </summary>
            <param name="user">The user whose two factor authentication enabled status should be set.</param>
            <param name="enabled">A flag indicating whether the specified <paramref name="user"/> has two factor authentication enabled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the operation
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.IsLockedOutAsync(`0)">
            <summary>
            Returns a flag indicating whether the specified <paramref name="user"/> his locked out,
            as an asynchronous operation.
            </summary>
            <param name="user">The user whose locked out status should be retrieved.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, true if the specified <paramref name="user "/>
            is locked out, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.SetLockoutEnabledAsync(`0,System.Boolean)">
            <summary>
            Sets a flag indicating whether the specified <paramref name="user"/> is locked out,
            as an asynchronous operation.
            </summary>
            <param name="user">The user whose locked out status should be set.</param>
            <param name="enabled">Flag indicating whether the user is locked out or not.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the operation
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetLockoutEnabledAsync(`0)">
            <summary>
            Retrieves a flag indicating whether user lockout can be enabled for the specified user.
            </summary>
            <param name="user">The user whose ability to be locked out should be returned.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, true if a user can be locked out, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetLockoutEndDateAsync(`0)">
            <summary>
            Gets the last <see cref="T:System.DateTimeOffset"/> a user's last lockout expired, if any.
            A time value in the past indicates a user is not currently locked out.
            </summary>
            <param name="user">The user whose lockout date should be retrieved.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the lookup, a <see cref="T:System.DateTimeOffset"/> containing the last time a user's lockout expired, if any.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.SetLockoutEndDateAsync(`0,System.Nullable{System.DateTimeOffset})">
            <summary>
            Locks out a user until the specified end date has passed. Setting a end date in the past immediately unlocks a user.
            </summary>
            <param name="user">The user whose lockout date should be set.</param>
            <param name="lockoutEnd">The <see cref="T:System.DateTimeOffset"/> after which the <paramref name="user"/>'s lockout should end.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.AccessFailedAsync(`0)">
            <summary>
            Increments the access failed count for the user as an asynchronous operation.
            If the failed access account is greater than or equal to the configured maximum number of attempts,
            the user will be locked out for the configured lockout time span.
            </summary>
            <param name="user">The user whose failed access count to increment.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.ResetAccessFailedCountAsync(`0)">
            <summary>
            Resets the access failed count for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose failed access count should be reset.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetAccessFailedCountAsync(`0)">
            <summary>
            Retrieves the current number of failed accesses for the given <paramref name="user"/>.
            </summary>
            <param name="user">The user whose access failed count should be retrieved for.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that contains the result the asynchronous operation, the current failed access count
            for the user.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetUsersForClaimAsync(System.Security.Claims.Claim)">
            <summary>
            Returns a list of users from the user store who have the specified <paramref name="claim"/>.
            </summary>
            <param name="claim">The claim to look for.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the result of the asynchronous query, a list of <typeparamref name="TUser"/>s who
            have the specified claim.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetUsersInRoleAsync(System.String)">
            <summary>
            Returns a list of users from the user store who are members of the specified <paramref name="roleName"/>.
            </summary>
            <param name="roleName">The name of the role whose users should be returned.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the result of the asynchronous query, a list of <typeparamref name="TUser"/>s who
            are members of the specified role.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetAuthenticationTokenAsync(`0,System.String,System.String)">
            <summary>
            Returns an authentication token for a user.
            </summary>
            <param name="user"></param>
            <param name="loginProvider">The authentication scheme for the provider the token is associated with.</param>
            <param name="tokenName">The name of the token.</param>
            <returns>The authentication token for a user</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.SetAuthenticationTokenAsync(`0,System.String,System.String,System.String)">
            <summary>
            Sets an authentication token for a user.
            </summary>
            <param name="user"></param>
            <param name="loginProvider">The authentication scheme for the provider the token is associated with.</param>
            <param name="tokenName">The name of the token.</param>
            <param name="tokenValue">The value of the token.</param>
            <returns>Whether the user was successfully updated.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.RemoveAuthenticationTokenAsync(`0,System.String,System.String)">
            <summary>
            Remove an authentication token for a user.
            </summary>
            <param name="user"></param>
            <param name="loginProvider">The authentication scheme for the provider the token is associated with.</param>
            <param name="tokenName">The name of the token.</param>
            <returns>Whether a token was removed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetAuthenticatorKeyAsync(`0)">
            <summary>
            Returns the authenticator key for the user.
            </summary>
            <param name="user">The user.</param>
            <returns>The authenticator key</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.ResetAuthenticatorKeyAsync(`0)">
            <summary>
            Resets the authenticator key for the user.
            </summary>
            <param name="user">The user.</param>
            <returns>Whether the user was successfully updated.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GenerateNewAuthenticatorKey">
            <summary>
            Generates a new base32 encoded 160-bit security secret (size of SHA1 hash).
            </summary>
            <returns>The new security secret.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GenerateNewTwoFactorRecoveryCodesAsync(`0,System.Int32)">
            <summary>
            Generates recovery codes for the user, this invalidates any previous recovery codes for the user.
            </summary>
            <param name="user">The user to generate recovery codes for.</param>
            <param name="number">The number of codes to generate.</param>
            <returns>The new recovery codes for the user.  Note: there may be less than number returned, as duplicates will be removed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.CreateTwoFactorRecoveryCode">
            <summary>
            Generate a new recovery code.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.RedeemTwoFactorRecoveryCodeAsync(`0,System.String)">
            <summary>
            Returns whether a recovery code is valid for a user. Note: recovery codes are only valid
            once, and will be invalid after use.
            </summary>
            <param name="user">The user who owns the recovery code.</param>
            <param name="code">The recovery code to use.</param>
            <returns>True if the recovery code was found for the user.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.CountRecoveryCodesAsync(`0)">
            <summary>
            Returns how many recovery code are still valid for a user.
            </summary>
            <param name="user">The user.</param>
            <returns>How many recovery code are still valid for a user.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the role manager and optionally releases the managed resources.
            </summary>
            <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.CreateSecurityTokenAsync(`0)">
            <summary>
            Creates bytes to use as a security token from the user's security stamp.
            </summary>
            <param name="user">The user.</param>
            <returns>The security token bytes.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.UpdatePasswordHash(`0,System.String,System.Boolean)">
            <summary>
            Updates a user's password hash.
            </summary>
            <param name="user">The user.</param>
            <param name="newPassword">The new password.</param>
            <param name="validatePassword">Whether to validate the password.</param>
            <returns>Whether the password has was successfully updated.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.GetChangeEmailTokenPurpose(System.String)">
            <summary>
            Generates the token purpose used to change email.
            </summary>
            <param name="newEmail">The new email address.</param>
            <returns>The token purpose.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.ValidateUserAsync(`0)">
            <summary>
            Should return <see cref="P:Microsoft.AspNetCore.Identity.IdentityResult.Success"/> if validation is successful. This is
            called before saving the user via Create or Update.
            </summary>
            <param name="user">The user</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> representing whether validation was successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.ValidatePasswordAsync(`0,System.String)">
            <summary>
            Should return <see cref="P:Microsoft.AspNetCore.Identity.IdentityResult.Success"/> if validation is successful. This is
            called before updating the password hash.
            </summary>
            <param name="user">The user.</param>
            <param name="password">The password.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> representing whether validation was successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.UpdateUserAsync(`0)">
            <summary>
            Called to update the user after validating and updating the normalized email/user name.
            </summary>
            <param name="user">The user.</param>
            <returns>Whether the operation was successful.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserManager`1.ThrowIfDisposed">
            <summary>
            Throws if this class has been disposed.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.UserOptions">
            <summary>
            Options for user validation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserOptions.AllowedUserNameCharacters">
            <summary>
            Gets or sets the list of allowed characters in the username used to validate user names. Defaults to abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+
            </summary>
            <value>
            The list of allowed characters in the username used to validate user names.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserOptions.RequireUniqueEmail">
            <summary>
            Gets or sets a flag indicating whether the application requires unique emails for its users. Defaults to false.
            </summary>
            <value>
            True if the application requires each user to have their own, unique email, otherwise false.
            </value>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.UserValidator`1">
            <summary>
            Provides validation services for user classes.
            </summary>
            <typeparam name="TUser">The type encapsulating a user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserValidator`1.#ctor(Microsoft.AspNetCore.Identity.IdentityErrorDescriber)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Identity.UserValidator`1"/>/
            </summary>
            <param name="errors">The <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> used to provider error messages.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserValidator`1.Describer">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> used to provider error messages for the current <see cref="T:Microsoft.AspNetCore.Identity.UserValidator`1"/>.
            </summary>
            <value>The <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> used to provider error messages for the current <see cref="T:Microsoft.AspNetCore.Identity.UserValidator`1"/>.</value>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserValidator`1.ValidateAsync(Microsoft.AspNetCore.Identity.UserManager{`0},`0)">
            <summary>
            Validates the specified <paramref name="user"/> as an asynchronous operation.
            </summary>
            <param name="manager">The <see cref="T:Microsoft.AspNetCore.Identity.UserManager`1"/> that can be used to retrieve user properties.</param>
            <param name="user">The user to validate.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the validation operation.</returns>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.IdentityServiceCollectionExtensions">
            <summary>
            Contains extension methods to <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> for configuring identity services.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.IdentityServiceCollectionExtensions.AddIdentityCore``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds and configures the identity system for the specified User type. Role services are not added 
            by default but can be added with <see cref="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddRoles``1"/>.
            </summary>
            <typeparam name="TUser">The type representing a User in the system.</typeparam>
            <param name="services">The services available in the application.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> for creating and configuring the identity system.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.IdentityServiceCollectionExtensions.AddIdentityCore``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Microsoft.AspNetCore.Identity.IdentityOptions})">
            <summary>
            Adds and configures the identity system for the specified User type. Role services are not added by default 
            but can be added with <see cref="M:Microsoft.AspNetCore.Identity.IdentityBuilder.AddRoles``1"/>.
            </summary>
            <typeparam name="TUser">The type representing a User in the system.</typeparam>
            <param name="services">The services available in the application.</param>
            <param name="setupAction">An action to configure the <see cref="T:Microsoft.AspNetCore.Identity.IdentityOptions"/>.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Identity.IdentityBuilder"/> for creating and configuring the identity system.</returns>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.ConcurrencyFailure">
            <summary>Optimistic concurrency failure, object has been modified.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.DefaultError">
            <summary>An unknown failure has occurred.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.DuplicateEmail">
            <summary>Email '{0}' is already taken.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Identity.Core.Resources.FormatDuplicateEmail(System.Object)">
            <summary>Email '{0}' is already taken.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.DuplicateRoleName">
            <summary>Role name '{0}' is already taken.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Identity.Core.Resources.FormatDuplicateRoleName(System.Object)">
            <summary>Role name '{0}' is already taken.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.DuplicateUserName">
            <summary>User name '{0}' is already taken.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Identity.Core.Resources.FormatDuplicateUserName(System.Object)">
            <summary>User name '{0}' is already taken.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.InvalidEmail">
            <summary>Email '{0}' is invalid.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Identity.Core.Resources.FormatInvalidEmail(System.Object)">
            <summary>Email '{0}' is invalid.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.InvalidManagerType">
            <summary>Type {0} must derive from {1}&lt;{2}&gt;.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Identity.Core.Resources.FormatInvalidManagerType(System.Object,System.Object,System.Object)">
            <summary>Type {0} must derive from {1}&lt;{2}&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.InvalidPasswordHasherCompatibilityMode">
            <summary>The provided PasswordHasherCompatibilityMode is invalid.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.InvalidPasswordHasherIterationCount">
            <summary>The iteration count must be a positive integer.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.InvalidRoleName">
            <summary>Role name '{0}' is invalid.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Identity.Core.Resources.FormatInvalidRoleName(System.Object)">
            <summary>Role name '{0}' is invalid.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.InvalidToken">
            <summary>Invalid token.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.InvalidUserName">
            <summary>User name '{0}' is invalid, can only contain letters or digits.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Identity.Core.Resources.FormatInvalidUserName(System.Object)">
            <summary>User name '{0}' is invalid, can only contain letters or digits.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.LoginAlreadyAssociated">
            <summary>A user with this login already exists.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.MustCallAddIdentity">
            <summary>AddIdentity must be called on the service collection.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.NoTokenProvider">
            <summary>No IUserTwoFactorTokenProvider&lt;{0}&gt; named '{1}' is registered.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Identity.Core.Resources.FormatNoTokenProvider(System.Object,System.Object)">
            <summary>No IUserTwoFactorTokenProvider&lt;{0}&gt; named '{1}' is registered.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.NullSecurityStamp">
            <summary>User security stamp cannot be null.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.PasswordMismatch">
            <summary>Incorrect password.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.PasswordRequiresDigit">
            <summary>Passwords must have at least one digit ('0'-'9').</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.PasswordRequiresLower">
            <summary>Passwords must have at least one lowercase ('a'-'z').</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.PasswordRequiresNonAlphanumeric">
            <summary>Passwords must have at least one non alphanumeric character.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.PasswordRequiresUpper">
            <summary>Passwords must have at least one uppercase ('A'-'Z').</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.PasswordTooShort">
            <summary>Passwords must be at least {0} characters.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Identity.Core.Resources.FormatPasswordTooShort(System.Object)">
            <summary>Passwords must be at least {0} characters.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.RoleNotFound">
            <summary>Role {0} does not exist.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Identity.Core.Resources.FormatRoleNotFound(System.Object)">
            <summary>Role {0} does not exist.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIQueryableRoleStore">
            <summary>Store does not implement IQueryableRoleStore&lt;TRole&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIQueryableUserStore">
            <summary>Store does not implement IQueryableUserStore&lt;TUser&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIRoleClaimStore">
            <summary>Store does not implement IRoleClaimStore&lt;TRole&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIUserAuthenticationTokenStore">
            <summary>Store does not implement IUserAuthenticationTokenStore&lt;User&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIUserClaimStore">
            <summary>Store does not implement IUserClaimStore&lt;TUser&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIUserConfirmationStore">
            <summary>Store does not implement IUserConfirmationStore&lt;TUser&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIUserEmailStore">
            <summary>Store does not implement IUserEmailStore&lt;TUser&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIUserLockoutStore">
            <summary>Store does not implement IUserLockoutStore&lt;TUser&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIUserLoginStore">
            <summary>Store does not implement IUserLoginStore&lt;TUser&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIUserPasswordStore">
            <summary>Store does not implement IUserPasswordStore&lt;TUser&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIUserPhoneNumberStore">
            <summary>Store does not implement IUserPhoneNumberStore&lt;TUser&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIUserRoleStore">
            <summary>Store does not implement IUserRoleStore&lt;TUser&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIUserSecurityStampStore">
            <summary>Store does not implement IUserSecurityStampStore&lt;TUser&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIUserAuthenticatorKeyStore">
            <summary>Store does not implement IUserAuthenticatorKeyStore&lt;User&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIUserTwoFactorStore">
            <summary>Store does not implement IUserTwoFactorStore&lt;TUser&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.RecoveryCodeRedemptionFailed">
            <summary>Recovery code redemption failed.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.UserAlreadyHasPassword">
            <summary>User already has a password set.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.UserAlreadyInRole">
            <summary>User already in role '{0}'.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Identity.Core.Resources.FormatUserAlreadyInRole(System.Object)">
            <summary>User already in role '{0}'.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.UserLockedOut">
            <summary>User is locked out.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.UserLockoutNotEnabled">
            <summary>Lockout is not enabled for this user.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.UserNameNotFound">
            <summary>User {0} does not exist.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Identity.Core.Resources.FormatUserNameNotFound(System.Object)">
            <summary>User {0} does not exist.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.UserNotInRole">
            <summary>User is not in role '{0}'.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Identity.Core.Resources.FormatUserNotInRole(System.Object)">
            <summary>User is not in role '{0}'.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIUserTwoFactorRecoveryCodeStore">
            <summary>Store does not implement IUserTwoFactorRecoveryCodeStore&lt;User&gt;.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.PasswordRequiresUniqueChars">
            <summary>Passwords must use at least {0} different characters.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Identity.Core.Resources.FormatPasswordRequiresUniqueChars(System.Object)">
            <summary>Passwords must use at least {0} different characters.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.NoRoleType">
            <summary>No RoleType was specified, try AddRoles&lt;TRole&gt;().</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.StoreNotIProtectedUserStore">
            <summary>Store does not implement IProtectedUserStore&lt;TUser&gt; which is required when ProtectPersonalData = true.</summary>
        </member>
        <member name="P:Microsoft.Extensions.Identity.Core.Resources.NoPersonalDataProtector">
            <summary>No IPersonalDataProtector service was registered, this is required when ProtectPersonalData = true.</summary>
        </member>
        <member name="T:System.Security.Claims.PrincipalExtensions">
            <summary>
            Claims related extensions for <see cref="T:System.Security.Claims.ClaimsPrincipal"/>.
            </summary>
        </member>
        <member name="M:System.Security.Claims.PrincipalExtensions.FindFirstValue(System.Security.Claims.ClaimsPrincipal,System.String)">
            <summary>
            Returns the value for the first claim of the specified type otherwise null the claim is not present.
            </summary>
            <param name="principal">The <see cref="T:System.Security.Claims.ClaimsPrincipal"/> instance this method extends.</param>
            <param name="claimType">The claim type whose first value should be returned.</param>
            <returns>The value of the first instance of the specified claim type, or null if the claim is not present.</returns>
        </member>
    </members>
</doc>
