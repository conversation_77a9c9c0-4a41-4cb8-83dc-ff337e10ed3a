<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Components.Forms</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Components.Forms.DataAnnotationsValidator">
            <summary>
            Adds Data Annotations validation support to an <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.DataAnnotationsValidator.OnInitialized">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Forms.EditContext">
            <summary>
            Holds metadata related to a data editing process, such as flags to indicate which
            fields have been modified and the current set of validation messages.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.EditContext.#ctor(System.Object)">
            <summary>
            Constructs an instance of <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/>.
            </summary>
            <param name="model">The model object for the <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/>. This object should hold the data being edited, for example as a set of properties.</param>
        </member>
        <member name="E:Microsoft.AspNetCore.Components.Forms.EditContext.OnFieldChanged">
            <summary>
            An event that is raised when a field value changes.
            </summary>
        </member>
        <member name="E:Microsoft.AspNetCore.Components.Forms.EditContext.OnValidationRequested">
            <summary>
            An event that is raised when validation is requested.
            </summary>
        </member>
        <member name="E:Microsoft.AspNetCore.Components.Forms.EditContext.OnValidationStateChanged">
            <summary>
            An event that is raised when validation state has changed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.EditContext.Field(System.String)">
            <summary>
            Supplies a <see cref="T:Microsoft.AspNetCore.Components.Forms.FieldIdentifier"/> corresponding to a specified field name
            on this <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/>'s <see cref="P:Microsoft.AspNetCore.Components.Forms.EditContext.Model"/>.
            </summary>
            <param name="fieldName">The name of the editable field.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Components.Forms.FieldIdentifier"/> corresponding to a specified field name on this <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/>'s <see cref="P:Microsoft.AspNetCore.Components.Forms.EditContext.Model"/>.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.Forms.EditContext.Model">
            <summary>
            Gets the model object for this <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.EditContext.NotifyFieldChanged(Microsoft.AspNetCore.Components.Forms.FieldIdentifier@)">
            <summary>
            Signals that the value for the specified field has changed.
            </summary>
            <param name="fieldIdentifier">Identifies the field whose value has been changed.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.EditContext.NotifyValidationStateChanged">
            <summary>
            Signals that some aspect of validation state has changed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.EditContext.MarkAsUnmodified(Microsoft.AspNetCore.Components.Forms.FieldIdentifier@)">
            <summary>
            Clears any modification flag that may be tracked for the specified field.
            </summary>
            <param name="fieldIdentifier">Identifies the field whose modification flag (if any) should be cleared.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.EditContext.MarkAsUnmodified">
            <summary>
            Clears all modification flags within this <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.EditContext.IsModified">
            <summary>
            Determines whether any of the fields in this <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/> have been modified.
            </summary>
            <returns>True if any of the fields in this <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/> have been modified; otherwise false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.EditContext.GetValidationMessages">
             <summary>
             Gets the current validation messages across all fields.
            
             This method does not perform validation itself. It only returns messages determined by previous validation actions.
             </summary>
             <returns>The current validation messages.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.EditContext.GetValidationMessages(Microsoft.AspNetCore.Components.Forms.FieldIdentifier)">
             <summary>
             Gets the current validation messages for the specified field.
            
             This method does not perform validation itself. It only returns messages determined by previous validation actions.
             </summary>
             <param name="fieldIdentifier">Identifies the field whose current validation messages should be returned.</param>
             <returns>The current validation messages for the specified field.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.EditContext.GetValidationMessages(System.Linq.Expressions.Expression{System.Func{System.Object}})">
             <summary>
             Gets the current validation messages for the specified field.
            
             This method does not perform validation itself. It only returns messages determined by previous validation actions.
             </summary>
             <param name="accessor">Identifies the field whose current validation messages should be returned.</param>
             <returns>The current validation messages for the specified field.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.EditContext.IsModified(Microsoft.AspNetCore.Components.Forms.FieldIdentifier@)">
            <summary>
            Determines whether the specified fields in this <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/> has been modified.
            </summary>
            <returns>True if the field has been modified; otherwise false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.EditContext.IsModified(System.Linq.Expressions.Expression{System.Func{System.Object}})">
            <summary>
            Determines whether the specified fields in this <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/> has been modified.
            </summary>
            <param name="accessor">Identifies the field whose current validation messages should be returned.</param>
            <returns>True if the field has been modified; otherwise false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.EditContext.Validate">
            <summary>
            Validates this <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/>.
            </summary>
            <returns>True if there are no validation messages after validation; otherwise false.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Forms.EditContextDataAnnotationsExtensions">
            <summary>
            Extension methods to add DataAnnotations validation to an <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.EditContextDataAnnotationsExtensions.AddDataAnnotationsValidation(Microsoft.AspNetCore.Components.Forms.EditContext)">
            <summary>
            Adds DataAnnotations validation support to the <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/>.
            </summary>
            <param name="editContext">The <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Forms.FieldChangedEventArgs">
            <summary>
            Provides information about the <see cref="E:Microsoft.AspNetCore.Components.Forms.EditContext.OnFieldChanged"/> event.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.FieldChangedEventArgs.#ctor(Microsoft.AspNetCore.Components.Forms.FieldIdentifier@)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Components.Forms.FieldChangedEventArgs"/>.
            </summary>
            <param name="fieldIdentifier">The <see cref="T:Microsoft.AspNetCore.Components.Forms.FieldIdentifier"/></param>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.Forms.FieldChangedEventArgs.FieldIdentifier">
            <summary>
            Identifies the field whose value has changed.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Forms.FieldIdentifier">
            <summary>
            Uniquely identifies a single field that can be edited. This may correspond to a property on a
            model object, or can be any other named value.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.FieldIdentifier.Create``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Components.Forms.FieldIdentifier"/> structure.
            </summary>
            <param name="accessor">An expression that identifies an object member.</param>
            <typeparam name="TField">The field <see cref="T:System.Type"/>.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.FieldIdentifier.#ctor(System.Object,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Components.Forms.FieldIdentifier"/> structure.
            </summary>
            <param name="model">The object that owns the field.</param>
            <param name="fieldName">The name of the editable field.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.Forms.FieldIdentifier.Model">
            <summary>
            Gets the object that owns the editable field.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.Forms.FieldIdentifier.FieldName">
            <summary>
            Gets the name of the editable field.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.FieldIdentifier.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.FieldIdentifier.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.FieldIdentifier.Equals(Microsoft.AspNetCore.Components.Forms.FieldIdentifier)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore">
            <summary>
            Holds validation messages for an <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore.#ctor(Microsoft.AspNetCore.Components.Forms.EditContext)">
            <summary>
            Creates an instance of <see cref="T:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore"/>.
            </summary>
            <param name="editContext">The <see cref="T:Microsoft.AspNetCore.Components.Forms.EditContext"/> with which this store should be associated.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore.Add(Microsoft.AspNetCore.Components.Forms.FieldIdentifier@,System.String)">
            <summary>
            Adds a validation message for the specified field.
            </summary>
            <param name="fieldIdentifier">The identifier for the field.</param>
            <param name="message">The validation message.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore.Add(System.Linq.Expressions.Expression{System.Func{System.Object}},System.String)">
            <summary>
            Adds a validation message for the specified field.
            </summary>
            <param name="accessor">Identifies the field for which to add the message.</param>
            <param name="message">The validation message.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore.Add(Microsoft.AspNetCore.Components.Forms.FieldIdentifier@,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds the messages from the specified collection for the specified field.
            </summary>
            <param name="fieldIdentifier">The identifier for the field.</param>
            <param name="messages">The validation messages to be added.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore.Add(System.Linq.Expressions.Expression{System.Func{System.Object}},System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds the messages from the specified collection for the specified field.
            </summary>
            <param name="accessor">Identifies the field for which to add the messages.</param>
            <param name="messages">The validation messages to be added.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore.Item(Microsoft.AspNetCore.Components.Forms.FieldIdentifier)">
             <summary>
             Gets the validation messages within this <see cref="T:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore"/> for the specified field.
            
             To get the validation messages across all validation message stores, use <see cref="M:Microsoft.AspNetCore.Components.Forms.EditContext.GetValidationMessages(Microsoft.AspNetCore.Components.Forms.FieldIdentifier)"/> instead
             </summary>
             <param name="fieldIdentifier">The identifier for the field.</param>
             <returns>The validation messages for the specified field within this <see cref="T:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore"/>.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore.Item(System.Linq.Expressions.Expression{System.Func{System.Object}})">
             <summary>
             Gets the validation messages within this <see cref="T:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore"/> for the specified field.
            
             To get the validation messages across all validation message stores, use <see cref="M:Microsoft.AspNetCore.Components.Forms.EditContext.GetValidationMessages(Microsoft.AspNetCore.Components.Forms.FieldIdentifier)"/> instead
             </summary>
             <param name="accessor">The identifier for the field.</param>
             <returns>The validation messages for the specified field within this <see cref="T:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore.Clear">
            <summary>
            Removes all messages within this <see cref="T:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore.Clear(System.Linq.Expressions.Expression{System.Func{System.Object}})">
            <summary>
            Removes all messages within this <see cref="T:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore"/> for the specified field.
            </summary>
            <param name="accessor">Identifies the field for which to remove the messages.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore.Clear(Microsoft.AspNetCore.Components.Forms.FieldIdentifier@)">
            <summary>
            Removes all messages within this <see cref="T:Microsoft.AspNetCore.Components.Forms.ValidationMessageStore"/> for the specified field.
            </summary>
            <param name="fieldIdentifier">The identifier for the field.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Forms.ValidationRequestedEventArgs">
            <summary>
            Provides information about the <see cref="E:Microsoft.AspNetCore.Components.Forms.EditContext.OnValidationRequested"/> event.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.Forms.ValidationRequestedEventArgs.Empty">
            <summary>
            Gets a shared empty instance of <see cref="T:Microsoft.AspNetCore.Components.Forms.ValidationRequestedEventArgs"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.ValidationRequestedEventArgs.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Components.Forms.ValidationRequestedEventArgs"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Components.Forms.ValidationStateChangedEventArgs">
            <summary>
            Provides information about the <see cref="E:Microsoft.AspNetCore.Components.Forms.EditContext.OnValidationStateChanged"/> event.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Components.Forms.ValidationStateChangedEventArgs.Empty">
            <summary>
            Gets a shared empty instance of <see cref="T:Microsoft.AspNetCore.Components.Forms.ValidationStateChangedEventArgs"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Components.Forms.ValidationStateChangedEventArgs.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Components.Forms.ValidationStateChangedEventArgs" />
            </summary>
        </member>
    </members>
</doc>
