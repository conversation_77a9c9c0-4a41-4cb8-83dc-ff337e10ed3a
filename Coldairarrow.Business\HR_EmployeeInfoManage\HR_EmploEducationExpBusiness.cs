﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HR_EmployeeInfoManage
{
    public class HR_EmploEducationExpBusiness : BaseBusiness<HR_EmploEducationExp>, IHR_EmploEducationExpBusiness, ITransientDependency
    {
        readonly IMapper _mapper;
        public HR_EmploEducationExpBusiness(IDbAccessor db, IMapper mapper)
            : base(db)
        {
            _mapper = mapper;
        }

        #region 外部接口

        public async Task<PageResult<HR_EmploEducationExpDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_EmploEducationExp>();
            var search = input.Search;

            //筛选
            if (!search.F_Id.IsNullOrEmpty())
            {
                q = q.Where(i => i.UserId.Contains(search.F_Id));
            }
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_EmploEducationExp, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).ProjectTo<HR_EmploEducationExpDTO>(_mapper.ConfigurationProvider).GetPageResultAsync(input);
        }
        public async Task<List<HR_EmploEducationExpDTO>> GetDataListByUserIdAsync(string userId)
        {
            return await GetIQueryable().Where(a => a.UserId == userId).ProjectTo<HR_EmploEducationExpDTO>(_mapper.ConfigurationProvider).ToListAsync();
        }

        public async Task<HR_EmploEducationExp> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_EmploEducationExp data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_EmploEducationExp data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public int AddData(HR_EmploEducationExp data)
        {
            return Insert(data);
        }

        public int UpdateData(HR_EmploEducationExp data)
        {
            return Update(data);
        }

        public int UpdateListData(List<HR_EmploEducationExp> data)
        {
            return Update(data);
        }

        public int DeleteData(HR_EmploEducationExp data)
        {
            return Delete(data);
        }

        #endregion

        #region 私有成员

        #endregion
    }
}