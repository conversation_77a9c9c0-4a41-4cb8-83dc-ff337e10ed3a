﻿using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using System.Net.Http;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Business.HolidayManage;
using Coldairarrow.Entity.HolidayManage;
using Microsoft.Extensions.Configuration;
using System.Text;
using Microsoft.Extensions.Caching.Distributed;
using QRCoder;
using System.Drawing.Imaging;

namespace Coldairarrow.Api.Controllers.Base_Manage
{
    [Route("/Base_Manage/[controller]/[action]")]
    public class Base_WechatUserController : BaseApiController
    {
        #region DI

        public Base_WechatUserController(IBase_WechatUserBusiness base_WechatUserBus, IBase_UserBusiness base_UserBusiness, IHR_FormalEmployeesBusiness hR_FormalEmployeesBusiness,
            IHR_HolidayLineBusiness hR_HolidayLineBus, IHR_AskLeaveBusiness hR_AskLeaveBus, IHR_TermLeaveBusiness hR_TermLeaveBus, IConfiguration configuration, IDistributedCache cache)
        {
            _base_WechatUserBus = base_WechatUserBus;
            _base_UserBusiness = base_UserBusiness;
            _hR_FormalEmployeesBusiness = hR_FormalEmployeesBusiness;
            _hR_HolidayLineBus = hR_HolidayLineBus;
            _hR_AskLeaveBus = hR_AskLeaveBus;
            _hR_TermLeaveBus = hR_TermLeaveBus;
            _configuration = configuration;
            _cache = cache;
        }
        readonly IConfiguration _configuration;
        IBase_WechatUserBusiness _base_WechatUserBus { get; }
        IBase_UserBusiness _base_UserBusiness { get; }

        IHR_FormalEmployeesBusiness _hR_FormalEmployeesBusiness { get; }

        IHR_HolidayLineBusiness _hR_HolidayLineBus { get; }

        IHR_AskLeaveBusiness _hR_AskLeaveBus { get; }

        IHR_TermLeaveBusiness _hR_TermLeaveBus { get; }
        private readonly IDistributedCache _cache;
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Base_WechatUser>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _base_WechatUserBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Base_WechatUser> GetTheData(IdInputDTO input)
        {
            return await _base_WechatUserBus.GetTheDataAsync(input.id);
        }

        [HttpGet("getWxQrCode")]
        public async Task<IActionResult> GetWxQrCode()
        {
            var qrCodeUrl = GenerateQrCodeUrl();
            var qrCodeInfo = new QRCodeInfo { QRCodeUrl = qrCodeUrl, Ticket = Guid.NewGuid().ToString(), Status = "pending" };
            await SaveQRCodeToRedis(qrCodeInfo);
            return Ok(new { qrCodeUrl = qrCodeInfo.QRCodeUrl });
        }

        private string GenerateQrCodeUrl()
        {
            var url = "https://login.work.weixin.qq.com/wwlogin/sso/login/?login_type=CorpApp&appid=wwe9e87379e762b7fa&agentid=1000076&redirect_uri=https%3A%2F%2Fcms.cqlandmark.com&state=test";
            var qrGenerator = new QRCodeGenerator();
            var qrCodeData = qrGenerator.CreateQrCode(url, QRCodeGenerator.ECCLevel.Q);
            var qrCode = new QRCode(qrCodeData);
            using (var bitmap = qrCode.GetGraphic(20))
            {
                using (var ms = new MemoryStream())
                {
                    bitmap.Save(ms, ImageFormat.Png);
                    return $"data:image/png;base64,{Convert.ToBase64String(ms.ToArray())}";
                }
            }
        }

        private async Task SaveQRCodeToRedis(QRCodeInfo qrCodeInfo)
        {
            var options = new DistributedCacheEntryOptions()
                .SetAbsoluteExpiration(TimeSpan.FromMinutes(10)); // 设置过期时间为10分钟
            await _cache.SetStringAsync(qrCodeInfo.Ticket, JsonConvert.SerializeObject(qrCodeInfo), options);
        }

        private async Task<QRCodeInfo> GetQRCodeFromRedis(string ticket)
        {
            var qrCodeJson = await _cache.GetStringAsync(ticket);
            if (qrCodeJson == null)
            {
                return null;
            }
            return JsonConvert.DeserializeObject<QRCodeInfo>(qrCodeJson);
        }

        public class QRCodeInfo
        {
            public string QRCodeUrl { get; set; }
            public string Ticket { get; set; }
            public string Status { get; set; }
            public string UserId { get; set; }
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Base_WechatUser data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _base_WechatUserBus.AddDataAsync(data);
            }
            else
            {
                await _base_WechatUserBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _base_WechatUserBus.DeleteDataAsync(ids);
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _base_WechatUserBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Base_WechatUser>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _base_WechatUserBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion

        #region 二次开发
        //private readonly string _agentId = "1000002";
        //private readonly string _secret = "hBZmFpBViWShm7nmRXEPokPM4wdtdi_9vmJXaylKHWA";
        //private readonly string _corpId = "wwe9e87379e762b7fa";
        ///// <summary>
        ///// 授权地址
        ///// </summary>
        //private readonly string _auth3url = "https://open.weixin.qq.com/connect/oauth3/authorize";
        ///// <summary>
        ///// 授权回调地址
        ///// </summary>
        //private readonly string _callbackurl = "http://******.zicp.vip/auth3callback/api/Auth3/Callback";
        ///// <summary>
        ///// 获取access_token地址
        ///// </summary>
        //private readonly string _gettokenurl = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";
        ///// <summary>
        ///// 获取访问用户身份地址
        ///// </summary>
        //private readonly string _getuserurl = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo";
        //[HttpGet]
        //public IActionResult Auth3(string redirecturi)
        //{            //string strurl = $"{_auth3url}?" +
        //    //    $"&appid={_corpId}" +
        //    //    $"&redirect_uri={System.Web.HttpUtility.UrlEncode(_callbackurl)}" +
        //    //    $"&response_type=code" +
        //    //    $"&scope={_secret}" +
        //    //    $"&agentid={_agentId}" +
        //    //    $"&state={System.Web.HttpUtility.UrlEncode(redirecturi)}#wechat_redirect";
        //    string strurl = "";
        //    return Redirect(strurl);
        //}

        //[HttpGet("Callback")]
        //public async Task<IActionResult> Callback(string code, string state)
        //{
        //    /**
        //     1）code只能消费一次，不能重复消费。比如说，是否存在多个服务器同时消费同一code情况。
        //     2）code需要在有效期间消费（5分钟），过期会自动失效。
        //     */
        //    string access_token = await GetAccessToken();
        //    string url = $"{_getuserurl}?access_token={access_token}&code={code}";
        //    HttpResponseMessage response = await  _clientFactory.CreateClient().GetAsync(url);
        //    if (response.StatusCode == System.Net.HttpStatusCode.OK)
        //    {
        //        using (var responseStream = await response.Content.ReadAsStreamAsync())
        //        {
        //            var userinfo = JsonConvert.DeserializeObject<dynamic>(new StreamReader(responseStream).ReadToEnd());
        //            int errcode = userinfo.errcode;
        //            if (errcode == 0)
        //            {
        //                //企业成员
        //                string UserId = userinfo.UserId;
        //                //外部成员
        //                string OpenId = userinfo.OpenId;
        //                /**
        //                 userid是系统生成的可以修改一次;
        //                 所以后面的业务逻辑如果遇到错误就要重新授权一下;   
        //                 */
        //                if (UserId == null)
        //                {
        //                    _memoryCache.Set<string>("UserId", OpenId);
        //                }
        //                else
        //                {
        //                    _memoryCache.Set<string>("UserId", UserId);
        //                }
        //            }
        //            else
        //            {
        //                _logger.LogError($"getuserinfo请求错误:{userinfo.errmsg}");
        //                return Ok();
        //            }
        //        }
        //    }
        //    return Redirect($"{System.Web.HttpUtility.UrlDecode(state)}?UserId={_memoryCache.Get<string>("UserId")}");
        //}
        //public async Task<string> GetAccessToken()
        //{
        //    //判断缓存里面是否又AccessToken
        //    var cacheStr = "AccessToken_" + _corpId + _secret;
        //    var cacheData=RedisHelper.Get("cacheStr");
        //    if (!string.IsNullOrWhiteSpace(cacheData))
        //    {
        //        string url = $"{_gettokenurl}?corpid={_corpId}&corpsecret={_secret}";
        //        HttpResponseMessage response =  HttpHelper.GetData(url);
        //        if (response.StatusCode == System.Net.HttpStatusCode.OK)
        //        {
        //            using (var responseStream = await response.Content.ReadAsStreamAsync())
        //            {
        //                var access_token_result = JsonConvert.DeserializeObject<dynamic>(new StreamReader(responseStream).ReadToEnd());
        //                int errcode = access_token_result.errcode;
        //                if (errcode == 0)
        //                {
        //                    string access_token = access_token_result.access_token;
        //                    int expires_in = access_token_result.expires_in;
        //                    _memoryCache.Set<string>("AccessToken", access_token, DateTimeOffset.Now.AddSeconds(expires_in - 10));
        //                }
        //                else
        //                {
        //                    _logger.LogError($"access_token请求错误:{access_token_result.errmsg }");
        //                }
        //            }

        //        }
        //    }
        //    return _memoryCache.Get<string>("AccessToken");
        //}



        //获取openId
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetOpenid()
        {
            try
            {
                string code = HttpContext.Request.Form["code"].ToString();
                if (code.IsNullOrEmpty())
                {
                    return Error("微信服务器波动");
                }
                string appid = "wxf192b3de950c0cfe";
                string secret = "f54208b749b027f3b50f79b6bd62b3ab";
                string grant_type = "authorization_code";
                using (var httpClient = new HttpClient())
                {
                    //post
                    var url = new System.Uri("https://api.weixin.qq.com/sns/jscode2session");
                    var body = new FormUrlEncodedContent(new Dictionary<string, string>
                {
                    { "appid", appid},
                    { "secret", secret },
                    { "js_code", code},
                    { "grant_type",grant_type}

                });
                    // response
                    var response = httpClient.PostAsync(url, body).Result;
                    var data = response.Content.ReadAsStringAsync().Result;
                    JObject jo = (JObject)JsonConvert.DeserializeObject(data);
                    var pm = new
                    {
                        openid = jo["openid"].ToString(),
                        session_key = jo["session_key"].ToString(),
                        unionid = jo["unionid"].ToString()
                    };
                    return Success(pm);
                }
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }

        }

        //获取openId(新食堂)
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetNewOpenid()
        {
            try
            {
                string code = HttpContext.Request.Form["code"].ToString();
                if (code.IsNullOrEmpty())
                {
                    return Error("微信服务器波动");
                }
                string appid = "wxb180436240b9b626";
                string secret = "a82075fb4fbed96d7fd65f3a122258d5";
                string grant_type = "authorization_code";
                using (var httpClient = new HttpClient())
                {
                    //post
                    var url = new System.Uri("https://api.weixin.qq.com/sns/jscode2session");
                    var body = new FormUrlEncodedContent(new Dictionary<string, string>
                {
                    { "appid", appid},
                    { "secret", secret },
                    { "js_code", code},
                    { "grant_type",grant_type}

                });
                    // response
                    var response = httpClient.PostAsync(url, body).Result;
                    var data = response.Content.ReadAsStringAsync().Result;
                    JObject jo = (JObject)JsonConvert.DeserializeObject(data);
                    var pm = new
                    {
                        openid = jo["openid"].ToString(),
                        session_key = jo["session_key"].ToString(),
                        unionid = jo["unionid"].ToString()
                    };
                    return Success(pm);
                }
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }

        }

        //根据openId查询用户信息
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetUserInfoById()
        {
            try
            {
                string id = HttpContext.Request.Form["openId"].ToString();
                string unionId = HttpContext.Request.Form["unionId"].ToString();
                var user = _base_WechatUserBus.GetTheDataAsync(id).Result;
                if (user != null)
                {
                    user.W_LastLogin = DateTime.Now;
                    //查询正式员工表
                    var formalUser = _hR_FormalEmployeesBusiness.GetDataByUserId(user.HR_UserId);
                    if (!formalUser.IsNullOrEmpty())
                    {
                        user.HR_DeptId = formalUser.F_DepartmentId;
                        user.HR_FormalId = formalUser.F_Id;
                        user.DepartmentName = formalUser.DepartmentName;
                    }
                    user.W_IsRegister = 1;
                    _base_WechatUserBus.UpdateDataAsync(user).Wait();
                    //生成token,有效期一天
                    JWTPayload jWTPayload = new JWTPayload
                    {
                        UserId = user.HR_UserId,
                        RealName = user.W_Realname,
                        ADName = user.W_ADNumber,
                        Expire = DateTime.Now.AddDays(1)
                    };
                    try
                    {
                        user.Mobile = !user.MobileSecurity.IsNullOrEmpty() ? AESHelper.DecryptString(user.MobileSecurity, AESHelper.AesKey) : user.Mobile;
                    }
                    catch (Exception ex)
                    {
                      
                    }
                 
                    user.Token = JWTHelper.GetToken(jWTPayload.ToJson(), JWTHelper.JWTSecret);
                    return Success(user);
                }
                else
                {
                    var newUser = new Base_WechatUser();
                    newUser.Id = id;
                    newUser.UnionId = unionId;
                    newUser.W_IsRegister = 0;
                    newUser.W_UserType = 0;
                    newUser.W_FirstLogin = DateTime.Now;
                    newUser.W_LastLogin = DateTime.Now;
                    newUser.F_CreateDate = DateTime.Now;
                    _base_WechatUserBus.AddDataAsync(newUser).Wait();
                    //生成token,有效期一天
                    //JWTPayload jWTPayload = new JWTPayload
                    //{
                    //    UserId = user.HR_UserId,
                    //    RealName = user.W_Realname,
                    //    ADName = user.W_NickName,
                    //    Expire = DateTime.Now.AddDays(1)
                    //};
                    //user.Token = JWTHelper.GetToken(jWTPayload.ToJson(), JWTHelper.JWTSecret);
                    return Success(newUser);
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        //通过手机号识别用户信息
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult UpdataUserPhone()
        {
            try
            {
                string id = HttpContext.Request.Form["id"].ToString();
                string mobile = HttpContext.Request.Form["mobile"].ToString();
                var user = _base_WechatUserBus.GetTheDataAsync(id).Result;
                if (user != null)
                {
                    //同时判断身份,
                    var companyUser = _base_UserBusiness.GetTheDataByPhone(mobile);
                    if (companyUser.IsNullOrEmpty())
                    {
                        //为空则赋值手机
                        user.Mobile = "************";
                        user.MobileSecurity = AESHelper.EncryptString(mobile, AESHelper.AesKey);
                        _base_WechatUserBus.UpdateDataAsync(user).Wait();
                        return Success("未查询到该手机号");
                    }
                    //是公司员工
                    user.W_Realname = companyUser.RealName;
                    if (!mobile.IsNullOrEmpty())
                    {
                        user.MobileSecurity = AESHelper.EncryptString(mobile, AESHelper.AesKey);
                        user.Mobile = "************";
                    }
                    user.W_Email = companyUser.UserName;
                    user.HR_UserId = companyUser.Id;
                    user.W_IsRegister = 1;
                    user.W_ADNumber = !companyUser.UserName.IsNullOrEmpty() ? companyUser.UserName?.Split("@")[0] : "";
                    //查询正式员工表
                    var formalUser = _hR_FormalEmployeesBusiness.GetDataByUserId(companyUser.Id);
                    if (!formalUser.IsNullOrEmpty())
                    {
                        user.HR_DeptId = formalUser.F_DepartmentId;
                        user.HR_FormalId = formalUser.F_Id;
                    }
                    _base_WechatUserBus.UpdateDataAsync(user).Wait();
                    return Success("身份认证成功");
                }
                else
                {
                    return Error("小程序用户获取失败，请重启小程序");
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        //根据openId更新用户信息
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult UpdataUserInfo()
        {
            try
            {
                string id = HttpContext.Request.Form["id"].ToString();
                string nickName = HttpContext.Request.Form["nickName"].ToString();
                string language = HttpContext.Request.Form["language"].ToString();
                string city = HttpContext.Request.Form["city"].ToString();
                string province = HttpContext.Request.Form["province"].ToString();
                string country = HttpContext.Request.Form["country"].ToString();
                string avatarUrl = HttpContext.Request.Form["avatarUrl"].ToString();
                int gender = HttpContext.Request.Form["gender"].ToString().ToInt();
                var user = _base_WechatUserBus.GetTheDataAsync(id).Result;
                if (user != null)
                {
                    user.W_NickName = nickName;
                    user.W_Language = language;
                    user.W_City = city;
                    user.W_Province = province;
                    user.W_Country = country;
                    user.W_Gender = gender;
                    user.W_Icon = avatarUrl;
                    _base_WechatUserBus.UpdateDataAsync(user).Wait();
                    return Success(user);
                }
                else
                {
                    return Error("无此用户");
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        /// <summary>
        /// 获取法定年假剩余天数
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetHoliday()
        {
            try
            {
                string id = HttpContext.Request.Form["id"].ToString();
                var data = _base_WechatUserBus.GetAnnualLeaveSYInfo(id);
                data.xjDay = _hR_TermLeaveBus.GetCount(id);
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        //

        /// <summary>
        /// 员工查询请假列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetEmpDataList(PageInput<ConditionDTO> input)
        {
            string id = HttpContext.Request.Form["id"].ToString();
            var list = _hR_AskLeaveBus.GetEmpDataListAsync(input, id).Result;
            return Success(list);
        }

        /// <summary>
        /// 提交请假
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public AjaxResult CommitAskLeave(HR_AskLeave data)
        {
            var ret = _hR_AskLeaveBus.ActWorkflow(data, _configuration["OAUrl"] + "rest/archives/", GetOperator());
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("提交流程失败");
            }
        }
        #endregion
    }
}