﻿using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.HR_EmployeeInfoManage
{
    [Route("/HR_EmployeeInfoManage/[controller]/[action]")]
    public class HR_EmploEducationExpController : BaseApiController
    {
        #region DI

        public HR_EmploEducationExpController(IHR_EmploEducationExpBusiness hR_EmploEducationExpBus)
        {
            _hR_EmploEducationExpBus = hR_EmploEducationExpBus;
        }

        IHR_EmploEducationExpBusiness _hR_EmploEducationExpBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_EmploEducationExpDTO>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_EmploEducationExpBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_EmploEducationExp> GetTheData(IdInputDTO input)
        {
            return await _hR_EmploEducationExpBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_EmploEducationExp data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);
                data.F_BusState = (int)ASKBusState.正常;
                await _hR_EmploEducationExpBus.AddDataAsync(data);
            }
            else
            {
                await _hR_EmploEducationExpBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_EmploEducationExpBus.DeleteDataAsync(ids);
        }

        #endregion
    }
}