﻿using Coldairarrow.Entity.Wechat_Shekou;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Util.DataAccess;

namespace Coldairarrow.Business.Wechat_Shekou
{
    public class ERP_BaoRecordBusiness : BaseBusiness<ERP_BaoRecord>, IERP_BaoRecordBusiness, ITransientDependency
    {
        public ERP_BaoRecordBusiness(IBusinessDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<ERP_BaoRecord>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<ERP_BaoRecord>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<ERP_BaoRecord, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<ERP_BaoRecord> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(ERP_BaoRecord data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(ERP_BaoRecord data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<ERP_BaoRecord>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<ERP_BaoRecord, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<ERP_BaoRecord, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        public ERP_BaoRecord chaDataByMoney(string index, decimal money)
        {
            var data = GetIQueryable().Where(x => x.ContractNumber == index && x.PayMoney == money).FirstOrDefault();
            return data;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}