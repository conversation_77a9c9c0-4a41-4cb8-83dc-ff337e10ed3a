﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Coldairarrow.Entity.HR_EmployeeInfoManage
{
    /// <summary>
    /// HR_ADAssociative
    /// </summary>
    [Table("HR_ADAssociative")]
    public class HR_ADAssociative
    {

        /// <summary>
        /// F_Id
        /// </summary>
        [Key, Column(Order = 1)]
        public String F_Id { get; set; }

        /// <summary>
        /// F_AD
        /// </summary>
        public String F_AD { get; set; }

        /// <summary>
        /// F_Name
        /// </summary>
        public String F_Name { get; set; }

        /// <summary>
        /// F_IDCard
        /// </summary>
        public String F_IDCard { get; set; }

    }
}