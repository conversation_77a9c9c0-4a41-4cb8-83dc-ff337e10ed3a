﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using System.Linq.Expressions;
using System;
using Coldairarrow.IBusiness;
using Coldairarrow.Entity;
using Coldairarrow.Entity.Base_Manage;
using System.IO;
using System.Drawing;
using Coldairarrow.Entity.HR_Manage.Extensions;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_InterviewRecordBusiness : BaseBusiness<HR_InterviewRecord>, IHR_InterviewRecordBusiness, ITransientDependency
    {
        IOperator _operator;
        public HR_InterviewRecordBusiness(IDbAccessor db, IOperator @operator)
            : base(db)
        {
            _operator = @operator;
        }
        #region 外部接口

        public async Task<PageResult<HR_InterviewRecordDTO>> GetInterviewRecordListAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_InterviewRecord, HR_Entry, HR_InterviewRecordDTO>> select = (i, e) => new HR_InterviewRecordDTO
            {
                Name = e.NameUser,
                Sex = e.Sex,
                CompanyName = e.F_CompanyName
            };
            select = select.BuildExtendSelectExpre();
            var q = from i in this.Db.GetIQueryable<HR_InterviewRecord>().AsExpandable()
                    join e in this.Db.GetIQueryable<HR_Entry>() on i.F_UserId equals e.F_Id into entry
                    from e in entry.DefaultIfEmpty()
                    select @select.Invoke(i, e);
            var where = LinqHelper.True<HR_InterviewRecordDTO>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_InterviewRecordDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }
        public async Task<PageResult<HR_InterviewRecord>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_InterviewRecord>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_InterviewRecord, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }
        [Transactional]
        /// <summary>
        /// 保存获取数据
        /// </summary>
        /// <param name="data"></param> 
        [DataAddLog(UserLogType.招聘管理, "UserName", "在线应聘者")]
        public void SaveFormData(HR_InterviewRecordFromDTO data, IOperator op)
        {
            try
            {

                ///员工入职表
                var hR_Entry = data.hR_Entry;
                //面试流程记录
                var interviewEntity = data.interviewEntity;
                //资格证书
                var recruitCertificates = data.hR_RecruitCertificates;
                //教育经历
                var hR_RecruitEduBacks = data.hR_RecruitEduBacks;
                //工作经历
                var recruitWorkExpes = data.hR_RecruitWorkExpes;

                if (hR_Entry.F_Id.IsNullOrEmpty())
                {
                    //新增
                    InitEntity(hR_Entry, _operator);
                    hR_Entry.F_BusState = (int)InductionBusState.未入职;
                    this.Db.Insert(hR_Entry);
                }
                else
                {
                    //编辑
                    UpdateEntity(hR_Entry, _operator);
                    this.Db.Update(hR_Entry);
                    //删除其他数据
                    var inid = this.GetIQueryable().Where(i => i.F_UserId == hR_Entry.F_Id).FirstOrDefault()?.F_Id;
                    if (!inid.IsNullOrEmpty())
                    {
                        this.Delete(inid);
                    }
                    var redids = this.Db.GetIQueryable<HR_RecruitCertificate>()
                                .Where(i => i.F_UserId == hR_Entry.F_Id)
                                .Select(i => i.F_Id)?.ToList();
                    this.Db.Delete(redids);
                    var enddids = this.Db.GetIQueryable<HR_RecruitEduBack>()
                                  .Where(i => i.UserId == hR_Entry.F_Id)
                                  .Select(i => i.F_Id)?.ToList();
                    this.Db.Delete(enddids);
                    var redrids = this.Db.GetIQueryable<HR_RecruitWorkExpe>()
                                   .Where(i => i.F_UserId == hR_Entry.F_Id)
                                   .Select(i => i.F_Id)?.ToList();
                    this.Db.Delete(redrids);
                }


                if (interviewEntity != null)
                {
                    interviewEntity.F_UserId = hR_Entry.F_Id;
                    InitEntity(interviewEntity, _operator);
                    interviewEntity.F_BusState = (int)ASKBusState.正常;
                    this.Db.Insert(interviewEntity);
                }

                if (recruitCertificates.Count() > 0)
                {
                    recruitCertificates.ForEach(item => InitEntity(item, _operator));
                    recruitCertificates.ForEach(item => { item.F_UserId = hR_Entry.F_Id; item.F_BusState = (int)ASKBusState.正常; });
                    this.Db.Insert(recruitCertificates);
                }

                if (hR_RecruitEduBacks.Count() > 0)
                {
                    hR_RecruitEduBacks.ForEach(item => InitEntity(item, _operator));
                    hR_RecruitEduBacks.ForEach(item => { item.UserId = hR_Entry.F_Id; item.F_BusState = (int)ASKBusState.正常; });
                    this.Db.Insert(hR_RecruitEduBacks);
                }
                if (recruitWorkExpes.Count() > 0)
                {
                    recruitWorkExpes.ForEach(item => InitEntity(item, _operator));
                    recruitWorkExpes.ForEach(item => { item.F_UserId = hR_Entry.F_Id; item.F_BusState = (int)ASKBusState.正常; });
                    this.Db.Insert(recruitWorkExpes);
                }
                //岗位
                if (!string.IsNullOrEmpty(data.hR_Entry.F_PositionId))
                {
                    var entity = Db.GetIQueryable<HR_Recruit>().Where(i => i.F_Role == data.hR_Entry.F_PositionId).FirstOrDefault();
                    if (entity == null)
                    {
                        var postModel = Db.GetIQueryable<Base_Post>().Where(i => i.F_Id == data.hR_Entry.F_PositionId).FirstOrDefault();
                        HR_Recruit hR_Recruit = new HR_Recruit()
                        {
                            F_Id = Guid.NewGuid().ToString("N"),
                            F_CreateUserId = op.UserId,
                            F_CreateUserName = op.RealName,
                            F_Content = "",
                            F_City = "",
                            F_Address = "",
                            F_CreateDate = DateTime.Now,
                            F_Detail = "",
                            F_Experience = "",
                            F_Role = data.hR_Entry.F_PositionId,
                            F_RecruitName = postModel.F_Name,
                            F_Education = postModel.F_Education,
                            F_Achieve = postModel.F_Qualifications,
                            F_BusState = 1,//招聘需求
                        };
                        this.Db.Insert(hR_Recruit);
                        entity = hR_Recruit;
                    }
                    HR_RecruitmentCandidates hR_RecruitmentCandidates = new HR_RecruitmentCandidates()
                    {
                        F_BusState = 0,
                        F_CreateDate = DateTime.Now,
                        F_Id = Guid.NewGuid().ToString("N"),
                        F_RecruitId = entity.F_Id,
                        F_UserId = data.hR_Entry.F_Id,
                        F_CreateUserId = op.UserId,
                        F_CreateUserName = op.RealName,
                        F_IsInvitedInt = 0,
                        F_Through = 0,
                    };
                    this.Db.Insert(hR_RecruitmentCandidates);
                }
                //公司
                if (!string.IsNullOrEmpty(data.hR_Entry.F_CompanyId))
                {
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<HR_InterviewRecord> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        public async Task<HR_InterviewRecordFromDTO> GetFormTheDataAsnc(string id)
        {
            HR_InterviewRecordFromDTO hR_InterviewRecordFromDTO = new HR_InterviewRecordFromDTO();
            hR_InterviewRecordFromDTO.interviewEntity = await GetEntityAsync(id);
            var Entity = hR_InterviewRecordFromDTO.interviewEntity;
            if (Entity != null)
            {
                hR_InterviewRecordFromDTO.hR_Entry = this.Db.GetIQueryable<HR_Entry>().Where(i => i.F_Id == Entity.F_UserId).Select(p => new HR_Entry()
                {
                    F_Id = p.F_Id,
                    F_CreateDate = p.F_CreateDate,
                    F_CreateUserId = p.F_CreateUserId,
                    F_CreateUserName = p.F_CreateUserName,
                    F_ModifyDate = p.F_ModifyDate,
                    F_ModifyUserId = p.F_ModifyUserId,
                    F_ModifyUserName = p.F_ModifyUserName,
                    F_WFId = p.F_WFId,
                    F_BusState = p.F_BusState,
                    F_WFState = p.F_WFState,
                    Remark = p.Remark,
                    NameUser = p.NameUser,
                    HeadPortrait = p.HeadPortrait,
                    EmployeesCode = p.EmployeesCode,
                    IdCardNumber = p.IdCardNumber,
                    PassportNo = p.PassportNo,
                    DirthDate = p.DirthDate,
                    EmployRelStatus = p.EmployRelStatus,
                    Sex = p.Sex,
                    IdCardAddress = p.IdCardAddress,
                    ND = p.ND,
                    ND2 = p.ND2,
                    ND3 = p.ND3,
                    ND4 = p.ND4,
                    ProfessionalCategory = p.ProfessionalCategory,
                    Nationality = p.Nationality,
                    NativePlace = p.NativePlace,
                    NationalInfo = p.NationalInfo,
                    AccountType = p.AccountType,
                    RegisteredResidence = p.RegisteredResidence,
                    MaritalStatus = p.MaritalStatus,
                    PoliticalLandscape = p.PoliticalLandscape,
                    FertilityStatus = p.FertilityStatus,
                    IsWhetherCar = p.IsWhetherCar,
                    LicenseInfo = p.LicenseInfo,
                    CurrentBankCard = p.CurrentBankCard,
                    OldBankCard = p.OldBankCard,
                    EffectiveDate = p.EffectiveDate,
                    MobilePhone = p.MobilePhone,
                    Email = p.Email,
                    OfficePhone = p.OfficePhone,
                    HomePhone = p.HomePhone,
                    HomeAddress = p.HomeAddress,
                    EmergencyContact = p.EmergencyContact,
                    EmergencyContactNumber = p.EmergencyContactNumber,
                    BaseUserId = p.BaseUserId,
                    F_PositionId = p.F_PositionId,
                    F_DepartmentId = p.F_DepartmentId,
                    F_Rank = p.F_Rank,
                    F_CompanyId = p.F_CompanyId,
                    F_RecordFormalschooling = p.F_RecordFormalschooling,
                    F_Stature = p.F_Stature,
                    F_BloodType = p.F_BloodType,
                    F_HealthCondition = p.F_HealthCondition,
                    F_ForeignLevel = p.F_ForeignLevel,
                    F_ProfessionalQualification = p.F_ProfessionalQualification,
                    EmergencyContact2 = p.EmergencyContact2,
                    EmergencyContactNumber2 = p.EmergencyContactNumber2,
                    F_CompanyName = p.F_CompanyName,
                    F_WeChatUserId = p.F_WeChatUserId,
                    F_Introduce = p.F_Introduce,
                    F_FolderId = p.F_FolderId,
                    F_Interview = p.F_Interview,
                    F_Sources = p.F_Sources,
                    F_SourcesPersonnel = p.F_SourcesPersonnel,
                    F_InterestsHobbies = p.F_InterestsHobbies,
                    F_Weight = p.F_Weight,
                    F_FileId = p.F_FileId,
                    F_FileBase64Img = !string.IsNullOrEmpty(p.HeadPortrait) ? "" : p.F_FileBase64Img,
                    F_IsFileModel = p.F_IsFileModel
                }).FirstOrDefault();

                if (hR_InterviewRecordFromDTO.hR_Entry != null)
                {
                    //如果头像为空
                    if (string.IsNullOrEmpty(hR_InterviewRecordFromDTO.hR_Entry.HeadPortrait))
                    {
                        //则保存并重新赋值
                        hR_InterviewRecordFromDTO.hR_Entry.HeadPortrait = SaveBase64Image(hR_InterviewRecordFromDTO.hR_Entry.F_FileBase64Img, hR_InterviewRecordFromDTO.hR_Entry.F_Id);
                        Db.Update<HR_Entry>(u => u.F_Id == hR_InterviewRecordFromDTO.hR_Entry.F_Id, u => u.HeadPortrait = hR_InterviewRecordFromDTO.hR_Entry.HeadPortrait);
                    }
                }
                hR_InterviewRecordFromDTO.hR_Entry.DecryptFormal();
                hR_InterviewRecordFromDTO.hR_RecruitEduBacks = await this.Db.GetIQueryable<HR_RecruitEduBack>().Where(i => i.UserId == Entity.F_UserId).ToListAsync();
                hR_InterviewRecordFromDTO.hR_RecruitWorkExpes = await this.Db.GetIQueryable<HR_RecruitWorkExpe>().Where(i => i.F_UserId == Entity.F_UserId).ToListAsync();
                hR_InterviewRecordFromDTO.hR_RecruitCertificates = await this.Db.GetIQueryable<HR_RecruitCertificate>().Where(i => i.F_UserId == Entity.F_UserId).ToListAsync();
            }
            return hR_InterviewRecordFromDTO;
        }

        public async Task<HR_InterviewRecordFromDTO> GetKeyTheDataAsync(InterviewDTO interviewDTO)
        {
            HR_InterviewRecordFromDTO hR_InterviewRecordFromDTO = new HR_InterviewRecordFromDTO();
            //hR_InterviewRecordFromDTO.interviewEntity = new InterviewEntity();
            //hR_InterviewRecordFromDTO.hR_Entry = new HR_Entry();
            ////通过id查询
            //if (interviewDTO.type == 1)
            //{
            //    hR_InterviewRecordFromDTO.interviewEntity = await GetEntityAsync(interviewDTO.KeyWord);
            //}
            //if (interviewDTO.type == 2)
            //{
            //    hR_InterviewRecordFromDTO.hR_Entry=this.
            //}
            return hR_InterviewRecordFromDTO;


        }
        public async Task<HR_InterviewRecordFromDTO> GetKeyTheDataAsync(string name, string card)
        {
            HR_InterviewRecordFromDTO hR_InterviewRecordFromDTO = new HR_InterviewRecordFromDTO();
            hR_InterviewRecordFromDTO.hR_Entry = this.Db.GetIQueryable<HR_Entry>()
                                                .Where(i => i.NameUser == name && i.IdCardNumber == card)
                                                .FirstOrDefault();
            var Entity = hR_InterviewRecordFromDTO.hR_Entry;
            if (Entity != null)
            {
                hR_InterviewRecordFromDTO.interviewEntity = this.GetIQueryable().Where(i => i.F_UserId == Entity.F_Id).FirstOrDefault();
                hR_InterviewRecordFromDTO.hR_RecruitEduBacks = await this.Db.GetIQueryable<HR_RecruitEduBack>().Where(i => i.UserId == Entity.F_Id).ToListAsync();
                hR_InterviewRecordFromDTO.hR_RecruitWorkExpes = await this.Db.GetIQueryable<HR_RecruitWorkExpe>().Where(i => i.F_UserId == Entity.F_Id).ToListAsync();
                hR_InterviewRecordFromDTO.hR_RecruitCertificates = await this.Db.GetIQueryable<HR_RecruitCertificate>().Where(i => i.F_UserId == Entity.F_Id).ToListAsync();
            }
            return hR_InterviewRecordFromDTO;

        }
        public async Task AddDataAsync(HR_InterviewRecord data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_InterviewRecord data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_InterviewRecord>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_InterviewRecord, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_InterviewRecord, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        public int AddData(HR_InterviewRecord data)
        {
            return Insert(data);
        }

        public int UpdateData(HR_InterviewRecord data)
        {
            return Update(data);
        }

        public int DeleteData(HR_InterviewRecord data)
        {
            return Delete(data);
        }

        /// <summary>
        /// 将base64编码的字符串转为图片并保存
        /// </summary>
        /// <param name="source"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        protected string SaveBase64Image(string source, string id)
        {
            try
            {
                var now = DateTime.Now;
                string filePath = Directory.GetCurrentDirectory() + "/wwwroot/UploadFile/" + DateTime.Now.ToString("yyyyMMdd") + "/";
                string fileName = id + ".png";

                string strbase64 = source.Substring(source.IndexOf(',') + 1);
                strbase64 = strbase64.Trim('\0');
                //Log.Debug("strbase64:" + strbase64);
                byte[] arr = Convert.FromBase64String(strbase64);
                using (MemoryStream ms = new MemoryStream(arr))
                {
                    Bitmap bmp = new Bitmap(ms);
                    if (!Directory.Exists(filePath))
                    {
                        Directory.CreateDirectory(filePath);
                    }
                    //if (!Directory.Exists(filePath))
                    //    Log.Debug("没有Directory");
                    //Directory.CreateDirectory(filePath);
                    //新建第二个bitmap类型的bmp2变量。
                    Bitmap bmp2 = new Bitmap(bmp, bmp.Width, bmp.Height);
                    //将第一个bmp拷贝到bmp2中
                    Graphics draw = Graphics.FromImage(bmp2);
                    draw.DrawImage(bmp, 0, 0);
                    draw.Dispose();

                    bmp2.Save(filePath + fileName, System.Drawing.Imaging.ImageFormat.Png);

                    ms.Close();
                    return "https://hrapi.cqlandmark.com/UploadFile/" + DateTime.Now.ToString("yyyyMMdd") + "/" + fileName;
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        #endregion

        #region 私有成员

        #endregion
    }
}