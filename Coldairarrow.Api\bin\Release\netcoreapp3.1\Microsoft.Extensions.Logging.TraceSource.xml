<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Logging.TraceSource</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Logging.TraceSourceFactoryExtensions">
            <summary>
            Extension methods for setting up <see cref="T:Microsoft.Extensions.Logging.TraceSource.TraceSourceLoggerProvider"/> on a <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.TraceSourceFactoryExtensions.AddTraceSource(Microsoft.Extensions.Logging.ILoggingBuilder,System.String)">
            <summary>
            Adds a TraceSource logger named 'TraceSource' to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to use.</param>
            <param name="switchName">The name of the <see cref="T:System.Diagnostics.SourceSwitch"/> to use.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.TraceSourceFactoryExtensions.AddTraceSource(Microsoft.Extensions.Logging.ILoggingBuilder,System.String,System.Diagnostics.TraceListener)">
            <summary>
            Adds a TraceSource logger named 'TraceSource' to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to use.</param>
            <param name="switchName">The name of the <see cref="T:System.Diagnostics.SourceSwitch"/> to use.</param>
            <param name="listener">The <see cref="T:System.Diagnostics.TraceListener"/> to use.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.TraceSourceFactoryExtensions.AddTraceSource(Microsoft.Extensions.Logging.ILoggingBuilder,System.Diagnostics.SourceSwitch)">
            <summary>
            Adds a TraceSource logger named 'TraceSource' to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to use.</param>
            <param name="sourceSwitch">The <see cref="T:System.Diagnostics.SourceSwitch"/> to use.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.TraceSourceFactoryExtensions.AddTraceSource(Microsoft.Extensions.Logging.ILoggingBuilder,System.Diagnostics.SourceSwitch,System.Diagnostics.TraceListener)">
            <summary>
            Adds a TraceSource logger named 'TraceSource' to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFactory"/> to use.</param>
            <param name="sourceSwitch">The <see cref="T:System.Diagnostics.SourceSwitch"/> to use.</param>
            <param name="listener">The <see cref="T:System.Diagnostics.TraceListener"/> to use.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Logging.TraceSource.TraceSourceLoggerProvider">
            <summary>
            Provides an ILoggerFactory based on System.Diagnostics.TraceSource.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.TraceSource.TraceSourceLoggerProvider.#ctor(System.Diagnostics.SourceSwitch)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Extensions.Logging.TraceSource.TraceSourceLoggerProvider"/> class.
            </summary>
            <param name="rootSourceSwitch">The <see cref="T:System.Diagnostics.SourceSwitch"/> to use.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.TraceSource.TraceSourceLoggerProvider.#ctor(System.Diagnostics.SourceSwitch,System.Diagnostics.TraceListener)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Extensions.Logging.TraceSource.TraceSourceLoggerProvider"/> class.
            </summary>
            <param name="rootSourceSwitch">The <see cref="T:System.Diagnostics.SourceSwitch"/> to use.</param>
            <param name="rootTraceListener">The <see cref="T:System.Diagnostics.TraceListener"/> to use.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.TraceSource.TraceSourceLoggerProvider.CreateLogger(System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.ILogger"/>  for the given component name.
            </summary>
            <param name="name">The name of the <see cref="N:Microsoft.Extensions.Logging.TraceSource"/> to add.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.TraceSource.TraceSourceLogger"/> that was created.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.TraceSource.TraceSourceLoggerProvider.Dispose">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Logging.TraceSource.TraceSourceScope">
            <summary>
            Provides an IDisposable that represents a logical operation scope based on System.Diagnostics LogicalOperationStack
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.TraceSource.TraceSourceScope.#ctor(System.Object)">
            <summary>
            Pushes state onto the LogicalOperationStack by calling
            <see cref="M:System.Diagnostics.CorrelationManager.StartLogicalOperation(System.Object)"/>
            </summary>
            <param name="state">The state.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.TraceSource.TraceSourceScope.Dispose">
            <summary>
            Pops a state off the LogicalOperationStack by calling
            <see cref="M:System.Diagnostics.CorrelationManager.StopLogicalOperation"/>
            </summary>
        </member>
    </members>
</doc>
