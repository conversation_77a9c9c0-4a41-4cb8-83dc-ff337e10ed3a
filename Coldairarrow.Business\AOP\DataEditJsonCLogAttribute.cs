﻿using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System.Threading.Tasks;

namespace Coldairarrow.Business
{ 
    public class DataEditJsonCLogAttribute : WriteDataLogAttribute
    {
        public DataEditJsonCLogAttribute(UserLogType logType, string nameField, string dataName)
            : base(logType, nameField, dataName)
        {
        }

        public override async Task After(IAOPContext context)
        {
            var op = context.ServiceProvider.GetService<IOperator>();
            var obj = context.Arguments[0];
            var jsonStr = "";
            if (obj != null)
            {
                jsonStr = JsonConvert.SerializeObject(obj);
            }
            op.WriteUserLog(_logType, $"修改{_dataName}:{obj.GetPropertyValue(_nameField)?.ToString()}", jsonStr);

            await Task.CompletedTask;
        }
    }
}
