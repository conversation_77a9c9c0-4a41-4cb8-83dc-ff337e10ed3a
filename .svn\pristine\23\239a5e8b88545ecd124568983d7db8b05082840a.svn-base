﻿using Coldairarrow.Entity.Base_Business;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Business.Base_Business
{
    public interface ITencent_WeekRankBusiness
    {
        Task<PageResult<Tencent_WeekRank>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<Tencent_WeekRank> GetTheDataAsync(string id);
        Task AddDataAsync(Tencent_WeekRank data);
        Task UpdateDataAsync(Tencent_WeekRank data);
        Task DeleteDataAsync(List<string> ids);
    }
}