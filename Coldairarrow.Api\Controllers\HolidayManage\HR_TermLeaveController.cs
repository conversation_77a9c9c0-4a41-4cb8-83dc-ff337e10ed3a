﻿using Coldairarrow.Business.HolidayManage;
using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Coldairarrow.Entity;
using Microsoft.Extensions.Configuration;

namespace Coldairarrow.Api.Controllers.HolidayManage
{
    [Route("/HolidayManage/[controller]/[action]")]
    public class HR_TermLeaveController : BaseApiController
    {
        #region DI

        public HR_TermLeaveController(IHR_TermLeaveBusiness hR_TermLeaveBus, IConfiguration configuration)
        {
            _configuration = configuration;
            _hR_TermLeaveBus = hR_TermLeaveBus;
        }

        readonly IConfiguration _configuration;
        IHR_TermLeaveBusiness _hR_TermLeaveBus { get; }

        #endregion

        #region 获取

        /// <summary>
        /// 根据请假ID获得已完成的销假数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<List<HR_TermLeaveDTO>> GetListByAskId(ConditionDTO input)
        {
            if(input == null)
            {
                throw new BusException("参数错误");
            }
            return await _hR_TermLeaveBus.GetListByAskIdAsync(input);
        }
        [HttpPost]
        public async Task<PageResult<HR_TermLeaveDTO>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_TermLeaveBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_TermLeave> GetTheData(IdInputDTO input)
        {
            return await _hR_TermLeaveBus.GetTheDataAsync(input.id);
        }

        [HttpPost]
        public HR_TermLeaveDTO GetTermLeave(IdInputDTO input)
        {
            return _hR_TermLeaveBus.GetTermLeave(input.id);
        }
        #endregion

        #region 提交

        /// <summary>
        /// 流程回调
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult FlowCallBack(FlowInputDTO input)
        {
            _hR_TermLeaveBus.FlowCallBack(input);

            return Success();
        }
        /// <summary>
        /// 保存并创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult SaveAndCreateFlow(HR_TermLeave data)
        {
            var ret = _hR_TermLeaveBus.CreateFlow(data, _configuration["OAUrl"] + "rest/archives/", GetOperator());
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("创建流程失败");
            }
        }

        /// <summary>
        /// 保存并创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ActWorkflow(HR_TermLeave data)
        {
            var ret = _hR_TermLeaveBus.ActWorkflow(data, _configuration["OAUrl"] + "rest/archives/", GetOperator());
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("提交流程失败");
            }
        }
        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ArchiveWorkflow(HR_TermLeave data)
        {
            var ret = _hR_TermLeaveBus.ArchiveWorkflow(data, _configuration["OAUrl"] + "rest/archives/");
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("强制归档失败");
            }
        }

        [HttpPost]
        public async Task SaveData(HR_TermLeave data)
        {
            if (data.F_PeriodTime == 0)
            {
                data.F_RealStartTime = (data.F_RealStartTime.Value.ToString("yyyy-MM-dd") + " 09:00").ToDateTime();
            }
            else
            {
                data.F_RealStartTime = (data.F_RealStartTime.Value.ToString("yyyy-MM-dd") + " 13:00").ToDateTime();
            }
            if (data.F_PeriodTime2 == 0)
            {
                data.F_RealEndTime = (data.F_RealEndTime.Value.ToString("yyyy-MM-dd") + " 12:30").ToDateTime();
            }
            else
            {
                data.F_RealEndTime = (data.F_RealEndTime.Value.ToString("yyyy-MM-dd") + " 18:00").ToDateTime();
            }
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_TermLeaveBus.AddDataAsync(data);
            }
            else
            {
                await _hR_TermLeaveBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_TermLeaveBus.DeleteDataAsync(ids);
        }

        #endregion
        

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<AskLeaveConditionDTO> input)
        {
            try
            {

                //取出数据源
                DataTable exportTable = _hR_TermLeaveBus.GetExcelListAsync(input);
                if (exportTable != null && exportTable.Rows.Count > 0)
                {
                    exportTable.Columns.Add("wfstate");
                    exportTable.Columns.Add("starttime");
                    exportTable.Columns.Add("endtime");
                    for (int i = 0; i < exportTable.Rows.Count; i++)
                    {
                        var wfstate = exportTable.Rows[i]["F_WFState"];
                        if (wfstate != null && !wfstate.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["wfstate"] = Enum.Parse(typeof(WFStates), wfstate.ToString()).ToString();
                        }
                        var starttime = exportTable.Rows[i]["F_RealStartTime"];
                        if (starttime != null && !starttime.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["starttime"] = Convert.ToDateTime(starttime).ToString("yyyy-MM-dd HH:mm");
                        }
                        var endtime = exportTable.Rows[i]["F_RealEndTime"];
                        if (endtime != null && !endtime.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["endtime"] = Convert.ToDateTime(endtime).ToString("yyyy-MM-dd HH:mm");
                        }
                    }
                }
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "销假单";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                int sort = 0;
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_askleavecode", ExcelColumn = "请假编号", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "username", ExcelColumn = "员工名称", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createdate", ExcelColumn = "申请日期", Alignment = "left", Sort = sort++ });
                //excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "askleavetype", ExcelColumn = "假期类型", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "starttime", ExcelColumn = "实际请假开始时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "endtime", ExcelColumn = "实际请假结束时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_realaskleavetime", ExcelColumn = "实际请假长度", Alignment = "left", Sort = sort++ });
                //excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_unit", ExcelColumn = "单位", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "wfstate", ExcelColumn = "单据状态", Alignment = "left", Sort = sort++ });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<HR_TermLeave>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _hR_TermLeaveBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}