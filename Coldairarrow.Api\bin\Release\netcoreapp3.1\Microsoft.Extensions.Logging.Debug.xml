<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Logging.Debug</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Logging.Debug.DebugLogger">
            <summary>
            A logger that writes messages in the debug output window only when a debugger is attached.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.Debug.DebugLogger.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Extensions.Logging.Debug.DebugLogger"/> class.
            </summary>
            <param name="name">The name of the logger.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.Debug.DebugLogger.BeginScope``1(``0)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.Debug.DebugLogger.IsEnabled(Microsoft.Extensions.Logging.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.Debug.DebugLogger.Log``1(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,``0,System.Exception,System.Func{``0,System.Exception,System.String})">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Logging.Debug.DebugLoggerProvider">
            <summary>
            The provider for the <see cref="T:Microsoft.Extensions.Logging.Debug.DebugLogger"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.Debug.DebugLoggerProvider.CreateLogger(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Logging.DebugLoggerFactoryExtensions">
            <summary>
            Extension methods for the <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.DebugLoggerFactoryExtensions.AddDebug(Microsoft.Extensions.Logging.ILoggingBuilder)">
            <summary>
            Adds a debug logger named 'Debug' to the factory.
            </summary>
            <param name="builder">The extension method argument.</param>
        </member>
        <member name="T:Microsoft.Extensions.Logging.NullExternalScopeProvider">
            <summary>
            Scope provider that does nothing.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.NullExternalScopeProvider.Instance">
            <summary>
            Returns a cached instance of <see cref="T:Microsoft.Extensions.Logging.NullExternalScopeProvider"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullExternalScopeProvider.Microsoft#Extensions#Logging#IExternalScopeProvider#ForEachScope``1(System.Action{System.Object,``0},``0)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullExternalScopeProvider.Microsoft#Extensions#Logging#IExternalScopeProvider#Push(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Logging.NullScope">
            <summary>
            An empty scope without any logic
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullScope.Dispose">
            <inheritdoc />
        </member>
    </members>
</doc>
