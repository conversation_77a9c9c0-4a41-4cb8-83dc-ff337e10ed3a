﻿using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers
{
    /// <summary>
    /// 用于对外的公共接口
    /// </summary>
    [Route("/[controller]/[action]")]
    public class PublicApiController : BaseController
    {
        readonly IDbAccessor _repository;
        public PublicApiController(IDbAccessor repository)
        {
            _repository = repository;
        }
        /// <summary>
        /// 查询假期
        /// </summary>
        /// <param name="Yearstr">具体时间例如2020</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult HRHolidays(string Yearstr)
        {
            //查询是否有处理完毕，返回url
            var msg = "获取成功";
            try
            {
                int year = int.Parse(Yearstr);
                List<HR_Calendar> mlist = _repository.GetIQueryable<HR_Calendar>().Where(x => x.F_Year == year && x.F_StartTime.HasValue).OrderBy(x => x.F_StartTime).ToList();
                //上班日
                var data = mlist.Where(p => p.F_BusState == 0).Select(p => p.F_StartTime.Value.ToString("yyyy-MM-dd")).ToList();
                List<string> list = new List<string>();
                //获得指定年,月 的周六和周日的   年月日
                DateTime counYear = Convert.ToDateTime(year + "-01-01"); //
                DateTime nestYear = counYear.AddYears(1);

                for (DateTime i = counYear; i < nestYear; i = i.AddDays(1))
                {
                    if ((int)i.DayOfWeek == 0 || (int)i.DayOfWeek == 6)
                    {  //周六周日
                        if (!(from a in data where a == i.ToString("yyyy-MM-dd") select a).Any())
                        {
                            list.Add(i.ToString("yyyy-MM-dd"));
                        }
                    }
                }

                var pdata = mlist.Where(p => p.F_BusState == 1).Select(p => p.F_StartTime.Value.ToString("yyyy-MM-dd")).ToList();

                var exData = mlist.Where(p => !string.IsNullOrEmpty(p.F_Name) && p.F_BusState == 1).Select(p => new Holiday() { FHolidayName = p.F_Name, Days = new List<string>() { p.F_StartTime.Value.ToString("yyyy-MM-dd") } }).ToList();
                for (int i = 0; i < exData.Count; i++)
                {
                    if (exData[i].FHolidayName.Contains("春节"))
                    {
                        exData[i].Days.Add(DateTime.Parse(exData[i].Days[0]).AddDays(1).ToString("yyyy-MM-dd"));
                        exData[i].Days.Add(DateTime.Parse(exData[i].Days[0]).AddDays(2).ToString("yyyy-MM-dd"));
                        exData[i].Days.Add(DateTime.Parse(exData[i].Days[0]).AddDays(3).ToString("yyyy-MM-dd"));
                    }
                    if (exData[i].FHolidayName.Contains("国庆"))
                    {
                        exData[i].Days.Add(DateTime.Parse(exData[i].Days[0]).AddDays(1).ToString("yyyy-MM-dd"));
                        exData[i].Days.Add(DateTime.Parse(exData[i].Days[0]).AddDays(2).ToString("yyyy-MM-dd"));
                        exData[i].Days.Add(DateTime.Parse(exData[i].Days[0]).AddDays(3).ToString("yyyy-MM-dd"));
                        exData[i].Days.Add(DateTime.Parse(exData[i].Days[0]).AddDays(4).ToString("yyyy-MM-dd"));
                    }
                    if (exData[i].FHolidayName.Contains("国庆") && exData[i].FHolidayName.Contains("中秋"))
                    {
                        exData[i].Days.Add(DateTime.Parse(exData[i].Days[0]).AddDays(1).ToString("yyyy-MM-dd"));
                        exData[i].Days.Add(DateTime.Parse(exData[i].Days[0]).AddDays(2).ToString("yyyy-MM-dd"));
                        exData[i].Days.Add(DateTime.Parse(exData[i].Days[0]).AddDays(3).ToString("yyyy-MM-dd"));
                        exData[i].FHolidayName = "国庆节";
                        exData.Add(new Holiday() { FHolidayName = "中秋节", Days = new List<string>() { exData[i].Days[0] } });
                    }
                }
                var res = new
                {
                    Status = "ok",
                    Msg = msg,
                    BackUrl = "",
                    Data = list.Union(pdata).OrderBy(p => p),//获取本年的全部周末
                    ExtData = exData//获取本年的全部假期
                };

                return JsonContent(res.ToJson());
            }
            catch (Exception ex)
            {
                var res = new
                {
                    Status = "fail",
                    Msg = ex.Message,
                    BackUrl = "",
                    Data = "",
                    ExtData = "",
                };

                return JsonContent(res.ToJson());
            }


        }

        /// <summary>
        /// 查询假期
        /// </summary>
        /// <param name="Datestr">具体时间例如2020-05-01</param>
        /// <returns></returns>
        [HttpGet]
        public ActionResult HRHoliday(string Datestr)
        {

            //查询是否有处理完毕，返回url
            var msg = "获取成功";
            try
            {
                var datenew = DateTime.Parse(Datestr);
                string sqlPart = string.Format(@"SELECT F_Name,F_StartTime,F_BusState FROM [HR_Calendar]  WHERE F_Year = N'{0}' AND F_StartTime IS NOT NULL  ORDER BY F_StartTime ASC", datenew.Year);
                List<HR_Calendar> mlist = _repository.GetListBySql<HR_Calendar>(sqlPart);
                //上班日
                var data = mlist.Where(p => p.F_BusState == 0).Select(p => p.F_StartTime.Value.ToString("yyyy-MM-dd")).ToList();
                List<string> list = new List<string>();
                //获得指定年,月 的周六和周日的   年月日
                DateTime counYear = Convert.ToDateTime(datenew.Year + "-01-01"); //
                DateTime nestYear = counYear.AddYears(1);

                for (DateTime i = counYear; i < nestYear; i = i.AddDays(1))
                {
                    if ((int)i.DayOfWeek == 0 || (int)i.DayOfWeek == 6)
                    {  //周六周日
                        if (!(from a in data where a == i.ToString("yyyy-MM-dd") select a).Any())
                        {
                            list.Add(i.ToString("yyyy-MM-dd"));
                        }
                    }
                }
                var tdate = mlist.Where(p => p.F_BusState == 1).OrderBy(p=>p.F_StartTime).ToList();
                var pdata = tdate.Select(p => p.F_StartTime.Value.ToString("yyyy-MM-dd")).ToList();

                //{ "Msg":"获取成功","Status":"ok","BackUrl":"","Data":{ "FDate":"\/Date(1588262400000)\/","FWeek":5,"FDayType":2,"boolHoliday":true,"FHolidayName":"劳动节","FAddTime":"\/Date(1577102564023)\/"},"ExtData":null}
                Rootobject obj = new Rootobject();
                obj.FWeek = (int)datenew.DayOfWeek;
                obj.FDate = JsonConvert.SerializeObject(datenew);
                obj.FAddTime = DateTime.Now;

                if ((from a in list.Union(pdata) where a == Datestr select a).Any())
                {
                    obj.boolHoliday = true;
                }
                for (int i = 0; i < tdate.Count; i++)
                {
                    if (tdate[i].F_StartTime.Value.ToString("yyyy-MM-dd") == Datestr)
                    {
                        obj.FDayType = 2;
                        if (string.IsNullOrEmpty(tdate[i].F_Name))
                        {
                            if (string.IsNullOrEmpty(tdate[i-1].F_Name))
                            {
                                if (string.IsNullOrEmpty(tdate[i-2].F_Name))
                                {
                                    if (string.IsNullOrEmpty(tdate[i-3].F_Name))
                                    {
                                        if (string.IsNullOrEmpty(tdate[i-4].F_Name))
                                        {

                                        }
                                        else
                                        {
                                            obj.FHolidayName = tdate[i-4].F_Name;
                                        }
                                    }
                                    else
                                    {
                                        obj.FHolidayName = tdate[i-3].F_Name;
                                    }
                                }
                                else
                                {
                                    obj.FHolidayName = tdate[i-2].F_Name;
                                }
                            }
                            else
                            {
                                obj.FHolidayName = tdate[i-1].F_Name;
                            }
                        }
                        else
                        {
                            obj.FHolidayName = tdate[i].F_Name;
                        }
                    }
                }
                var res = new
                {
                    Status = "ok",
                    Msg = msg,
                    BackUrl = "",
                    Data = obj,//
                    ExtData = ""//
                };

                return JsonContent(res.ToJson());
            }
            catch (Exception ex)
            {
                var res = new
                {
                    Status = "fail",
                    Msg = ex.Message,
                    BackUrl = "",
                    Data = "",
                    ExtData = "",
                };

                return JsonContent(res.ToJson());
            }

        }
        private class Holiday
        {
            public string FHolidayName { get; set; }
            public List<string> Days { get; set; }
        }


        public class Rootobject
        {
            public string FDate { get; set; }
            public int FWeek { get; set; }
            public int FDayType { get; set; } = 1;
            public bool boolHoliday { get; set; } = false;
            public string FHolidayName { get; set; }
            public DateTime FAddTime { get; set; }
        }

    }
}