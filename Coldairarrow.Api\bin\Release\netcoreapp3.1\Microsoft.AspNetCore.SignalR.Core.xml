<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.SignalR.Core</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.SignalR.ClientProxyExtensions">
            <summary>
            Extension methods for <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(Microsoft.AspNetCore.SignalR.IClientProxy,System.String,System.Threading.CancellationToken)">
            <summary>
            Invokes a method on the connection(s) represented by the <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/> instance.
            Does not wait for a response from the receiver.
            </summary>
            <param name="clientProxy">The <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/></param>
            <param name="method">The name of the method to invoke.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous invoke.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(Microsoft.AspNetCore.SignalR.IClientProxy,System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            Invokes a method on the connection(s) represented by the <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/> instance.
            Does not wait for a response from the receiver.
            </summary>
            <param name="clientProxy">The <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/></param>
            <param name="method">The name of the method to invoke.</param>
            <param name="arg1">The first argument.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous invoke.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(Microsoft.AspNetCore.SignalR.IClientProxy,System.String,System.Object,System.Object,System.Threading.CancellationToken)">
            <summary>
            Invokes a method on the connection(s) represented by the <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/> instance.
            Does not wait for a response from the receiver.
            </summary>
            <param name="clientProxy">The <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/></param>
            <param name="method">The name of the method to invoke.</param>
            <param name="arg1">The first argument.</param>
            <param name="arg2">The second argument.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous invoke.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(Microsoft.AspNetCore.SignalR.IClientProxy,System.String,System.Object,System.Object,System.Object,System.Threading.CancellationToken)">
            <summary>
            Invokes a method on the connection(s) represented by the <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/> instance.
            Does not wait for a response from the receiver.
            </summary>
            <param name="clientProxy">The <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/></param>
            <param name="method">The name of the method to invoke.</param>
            <param name="arg1">The first argument.</param>
            <param name="arg2">The second argument.</param>
            <param name="arg3">The third argument.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous invoke.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(Microsoft.AspNetCore.SignalR.IClientProxy,System.String,System.Object,System.Object,System.Object,System.Object,System.Threading.CancellationToken)">
            <summary>
            Invokes a method on the connection(s) represented by the <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/> instance.
            Does not wait for a response from the receiver.
            </summary>
            <param name="clientProxy">The <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/></param>
            <param name="method">The name of the method to invoke.</param>
            <param name="arg1">The first argument.</param>
            <param name="arg2">The second argument.</param>
            <param name="arg3">The third argument.</param>
            <param name="arg4">The fourth argument.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous invoke.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(Microsoft.AspNetCore.SignalR.IClientProxy,System.String,System.Object,System.Object,System.Object,System.Object,System.Object,System.Threading.CancellationToken)">
            <summary>
            Invokes a method on the connection(s) represented by the <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/> instance.
            Does not wait for a response from the receiver.
            </summary>
            <param name="clientProxy">The <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/></param>
            <param name="method">The name of the method to invoke.</param>
            <param name="arg1">The first argument.</param>
            <param name="arg2">The second argument.</param>
            <param name="arg3">The third argument.</param>
            <param name="arg4">The fourth argument.</param>
            <param name="arg5">The fifth argument.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous invoke.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(Microsoft.AspNetCore.SignalR.IClientProxy,System.String,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Threading.CancellationToken)">
            <summary>
            Invokes a method on the connection(s) represented by the <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/> instance.
            Does not wait for a response from the receiver.
            </summary>
            <param name="clientProxy">The <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/></param>
            <param name="method">The name of the method to invoke.</param>
            <param name="arg1">The first argument.</param>
            <param name="arg2">The second argument.</param>
            <param name="arg3">The third argument.</param>
            <param name="arg4">The fourth argument.</param>
            <param name="arg5">The fifth argument.</param>
            <param name="arg6">The sixth argument.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous invoke.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(Microsoft.AspNetCore.SignalR.IClientProxy,System.String,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Threading.CancellationToken)">
            <summary>
            Invokes a method on the connection(s) represented by the <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/> instance.
            Does not wait for a response from the receiver.
            </summary>
            <param name="clientProxy">The <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/></param>
            <param name="method">The name of the method to invoke.</param>
            <param name="arg1">The first argument.</param>
            <param name="arg2">The second argument.</param>
            <param name="arg3">The third argument.</param>
            <param name="arg4">The fourth argument.</param>
            <param name="arg5">The fifth argument.</param>
            <param name="arg6">The sixth argument.</param>
            <param name="arg7">The seventh argument.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous invoke.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(Microsoft.AspNetCore.SignalR.IClientProxy,System.String,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Threading.CancellationToken)">
            <summary>
            Invokes a method on the connection(s) represented by the <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/> instance.
            Does not wait for a response from the receiver.
            </summary>
            <param name="clientProxy">The <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/></param>
            <param name="method">The name of the method to invoke.</param>
            <param name="arg1">The first argument.</param>
            <param name="arg2">The second argument.</param>
            <param name="arg3">The third argument.</param>
            <param name="arg4">The fourth argument.</param>
            <param name="arg5">The fifth argument.</param>
            <param name="arg6">The sixth argument.</param>
            <param name="arg7">The seventh argument.</param>
            <param name="arg8">The eigth argument.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous invoke.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(Microsoft.AspNetCore.SignalR.IClientProxy,System.String,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Threading.CancellationToken)">
            <summary>
            Invokes a method on the connection(s) represented by the <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/> instance.
            Does not wait for a response from the receiver.
            </summary>
            <param name="clientProxy">The <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/></param>
            <param name="method">The name of the method to invoke.</param>
            <param name="arg1">The first argument.</param>
            <param name="arg2">The second argument.</param>
            <param name="arg3">The third argument.</param>
            <param name="arg4">The fourth argument.</param>
            <param name="arg5">The fifth argument.</param>
            <param name="arg6">The sixth argument.</param>
            <param name="arg7">The seventh argument.</param>
            <param name="arg8">The eigth argument.</param>
            <param name="arg9">The ninth argument.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous invoke.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(Microsoft.AspNetCore.SignalR.IClientProxy,System.String,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Threading.CancellationToken)">
            <summary>
            Invokes a method on the connection(s) represented by the <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/> instance.
            Does not wait for a response from the receiver.
            </summary>
            <param name="clientProxy">The <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/></param>
            <param name="method">The name of the method to invoke.</param>
            <param name="arg1">The first argument.</param>
            <param name="arg2">The second argument.</param>
            <param name="arg3">The third argument.</param>
            <param name="arg4">The fourth argument.</param>
            <param name="arg5">The fifth argument.</param>
            <param name="arg6">The sixth argument.</param>
            <param name="arg7">The seventh argument.</param>
            <param name="arg8">The eigth argument.</param>
            <param name="arg9">The ninth argument.</param>
            <param name="arg10">The tenth argument.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous invoke.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1">
            <summary>
            A default in-memory lifetime manager abstraction for <see cref="T:Microsoft.AspNetCore.SignalR.Hub"/> instances.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1.#ctor(Microsoft.Extensions.Logging.ILogger{Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager{`0}})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1"/> class.
            </summary>
            <param name="logger">The logger.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1.AddToGroupAsync(System.String,System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1.RemoveFromGroupAsync(System.String,System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1.SendAllAsync(System.String,System.Object[],System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1.SendConnectionAsync(System.String,System.String,System.Object[],System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1.SendGroupAsync(System.String,System.String,System.Object[],System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1.SendGroupsAsync(System.Collections.Generic.IReadOnlyList{System.String},System.String,System.Object[],System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1.SendGroupExceptAsync(System.String,System.String,System.Object[],System.Collections.Generic.IReadOnlyList{System.String},System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1.SendUserAsync(System.String,System.String,System.Object[],System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1.OnConnectedAsync(Microsoft.AspNetCore.SignalR.HubConnectionContext)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1.OnDisconnectedAsync(Microsoft.AspNetCore.SignalR.HubConnectionContext)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1.SendAllExceptAsync(System.String,System.Object[],System.Collections.Generic.IReadOnlyList{System.String},System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1.SendConnectionsAsync(System.Collections.Generic.IReadOnlyList{System.String},System.String,System.Object[],System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultHubLifetimeManager`1.SendUsersAsync(System.Collections.Generic.IReadOnlyList{System.String},System.String,System.Object[],System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.DefaultUserIdProvider">
            <summary>
            The default provider for getting the user ID from a connection.
            This provider gets the user ID from the connection's <see cref="P:Microsoft.AspNetCore.SignalR.HubConnectionContext.User"/> name identifier claim.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DefaultUserIdProvider.GetUserId(Microsoft.AspNetCore.SignalR.HubConnectionContext)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.DynamicHub">
            <summary>
            A base class for SignalR hubs that use <c>dynamic</c> to represent client invocations.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.DynamicHub.Clients">
            <summary>
            Gets or sets an object that can be used to invoke methods on the clients connected to this hub.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.DynamicHubClients">
            <summary>
            A class that provides <c>dynamic</c> access to connections, including the one that sent the current invocation.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DynamicHubClients.#ctor(Microsoft.AspNetCore.SignalR.IHubCallerClients)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.SignalR.DynamicHubClients"/> class.
            </summary>
            <param name="clients">A wrapped <see cref="T:Microsoft.AspNetCore.SignalR.IHubCallerClients"/> that is used to invoke methods.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.DynamicHubClients.All">
            <summary>
            Gets an object that can be used to invoke methods on all clients connected to the hub.
            </summary>
            <returns>An object that can be used to invoke methods on the specified user.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DynamicHubClients.AllExcept(System.Collections.Generic.IReadOnlyList{System.String})">
            <summary>
            Gets an object that can be used to invoke methods on all clients connected to the hub excluding the specified connections.
            </summary>
            <param name="excludedConnectionIds">A collection of connection IDs to exclude.</param>
            <returns>An object that can be used to invoke methods on the specified user.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.DynamicHubClients.Caller">
            <summary>
            Gets an object that can be used to invoke methods on the connection which triggered the current invocation.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DynamicHubClients.Client(System.String)">
            <summary>
            Gets an object that can be used to invoke methods on the specified connection.
            </summary>
            <param name="connectionId">The connection ID.</param>
            <returns>An object that can be used to invoke methods.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DynamicHubClients.Clients(System.Collections.Generic.IReadOnlyList{System.String})">
            <summary>
            Gets an object that can be used to invoke methods on the specified connections.
            </summary>
            <param name="connectionIds">The connection IDs.</param>
            <returns>An object that can be used to invoke methods.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DynamicHubClients.Group(System.String)">
            <summary>
            Gets an object that can be used to invoke methods on all connections in the specified group.
            </summary>
            <param name="groupName">The group name.</param>
            <returns>An object that can be used to invoke methods.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DynamicHubClients.Groups(System.Collections.Generic.IReadOnlyList{System.String})">
            <summary>
            Gets an object that can be used to invoke methods on all connections in all of the specified groups.
            </summary>
            <param name="groupNames">The group names.</param>
            <returns>An object that can be used to invoke methods on the specified user.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DynamicHubClients.GroupExcept(System.String,System.Collections.Generic.IReadOnlyList{System.String})">
            <summary>
            Gets an object that can be used to invoke methods on all connections in the specified group excluding the specified connections.
            </summary>
            <param name="groupName">The group name.</param>
            <param name="excludedConnectionIds">A collection of connection IDs to exclude.</param>
            <returns>An object that can be used to invoke methods.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DynamicHubClients.OthersInGroup(System.String)">
            <summary>
            Gets an object that can be used to invoke methods on connections in a group other than the caller.
            </summary>
            <returns>An object that can be used to invoke methods.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.DynamicHubClients.Others">
            <summary>
            Gets an object that can be used to invoke methods on connections other than the caller.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DynamicHubClients.User(System.String)">
            <summary>
            Gets an object that can be used to invoke methods on all connections associated with the specified user.
            </summary>
            <param name="userId">The user ID.</param>
            <returns>An object that can be used to invoke methods.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.DynamicHubClients.Users(System.Collections.Generic.IReadOnlyList{System.String})">
            <summary>
            Gets an object that can be used to invoke methods on all connections associated with all of the specified users.
            </summary>
            <param name="userIds">The user IDs.</param>
            <returns>An object that can be used to invoke methods.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.Hub">
            <summary>
            A base class for a SignalR hub.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.Hub.Clients">
            <summary>
            Gets or sets an object that can be used to invoke methods on the clients connected to this hub.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.Hub.Context">
            <summary>
            Gets or sets the hub caller context.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.Hub.Groups">
            <summary>
            Gets or sets the group manager.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.Hub.OnConnectedAsync">
            <summary>
            Called when a new connection is established with the hub.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous connect.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.Hub.OnDisconnectedAsync(System.Exception)">
            <summary>
            Called when a connection with the hub is terminated.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous disconnect.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.Hub.Dispose(System.Boolean)">
            <summary>
            Releases all resources currently used by this <see cref="T:Microsoft.AspNetCore.SignalR.Hub"/> instance.
            </summary>
            <param name="disposing"><c>true</c> if this method is being invoked by the <see cref="M:Microsoft.AspNetCore.SignalR.Hub.Dispose"/> method,
            otherwise <c>false</c>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.Hub.Dispose">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.HubCallerContext">
            <summary>
            A context abstraction for accessing information about the hub caller connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubCallerContext.ConnectionId">
            <summary>
            Gets the connection ID.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubCallerContext.UserIdentifier">
            <summary>
            Gets the user identifier.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubCallerContext.User">
            <summary>
            Gets the user.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubCallerContext.Items">
            <summary>
            Gets a key/value collection that can be used to share data within the scope of this connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubCallerContext.Features">
            <summary>
            Gets the collection of HTTP features available on the connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubCallerContext.ConnectionAborted">
            <summary>
            Gets a <see cref="T:System.Threading.CancellationToken"/> that notifies when the connection is aborted.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubCallerContext.Abort">
            <summary>
            Aborts the connection.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.HubClientsExtensions">
            <summary>
            Extension methods for <see cref="T:Microsoft.AspNetCore.SignalR.IHubClients`1"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.AllExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all clients connected to the hub excluding the specified connection.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.AllExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all clients connected to the hub excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <param name="excludedConnectionId2">The second connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.AllExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all clients connected to the hub excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <param name="excludedConnectionId2">The second connection to exclude.</param>
            <param name="excludedConnectionId3">The third connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.AllExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all clients connected to the hub excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <param name="excludedConnectionId2">The second connection to exclude.</param>
            <param name="excludedConnectionId3">The third connection to exclude.</param>
            <param name="excludedConnectionId4">The fourth connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.AllExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all clients connected to the hub excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <param name="excludedConnectionId2">The second connection to exclude.</param>
            <param name="excludedConnectionId3">The third connection to exclude.</param>
            <param name="excludedConnectionId4">The fourth connection to exclude.</param>
            <param name="excludedConnectionId5">The fifth connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.AllExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all clients connected to the hub excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <param name="excludedConnectionId2">The second connection to exclude.</param>
            <param name="excludedConnectionId3">The third connection to exclude.</param>
            <param name="excludedConnectionId4">The fourth connection to exclude.</param>
            <param name="excludedConnectionId5">The fifth connection to exclude.</param>
            <param name="excludedConnectionId6">The sixth connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.AllExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all clients connected to the hub excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <param name="excludedConnectionId2">The second connection to exclude.</param>
            <param name="excludedConnectionId3">The third connection to exclude.</param>
            <param name="excludedConnectionId4">The fourth connection to exclude.</param>
            <param name="excludedConnectionId5">The fifth connection to exclude.</param>
            <param name="excludedConnectionId6">The sixth connection to exclude.</param>
            <param name="excludedConnectionId7">The seventh connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.AllExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all clients connected to the hub excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <param name="excludedConnectionId2">The second connection to exclude.</param>
            <param name="excludedConnectionId3">The third connection to exclude.</param>
            <param name="excludedConnectionId4">The fourth connection to exclude.</param>
            <param name="excludedConnectionId5">The fifth connection to exclude.</param>
            <param name="excludedConnectionId6">The sixth connection to exclude.</param>
            <param name="excludedConnectionId7">The seventh connection to exclude.</param>
            <param name="excludedConnectionId8">The eighth connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Clients``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="connection1">The first connection to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Clients``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="connection1">The first connection to include.</param>
            <param name="connection2">The second connection to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Clients``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="connection1">The first connection to include.</param>
            <param name="connection2">The second connection to include.</param>
            <param name="connection3">The third connection to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Clients``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="connection1">The first connection to include.</param>
            <param name="connection2">The second connection to include.</param>
            <param name="connection3">The third connection to include.</param>
            <param name="connection4">The fourth connection to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Clients``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="connection1">The first connection to include.</param>
            <param name="connection2">The second connection to include.</param>
            <param name="connection3">The third connection to include.</param>
            <param name="connection4">The fourth connection to include.</param>
            <param name="connection5">The fifth connection to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Clients``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="connection1">The first connection to include.</param>
            <param name="connection2">The second connection to include.</param>
            <param name="connection3">The third connection to include.</param>
            <param name="connection4">The fourth connection to include.</param>
            <param name="connection5">The fifth connection to include.</param>
            <param name="connection6">The sixth connection to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Clients``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="connection1">The first connection to include.</param>
            <param name="connection2">The second connection to include.</param>
            <param name="connection3">The third connection to include.</param>
            <param name="connection4">The fourth connection to include.</param>
            <param name="connection5">The fifth connection to include.</param>
            <param name="connection6">The sixth connection to include.</param>
            <param name="connection7">The seventh connection to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Clients``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="connection1">The first connection to include.</param>
            <param name="connection2">The second connection to include.</param>
            <param name="connection3">The third connection to include.</param>
            <param name="connection4">The fourth connection to include.</param>
            <param name="connection5">The fifth connection to include.</param>
            <param name="connection6">The sixth connection to include.</param>
            <param name="connection7">The seventh connection to include.</param>
            <param name="connection8">The eighth connection to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Groups``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in all of the specified groups.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="group1">The first group to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Groups``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in all of the specified groups.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="group1">The first group to include.</param>
            <param name="group2">The second group to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Groups``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in all of the specified groups.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="group1">The first group to include.</param>
            <param name="group2">The second group to include.</param>
            <param name="group3">The third group to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Groups``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in all of the specified groups.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="group1">The first group to include.</param>
            <param name="group2">The second group to include.</param>
            <param name="group3">The third group to include.</param>
            <param name="group4">The fourth group to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Groups``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in all of the specified groups.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="group1">The first group to include.</param>
            <param name="group2">The second group to include.</param>
            <param name="group3">The third group to include.</param>
            <param name="group4">The fourth group to include.</param>
            <param name="group5">The fifth group to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Groups``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in all of the specified groups.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="group1">The first group to include.</param>
            <param name="group2">The second group to include.</param>
            <param name="group3">The third group to include.</param>
            <param name="group4">The fourth group to include.</param>
            <param name="group5">The fifth group to include.</param>
            <param name="group6">The sixth group to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Groups``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in all of the specified groups.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="group1">The first group to include.</param>
            <param name="group2">The second group to include.</param>
            <param name="group3">The third group to include.</param>
            <param name="group4">The fourth group to include.</param>
            <param name="group5">The fifth group to include.</param>
            <param name="group6">The sixth group to include.</param>
            <param name="group7">The seventh group to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Groups``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in all of the specified groups.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="group1">The first group to include.</param>
            <param name="group2">The second group to include.</param>
            <param name="group3">The third group to include.</param>
            <param name="group4">The fourth group to include.</param>
            <param name="group5">The fifth group to include.</param>
            <param name="group6">The sixth group to include.</param>
            <param name="group7">The seventh group to include.</param>
            <param name="group8">The eighth group to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.GroupExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in the specified group excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="groupName">The group name.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.GroupExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in the specified group excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="groupName">The group name.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <param name="excludedConnectionId2">The second connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.GroupExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in the specified group excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="groupName">The group name.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <param name="excludedConnectionId2">The second connection to exclude.</param>
            <param name="excludedConnectionId3">The third connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.GroupExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in the specified group excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="groupName">The group name.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <param name="excludedConnectionId2">The second connection to exclude.</param>
            <param name="excludedConnectionId3">The third connection to exclude.</param>
            <param name="excludedConnectionId4">The fourth connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.GroupExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in the specified group excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="groupName">The group name.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <param name="excludedConnectionId2">The second connection to exclude.</param>
            <param name="excludedConnectionId3">The third connection to exclude.</param>
            <param name="excludedConnectionId4">The fourth connection to exclude.</param>
            <param name="excludedConnectionId5">The fifth connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.GroupExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in the specified group excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="groupName">The group name.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <param name="excludedConnectionId2">The second connection to exclude.</param>
            <param name="excludedConnectionId3">The third connection to exclude.</param>
            <param name="excludedConnectionId4">The fourth connection to exclude.</param>
            <param name="excludedConnectionId5">The fifth connection to exclude.</param>
            <param name="excludedConnectionId6">The sixth connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.GroupExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in the specified group excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="groupName">The group name.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <param name="excludedConnectionId2">The second connection to exclude.</param>
            <param name="excludedConnectionId3">The third connection to exclude.</param>
            <param name="excludedConnectionId4">The fourth connection to exclude.</param>
            <param name="excludedConnectionId5">The fifth connection to exclude.</param>
            <param name="excludedConnectionId6">The sixth connection to exclude.</param>
            <param name="excludedConnectionId7">The seventh connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.GroupExcept``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in the specified group excluding the specified connections.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="groupName">The group name.</param>
            <param name="excludedConnectionId1">The first connection to exclude.</param>
            <param name="excludedConnectionId2">The second connection to exclude.</param>
            <param name="excludedConnectionId3">The third connection to exclude.</param>
            <param name="excludedConnectionId4">The fourth connection to exclude.</param>
            <param name="excludedConnectionId5">The fifth connection to exclude.</param>
            <param name="excludedConnectionId6">The sixth connection to exclude.</param>
            <param name="excludedConnectionId7">The seventh connection to exclude.</param>
            <param name="excludedConnectionId8">The eighth connection to exclude.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Users``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections associated with all of the specified users.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="user1">The first user to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Users``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections associated with all of the specified users.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="user1">The first user to include.</param>
            <param name="user2">The second user to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Users``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections associated with all of the specified users.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="user1">The first user to include.</param>
            <param name="user2">The second user to include.</param>
            <param name="user3">The third user to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Users``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections associated with all of the specified users.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="user1">The first user to include.</param>
            <param name="user2">The second user to include.</param>
            <param name="user3">The third user to include.</param>
            <param name="user4">The fourth user to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Users``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections associated with all of the specified users.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="user1">The first user to include.</param>
            <param name="user2">The second user to include.</param>
            <param name="user3">The third user to include.</param>
            <param name="user4">The fourth user to include.</param>
            <param name="user5">The fifth user to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Users``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections associated with all of the specified users.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="user1">The first user to include.</param>
            <param name="user2">The second user to include.</param>
            <param name="user3">The third user to include.</param>
            <param name="user4">The fourth user to include.</param>
            <param name="user5">The fifth user to include.</param>
            <param name="user6">The sixth user to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Users``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections associated with all of the specified users.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="user1">The first user to include.</param>
            <param name="user2">The second user to include.</param>
            <param name="user3">The third user to include.</param>
            <param name="user4">The fourth user to include.</param>
            <param name="user5">The fifth user to include.</param>
            <param name="user6">The sixth user to include.</param>
            <param name="user7">The seventh user to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubClientsExtensions.Users``1(Microsoft.AspNetCore.SignalR.IHubClients{``0},System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections associated with all of the specified users.
            </summary>
            <param name="hubClients">The abstraction that provides access to connections.</param>
            <param name="user1">The first user to include.</param>
            <param name="user2">The second user to include.</param>
            <param name="user3">The third user to include.</param>
            <param name="user4">The fourth user to include.</param>
            <param name="user5">The fifth user to include.</param>
            <param name="user6">The sixth user to include.</param>
            <param name="user7">The seventh user to include.</param>
            <param name="user8">The eighth user to include.</param>
            <returns>A <typeparamref name="T" /> representing the methods that can be invoked on the clients.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.HubConnectionContext">
            <summary>
            Encapsulates all information about an individual connection to a SignalR Hub.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubConnectionContext.#ctor(Microsoft.AspNetCore.Connections.ConnectionContext,Microsoft.AspNetCore.SignalR.HubConnectionContextOptions,Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.SignalR.HubConnectionContext"/> class.
            </summary>
            <param name="connectionContext">The underlying <see cref="T:Microsoft.AspNetCore.Connections.ConnectionContext"/>.</param>
            <param name="loggerFactory">The logger factory.</param>
            <param name="contextOptions">The options to configure the HubConnectionContext.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubConnectionContext.ConnectionAborted">
            <summary>
            Gets a <see cref="T:System.Threading.CancellationToken"/> that notifies when the connection is aborted.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubConnectionContext.ConnectionId">
            <summary>
            Gets the ID for this connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubConnectionContext.User">
            <summary>
            Gets the user for this connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubConnectionContext.Features">
            <summary>
            Gets the collection of features available on this connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubConnectionContext.Items">
            <summary>
            Gets a key/value collection that can be used to share data within the scope of this connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubConnectionContext.UserIdentifier">
            <summary>
            Gets or sets the user identifier for this connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubConnectionContext.Protocol">
            <summary>
            Gets the protocol used by this connection.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubConnectionContext.WriteAsync(Microsoft.AspNetCore.SignalR.SerializedHubMessage,System.Threading.CancellationToken)">
            <summary>
            This method is designed to support the framework and is not intended to be used by application code. Writes a pre-serialized message to the
            connection.
            </summary>
            <param name="message">The serialization cache to use.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubConnectionContext.Abort">
            <summary>
            Aborts the connection.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.HubConnectionContextOptions">
            <summary>
            Options used to configure <see cref="T:Microsoft.AspNetCore.SignalR.HubConnectionContext"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubConnectionContextOptions.KeepAliveInterval">
            <summary>
            Gets or sets the interval used to send keep alive pings to connected clients.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubConnectionContextOptions.ClientTimeoutInterval">
            <summary>
            Gets or sets the time window clients have to send a message before the server closes the connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubConnectionContextOptions.StreamBufferCapacity">
            <summary>
            Gets or sets the max buffer size for client upload streams.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubConnectionContextOptions.MaximumReceiveMessageSize">
            <summary>
            Gets or sets the maximum message size the client can send.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.HubConnectionHandler`1">
            <summary>
            Handles incoming connections and implements the SignalR Hub Protocol.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubConnectionHandler`1.#ctor(Microsoft.AspNetCore.SignalR.HubLifetimeManager{`0},Microsoft.AspNetCore.SignalR.IHubProtocolResolver,Microsoft.Extensions.Options.IOptions{Microsoft.AspNetCore.SignalR.HubOptions},Microsoft.Extensions.Options.IOptions{Microsoft.AspNetCore.SignalR.HubOptions{`0}},Microsoft.Extensions.Logging.ILoggerFactory,Microsoft.AspNetCore.SignalR.IUserIdProvider,Microsoft.Extensions.DependencyInjection.IServiceScopeFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.SignalR.HubConnectionHandler`1"/> class.
            </summary>
            <param name="lifetimeManager">The hub lifetime manager.</param>
            <param name="protocolResolver">The protocol resolver used to resolve the protocols between client and server.</param>
            <param name="globalHubOptions">The global options used to initialize hubs.</param>
            <param name="hubOptions">Hub specific options used to initialize hubs. These options override the global options.</param>
            <param name="loggerFactory">The logger factory.</param>
            <param name="userIdProvider">The user ID provider used to get the user ID from a hub connection.</param>
            <param name="serviceScopeFactory">The service scope factory.</param>
            <remarks>This class is typically created via dependency injection.</remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubConnectionHandler`1.OnConnectedAsync(Microsoft.AspNetCore.Connections.ConnectionContext)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.HubInvocationContext">
            <summary>
            Context for a Hub invocation.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubInvocationContext.#ctor(Microsoft.AspNetCore.SignalR.HubCallerContext,System.String,System.Object[])">
            <summary>
            Instantiates a new instance of the <see cref="T:Microsoft.AspNetCore.SignalR.HubInvocationContext"/> class.
            </summary>
            <param name="context">Context for the active Hub connection and caller.</param>
            <param name="hubMethodName">The name of the Hub method being invoked.</param>
            <param name="hubMethodArguments">The arguments provided by the client.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubInvocationContext.Context">
            <summary>
            Gets the context for the active Hub connection and caller.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubInvocationContext.HubMethodName">
            <summary>
            Gets the name of the Hub method being invoked.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubInvocationContext.HubMethodArguments">
            <summary>
            Gets the arguments provided by the client.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.HubLifetimeManager`1">
            <summary>
            A lifetime manager abstraction for <see cref="T:Microsoft.AspNetCore.SignalR.Hub"/> instances.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubLifetimeManager`1.OnConnectedAsync(Microsoft.AspNetCore.SignalR.HubConnectionContext)">
            <summary>
            Called when a connection is started.
            </summary>
            <param name="connection">The connection.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous connect.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubLifetimeManager`1.OnDisconnectedAsync(Microsoft.AspNetCore.SignalR.HubConnectionContext)">
            <summary>
            Called when a connection is finished.
            </summary>
            <param name="connection">The connection.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous disconnect.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubLifetimeManager`1.SendAllAsync(System.String,System.Object[],System.Threading.CancellationToken)">
            <summary>
            Sends an invocation message to all hub connections.
            </summary>
            <param name="methodName">The invocation method name.</param>
            <param name="args">The invocation arguments.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous send.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubLifetimeManager`1.SendAllExceptAsync(System.String,System.Object[],System.Collections.Generic.IReadOnlyList{System.String},System.Threading.CancellationToken)">
            <summary>
            Sends an invocation message to all hub connections excluding the specified connections.
            </summary>
            <param name="methodName">The invocation method name.</param>
            <param name="args">The invocation arguments.</param>
            <param name="excludedConnectionIds">A collection of connection IDs to exclude.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous send.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubLifetimeManager`1.SendConnectionAsync(System.String,System.String,System.Object[],System.Threading.CancellationToken)">
            <summary>
            Sends an invocation message to the specified connection.
            </summary>
            <param name="connectionId">The connection ID.</param>
            <param name="methodName">The invocation method name.</param>
            <param name="args">The invocation arguments.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous send.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubLifetimeManager`1.SendConnectionsAsync(System.Collections.Generic.IReadOnlyList{System.String},System.String,System.Object[],System.Threading.CancellationToken)">
            <summary>
            Sends an invocation message to the specified connections.
            </summary>
            <param name="connectionIds">The connection IDs.</param>
            <param name="methodName">The invocation method name.</param>
            <param name="args">The invocation arguments.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous send.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubLifetimeManager`1.SendGroupAsync(System.String,System.String,System.Object[],System.Threading.CancellationToken)">
            <summary>
            Sends an invocation message to the specified group.
            </summary>
            <param name="groupName">The group name.</param>
            <param name="methodName">The invocation method name.</param>
            <param name="args">The invocation arguments.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous send.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubLifetimeManager`1.SendGroupsAsync(System.Collections.Generic.IReadOnlyList{System.String},System.String,System.Object[],System.Threading.CancellationToken)">
            <summary>
            Sends an invocation message to the specified groups.
            </summary>
            <param name="groupNames">The group names.</param>
            <param name="methodName">The invocation method name.</param>
            <param name="args">The invocation arguments.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous send.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubLifetimeManager`1.SendGroupExceptAsync(System.String,System.String,System.Object[],System.Collections.Generic.IReadOnlyList{System.String},System.Threading.CancellationToken)">
            <summary>
            Sends an invocation message to the specified group excluding the specified connections.
            </summary>
            <param name="groupName">The group name.</param>
            <param name="methodName">The invocation method name.</param>
            <param name="args">The invocation arguments.</param>
            <param name="excludedConnectionIds">A collection of connection IDs to exclude.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous send.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubLifetimeManager`1.SendUserAsync(System.String,System.String,System.Object[],System.Threading.CancellationToken)">
            <summary>
            Sends an invocation message to the specified user.
            </summary>
            <param name="userId">The user ID.</param>
            <param name="methodName">The invocation method name.</param>
            <param name="args">The invocation arguments.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous send.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubLifetimeManager`1.SendUsersAsync(System.Collections.Generic.IReadOnlyList{System.String},System.String,System.Object[],System.Threading.CancellationToken)">
            <summary>
            Sends an invocation message to the specified users.
            </summary>
            <param name="userIds">The user IDs.</param>
            <param name="methodName">The invocation method name.</param>
            <param name="args">The invocation arguments.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous send.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubLifetimeManager`1.AddToGroupAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Adds a connection to the specified group.
            </summary>
            <param name="connectionId">The connection ID to add to a group.</param>
            <param name="groupName">The group name.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous add.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubLifetimeManager`1.RemoveFromGroupAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Removes a connection from the specified group.
            </summary>
            <param name="connectionId">The connection ID to remove from a group.</param>
            <param name="groupName">The group name.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous remove.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.HubMetadata">
            <summary>
            Metadata that describes the <see cref="T:Microsoft.AspNetCore.SignalR.Hub"/> information associated with a specific endpoint.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubMetadata.HubType">
            <summary>
            The type of <see cref="T:Microsoft.AspNetCore.SignalR.Hub"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.HubMethodNameAttribute">
            <summary>
            Customizes the name of a hub method.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubMethodNameAttribute.Name">
            <summary>
            The customized name of the hub method.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.HubMethodNameAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.SignalR.HubMethodNameAttribute"/> class.
            </summary>
            <param name="name">The customized name of the hub method.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.HubOptions">
            <summary>
            Options used to configure hub instances.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubOptions.HandshakeTimeout">
            <summary>
            Gets or sets the interval used by the server to timeout incoming handshake requests by clients. The default timeout is 15 seconds.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubOptions.KeepAliveInterval">
            <summary>
            Gets or sets the interval used by the server to send keep alive pings to connected clients. The default interval is 15 seconds.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubOptions.ClientTimeoutInterval">
            <summary>
            Gets or sets the time window clients have to send a message before the server closes the connection. The default timeout is 30 seconds.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubOptions.SupportedProtocols">
            <summary>
            Gets or sets a collection of supported hub protocol names.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubOptions.MaximumReceiveMessageSize">
            <summary>
            Gets or sets the maximum message size of a single incoming hub message. The default is 32KB.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubOptions.EnableDetailedErrors">
            <summary>
            Gets or sets a value indicating whether detailed error messages are sent to the client.
            Detailed error messages include details from exceptions thrown on the server.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.HubOptions.StreamBufferCapacity">
            <summary>
            Gets or sets the max buffer size for client upload streams. The default size is 10.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.HubOptions`1">
            <summary>
            Options used to configure the specified hub type instances. These options override globally set options.
            </summary>
            <typeparam name="THub">The hub type to configure.</typeparam>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.Hub`1">
            <summary>
            A base class for a strongly typed SignalR hub.
            </summary>
            <typeparam name="T">The type of client.</typeparam>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.Hub`1.Clients">
            <summary>
            Gets or sets a <typeparamref name="T"/> that can be used to invoke methods on the clients connected to this hub.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.IClientProxy">
            <summary>
            A proxy abstraction for invoking hub methods.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IClientProxy.SendCoreAsync(System.String,System.Object[],System.Threading.CancellationToken)">
            <summary>
            Invokes a method on the connection(s) represented by the <see cref="T:Microsoft.AspNetCore.SignalR.IClientProxy"/> instance.
            Does not wait for a response from the receiver.
            </summary>
            <param name="method">Name of the method to invoke.</param>
            <param name="args">A collection of arguments to pass to the client.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous invoke.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.IGroupManager">
            <summary>
            A manager abstraction for adding and removing connections from groups.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IGroupManager.AddToGroupAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Adds a connection to the specified group.
            </summary>
            <param name="connectionId">The connection ID to add to a group.</param>
            <param name="groupName">The group name.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous add.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IGroupManager.RemoveFromGroupAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Removes a connection from the specified group.
            </summary>
            <param name="connectionId">The connection ID to remove from a group.</param>
            <param name="groupName">The group name.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous remove.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.IHubActivator`1">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.SignalR.Hub"/> activator abstraction.
            </summary>
            <typeparam name="THub">The hub type.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IHubActivator`1.Create">
            <summary>
            Creates a hub.
            </summary>
            <returns>The created hub.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IHubActivator`1.Release(`0)">
            <summary>
            Releases the specified hub.
            </summary>
            <param name="hub">The hub to release.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.IHubCallerClients">
            <summary>
            A clients caller abstraction for a hub.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.IHubCallerClients`1">
            <summary>
            An abstraction that provides access to client connections, including the one that sent the current invocation.
            </summary>
            <typeparam name="T">The client caller type.</typeparam>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.IHubCallerClients`1.Caller">
            <summary>
            Gets a caller to the connection which triggered the current invocation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.IHubCallerClients`1.Others">
            <summary>
            Gets a caller to all connections except the one which triggered the current invocation.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IHubCallerClients`1.OthersInGroup(System.String)">
            <summary>
            Gets a caller to all connections in the specified group, except the one which triggered the current invocation.
            </summary>
            <returns>A client caller.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.IHubClients">
            <summary>
            An abstraction that provides access to client connections.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.IHubClients`1">
            <summary>
            An abstraction that provides access to client connections.
            </summary>
            <typeparam name="T">The client invoker type.</typeparam>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.IHubClients`1.All">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all clients connected to the hub.
            </summary>
            <returns>A client caller.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IHubClients`1.AllExcept(System.Collections.Generic.IReadOnlyList{System.String})">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all clients connected to the hub excluding the specified client connections.
            </summary>
            <param name="excludedConnectionIds">A collection of connection IDs to exclude.</param>
            <returns>A client caller.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IHubClients`1.Client(System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on the specified client connection.
            </summary>
            <param name="connectionId">The connection ID.</param>
            <returns>A client caller.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IHubClients`1.Clients(System.Collections.Generic.IReadOnlyList{System.String})">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on the specified client connections.
            </summary>
            <param name="connectionIds">The connection IDs.</param>
            <returns>A client caller.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IHubClients`1.Group(System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in the specified group.
            </summary>
            <param name="groupName">The group name.</param>
            <returns>A client caller.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IHubClients`1.Groups(System.Collections.Generic.IReadOnlyList{System.String})">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in all of the specified groups.
            </summary>
            <param name="groupNames">The group names.</param>
            <returns>A client caller.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IHubClients`1.GroupExcept(System.String,System.Collections.Generic.IReadOnlyList{System.String})">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections in the specified group excluding the specified connections.
            </summary>
            <param name="groupName">The group name.</param>
            <param name="excludedConnectionIds">A collection of connection IDs to exclude.</param>
            <returns>A client caller.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IHubClients`1.User(System.String)">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections associated with the specified user.
            </summary>
            <param name="userId">The user ID.</param>
            <returns>A client caller.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IHubClients`1.Users(System.Collections.Generic.IReadOnlyList{System.String})">
            <summary>
            Gets a <typeparamref name="T" /> that can be used to invoke methods on all connections associated with all of the specified users.
            </summary>
            <param name="userIds">The user IDs.</param>
            <returns>A client caller.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.IHubContext`1">
            <summary>
            A context abstraction for a hub.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.IHubContext`1.Clients">
            <summary>
            Gets a <see cref="T:Microsoft.AspNetCore.SignalR.IHubClients"/> that can be used to invoke methods on clients connected to the hub.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.IHubContext`1.Groups">
            <summary>
            Gets a <see cref="T:Microsoft.AspNetCore.SignalR.IGroupManager"/> that can be used to add and remove connections to named groups.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.IHubContext`2">
            <summary>
            A context abstraction for a hub.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.IHubContext`2.Clients">
            <summary>
            Gets a <see cref="T:Microsoft.AspNetCore.SignalR.IHubClients`1"/> that can be used to invoke methods on clients connected to the hub.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.IHubContext`2.Groups">
            <summary>
            Gets a <see cref="T:Microsoft.AspNetCore.SignalR.IGroupManager"/> that can be used to add and remove connections to named groups.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.IHubProtocolResolver">
            <summary>
            A resolver abstraction for working with <see cref="T:Microsoft.AspNetCore.SignalR.Protocol.IHubProtocol"/> instances.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.IHubProtocolResolver.AllProtocols">
            <summary>
            Gets a collection of all available hub protocols.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IHubProtocolResolver.GetProtocol(System.String,System.Collections.Generic.IReadOnlyList{System.String})">
            <summary>
            Gets the hub protocol with the specified name, if it is allowed by the specified list of supported protocols.
            </summary>
            <param name="protocolName">The protocol name.</param>
            <param name="supportedProtocols">A collection of supported protocols.</param>
            <returns>A matching <see cref="T:Microsoft.AspNetCore.SignalR.Protocol.IHubProtocol"/> or <c>null</c> if no matching protocol was found.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.Internal.DefaultHubCallerContext">
            <summary>
            A context for accessing information about the hub caller from their connection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.Internal.DefaultHubCallerContext.ConnectionId">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.Internal.DefaultHubCallerContext.UserIdentifier">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.Internal.DefaultHubCallerContext.User">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.Internal.DefaultHubCallerContext.Items">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.Internal.DefaultHubCallerContext.Features">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.Internal.DefaultHubCallerContext.ConnectionAborted">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.Internal.DefaultHubCallerContext.Abort">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.Internal.AsyncEnumerableAdapters.CancelableAsyncEnumerable`1">
            <summary>Converts an IAsyncEnumerable of T to an IAsyncEnumerable of object.</summary>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.ISignalRServerBuilder">
            <summary>
            A builder abstraction for configuring SignalR servers.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.IUserIdProvider">
            <summary>
            A provider abstraction for configuring the "User ID" for a connection.
            </summary>
            <remarks><see cref="T:Microsoft.AspNetCore.SignalR.IUserIdProvider"/> is used by <see cref="M:Microsoft.AspNetCore.SignalR.IHubClients`1.User(System.String)"/> to invoke connections associated with a user.</remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.IUserIdProvider.GetUserId(Microsoft.AspNetCore.SignalR.HubConnectionContext)">
            <summary>
            Gets the user ID for the specified connection.
            </summary>
            <param name="connection">The connection to get the user ID for.</param>
            <returns>The user ID for the specified connection.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.SerializedHubMessage">
            <summary>
            Represents a serialization cache for a single message.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.SerializedHubMessage.Message">
            <summary>
            Gets the hub message for the serialization cache.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.SerializedHubMessage.#ctor(System.Collections.Generic.IReadOnlyList{Microsoft.AspNetCore.SignalR.SerializedMessage})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.SignalR.SerializedHubMessage"/> class.
            </summary>
            <param name="messages">A collection of already serialized messages to cache.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.SerializedHubMessage.#ctor(Microsoft.AspNetCore.SignalR.Protocol.HubMessage)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.SignalR.SerializedHubMessage"/> class.
            </summary>
            <param name="message">The hub message for the cache. This will be serialized with an <see cref="T:Microsoft.AspNetCore.SignalR.Protocol.IHubProtocol"/> in <see cref="M:Microsoft.AspNetCore.SignalR.SerializedHubMessage.GetSerializedMessage(Microsoft.AspNetCore.SignalR.Protocol.IHubProtocol)"/> to get the message's serialized representation.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.SerializedHubMessage.GetSerializedMessage(Microsoft.AspNetCore.SignalR.Protocol.IHubProtocol)">
            <summary>
            Gets the serialized representation of the <see cref="T:Microsoft.AspNetCore.SignalR.Protocol.HubMessage"/> using the specified <see cref="T:Microsoft.AspNetCore.SignalR.Protocol.IHubProtocol"/>.
            </summary>
            <param name="protocol">The protocol used to create the serialized representation.</param>
            <returns>The serialized representation of the <see cref="T:Microsoft.AspNetCore.SignalR.Protocol.HubMessage"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.SerializedMessage">
            <summary>
            Represents a serialized message.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.SerializedMessage.ProtocolName">
            <summary>
            Gets the protocol of the serialized message.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.SignalR.SerializedMessage.Serialized">
            <summary>
            Gets the serialized representation of the message.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.SerializedMessage.#ctor(System.String,System.ReadOnlyMemory{System.Byte})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.SignalR.SerializedMessage"/> class.
            </summary>
            <param name="protocolName">The protocol of the serialized message.</param>
            <param name="serialized">The serialized representation of the message.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.SignalR.SignalRConnectionBuilderExtensions">
            <summary>
            Extension methods for <see cref="T:Microsoft.AspNetCore.Connections.IConnectionBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.SignalRConnectionBuilderExtensions.UseHub``1(Microsoft.AspNetCore.Connections.IConnectionBuilder)">
            <summary>
            Configure the connection to host the specified <see cref="T:Microsoft.AspNetCore.SignalR.Hub"/> type.
            </summary>
            <typeparam name="THub">The <see cref="T:Microsoft.AspNetCore.SignalR.Hub"/> type to host on the connection.</typeparam>
            <param name="connectionBuilder">The connection to configure.</param>
            <returns>The same instance of the <see cref="T:Microsoft.AspNetCore.Connections.IConnectionBuilder"/> for chaining.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.SignalR.StreamTracker.AddStream(System.String,System.Type,System.Type)">
            <summary>
            Creates a new stream and returns the ChannelReader for it as an object.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.SignalRDependencyInjectionExtensions">
            <summary>
            Extension methods for <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.SignalRDependencyInjectionExtensions.AddSignalRCore(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds the minimum essential SignalR services to the specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />. Additional services
            must be added separately using the <see cref="T:Microsoft.AspNetCore.SignalR.ISignalRServerBuilder"/> returned from this method.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.SignalR.ISignalRServerBuilder"/> that can be used to further configure the SignalR services.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Internal.ClosedGenericMatcher">
            <summary>
            Helper related to generic interface definitions and implementing classes.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Internal.ClosedGenericMatcher.ExtractGenericInterface(System.Type,System.Type)">
            <summary>
            Determine whether <paramref name="queryType"/> is or implements a closed generic <see cref="T:System.Type"/>
            created from <paramref name="interfaceType"/>.
            </summary>
            <param name="queryType">The <see cref="T:System.Type"/> of interest.</param>
            <param name="interfaceType">The open generic <see cref="T:System.Type"/> to match. Usually an interface.</param>
            <returns>
            The closed generic <see cref="T:System.Type"/> created from <paramref name="interfaceType"/> that
            <paramref name="queryType"/> is or implements. <c>null</c> if the two <see cref="T:System.Type"/>s have no such
            relationship.
            </returns>
            <remarks>
            This method will return <paramref name="queryType"/> if <paramref name="interfaceType"/> is
            <c>typeof(KeyValuePair{,})</c>, and <paramref name="queryType"/> is
            <c>typeof(KeyValuePair{string, object})</c>.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.Internal.ObjectMethodExecutor.Execute(System.Object,System.Object[])">
            <summary>
            Executes the configured method on <paramref name="target"/>. This can be used whether or not
            the configured method is asynchronous.
            </summary>
            <remarks>
            Even if the target method is asynchronous, it's desirable to invoke it using Execute rather than
            ExecuteAsync if you know at compile time what the return type is, because then you can directly
            "await" that value (via a cast), and then the generated code will be able to reference the
            resulting awaitable as a value-typed variable. If you use ExecuteAsync instead, the generated
            code will have to treat the resulting awaitable as a boxed object, because it doesn't know at
            compile time what type it would be.
            </remarks>
            <param name="target">The object whose method is to be executed.</param>
            <param name="parameters">Parameters to pass to the method.</param>
            <returns>The method return value.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Internal.ObjectMethodExecutor.ExecuteAsync(System.Object,System.Object[])">
            <summary>
            Executes the configured method on <paramref name="target"/>. This can only be used if the configured
            method is asynchronous.
            </summary>
            <remarks>
            If you don't know at compile time the type of the method's returned awaitable, you can use ExecuteAsync,
            which supplies an awaitable-of-object. This always works, but can incur several extra heap allocations
            as compared with using Execute and then using "await" on the result value typecasted to the known
            awaitable type. The possible extra heap allocations are for:
            
            1. The custom awaitable (though usually there's a heap allocation for this anyway, since normally
               it's a reference type, and you normally create a new instance per call).
            2. The custom awaiter (whether or not it's a value type, since if it's not, you need a new instance
               of it, and if it is, it will have to be boxed so the calling code can reference it as an object).
            3. The async result value, if it's a value type (it has to be boxed as an object, since the calling
               code doesn't know what type it's going to be).
            </remarks>
            <param name="target">The object whose method is to be executed.</param>
            <param name="parameters">Parameters to pass to the method.</param>
            <returns>An object that you can "await" to get the method return value.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Internal.ObjectMethodExecutorAwaitable">
            <summary>
            Provides a common awaitable structure that <see cref="M:Microsoft.Extensions.Internal.ObjectMethodExecutor.ExecuteAsync(System.Object,System.Object[])"/> can
            return, regardless of whether the underlying value is a System.Task, an FSharpAsync, or an
            application-defined custom awaitable.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Internal.ObjectMethodExecutorFSharpSupport">
            <summary>
            Helper for detecting whether a given type is FSharpAsync`1, and if so, supplying
            an <see cref="T:System.Linq.Expressions.Expression"/> for mapping instances of that type to a C# awaitable.
            </summary>
            <remarks>
            The main design goal here is to avoid taking a compile-time dependency on
            FSharp.Core.dll, because non-F# applications wouldn't use it. So all the references
            to FSharp types have to be constructed dynamically at runtime.
            </remarks>
        </member>
    </members>
</doc>
