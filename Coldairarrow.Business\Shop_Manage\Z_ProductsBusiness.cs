﻿using Coldairarrow.Entity.Shop_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System.IO;
using System.Net.Security;
using System.Net;
using System.Text;
using System;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Coldairarrow.Entity.Shop_Manage.Model;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.IBusiness;
using System.Reflection.Emit;
using Coldairarrow.Util.UEditor;
using NPOI.Util;
using static Coldairarrow.Entity.Shop_Manage.Model.AliJDResultModel;
using static org.apache.zookeeper.ZooDefs;
using NPOI.XSSF.Model;
using Aliyun.Base.xiaoxizn;
using static Coldairarrow.Entity.Shop_Manage.Enum.ShopEnum;
using Microsoft.EntityFrameworkCore.Internal;

namespace Coldairarrow.Business.Shop_Manage
{
    public class Z_ProductsBusiness : BaseBusiness<Z_Products>, IZ_ProductsBusiness, ITransientDependency
    {
        readonly IConfiguration _configuration;
        readonly IZ_ProductsDetailBusiness _z_ProductsDetailBusiness;
        IOperator _operator;
        public Z_ProductsBusiness(IDbAccessor db, IConfiguration configuration, IOperator @operator, IZ_ProductsDetailBusiness z_ProductsDetailBusiness)
            : base(db)
        {
            _configuration = configuration;
            _operator = @operator;
            _z_ProductsDetailBusiness = z_ProductsDetailBusiness;
        }

        #region 外部接口

        public async Task<PageResult<Z_Products>> GetDataListAsync(PageInput<ProductConditinDto> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Z_Products>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Z_Products, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            if (search.isView.HasValue)
            {
                where = where.And(x => x.F_IsView == (YesOrNo)search.isView);
            }
            if (!search.searchValue.IsNullOrWhiteSpace())
            {
                where = where.And(x => x.F_Name.Contains(search.searchValue));
            }
            if (!search.typeId.IsNullOrWhiteSpace())
            {
                where = where.And(x => x.F_Type == search.typeId);
            }
            var pageResult = await q.Where(where).AsNoTracking().GetPageResultAsync(input);
            var productsTypes = this.Db.GetIQueryable<Z_ProductsType>();
            if (pageResult.Success && pageResult.Data.Count > 0 && productsTypes.Count() > 0)
            {
                pageResult.Data.ForEach((item) =>
                {
                    item.F_TypeName = !item.F_Type.IsNullOrWhiteSpace() ? productsTypes.Where(x => x.F_Id == item.F_Type).FirstOrDefault()?.F_Name : "";
                    // 获取当前时间的Ticks作为时间戳
                    long timestamp = DateTime.Now.Ticks;

                    // 构建带有时间戳的图片URL
                    item.F_Img = $"{item.F_Img}?timestamp={timestamp}";
                });
                //var list=pageResult.Data.AsQueryable();
                // pageResult =  list.AsNoTracking().GetPageResult(input);
            }
            return pageResult;
        }

        public async Task<PageResult<Z_Products>> GetDataListByAdressAsync(PageInput<ProductConditinDto> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Z_Products>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Z_Products, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            if (search.isView.HasValue)
            {
                where = where.And(x => x.F_IsView == (YesOrNo)search.isView);
            }
            if (!search.searchValue.IsNullOrWhiteSpace())
            {
                where = where.And(x => x.F_Name.Contains(search.searchValue));
            }
            if (!search.typeId.IsNullOrWhiteSpace())
            {
                where = where.And(x => x.F_Type == search.typeId);
            }
            var pageResult = await q.Where(where).AsNoTracking().GetPageResultAsync(input);
            var productsTypes = this.Db.GetIQueryable<Z_ProductsType>();
            if (pageResult.Success && pageResult.Data.Count > 0 && productsTypes.Count() > 0)
            {
                pageResult.Data.ForEach((item) =>
                {
                    item.F_TypeName = !item.F_Type.IsNullOrWhiteSpace() ? productsTypes.Where(x => x.F_Id == item.F_Type).FirstOrDefault()?.F_Name : "";
                });
                //var list=pageResult.Data.AsQueryable();
                // pageResult =  list.AsNoTracking().GetPageResult(input);
            }
            return pageResult;
        }
        public async Task<Z_Products> GetTheDataAsync(string id)
        {
            var z_Products = await GetEntityAsync(id);
            if (z_Products != null)
            {
                z_Products.PDetail = this.Db.GetIQueryable<Z_ProductsDetail>().Where(x => x.F_ProductId.Equals(z_Products.F_Id)).ToList();
                if (z_Products.PDetail.Count > 0)
                {
                    z_Products.PDetail.ForEach(item =>
                    {
                        // 获取当前时间的Ticks作为时间戳
                        long timestamp = DateTime.Now.Ticks;
                        item.F_Str = $"{item.F_Str}?timestamp={timestamp}";
                    });
                }
            }
            return z_Products;
        }

        /// <summary>
        /// 通过京东商品链接获取商品详情
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        /// 

        public async Task<JD_Result_ZProducts> GetJDProductInfoByUrl(string url)
        {
            //解析链接获取商品id 
            var id = System.Text.RegularExpressions.Regex.Replace(url, @"[^0-9]+", "");
            JD_Result_ZProducts jD_Result_ZProducts = new JD_Result_ZProducts()
            {
                isSucccess = false,
                erroResult = "",
                z_Products = new Z_Products()

            };
            if (!string.IsNullOrWhiteSpace(id))
            {
                //登录写入日志
                var log = new Base_UserLog
                {
                    Id = IdHelper.GetId(),
                    CreateTime = DateTime.Now,
                    CreatorId = _operator.UserId ?? "superAdmin",
                    CreatorRealName = _operator.RealName ?? "超级管理员",
                    LogContent = "获取京东商品链接接口",
                    JsonContent = url,
                };
                var apiKey = _configuration["JDWB:apiKey"].ToString();
                var apiSecret = _configuration["JDWB:apiSecret"].ToString();
                var appcode = _configuration["JDWB:appcode"].ToString();
                var callBackUrl = _configuration["JDWB:callBackUrl"].ToString();
                // 请求示例 url 默认请求参数已经做URL编码
                //string requestUrl = "https://jmjdspxqts.market.alicloudapi.com/jd/goods/request";
                Dictionary<string, object> parDict = new Dictionary<string, object>
                {
                    { "goodsId", id },
                    { "notifyUrl",callBackUrl}
                };
                Dictionary<string, string> headerDict = new Dictionary<string, string>
                {
                    { "Authorization", "APPCODE" + appcode }
                };
                var backData = HttpHelper.JDPostData(appcode, id, callBackUrl);
                if (!backData.IsNullOrEmpty())
                {
                    log.ResultJson = backData;
                    var JDResultRoot = JsonConvert.DeserializeObject<AliJDResultModel.JDResultRoot>(backData);
                    //接收成功
                    if (JDResultRoot.code == 200 && JDResultRoot.success == "true")
                    {
                        //jD_Result_ZProducts.isSucccess = true;
                        //var others = JDResultRoot.data.data.wareData.others;
                        //Z_Products z_Products = new Z_Products()
                        //{
                        //    F_Name = others.wareInfo.name,
                        //    F_SubName = others.wareInfo.name,
                        //    F_Img = others.property.chatUrl,
                        //    F_Price = (decimal)others.priceInfo.jprice.ToDouble(),
                        //    F_Type = "",
                        //    F_DescShort = "",
                        //    F_Sales = 0,
                        //    F_JDId = id,
                        //    F_JDUrl = url,
                        //    F_Describe = "",
                        //    PDetail = new List<Z_ProductsDetail>()
                        //};
                        ////商品图片
                        //if (others.wareImage.Count > 0)
                        //{
                        //    others.wareImage.ForEach(img =>
                        //    {
                        //        //商品封面
                        //        z_Products.PDetail.Add(new Z_ProductsDetail()
                        //        {
                        //            F_Str = img.small
                        //        });
                        //    });

                        //}
                        //jD_Result_ZProducts.z_Products = z_Products;
                    }
                    //接收失败
                    else
                    {
                        jD_Result_ZProducts.erroResult = JDResultRoot.msg;
                    }
                }
                _operator.WriteUserLog(log);
                return jD_Result_ZProducts;
            }
            else
            {
                return jD_Result_ZProducts;
            }
        }


        public async Task SaveJdProductInfo(JdCallBackDTO input)
        {
            //判断是否存在该商品id 
            var oldProduct = this.GetIQueryable().FirstOrDefaultAsync(x => x.F_JDId == input.goodsId);
            if (oldProduct != null)
            {
                //更新
            }
            else
            {
                //新增
                var mainItem = input.data.item.item;
                var mainInfo = input.data.info;
                Z_Products z_Products = new Z_Products()
                {
                    F_Name = mainInfo.shareInfo.title,
                    F_SubName = mainInfo.shareInfo.shortTitle,
                    F_Img = "",
                    F_Price = (decimal)mainInfo.priceFloor.price.ToDouble(),
                    F_Type = "",
                    F_DescShort = "",
                    F_Sales = 1,
                    F_JDId = input.goodsId,
                    F_JDUrl = $"https://item.jd.com/{input.goodsId}.html",
                    F_Describe = "",
                    PDetail = new List<Z_ProductsDetail>()
                };
                this.InitEntity(z_Products, _operator);
                //商品图片
                if (mainItem.image.Count > 0)
                {
                    mainItem.image.ForEach(img =>
                    {
                        //商品封面
                        z_Products.PDetail.Add(new Z_ProductsDetail()
                        {
                            F_Str = "https://img10.360buyimg.com/umm/" + img,
                        });
                    });

                }
            }

        }

        //[Transactional]
        public async Task AddDataAsync(Z_Products data)
        {
            //查询数据库是否有相同的京东商品编号
            if (!data.F_JDId.IsNullOrWhiteSpace())
            {
                if (await GetIQueryable().AnyAsync(x => x.F_JDId == data.F_JDId))
                {
                    throw new Exception("该商品链接已存在！");
                }
            }
            this.InitEntity(data, _operator);
            await InsertAsync(data);
            //新增图片
            if (data.PDetail != null && data.PDetail.Count > 0)
            {
                data.PDetail.ForEach(item =>
                {
                    InitEntity(item, _operator);
                    item.F_ProductId = data.F_Id;
                });
                await Task.Run(() => this.Db.BulkInsert(data.PDetail));
            }
        }

        //[Transactional]
        public async Task UpdateDataAsync(Z_Products data)
        {
            await UpdateAsync(data);
            //删除详情图片
            var productsDetails = this.Db.GetIQueryable<Z_ProductsDetail>().Where(x => x.F_ProductId == data.F_Id).ToList();
            if (productsDetails.Count > 0)
            {
                await this.Db.DeleteAsync(productsDetails);
            }
            //新增图片
            if (data.PDetail != null && data.PDetail.Count > 0)
            {
                data.PDetail.ForEach(item =>
                {
                    item.F_Str = !item.F_Str.IsNullOrWhiteSpace() ? item.F_Str.Split('?')[0] : "";
                    InitEntity(item, _operator);
                    item.F_ProductId = data.F_Id;
                });
                await Task.Run(() => this.Db.BulkInsert(data.PDetail));
            }
        }

        public async Task AddDataAsync2(Z_Products data)
        {
            await InsertAsync(data);
        }
        public async Task UpdateDataAsync2(Z_Products data)
        {
            await UpdateAsync(data);
        }

        public async Task UpdateDataAsyncById(Z_Products data)
        {
            data.F_Img= !data.F_Img.IsNullOrWhiteSpace() ? data.F_Img.Split('?')[0] : "";
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            var z_Products = await this.GetIQueryable().Where(x => ids.Contains(x.F_Id)).AsNoTracking().ToListAsync();
            //if (z_Products.Count() > 0)
            //{
            //    z_Products.ForEach
            //}
            await DeleteAsync(ids);
            //删除详情图片
            var productsDetails = this.Db.GetIQueryable<Z_ProductsDetail>().Where(x => ids.Contains(x.F_ProductId)).ToList();
            if (productsDetails.Count > 0)
            {
                await this.Db.DeleteAsync(productsDetails);
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<List<Z_Products>> GetDataByIdsAsync(List<string> ids)
        {
            return GetIQueryable().Where(x => ids.Contains(x.F_Id)).ToList();
        }
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Z_Products>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Z_Products, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<Z_Products, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}