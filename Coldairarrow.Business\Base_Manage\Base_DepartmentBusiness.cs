﻿using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

namespace Coldairarrow.Business.Base_Manage
{
    public class Base_DepartmentBusiness : BaseBusiness<Base_Department>, IBase_DepartmentBusiness, ITransientDependency
    {
        public Base_DepartmentBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<List<Base_DepartmentTreeDTO>> GetTreeDataListAsync(DepartmentsTreeInputDTO input)
        {
            var where = LinqHelper.True<Base_Department>();
            if (!input.parentId.IsNullOrEmpty())
                where = where.And(x => x.ParentId == input.parentId);
            if (!input.companyId.IsNullOrEmpty())
                where = where.And(x => x.F_CompanyId == input.companyId);
            var list = await GetIQueryable().Where(where).ToListAsync();
            var treeList = list
                .Select(x => new Base_DepartmentTreeDTO
                {
                    Id = x.Id,
                    ParentId = x.ParentId,
                    Text = x.Name,
                    Value = x.Id
                }).ToList();

            return TreeHelper.BuildTree(treeList);
        }
        public async Task<List<Base_DepartmentTreeDTO>> GetTreeByCompanyDataList(DepartmentsTreeInputDTO input)
        {
            // 获取所有公司数据
            var companyList = await GetCompanyListAsync();

            // 获取所有部门数据，并关联公司信息
            var departmentList = await GetDepartmentListAsync(companyList);

            // 构建最终的树形结构
            var treeList = BuildTreeWithCompaniesAndDepartments(companyList, departmentList);

            return treeList;
        }

        private async Task<List<Base_Company>> GetCompanyListAsync()
        {
            var where = LinqHelper.True<Base_Company>();
            var list = await this.Db.GetIQueryable<Base_Company>().Where(where).ToListAsync();
            return list;
        }

        private async Task<List<Base_Department>> GetDepartmentListAsync(List<Base_Company> companyList)
        {
            var where = LinqHelper.True<Base_Department>();
            if (companyList.Any())
            {
                var companyIds = companyList.Select(c => c.F_Id).ToList();
                where = where.And(x => companyIds.Contains(x.F_CompanyId));
            }

            var list = await this.Db.GetIQueryable<Base_Department>().Where(where).ToListAsync();
            return list;
        }

        private List<Base_DepartmentTreeDTO> BuildTreeWithCompaniesAndDepartments(List<Base_Company> companyList, List<Base_Department> departmentList)
        {
            // 将公司数据转换为 DTO
            var companyDtos = companyList
                .Select(x => new Base_DepartmentTreeDTO
                {
                    Id = x.F_Id,
                    ParentId = x.F_ParentId , // 如果 F_ParentId 为空，则使用 F_CompanyId
                    Text = x.F_FullName,
                    Value = x.F_Id,
                    Type = "company"
                })
                .ToList();

            // 将部门数据转换为 DTO 并关联公司信息
            var departmentDtos = departmentList
                .Select(x => new Base_DepartmentTreeDTO
                {
                    Id = x.Id,
                    ParentId = x.ParentId ?? x.F_CompanyId, // 如果 F_ParentId 为空，则使用 F_CompanyId
                    Text = x.Name,
                    Value = x.Id,
                    Type = "department",
                    CompanyId = x.F_CompanyId,
                    CompanyName = companyList.FirstOrDefault(c => c.F_Id == x.F_CompanyId)?.F_FullName
                })
                .ToList();

            // 合并公司和部门数据
            var combinedList = companyDtos.Concat(departmentDtos).ToList();

            // 构建树形结构
            return TreeHelper.BuildTree(combinedList);
        }
        public List<Base_DepartmentTreeDTO> GetTreeDataList(PageInput<ConditionDTO> input)
        {
            var where = LinqHelper.True<Base_Department>();
            //if (!input.parentId.IsNullOrEmpty())
            //    where = where.And(x => x.ParentId == input.parentId);
            var search = input.Search;
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Base_Department, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            var list = this.GetIQueryable().Where(where).ToList();
            var treeList = list
                .Select(x => new Base_DepartmentTreeDTO
                {
                    Id = x.Id,
                    ParentId = x.ParentId,
                    Text = x.Name,
                    Value = x.Id
                }).ToList();

            return TreeHelper.BuildTree(treeList);
        }
        /// <summary>
        /// 通过递归获取他的所有下级
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public List<string> GetDepList(string id)
        {
            List<string> DepIds = new List<string>();
            if (!id.IsNullOrEmpty())
            {
                DepIds.Add(id);
                var parentIds = this.GetIQueryable().Where(i => i.ParentId == id).Select(i => i.Id).ToList();
                if (parentIds.Count > 0)
                {
                    //DepIds.AddRange(parentIds);
                    foreach (var item in parentIds)
                    {
                        DepIds.AddRange(GetDepList(item));
                    }
                }
                else
                {
                    DepIds.Add(id);
                }
            }
            return DepIds;
        }
        /// <summary>
        /// 获取这个子部门内下所有部门信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public List<string> GetDetListByNode(string id)
        {
            var list = new List<string>();
            var department = GetRootDept(id);
            if (department != null)
            {
                list = GetDepList(department.Id);
            }
            return list;
        }
        /// <summary>
        /// 获取一级部门
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Base_Department GetRootDept(string id)
        {
            Base_Department retDept = new Base_Department();
            if (!id.IsNullOrEmpty())
            {
                retDept = GetEntity(id);
                if (retDept != null)
                {
                    while (!string.IsNullOrEmpty(retDept.ParentId))
                    {
                        retDept = GetEntity(retDept.ParentId);
                        if (retDept == null)
                        {
                            break;
                        }
                    }
                }
            }

            return retDept;
        }
        public async Task<List<Base_Department>> GetDataListAsync()
        {
            return await GetIQueryable().ToListAsync();
        }

        public async Task<List<string>> GetChildrenIdsAsync(string departmentId)
        {
            var allNode = await GetIQueryable().Select(x => new TreeModel
            {
                Id = x.Id,
                ParentId = x.ParentId,
                Text = x.Name,
                Value = x.Id
            }).ToListAsync();

            var children = TreeHelper
                .GetChildren(allNode, allNode.Where(x => x.Id == departmentId).FirstOrDefault(), true)
                .Select(x => x.Id)
                .ToList();

            return children;
        }

        public async Task<Base_Department> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        public Base_Department GetTheData(string id)
        {
            return GetEntity(id);
        }
        [DataAddLog(UserLogType.部门管理, "Name", "部门名")]
        public async Task AddDataAsync(Base_Department newData)
        {
            await InsertAsync(newData);
        }
        [DataEditLog(UserLogType.部门管理, "Name", "部门名")]
        public async Task UpdateDataAsync(Base_Department theData)
        {
            await UpdateAsync(theData);
        }

        [DataDeleteLog(UserLogType.部门管理, "Name", "部门名")]
        public async Task DeleteDataAsync(List<string> ids)
        {
            if (await GetIQueryable().AnyAsync(x => ids.Contains(x.ParentId)))
                throw new BusException("禁止删除！请先删除所有子级！");

            await DeleteAsync(ids);
        }

        #endregion
    }
}