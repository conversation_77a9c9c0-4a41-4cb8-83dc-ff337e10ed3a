﻿using Coldairarrow.Business.Base_Business;
using Coldairarrow.Entity.Base_Business;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Base_Business
{
    [Route("/Base_Business/[controller]/[action]")]
    public class Tencent_WeekRankController : BaseApiController
    {
        #region DI

        public Tencent_WeekRankController(ITencent_WeekRankBusiness tencent_WeekRankBus)
        {
            _tencent_WeekRankBus = tencent_WeekRankBus;
        }

        ITencent_WeekRankBusiness _tencent_WeekRankBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Tencent_WeekRank>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _tencent_WeekRankBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Tencent_WeekRank> GetTheData(IdInputDTO input)
        {
            return await _tencent_WeekRankBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Tencent_WeekRank data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _tencent_WeekRankBus.AddDataAsync(data);
            }
            else
            {
                await _tencent_WeekRankBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _tencent_WeekRankBus.DeleteDataAsync(ids);
        }

        #endregion
    }
}