<template>
  <div class="editable-cell">
    <!-- <div v-if="editable" class="editable-cell-input-wrapper">

      
    </div>
    <div v-else class="editable-cell-text-wrapper">
      {{ value || ' ' }}
      <a-icon type="edit" class="editable-cell-icon" @click="edit" />
    </div> -->
    <div class="editable-cell-input-wrapper">
      <a-input :value="value" @change="handleChange" @pressEnter="check" />
      <a-icon type="check" class="editable-cell-icon-check" @click="check" />
    </div>

  </div>
</template>
<script>
export default {
  props: {
    text: String,
  },
  data () {
    return {
      value: this.text,
      editable: true,
    }
  },
  methods: {
    handleChange (e) {
      const value = e.target.value
      this.value = value
      // this.$emit('change', this.value)
    },
    check () {
      // this.editable = false
      this.$emit('change', this.value)
    },
    edit () {
      this.editable = true
    },
  },
}
</script>

