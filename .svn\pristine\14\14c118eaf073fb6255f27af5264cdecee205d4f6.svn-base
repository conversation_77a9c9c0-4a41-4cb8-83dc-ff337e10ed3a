﻿<template>
  <a-modal
    :title="title"
    width="60%"
    :visible="visible"
    :confirmLoading="loading"
    @cancel="
      () => {
        this.visible = false
      }
    "
  >
    <template v-if="!isEdit" slot="footer">
      <a-button :loading="loading" @click="save(0)" icon="edit">
        保存草稿
      </a-button>
      <a-button :loading="loading" @click="save(5)" icon="check-square">
        提交生效
      </a-button>
      <a-button
        key="cancel"
        :loading="loading"
        @click="
          () => {
            this.visible = false
          }
        "
        icon="close"
      >
        取消
      </a-button>
    </template>
    <template v-else slot="footer">
      <a-button
        key="cancel"
        icon="close"
        :loading="loading"
        @click="
          () => {
            this.visible = false
          }
        "
      >
        关闭
      </a-button>
    </template>
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="entity" :rules="rules">
        <a-row :gutter="24">
          <a-divider orientation="left">招聘计划</a-divider>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="招聘名称" prop="F_RecruitId" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-input-search
                  v-model="entity.F_RecruitName"
                  :disabled="isEdit"
                  placeholder="请选择招聘信息"
                  style="width: 100%"
                  @search="onSearch"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item
                label="招聘开始时间"
                prop="F_StartTime"
                :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 14 }"
              >
                <a-date-picker
                  v-model="entity.F_StartTime"
                  :disabled-date="disabledStartDate"
                  :disabled="isEdit"
                  type="date"
                  format="YYYY-MM-DD"
                  placeholder="请选择开始时间"
                  style="width: 100%;"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="招聘结束时间"
                prop="F_EndTime"
                :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 14 }"
              >
                <a-date-picker
                  v-model="entity.F_EndTime"
                  :disabled-date="disabledEndDate"
                  :disabled="isEdit"
                  type="date"
                  format="YYYY-MM-DD"
                  placeholder="请选择结束时间"
                  style="width: 100%;"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="备注" :labelCol="{ span: 3 }" :wrapperCol="{ span: 19 }">
                <a-textarea v-model="entity.F_Remark" :disabled="isEdit" placeholder="请输入" :rows="4" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-descriptions title="招聘信息" bordered>
            <a-descriptions-item label="招聘公司名称">
              {{ entityRecruit.F_RecruitName }}
            </a-descriptions-item>
            <a-descriptions-item label="招聘岗位">
              {{ entityRecruit.F_Role }}
            </a-descriptions-item>
            <a-descriptions-item label="经验">
              {{ entityRecruit.F_Experience }}
            </a-descriptions-item>
            <a-descriptions-item label="城市">
              {{ entityRecruit.F_City }}
            </a-descriptions-item>
            <a-descriptions-item label="学历">
              {{ entityRecruit.F_Education }}
            </a-descriptions-item>
            <a-descriptions-item label="薪酬待遇">
              {{ entityRecruit.F_Salary }}
            </a-descriptions-item>
            <a-descriptions-item label="招聘介绍" :span="3">
              {{ entityRecruit.F_Achieve }}
            </a-descriptions-item>
            <a-descriptions-item label="岗位要求" :span="3">
              {{ entityRecruit.F_Content }}
            </a-descriptions-item>
            <a-descriptions-item label="工作地点" :span="3">
              {{ entityRecruit.F_Address }}
            </a-descriptions-item>
          </a-descriptions>
        </a-row>
      </a-form-model>
    </a-spin>
    <SelectRecruit ref="selectRecruit" :callBack="SeletedRecruit"></SelectRecruit>
  </a-modal>
</template>

<script>
import moment from 'moment'
import SelectRecruit from '@/components/SelectRecruit/Index'
export default {
  components: {
    SelectRecruit
  },
  props: {
    parentObj: Object
  },
  data() {
    return {
      layout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 18 }
      },
      visible: false,
      loading: false,
      entity: {},
      entityRecruit: {},
      rules: {
        F_RecruitId: [{ required: true, message: '请选择招聘信息', trigger: 'blur' }],
        F_StartTime: [{ required: true, message: '请选择开始时间', trigger: 'blur' }],
        F_EndTime: [{ required: true, message: '请选择结束时间', trigger: 'blur' }]
      },
      isEdit: false,
      title: ''
    }
  },
  methods: {
    SeletedRecruit(model) {
      console.log(model)
      this.entityRecruit = model[0]
      this.entity.F_RecruitName = model[0].F_RecruitName
      this.entity.F_RecruitId = model[0].F_Id
    },
    disabledStartDate(startValue) {
      const endValue = this.entity.F_EndTime
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate(endValue) {
      const startValue = this.entity.F_StartTime
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    onSearch() {
      this.$refs.selectRecruit.openForm('选择招聘信息')
    },
    init() {
      this.visible = true
      this.entity = {
        F_RecruitName: ''
      }
      this.entityRecruit = {}
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    openForm(id, title, isEdit) {
      this.title = title
      this.isEdit = isEdit
      this.init()

      if (id) {
        this.loading = true
        this.$http.post('/HR_Manage/HR_RecruitPlan/GetRecruitPlan', { id: id }).then(resJson => {
          this.loading = false
          if (resJson && resJson.Data) {
            if (resJson.Data.F_StartTime) {
              resJson.Data.F_StartTime = moment(resJson.Data.F_StartTime)
            }
            if (resJson.Data.F_EndTime) {
              resJson.Data.F_EndTime = moment(resJson.Data.F_EndTime)
            }
            this.entity = resJson.Data
            this.entityRecruit = this.entity.Recruit
            this.entity.F_RecruitName = this.entityRecruit.F_RecruitName
          }
        })
      }
    },
    //保存草稿
    save(state) {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.loading = true
        this.entity.F_WFState = state
        this.$http.post('/HR_Manage/HR_RecruitPlan/SaveData', this.entity).then(resJson => {
          this.loading = false

          if (resJson.Success) {
            this.$message.success('操作成功!')
            this.visible = false

            this.parentObj.getDataList()
          } else {
            this.$message.error(resJson.Msg)
          }
        })
      })
    }
  }
}
</script>
