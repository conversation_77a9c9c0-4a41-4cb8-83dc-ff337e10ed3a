﻿using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Microsoft.AspNetCore.Authorization;
using System.Threading;
using Microsoft.Extensions.Logging;

namespace Coldairarrow.Api.Controllers.Base_Manage
{
    [Route("/Base_Manage/[controller]/[action]")]
    public class Base_IpLockController : BaseApiController
    {
        #region DI
        private readonly ILogger _logger;
        public Base_IpLockController(IBase_IpLockBusiness base_IpLockBus, ILogger<GlobalExceptionFilter> logger)
        {
            _base_IpLockBus = base_IpLockBus;
            _logger= logger;
        }

        IBase_IpLockBusiness _base_IpLockBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Base_IpLock>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _base_IpLockBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Base_IpLock> GetTheData(IdInputDTO input)
        {
            return await _base_IpLockBus.GetTheDataAsync(input.id);
        }
        /// <summary>
        /// 更新深信服Ip组
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NoCheckJWT]
        public async Task<IActionResult> UpdateSXFIpGroups()
        {
            await _base_IpLockBus.UpdateSXFIpGroups();
            return Ok("成功");
        }
        /// <summary>
        /// 获取恶意ip
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task GetMaliciousIp()
        {
            await _base_IpLockBus.GetMaliciousIpAsync();
        }
        /// <summary>
        /// 封禁恶意ip
        /// </summary>
        /// <returns></returns>
        //[HttpPost]
        //[NoCheckJWT]
        //public async Task blockIp()
        //{
        //    await _base_IpLockBus.BlockIpAsync();
        //}
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1); // 初始值为 1，表示只有一个
        /// <summary>
        /// 封禁恶意ip
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NoCheckJWT]
        public async Task<IActionResult> BlockAllIp()
        {
            // 尝试获取锁，如果锁被占用，则直接返回
            if (!await _semaphore.WaitAsync(TimeSpan.FromMinutes(2)))
            {
                return Ok("已有任务正在执行，请稍后再试。");
            }

            try
            {
                // 启动一个后台任务来执行封禁操作
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _base_IpLockBus.BlockAllIpAsync();
                    }
                    catch (Exception ex)
                    {
                        // 记录异常日志
                        _logger.LogError(ex, "封禁IP时发生错误");
                    }
                    finally
                    {
                        // 释放锁
                        _semaphore.Release();
                    }
                });

                // 立即返回成功响应
                return Ok("封禁IP任务已启动，请稍后查看结果。");
            }
            catch (Exception ex)
            {
                // 如果发生异常，释放锁并返回错误响应
                _semaphore.Release();
                _logger.LogError(ex, "启动封禁IP任务时发生错误");
                return StatusCode(500, "启动封禁IP任务时发生错误");
            }
        }
        /// <summary>
        /// 获取阿里云防护列表（定时）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task GetAliYunProtectionList()
        {
            await _base_IpLockBus.GetAliYunProtectionList();
        }

        /// <summary>
        /// 判断ip是否在黑名单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NoCheckJWT]
        public async Task<dynamic> GetMaliciousIpInfo(string ip)
        {
            return await _base_IpLockBus.GetMaliciousIpInfoAsync(ip);
        }

        /// <summary>
        /// 新增恶意ip记录
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task AddMaliciousIp(Base_IpLock input)
        {
            await _base_IpLockBus.AddMaliciousIpAsync(input);
        }

        /// <summary>
        /// 新增恶意ip记录
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NoCheckJWT]
        public async Task AddMaliciousIp(string ip)
        {
            if (!string.IsNullOrWhiteSpace(ip))
            {
                await _base_IpLockBus.AddMaliciousIpAsync(new Base_IpLock { F_IP = ip, F_System = "黑名单手动上传封禁" });
            }

        }
        /// <summary>
        /// 新增恶意ip记录
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task UpdateMaliciousIpState(Base_IpLock input)
        {
            await _base_IpLockBus.UpdateMaliciousIpState(input, null) ;
        }
        /// <summary>
        /// 批量新增ip
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task<List<string>> SaveBathLockIp(string ips)
        {
            if (!string.IsNullOrWhiteSpace(ips))
            {
                return await _base_IpLockBus.SaveBatchLockIpAsync(ips);

            }
            else
            {
                throw new Exception("ip不能为空");
            }
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Base_IpLock data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _base_IpLockBus.AddDataAsync(data);
            }
            else
            {
                await _base_IpLockBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _base_IpLockBus.DeleteDataAsync(ids);
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _base_IpLockBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Base_IpLock>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _base_IpLockBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}