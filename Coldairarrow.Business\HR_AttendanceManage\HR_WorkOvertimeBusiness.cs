﻿using Coldairarrow.Entity.HR_AttendanceManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System;
using System.Linq.Expressions;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util.Helper;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity;
using Microsoft.Extensions.Configuration;
using Coldairarrow.IBusiness;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Coldairarrow.Business.HR_AttendanceManage
{
    public class HR_WorkOvertimeBusiness : BaseBusiness<HR_WorkOvertime>, IHR_WorkOvertimeBusiness, ITransientDependency
    {
        public HR_WorkOvertimeBusiness(IDbAccessor db, IConfiguration configuratio, IOperator @operator)
            : base(db)
        {
            _configuration = configuratio;
            _operator = @operator;
        }
        IOperator _operator;
        IConfiguration _configuration;
        //private string formId = "-412";
        #region 外部接口

        public async Task<PageResult<HR_WorkOvertimeDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_FormalEmployees, HR_WorkOvertime, HR_Positive, Base_Company, HR_WorkOvertimeGroup, HR_WorkOvertimeDTO>> select = (a1, d, c, e, h) => new HR_WorkOvertimeDTO
            {
                F_Id = d.F_Id,
                EmployeesCode = a1.EmployeesCode,
                F_UserName = d.F_UserName,
                F_PositiveOrg = c.F_PositiveOrg,
                F_PositivePosition = c.F_PositivePosition,
                F_WorkODate = d.F_WorkODate,
                F_WorkOStartTime = d.F_WorkOStartTime,
                F_WorkOEndTime = d.F_WorkOEndTime,
                F_RestTime = d.F_RestTime,
                F_WorkOTime = d.F_WorkOTime,
                F_WorkOReason = d.F_WorkOReason,
                F_WorkOType = d.F_WorkOType,
                F_IsManyPeopleWork = h.F_IsManyPeopleWork,
                F_WFState = d.F_WFState,
                F_WFId = d.F_WFId,
                Id = h.F_Id,
                F_GroupId = d.F_GroupId
            };
            select = select.BuildExtendSelectExpre();
            var where = LinqHelper.True<HR_WorkOvertimeDTO>();
            var search = input.Search;
            var q = from e in this.Db.GetIQueryable<HR_WorkOvertime>().AsExpandable()
                    join a1 in this.Db.GetIQueryable<HR_FormalEmployees>() on e.F_UserId equals a1.F_Id into form
                    from a1 in form.DefaultIfEmpty()
                    join d in this.Db.GetIQueryable<HR_Positive>() on e.F_UserId equals d.F_UserId into dept
                    from d in dept.DefaultIfEmpty()
                    join p in this.Db.GetIQueryable<Base_Company>() on e.F_CompanyId equals p.F_Id into Company
                    from p in Company.DefaultIfEmpty()
                    join w in this.Db.GetIQueryable<HR_WorkOvertimeGroup>() on e.F_GroupId equals w.F_Id into Overtime
                    from O in Overtime.DefaultIfEmpty()
                    select @select.Invoke(a1, e, d, p, O);

            //var q = GetIQueryable();
            //var q = await ExecuteSqlList("select t.*,f.EmployeesCode,p.F_PositiveOrg,p.F_PositivePosition FROM HR_WorkOvertime t LEFT JOIN HR_FormalEmployees f on t.F_UserId=f.F_Id LEFT JOIN HR_Positive p on t.F_UserId=p.F_UserId ");
            //var where = LinqHelper.True<HR_WorkOvertimeDTO>();
            //var search = input.Search;
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_WorkOvertimeDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_WorkOvertime> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        /// <summary>
        /// 获取单人加班表单数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<HR_WorkOvertimeDTO> GetFormDataAsync(string id)
        {
            HR_WorkOvertimeDTO hR_WorkOvertime = new HR_WorkOvertimeDTO();
            var workOvertime = await GetTheDataAsync(id);
            if (workOvertime != null && !string.IsNullOrWhiteSpace(workOvertime.F_UserId))
            {
                hR_WorkOvertime = MapHelper.Mapping<HR_WorkOvertimeDTO, HR_WorkOvertime>(workOvertime);
                hR_WorkOvertime.F_CompanyName = !string.IsNullOrWhiteSpace(hR_WorkOvertime.F_CompanyId) ?
                                this.Db.GetEntity<Base_Company>(hR_WorkOvertime.F_CompanyId)?.F_FullName : "";
                hR_WorkOvertime.EmployeesCode = this.Db.GetEntity<HR_FormalEmployees>(workOvertime.F_UserId)?.EmployeesCode;
                var positive = this.Db.GetIQueryable<HR_Positive>().Where(i => i.F_UserId == workOvertime.F_UserId).FirstOrDefault();
                if (positive != null)
                {
                    hR_WorkOvertime.F_PositiveOrg = positive.F_PositiveOrg;
                    hR_WorkOvertime.F_PositivePosition = positive.F_PositivePosition;
                }
                //hR_WorkOvertime.Id = this.Db.GetIQueryable<HR_WorkOvertimeGroup>().FirstOrDefault(i => i.F_Id == hR_WorkOvertime.F_GroupId).F_Id;
            }

            return hR_WorkOvertime;
        }

        /// <summary>
        /// 根据分组id获取加班所有数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<WorkOverTimeGroup> GetOverTimeGroup(string id)
        {
            WorkOverTimeGroup workOverTimeGroup = new WorkOverTimeGroup();
            if (!id.IsNullOrEmpty())
            {
                await Task.Run(() =>
                 {
                     var workOvertimeGroup = this.Db.GetIQueryable<HR_WorkOvertimeGroup>()
                         .FirstOrDefault(i => i.F_Id == id);
                     if (workOvertimeGroup != null)
                     {
                         var groupDTO = MapHelper.Mapping<HR_WorkOvertimeGroupDTO, HR_WorkOvertimeGroup>
                                                                 (workOvertimeGroup);
                         workOverTimeGroup.hR_WorkOvertimeGroup = groupDTO;
                         workOverTimeGroup.hR_WorkOvertimeGroup.ApplyRemark = groupDTO.F_Remark != null ? groupDTO.F_Remark : "";
                         workOverTimeGroup.hR_WorkOvertimeGroup.ApplyTime = groupDTO?.F_CreateDate;
                         workOverTimeGroup.hR_WorkOvertimeGroup.ApplyUserName = !groupDTO.F_CreateUserName.IsNullOrEmpty() ? groupDTO.F_CreateUserName : "";
                         //查询所有员工
                         var workOvertimes = this.Db.GetIQueryable<HR_WorkOvertime>()
                                         .Where(i => i.F_GroupId == workOverTimeGroup.hR_WorkOvertimeGroup.F_Id)
                                         .ToList();
                         List<HR_WorkOvertimeDTO> hR_WorkOvertimeDTOs = new List<HR_WorkOvertimeDTO>();
                         if (workOvertimes.Count > 0)
                         {
                             workOverTimeGroup.hR_WorkOvertimeGroup.F_WorkONo = workOvertimes.Select(i => i.F_WorkONo).FirstOrDefault();
                             workOvertimes.ForEach((item) =>
                             {
                                 HR_WorkOvertimeDTO hR_WorkOvertime = new HR_WorkOvertimeDTO();
                                 hR_WorkOvertime = MapHelper.Mapping<HR_WorkOvertimeDTO, HR_WorkOvertime>(item);
                                 hR_WorkOvertime.F_CompanyName = !string.IsNullOrWhiteSpace(hR_WorkOvertime.F_CompanyId) ?
                                                 this.Db.GetEntity<Base_Company>(hR_WorkOvertime.F_CompanyId)?.F_FullName : "";
                                 hR_WorkOvertime.EmployeesCode = this.Db.GetEntity<HR_FormalEmployees>(item.F_UserId)?.EmployeesCode;
                                 var positive = this.Db.GetIQueryable<HR_Positive>().Where(i => i.F_UserId == item.F_UserId).FirstOrDefault();
                                 if (positive != null)
                                 {
                                     hR_WorkOvertime.F_PositiveOrg = positive.F_PositiveOrg;
                                     hR_WorkOvertime.F_PositivePosition = positive.F_PositivePosition;
                                 }
                                 hR_WorkOvertimeDTOs.Add(hR_WorkOvertime);
                             });
                         }
                         workOverTimeGroup.hR_WorkOvertime = hR_WorkOvertimeDTOs;
                     }
                 });
            }
            return workOverTimeGroup;
        }
        /// <summary>
        /// 单人加班单保存
        /// </summary>
        /// <param name="data"></param>
        //[Transactional]
        public void SaveData(HR_WorkOvertime data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data, _operator);
                HR_WorkOvertimeGroup hR_WorkOvertimeGroup = new HR_WorkOvertimeGroup()
                {
                    F_IsManyPeopleWork = 0,
                    F_BusState = (int)ASKBusState.正常,
                    F_WFState = (int)WFStates.草稿,
                    F_ProjectName=data.F_ProjectName
                };
                InitEntity(hR_WorkOvertimeGroup, _operator);
                this.Db.Insert(hR_WorkOvertimeGroup);
                data.F_GroupId = hR_WorkOvertimeGroup.F_Id;
                data.F_BusState = (int)ASKBusState.正常;
                AddData(data);
            }
            else
            {
                UpdateData(data);
            }
        }
        /// <summary>
        /// 单人加班单保存
        /// </summary>
        /// <param name="data"></param>
        //[Transactional]
        public DataInfo SaveDataInfo(HR_WorkOvertime data, string id)
        {
            DataInfo dataInfo = new DataInfo();
            HR_WorkOvertimeGroup hR_WorkOvertimeGroup = new HR_WorkOvertimeGroup();
            if (id.IsNullOrEmpty())
            {
                InitEntity(data, _operator);
                hR_WorkOvertimeGroup = new HR_WorkOvertimeGroup()
                {
                    F_IsManyPeopleWork = 0,
                    F_BusState = (int)ASKBusState.正常,
                    F_WFState = (int)WFStates.草稿
                };
                InitEntity(hR_WorkOvertimeGroup, _operator);
                this.Db.Insert(hR_WorkOvertimeGroup);
            }
            else
            {
                hR_WorkOvertimeGroup = this.Db.GetIQueryable<HR_WorkOvertimeGroup>().FirstOrDefault(i => i.F_Id == id);
                hR_WorkOvertimeGroup.F_Remark = data.F_GroupRemark;
                if (hR_WorkOvertimeGroup != null)
                {
                    this.Db.Update(hR_WorkOvertimeGroup);
                    var ids = this.GetIQueryable().Where(i => i.F_GroupId == hR_WorkOvertimeGroup.F_Id).Select(i => i.F_Id).ToList();
                    if (ids.Count > 0)
                    {
                        this.Delete(ids);
                    }
                }
                InitEntity(data, _operator);
                dataInfo.hR_Works = this.Db.GetIQueryable<HR_WorkOvertimeGroup>().FirstOrDefault(i => i.F_Id == data.F_GroupId);
            }
            dataInfo.hR_Works = hR_WorkOvertimeGroup;
            data.F_GroupId = hR_WorkOvertimeGroup.F_Id;
            data.F_BusState = (int)ASKBusState.正常;
            data.F_WFState = (int)WFStates.草稿;
            AddData(data);
            dataInfo.hR_WorkOvertimes = new List<HR_WorkOvertime>();
            dataInfo.hR_WorkOvertimes.Add(data);
            return dataInfo;
        }
        /// <summary>
        /// 多人加班单保存
        /// </summary>
        /// <param name="data"></param>
        //[Transactional]
        public void MultSaveData(List<HR_WorkOvertime> data)
        {
            try
            {
                if (data.Count > 0)
                {
                    HR_WorkOvertimeGroup hR_WorkOvertimeGroup = new HR_WorkOvertimeGroup()
                    {
                        F_IsManyPeopleWork = 1,
                        F_BusState = (int)ASKBusState.正常,
                        F_WFState = (int)WFStates.草稿,
                        F_Remark = data.FirstOrDefault()?.F_GroupRemark,
                        F_ProjectName = data.FirstOrDefault()?.F_ProjectName,
                    };
                    InitEntity(hR_WorkOvertimeGroup, _operator);
                    this.Db.Insert(hR_WorkOvertimeGroup);
                    data.ForEach(item =>
                    {
                        InitEntity(item, _operator);
                        item.F_ActualWorkOEnd = item.F_WorkOEndTime;
                        item.F_ActualWorkOStart = item.F_WorkOStartTime;
                        item.F_ActualWorkOTime = item.F_WorkOTime;
                        item.F_GroupId = hR_WorkOvertimeGroup.F_Id;
                        item.F_BusState = (int)ASKBusState.正常;
                        item.F_WFState = (int)WFStates.草稿;
                        AddData(item);
                    });

                    //JObject obj1 = JObject.FromObject(data);
                    //obj1.Remove("F_GroupRemark");
                    //this.Db.BulkInsert(data);
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
        }
        /// 多人加班单保存
        /// </summary>
        /// <param name="data"></param>
         //[Transactional]
        public DataInfo MultSaveDataInfo(List<HR_WorkOvertime> data, string id)
        {
            DataInfo dataInfo = new DataInfo();
            if (data.Count > 0)
            {
                var group = new HR_WorkOvertimeGroup();
                ///编辑
                if (!id.IsNullOrEmpty())
                {
                    group = this.Db.GetIQueryable<HR_WorkOvertimeGroup>().FirstOrDefault(i => i.F_Id == id);
                    if (group != null)
                    {
                        var ids = this.GetIQueryable().Where(i => i.F_GroupId == group.F_Id).Select(i => i.F_Id).ToList();
                        if (ids.Count > 0)
                        {
                            this.Delete(ids);
                        }
                    }
                }
                else
                {
                    group = new HR_WorkOvertimeGroup()
                    {
                        F_IsManyPeopleWork = 1,
                        F_BusState = (int)ASKBusState.正常,
                        F_WFState = (int)WFStates.草稿,
                        F_Remark = data.FirstOrDefault()?.F_GroupRemark
                    };
                    InitEntity(group, _operator);
                    this.Db.Insert(group);
                }
                group.F_ProjectName = data.FirstOrDefault()?.F_ProjectName;
                dataInfo.hR_Works = group;
                data.ForEach(item =>
                {
                    InitEntity(item, _operator);
                    item.F_ActualWorkOEnd = item.F_WorkOEndTime;
                    item.F_ActualWorkOStart = item.F_WorkOStartTime;
                    item.F_ActualWorkOTime = item.F_WorkOTime;
                    item.F_GroupId = group.F_Id;
                    item.F_WFId = group.F_WFId;
                    item.F_WFState = group.F_WFState;
                });
                foreach (var ietm in data)
                {
                    AddData(ietm);
                }
                //BulkInsert(data);
                if (!id.IsNullOrEmpty())
                {
                    data.ForEach((item) =>
                    {
                        item.F_WFId = group.F_WFId;
                        item.F_WFState = group.F_WFState;
                    });

                }
                //BulkInsert(MapHelper.RemoveAttribute(data));
                dataInfo.hR_WorkOvertimes = data;
            }
            return dataInfo;
        }

        public async Task AddDataAsync(HR_WorkOvertime data)
        {
            await InsertAsync(data);
        }
        public int AddData(HR_WorkOvertime data)
        {
            return this.Db.Insert(data);
        }

        public async Task UpdateDataAsync(HR_WorkOvertime data)
        {
            await UpdateAsync(data);
        }
        public int UpdateData(HR_WorkOvertime data)
        {
            return this.Db.Update(data);
        }
        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_WorkOvertime>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_WorkOvertime, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_WorkOvertime, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);

            q = q.Where(where);
            List<HR_WorkOvertimeExcelDTO> hR_WorkOvertimeExcelDTO = new List<HR_WorkOvertimeExcelDTO>();
            if (q.Count() > 0)
            {
                foreach (var item in q)
                {
                    hR_WorkOvertimeExcelDTO.Add(MapHelper.Mapping<HR_WorkOvertimeExcelDTO, HR_WorkOvertime>(item));
                }
            }
            return hR_WorkOvertimeExcelDTO.ToDataTable();
        }
        #endregion

        #region 流程
        /// <summary>
        /// 提交流程
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        ///IsManyPeopleWork 1表示多人  0表示单人
        /// <returns>是否创建成功</returns>
        //[Transactional]
        public bool CreateFlow(List<HR_WorkOvertime> data, string url, int IsManyPeopleWork)
        {
            string formId = _configuration["OAFormId:WorkOvertimeFlow"];
            DataInfo dataInfo = new DataInfo();
            bool ret = false;
            if (IsManyPeopleWork == 1)
            {
                dataInfo = MultSaveDataInfo(data, data.FirstOrDefault().F_GroupId);
            }
            if (IsManyPeopleWork == 0)
            {
                dataInfo = SaveDataInfo(data.FirstOrDefault(), data.FirstOrDefault().F_GroupId);
            }
            var user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == dataInfo.hR_Works.F_CreateUserId);
            var userName = user != null ? user.UserName.Replace("@cqlandmark.com", "") : "";
            string token = JWTHelper.GetBusinessToken(dataInfo.hR_Works.F_Id, userName);
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            paramters.Add("reqCode", token);
            paramters.Add("reqNo", dataInfo.hR_Works.F_Id);
            paramters.Add("applyPerson", userName);
            paramters.Add("formId", formId);
            paramters.Add("code", data.Where(i => !i.F_WorkONo.IsNullOrEmpty())
                                                    .Select(i => i.F_WorkONo).FirstOrDefault());
            paramters.Add("applyDate", data[0].F_CreateDate.IsNullOrEmpty() ? DateTime.Now.ToString("yyyy-MM-dd") :
                        data[0].F_CreateDate.ToString("yyyy-MM-dd"));
            paramters.Add("project", !data[0].F_ProjectName.IsNullOrEmpty() ? data[0].F_ProjectName : "");
            paramters.Add("remark", !data[0].F_GroupRemark.IsNullOrEmpty() ? data[0].F_GroupRemark : "");
            paramters.Add("name", !data[0].F_CreateUserName.IsNullOrEmpty() ? data[0].F_CreateUserName : _operator.RealName);
            List<CreatDto> hrDatas = new List<CreatDto>();
            foreach (var item in dataInfo.hR_WorkOvertimes)
            {
                CreatDto pairs = new CreatDto();
                pairs.name = item.F_UserName;
                pairs.hrDetaiId = item.F_Id;
                pairs.over_work_date = Convert.ToDateTime(item.F_WorkODate).ToString("yyyy-MM-dd");
                pairs.over_work_type = item.F_WorkOType;
                pairs.over_work_start_time = Convert.ToDateTime(item.F_WorkOStartTime).ToString("yyyy-MM-dd HH:ss");
                pairs.note = OAHelper.GetNuLL(item.F_Remark);
                pairs.over_work_end_time = Convert.ToDateTime(item.F_WorkOEndTime).ToString("yyyy-MM-dd HH:ss");
                pairs.rest_time = item.F_RestTime.HasValue ? item.F_RestTime.Value.ToString() : "";
                pairs.over_time_hours = item.F_WorkOTime.HasValue ? item.F_WorkOTime.Value.ToString() : "";
                pairs.compensation_way = item.F_CompensationMode;
                pairs.over_work_reason = item.F_WorkOReason;
                hrDatas.Add(pairs);
            }
            paramters.Add("detail", JsonConvert.SerializeObject(hrDatas));
            string action = "createOverTimeWorkflow";
            string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);
            LogHelper.WriteLog_LocalTxt(str);
            var retObj = str.ToObject<Dictionary<string, string>>();
            if (retObj != null)
            {
                if (retObj["errCode"] == "0000")
                {
                    dataInfo.hR_Works.F_WFId = retObj["oaReqId"];
                    dataInfo.hR_Works.F_WFState = (int)WFStates.审核中;
                    dataInfo.hR_Works.F_ModifyDate = DateTime.Now;
                    this.Db.Update(dataInfo.hR_Works);
                    dataInfo.hR_WorkOvertimes.ForEach((item) =>
                    {
                        item.F_WFId = retObj["oaReqId"];
                        item.F_WFState = (int)WFStates.审核中;
                        item.F_ModifyDate = DateTime.Now;
                    });
                    Update(dataInfo.hR_WorkOvertimes);
                    ret = true;
                }
            }

            return ret;
        }

        /// <summary>
        /// 提交、退回流程
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <param name="act">submit:提交  reject：退回</param>
        /// <returns>是否成功</returns>
        public bool ActWorkflow(List<HR_WorkOvertime> data, int IsManyPeopleWork, string url, string id, string act = "submit")
        {
            string formId = _configuration["OAFormId:WorkOvertimeFlow"];
            bool ret = false;
            DataInfo dataInfo = new DataInfo();
            if (IsManyPeopleWork == 1)
            {
                dataInfo = MultSaveDataInfo(data, id);
            }
            if (IsManyPeopleWork == 0)
            {
                dataInfo = SaveDataInfo(data.FirstOrDefault(), id);
            }
            var user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == dataInfo.hR_Works.F_CreateUserId);
            var userName = user != null ? user.UserName.Replace("@cqlandmark.com", "") : "";
            string token = JWTHelper.GetBusinessToken(dataInfo.hR_Works.F_Id, userName);
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            paramters.Add("reqCode", token);
            paramters.Add("reqNo", dataInfo.hR_Works.F_Id);
            paramters.Add("applyPerson", userName);
            paramters.Add("formId", formId);
            paramters.Add("act", act);
            paramters.Add("sign", "");
            paramters.Add("code", data.Where(i => !i.F_WorkONo.IsNullOrEmpty())
                                                    .Select(i => i.F_WorkONo).FirstOrDefault());
            paramters.Add("applyDate", data[0].F_CreateDate.IsNullOrEmpty() ? DateTime.Now.ToString("yyyy-MM-dd") :
                        data[0].F_CreateDate.ToString("yyyy-MM-dd"));
            paramters.Add("remark", !data[0].F_GroupRemark.IsNullOrEmpty() ? data[0].F_GroupRemark : "");
            paramters.Add("project", !data[0].F_ProjectName.IsNullOrEmpty() ? data[0].F_ProjectName : "");
            paramters.Add("oaId", dataInfo.hR_Works.F_WFId);
            paramters.Add("name", !data[0].F_CreateUserName.IsNullOrEmpty() ? data[0].F_CreateUserName : _operator.RealName);
            List<CreatDto> hrDatas = new List<CreatDto>();
            foreach (var item in dataInfo.hR_WorkOvertimes)
            {
                CreatDto pairs = new CreatDto();
                pairs.name = item.F_UserName;
                pairs.hrDetaiId = item.F_Id;
                pairs.over_work_date = Convert.ToDateTime(item.F_WorkODate).ToString("yyyy-MM-dd");
                pairs.over_work_type = item.F_WorkOType;
                pairs.over_work_start_time = Convert.ToDateTime(item.F_WorkOStartTime).ToString("yyyy-MM-dd HH:ss");
                pairs.note = OAHelper.GetNuLL(item.F_Remark);
                pairs.over_work_end_time = Convert.ToDateTime(item.F_WorkOEndTime).ToString("yyyy-MM-dd HH:ss");
                pairs.rest_time = item.F_RestTime.HasValue ? item.F_RestTime.Value.ToString() : "";
                pairs.over_time_hours = item.F_WorkOTime.HasValue ? item.F_WorkOTime.Value.ToString() : "";
                pairs.compensation_way = item.F_CompensationMode;
                pairs.over_work_reason = item.F_WorkOReason;
                hrDatas.Add(pairs);
            }
            paramters.Add("detail", JsonConvert.SerializeObject(hrDatas));
            string action = "actOverTimeWorkflow";
            string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);
            LogHelper.WriteLog_LocalTxt(str);

            var retObj = str.ToObject<Dictionary<string, string>>();
            if (retObj != null)
            {
                if (retObj["errCode"] == "0000")
                {
                    if (act == "submit")
                    {
                        dataInfo.hR_Works.F_WFId = retObj["oaReqId"];
                        dataInfo.hR_Works.F_WFState = (int)WFStates.审核中;
                        dataInfo.hR_Works.F_ModifyDate = DateTime.Now;
                        this.Db.Update(dataInfo.hR_Works);
                        dataInfo.hR_WorkOvertimes.ForEach((item) =>
                        {
                            item.F_WFId = retObj["oaReqId"];
                            item.F_WFState = (int)WFStates.审核中;
                            item.F_ModifyDate = DateTime.Now;
                        });
                        Update(dataInfo.hR_WorkOvertimes);
                    }
                    ret = true;
                }
            }

            return ret;
        }

        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <returns>是否成功</returns>
        public bool ArchiveWorkflow(List<HR_WorkOvertime> data, string url, string id)
        {
            bool ret = false;
            var group = this.Db.GetIQueryable<HR_WorkOvertimeGroup>().FirstOrDefault(i => i.F_Id == id);
            if (group != null)
            {
                var user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == group.F_CreateUserId);
                var userName = user != null ? user.UserName.Replace("@cqlandmark.com", "") : "";
                string token = JWTHelper.GetBusinessToken(group.F_Id, userName);
                Dictionary<string, object> paramters = new Dictionary<string, object>();
                paramters.Add("reqCode", token);
                paramters.Add("reqNo", group.F_Id);
                paramters.Add("applyPerson", userName);
                paramters.Add("oaId", group.F_WFId);
                string action = "archiveWorkflow";
                string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);
                LogHelper.WriteLog_LocalTxt(str);
                var retObj = str.ToObject<Dictionary<string, string>>();
                if (retObj != null)
                {
                    if (retObj["errCode"] == "0000")
                    {
                        group.F_WFId = retObj["oaReqId"];
                        group.F_WFState = (int)WFStates.取消流程;
                        group.F_ModifyDate = DateTime.Now;
                        this.Db.Update(data);
                        var workOvertime = this.GetIQueryable().Where(x => x.F_GroupId == id).ToList();
                        if (workOvertime.Count() > 0)
                        {
                            workOvertime.ForEach((item) =>
                            {
                                item.F_WFId = retObj["oaReqId"];
                                item.F_WFState = (int)WFStates.取消流程;
                                item.F_ModifyDate = DateTime.Now;
                            });
                        }
                        this.Db.Update(workOvertime);
                        ret = true;
                    }
                }

            }

            return ret;
        }

        /// <summary>
        /// 流程回调方法
        /// </summary>
        /// <param name="input"></param>
        public void FlowCallBack(FlowInputDTO input)
        {
            if (input == null)
            {
                throw new System.Exception("参数错误");
            }
            var entity = this.Db.GetIQueryable<HR_WorkOvertimeGroup>().Where(i => i.F_Id == input.id).FirstOrDefault();
            if (entity == null)
            {
                throw new System.Exception("未找到对象");
            }
            entity.F_WFState = input.status;
            var workOvertimeGroup = this.Db.GetIQueryable<HR_WorkOvertime>().Where(x => x.F_GroupId == entity.F_Id).ToList();
            this.Db.Update(entity);
            if (workOvertimeGroup.Count > 0)
            {
                workOvertimeGroup.ForEach((item) =>
                {
                    item.F_WFState = input.status;
                });
                this.Db.Update(workOvertimeGroup);
            }
        }

        public List<HR_WorkOvertime> UploadFileByForm(List<HR_WorkOvertime> data)
        {
            List<HR_WorkOvertime> hR_WorkOvertimes = new List<HR_WorkOvertime>();
            data.ForEach((item) =>
            {
                //业务判断逻辑
                //判断姓名是否存在正式员工里面
                HR_WorkOvertime hR_WorkOvertime = item;
                if (!hR_WorkOvertime.F_UserName.IsNullOrEmpty())
                {
                    var hR_FormalEmployees = this.Db.GetIQueryable<HR_FormalEmployees>()
                                            .FirstOrDefault(i => i.NameUser == hR_WorkOvertime.F_UserName);
                    if (hR_FormalEmployees != null)
                    {
                        hR_WorkOvertime.F_UserId = hR_FormalEmployees.F_Id;
                        hR_WorkOvertimes.Add(hR_WorkOvertime);
                    }
                }

            });
            return hR_WorkOvertimes;
        }
        #endregion
        #region 私有成员
        public class DataInfo
        {
            public List<HR_WorkOvertime> hR_WorkOvertimes { get; set; }
            public HR_WorkOvertimeGroup hR_Works { get; set; }
        }
        #endregion
    }
}