﻿using AutoMapper;
using Coldairarrow.Entity;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.DTO;
using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using EFCore.Sharding;
using Microsoft.EntityFrameworkCore;
using OracleInternal.Secure.Network;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Microsoft.Extensions.Caching.Distributed;
using AutoMapper.Configuration;
using static Coldairarrow.Entity.Shop_Manage.Model.JDResultModel;
using Coldairarrow.Entity.Serivces;

namespace Coldairarrow.Business.Base_Manage
{
    public class HomeBusiness : BaseBusiness<Base_User>, IHomeBusiness, ITransientDependency
    {
        readonly IOperator _operator;
        readonly IMapper _mapper;
        readonly IDistributedCache _cache;
        readonly IBase_DepartmentBusiness _base_DepartmentBusiness;
        public HomeBusiness(IDbAccessor db, IOperator @operator, IMapper mapper, IDistributedCache cache, IBase_DepartmentBusiness base_DepartmentBusiness)
            : base(db)
        {
            _operator = @operator;
            _mapper = mapper;
            _cache = cache;
            _base_DepartmentBusiness = base_DepartmentBusiness;
        }

        public async Task<string> SubmitLoginAsync(LoginInputDTO input)
        {
            input.password = input.password.ToMD5String();
            var users = await GetIQueryable().ToListAsync();
            var theUser = users
             .Where(x => !x.UserName.IsNullOrEmpty() && (x.UserName.ToLower() == input.userName.ToLower()) && (x.Password == input.password) && x.Deleted == false)
            .FirstOrDefault();
            if (theUser != null)
            {
                var user = theUser.UserName.Replace("@cqlandmark.com", "");
                theUser = users.Where(x => !x.UserName.IsNullOrEmpty() && x.UserName.ToLower().Contains(user.ToLower())).OrderBy(x => x.CreateTime).FirstOrDefault();
            }
            if (theUser.IsNullOrEmpty())
            {
                //登录写入日志
                var zlog = new Base_UserLog
                {
                    Id = IdHelper.GetId(),
                    CreateTime = DateTime.Now,
                    CreatorId = input.userName,
                    CreatorRealName = input.userName,
                    LogContent = $"{input.userName}登录系统",
                };
                switch (input.visitType)
                {
                    case "HR":
                        zlog.LogType = UserLogType.HR系统.ToString();
                        break;
                    case "OP":
                        zlog.LogType = UserLogType.运营驾驶舱.ToString();
                        break;
                    case "Exam":
                        zlog.LogType = UserLogType.招置学堂.ToString();
                        break;
                }
                //如果是管理员则失败一次则禁用
                if (input.userName.ToLower() == "admin")
                {
                    var adminUser = await GetIQueryable().FirstOrDefaultAsync(x => x.UserName.ToLower() == input.userName.ToLower());
                    if (adminUser != null)
                    {
                        //禁用
                        adminUser.Deleted = true;
                        await this.UpdateAsync(adminUser);
                    }
                }
                zlog.JsonContent = "登录失败";
                _operator.WriteUserLog(zlog);
                throw new BusException("登录失败，请按照指定方式进入！");
            }
            //登录写入日志
            var log = new Base_UserLog
            {
                Id = IdHelper.GetId(),
                CreateTime = DateTime.Now,
                CreatorId = theUser.Id,
                CreatorRealName = theUser.RealName,
                LogContent = $"{theUser.RealName}登录系统",
                JsonContent = "登录成功",
            };
            switch (input.visitType)
            {
                case "HR":
                    log.LogType = UserLogType.HR系统.ToString();
                    break;
                case "OP":
                    log.LogType = UserLogType.运营驾驶舱.ToString();
                    break;
                case "Exam":
                    log.LogType = UserLogType.招置学堂.ToString();
                    break;
            }

            //生成token,有效期一天
            JWTPayload jWTPayload = new JWTPayload
            {
                UserId = theUser.Id,
                RealName = theUser.RealName,
                ADName = theUser.UserName,
                Expire = DateTime.Now.AddDays(1)
            };
            string token = JWTHelper.GetToken(jWTPayload.ToJson(), JWTHelper.JWTSecret);
            _operator.WriteUserLog(log);
            return token;
        }
        public async Task<string> SubmitPhoneLoginAsync(LoginInputDTO input)
        {
            //登录写入日志
            var zlog = new Base_UserLog
            {
                Id = IdHelper.GetId(),
                CreateTime = DateTime.Now,
                CreatorId = input.userName,
                CreatorRealName = input.userName,
                LogContent = $"{input.userName}手机号登录系统",
                JsonContent = "登录成功",
            };
            switch (input.visitType)
            {
                case "HR":
                    zlog.LogType = UserLogType.HR系统.ToString();
                    break;
                case "OP":
                    zlog.LogType = UserLogType.运营驾驶舱.ToString();
                    break;
                case "Exam":
                    zlog.LogType = UserLogType.招置学堂.ToString();
                    break;
            }
            input.password = input.password.ToMD5String();
            var theUsers = await GetIQueryable().ToListAsync();
            var theUser = theUsers
                  .Where(x => x.MobileStr == input.userName)
                  .FirstOrDefault();
            if (theUser != null)
            {
                var user = theUser.UserName.Replace("@cqlandmark.com", "");
                theUser = theUsers.Where(x => x.UserName.Contains(user)).OrderBy(x => x.CreateTime).FirstOrDefault();
            }
            //var theUser = theUsers
            //    .Where(x => x.MobileStr.Equals(input.userName))
            //    .FirstOrDefault();

            if (theUser.IsNullOrEmpty())
            {
                zlog.JsonContent = "登录失败:该手机号未注册！";
                _operator.WriteUserLog(zlog);
                throw new BusException("该手机号未注册！");
            }

            if (!theUser.Password.Equals(input.password))
            {
                zlog.JsonContent = "登录失败:账号或密码不正确！";
                _operator.WriteUserLog(zlog);
                throw new BusException("账号或密码不正确！");
            }

            //生成token,有效期一天
            JWTPayload jWTPayload = new JWTPayload
            {
                UserId = theUser.Id,
                RealName = theUser.RealName,
                ADName = theUser.UserName,
                Expire = DateTime.Now.AddDays(1)
            };
            string token = JWTHelper.GetToken(jWTPayload.ToJson(), JWTHelper.JWTSecret);
            _operator.WriteUserLog(zlog);
            return token;
        }
        /// <summary>
        /// 单点验证OA登录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<string> VerifyValidityAsync(VerifyValidityDTO input)
        {
            Base_User theUser = null;
            string str = "cqlandmark"; //正式用"cqlandmark"
            var users = await GetIQueryable().ToListAsync();
            //使用手机号的MD5登录
            if (!string.IsNullOrEmpty(input.Token) && !string.IsNullOrEmpty(input.Security))
            {
                theUser = users
                    .Where(x => x.MobileSecurity == input.Security)
                    .FirstOrDefault();
                if (theUser != null)
                {
                    var user = theUser.UserName.Replace("@cqlandmark.com", "");
                    theUser = users.Where(x => x.UserName.Contains(user)).OrderBy(x => x.CreateTime).FirstOrDefault();
                }
            }
            else
            {
                //正常用户名单点登录验证
                string strInfo = input.UserName + input.SId + str;

                System.Security.Cryptography.MD5 md5 = System.Security.Cryptography.MD5.Create();
                // 加密后是一个字节类型的数组，这里要注意编码UTF8/Unicode等的选择　
                byte[] s = md5.ComputeHash(Encoding.UTF8.GetBytes(strInfo));
                // 通过使用循环，将字节类型的数组转换为字符串，此字符串是常规字符格式化所得
                string tokenNow = "";
                //for (int i = 0; i < s.Length; i++)
                //{
                //    // 将得到的字符串使用十六进制类型格式。格式后的字符是小写的字母，如果使用大写（X）则格式后的字符是大写字符 
                //    tokenNow += s[i].ToString("X"); 
                //}

                foreach (byte b in s)
                {
                    //得到的字符串使用十六进制类型格式。格式后的字符是小写的字母，如果使用大写（X）则格式后的字符是大写字符 
                    //但是在和对方测试过程中，发现我这边的MD5加密编码，经常出现少一位或几位的问题；
                    //后来分析发现是 字符串格式符的问题， X 表示大写， x 表示小写， 
                    //X2和x2表示不省略首位为0的十六进制数字；
                    tokenNow += b.ToString("x2");
                }
                tokenNow = tokenNow.ToUpper();
                if (input.Token != tokenNow)
                {
                    return "";
                }
                theUser = users
                   .Where(x => (x.UserName == input.UserName) || (x.UserName == input.UserName + "@cqlandmark.com"))
                   .OrderBy(x => x.CreateTime).FirstOrDefault();

            }

            if (theUser.IsNullOrEmpty())
            {
                //登录写入日志
                var zlog = new Base_UserLog
                {
                    Id = IdHelper.GetId(),
                    CreateTime = DateTime.Now,
                    CreatorId = input.UserName,
                    CreatorRealName = input.UserName,
                    LogContent = $"{input.UserName}单点登录系统",
                    JsonContent = "登录失败",
                };
                switch (input.visitType)
                {
                    case "HR":
                        zlog.LogType = UserLogType.HR系统.ToString();
                        break;
                    case "OP":
                        zlog.LogType = UserLogType.运营驾驶舱.ToString();
                        break;
                    case "Exam":
                        zlog.LogType = UserLogType.招置学堂.ToString();
                        break;
                }
                _operator.WriteUserLog(zlog);
                return "";
            }
            //生成token,有效期一天
            JWTPayload jWTPayload = new JWTPayload
            {
                UserId = theUser.Id,
                RealName = theUser.RealName,
                ADName = theUser.UserName,
                Expire = DateTime.Now.AddDays(1)
            };
            string token = JWTHelper.GetToken(jWTPayload.ToJson(), JWTHelper.JWTSecret);
            //登录写入日志
            var log = new Base_UserLog
            {
                Id = IdHelper.GetId(),
                CreateTime = DateTime.Now,
                CreatorId = theUser.Id,
                CreatorRealName = theUser.RealName,
                LogContent = $"{theUser.RealName}单点登录系统",
                JsonContent = "登录成功",
            };
            switch (input.visitType)
            {
                case "HR":
                    log.LogType = UserLogType.HR系统.ToString();
                    break;
                case "OP":
                    log.LogType = UserLogType.运营驾驶舱.ToString();
                    break;
                case "Exam":
                    log.LogType = UserLogType.招置学堂.ToString();
                    break;
            }
            _operator.WriteUserLog(log);
            return token;
        }
        public async Task ChangePwdAsync(ChangePwdInputDTO input)
        {
            var theUser = _operator.Property;
            if (theUser.Password != input.oldPwd?.ToMD5String())
                throw new BusException("原密码错误!");

            theUser.Password = input.newPwd.ToMD5String();
            await UpdateAsync(_mapper.Map<Base_User>(theUser));
        }
        public async Task registerUser(registerInputDTO registerInput)
        {
            if (registerInput.isWx == 0)
            {
                //查询手机短信是否正确
                var code = (await _cache.GetObjectAsync($"{registerInput.PhoneNumbers}AliMsg")).ToString();
                if (string.IsNullOrEmpty(code))
                    throw new BusException("该手机号已注册!");
                if (!code.Equals(registerInput.Code))
                    throw new BusException("手机验证码不正确!");
            }
            //新增用户
            var user = new Base_User()
            {
                Id = Guid.NewGuid().ToString("N"),
                guid = Guid.NewGuid().ToString(),
                RealName = registerInput.RealName,
                Deleted = false,
                Password = registerInput.pwd.ToMD5String(),
                displayName = registerInput.RealName,
                IsExamUser = 1
            };
            if (!registerInput.PhoneNumbers.IsNullOrEmpty())
            {
                user.Mobile = AESHelper.EncryptString(registerInput.PhoneNumbers, AESHelper.AesKey);
                user.MobileSecurity = registerInput.PhoneNumbers.ToMD5String();
            }
            await this.Db.InsertAsync(user);
        }
        /// <summary>
        /// 调用支付消息平台的阿里短信接口
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task<AliMsgOutput> SendAliMsg(SendMsgDto data)
        {
            await _cache.RemoveAsync($"{data.PhoneNumbers}AliMsg");
            //判断
            var code = RandomHelper.RandNum(4);
            await _cache.SetObjectAsync($"{data.PhoneNumbers}AliMsg", code);
            //生成4位数随机码
            AliMsgInput aliMsgInput = new AliMsgInput()
            {
                PhoneNumbers = data.PhoneNumbers,
                TemplateParam = new ParmDto()
                {
                    code = code
                },
                FSendForm = "HR系统"
            };
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            Dictionary<string, object> SMSData = new Dictionary<string, object>();
            SMSData.Add("PhoneNumbers", data.PhoneNumbers);
            SMSData.Add("TemplateParam", JsonConvert.SerializeObject(new ParmDto()
            {
                code = code
            }));
            SMSData.Add("FSendForm", "HR系统");
            paramters.Add("AliMsgInput", SMSData);
            //支付消息平台发送短信
            var str = HttpHelper.PostData(data.url + data.SMSAPIUrl, SMSData, null, ContentType.Json);
            LogHelper.WriteLog_LocalTxt(str);
            var retObj = str.ToObject<Dictionary<string, object>>();
            AliMsgOutput aliMsgOutput = new AliMsgOutput();
            if (retObj != null)
            {
                if (Convert.ToBoolean(retObj["success"]))
                {
                    aliMsgOutput = JsonConvert.DeserializeObject<AliMsgOutput>(retObj["data"].ToString());
                    if (aliMsgOutput.Code == "isv.BUSINESS_LIMIT_CONTROL")
                    {
                        throw new Exception("发送超时,请5分钟后再次发送!");
                    }
                    if (aliMsgOutput.Code == "isv.AMOUNT_NOT_ENOUGH")
                    {
                        throw new Exception("发送验证嘛,账户余额不足,请您联系管理员！");
                    }
                    //if (string.IsNullOrEmpty(aliMsgOutPut.BizId))
                    //{
                    //    logAliMessage.Status = CommonStatus.FAIL;
                    //    logAliMessage.FMessage = aliMsgOutPut.Message;
                    //}
                }
            }
            else
            {
                aliMsgOutput.Message = "发送失败";
                aliMsgOutput.Code = "500";
            }
            return await Task.Run(() => { return aliMsgOutput; });
        }
        /// <summary>
        /// 获取我的团员信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public List<MyTeamEmp> GetMyTeam(string userId)
        {
            if (userId.IsNullOrEmpty())
            {
                throw new BusException("参数不能为空");
            }
            List<MyTeamEmp> revDtos = new List<MyTeamEmp>();
            //得到我的部门下人员ID
            var deptIds = from e in Db.GetIQueryable<HR_FormalEmployees>()
                          where e.BaseUserId == userId
                          select e.F_DepartmentId;
            if (deptIds != null)
            {
                List<string> relStatus = new List<string>()
                {
                    "派驻",
                    "正式员工",
                    "试用员工",
                    "第三方用工",
                    "第三方员工",
                    "试用员工（延期转正）",
                    "正式员工（校招）",
                    "正式员工（销售）",
                    "顾问"
                };
                //查询部门内信息
                var depts = _base_DepartmentBusiness.GetDetListByNode(deptIds != null && deptIds.Count() > 0 ? deptIds.First() : "");

                var empList = from e in Db.GetIQueryable<HR_FormalEmployees>().Where(i => relStatus.Contains(i.EmployRelStatus))
                              where depts.Contains(e.F_DepartmentId)
                              select new MyTeamEmp
                              {
                                  Id = e.F_Id,
                                  EmpName = e.NameUser,
                                  Avatar = e.HeadPortrait

                              };
                var ids = empList.Select(s => s.Id);
                var liz = Db.GetIQueryable<HR_Departure>().Where(i => ids.Contains(i.F_UserId) && i.F_TrueDepartureDate <= DateTime.Now.Date).ToList();
                var liIds = liz.Select(s => s.F_UserId);
                empList = empList.Where(i => !liIds.Contains(i.Id));
                if (empList != null)
                {
                    revDtos.AddRange(empList.ToList());
                }
            }
            return revDtos;
        }
        /// <summary>
        /// 获取我的团队动态信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public PageResult<TeamDynamicsDTO> GetEmpTeamInfo(PageInput<RolesInputDTO> input, string userId)
        {
            if (userId.IsNullOrEmpty())
            {
                throw new BusException("参数不能为空");
            }
            //过滤sql注入
            userId.FilterSql();
            //IEnumerable<TeamDynamicsDTO> revDtos = null;
            string sql = @"select * from(
                select  b.NameUser as EmpName, b.HeadPortrait as Avatar, '请假' as ActionType,b.F_DepartmentId as F_DepartmentId,
                 (select F_DepartmentId from [dbo].[HR_FormalEmployees] where BaseUserId = @BaseUserId) as MyDepId,
                a.F_AskLeaveTime as DayInfo,a.F_StartTime as BeginDate,a.F_CreateDate as CreateDate,a.F_UserId
                 from[dbo].[HR_AskLeave] a,[dbo].[HR_FormalEmployees] b where a.F_UserId=b.F_Id and a.F_WFState=3
 
                   ) t
              ";
            //string sql = @"select * from(
            //    select  b.NameUser as EmpName, b.HeadPortrait as Avatar, '请假' as ActionType,b.F_DepartmentId as F_DepartmentId,a.F_AskLeaveTime as DayInfo,a.F_StartTime as BeginDate,a.F_CreateDate as CreateDate,a.F_UserId
            //     from[dbo].[HR_AskLeave] a,[dbo].[HR_FormalEmployees] b where a.F_UserId=b.F_Id and a.F_WFState=3
            //    and b.F_DepartmentId in (select F_DepartmentId from [dbo].[HR_FormalEmployees] where BaseUserId = @BaseUserId)
            //       ) t
            //  ";
            //UNION ALL
            //     select* from(
            //    select b.NameUser as EmpName, b.HeadPortrait as Avatar, '离职' as ActionType, DATEDIFF(day, a.F_DepartureDate, getdate())
            //   as DayInfo,a.F_DepartureDate as BeginDate,a.F_CreateDate as CreateDate,a.F_UserId from
            //    HR_Departure a,[dbo].[HR_FormalEmployees] b where a.F_UserId = b.F_Id and a.F_WFState = 3
            //    and b.F_DepartmentId in (select F_DepartmentId from[dbo].[HR_FormalEmployees] where BaseUserId = @BaseUserId) 
            //     ) q
            var list = Db.GetListBySql<TeamDynamicsDTO>(sql, ("BaseUserId", userId));
            if (list.Count > 0)
            {
                //查询部门内信息
                var depts = _base_DepartmentBusiness.GetDetListByNode(list.First().MyDepId);
                if (depts.Count > 0)
                {
                    list = list.Where(x => depts.Contains(x.F_DepartmentId)).ToList();
                }
                else if (userId != "Admin")
                {
                    list = new List<TeamDynamicsDTO>();
                }
            }
            var revDtos = list.GetPageResult<TeamDynamicsDTO>(input);
            return revDtos;
        }
        #region 首页获取数据
        public HomeDataInfo GetHomeInfo(OAInputDTO model)
        {
            try
            {
                HomeDataInfo homeInfo = new HomeDataInfo();
                var formalEmployees = !model.UserId.IsNullOrEmpty() ? this.Db.GetIQueryable<HR_FormalEmployees>()
                                            .FirstOrDefault(i => i.BaseUserId == model.UserId) : new HR_FormalEmployees();
                var noticeEntity = Db.GetIQueryable<HR_Message>().Where(i => i.F_ReceiveUserId == model.UserId);
                homeInfo.MessagesNum = noticeEntity.Count();
                homeInfo.MessageList = noticeEntity.OrderByDescending(i => i.F_IsRead).OrderByDescending(i => i.F_CreateDate).Take(5).ToList();
                homeInfo.Year = DateTime.Now.AddYears(1).Year;
                homeInfo.RunNum = 0;
                homeInfo.AgentsNum = 0;
                homeInfo.RemainingDays = 0;
                if (formalEmployees != null)
                {
                    var dataNow = DateTime.Now.Date;
                    var hoLineModel = Db.GetIQueryable<HR_HolidayLine>().Where(x => x.F_UserId == formalEmployees.F_Id && x.F_EffectTime <= dataNow && x.F_EndTime >= dataNow && x.F_HolidayTypes.Contains("法定年假")).ToList();
                    //var hoLineModel = Db.GetIQueryable<HR_HolidayLine>().Where(i => i.F_UserId == formalEmployees.F_Id).ToList();
                    //if (hoLineModel != null)
                    //{
                    //    homeInfo.RemainingDays = hoLineModel.F_ActualAmount.HasValue ? hoLineModel.F_ActualAmount.Value : 0;
                    //}
                    if (hoLineModel.Count() > 0)
                    {
                        homeInfo.RemainingDays = (hoLineModel.Sum(s => s.F_ActualAmount) - hoLineModel.Sum(s => s.F_UsedLine)).Value;
                    }
                }
                //开始调接口
                //获取token
                //var tokenInfo = OAHelper.GetOAToken(model.Url3, model.ADName);
                var tokenInfo = JWTHelper.GetBusinessToken(model.BusinessID, model.ADName);
                //代办
                if (model.ADName.ToLower() == "admin")
                {
                    return homeInfo;
                }
                Dictionary<string, object> paramters = new Dictionary<string, object>();
                paramters.Add("reqCode", tokenInfo);
                paramters.Add("reqNo", model.BusinessID);
                paramters.Add("user", model.ADName);
                paramters.Add("pageSize", model.PageSize);
                paramters.Add("currentPage", model.CurrentPage);
                string str = HttpHelper.PostData(model.Url, paramters, null, ContentType.Json);
                var outInfo = JsonConvert.DeserializeObject<AOOutput>(str);
                if (outInfo.errCode == "0000")
                {
                    homeInfo.AgentsNum = Convert.ToInt32(outInfo.total);
                    homeInfo.AgentList = outInfo.rows;
                }
                else
                {
                    //throw new BusException(outInfo.errDesc);
                }
                Dictionary<string, object> paramtersHas = new Dictionary<string, object>();
                paramtersHas.Add("reqCode", tokenInfo);
                paramtersHas.Add("reqNo", model.BusinessID);
                paramtersHas.Add("user", model.ADName);
                string strHas = HttpHelper.PostData(model.Url2, paramters, null, ContentType.Json);
                var outInfoHas = JsonConvert.DeserializeObject<AOOutput>(strHas);
                if (outInfoHas.errCode == "0000")
                {
                    homeInfo.RunNum = Convert.ToInt32(outInfoHas.total);
                }
                else
                {
                    //throw new BusException(outInfo.errDesc);
                }
                return homeInfo;
            }
            catch (Exception ex)
            {
                return null;
            }
        }




        #endregion
        #region 数据同步方法
        public void GetPost()
        {
            var entity = Db.GetIQueryable<Base_Post>().Where(i => i.F_EnCode != i.F_EnCodeP).ToList();
            var entityOther = Db.GetIQueryable<Base_Post>().ToList();
            foreach (var item in entity)
            {
                var model = entityOther.FirstOrDefault(i => i.F_EnCode == item.F_EnCodeP);
                if (model != null)
                {
                    item.F_ParentId = model.F_Id;
                }
            }
            Db.UpdateAsync(entity);
        }
        /// <summary>
        /// 同步用户的岗位部门公司
        /// </summary>
        public void GetUser()
        {
            var entity = Db.GetIQueryable<HR_FormalEmployees>().ToList();
            var depEntity = Db.GetIQueryable<Base_Department>().ToList();
            var postEntity = Db.GetIQueryable<Base_Post>().ToList();
            foreach (var item in entity)
            {
                var depModel = depEntity.FirstOrDefault(i => i.Name == item.F_DepartmentId);
                if (depModel != null)
                {
                    item.F_DepartmentId = depModel.Id;
                }
                var postModel = postEntity.FirstOrDefault(i => i.F_Name == item.F_PositionId);
                if (postModel != null)
                {
                    item.F_PositionId = postModel.F_Id;
                }
                item.F_CompanyId = "7a579db2-f4e3-489c-aed9-d39cf78a1cfe";
            }
            Db.UpdateAsync(entity);
        }
        /// <summary>
        /// 同步入职转正离职
        /// </summary>
        public void GetUserIPosition()
        {
            try
            {
                string sql = "SELECT 姓名,t1.F_Id as UserId,身份证号码,员工状态,入职日期,转正日期,离职日期,合同劳动签订公司,岗位,部门 FROM Test t LEFT JOIN HR_FormalEmployees t1 on t1.NameUser=t.姓名 and t1.IdCardNumber=t.身份证号码";
                var userIPositions = this.Db.GetListBySql<UserIPositionDTO>(sql);
                if (userIPositions != null & userIPositions.Count > 0)
                {
                    //获取正式员工ID
                    var userId = userIPositions.Select(i => i.UserId).ToList();
                    var formalEmployees = this.Db.GetIQueryable<HR_FormalEmployees>()
                                        .Where(i => userId.Contains(i.F_Id)).ToList();
                    //同步入职数据
                    List<HR_Induction> hR_Inductions = new List<HR_Induction>();
                    //转正
                    List<HR_Positive> hR_Positives = new List<HR_Positive>();
                    //离职
                    List<HR_Departure> hR_Departures = new List<HR_Departure>();
                    foreach (var item in formalEmployees)
                    {
                        var userIPosition = userIPositions.FirstOrDefault(i => i.UserId == item.F_Id);
                        if (userIPosition != null)
                        {
                            //var post = !item.F_PositionId.IsNullOrEmpty() ?
                            //                       await this.Db.GetIQueryable<Base_Post>().FirstOrDefaultAsync(i=>i.F_Id == item.F_PositionId) : new Base_Post();
                            if (userIPosition.入职日期.HasValue)
                            {
                                HR_Induction hR_Induction = new HR_Induction()
                                {
                                    F_UserId = item.F_Id,
                                    F_InductionDate = userIPosition.入职日期,
                                    F_ProbationPeriod = 3,
                                    //F_InductionPosition = post!=null ?post?.F_Name : "",
                                    F_BusState = (int)ASKBusState.正常,
                                    F_InductionRank = item.F_Rank,
                                    F_ChangesOperating = "雇佣入职",
                                    F_ChangesType = "雇佣入职",
                                    F_ChangesDate = userIPosition.入职日期,
                                    F_CreateUserName = "李林俊",
                                    F_CreateUserId = "1324537928872890368",
                                    F_InductionPosition = userIPosition.岗位,
                                    F_InductionOrg = (userIPosition.合同劳动签订公司.IsNullOrEmpty() ? "" : userIPosition.合同劳动签订公司 + "-") +
                                        (userIPosition.部门.IsNullOrEmpty() ? "" : userIPosition.部门 + "-") +
                                        (userIPosition.岗位.IsNullOrEmpty() ? "" : userIPosition.岗位),
                                    F_CreateDate = DateTime.Now,
                                    F_Id = IdHelper.GetId(),

                                    F_InductionEmployRelStatus = item.EmployRelStatus
                                };
                                //InitEntity(hR_Induction, _operator);
                                hR_Inductions.Add(hR_Induction);
                            }
                            if (userIPosition.转正日期.HasValue)
                            {
                                HR_Positive hR_Positive = new HR_Positive()
                                {
                                    F_UserId = item.F_Id,
                                    F_PositiveDate = userIPosition.转正日期,
                                    F_ProbationPeriod = 3,
                                    //F_PositivePosition = post != null ? post?.F_Name : "",
                                    F_BusState = (int)ASKBusState.正常,
                                    F_PositiveRand = item.F_Rank,
                                    F_Id = IdHelper.GetId(),
                                    F_CreateUserName = "李林俊",
                                    F_PositivePosition = userIPosition.岗位,
                                    F_PositiveOrg = (userIPosition.合同劳动签订公司.IsNullOrEmpty() ? "" : userIPosition.合同劳动签订公司 + "-") +
                                        (userIPosition.部门.IsNullOrEmpty() ? "" : userIPosition.部门 + "-") +
                                        (userIPosition.岗位.IsNullOrEmpty() ? "" : userIPosition.岗位),
                                    F_CreateUserId = "1324537928872890368",
                                    F_ChangesOperating = "转正",
                                    F_CreateDate = DateTime.Now,
                                    F_ChangesType = "转正",
                                };
                                //InitEntity(hR_Positive, _operator);
                                hR_Positives.Add(hR_Positive);
                            }
                            if (userIPosition.离职日期.HasValue)
                            {
                                HR_Departure hR_Departure = new HR_Departure()
                                {
                                    F_UserId = item.F_Id,
                                    F_OriginalEmplStatus = item.EmployRelStatus,
                                    F_MobilzOriEmplStatus = "主动离职",
                                    F_CreateUserName = "李林俊",
                                    F_CreateUserId = "1324537928872890368",
                                    F_ChangesOperating = "辞职",
                                    F_ChangesType = "辞职",
                                    F_Id = IdHelper.GetId(),
                                    F_DepartureReason = "个人原因",
                                    F_CreateDate = DateTime.Now,
                                    F_TrueDepartureDate = userIPosition.离职日期
                                };
                                //InitEntity(hR_Departure, _operator);
                                hR_Departures.Add(hR_Departure);
                            }
                        }
                    }
                    if (hR_Inductions.Count > 0)
                    {
                        this.Db.Insert(hR_Inductions);
                        //await this.Db.InsertAsync(hR_Inductions);
                    }
                    if (hR_Positives.Count > 0)
                    {
                        this.Db.Insert(hR_Positives);
                        //await this.Db.InsertAsync(hR_Positives);
                    }
                    if (hR_Departures.Count > 0)
                    {
                        this.Db.Insert(hR_Departures);
                        //await this.Db.InsertAsync(hR_Departures);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }
        }

        #endregion

    }
}
