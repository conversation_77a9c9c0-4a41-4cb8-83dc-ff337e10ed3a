﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Sockets</name>
  </assembly>
  <members>
    <member name="T:System.Net.Sockets.IOControlCode">
      <summary>Specifies the IO control codes supported by the <see cref="M:System.Net.Sockets.Socket.IOControl(System.Int32,System.Byte[],System.Byte[])" /> method.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.AbsorbRouterAlert">
      <summary>This value is equal to the Winsock 2 SIO_ABSORB_RTRALERT constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.AddMulticastGroupOnInterface">
      <summary>Join a multicast group using an interface identified by its index. This control code is supported on Windows 2000 and later operating systems. This value is equal to the Winsock 2 SIO_INDEX_ADD_MCAST constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.AddressListChange">
      <summary>Enable receiving notification when the list of local interfaces for the socket's protocol family changes. This control code is supported on Windows 2000 and later operating systems. This value is equal to the Winsock 2 SIO_ADDRESS_LIST_CHANGE constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.AddressListQuery">
      <summary>Return the list of local interfaces that the socket can bind to. This control code is supported on Windows 2000 and later operating systems. This value is equal to the Winsock 2 SIO_ADDRESS_LIST_QUERY constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.AddressListSort">
      <summary>Sort the structure returned by the <see cref="F:System.Net.Sockets.IOControlCode.AddressListQuery" /> field and add scope ID information for IPv6 addresses. This control code is supported on Windows XP and later operating systems. This value is equal to the Winsock 2 SIO_ADDRESS_LIST_SORT constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.AssociateHandle">
      <summary>Associate this socket with the specified handle of a companion interface. Refer to the appropriate  protocol-specific annex in the Winsock 2 reference or documentation for the particular companion interface for additional details. It is recommended that the Component Object Model (COM) be used instead of this IOCTL to discover and track other interfaces that might be supported by a socket. This control code is present for backward compatibility with systems where COM is not available or cannot be used for some other reason. This value is equal to the Winsock 2 SIO_ASSOCIATE_HANDLE constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.AsyncIO">
      <summary>Enable notification for when data is waiting to be received. This value is equal to the Winsock 2 FIOASYNC constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.BindToInterface">
      <summary>Bind the socket to a specified interface index. This control code is supported on Windows 2000 and later operating systems. This value is equal to the Winsock 2 SIO_INDEX_BIND constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.DataToRead">
      <summary>Return the number of bytes available for reading. This value is equal to the Winsock 2 FIONREAD constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.DeleteMulticastGroupFromInterface">
      <summary>Remove the socket from a multicast group. This control code is supported on Windows 2000 and later operating systems. This value is equal to the Winsock 2 SIO_INDEX_ADD_MCAST constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.EnableCircularQueuing">
      <summary>Replace the oldest queued datagram with an incoming datagram when the incoming message queues are full. This value is equal to the Winsock 2 SIO_ENABLE_CIRCULAR_QUEUEING constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.Flush">
      <summary>Discard the contents of the sending queue. This value is equal to the Winsock 2 SIO_FLUSH constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.GetBroadcastAddress">
      <summary>Return a SOCKADDR structure that contains the broadcast address for the address family of the current socket. The returned address can be used with the <see cref="Overload:System.Net.Sockets.Socket.SendTo" /> method. This value is equal to the Winsock 2 SIO_GET_BROADCAST_ADDRESS constant. This value can be used on User Datagram Protocol (UDP) sockets only.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.GetExtensionFunctionPointer">
      <summary>Obtain provider-specific functions that are not part of the Winsock specification. Functions are specified using their provider-assigned GUID. This value is equal to the Winsock 2 SIO_GET_EXTENSION_FUNCTION_POINTER constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.GetGroupQos">
      <summary>Return the Quality of Service (QOS) attributes for the socket group. This value is reserved for future use, and is equal to the Winsock 2 SIO_GET_GROUP_QOS constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.GetQos">
      <summary>Retrieve the QOS structure associated with the socket. This control is only supported on platforms that provide a QOS capable transport (Windows Me, Windows 2000, and later.) This value is equal to the Winsock 2 SIO_GET_QOS constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.KeepAliveValues">
      <summary>Control sending TCP keep-alive packets and the interval at which they are sent. This control code is supported on Windows 2000 and later operating systems. For additional information, see RFC 1122 section 4.2.3.6. This value is equal to the Winsock 2 SIO_KEEPALIVE_VALS constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.LimitBroadcasts">
      <summary>This value is equal to the Winsock 2 SIO_LIMIT_BROADCASTS constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.MulticastInterface">
      <summary>Set the interface used for outgoing multicast packets. The interface is identified by its index. This control code is supported on Windows 2000 and later operating systems.  This value is equal to the Winsock 2 SIO_INDEX_MCASTIF constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.MulticastScope">
      <summary>Control the number of times a multicast packet can be forwarded by a router, also known as the Time to Live (TTL), or hop count. This value is equal to the Winsock 2 SIO_MULTICAST_SCOPE constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.MultipointLoopback">
      <summary>Control whether multicast data sent by the socket appears as incoming data in the sockets receive queue. This value is equal to the Winsock 2 SIO_MULTIPOINT_LOOPBACK constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.NamespaceChange">
      <summary>Control whether the socket receives notification when a namespace query becomes invalid. This control code is supported on Windows XP and later operating systems. This value is equal to the Winsock 2 SIO_NSP_NOTIFY_CHANGE constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.NonBlockingIO">
      <summary>Control the blocking behavior of the socket. If the argument specified with this control code is zero, the socket is placed in blocking mode. If the argument is nonzero, the socket is placed in nonblocking mode. This value is equal to the Winsock 2 FIONBIO constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.OobDataRead">
      <summary>Return information about out-of-band data waiting to be received. When using this control code on stream sockets, the return value indicates the number of bytes available.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.QueryTargetPnpHandle">
      <summary>Retrieve the underlying provider's SOCKET handle. This handle can be used to receive plug-and-play event notification. This control code is supported on Windows 2000 and later operating systems. This value is equal to the Winsock 2 SIO_QUERY_TARGET_PNP_HANDLE constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.ReceiveAll">
      <summary>Enable receiving all IPv4 packets on the network. The socket must have address family <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" />, the socket type must be <see cref="F:System.Net.Sockets.SocketType.Raw" />, and the protocol type must be <see cref="F:System.Net.Sockets.ProtocolType.IP" />. The current user must belong to the Administrators group on the local computer, and the socket must be bound to a specific port. This control code is supported on Windows 2000 and later operating systems. This value is equal to the Winsock 2 SIO_RCVALL constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.ReceiveAllIgmpMulticast">
      <summary>Enable receiving all Internet Group Management Protocol (IGMP) packets on the network. The socket must have address family <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" />, the socket type must be <see cref="F:System.Net.Sockets.SocketType.Raw" />, and the protocol type must be <see cref="F:System.Net.Sockets.ProtocolType.Igmp" />. The current user must belong to the Administrators group on the local computer, and the socket must be bound to a specific port. This control code is supported on Windows 2000 and later operating systems. This value is equal to the Winsock 2 SIO_RCVALL_IGMPMCAST constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.ReceiveAllMulticast">
      <summary>Enable receiving all multicast IPv4 packets on the network. These are packets with destination addresses in the range ********* through ***************. The socket must have address family <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" />, the socket type must be <see cref="F:System.Net.Sockets.SocketType.Raw" />, and the protocol type must be <see cref="F:System.Net.Sockets.ProtocolType.Udp" />. The current user must belong to the Administrators group on the local computer, and the socket must be bound to a specific port. This control code is supported on Windows 2000 and later operating systems. This value is equal to the Winsock 2 SIO_RCVALL_MCAST constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.RoutingInterfaceChange">
      <summary>Enable receiving notification when the local interface used to access a remote endpoint changes. This value is equal to the Winsock 2 SIO_ROUTING_INTERFACE_CHANGE constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.RoutingInterfaceQuery">
      <summary>Return the interface addresses that can be used to connect to the specified remote address. This value is equal to the Winsock 2 SIO_ROUTING_INTERFACE_QUERY constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.SetGroupQos">
      <summary>Set the Quality of Service (QOS) attributes for the socket group. This value is reserved for future use and is equal to the Winsock 2 SIO_SET_GROUP_QOS constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.SetQos">
      <summary>Set the Quality of Service (QOS) attributes for the socket. QOS defines the bandwidth requirements for the socket. This control code is supported on Windows Me, Windows 2000, and later operating systems. This value is equal to the Winsock 2 SIO_SET_QOS constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.TranslateHandle">
      <summary>Return a handle for the socket that is valid in the context of a companion interface. This value is equal to the Winsock 2 SIO_TRANSLATE_HANDLE constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.IOControlCode.UnicastInterface">
      <summary>Set the interface used for outgoing unicast packets. This value is equal to the Winsock 2 SIO_UCAST_IF constant.</summary>
    </member>
    <member name="T:System.Net.Sockets.IPPacketInformation">
      <summary>Presents the packet information from a call to <see cref="M:System.Net.Sockets.Socket.ReceiveMessageFrom(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags@,System.Net.EndPoint@,System.Net.Sockets.IPPacketInformation@)" /> or <see cref="M:System.Net.Sockets.Socket.EndReceiveMessageFrom(System.IAsyncResult,System.Net.Sockets.SocketFlags@,System.Net.EndPoint@,System.Net.Sockets.IPPacketInformation@)" />.</summary>
    </member>
    <member name="P:System.Net.Sockets.IPPacketInformation.Address">
      <summary>Gets the origin information of the packet that was received as a result of calling the <see cref="M:System.Net.Sockets.Socket.ReceiveMessageFrom(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags@,System.Net.EndPoint@,System.Net.Sockets.IPPacketInformation@)" /> method or <see cref="M:System.Net.Sockets.Socket.EndReceiveMessageFrom(System.IAsyncResult,System.Net.Sockets.SocketFlags@,System.Net.EndPoint@,System.Net.Sockets.IPPacketInformation@)" /> method.</summary>
      <returns>An <see cref="T:System.Net.IPAddress" /> that indicates the origin information of the packet that was received as a result of calling the <see cref="M:System.Net.Sockets.Socket.ReceiveMessageFrom(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags@,System.Net.EndPoint@,System.Net.Sockets.IPPacketInformation@)" /> method or <see cref="M:System.Net.Sockets.Socket.EndReceiveMessageFrom(System.IAsyncResult,System.Net.Sockets.SocketFlags@,System.Net.EndPoint@,System.Net.Sockets.IPPacketInformation@)" /> method. For packets that were sent from a unicast address, the <see cref="P:System.Net.Sockets.IPPacketInformation.Address" /> property will return the <see cref="T:System.Net.IPAddress" /> of the sender; for multicast or broadcast packets, the <see cref="P:System.Net.Sockets.IPPacketInformation.Address" /> property will return the multicast or broadcast <see cref="T:System.Net.IPAddress" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.IPPacketInformation.Equals(System.Object)">
      <summary>Returns a value that indicates whether this instance is equal to a specified object.</summary>
      <param name="comparand">The object to compare with this instance.</param>
      <returns>
        <see langword="true" /> if <paramref name="comparand" /> is an instance of <see cref="T:System.Net.Sockets.IPPacketInformation" /> and equals the value of the instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.IPPacketInformation.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>An Int32 hash code.</returns>
    </member>
    <member name="P:System.Net.Sockets.IPPacketInformation.Interface">
      <summary>Gets the network interface information that is associated with a call to <see cref="M:System.Net.Sockets.Socket.ReceiveMessageFrom(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags@,System.Net.EndPoint@,System.Net.Sockets.IPPacketInformation@)" /> or <see cref="M:System.Net.Sockets.Socket.EndReceiveMessageFrom(System.IAsyncResult,System.Net.Sockets.SocketFlags@,System.Net.EndPoint@,System.Net.Sockets.IPPacketInformation@)" />.</summary>
      <returns>An <see cref="T:System.Int32" /> value, which represents the index of the network interface. You can use this index with <see cref="M:System.Net.NetworkInformation.NetworkInterface.GetAllNetworkInterfaces" /> to get more information about the relevant interface.</returns>
    </member>
    <member name="M:System.Net.Sockets.IPPacketInformation.op_Equality(System.Net.Sockets.IPPacketInformation,System.Net.Sockets.IPPacketInformation)">
      <summary>Tests whether two specified <see cref="T:System.Net.Sockets.IPPacketInformation" /> instances are equivalent.</summary>
      <param name="packetInformation1">The <see cref="T:System.Net.Sockets.IPPacketInformation" /> instance that is to the left of the equality operator.</param>
      <param name="packetInformation2">The <see cref="T:System.Net.Sockets.IPPacketInformation" /> instance that is to the right of the equality operator.</param>
      <returns>
        <see langword="true" /> if <paramref name="packetInformation1" /> and <paramref name="packetInformation2" /> are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.IPPacketInformation.op_Inequality(System.Net.Sockets.IPPacketInformation,System.Net.Sockets.IPPacketInformation)">
      <summary>Tests whether two specified <see cref="T:System.Net.Sockets.IPPacketInformation" /> instances are not equal.</summary>
      <param name="packetInformation1">The <see cref="T:System.Net.Sockets.IPPacketInformation" /> instance that is to the left of the inequality operator.</param>
      <param name="packetInformation2">The <see cref="T:System.Net.Sockets.IPPacketInformation" /> instance that is to the right of the inequality operator.</param>
      <returns>
        <see langword="true" /> if <paramref name="packetInformation1" /> and <paramref name="packetInformation2" /> are unequal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.Sockets.IPProtectionLevel">
      <summary>A value that enables restriction of an IPv6 socket to a specified scope, such as addresses with the same link local or site local prefix.</summary>
    </member>
    <member name="F:System.Net.Sockets.IPProtectionLevel.EdgeRestricted">
      <summary>The IP protection level is edge restricted. This value would be used by applications designed to operate across the Internet. This setting does not allow Network Address Translation (NAT) traversal using the Windows Teredo implementation. These applications may bypass IPv4 firewalls, so applications must be hardened against Internet attacks directed at the opened port. On Windows Server 2003 and Windows XP, the default value for the IP Protection level on a socket is edge restricted.</summary>
    </member>
    <member name="F:System.Net.Sockets.IPProtectionLevel.Restricted">
      <summary>The IP protection level is restricted. This value would be used by intranet applications that do not implement Internet scenarios. These applications are generally not tested or hardened against Internet-style attacks. This setting will limit the received traffic to link-local only.</summary>
    </member>
    <member name="F:System.Net.Sockets.IPProtectionLevel.Unrestricted">
      <summary>The IP protection level is unrestricted. This value would be used by applications designed to operate across the Internet, including applications taking advantage of IPv6 NAT traversal capabilities built into Windows (Teredo, for example). These applications may bypass IPv4 firewalls, so applications must be hardened against Internet attacks directed at the opened port. On Windows Server 2008 R2 and Windows Vista, the default value for the IP Protection level on a socket is unrestricted.</summary>
    </member>
    <member name="F:System.Net.Sockets.IPProtectionLevel.Unspecified">
      <summary>The IP protection level is unspecified. On Windows 7 and Windows Server 2008 R2, the default value for the IP Protection level on a socket is unspecified.</summary>
    </member>
    <member name="T:System.Net.Sockets.IPv6MulticastOption">
      <summary>Contains option values for joining an IPv6 multicast group.</summary>
    </member>
    <member name="M:System.Net.Sockets.IPv6MulticastOption.#ctor(System.Net.IPAddress)">
      <summary>Initializes a new version of the <see cref="T:System.Net.Sockets.IPv6MulticastOption" /> class for the specified IP multicast group.</summary>
      <param name="group">The <see cref="T:System.Net.IPAddress" /> of the multicast group.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="group" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.IPv6MulticastOption.#ctor(System.Net.IPAddress,System.Int64)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.IPv6MulticastOption" /> class with the specified IP multicast group and the local interface address.</summary>
      <param name="group">The group <see cref="T:System.Net.IPAddress" />.</param>
      <param name="ifindex">The local interface address.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="ifindex" /> is less than 0.
-or-
<paramref name="ifindex" /> is greater than 0x00000000FFFFFFFF.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="group" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Net.Sockets.IPv6MulticastOption.Group">
      <summary>Gets or sets the IP address of a multicast group.</summary>
      <returns>An <see cref="T:System.Net.IPAddress" /> that contains the Internet address of a multicast group.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="group" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Net.Sockets.IPv6MulticastOption.InterfaceIndex">
      <summary>Gets or sets the interface index that is associated with a multicast group.</summary>
      <returns>A <see cref="T:System.UInt64" /> value that specifies the address of the interface.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value that is specified for a set operation is less than 0 or greater than 0x00000000FFFFFFFF.</exception>
    </member>
    <member name="T:System.Net.Sockets.LingerOption">
      <summary>Specifies whether a <see cref="T:System.Net.Sockets.Socket" /> will remain connected after a call to the <see cref="M:System.Net.Sockets.Socket.Close" /> or <see cref="M:System.Net.Sockets.TcpClient.Close" /> methods and the length of time it will remain connected, if data remains to be sent.</summary>
    </member>
    <member name="M:System.Net.Sockets.LingerOption.#ctor(System.Boolean,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.LingerOption" /> class.</summary>
      <param name="enable">
        <see langword="true" /> to remain connected after the <see cref="M:System.Net.Sockets.Socket.Close" /> method is called; otherwise, <see langword="false" />.</param>
      <param name="seconds">The number of seconds to remain connected after the <see cref="M:System.Net.Sockets.Socket.Close" /> method is called.</param>
    </member>
    <member name="P:System.Net.Sockets.LingerOption.Enabled">
      <summary>Gets or sets a value that indicates whether to linger after the <see cref="T:System.Net.Sockets.Socket" /> is closed.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.Socket" /> should linger after <see cref="M:System.Net.Sockets.Socket.Close" /> is called; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Sockets.LingerOption.LingerTime">
      <summary>Gets or sets the amount of time to remain connected after calling the <see cref="M:System.Net.Sockets.Socket.Close" /> method if data remains to be sent.</summary>
      <returns>The amount of time, in seconds, to remain connected after calling <see cref="M:System.Net.Sockets.Socket.Close" />.</returns>
    </member>
    <member name="T:System.Net.Sockets.MulticastOption">
      <summary>Contains <see cref="T:System.Net.IPAddress" /> values used to join and drop multicast groups.</summary>
    </member>
    <member name="M:System.Net.Sockets.MulticastOption.#ctor(System.Net.IPAddress)">
      <summary>Initializes a new version of the <see cref="T:System.Net.Sockets.MulticastOption" /> class for the specified IP multicast group.</summary>
      <param name="group">The <see cref="T:System.Net.IPAddress" /> of the multicast group.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="group" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.MulticastOption.#ctor(System.Net.IPAddress,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.MulticastOption" /> class with the specified IP multicast group address and interface index.</summary>
      <param name="group">The <see cref="T:System.Net.IPAddress" /> of the multicast group.</param>
      <param name="interfaceIndex">The index of the interface that is used to send and receive multicast packets.</param>
    </member>
    <member name="M:System.Net.Sockets.MulticastOption.#ctor(System.Net.IPAddress,System.Net.IPAddress)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.MulticastOption" /> class with the specified IP multicast group address and local IP address associated with a network interface.</summary>
      <param name="group">The group <see cref="T:System.Net.IPAddress" />.</param>
      <param name="mcint">The local <see cref="T:System.Net.IPAddress" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="group" /> is <see langword="null" />.
-or-
<paramref name="mcint" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Net.Sockets.MulticastOption.Group">
      <summary>Gets or sets the IP address of a multicast group.</summary>
      <returns>An <see cref="T:System.Net.IPAddress" /> that contains the Internet address of a multicast group.</returns>
    </member>
    <member name="P:System.Net.Sockets.MulticastOption.InterfaceIndex">
      <summary>Gets or sets the index of the interface that is used to send and receive multicast packets.</summary>
      <returns>An integer that represents the index of a <see cref="T:System.Net.NetworkInformation.NetworkInterface" /> array element.</returns>
    </member>
    <member name="P:System.Net.Sockets.MulticastOption.LocalAddress">
      <summary>Gets or sets the local address associated with a multicast group.</summary>
      <returns>An <see cref="T:System.Net.IPAddress" /> that contains the local address associated with a multicast group.</returns>
    </member>
    <member name="T:System.Net.Sockets.NetworkStream">
      <summary>Provides the underlying stream of data for network access.</summary>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.#ctor(System.Net.Sockets.Socket)">
      <summary>Creates a new instance of the <see cref="T:System.Net.Sockets.NetworkStream" /> class for the specified <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="socket">The <see cref="T:System.Net.Sockets.Socket" /> that the <see cref="T:System.Net.Sockets.NetworkStream" /> will use to send and receive data.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="socket" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.IO.IOException">The <paramref name="socket" /> parameter is not connected.
-or-
The <see cref="P:System.Net.Sockets.Socket.SocketType" /> property of the <paramref name="socket" /> parameter is not <see cref="F:System.Net.Sockets.SocketType.Stream" />.
-or-
The <paramref name="socket" /> parameter is in a nonblocking state.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.#ctor(System.Net.Sockets.Socket,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.NetworkStream" /> class for the specified <see cref="T:System.Net.Sockets.Socket" /> with the specified <see cref="T:System.Net.Sockets.Socket" /> ownership.</summary>
      <param name="socket">The <see cref="T:System.Net.Sockets.Socket" /> that the <see cref="T:System.Net.Sockets.NetworkStream" /> will use to send and receive data.</param>
      <param name="ownsSocket">Set to <see langword="true" /> to indicate that the <see cref="T:System.Net.Sockets.NetworkStream" /> will take ownership of the <see cref="T:System.Net.Sockets.Socket" />; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="socket" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.IO.IOException">The <paramref name="socket" /> parameter is not connected.
-or-
the value of the <see cref="P:System.Net.Sockets.Socket.SocketType" /> property of the <paramref name="socket" /> parameter is not <see cref="F:System.Net.Sockets.SocketType.Stream" />.
-or-
the <paramref name="socket" /> parameter is in a nonblocking state.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.#ctor(System.Net.Sockets.Socket,System.IO.FileAccess)">
      <summary>Creates a new instance of the <see cref="T:System.Net.Sockets.NetworkStream" /> class for the specified <see cref="T:System.Net.Sockets.Socket" /> with the specified access rights.</summary>
      <param name="socket">The <see cref="T:System.Net.Sockets.Socket" /> that the <see cref="T:System.Net.Sockets.NetworkStream" /> will use to send and receive data.</param>
      <param name="access">A bitwise combination of the <see cref="T:System.IO.FileAccess" /> values that specify the type of access given to the <see cref="T:System.Net.Sockets.NetworkStream" /> over the provided <see cref="T:System.Net.Sockets.Socket" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="socket" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.IO.IOException">The <paramref name="socket" /> parameter is not connected.
-or-
the <see cref="P:System.Net.Sockets.Socket.SocketType" /> property of the <paramref name="socket" /> parameter is not <see cref="F:System.Net.Sockets.SocketType.Stream" />.
-or-
the <paramref name="socket" /> parameter is in a nonblocking state.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.#ctor(System.Net.Sockets.Socket,System.IO.FileAccess,System.Boolean)">
      <summary>Creates a new instance of the <see cref="T:System.Net.Sockets.NetworkStream" /> class for the specified <see cref="T:System.Net.Sockets.Socket" /> with the specified access rights and the specified <see cref="T:System.Net.Sockets.Socket" /> ownership.</summary>
      <param name="socket">The <see cref="T:System.Net.Sockets.Socket" /> that the <see cref="T:System.Net.Sockets.NetworkStream" /> will use to send and receive data.</param>
      <param name="access">A bitwise combination of the <see cref="T:System.IO.FileAccess" /> values that specifies the type of access given to the <see cref="T:System.Net.Sockets.NetworkStream" /> over the provided <see cref="T:System.Net.Sockets.Socket" />.</param>
      <param name="ownsSocket">Set to <see langword="true" /> to indicate that the <see cref="T:System.Net.Sockets.NetworkStream" /> will take ownership of the <see cref="T:System.Net.Sockets.Socket" />; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="socket" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.IO.IOException">The <paramref name="socket" /> parameter is not connected.
-or-
The <see cref="P:System.Net.Sockets.Socket.SocketType" /> property of the <paramref name="socket" /> parameter is not <see cref="F:System.Net.Sockets.SocketType.Stream" />.
-or-
The <paramref name="socket" /> parameter is in a nonblocking state.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous read from the <see cref="T:System.Net.Sockets.NetworkStream" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the location in memory to store data read from the <see cref="T:System.Net.Sockets.NetworkStream" />.</param>
      <param name="offset">The location in <paramref name="buffer" /> to begin storing the data.</param>
      <param name="size">The number of bytes to read from the <see cref="T:System.Net.Sockets.NetworkStream" />.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate that is executed when <see cref="M:System.Net.Sockets.NetworkStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> completes.</param>
      <param name="state">An object that contains any additional user-defined data.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous call.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> parameter is less than 0.
-or-
The <paramref name="offset" /> parameter is greater than the length of the <paramref name="buffer" /> paramater.
-or-
The <paramref name="size" /> is less than 0.
-or-
The <paramref name="size" /> is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
      <exception cref="T:System.IO.IOException">The underlying <see cref="T:System.Net.Sockets.Socket" /> is closed.
-or-
There was a failure while reading from the network.
-or-
An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous write to a stream.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the data to write to the <see cref="T:System.Net.Sockets.NetworkStream" />.</param>
      <param name="offset">The location in <paramref name="buffer" /> to begin sending the data.</param>
      <param name="size">The number of bytes to write to the <see cref="T:System.Net.Sockets.NetworkStream" />.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate that is executed when <see cref="M:System.Net.Sockets.NetworkStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> completes.</param>
      <param name="state">An object that contains any additional user-defined data.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that represents the asynchronous call.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> parameter is less than 0.
-or-
The <paramref name="offset" /> parameter is greater than the length of <paramref name="buffer" />.
-or-
The <paramref name="size" /> parameter is less than 0.
-or-
The <paramref name="size" /> parameter is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
      <exception cref="T:System.IO.IOException">The underlying <see cref="T:System.Net.Sockets.Socket" /> is closed.
-or-
There was a failure while writing to the network.
-or-
An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.NetworkStream.CanRead">
      <summary>Gets a value that indicates whether the <see cref="T:System.Net.Sockets.NetworkStream" /> supports reading.</summary>
      <returns>
        <see langword="true" /> if data can be read from the stream; otherwise, <see langword="false" />. The default value is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Net.Sockets.NetworkStream.CanSeek">
      <summary>Gets a value that indicates whether the stream supports seeking. This property is not currently supported.This property always returns <see langword="false" />.</summary>
      <returns>
        <see langword="false" /> in all cases to indicate that <see cref="T:System.Net.Sockets.NetworkStream" /> cannot seek a specific location in the stream.</returns>
    </member>
    <member name="P:System.Net.Sockets.NetworkStream.CanTimeout">
      <summary>Indicates whether timeout properties are usable for <see cref="T:System.Net.Sockets.NetworkStream" />.</summary>
      <returns>
        <see langword="true" /> in all cases.</returns>
    </member>
    <member name="P:System.Net.Sockets.NetworkStream.CanWrite">
      <summary>Gets a value that indicates whether the <see cref="T:System.Net.Sockets.NetworkStream" /> supports writing.</summary>
      <returns>
        <see langword="true" /> if data can be written to the <see cref="T:System.Net.Sockets.NetworkStream" />; otherwise, <see langword="false" />. The default value is <see langword="true" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.Close(System.Int32)">
      <summary>Closes the <see cref="T:System.Net.Sockets.NetworkStream" /> after waiting the specified time to allow data to be sent.</summary>
      <param name="timeout">A 32-bit signed integer that specifies the number of milliseconds to wait to send any remaining data before closing.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="timeout" /> parameter is less than -1.</exception>
    </member>
    <member name="P:System.Net.Sockets.NetworkStream.DataAvailable">
      <summary>Gets a value that indicates whether data is available on the <see cref="T:System.Net.Sockets.NetworkStream" /> to be read.</summary>
      <returns>
        <see langword="true" /> if data is available on the stream to be read; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
      <exception cref="T:System.IO.IOException">The underlying <see cref="T:System.Net.Sockets.Socket" /> is closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Use the <see cref="P:System.Net.Sockets.SocketException.ErrorCode" /> property to obtain the specific error code and refer to the Windows Sockets version 2 API error code documentation for a detailed description of the error.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Sockets.NetworkStream" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.EndRead(System.IAsyncResult)">
      <summary>Handles the end of an asynchronous read.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> that represents an asynchronous call.</param>
      <returns>The number of bytes read from the <see cref="T:System.Net.Sockets.NetworkStream" />.</returns>
      <exception cref="T:System.ArgumentException">The <paramref name="asyncResult" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.IO.IOException">The underlying <see cref="T:System.Net.Sockets.Socket" /> is closed.
-or-
An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.EndWrite(System.IAsyncResult)">
      <summary>Handles the end of an asynchronous write.</summary>
      <param name="asyncResult">The <see cref="T:System.IAsyncResult" /> that represents the asynchronous call.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="asyncResult" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.IO.IOException">The underlying <see cref="T:System.Net.Sockets.Socket" /> is closed.
-or-
An error occurred while writing to the network.
-or-
An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.Finalize">
      <summary>Releases all resources used by the <see cref="T:System.Net.Sockets.NetworkStream" />.</summary>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.Flush">
      <summary>Flushes data from the stream. This method is reserved for future use.</summary>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>Flushes data from the stream as an asynchronous operation.</summary>
      <param name="cancellationToken">A cancellation token used to propagate notification that this  operation should be canceled.</param>
      <returns>The task object representing the asynchronous operation.</returns>
    </member>
    <member name="P:System.Net.Sockets.NetworkStream.Length">
      <summary>Gets the length of the data available on the stream. This property is not currently supported and always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <returns>The length of the data available on the stream.</returns>
      <exception cref="T:System.NotSupportedException">Any use of this property.</exception>
    </member>
    <member name="P:System.Net.Sockets.NetworkStream.Position">
      <summary>Gets or sets the current position in the stream. This property is not currently supported and always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <returns>The current position in the stream.</returns>
      <exception cref="T:System.NotSupportedException">Any use of this property.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Reads data from the <see cref="T:System.Net.Sockets.NetworkStream" /> and stores it to a byte array.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the location in memory to store data read from the <see cref="T:System.Net.Sockets.NetworkStream" />.</param>
      <param name="offset">The location in <paramref name="buffer" /> to begin storing the data to.</param>
      <param name="size">The number of bytes to read from the <see cref="T:System.Net.Sockets.NetworkStream" />.</param>
      <returns>The number of bytes read from the <see cref="T:System.Net.Sockets.NetworkStream" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is greater than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of <paramref name="buffer" /> minus <paramref name="offset" />.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.NetworkStream" /> does not support reading.</exception>
      <exception cref="">An error occurred when accessing the socket.
-or-
There is a failure reading from the network.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.Read(System.Span{System.Byte})">
      <summary>Reads data from the <see cref="T:System.Net.Sockets.NetworkStream" /> and stores it to a span of bytes in memory.</summary>
      <param name="buffer">A region of memory to store data read from the <see cref="T:System.Net.Sockets.NetworkStream" />.</param>
      <returns>The number of bytes read from the <see cref="T:System.Net.Sockets.NetworkStream" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.NetworkStream" /> does not support reading.</exception>
      <exception cref="">An error occurred when accessing the socket.
-or-
There is a failure reading from the network.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.NetworkStream.Readable">
      <summary>Gets or sets a value that indicates whether the <see cref="T:System.Net.Sockets.NetworkStream" /> can be read.</summary>
      <returns>
        <see langword="true" /> to indicate that the <see cref="T:System.Net.Sockets.NetworkStream" /> can be read; otherwise, <see langword="false" />. The default value is <see langword="true" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Reads data from the <see cref="T:System.Net.Sockets.NetworkStream" /> and stores it to a specified range of a byte array as an asynchronous operation.</summary>
      <param name="buffer">The buffer to write the data into.</param>
      <param name="offset">The location in <paramref name="buffer" /> to begin storing the data to.</param>
      <param name="size">The number of bytes to read from the <see cref="T:System.Net.Sockets.NetworkStream" />.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests.</param>
      <returns>A task that represents the asynchronous read operation. The value of its <see cref="P:System.Threading.Tasks.Task`1.Result" /> property contains the total number of bytes read into <paramref name="buffer" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.NetworkStream" /> does not support reading.</exception>
      <exception cref="">An error occurred when accessing the socket.
-or-
There is a failure reading from the network.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.ReadAsync(System.Memory{System.Byte},System.Threading.CancellationToken)">
      <summary>Reads data from the <see cref="T:System.Net.Sockets.NetworkStream" /> and stores it in a byte memory range as an asynchronous operation.</summary>
      <param name="buffer">The buffer to write the data to.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests.</param>
      <returns>A <see cref="T:System.Threading.Tasks.ValueTask`1" /> that represents the asynchronous read operation. The value of its <see cref="P:System.Threading.Tasks.ValueTask`1.Result" /> property contains the total number of bytes read into <paramref name="buffer" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.NetworkStream" /> does not support reading.</exception>
      <exception cref="">An error occurred when accessing the socket.
-or-
There is a failure reading from the network.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.ReadByte">
      <summary>Reads a byte from the <see cref="T:System.Net.Sockets.NetworkStream" /> and advances the position within the stream by one byte, or returns -1 if at the end of the stream.</summary>
      <returns>The unsigned byte cast to an <see langword="Int32" />, or -1 if at the end of the stream.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.NetworkStream" /> does not support reading.</exception>
      <exception cref="">An error occurred when accessing the socket.
-or-
There is a failure reading from the network.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.NetworkStream.ReadTimeout">
      <summary>Gets or sets the amount of time that a read operation blocks waiting for data.</summary>
      <returns>A <see cref="T:System.Int32" /> that specifies the amount of time, in milliseconds, that will elapse before a read operation fails. The default value, <see cref="F:System.Threading.Timeout.Infinite" />, specifies that the read operation does not time out.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified is less than or equal to zero and is not <see cref="F:System.Threading.Timeout.Infinite" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Sets the current position of the stream to the given value. This method is not currently supported and always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <param name="offset">This parameter is not used.</param>
      <param name="origin">This parameter is not used.</param>
      <returns>The position in the stream.</returns>
      <exception cref="T:System.NotSupportedException">Any use of this property.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.SetLength(System.Int64)">
      <summary>Sets the length of the stream. This method always throws a <see cref="T:System.NotSupportedException" />.</summary>
      <param name="value">This parameter is not used.</param>
      <exception cref="T:System.NotSupportedException">Any use of this property.</exception>
    </member>
    <member name="P:System.Net.Sockets.NetworkStream.Socket">
      <summary>Gets the underlying <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>A <see cref="T:System.Net.Sockets.Socket" /> that represents the underlying network connection.</returns>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Writes data to the <see cref="T:System.Net.Sockets.NetworkStream" /> from a specified range of a byte array.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the data to write to the <see cref="T:System.Net.Sockets.NetworkStream" />.</param>
      <param name="offset">The location in <paramref name="buffer" /> from which to start writing data.</param>
      <param name="size">The number of bytes to write to the <see cref="T:System.Net.Sockets.NetworkStream" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> parameter is less than 0.
-or-
The <paramref name="offset" /> parameter is greater than the length of <paramref name="buffer" />.
-or-
The <paramref name="size" /> parameter is less than 0.
-or-
The <paramref name="size" /> parameter is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.NetworkStream" /> does not support writing.</exception>
      <exception cref="T:System.IO.IOException">An error occurred when accessing the socket.
-or-
There was a failure while writing to the network.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.Write(System.ReadOnlySpan{System.Byte})">
      <summary>Writes data to the <see cref="T:System.Net.Sockets.NetworkStream" />  from a read-only byte span.</summary>
      <param name="buffer">The data to write to the <see cref="T:System.Net.Sockets.NetworkStream" />.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.NetworkStream" /> does not support writing.</exception>
      <exception cref="T:System.IO.IOException">An error occurred when accessing the socket.
-or-
There was a failure while writing to the network.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.NetworkStream.Writeable">
      <summary>Gets a value that indicates whether the <see cref="T:System.Net.Sockets.NetworkStream" /> is writable.</summary>
      <returns>
        <see langword="true" /> if data can be written to the stream; otherwise, <see langword="false" />. The default value is <see langword="true" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Writes data to the <see cref="T:System.Net.Sockets.NetworkStream" /> from the specified range of a byte array as an asynchronous operation.</summary>
      <param name="buffer">A byte array that contains the data to write to the <see cref="T:System.Net.Sockets.NetworkStream" />.</param>
      <param name="offset">The location in <paramref name="buffer" /> from which to start writing data.</param>
      <param name="size">The number of bytes to write to the <see cref="T:System.Net.Sockets.NetworkStream" />.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests.</param>
      <returns>A task that represents the asynchronous write operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> parameter is less than 0.
-or-
The <paramref name="offset" /> parameter is greater than the length of <paramref name="buffer" />.
-or-
The <paramref name="size" /> parameter is less than 0.
-or-
The <paramref name="size" /> parameter is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.NetworkStream" /> does not support writing.</exception>
      <exception cref="T:System.IO.IOException">There was a failure while writing to the network.
-or-
An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.WriteAsync(System.ReadOnlyMemory{System.Byte},System.Threading.CancellationToken)">
      <summary>Writes data to the <see cref="T:System.Net.Sockets.NetworkStream" /> from a read-only memory byte memory range as an asynchronous operation.</summary>
      <param name="buffer">A region of memory that contains the data to write to the <see cref="T:System.Net.Sockets.NetworkStream" />.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests.</param>
      <returns>A task that represents the asynchronous write operation.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.NetworkStream" /> does not support writing.</exception>
      <exception cref="T:System.IO.IOException">An error occurred when accessing the socket.
-or-
There was a failure while writing to the network.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.NetworkStream.WriteByte(System.Byte)">
      <summary>Writes a byte to the current position in the <see cref="T:System.Net.Sockets.NetworkStream" /> and advances the position within the stream by one byte.</summary>
      <param name="value">The byte to write to the stream.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.NetworkStream" /> does not support writing.</exception>
      <exception cref="T:System.IO.IOException">An error occurred when accessing the socket.
-or-
There was a failure while writing to the network.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.NetworkStream" /> is closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.NetworkStream.WriteTimeout">
      <summary>Gets or sets the amount of time that a write operation blocks waiting for data.</summary>
      <returns>A <see cref="T:System.Int32" /> that specifies the amount of time, in milliseconds, that will elapse before a write operation fails. The default value, <see cref="F:System.Threading.Timeout.Infinite" />, specifies that the write operation does not time out.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified is less than or equal to zero and is not <see cref="F:System.Threading.Timeout.Infinite" />.</exception>
    </member>
    <member name="T:System.Net.Sockets.ProtocolFamily">
      <summary>Specifies the type of protocol that an instance of the <see cref="T:System.Net.Sockets.Socket" /> class can use.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.AppleTalk">
      <summary>AppleTalk protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Atm">
      <summary>Native ATM services protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Banyan">
      <summary>Banyan protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Ccitt">
      <summary>CCITT protocol, such as X.25.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Chaos">
      <summary>MIT CHAOS protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Cluster">
      <summary>Microsoft Cluster products protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.ControllerAreaNetwork">
      <summary>Controller Area Network protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.DataKit">
      <summary>DataKit protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.DataLink">
      <summary>Direct data link protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.DecNet">
      <summary>DECNet protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Ecma">
      <summary>European Computer Manufacturers Association (ECMA) protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.FireFox">
      <summary>FireFox protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.HyperChannel">
      <summary>NSC HyperChannel protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Ieee12844">
      <summary>IEEE 1284.4 workgroup protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.ImpLink">
      <summary>ARPANET IMP protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.InterNetwork">
      <summary>IP version 4 protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.InterNetworkV6">
      <summary>IP version 6 protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Ipx">
      <summary>IPX or SPX protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Irda">
      <summary>IrDA protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Iso">
      <summary>ISO protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Lat">
      <summary>LAT protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Max">
      <summary>MAX protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.NetBios">
      <summary>NetBIOS protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.NetworkDesigners">
      <summary>Network Designers OSI gateway enabled protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.NS">
      <summary>Xerox NS protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Osi">
      <summary>OSI protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Packet">
      <summary>Low-level Packet protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Pup">
      <summary>PUP protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Sna">
      <summary>IBM SNA protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Unix">
      <summary>Unix local to host protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Unknown">
      <summary>Unknown protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.Unspecified">
      <summary>Unspecified protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolFamily.VoiceView">
      <summary>VoiceView protocol.</summary>
    </member>
    <member name="T:System.Net.Sockets.ProtocolType">
      <summary>Specifies the protocols that the <see cref="T:System.Net.Sockets.Socket" /> class supports.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Ggp">
      <summary>Gateway To Gateway Protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Icmp">
      <summary>Internet Control Message Protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.IcmpV6">
      <summary>Internet Control Message Protocol for IPv6.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Idp">
      <summary>Internet Datagram Protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Igmp">
      <summary>Internet Group Management Protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.IP">
      <summary>Internet Protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.IPSecAuthenticationHeader">
      <summary>IPv6 Authentication header. For details, see RFC 2292 section 2.2.1, available at https://www.ietf.org.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.IPSecEncapsulatingSecurityPayload">
      <summary>IPv6 Encapsulating Security Payload header.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.IPv4">
      <summary>Internet Protocol version 4.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.IPv6">
      <summary>Internet Protocol version 6 (IPv6).</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.IPv6DestinationOptions">
      <summary>IPv6 Destination Options header.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.IPv6FragmentHeader">
      <summary>IPv6 Fragment header.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.IPv6HopByHopOptions">
      <summary>IPv6 Hop by Hop Options header.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.IPv6NoNextHeader">
      <summary>IPv6 No next header.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.IPv6RoutingHeader">
      <summary>IPv6 Routing header.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Ipx">
      <summary>Internet Packet Exchange Protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.ND">
      <summary>Net Disk Protocol (unofficial).</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Pup">
      <summary>PARC Universal Packet Protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Raw">
      <summary>Raw IP packet protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Spx">
      <summary>Sequenced Packet Exchange protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.SpxII">
      <summary>Sequenced Packet Exchange version 2 protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Tcp">
      <summary>Transmission Control Protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Udp">
      <summary>User Datagram Protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Unknown">
      <summary>Unknown protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Unspecified">
      <summary>Unspecified protocol.</summary>
    </member>
    <member name="T:System.Net.Sockets.SafeSocketHandle">
      <summary>Represents a wrapper class for a socket handle.</summary>
    </member>
    <member name="M:System.Net.Sockets.SafeSocketHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SafeSocketHandle" /> class.</summary>
      <param name="preexistingHandle">The pre-existing handle to use.</param>
      <param name="ownsHandle">
        <see langword="true" /> to reliably release the handle during the finalization phase; <see langword="false" /> to prevent reliable release (not recommended).</param>
    </member>
    <member name="T:System.Net.Sockets.SelectMode">
      <summary>Defines the polling modes for the <see cref="M:System.Net.Sockets.Socket.Poll(System.Int32,System.Net.Sockets.SelectMode)" /> method.</summary>
    </member>
    <member name="F:System.Net.Sockets.SelectMode.SelectError">
      <summary>Error status mode.</summary>
    </member>
    <member name="F:System.Net.Sockets.SelectMode.SelectRead">
      <summary>Read status mode.</summary>
    </member>
    <member name="F:System.Net.Sockets.SelectMode.SelectWrite">
      <summary>Write status mode.</summary>
    </member>
    <member name="T:System.Net.Sockets.SendPacketsElement">
      <summary>Represents an element in a <see cref="T:System.Net.Sockets.SendPacketsElement" /> array.</summary>
    </member>
    <member name="M:System.Net.Sockets.SendPacketsElement.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SendPacketsElement" /> class using the specified buffer.</summary>
      <param name="buffer">A byte array of data to send using the <see cref="M:System.Net.Sockets.Socket.SendPacketsAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> method.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> parameter cannot be null</exception>
    </member>
    <member name="M:System.Net.Sockets.SendPacketsElement.#ctor(System.Byte[],System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SendPacketsElement" /> class using the specified range of the buffer.</summary>
      <param name="buffer">A byte array of data to send using the <see cref="M:System.Net.Sockets.Socket.SendPacketsAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> method.</param>
      <param name="offset">The offset, in bytes, from the beginning of the <paramref name="buffer" /> to the location in the <paramref name="buffer" /> to start sending the data specified in the <paramref name="buffer" /> parameter.</param>
      <param name="count">The number of bytes to send starting from the <paramref name="offset" /> parameter. If <paramref name="count" /> is zero, no bytes are sent.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> parameter cannot be null</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> and <paramref name="count" /> parameters must be greater than or equal to zero.
The <paramref name="offset" /> and <paramref name="count" /> must be less than the size of the buffer</exception>
    </member>
    <member name="M:System.Net.Sockets.SendPacketsElement.#ctor(System.Byte[],System.Int32,System.Int32,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SendPacketsElement" /> class using the specified range of the buffer with an option to combine this element with the next element in a single send request from the sockets layer to the transport.</summary>
      <param name="buffer">A byte array of data to send using the <see cref="M:System.Net.Sockets.Socket.SendPacketsAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> method.</param>
      <param name="offset">The offset, in bytes, from the beginning of the <paramref name="buffer" /> to the location in the <paramref name="buffer" /> to start sending the data specified in the <paramref name="buffer" /> parameter.</param>
      <param name="count">The number bytes to send starting from the <paramref name="offset" /> parameter. If <paramref name="count" /> is zero, no bytes are sent.</param>
      <param name="endOfPacket">A Boolean value that specifies that this element should not be combined with the next element in a single send request from the sockets layer to the transport. This flag is used for granular control of the content of each message on a datagram or message-oriented socket.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> parameter cannot be null</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> and <paramref name="count" /> parameters must be greater than or equal to zero.
The <paramref name="offset" /> and <paramref name="count" /> must be less than the size of the buffer</exception>
    </member>
    <member name="M:System.Net.Sockets.SendPacketsElement.#ctor(System.IO.FileStream)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SendPacketsElement" /> class using the specified <see cref="T:System.IO.FileStream" /> object.</summary>
      <param name="fileStream">The file to be transmitted using the <see cref="M:System.Net.Sockets.Socket.SendPacketsAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> method.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileStream" /> is not open for asynchronous reading and writing.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileStream" /> cannot be null.</exception>
    </member>
    <member name="M:System.Net.Sockets.SendPacketsElement.#ctor(System.IO.FileStream,System.Int64,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SendPacketsElement" /> class using the specified range of a <see cref="T:System.IO.FileStream" /> object.</summary>
      <param name="fileStream">The file to be transmitted using the <see cref="M:System.Net.Sockets.Socket.SendPacketsAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> method.</param>
      <param name="offset">The offset, in bytes, from the beginning of the file to the location in the file to start sending the bytes in the file.</param>
      <param name="count">The number of bytes to send starting from the <paramref name="offset" />. If <paramref name="count" /> is zero, the entire file is sent.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="fileStream" /> parameter must have been opened for asynchronous reading and writing.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="fileStream" /> parameter cannot be <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> and <paramref name="count" /> parameters must be greater than or equal to zero.</exception>
    </member>
    <member name="M:System.Net.Sockets.SendPacketsElement.#ctor(System.IO.FileStream,System.Int64,System.Int32,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SendPacketsElement" /> class using the specified range of a <see cref="T:System.IO.FileStream" /> object with an option to combine this element with the next element in a single send request from the sockets layer to the transport.</summary>
      <param name="fileStream">The file to be transmitted using the <see cref="M:System.Net.Sockets.Socket.SendPacketsAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> method.</param>
      <param name="offset">TThe offset, in bytes, from the beginning of the file to the location in the file to start sending the bytes in the file.</param>
      <param name="count">The number of bytes to send starting from the offset parameter. If count is zero, the entire file is sent.</param>
      <param name="endOfPacket">Specifies that this element should not be combined with the next element in a single send request from the sockets layer to the transport. This flag is used for granular control of the content of each message on a datagram or message-oriented socket.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="fileStream" /> parameter must have been opened for asynchronous reading and writing.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="fileStream" /> parameter cannot be <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> and <paramref name="count" /> parameters must be greater than or equal to zero.</exception>
    </member>
    <member name="M:System.Net.Sockets.SendPacketsElement.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SendPacketsElement" /> class using the specified file.</summary>
      <param name="filepath">The filename of the file to be transmitted using the <see cref="M:System.Net.Sockets.Socket.SendPacketsAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> method.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="filepath" /> parameter cannot be null</exception>
    </member>
    <member name="M:System.Net.Sockets.SendPacketsElement.#ctor(System.String,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SendPacketsElement" /> class using the specified range of the file.</summary>
      <param name="filepath">The filename of the file to be transmitted using the <see cref="M:System.Net.Sockets.Socket.SendPacketsAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> method.</param>
      <param name="offset">The offset, in bytes, from the beginning of the file to the location in the file to start sending the file specified in the <paramref name="filepath" /> parameter.</param>
      <param name="count">The number of bytes to send starting from the <paramref name="offset" /> parameter. If <paramref name="count" /> is zero, the entire file is sent.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="filepath" /> parameter cannot be null</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> and <paramref name="count" /> parameters must be greater than or equal to zero.</exception>
    </member>
    <member name="M:System.Net.Sockets.SendPacketsElement.#ctor(System.String,System.Int32,System.Int32,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SendPacketsElement" /> class using the specified range of the file with an option to combine this element with the next element in a single send request from the sockets layer to the transport.</summary>
      <param name="filepath">The filename of the file to be transmitted using the <see cref="M:System.Net.Sockets.Socket.SendPacketsAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> method.</param>
      <param name="offset">The offset, in bytes, from the beginning of the file to the location in the file to start sending the file specified in the <paramref name="filepath" /> parameter.</param>
      <param name="count">The number of bytes to send starting from the <paramref name="offset" /> parameter. If <paramref name="count" /> is zero, the entire file is sent.</param>
      <param name="endOfPacket">A Boolean value that specifies that this element should not be combined with the next element in a single send request from the sockets layer to the transport. This flag is used for granular control of the content of each message on a datagram or message-oriented socket.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="filepath" /> parameter cannot be null</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> and <paramref name="count" /> parameters must be greater than or equal to zero.</exception>
    </member>
    <member name="M:System.Net.Sockets.SendPacketsElement.#ctor(System.String,System.Int64,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SendPacketsElement" /> class using the specified range of the file.</summary>
      <param name="filepath">The filename of the file to be transmitted using the <see cref="M:System.Net.Sockets.Socket.SendPacketsAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> method.</param>
      <param name="offset">The offset, in bytes, from the beginning of the file to the location in the file to start sending the file specified in the <paramref name="filepath" /> parameter.</param>
      <param name="count">The number of bytes to send starting from the <paramref name="offset" />. If <paramref name="count" /> is zero, the entire file is sent.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="filepath" /> parameter cannot be <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> and <paramref name="count" /> parameters must be greater than or equal to zero.</exception>
    </member>
    <member name="M:System.Net.Sockets.SendPacketsElement.#ctor(System.String,System.Int64,System.Int32,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SendPacketsElement" /> class using the specified range of the file with an option to combine this element with the next element in a single send request from the sockets layer to the transport.</summary>
      <param name="filepath">The filename of the file to be transmitted using the <see cref="M:System.Net.Sockets.Socket.SendPacketsAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> method.</param>
      <param name="offset">The offset, in bytes, from the beginning of the file to the location in the file to start sending the file specified in the <paramref name="filepath" /> parameter.</param>
      <param name="count">The number of bytes to send starting from the <paramref name="offset" /> parameter. If <paramref name="count" /> is zero, the entire file is sent.</param>
      <param name="endOfPacket">Specifies that this element should not be combined with the next element in a single send request from the sockets layer to the transport. This flag is used for granular control of the content of each message on a datagram or message-oriented socket.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="filepath" /> parameter cannot be <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="offset" /> and <paramref name="count" /> parameters must be greater than or equal to zero.</exception>
    </member>
    <member name="P:System.Net.Sockets.SendPacketsElement.Buffer">
      <summary>Gets the buffer to be sent if the <see cref="T:System.Net.Sockets.SendPacketsElement" /> object was initialized with a <paramref name="buffer" /> parameter.</summary>
      <returns>The byte buffer to send if the <see cref="T:System.Net.Sockets.SendPacketsElement" /> object was initialized with a <paramref name="buffer" /> parameter.</returns>
    </member>
    <member name="P:System.Net.Sockets.SendPacketsElement.Count">
      <summary>Gets the count of bytes to be sent.</summary>
      <returns>The count of bytes to send if the <see cref="T:System.Net.Sockets.SendPacketsElement" /> class was initialized with a <paramref name="count" /> parameter.</returns>
    </member>
    <member name="P:System.Net.Sockets.SendPacketsElement.EndOfPacket">
      <summary>Gets a Boolean value that indicates if this element should not be combined with the next element in a single send request from the sockets layer to the transport.</summary>
      <returns>A Boolean value that indicates if this element should not be combined with the next element in a single send request.</returns>
    </member>
    <member name="P:System.Net.Sockets.SendPacketsElement.FilePath">
      <summary>Gets the filename of the file to send if the <see cref="T:System.Net.Sockets.SendPacketsElement" /> object was initialized with a <paramref name="filepath" /> parameter.</summary>
      <returns>The filename of the file to send if the <see cref="T:System.Net.Sockets.SendPacketsElement" /> object was initialized with a <paramref name="filepath" /> parameter.</returns>
    </member>
    <member name="P:System.Net.Sockets.SendPacketsElement.FileStream">
      <summary>Gets the object representation of the file to send if the <see cref="T:System.Net.Sockets.SendPacketsElement" /> object was initialized with a <see cref="T:System.IO.FileStream" /> parameter.</summary>
      <returns>An object representation of the file to send if the <see cref="T:System.Net.Sockets.SendPacketsElement" /> object was initialized with a <see cref="T:System.IO.FileStream" /> parameter.</returns>
    </member>
    <member name="P:System.Net.Sockets.SendPacketsElement.Offset">
      <summary>Gets the offset, in bytes, from the beginning of the data buffer or file to the location in the buffer or file to start sending the data.</summary>
      <returns>The offset, in bytes, from the beginning of the data buffer or file to the location in the buffer or file to start sending the data.</returns>
    </member>
    <member name="P:System.Net.Sockets.SendPacketsElement.OffsetLong">
      <summary>Gets the offset, in bytes, from the beginning of the data buffer or file to the location in the buffer or file to start sending the data.</summary>
      <returns>The offset, in bytes, from the beginning of the data buffer or file to the location in the buffer or file to start sending the data.</returns>
    </member>
    <member name="T:System.Net.Sockets.Socket">
      <summary>Implements the Berkeley sockets interface.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.#ctor(System.Net.Sockets.AddressFamily,System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.Socket" /> class using the specified address family, socket type and protocol.</summary>
      <param name="addressFamily">One of the <see cref="T:System.Net.Sockets.AddressFamily" /> values.</param>
      <param name="socketType">One of the <see cref="T:System.Net.Sockets.SocketType" /> values.</param>
      <param name="protocolType">One of the <see cref="T:System.Net.Sockets.ProtocolType" /> values.</param>
      <exception cref="T:System.Net.Sockets.SocketException">The combination of <paramref name="addressFamily" />, <paramref name="socketType" />, and <paramref name="protocolType" /> results in an invalid socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.#ctor(System.Net.Sockets.SocketInformation)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.Socket" /> class using the specified value returned from <see cref="M:System.Net.Sockets.Socket.DuplicateAndClose(System.Int32)" />.</summary>
      <param name="socketInformation">The socket information returned by <see cref="M:System.Net.Sockets.Socket.DuplicateAndClose(System.Int32)" />.</param>
    </member>
    <member name="M:System.Net.Sockets.Socket.#ctor(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.Socket" /> class using the specified socket type and protocol.</summary>
      <param name="socketType">One of the <see cref="T:System.Net.Sockets.SocketType" /> values.</param>
      <param name="protocolType">One of the <see cref="T:System.Net.Sockets.ProtocolType" /> values.</param>
      <exception cref="T:System.Net.Sockets.SocketException">The combination of  <paramref name="socketType" /> and <paramref name="protocolType" /> results in an invalid socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Accept">
      <summary>Creates a new <see cref="T:System.Net.Sockets.Socket" /> for a newly created connection.</summary>
      <returns>A <see cref="T:System.Net.Sockets.Socket" /> for a newly created connection.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.InvalidOperationException">The accepting socket is not listening for connections. You must call <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)" /> and <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" /> before calling <see cref="M:System.Net.Sockets.Socket.Accept" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Begins an asynchronous operation to accept an incoming connection attempt.</summary>
      <param name="e">The <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object to use for this asynchronous socket operation.</param>
      <returns>
        <see langword="true" /> if the I/O operation is pending. The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will be raised upon completion of the operation.
<see langword="false" /> if the I/O operation completed synchronously. The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will not be raised and the <paramref name="e" /> object passed as a parameter may be examined immediately after the method call returns to retrieve the result of the operation.</returns>
      <exception cref="T:System.ArgumentException">An argument is not valid. This exception occurs if the buffer provided is not large enough. The buffer must be at least 2 * (sizeof(SOCKADDR_STORAGE + 16) bytes.
This exception also occurs if multiple buffers are specified, the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> property is not null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An argument is out of range. The exception occurs if the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Count" /> is less than 0.</exception>
      <exception cref="T:System.InvalidOperationException">An invalid operation was requested. This exception occurs if the accepting <see cref="T:System.Net.Sockets.Socket" /> is not listening for connections or the accepted socket is bound.
You must call the <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)" /> and <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" /> method before calling the <see cref="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> method.
This exception also occurs if the socket is already connected or a socket operation was already in progress using the specified <paramref name="e" /> parameter.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP or later is required for this method.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.AddressFamily">
      <summary>Gets the address family of the <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>One of the <see cref="T:System.Net.Sockets.AddressFamily" /> values.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.Available">
      <summary>Gets the amount of data that has been received from the network and is available to be read.</summary>
      <returns>The number of bytes of data received from the network and available to be read.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginAccept(System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation to accept an incoming connection attempt.</summary>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate.</param>
      <param name="state">An object that contains state information for this request.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous <see cref="T:System.Net.Sockets.Socket" /> creation.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> object has been closed.</exception>
      <exception cref="T:System.NotSupportedException">Windows NT is required for this method.</exception>
      <exception cref="T:System.InvalidOperationException">The accepting socket is not listening for connections. You must call <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)" /> and <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" /> before calling <see cref="M:System.Net.Sockets.Socket.BeginAccept(System.AsyncCallback,System.Object)" />.
-or-
The accepted socket is bound.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="receiveSize" /> is less than 0.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginAccept(System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation to accept an incoming connection attempt and receives the first block of data sent by the client application.</summary>
      <param name="receiveSize">The number of bytes to accept from the sender.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate.</param>
      <param name="state">An object that contains state information for this request.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous <see cref="T:System.Net.Sockets.Socket" /> creation.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> object has been closed.</exception>
      <exception cref="T:System.NotSupportedException">Windows NT is required for this method.</exception>
      <exception cref="T:System.InvalidOperationException">The accepting socket is not listening for connections. You must call <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)" /> and <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" /> before calling <see cref="M:System.Net.Sockets.Socket.BeginAccept(System.AsyncCallback,System.Object)" />.
-or-
The accepted socket is bound.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="receiveSize" /> is less than 0.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginAccept(System.Net.Sockets.Socket,System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation to accept an incoming connection attempt from a specified socket and receives the first block of data sent by the client application.</summary>
      <param name="acceptSocket">The accepted <see cref="T:System.Net.Sockets.Socket" /> object. This value may be <see langword="null" />.</param>
      <param name="receiveSize">The maximum number of bytes to receive.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate.</param>
      <param name="state">An object that contains state information for this request.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> object that references the asynchronous <see cref="T:System.Net.Sockets.Socket" /> object creation.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> object has been closed.</exception>
      <exception cref="T:System.NotSupportedException">Windows NT is required for this method.</exception>
      <exception cref="T:System.InvalidOperationException">The accepting socket is not listening for connections. You must call <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)" /> and <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" /> before calling <see cref="M:System.Net.Sockets.Socket.BeginAccept(System.AsyncCallback,System.Object)" />.
-or-
The accepted socket is bound.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="receiveSize" /> is less than 0.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginConnect(System.Net.EndPoint,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous request for a remote host connection.</summary>
      <param name="remoteEP">An <see cref="T:System.Net.EndPoint" /> that represents the remote host.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate.</param>
      <param name="state">An object that contains state information for this request.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous connection.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="remoteEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller higher in the call stack does not have permission for the requested operation.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.Socket" /> has been placed in a listening state by calling <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" />, or an asynchronous operation is already in progress.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginConnect(System.Net.IPAddress,System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous request for a remote host connection. The host is specified by an <see cref="T:System.Net.IPAddress" /> and a port number.</summary>
      <param name="address">The <see cref="T:System.Net.IPAddress" /> of the remote host.</param>
      <param name="port">The port number of the remote host.</param>
      <param name="requestCallback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the connect operation is complete.</param>
      <param name="state">A user-defined object that contains information about the connect operation. This object is passed to the <paramref name="requestCallback" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous connection.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="address" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Net.Sockets.Socket" /> is not in the socket family.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The port number is not valid.</exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="address" /> is zero.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.Socket" /> has been placed in a listening state by calling <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" />, or an asynchronous operation is already in progress.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginConnect(System.Net.IPAddress[],System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous request for a remote host connection. The host is specified by an <see cref="T:System.Net.IPAddress" /> array and a port number.</summary>
      <param name="addresses">At least one <see cref="T:System.Net.IPAddress" />, designating the remote host.</param>
      <param name="port">The port number of the remote host.</param>
      <param name="requestCallback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the connect operation is complete.</param>
      <param name="state">A user-defined object that contains information about the connect operation. This object is passed to the <paramref name="requestCallback" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous connections.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="addresses" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.NotSupportedException">This method is valid for sockets that use <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> or <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The port number is not valid.</exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="address" /> is zero.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.Socket" /> has been placed in a listening state by calling <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" />, or an asynchronous operation is already in progress.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginConnect(System.String,System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous request for a remote host connection. The host is specified by a host name and a port number.</summary>
      <param name="host">The name of the remote host.</param>
      <param name="port">The port number of the remote host.</param>
      <param name="requestCallback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the connect operation is complete.</param>
      <param name="state">A user-defined object that contains information about the connect operation. This object is passed to the <paramref name="requestCallback" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous connection.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="host" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.NotSupportedException">This method is valid for sockets in the <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> or <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" /> families.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The port number is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.Socket" /> has been placed in a listening state by calling <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" />, or an asynchronous operation is already in progress.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginDisconnect(System.Boolean,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous request to disconnect from a remote endpoint.</summary>
      <param name="reuseSocket">
        <see langword="true" /> if this socket can be reused after the connection is closed; otherwise, <see langword="false" />.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate.</param>
      <param name="state">An object that contains state information for this request.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> object that references the asynchronous operation.</returns>
      <exception cref="T:System.NotSupportedException">The operating system is Windows 2000 or earlier, and this method requires Windows XP.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> object has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginReceive(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.AsyncCallback,System.Object)">
      <summary>Begins to asynchronously receive data from a connected <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the storage location for the received data.</param>
      <param name="offset">The zero-based position in the <paramref name="buffer" /> parameter at which to store the received data.</param>
      <param name="size">The number of bytes to receive.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="callback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the operation is complete.</param>
      <param name="state">A user-defined object that contains information about the receive operation. This object is passed to the <see cref="M:System.Net.Sockets.Socket.EndReceive(System.IAsyncResult)" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous read.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is greater than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginReceive(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.Net.Sockets.SocketError@,System.AsyncCallback,System.Object)">
      <summary>Begins to asynchronously receive data from a connected <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the storage location for the received data.</param>
      <param name="offset">The location in <paramref name="buffer" /> to store the received data.</param>
      <param name="size">The number of bytes to receive.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="errorCode">A <see cref="T:System.Net.Sockets.SocketError" /> object that stores the socket error.</param>
      <param name="callback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the operation is complete.</param>
      <param name="state">A user-defined object that contains information about the receive operation. This object is passed to the <see cref="M:System.Net.Sockets.Socket.EndReceive(System.IAsyncResult)" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous read.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is greater than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginReceive(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.Net.Sockets.SocketFlags,System.AsyncCallback,System.Object)">
      <summary>Begins to asynchronously receive data from a connected <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="buffers">An array of type <see cref="T:System.Byte" /> that is the storage location for the received data.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="callback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the operation is complete.</param>
      <param name="state">A user-defined object that contains information about the receive operation. This object is passed to the <see cref="M:System.Net.Sockets.Socket.EndReceive(System.IAsyncResult)" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous read.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginReceive(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.Net.Sockets.SocketFlags,System.Net.Sockets.SocketError@,System.AsyncCallback,System.Object)">
      <summary>Begins to asynchronously receive data from a connected <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="buffers">An array of type <see cref="T:System.Byte" /> that is the storage location for the received data.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="errorCode">A <see cref="T:System.Net.Sockets.SocketError" /> object that stores the socket error.</param>
      <param name="callback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the operation is complete.</param>
      <param name="state">A user-defined object that contains information about the receive operation. This object is passed to the <see cref="M:System.Net.Sockets.Socket.EndReceive(System.IAsyncResult)" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous read.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginReceiveFrom(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.Net.EndPoint@,System.AsyncCallback,System.Object)">
      <summary>Begins to asynchronously receive data from a specified network device.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the storage location for the received data.</param>
      <param name="offset">The zero-based position in the <paramref name="buffer" /> parameter at which to store the data.</param>
      <param name="size">The number of bytes to receive.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="remoteEP">An <see cref="T:System.Net.EndPoint" /> that represents the source of the data.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate.</param>
      <param name="state">An object that contains state information for this request.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous read.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.
-or-
<paramref name="remoteEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is greater than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller higher in the call stack does not have permission for the requested operation.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginReceiveMessageFrom(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.Net.EndPoint@,System.AsyncCallback,System.Object)">
      <summary>Begins to asynchronously receive the specified number of bytes of data into the specified location of the data buffer, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />, and stores the endpoint and packet information.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the storage location for the received data.</param>
      <param name="offset">The zero-based position in the <paramref name="buffer" /> parameter at which to store the data.</param>
      <param name="size">The number of bytes to receive.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="remoteEP">An <see cref="T:System.Net.EndPoint" /> that represents the source of the data.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate.</param>
      <param name="state">An object that contains state information for this request.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous read.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.
-or-
<paramref name="remoteEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is greater than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.NotSupportedException">The operating system is Windows 2000 or earlier, and this method requires Windows XP.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginSend(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.AsyncCallback,System.Object)">
      <summary>Sends data asynchronously to a connected <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the data to send.</param>
      <param name="offset">The zero-based position in the <paramref name="buffer" /> parameter at which to begin sending data.</param>
      <param name="size">The number of bytes to send.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate.</param>
      <param name="state">An object that contains state information for this request.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous send.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket. See remarks section below.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is less than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginSend(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.Net.Sockets.SocketError@,System.AsyncCallback,System.Object)">
      <summary>Sends data asynchronously to a connected <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the data to send.</param>
      <param name="offset">The zero-based position in the <paramref name="buffer" /> parameter at which to begin sending data.</param>
      <param name="size">The number of bytes to send.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="errorCode">A <see cref="T:System.Net.Sockets.SocketError" /> object that stores the socket error.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate.</param>
      <param name="state">An object that contains state information for this request.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous send.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket. See remarks section below.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is less than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginSend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.Net.Sockets.SocketFlags,System.AsyncCallback,System.Object)">
      <summary>Sends data asynchronously to a connected <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="buffers">An array of type <see cref="T:System.Byte" /> that contains the data to send.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate.</param>
      <param name="state">An object that contains state information for this request.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous send.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffers" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="buffers" /> is empty.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket. See remarks section below.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginSend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.Net.Sockets.SocketFlags,System.Net.Sockets.SocketError@,System.AsyncCallback,System.Object)">
      <summary>Sends data asynchronously to a connected <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="buffers">An array of type <see cref="T:System.Byte" /> that contains the data to send.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="errorCode">A <see cref="T:System.Net.Sockets.SocketError" /> object that stores the socket error.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate.</param>
      <param name="state">An object that contains state information for this request.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous send.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffers" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="buffers" /> is empty.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket. See remarks section below.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginSendFile(System.String,System.AsyncCallback,System.Object)">
      <summary>Sends the file <paramref name="fileName" /> to a connected <see cref="T:System.Net.Sockets.Socket" /> object using the <see cref="F:System.Net.Sockets.TransmitFileOptions.UseDefaultWorkerThread" /> flag.</summary>
      <param name="fileName">A string that contains the path and name of the file to send. This parameter can be <see langword="null" />.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate.</param>
      <param name="state">An object that contains state information for this request.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> object that represents the asynchronous send.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> object has been closed.</exception>
      <exception cref="T:System.NotSupportedException">The socket is not connected to a remote host.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file <paramref name="fileName" /> was not found.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket. See remarks section below.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginSendFile(System.String,System.Byte[],System.Byte[],System.Net.Sockets.TransmitFileOptions,System.AsyncCallback,System.Object)">
      <summary>Sends a file and buffers of data asynchronously to a connected <see cref="T:System.Net.Sockets.Socket" /> object.</summary>
      <param name="fileName">A string that contains the path and name of the file to be sent. This parameter can be <see langword="null" />.</param>
      <param name="preBuffer">A <see cref="T:System.Byte" /> array that contains data to be sent before the file is sent. This parameter can be <see langword="null" />.</param>
      <param name="postBuffer">A <see cref="T:System.Byte" /> array that contains data to be sent after the file is sent. This parameter can be <see langword="null" />.</param>
      <param name="flags">A bitwise combination of <see cref="T:System.Net.Sockets.TransmitFileOptions" /> values.</param>
      <param name="callback">An <see cref="T:System.AsyncCallback" /> delegate to be invoked when this operation completes. This parameter can be <see langword="null" />.</param>
      <param name="state">A user-defined object that contains state information for this request. This parameter can be <see langword="null" />.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> object that represents the asynchronous operation.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> object has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket. See remarks section below.</exception>
      <exception cref="T:System.NotSupportedException">The operating system is not Windows NT or later.
-or-
The socket is not connected to a remote host.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file <paramref name="fileName" /> was not found.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.BeginSendTo(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.Net.EndPoint,System.AsyncCallback,System.Object)">
      <summary>Sends data asynchronously to a specific remote host.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the data to send.</param>
      <param name="offset">The zero-based position in <paramref name="buffer" /> at which to begin sending data.</param>
      <param name="size">The number of bytes to send.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="remoteEP">An <see cref="T:System.Net.EndPoint" /> that represents the remote device.</param>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate.</param>
      <param name="state">An object that contains state information for this request.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous send.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.
-or-
<paramref name="remoteEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is greater than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller higher in the call stack does not have permission for the requested operation.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)">
      <summary>Associates a <see cref="T:System.Net.Sockets.Socket" /> with a local endpoint.</summary>
      <param name="localEP">The local <see cref="T:System.Net.EndPoint" /> to associate with the <see cref="T:System.Net.Sockets.Socket" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="localEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller higher in the call stack does not have permission for the requested operation.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.Blocking">
      <summary>Gets or sets a value that indicates whether the <see cref="T:System.Net.Sockets.Socket" /> is in blocking mode.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.Socket" /> will block; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.CancelConnectAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Cancels an asynchronous request for a remote host connection.</summary>
      <param name="e">The <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object used to request the connection to the remote host by calling one of the <see cref="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType,System.Net.Sockets.SocketAsyncEventArgs)" /> methods.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="e" /> parameter cannot be null and the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> cannot be null.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller higher in the call stack does not have permission for the requested operation.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Close">
      <summary>Closes the <see cref="T:System.Net.Sockets.Socket" /> connection and releases all associated resources.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.Close(System.Int32)">
      <summary>Closes the <see cref="T:System.Net.Sockets.Socket" /> connection and releases all associated resources with a specified timeout to allow queued data to be sent.</summary>
      <param name="timeout">Wait up to <paramref name="timeout" /> seconds to send any remaining data, then close the socket.</param>
    </member>
    <member name="M:System.Net.Sockets.Socket.Connect(System.Net.EndPoint)">
      <summary>Establishes a connection to a remote host.</summary>
      <param name="remoteEP">An <see cref="T:System.Net.EndPoint" /> that represents the remote device.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="remoteEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller higher in the call stack does not have permission for the requested operation.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.Socket" /> has been placed in a listening state by calling <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Connect(System.Net.IPAddress,System.Int32)">
      <summary>Establishes a connection to a remote host. The host is specified by an IP address and a port number.</summary>
      <param name="address">The IP address of the remote host.</param>
      <param name="port">The port number of the remote host.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="address" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The port number is not valid.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.NotSupportedException">This method is valid for sockets in the <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> or <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" /> families.</exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="address" /> is zero.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.Socket" /> has been placed in a listening state by calling <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Connect(System.Net.IPAddress[],System.Int32)">
      <summary>Establishes a connection to a remote host. The host is specified by an array of IP addresses and a port number.</summary>
      <param name="addresses">The IP addresses of the remote host.</param>
      <param name="port">The port number of the remote host.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="addresses" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The port number is not valid.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.NotSupportedException">This method is valid for sockets in the <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> or <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" /> families.</exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="address" /> is zero.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.Socket" /> has been placed in a listening state by calling <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Connect(System.String,System.Int32)">
      <summary>Establishes a connection to a remote host. The host is specified by a host name and a port number.</summary>
      <param name="host">The name of the remote host.</param>
      <param name="port">The port number of the remote host.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="host" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The port number is not valid.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.NotSupportedException">This method is valid for sockets in the <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> or <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" /> families.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.Socket" /> has been placed in a listening state by calling <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Begins an asynchronous request for a connection to a remote host.</summary>
      <param name="e">The <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object to use for this asynchronous socket operation.</param>
      <returns>
        <see langword="true" /> if the I/O operation is pending. The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will be raised upon completion of the operation.
<see langword="false" /> if the I/O operation completed synchronously. In this case, The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will not be raised and the <paramref name="e" /> object passed as a parameter may be examined immediately after the method call returns to retrieve the result of the operation.</returns>
      <exception cref="T:System.ArgumentException">An argument is not valid. This exception occurs if multiple buffers are specified, the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> property is not null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="e" /> parameter cannot be null and the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> cannot be null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.Socket" /> is listening or a socket operation was already in progress using the <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object specified in the <paramref name="e" /> parameter.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP or later is required for this method. This exception also occurs if the local endpoint and the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> are not the same address family.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller higher in the call stack does not have permission for the requested operation.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType,System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Begins an asynchronous request for a connection to a remote host.</summary>
      <param name="socketType">One of the <see cref="T:System.Net.Sockets.SocketType" /> values.</param>
      <param name="protocolType">One of the <see cref="T:System.Net.Sockets.ProtocolType" /> values.</param>
      <param name="e">The <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object to use for this asynchronous socket operation.</param>
      <returns>
        <see langword="true" /> if the I/O operation is pending. The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will be raised upon completion of the operation.
<see langword="false" /> if the I/O operation completed synchronously. In this case, The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will not be raised and the <paramref name="e" /> object passed as a parameter may be examined immediately after the method call returns to retrieve the result of the operation.</returns>
      <exception cref="T:System.ArgumentException">An argument is not valid. This exception occurs if multiple buffers are specified, the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> property is not null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="e" /> parameter cannot be null and the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> cannot be null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.Socket" /> is listening or a socket operation was already in progress using the <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object specified in the <paramref name="e" /> parameter.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP or later is required for this method. This exception also occurs if the local endpoint and the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> are not the same address family.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller higher in the call stack does not have permission for the requested operation.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.Connected">
      <summary>Gets a value that indicates whether a <see cref="T:System.Net.Sockets.Socket" /> is connected to a remote host as of the last <see cref="Overload:System.Net.Sockets.Socket.Send" /> or <see cref="Overload:System.Net.Sockets.Socket.Receive" /> operation.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.Socket" /> was connected to a remote resource as of the most recent operation; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.Disconnect(System.Boolean)">
      <summary>Closes the socket connection and allows reuse of the socket.</summary>
      <param name="reuseSocket">
        <see langword="true" /> if this socket can be reused after the current connection is closed; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.PlatformNotSupportedException">This method requires Windows 2000 or earlier, or the exception will be thrown.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> object has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.DisconnectAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Begins an asynchronous request to disconnect from a remote endpoint.</summary>
      <param name="e">The <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object to use for this asynchronous socket operation.</param>
      <returns>
        <see langword="true" /> if the I/O operation is pending. The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will be raised upon completion of the operation.
<see langword="false" /> if the I/O operation completed synchronously. In this case, The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will not be raised and the <paramref name="e" /> object passed as a parameter may be examined immediately after the method call returns to retrieve the result of the operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="e" /> parameter cannot be null.</exception>
      <exception cref="T:System.InvalidOperationException">A socket operation was already in progress using the <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object specified in the <paramref name="e" /> parameter.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP or later is required for this method.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.Net.Sockets.Socket" /> class.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Sockets.Socket" />, and optionally disposes of the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to releases only unmanaged resources.</param>
    </member>
    <member name="P:System.Net.Sockets.Socket.DontFragment">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that specifies whether the <see cref="T:System.Net.Sockets.Socket" /> allows Internet Protocol (IP) datagrams to be fragmented.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.Socket" /> allows datagram fragmentation; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
      <exception cref="T:System.NotSupportedException">This property can be set only for sockets in the <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> or <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" /> families.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.DualMode">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that specifies whether the <see cref="T:System.Net.Sockets.Socket" /> is a dual-mode socket used for both IPv4 and IPv6.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.Socket" /> is a  dual-mode socket; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.DuplicateAndClose(System.Int32)">
      <summary>Duplicates the socket reference for the target process, and closes the socket for this process.</summary>
      <param name="targetProcessId">The ID of the target process where a duplicate of the socket reference is created.</param>
      <returns>The socket reference to be passed to the target process.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">
        <paramref name="targetProcessID" /> is not a valid process id.
-or-
Duplication of the socket reference failed.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.EnableBroadcast">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that specifies whether the <see cref="T:System.Net.Sockets.Socket" /> can send or receive broadcast packets.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.Socket" /> allows broadcast packets; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">This option is valid for a datagram socket only.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.EndAccept(System.Byte[]@,System.IAsyncResult)">
      <summary>Asynchronously accepts an incoming connection attempt and creates a new <see cref="T:System.Net.Sockets.Socket" /> object to handle remote host communication. This method returns a buffer that contains the initial data transferred.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the bytes transferred.</param>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> object that stores state information for this asynchronous operation as well as any user defined data.</param>
      <returns>A <see cref="T:System.Net.Sockets.Socket" /> object to handle communication with the remote host.</returns>
      <exception cref="T:System.NotSupportedException">Windows NT is required for this method.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> object has been closed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is empty.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not created by a call to <see cref="M:System.Net.Sockets.Socket.BeginAccept(System.AsyncCallback,System.Object)" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.EndAccept(System.IAsyncResult)" /> method was previously called.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the <see cref="T:System.Net.Sockets.Socket" /></exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.EndAccept(System.Byte[]@,System.Int32@,System.IAsyncResult)">
      <summary>Asynchronously accepts an incoming connection attempt and creates a new <see cref="T:System.Net.Sockets.Socket" /> object to handle remote host communication. This method returns a buffer that contains the initial data and the number of bytes transferred.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the bytes transferred.</param>
      <param name="bytesTransferred">The number of bytes transferred.</param>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> object that stores state information for this asynchronous operation as well as any user defined data.</param>
      <returns>A <see cref="T:System.Net.Sockets.Socket" /> object to handle communication with the remote host.</returns>
      <exception cref="T:System.NotSupportedException">Windows NT is required for this method.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> object has been closed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is empty.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not created by a call to <see cref="M:System.Net.Sockets.Socket.BeginAccept(System.AsyncCallback,System.Object)" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.EndAccept(System.IAsyncResult)" /> method was previously called.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the <see cref="T:System.Net.Sockets.Socket" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.EndAccept(System.IAsyncResult)">
      <summary>Asynchronously accepts an incoming connection attempt and creates a new <see cref="T:System.Net.Sockets.Socket" /> to handle remote host communication.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> that stores state information for this asynchronous operation as well as any user defined data.</param>
      <returns>A <see cref="T:System.Net.Sockets.Socket" /> to handle communication with the remote host.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not created by a call to <see cref="M:System.Net.Sockets.Socket.BeginAccept(System.AsyncCallback,System.Object)" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket. See the Remarks section for more information.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.EndAccept(System.IAsyncResult)" /> method was previously called.</exception>
      <exception cref="T:System.NotSupportedException">Windows NT is required for this method.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.EndConnect(System.IAsyncResult)">
      <summary>Ends a pending asynchronous connection request.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> that stores state information and any user defined data for this asynchronous operation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by a call to the <see cref="M:System.Net.Sockets.Socket.BeginConnect(System.Net.EndPoint,System.AsyncCallback,System.Object)" /> method.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.EndConnect(System.IAsyncResult)" /> was previously called for the asynchronous connection.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.EndDisconnect(System.IAsyncResult)">
      <summary>Ends a pending asynchronous disconnect request.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> object that stores state information and any user-defined data for this asynchronous operation.</param>
      <exception cref="T:System.NotSupportedException">The operating system is Windows 2000 or earlier, and this method requires Windows XP.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> object has been closed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by a call to the <see cref="M:System.Net.Sockets.Socket.BeginDisconnect(System.Boolean,System.AsyncCallback,System.Object)" /> method.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.EndDisconnect(System.IAsyncResult)" /> was previously called for the asynchronous connection.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.Net.WebException">The disconnect request has timed out.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.EndReceive(System.IAsyncResult)">
      <summary>Ends a pending asynchronous read.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> that stores state information and any user defined data for this asynchronous operation.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by a call to the <see cref="M:System.Net.Sockets.Socket.BeginReceive(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.AsyncCallback,System.Object)" /> method.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.EndReceive(System.IAsyncResult)" /> was previously called for the asynchronous read.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.EndReceive(System.IAsyncResult,System.Net.Sockets.SocketError@)">
      <summary>Ends a pending asynchronous read.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> that stores state information and any user defined data for this asynchronous operation.</param>
      <param name="errorCode">A <see cref="T:System.Net.Sockets.SocketError" /> object that stores the socket error.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by a call to the <see cref="M:System.Net.Sockets.Socket.BeginReceive(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.AsyncCallback,System.Object)" /> method.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.EndReceive(System.IAsyncResult)" /> was previously called for the asynchronous read.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.EndReceiveFrom(System.IAsyncResult,System.Net.EndPoint@)">
      <summary>Ends a pending asynchronous read from a specific endpoint.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> that stores state information and any user defined data for this asynchronous operation.</param>
      <param name="endPoint">The source <see cref="T:System.Net.EndPoint" />.</param>
      <returns>If successful, the number of bytes received. If unsuccessful, returns 0.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by a call to the <see cref="M:System.Net.Sockets.Socket.BeginReceiveFrom(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.Net.EndPoint@,System.AsyncCallback,System.Object)" /> method.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.EndReceiveFrom(System.IAsyncResult,System.Net.EndPoint@)" /> was previously called for the asynchronous read.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.EndReceiveMessageFrom(System.IAsyncResult,System.Net.Sockets.SocketFlags@,System.Net.EndPoint@,System.Net.Sockets.IPPacketInformation@)">
      <summary>Ends a pending asynchronous read from a specific endpoint. This method also reveals more information about the packet than <see cref="M:System.Net.Sockets.Socket.EndReceiveFrom(System.IAsyncResult,System.Net.EndPoint@)" />.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> that stores state information and any user defined data for this asynchronous operation.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values for the received packet.</param>
      <param name="endPoint">The source <see cref="T:System.Net.EndPoint" />.</param>
      <param name="ipPacketInformation">The <see cref="T:System.Net.IPAddress" /> and interface of the received packet.</param>
      <returns>If successful, the number of bytes received. If unsuccessful, returns 0.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />
-or-
<paramref name="endPoint" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by a call to the <see cref="M:System.Net.Sockets.Socket.BeginReceiveMessageFrom(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.Net.EndPoint@,System.AsyncCallback,System.Object)" /> method.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.EndReceiveMessageFrom(System.IAsyncResult,System.Net.Sockets.SocketFlags@,System.Net.EndPoint@,System.Net.Sockets.IPPacketInformation@)" /> was previously called for the asynchronous read.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.EndSend(System.IAsyncResult)">
      <summary>Ends a pending asynchronous send.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> that stores state information for this asynchronous operation.</param>
      <returns>If successful, the number of bytes sent to the <see cref="T:System.Net.Sockets.Socket" />; otherwise, an invalid <see cref="T:System.Net.Sockets.Socket" /> error.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by a call to the <see cref="M:System.Net.Sockets.Socket.BeginSend(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.AsyncCallback,System.Object)" /> method.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.EndSend(System.IAsyncResult)" /> was previously called for the asynchronous send.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.EndSend(System.IAsyncResult,System.Net.Sockets.SocketError@)">
      <summary>Ends a pending asynchronous send.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> that stores state information for this asynchronous operation.</param>
      <param name="errorCode">A <see cref="T:System.Net.Sockets.SocketError" /> object that stores the socket error.</param>
      <returns>If successful, the number of bytes sent to the <see cref="T:System.Net.Sockets.Socket" />; otherwise, an invalid <see cref="T:System.Net.Sockets.Socket" /> error.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by a call to the <see cref="M:System.Net.Sockets.Socket.BeginSend(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.AsyncCallback,System.Object)" /> method.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.EndSend(System.IAsyncResult)" /> was previously called for the asynchronous send.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.EndSendFile(System.IAsyncResult)">
      <summary>Ends a pending asynchronous send of a file.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> object that stores state information for this asynchronous operation.</param>
      <exception cref="T:System.NotSupportedException">Windows NT is required for this method.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> object has been closed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is empty.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by a call to the <see cref="M:System.Net.Sockets.Socket.BeginSendFile(System.String,System.AsyncCallback,System.Object)" /> method.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.EndSendFile(System.IAsyncResult)" /> was previously called for the asynchronous <see cref="M:System.Net.Sockets.Socket.BeginSendFile(System.String,System.AsyncCallback,System.Object)" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket. See remarks section below.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.EndSendTo(System.IAsyncResult)">
      <summary>Ends a pending asynchronous send to a specific location.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> that stores state information and any user defined data for this asynchronous operation.</param>
      <returns>If successful, the number of bytes sent; otherwise, an invalid <see cref="T:System.Net.Sockets.Socket" /> error.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by a call to the <see cref="M:System.Net.Sockets.Socket.BeginSendTo(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.Net.EndPoint,System.AsyncCallback,System.Object)" /> method.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.EndSendTo(System.IAsyncResult)" /> was previously called for the asynchronous send.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.ExclusiveAddressUse">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that specifies whether the <see cref="T:System.Net.Sockets.Socket" /> allows only one process to bind to a port.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.Socket" /> allows only one socket to bind to a specific port; otherwise, <see langword="false" />. The default is <see langword="true" /> for Windows Server 2003 and Windows XP Service Pack 2, and <see langword="false" /> for all other versions.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)" /> has been called for this <see cref="T:System.Net.Sockets.Socket" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Finalize">
      <summary>Frees resources used by the <see cref="T:System.Net.Sockets.Socket" /> class.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.GetSocketOption(System.Net.Sockets.SocketOptionLevel,System.Net.Sockets.SocketOptionName)">
      <summary>Returns the value of a specified <see cref="T:System.Net.Sockets.Socket" /> option, represented as an object.</summary>
      <param name="optionLevel">One of the <see cref="T:System.Net.Sockets.SocketOptionLevel" /> values.</param>
      <param name="optionName">One of the <see cref="T:System.Net.Sockets.SocketOptionName" /> values.</param>
      <returns>An object that represents the value of the option. When the <paramref name="optionName" /> parameter is set to <see cref="F:System.Net.Sockets.SocketOptionName.Linger" /> the return value is an instance of the <see cref="T:System.Net.Sockets.LingerOption" /> class. When <paramref name="optionName" /> is set to <see cref="F:System.Net.Sockets.SocketOptionName.AddMembership" /> or <see cref="F:System.Net.Sockets.SocketOptionName.DropMembership" />, the return value is an instance of the <see cref="T:System.Net.Sockets.MulticastOption" /> class. When <paramref name="optionName" /> is any other value, the return value is an integer.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.
-or-
<paramref name="optionName" /> was set to the unsupported value <see cref="F:System.Net.Sockets.SocketOptionName.MaxConnections" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.GetSocketOption(System.Net.Sockets.SocketOptionLevel,System.Net.Sockets.SocketOptionName,System.Byte[])">
      <summary>Returns the specified <see cref="T:System.Net.Sockets.Socket" /> option setting, represented as a byte array.</summary>
      <param name="optionLevel">One of the <see cref="T:System.Net.Sockets.SocketOptionLevel" /> values.</param>
      <param name="optionName">One of the <see cref="T:System.Net.Sockets.SocketOptionName" /> values.</param>
      <param name="optionValue">An array of type <see cref="T:System.Byte" /> that is to receive the option setting.</param>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.
-or-
In .NET Compact Framework applications, the Windows CE default buffer space is set to 32768 bytes. You can change the per socket buffer space by calling <see cref="Overload:System.Net.Sockets.Socket.SetSocketOption" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.GetSocketOption(System.Net.Sockets.SocketOptionLevel,System.Net.Sockets.SocketOptionName,System.Int32)">
      <summary>Returns the value of the specified <see cref="T:System.Net.Sockets.Socket" /> option in an array.</summary>
      <param name="optionLevel">One of the <see cref="T:System.Net.Sockets.SocketOptionLevel" /> values.</param>
      <param name="optionName">One of the <see cref="T:System.Net.Sockets.SocketOptionName" /> values.</param>
      <param name="optionLength">The length, in bytes, of the expected return value.</param>
      <returns>An array of type <see cref="T:System.Byte" /> that contains the value of the socket option.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.
-or-
In .NET Compact Framework applications, the Windows CE default buffer space is set to 32768 bytes. You can change the per socket buffer space by calling <see cref="Overload:System.Net.Sockets.Socket.SetSocketOption" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.Handle">
      <summary>Gets the operating system handle for the <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>An <see cref="T:System.IntPtr" /> that represents the operating system handle for the <see cref="T:System.Net.Sockets.Socket" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.IOControl(System.Int32,System.Byte[],System.Byte[])">
      <summary>Sets low-level operating modes for the <see cref="T:System.Net.Sockets.Socket" /> using numerical control codes.</summary>
      <param name="ioControlCode">An <see cref="T:System.Int32" /> value that specifies the control code of the operation to perform.</param>
      <param name="optionInValue">A <see cref="T:System.Byte" /> array that contains the input data required by the operation.</param>
      <param name="optionOutValue">A <see cref="T:System.Byte" /> array that contains the output data returned by the operation.</param>
      <returns>The number of bytes in the <paramref name="optionOutValue" /> parameter.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.InvalidOperationException">An attempt was made to change the blocking mode without using the <see cref="P:System.Net.Sockets.Socket.Blocking" /> property.</exception>
      <exception cref="T:System.Security.SecurityException">A caller in the call stack does not have the required permissions.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.IOControl(System.Net.Sockets.IOControlCode,System.Byte[],System.Byte[])">
      <summary>Sets low-level operating modes for the <see cref="T:System.Net.Sockets.Socket" /> using the <see cref="T:System.Net.Sockets.IOControlCode" /> enumeration to specify control codes.</summary>
      <param name="ioControlCode">A <see cref="T:System.Net.Sockets.IOControlCode" /> value that specifies the control code of the operation to perform.</param>
      <param name="optionInValue">An array of type <see cref="T:System.Byte" /> that contains the input data required by the operation.</param>
      <param name="optionOutValue">An array of type <see cref="T:System.Byte" /> that contains the output data returned by the operation.</param>
      <returns>The number of bytes in the <paramref name="optionOutValue" /> parameter.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.InvalidOperationException">An attempt was made to change the blocking mode without using the <see cref="P:System.Net.Sockets.Socket.Blocking" /> property.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.IsBound">
      <summary>Gets a value that indicates whether the <see cref="T:System.Net.Sockets.Socket" /> is bound to a specific local port.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.Socket" /> is bound to a local port; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.LingerState">
      <summary>Gets or sets a value that specifies whether the <see cref="T:System.Net.Sockets.Socket" /> will delay closing a socket in an attempt to send all pending data.</summary>
      <returns>A <see cref="T:System.Net.Sockets.LingerOption" /> that specifies how to linger while closing a socket.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Listen(System.Int32)">
      <summary>Places a <see cref="T:System.Net.Sockets.Socket" /> in a listening state.</summary>
      <param name="backlog">The maximum length of the pending connections queue.</param>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.LocalEndPoint">
      <summary>Gets the local endpoint.</summary>
      <returns>The <see cref="T:System.Net.EndPoint" /> that the <see cref="T:System.Net.Sockets.Socket" /> is using for communications.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.MulticastLoopback">
      <summary>Gets or sets a value that specifies whether outgoing multicast packets are delivered to the sending application.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.Socket" /> receives outgoing multicast packets; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.NoDelay">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that specifies whether the stream <see cref="T:System.Net.Sockets.Socket" /> is using the Nagle algorithm.</summary>
      <returns>
        <see langword="false" /> if the <see cref="T:System.Net.Sockets.Socket" /> uses the Nagle algorithm; otherwise, <see langword="true" />. The default is <see langword="false" />.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the <see cref="T:System.Net.Sockets.Socket" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.OSSupportsIPv4">
      <summary>Indicates whether the underlying operating system and network adaptors support Internet Protocol version 4 (IPv4).</summary>
      <returns>
        <see langword="true" /> if the operating system and network adaptors support the IPv4 protocol; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.OSSupportsIPv6">
      <summary>Indicates whether the underlying operating system and network adaptors support Internet Protocol version 6 (IPv6).</summary>
      <returns>
        <see langword="true" /> if the operating system and network adaptors support the IPv6 protocol; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.Poll(System.Int32,System.Net.Sockets.SelectMode)">
      <summary>Determines the status of the <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="microSeconds">The time to wait for a response, in microseconds.</param>
      <param name="mode">One of the <see cref="T:System.Net.Sockets.SelectMode" /> values.</param>
      <returns>The status of the <see cref="T:System.Net.Sockets.Socket" /> based on the polling mode value passed in the <paramref name="mode" /> parameter.
  Mode  
  
  Return Value  
  
 <see cref="F:System.Net.Sockets.SelectMode.SelectRead" /><see langword="true" /> if <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" /> has been called and a connection is pending;  
  
 -or-  
  
 <see langword="true" /> if data is available for reading;  
  
 -or-  
  
 <see langword="true" /> if the connection has been closed, reset, or terminated;  
  
 otherwise, returns <see langword="false" />.  
  
 <see cref="F:System.Net.Sockets.SelectMode.SelectWrite" /><see langword="true" />, if processing a <see cref="M:System.Net.Sockets.Socket.Connect(System.Net.EndPoint)" />, and the connection has succeeded;  
  
 -or-  
  
 <see langword="true" /> if data can be sent;  
  
 otherwise, returns <see langword="false" />.  
  
 <see cref="F:System.Net.Sockets.SelectMode.SelectError" /><see langword="true" /> if processing a <see cref="M:System.Net.Sockets.Socket.Connect(System.Net.EndPoint)" /> that does not block, and the connection has failed;  
  
 -or-  
  
 <see langword="true" /> if <see cref="F:System.Net.Sockets.SocketOptionName.OutOfBandInline" /> is not set and out-of-band data is available;  
  
 otherwise, returns <see langword="false" />.</returns>
      <exception cref="T:System.NotSupportedException">The <paramref name="mode" /> parameter is not one of the <see cref="T:System.Net.Sockets.SelectMode" /> values.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket. See remarks below.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.ProtocolType">
      <summary>Gets the protocol type of the <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>One of the <see cref="T:System.Net.Sockets.ProtocolType" /> values.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.Receive(System.Byte[])">
      <summary>Receives data from a bound <see cref="T:System.Net.Sockets.Socket" /> into a receive buffer.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the storage location for the received data.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller in the call stack does not have the required permissions.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Receive(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags)">
      <summary>Receives the specified number of bytes from a bound <see cref="T:System.Net.Sockets.Socket" /> into the specified offset position of the receive buffer, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the storage location for received data.</param>
      <param name="offset">The location in <paramref name="buffer" /> to store the received data.</param>
      <param name="size">The number of bytes to receive.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is greater than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">
        <paramref name="socketFlags" /> is not a valid combination of values.
-or-
The <see cref="P:System.Net.Sockets.Socket.LocalEndPoint" /> property was not set.
-or-
An operating system error occurs while accessing the <see cref="T:System.Net.Sockets.Socket" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller in the call stack does not have the required permissions.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Receive(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.Net.Sockets.SocketError@)">
      <summary>Receives data from a bound <see cref="T:System.Net.Sockets.Socket" /> into a receive buffer, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the storage location for the received data.</param>
      <param name="offset">The position in the <paramref name="buffer" /> parameter to store the received data.</param>
      <param name="size">The number of bytes to receive.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="errorCode">A <see cref="T:System.Net.Sockets.SocketError" /> object that stores the socket error.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is greater than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">
        <paramref name="socketFlags" /> is not a valid combination of values.
-or-
The <see cref="P:System.Net.Sockets.Socket.LocalEndPoint" /> property is not set.
-or-
An operating system error occurs while accessing the <see cref="T:System.Net.Sockets.Socket" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller in the call stack does not have the required permissions.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Receive(System.Byte[],System.Int32,System.Net.Sockets.SocketFlags)">
      <summary>Receives the specified number of bytes of data from a bound <see cref="T:System.Net.Sockets.Socket" /> into a receive buffer, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the storage location for the received data.</param>
      <param name="size">The number of bytes to receive.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="size" /> exceeds the size of <paramref name="buffer" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller in the call stack does not have the required permissions.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Receive(System.Byte[],System.Net.Sockets.SocketFlags)">
      <summary>Receives data from a bound <see cref="T:System.Net.Sockets.Socket" /> into a receive buffer, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the storage location for the received data.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller in the call stack does not have the required permissions.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Receive(System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
      <summary>Receives data from a bound <see cref="T:System.Net.Sockets.Socket" /> into the list of receive buffers.</summary>
      <param name="buffers">A list of <see cref="T:System.ArraySegment`1" />s of type <see cref="T:System.Byte" /> that contains the received data.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred while attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Receive(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.Net.Sockets.SocketFlags)">
      <summary>Receives data from a bound <see cref="T:System.Net.Sockets.Socket" /> into the list of receive buffers, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />.</summary>
      <param name="buffers">A list of <see cref="T:System.ArraySegment`1" />s of type <see cref="T:System.Byte" /> that contains the received data.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffers" /> is <see langword="null" />.
-or-
<paramref name="buffers" />.Count is zero.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred while attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Receive(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.Net.Sockets.SocketFlags,System.Net.Sockets.SocketError@)">
      <summary>Receives data from a bound <see cref="T:System.Net.Sockets.Socket" /> into the list of receive buffers, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />.</summary>
      <param name="buffers">A list of <see cref="T:System.ArraySegment`1" />s of type <see cref="T:System.Byte" /> that contains the received data.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="errorCode">A <see cref="T:System.Net.Sockets.SocketError" /> object that stores the socket error.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffers" /> is <see langword="null" />.
-or-
<paramref name="buffers" />.Count is zero.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred while attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Receive(System.Span{System.Byte})">
      <param name="buffer" />
    </member>
    <member name="M:System.Net.Sockets.Socket.Receive(System.Span{System.Byte},System.Net.Sockets.SocketFlags)">
      <param name="buffer" />
      <param name="socketFlags" />
    </member>
    <member name="M:System.Net.Sockets.Socket.Receive(System.Span{System.Byte},System.Net.Sockets.SocketFlags,System.Net.Sockets.SocketError@)">
      <param name="buffer" />
      <param name="socketFlags" />
      <param name="errorCode" />
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Begins an asynchronous request to receive data from a connected <see cref="T:System.Net.Sockets.Socket" /> object.</summary>
      <param name="e">The <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object to use for this asynchronous socket operation.</param>
      <returns>
        <see langword="true" /> if the I/O operation is pending. The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will be raised upon completion of the operation.
<see langword="false" /> if the I/O operation completed synchronously. In this case, The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will not be raised and the <paramref name="e" /> object passed as a parameter may be examined immediately after the method call returns to retrieve the result of the operation.</returns>
      <exception cref="T:System.ArgumentException">An argument was invalid. The <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> or <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> properties on the <paramref name="e" /> parameter must reference valid buffers. One or the other of these properties may be set, but not both at the same time.</exception>
      <exception cref="T:System.InvalidOperationException">A socket operation was already in progress using the <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object specified in the <paramref name="e" /> parameter.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP or later is required for this method.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.ReceiveBufferSize">
      <summary>Gets or sets a value that specifies the size of the receive buffer of the <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>An <see cref="T:System.Int32" /> that contains the size, in bytes, of the receive buffer. The default is 8192.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified for a set operation is less than 0.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveFrom(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.Net.EndPoint@)">
      <summary>Receives the specified number of bytes of data into the specified location of the data buffer, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />, and stores the endpoint.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the storage location for received data.</param>
      <param name="offset">The position in the <paramref name="buffer" /> parameter to store the received data.</param>
      <param name="size">The number of bytes to receive.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="remoteEP">An <see cref="T:System.Net.EndPoint" />, passed by reference, that represents the remote server.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.
-or-
<paramref name="remoteEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is greater than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of the <paramref name="buffer" /> minus the value of the offset parameter.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">
        <paramref name="socketFlags" /> is not a valid combination of values.
-or-
The <see cref="P:System.Net.Sockets.Socket.LocalEndPoint" /> property was not set.
-or-
An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveFrom(System.Byte[],System.Int32,System.Net.Sockets.SocketFlags,System.Net.EndPoint@)">
      <summary>Receives the specified number of bytes into the data buffer, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />, and stores the endpoint.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the storage location for received data.</param>
      <param name="size">The number of bytes to receive.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="remoteEP">An <see cref="T:System.Net.EndPoint" />, passed by reference, that represents the remote server.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.
-or-
<paramref name="remoteEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of <paramref name="buffer" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">
        <paramref name="socketFlags" /> is not a valid combination of values.
-or-
The <see cref="P:System.Net.Sockets.Socket.LocalEndPoint" /> property was not set.
-or-
An operating system error occurs while accessing the <see cref="T:System.Net.Sockets.Socket" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller in the call stack does not have the required permissions.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveFrom(System.Byte[],System.Net.EndPoint@)">
      <summary>Receives a datagram into the data buffer and stores the endpoint.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the storage location for received data.</param>
      <param name="remoteEP">An <see cref="T:System.Net.EndPoint" />, passed by reference, that represents the remote server.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.
-or-
<paramref name="remoteEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller in the call stack does not have the required permissions.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveFrom(System.Byte[],System.Net.Sockets.SocketFlags,System.Net.EndPoint@)">
      <summary>Receives a datagram into the data buffer, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />, and stores the endpoint.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the storage location for the received data.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="remoteEP">An <see cref="T:System.Net.EndPoint" />, passed by reference, that represents the remote server.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.
-or-
<paramref name="remoteEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller in the call stack does not have the required permissions.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveFromAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Begins to asynchronously receive data from a specified network device.</summary>
      <param name="e">The <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object to use for this asynchronous socket operation.</param>
      <returns>
        <see langword="true" /> if the I/O operation is pending. The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will be raised upon completion of the operation.
<see langword="false" /> if the I/O operation completed synchronously. In this case, The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will not be raised and the <paramref name="e" /> object passed as a parameter may be examined immediately after the method call returns to retrieve the result of the operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> cannot be null.</exception>
      <exception cref="T:System.InvalidOperationException">A socket operation was already in progress using the <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object specified in the <paramref name="e" /> parameter.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP or later is required for this method.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveMessageFrom(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags@,System.Net.EndPoint@,System.Net.Sockets.IPPacketInformation@)">
      <summary>Receives the specified number of bytes of data into the specified location of the data buffer, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />, and stores the endpoint and packet information.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that is the storage location for received data.</param>
      <param name="offset">The position in the <paramref name="buffer" /> parameter to store the received data.</param>
      <param name="size">The number of bytes to receive.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="remoteEP">An <see cref="T:System.Net.EndPoint" />, passed by reference, that represents the remote server.</param>
      <param name="ipPacketInformation">An <see cref="T:System.Net.Sockets.IPPacketInformation" /> holding address and interface information.</param>
      <returns>The number of bytes received.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.
- or-
<paramref name="remoteEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is greater than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of the <paramref name="buffer" /> minus the value of the offset parameter.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">
        <paramref name="socketFlags" /> is not a valid combination of values.
-or-
The <see cref="P:System.Net.Sockets.Socket.LocalEndPoint" /> property was not set.
-or-
The .NET Framework is running on an AMD 64-bit processor.
-or-
An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.NotSupportedException">The operating system is Windows 2000 or earlier, and this method requires Windows XP.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveMessageFromAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Begins to asynchronously receive the specified number of bytes of data into the specified location in the data buffer, using the specified <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.SocketFlags" />, and stores the endpoint and packet information.</summary>
      <param name="e">The <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object to use for this asynchronous socket operation.</param>
      <returns>
        <see langword="true" /> if the I/O operation is pending. The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will be raised upon completion of the operation.
<see langword="false" /> if the I/O operation completed synchronously. In this case, The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will not be raised and the <paramref name="e" /> object passed as a parameter may be examined immediately after the method call returns to retrieve the result of the operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> cannot be null.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP or later is required for this method.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.ReceiveTimeout">
      <summary>Gets or sets a value that specifies the amount of time after which a synchronous <see cref="Overload:System.Net.Sockets.Socket.Receive" /> call will time out.</summary>
      <returns>The time-out value, in milliseconds. The default value is 0, which indicates an infinite time-out period. Specifying -1 also indicates an infinite time-out period.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified for a set operation is less than -1.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.RemoteEndPoint">
      <summary>Gets the remote endpoint.</summary>
      <returns>The <see cref="T:System.Net.EndPoint" /> with which the <see cref="T:System.Net.Sockets.Socket" /> is communicating.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.SafeHandle">
      <summary>Gets a <see cref="T:System.Net.Sockets.SafeSocketHandle" /> that represents the socket handle that the current <see cref="T:System.Net.Sockets.Socket" /> object encapsulates.</summary>
      <returns>A socket handle exposed in a safe manner for the socket that the current <see cref="T:System.Net.Sockets.Socket" /> object encapsulates.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.Select(System.Collections.IList,System.Collections.IList,System.Collections.IList,System.Int32)">
      <summary>Determines the status of one or more sockets.</summary>
      <param name="checkRead">An <see cref="T:System.Collections.IList" /> of <see cref="T:System.Net.Sockets.Socket" /> instances to check for readability.</param>
      <param name="checkWrite">An <see cref="T:System.Collections.IList" /> of <see cref="T:System.Net.Sockets.Socket" /> instances to check for writability.</param>
      <param name="checkError">An <see cref="T:System.Collections.IList" /> of <see cref="T:System.Net.Sockets.Socket" /> instances to check for errors.</param>
      <param name="microSeconds">The time-out value, in microseconds. A -1 value indicates an infinite time-out.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="checkRead" /> parameter is <see langword="null" /> or empty.
-and-
The <paramref name="checkWrite" /> parameter is <see langword="null" /> or empty
-and-
The <paramref name="checkError" /> parameter is <see langword="null" /> or empty.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Send(System.Byte[])">
      <summary>Sends data to a connected <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the data to be sent.</param>
      <returns>The number of bytes sent to the <see cref="T:System.Net.Sockets.Socket" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Send(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags)">
      <summary>Sends the specified number of bytes of data to a connected <see cref="T:System.Net.Sockets.Socket" />, starting at the specified offset, and using the specified <see cref="T:System.Net.Sockets.SocketFlags" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the data to be sent.</param>
      <param name="offset">The position in the data buffer at which to begin sending data.</param>
      <param name="size">The number of bytes to send.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <returns>The number of bytes sent to the <see cref="T:System.Net.Sockets.Socket" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is greater than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">
        <paramref name="socketFlags" /> is not a valid combination of values.
-or-
An operating system error occurs while accessing the <see cref="T:System.Net.Sockets.Socket" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Send(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.Net.Sockets.SocketError@)">
      <summary>Sends the specified number of bytes of data to a connected <see cref="T:System.Net.Sockets.Socket" />, starting at the specified offset, and using the specified <see cref="T:System.Net.Sockets.SocketFlags" /></summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the data to be sent.</param>
      <param name="offset">The position in the data buffer at which to begin sending data.</param>
      <param name="size">The number of bytes to send.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="errorCode">A <see cref="T:System.Net.Sockets.SocketError" /> object that stores the socket error.</param>
      <returns>The number of bytes sent to the <see cref="T:System.Net.Sockets.Socket" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is greater than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">
        <paramref name="socketFlags" /> is not a valid combination of values.
-or-
An operating system error occurs while accessing the <see cref="T:System.Net.Sockets.Socket" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Send(System.Byte[],System.Int32,System.Net.Sockets.SocketFlags)">
      <summary>Sends the specified number of bytes of data to a connected <see cref="T:System.Net.Sockets.Socket" />, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the data to be sent.</param>
      <param name="size">The number of bytes to send.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <returns>The number of bytes sent to the <see cref="T:System.Net.Sockets.Socket" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="size" /> is less than 0 or exceeds the size of the buffer.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">
        <paramref name="socketFlags" /> is not a valid combination of values.
-or-
An operating system error occurs while accessing the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Send(System.Byte[],System.Net.Sockets.SocketFlags)">
      <summary>Sends data to a connected <see cref="T:System.Net.Sockets.Socket" /> using the specified <see cref="T:System.Net.Sockets.SocketFlags" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the data to be sent.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <returns>The number of bytes sent to the <see cref="T:System.Net.Sockets.Socket" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Send(System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
      <summary>Sends the set of buffers in the list to a connected <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="buffers">A list of <see cref="T:System.ArraySegment`1" />s of type <see cref="T:System.Byte" /> that contains the data to be sent.</param>
      <returns>The number of bytes sent to the <see cref="T:System.Net.Sockets.Socket" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffers" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="buffers" /> is empty.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket. See remarks section below.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Send(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.Net.Sockets.SocketFlags)">
      <summary>Sends the set of buffers in the list to a connected <see cref="T:System.Net.Sockets.Socket" />, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />.</summary>
      <param name="buffers">A list of <see cref="T:System.ArraySegment`1" />s of type <see cref="T:System.Byte" /> that contains the data to be sent.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <returns>The number of bytes sent to the <see cref="T:System.Net.Sockets.Socket" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffers" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="buffers" /> is empty.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Send(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.Net.Sockets.SocketFlags,System.Net.Sockets.SocketError@)">
      <summary>Sends the set of buffers in the list to a connected <see cref="T:System.Net.Sockets.Socket" />, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />.</summary>
      <param name="buffers">A list of <see cref="T:System.ArraySegment`1" />s of type <see cref="T:System.Byte" /> that contains the data to be sent.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="errorCode">A <see cref="T:System.Net.Sockets.SocketError" /> object that stores the socket error.</param>
      <returns>The number of bytes sent to the <see cref="T:System.Net.Sockets.Socket" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffers" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="buffers" /> is empty.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Send(System.ReadOnlySpan{System.Byte})">
      <param name="buffer" />
    </member>
    <member name="M:System.Net.Sockets.Socket.Send(System.ReadOnlySpan{System.Byte},System.Net.Sockets.SocketFlags)">
      <param name="buffer" />
      <param name="socketFlags" />
    </member>
    <member name="M:System.Net.Sockets.Socket.Send(System.ReadOnlySpan{System.Byte},System.Net.Sockets.SocketFlags,System.Net.Sockets.SocketError@)">
      <param name="buffer" />
      <param name="socketFlags" />
      <param name="errorCode" />
    </member>
    <member name="M:System.Net.Sockets.Socket.SendAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Sends data asynchronously to a connected <see cref="T:System.Net.Sockets.Socket" /> object.</summary>
      <param name="e">The <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object to use for this asynchronous socket operation.</param>
      <returns>
        <see langword="true" /> if the I/O operation is pending. The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will be raised upon completion of the operation.
<see langword="false" /> if the I/O operation completed synchronously. In this case, The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will not be raised and the <paramref name="e" /> object passed as a parameter may be examined immediately after the method call returns to retrieve the result of the operation.</returns>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> or <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> properties on the <paramref name="e" /> parameter must reference valid buffers. One or the other of these properties may be set, but not both at the same time.</exception>
      <exception cref="T:System.InvalidOperationException">A socket operation was already in progress using the <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object specified in the <paramref name="e" /> parameter.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP or later is required for this method.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">The <see cref="T:System.Net.Sockets.Socket" /> is not yet connected or was not obtained via an <see cref="M:System.Net.Sockets.Socket.Accept" />, <see cref="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)" />,or <see cref="Overload:System.Net.Sockets.Socket.BeginAccept" />, method.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.SendBufferSize">
      <summary>Gets or sets a value that specifies the size of the send buffer of the <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>An <see cref="T:System.Int32" /> that contains the size, in bytes, of the send buffer. The default is 8192.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified for a set operation is less than 0.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendFile(System.String)">
      <summary>Sends the file <paramref name="fileName" /> to a connected <see cref="T:System.Net.Sockets.Socket" /> object with the <see cref="F:System.Net.Sockets.TransmitFileOptions.UseDefaultWorkerThread" /> transmit flag.</summary>
      <param name="fileName">A <see cref="T:System.String" /> that contains the path and name of the file to be sent. This parameter can be <see langword="null" />.</param>
      <exception cref="T:System.NotSupportedException">The socket is not connected to a remote host.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> object has been closed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.Socket" /> object is not in blocking mode and cannot accept this synchronous call.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file <paramref name="fileName" /> was not found.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendFile(System.String,System.Byte[],System.Byte[],System.Net.Sockets.TransmitFileOptions)">
      <summary>Sends the file <paramref name="fileName" /> and buffers of data to a connected <see cref="T:System.Net.Sockets.Socket" /> object using the specified <see cref="T:System.Net.Sockets.TransmitFileOptions" /> value.</summary>
      <param name="fileName">A <see cref="T:System.String" /> that contains the path and name of the file to be sent. This parameter can be <see langword="null" />.</param>
      <param name="preBuffer">A <see cref="T:System.Byte" /> array that contains data to be sent before the file is sent. This parameter can be <see langword="null" />.</param>
      <param name="postBuffer">A <see cref="T:System.Byte" /> array that contains data to be sent after the file is sent. This parameter can be <see langword="null" />.</param>
      <param name="flags">One or more of <see cref="T:System.Net.Sockets.TransmitFileOptions" /> values.</param>
      <exception cref="T:System.NotSupportedException">The operating system is not Windows NT or later.
-or-
The socket is not connected to a remote host.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> object has been closed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.Socket" /> object is not in blocking mode and cannot accept this synchronous call.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file <paramref name="fileName" /> was not found.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendPacketsAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Sends a collection of files or in memory data buffers asynchronously to a connected <see cref="T:System.Net.Sockets.Socket" /> object.</summary>
      <param name="e">The <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object to use for this asynchronous socket operation.</param>
      <returns>
        <see langword="true" /> if the I/O operation is pending. The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will be raised upon completion of the operation.
<see langword="false" /> if the I/O operation completed synchronously. In this case, The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will not be raised and the <paramref name="e" /> object passed as a parameter may be examined immediately after the method call returns to retrieve the result of the operation.</returns>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in the <see cref="P:System.Net.Sockets.SendPacketsElement.FilePath" /> property was not found.</exception>
      <exception cref="T:System.InvalidOperationException">A socket operation was already in progress using the <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object specified in the <paramref name="e" /> parameter.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP or later is required for this method. This exception also occurs if the <see cref="T:System.Net.Sockets.Socket" /> is not connected to a remote host.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">A connectionless <see cref="T:System.Net.Sockets.Socket" /> is being used and the file being sent exceeds the maximum packet size of the underlying transport.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.SendTimeout">
      <summary>Gets or sets a value that specifies the amount of time after which a synchronous <see cref="Overload:System.Net.Sockets.Socket.Send" /> call will time out.</summary>
      <returns>The time-out value, in milliseconds. If you set the property with a value between 1 and 499, the value will be changed to 500. The default value is 0, which indicates an infinite time-out period. Specifying -1 also indicates an infinite time-out period.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified for a set operation is less than -1.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendTo(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.Net.EndPoint)">
      <summary>Sends the specified number of bytes of data to the specified endpoint, starting at the specified location in the buffer, and using the specified <see cref="T:System.Net.Sockets.SocketFlags" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the data to be sent.</param>
      <param name="offset">The position in the data buffer at which to begin sending data.</param>
      <param name="size">The number of bytes to send.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="remoteEP">The <see cref="T:System.Net.EndPoint" /> that represents the destination location for the data.</param>
      <returns>The number of bytes sent.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.
-or-
<paramref name="remoteEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than 0.
-or-
<paramref name="offset" /> is greater than the length of <paramref name="buffer" />.
-or-
<paramref name="size" /> is less than 0.
-or-
<paramref name="size" /> is greater than the length of <paramref name="buffer" /> minus the value of the <paramref name="offset" /> parameter.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">
        <paramref name="socketFlags" /> is not a valid combination of values.
-or-
An operating system error occurs while accessing the <see cref="T:System.Net.Sockets.Socket" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller in the call stack does not have the required permissions.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendTo(System.Byte[],System.Int32,System.Net.Sockets.SocketFlags,System.Net.EndPoint)">
      <summary>Sends the specified number of bytes of data to the specified endpoint using the specified <see cref="T:System.Net.Sockets.SocketFlags" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the data to be sent.</param>
      <param name="size">The number of bytes to send.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="remoteEP">The <see cref="T:System.Net.EndPoint" /> that represents the destination location for the data.</param>
      <returns>The number of bytes sent.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.
-or-
<paramref name="remoteEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The specified <paramref name="size" /> exceeds the size of <paramref name="buffer" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendTo(System.Byte[],System.Net.EndPoint)">
      <summary>Sends data to the specified endpoint.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the data to be sent.</param>
      <param name="remoteEP">The <see cref="T:System.Net.EndPoint" /> that represents the destination for the data.</param>
      <returns>The number of bytes sent.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.
-or-
<paramref name="remoteEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendTo(System.Byte[],System.Net.Sockets.SocketFlags,System.Net.EndPoint)">
      <summary>Sends data to a specific endpoint using the specified <see cref="T:System.Net.Sockets.SocketFlags" />.</summary>
      <param name="buffer">An array of type <see cref="T:System.Byte" /> that contains the data to be sent.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="remoteEP">The <see cref="T:System.Net.EndPoint" /> that represents the destination location for the data.</param>
      <returns>The number of bytes sent.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.
-or-
<paramref name="remoteEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendToAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Sends data asynchronously to a specific remote host.</summary>
      <param name="e">The <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object to use for this asynchronous socket operation.</param>
      <returns>
        <see langword="true" /> if the I/O operation is pending. The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will be raised upon completion of the operation.
<see langword="false" /> if the I/O operation completed synchronously. In this case, The <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> event on the <paramref name="e" /> parameter will not be raised and the <paramref name="e" /> object passed as a parameter may be examined immediately after the method call returns to retrieve the result of the operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> cannot be null.</exception>
      <exception cref="T:System.InvalidOperationException">A socket operation was already in progress using the <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> object specified in the <paramref name="e" /> parameter.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP or later is required for this method.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">The protocol specified is connection-oriented, but the <see cref="T:System.Net.Sockets.Socket" /> is not yet connected.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.SetIPProtectionLevel(System.Net.Sockets.IPProtectionLevel)">
      <summary>Set the IP protection level on a socket.</summary>
      <param name="level">The IP protection level to set on this socket.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="level" /> parameter cannot be <see cref="F:System.Net.Sockets.IPProtectionLevel.Unspecified" />. The IP protection level cannot be set to unspecified.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Net.Sockets.AddressFamily" /> of the socket must be either <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" /> or <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.SetSocketOption(System.Net.Sockets.SocketOptionLevel,System.Net.Sockets.SocketOptionName,System.Boolean)">
      <summary>Sets the specified <see cref="T:System.Net.Sockets.Socket" /> option to the specified <see cref="T:System.Boolean" /> value.</summary>
      <param name="optionLevel">One of the <see cref="T:System.Net.Sockets.SocketOptionLevel" /> values.</param>
      <param name="optionName">One of the <see cref="T:System.Net.Sockets.SocketOptionName" /> values.</param>
      <param name="optionValue">The value of the option, represented as a <see cref="T:System.Boolean" />.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> object has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.SetSocketOption(System.Net.Sockets.SocketOptionLevel,System.Net.Sockets.SocketOptionName,System.Byte[])">
      <summary>Sets the specified <see cref="T:System.Net.Sockets.Socket" /> option to the specified value, represented as a byte array.</summary>
      <param name="optionLevel">One of the <see cref="T:System.Net.Sockets.SocketOptionLevel" /> values.</param>
      <param name="optionName">One of the <see cref="T:System.Net.Sockets.SocketOptionName" /> values.</param>
      <param name="optionValue">An array of type <see cref="T:System.Byte" /> that represents the value of the option.</param>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.SetSocketOption(System.Net.Sockets.SocketOptionLevel,System.Net.Sockets.SocketOptionName,System.Int32)">
      <summary>Sets the specified <see cref="T:System.Net.Sockets.Socket" /> option to the specified integer value.</summary>
      <param name="optionLevel">One of the <see cref="T:System.Net.Sockets.SocketOptionLevel" /> values.</param>
      <param name="optionName">One of the <see cref="T:System.Net.Sockets.SocketOptionName" /> values.</param>
      <param name="optionValue">A value of the option.</param>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.SetSocketOption(System.Net.Sockets.SocketOptionLevel,System.Net.Sockets.SocketOptionName,System.Object)">
      <summary>Sets the specified <see cref="T:System.Net.Sockets.Socket" /> option to the specified value, represented as an object.</summary>
      <param name="optionLevel">One of the <see cref="T:System.Net.Sockets.SocketOptionLevel" /> values.</param>
      <param name="optionName">One of the <see cref="T:System.Net.Sockets.SocketOptionName" /> values.</param>
      <param name="optionValue">A <see cref="T:System.Net.Sockets.LingerOption" /> or <see cref="T:System.Net.Sockets.MulticastOption" /> that contains the value of the option.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="optionValue" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Shutdown(System.Net.Sockets.SocketShutdown)">
      <summary>Disables sends and receives on a <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="how">One of the <see cref="T:System.Net.Sockets.SocketShutdown" /> values that specifies the operation that will no longer be allowed.</param>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.SocketType">
      <summary>Gets the type of the <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>One of the <see cref="T:System.Net.Sockets.SocketType" /> values.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.SupportsIPv4">
      <summary>Gets a value indicating whether IPv4 support is available and enabled on the current host.</summary>
      <returns>
        <see langword="true" /> if the current host supports the IPv4 protocol; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.SupportsIPv6">
      <summary>Gets a value that indicates whether the Framework supports IPv6 for certain obsolete <see cref="T:System.Net.Dns" /> members.</summary>
      <returns>
        <see langword="true" /> if the Framework supports IPv6 for certain obsolete <see cref="T:System.Net.Dns" /> methods; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.Ttl">
      <summary>Gets or sets a value that specifies the Time To Live (TTL) value of Internet Protocol (IP) packets sent by the <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>The TTL value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The TTL value can't be set to a negative number.</exception>
      <exception cref="T:System.NotSupportedException">This property can be set only for sockets in the <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> or <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" /> families.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket. This error is also returned when an attempt was made to set TTL to a value higher than 255.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.UseOnlyOverlappedIO">
      <summary>Specifies whether the socket should only use Overlapped I/O mode.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.Socket" /> uses only overlapped I/O; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The socket has been bound to a completion port.</exception>
    </member>
    <member name="T:System.Net.Sockets.SocketAsyncEventArgs">
      <summary>Represents an asynchronous socket operation.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.#ctor">
      <summary>Creates an empty <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> instance.</summary>
      <exception cref="T:System.NotSupportedException">The platform is not supported.</exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.AcceptSocket">
      <summary>Gets or sets the socket to use or the socket created for accepting a connection with an asynchronous socket method.</summary>
      <returns>The <see cref="T:System.Net.Sockets.Socket" /> to use or the socket created for accepting a connection with an asynchronous socket method.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer">
      <summary>Gets the data buffer to use with an asynchronous socket method.</summary>
      <returns>A <see cref="T:System.Byte" /> array that represents the data buffer to use with an asynchronous socket method.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList">
      <summary>Gets or sets an array of data buffers to use with an asynchronous socket method.</summary>
      <returns>An <see cref="T:System.Collections.IList" /> that represents an array of data buffers to use with an asynchronous socket method.</returns>
      <exception cref="T:System.ArgumentException">There are ambiguous buffers specified on a set operation. This exception occurs if the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> property has been set to a non-null value and an attempt was made to set the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> property to a non-null value.</exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.BytesTransferred">
      <summary>Gets the number of bytes transferred in the socket operation.</summary>
      <returns>An <see cref="T:System.Int32" /> that contains the number of bytes transferred in the socket operation.</returns>
    </member>
    <member name="E:System.Net.Sockets.SocketAsyncEventArgs.Completed">
      <summary>The event used to complete an asynchronous operation.</summary>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.ConnectByNameError">
      <summary>Gets the exception in the case of a connection failure when a <see cref="T:System.Net.DnsEndPoint" /> was used.</summary>
      <returns>An <see cref="T:System.Exception" /> that indicates the cause of the connection error when a <see cref="T:System.Net.DnsEndPoint" /> was specified for the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> property.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.ConnectSocket">
      <summary>The created and connected <see cref="T:System.Net.Sockets.Socket" /> object after successful completion of the <see cref="Overload:System.Net.Sockets.Socket.ConnectAsync" /> method.</summary>
      <returns>The connected <see cref="T:System.Net.Sockets.Socket" /> object.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Count">
      <summary>Gets the maximum amount of data, in bytes, to send or receive in an asynchronous operation.</summary>
      <returns>An <see cref="T:System.Int32" /> that contains the maximum amount of data, in bytes, to send or receive.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.DisconnectReuseSocket">
      <summary>Gets or sets a value that specifies if socket can be reused after a disconnect operation.</summary>
      <returns>A <see cref="T:System.Boolean" /> that specifies if socket can be reused after a disconnect operation.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.Dispose">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> instance and optionally disposes of the managed resources.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.Finalize">
      <summary>Frees resources used by the <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> class.</summary>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.LastOperation">
      <summary>Gets the type of socket operation most recently performed with this context object.</summary>
      <returns>A <see cref="T:System.Net.Sockets.SocketAsyncOperation" /> instance that indicates the type of socket operation most recently performed with this context object.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.MemoryBuffer" />
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Offset">
      <summary>Gets the offset, in bytes, into the data buffer referenced by the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> property.</summary>
      <returns>An <see cref="T:System.Int32" /> that contains the offset, in bytes, into the data buffer referenced by the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> property.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.OnCompleted(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Represents a method that is called when an asynchronous operation completes.</summary>
      <param name="e">The event that is signaled.</param>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.ReceiveMessageFromPacketInfo">
      <summary>Gets the IP address and interface of a received packet.</summary>
      <returns>An <see cref="T:System.Net.Sockets.IPPacketInformation" /> instance that contains the destination IP address and interface of a received packet.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint">
      <summary>Gets or sets the remote IP endpoint for an asynchronous operation.</summary>
      <returns>An <see cref="T:System.Net.EndPoint" /> that represents the remote IP endpoint for an asynchronous operation.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.SendPacketsElements">
      <summary>Gets or sets an array of buffers to be sent for an asynchronous operation used by the <see cref="M:System.Net.Sockets.Socket.SendPacketsAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> method.</summary>
      <returns>An array of <see cref="T:System.Net.Sockets.SendPacketsElement" /> objects that represent an array of buffers to be sent.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.SendPacketsFlags">
      <summary>Gets or sets a bitwise combination of <see cref="T:System.Net.Sockets.TransmitFileOptions" /> values for an asynchronous operation used by the <see cref="M:System.Net.Sockets.Socket.SendPacketsAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> method.</summary>
      <returns>A <see cref="T:System.Net.Sockets.TransmitFileOptions" /> that contains a bitwise combination of values that are used with an asynchronous operation.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.SendPacketsSendSize">
      <summary>Gets or sets the size, in bytes, of the data block used in the send operation.</summary>
      <returns>An <see cref="T:System.Int32" /> that contains the size, in bytes, of the data block used in the send operation.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.SetBuffer(System.Byte[],System.Int32,System.Int32)">
      <summary>Sets the data buffer to use with an asynchronous socket method.</summary>
      <param name="buffer">The data buffer to use with an asynchronous socket method.</param>
      <param name="offset">The offset, in bytes, in the data buffer where the operation starts.</param>
      <param name="count">The maximum amount of data, in bytes, to send or receive in the buffer.</param>
      <exception cref="T:System.ArgumentException">There are ambiguous buffers specified. This exception occurs if the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> property is also not null and the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> property is also not null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An argument was out of range. This exception occurs if the <paramref name="offset" /> parameter is less than zero or greater than the length of the array in the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> property. This exception also occurs if the <paramref name="count" /> parameter is less than zero or greater than the length of the array in the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> property minus the <paramref name="offset" /> parameter.</exception>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.SetBuffer(System.Int32,System.Int32)">
      <summary>Sets the data buffer to use with an asynchronous socket method.</summary>
      <param name="offset">The offset, in bytes, in the data buffer where the operation starts.</param>
      <param name="count">The maximum amount of data, in bytes, to send or receive in the buffer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">An argument was out of range. This exception occurs if the <paramref name="offset" /> parameter is less than zero or greater than the length of the array in the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> property. This exception also occurs if the <paramref name="count" /> parameter is less than zero or greater than the length of the array in the <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> property minus the <paramref name="offset" /> parameter.</exception>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.SetBuffer(System.Memory{System.Byte})">
      <param name="buffer" />
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.SocketError">
      <summary>Gets or sets the result of the asynchronous socket operation.</summary>
      <returns>A <see cref="T:System.Net.Sockets.SocketError" /> that represents the result of the asynchronous socket operation.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.SocketFlags">
      <summary>Gets the results of an asynchronous socket operation or sets the behavior of an asynchronous operation.</summary>
      <returns>A <see cref="T:System.Net.Sockets.SocketFlags" /> that represents the results of an asynchronous socket operation.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.UserToken">
      <summary>Gets or sets a user or application object associated with this asynchronous socket operation.</summary>
      <returns>An object that represents the user or application object associated with this asynchronous socket operation.</returns>
    </member>
    <member name="T:System.Net.Sockets.SocketAsyncOperation">
      <summary>The type of asynchronous socket operation most recently performed with this context object.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Accept">
      <summary>A socket Accept operation.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Connect">
      <summary>A socket Connect operation.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Disconnect">
      <summary>A socket Disconnect operation.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.None">
      <summary>None of the socket operations.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Receive">
      <summary>A socket Receive operation.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.ReceiveFrom">
      <summary>A socket ReceiveFrom operation.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.ReceiveMessageFrom">
      <summary>A socket ReceiveMessageFrom operation.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Send">
      <summary>A socket Send operation.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.SendPackets">
      <summary>A socket SendPackets operation.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.SendTo">
      <summary>A socket SendTo operation.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketFlags">
      <summary>Specifies socket send and receive behaviors.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketFlags.Broadcast">
      <summary>Indicates a broadcast packet.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketFlags.ControlDataTruncated">
      <summary>Indicates that the control data did not fit into an internal 64-KB buffer and was truncated.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketFlags.DontRoute">
      <summary>Send without using routing tables.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketFlags.Multicast">
      <summary>Indicates a multicast packet.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketFlags.None">
      <summary>Use no flags for this call.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketFlags.OutOfBand">
      <summary>Process out-of-band data.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketFlags.Partial">
      <summary>Partial send or receive for message.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketFlags.Peek">
      <summary>Peek at the incoming message.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketFlags.Truncated">
      <summary>The message was too large to fit into the specified buffer and was truncated.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketInformation">
      <summary>Encapsulates the information that is necessary to duplicate a <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="P:System.Net.Sockets.SocketInformation.Options">
      <summary>Gets or sets the options for a <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>A <see cref="T:System.Net.Sockets.SocketInformationOptions" /> instance.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketInformation.ProtocolInformation">
      <summary>Gets or sets the protocol information for a <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>An array of type <see cref="T:System.Byte" />.</returns>
    </member>
    <member name="T:System.Net.Sockets.SocketInformationOptions">
      <summary>Describes states for a <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketInformationOptions.Connected">
      <summary>The <see cref="T:System.Net.Sockets.Socket" /> is connected.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketInformationOptions.Listening">
      <summary>The <see cref="T:System.Net.Sockets.Socket" /> is listening for new connections.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketInformationOptions.NonBlocking">
      <summary>The <see cref="T:System.Net.Sockets.Socket" /> is nonblocking.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketInformationOptions.UseOnlyOverlappedIO">
      <summary>The <see cref="T:System.Net.Sockets.Socket" /> uses overlapped I/O.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketOptionLevel">
      <summary>Defines socket option levels for the <see cref="M:System.Net.Sockets.Socket.SetSocketOption(System.Net.Sockets.SocketOptionLevel,System.Net.Sockets.SocketOptionName,System.Int32)" /> and <see cref="M:System.Net.Sockets.Socket.GetSocketOption(System.Net.Sockets.SocketOptionLevel,System.Net.Sockets.SocketOptionName)" /> methods.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionLevel.IP">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" /> options apply only to IP sockets.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionLevel.IPv6">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" /> options apply only to IPv6 sockets.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionLevel.Socket">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" /> options apply to all sockets.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionLevel.Tcp">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" /> options apply only to TCP sockets.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionLevel.Udp">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" /> options apply only to UDP sockets.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketOptionName">
      <summary>Defines configuration option names.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.AcceptConnection">
      <summary>The socket is listening.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.AddMembership">
      <summary>Add an IP group membership.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.AddSourceMembership">
      <summary>Join a source group.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.BlockSource">
      <summary>Block data from a source.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.Broadcast">
      <summary>Permit sending broadcast messages on the socket.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.BsdUrgent">
      <summary>Use urgent data as defined in RFC-1222. This option can be set only once; after it is set, it cannot be turned off.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.ChecksumCoverage">
      <summary>Set or get the UDP checksum coverage.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.Debug">
      <summary>Record debugging information.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.DontFragment">
      <summary>Do not fragment IP datagrams.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.DontLinger">
      <summary>Close the socket gracefully without lingering.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.DontRoute">
      <summary>Do not route; send the packet directly to the interface addresses.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.DropMembership">
      <summary>Drop an IP group membership.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.DropSourceMembership">
      <summary>Drop a source group.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.Error">
      <summary>Gets the error status and clear.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.ExclusiveAddressUse">
      <summary>Enables a socket to be bound for exclusive access.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.Expedited">
      <summary>Use expedited data as defined in RFC-1222. This option can be set only once; after it is set, it cannot be turned off.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.HeaderIncluded">
      <summary>Indicates that the application provides the IP header for outgoing datagrams.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.HopLimit">
      <summary>Specifies the maximum number of router hops for an Internet Protocol version 6 (IPv6) packet. This is similar to Time to Live (TTL) for Internet Protocol version 4.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.IPOptions">
      <summary>Specifies the IP options to be inserted into outgoing datagrams.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.IPProtectionLevel">
      <summary>Enables restriction of a IPv6 socket to a specified scope, such as addresses with the same link local or site local prefix.This socket option enables applications to place access restrictions on IPv6 sockets. Such restrictions enable an application running on a private LAN to simply and robustly harden itself against external attacks. This socket option widens or narrows the scope of a listening socket, enabling unrestricted access from public and private users when appropriate, or restricting access only to the same site, as required. This socket option has defined protection levels specified in the <see cref="T:System.Net.Sockets.IPProtectionLevel" /> enumeration.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.IpTimeToLive">
      <summary>Set the IP header Time-to-Live field.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.IPv6Only">
      <summary>Indicates if a socket created for the AF_INET6 address family is restricted to IPv6 communications only. Sockets created for the AF_INET6 address family may be used for both IPv6 and IPv4 communications. Some applications may want to restrict their use of a socket created for the AF_INET6 address family to IPv6 communications only. When this value is non-zero (the default on Windows), a socket created for the AF_INET6 address family can be used to send and receive IPv6 packets only. When this value is zero, a socket created for the AF_INET6 address family can be used to send and receive packets to and from an IPv6 address or an IPv4 address. Note that the ability to interact with an IPv4 address requires the use of IPv4 mapped addresses. This socket option is supported on Windows Vista or later.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.KeepAlive">
      <summary>Use keep-alives.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.Linger">
      <summary>Linger on close if unsent data is present.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.MaxConnections">
      <summary>Not supported; will throw a <see cref="T:System.Net.Sockets.SocketException" /> if used.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.MulticastInterface">
      <summary>Set the interface for outgoing multicast packets.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.MulticastLoopback">
      <summary>An IP multicast loopback.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.MulticastTimeToLive">
      <summary>An IP multicast Time to Live.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.NoChecksum">
      <summary>Send UDP datagrams with checksum set to zero.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.NoDelay">
      <summary>Disables the Nagle algorithm for send coalescing.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.OutOfBandInline">
      <summary>Receives out-of-band data in the normal data stream.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.PacketInformation">
      <summary>Return information about received packets.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.ReceiveBuffer">
      <summary>Specifies the total per-socket buffer space reserved for receives. This is unrelated to the maximum message size or the size of a TCP window.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.ReceiveLowWater">
      <summary>Specifies the low water mark for <see cref="Overload:System.Net.Sockets.Socket.Receive" /> operations.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.ReceiveTimeout">
      <summary>Receive a time-out. This option applies only to synchronous methods; it has no effect on asynchronous methods such as the <see cref="M:System.Net.Sockets.Socket.BeginSend(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.AsyncCallback,System.Object)" /> method.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.ReuseAddress">
      <summary>Allows the socket to be bound to an address that is already in use.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.ReuseUnicastPort">
      <summary>Indicates that the system should defer ephemeral port allocation for outbound connections. This is equivalent to using the Winsock2 SO_REUSE_UNICASTPORT socket option.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.SendBuffer">
      <summary>Specifies the total per-socket buffer space reserved for sends. This is unrelated to the maximum message size or the size of a TCP window.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.SendLowWater">
      <summary>Specifies the low water mark for <see cref="Overload:System.Net.Sockets.Socket.Send" /> operations.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.SendTimeout">
      <summary>Send a time-out. This option applies only to synchronous methods; it has no effect on asynchronous methods such as the <see cref="M:System.Net.Sockets.Socket.BeginSend(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.AsyncCallback,System.Object)" /> method.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.TcpKeepAliveInterval">
      <summary>The number of seconds a TCP connection will wait for a keepalive response before sending another keepalive probe.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.TcpKeepAliveRetryCount">
      <summary>The number of TCP keep alive probes that will be sent before the connection is terminated.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.TcpKeepAliveTime">
      <summary>The number of seconds a TCP connection will remain alive/idle before keepalive probes are sent to the remote.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.Type">
      <summary>Gets the socket type.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.TypeOfService">
      <summary>Change the IP header type of the service field.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.UnblockSource">
      <summary>Unblock a previously blocked source.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.UpdateAcceptContext">
      <summary>Updates an accepted socket's properties by using those of an existing socket. This is equivalent to using the Winsock2 SO_UPDATE_ACCEPT_CONTEXT socket option and is supported only on connection-oriented sockets.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.UpdateConnectContext">
      <summary>Updates a connected socket's properties by using those of an existing socket. This is equivalent to using the Winsock2 SO_UPDATE_CONNECT_CONTEXT socket option and is supported only on connection-oriented sockets.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketOptionName.UseLoopback">
      <summary>Bypass hardware when possible.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketReceiveFromResult">
      <summary>The result of a <see cref="M:System.Net.Sockets.SocketTaskExtensions.ReceiveFromAsync(System.Net.Sockets.Socket,System.ArraySegment{System.Byte},System.Net.Sockets.SocketFlags,System.Net.EndPoint)" /> operation.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketReceiveFromResult.ReceivedBytes">
      <summary>The number of bytes received. If the <see cref="M:System.Net.Sockets.SocketTaskExtensions.ReceiveFromAsync(System.Net.Sockets.Socket,System.ArraySegment{System.Byte},System.Net.Sockets.SocketFlags,System.Net.EndPoint)" /> operation was unsuccessful, then 0.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketReceiveFromResult.RemoteEndPoint">
      <summary>The source <see cref="T:System.Net.EndPoint" />.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketReceiveMessageFromResult">
      <summary>The result of a <see cref="M:System.Net.Sockets.SocketTaskExtensions.ReceiveMessageFromAsync(System.Net.Sockets.Socket,System.ArraySegment{System.Byte},System.Net.Sockets.SocketFlags,System.Net.EndPoint)" /> operation.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketReceiveMessageFromResult.PacketInformation">
      <summary>An <see cref="T:System.Net.Sockets.IPPacketInformation" /> holding address and interface information.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketReceiveMessageFromResult.ReceivedBytes">
      <summary>The number of bytes received. If the <see cref="M:System.Net.Sockets.SocketTaskExtensions.ReceiveMessageFromAsync(System.Net.Sockets.Socket,System.ArraySegment{System.Byte},System.Net.Sockets.SocketFlags,System.Net.EndPoint)" /> operation is unsuccessful, this value will be 0.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketReceiveMessageFromResult.RemoteEndPoint">
      <summary>The source <see cref="T:System.Net.EndPoint" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketReceiveMessageFromResult.SocketFlags">
      <summary>A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values for the received packet.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketShutdown">
      <summary>Defines constants that are used by the <see cref="M:System.Net.Sockets.Socket.Shutdown(System.Net.Sockets.SocketShutdown)" /> method.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Both">
      <summary>Disables a <see cref="T:System.Net.Sockets.Socket" /> for both sending and receiving. This field is constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Receive">
      <summary>Disables a <see cref="T:System.Net.Sockets.Socket" /> for receiving. This field is constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Send">
      <summary>Disables a <see cref="T:System.Net.Sockets.Socket" /> for sending. This field is constant.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketTaskExtensions">
      <summary>This class contains extension methods to the <see cref="T:System.Net.Sockets.Socket" /> class.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.AcceptAsync(System.Net.Sockets.Socket)">
      <summary>Performs an asynchronous operation on to accept an incoming connection attempt on the socket.</summary>
      <param name="socket">The socket that is listening for connections.</param>
      <returns>An asynchronous task that completes with a <see cref="T:System.Net.Sockets.Socket" /> to handle communication with the remote host.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.AcceptAsync(System.Net.Sockets.Socket,System.Net.Sockets.Socket)">
      <summary>Performs an asynchronous operation on to accept an incoming connection attempt on the socket.</summary>
      <param name="socket">The socket that is listening for incoming connections.</param>
      <param name="acceptSocket">The accepted <see cref="T:System.Net.Sockets.Socket" /> object. This value may be <see langword="null" />.</param>
      <returns>An asynchronous task that completes with a <see cref="T:System.Net.Sockets.Socket" /> to handle communication with the remote host.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.ConnectAsync(System.Net.Sockets.Socket,System.Net.EndPoint)">
      <summary>Establishes a connection to a remote host.</summary>
      <param name="socket">The socket that is used for establishing a connection.</param>
      <param name="remoteEP">An EndPoint that represents the remote device.</param>
      <returns>An asynchronous Task.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.ConnectAsync(System.Net.Sockets.Socket,System.Net.IPAddress,System.Int32)">
      <summary>Establishes a connection to a remote host. The host is specified by an IP address and a port number.</summary>
      <param name="socket">The socket to perform the connect operation on.</param>
      <param name="address">The IP address of the remote host.</param>
      <param name="port">The port number of the remote host.</param>
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.ConnectAsync(System.Net.Sockets.Socket,System.Net.IPAddress[],System.Int32)">
      <summary>Establishes a connection to a remote host. The host is specified by an array of IP addresses and a port number.</summary>
      <param name="socket">The socket that the connect operation is performed on.</param>
      <param name="addresses">The IP addresses of the remote host.</param>
      <param name="port">The port number of the remote host.</param>
      <returns>A task that represents the asynchronous connect operation.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.ConnectAsync(System.Net.Sockets.Socket,System.String,System.Int32)">
      <summary>Establishes a connection to a remote host. The host is specified by a host name and a port number.</summary>
      <param name="socket">The socket to perform the connect operation on.</param>
      <param name="host">The name of the remote host.</param>
      <param name="port">The port number of the remote host.</param>
      <returns>An asynchronous task.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.ReceiveAsync(System.Net.Sockets.Socket,System.ArraySegment{System.Byte},System.Net.Sockets.SocketFlags)">
      <summary>Receives data from a connected socket.</summary>
      <param name="socket">The socket to perform the receive operation on.</param>
      <param name="buffer">An array that is the storage location for the received data.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <returns>A task that represents the asynchronous receive operation. The value of the <paramref name="TResult" /> parameter contains the number of bytes received.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.ReceiveAsync(System.Net.Sockets.Socket,System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.Net.Sockets.SocketFlags)">
      <summary>Receives data from a connected socket.</summary>
      <param name="socket">The socket to perform the receive operation on.</param>
      <param name="buffers">An array that is the storage location for the received data.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <returns>A task that represents the asynchronous receive operation. The value of the <paramref name="TResult" /> parameter contains the number of bytes received.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.ReceiveAsync(System.Net.Sockets.Socket,System.Memory{System.Byte},System.Net.Sockets.SocketFlags,System.Threading.CancellationToken)">
      <param name="socket" />
      <param name="buffer" />
      <param name="socketFlags" />
      <param name="cancellationToken" />
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.ReceiveFromAsync(System.Net.Sockets.Socket,System.ArraySegment{System.Byte},System.Net.Sockets.SocketFlags,System.Net.EndPoint)">
      <summary>Receives data from a specified network device.</summary>
      <param name="socket">The socket to perform the ReceiveFrom operation on.</param>
      <param name="buffer">An array of type Byte that is the storage location for the received data.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="remoteEndPoint">An EndPoint that represents the source of the data.</param>
      <returns>An asynchronous Task that completes with a SocketReceiveFromResult struct.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.ReceiveMessageFromAsync(System.Net.Sockets.Socket,System.ArraySegment{System.Byte},System.Net.Sockets.SocketFlags,System.Net.EndPoint)">
      <summary>Receives the specified number of bytes of data into the specified location of the data buffer, using the specified <see cref="T:System.Net.Sockets.SocketFlags" />, and stores the endpoint and packet information.</summary>
      <param name="socket">The socket to perform the operation on.</param>
      <param name="buffer">An array that is the storage location for received data.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="remoteEndPoint">An <see cref="T:System.Net.EndPoint" />, that represents the remote server.</param>
      <returns>An asynchronous Task that completes with a <see cref="T:System.Net.Sockets.SocketReceiveMessageFromResult" /> struct.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.SendAsync(System.Net.Sockets.Socket,System.ArraySegment{System.Byte},System.Net.Sockets.SocketFlags)">
      <summary>Sends data to a connected socket.</summary>
      <param name="socket">The socket to perform the operation on.</param>
      <param name="buffer">An array of type Byte that contains the data to send.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <returns>An asynchronous task that completes with number of bytes sent to the socket if the operation was successful. Otherwise, the task will complete with an invalid socket error.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.SendAsync(System.Net.Sockets.Socket,System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.Net.Sockets.SocketFlags)">
      <summary>Sends data to a connected socket.</summary>
      <param name="socket">The socket to perform the operation on.</param>
      <param name="buffers">An array that contains the data to send.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <returns>An asynchronous task that completes with number of bytes sent to the socket if the operation was successful. Otherwise, the task will complete with an invalid socket error.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.SendAsync(System.Net.Sockets.Socket,System.ReadOnlyMemory{System.Byte},System.Net.Sockets.SocketFlags,System.Threading.CancellationToken)">
      <param name="socket" />
      <param name="buffer" />
      <param name="socketFlags" />
      <param name="cancellationToken" />
    </member>
    <member name="M:System.Net.Sockets.SocketTaskExtensions.SendToAsync(System.Net.Sockets.Socket,System.ArraySegment{System.Byte},System.Net.Sockets.SocketFlags,System.Net.EndPoint)">
      <summary>Sends data asynchronously to a specific remote host.</summary>
      <param name="socket">The socket to perform the operation on.</param>
      <param name="buffer">An array that contains the data to send.</param>
      <param name="socketFlags">A bitwise combination of the <see cref="T:System.Net.Sockets.SocketFlags" /> values.</param>
      <param name="remoteEP">An <see cref="T:System.Net.EndPoint" /> that represents the remote device.</param>
      <returns>An asynchronous task that completes with number of bytes sent if the operation was successful. Otherwise, the task will complete with an invalid socket error.</returns>
    </member>
    <member name="T:System.Net.Sockets.SocketType">
      <summary>Specifies the type of socket that an instance of the <see cref="T:System.Net.Sockets.Socket" /> class represents.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Dgram">
      <summary>Supports datagrams, which are connectionless, unreliable messages of a fixed (typically small) maximum length. Messages might be lost or duplicated and might arrive out of order. A <see cref="T:System.Net.Sockets.Socket" /> of type <see cref="F:System.Net.Sockets.SocketType.Dgram" /> requires no connection prior to sending and receiving data, and can communicate with multiple peers. <see cref="F:System.Net.Sockets.SocketType.Dgram" /> uses the Datagram Protocol (<see langword="ProtocolType" />.<see cref="F:System.Net.Sockets.ProtocolType.Udp" />) and the <see langword="AddressFamily" />.<see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> address family.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Raw">
      <summary>Supports access to the underlying transport protocol. Using <see cref="F:System.Net.Sockets.SocketType.Raw" />, you can communicate using protocols like Internet Control Message Protocol (<see langword="ProtocolType" />.<see cref="F:System.Net.Sockets.ProtocolType.Icmp" />) and Internet Group Management Protocol (<see langword="ProtocolType" />.<see cref="F:System.Net.Sockets.ProtocolType.Igmp" />). Your application must provide a complete IP header when sending. Received datagrams return with the IP header and options intact.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Rdm">
      <summary>Supports connectionless, message-oriented, reliably delivered messages, and preserves message boundaries in data. Rdm (Reliably Delivered Messages) messages arrive unduplicated and in order. Furthermore, the sender is notified if messages are lost. If you initialize a <see cref="T:System.Net.Sockets.Socket" /> using <see cref="F:System.Net.Sockets.SocketType.Rdm" />, you do not require a remote host connection before sending and receiving data. With <see cref="F:System.Net.Sockets.SocketType.Rdm" />, you can communicate with multiple peers.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Seqpacket">
      <summary>Provides connection-oriented and reliable two-way transfer of ordered byte streams across a network. <see cref="F:System.Net.Sockets.SocketType.Seqpacket" /> does not duplicate data, and it preserves boundaries within the data stream. A <see cref="T:System.Net.Sockets.Socket" /> of type <see cref="F:System.Net.Sockets.SocketType.Seqpacket" /> communicates with a single peer and requires a remote host connection before communication can begin.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Stream">
      <summary>Supports reliable, two-way, connection-based byte streams without the duplication of data and without preservation of boundaries. A <see cref="T:System.Net.Sockets.Socket" /> of this type communicates with a single peer and requires a remote host connection before communication can begin. <see cref="F:System.Net.Sockets.SocketType.Stream" /> uses the Transmission Control Protocol (<see langword="ProtocolType" />.<see cref="F:System.Net.Sockets.ProtocolType.Tcp" />) and the <see langword="AddressFamily" />.<see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> address family.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Unknown">
      <summary>Specifies an unknown <see cref="T:System.Net.Sockets.Socket" /> type.</summary>
    </member>
    <member name="T:System.Net.Sockets.TcpClient">
      <summary>Provides client connections for TCP network services.</summary>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.TcpClient" /> class.</summary>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.#ctor(System.Net.IPEndPoint)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.TcpClient" /> class and binds it to the specified local endpoint.</summary>
      <param name="localEP">The <see cref="T:System.Net.IPEndPoint" /> to which you bind the TCP <see cref="T:System.Net.Sockets.Socket" />.</param>
      <exception cref="T:System.ArgumentNullException">The  <paramref name="localEP" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.#ctor(System.Net.Sockets.AddressFamily)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.TcpClient" /> class with the specified family.</summary>
      <param name="family">The <see cref="P:System.Net.IPAddress.AddressFamily" /> of the IP protocol.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="family" /> parameter is not equal to AddressFamily.InterNetwork
-or-
The <paramref name="family" /> parameter is not equal to AddressFamily.InterNetworkV6</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.#ctor(System.String,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.TcpClient" /> class and connects to the specified port on the specified host.</summary>
      <param name="hostname">The DNS name of the remote host to which you intend to connect.</param>
      <param name="port">The port number of the remote host to which you intend to connect.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="hostname" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="port" /> parameter is not between <see cref="F:System.Net.IPEndPoint.MinPort" /> and <see cref="F:System.Net.IPEndPoint.MaxPort" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="P:System.Net.Sockets.TcpClient.Active">
      <summary>Gets or sets a value that indicates whether a connection has been made.</summary>
      <returns>
        <see langword="true" /> if the connection has been made; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Sockets.TcpClient.Available">
      <summary>Gets the amount of data that has been received from the network and is available to be read.</summary>
      <returns>The number of bytes of data received from the network and available to be read.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.BeginConnect(System.Net.IPAddress,System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous request for a remote host connection. The remote host is specified by an <see cref="T:System.Net.IPAddress" /> and a port number (<see cref="T:System.Int32" />).</summary>
      <param name="address">The <see cref="T:System.Net.IPAddress" /> of the remote host.</param>
      <param name="port">The port number of the remote host.</param>
      <param name="requestCallback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the operation is complete.</param>
      <param name="state">A user-defined object that contains information about the connect operation. This object is passed to the <paramref name="requestCallback" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> object that references the asynchronous connection.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="address" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller higher in the call stack does not have permission for the requested operation.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The port number is not valid.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.BeginConnect(System.Net.IPAddress[],System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous request for a remote host connection. The remote host is specified by an <see cref="T:System.Net.IPAddress" /> array and a port number (<see cref="T:System.Int32" />).</summary>
      <param name="addresses">At least one <see cref="T:System.Net.IPAddress" /> that designates the remote hosts.</param>
      <param name="port">The port number of the remote hosts.</param>
      <param name="requestCallback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the operation is complete.</param>
      <param name="state">A user-defined object that contains information about the connect operation. This object is passed to the <paramref name="requestCallback" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> object that references the asynchronous connection.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="addresses" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller higher in the call stack does not have permission for the requested operation.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The port number is not valid.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.BeginConnect(System.String,System.Int32,System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous request for a remote host connection. The remote host is specified by a host name (<see cref="T:System.String" />) and a port number (<see cref="T:System.Int32" />).</summary>
      <param name="host">The name of the remote host.</param>
      <param name="port">The port number of the remote host.</param>
      <param name="requestCallback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the operation is complete.</param>
      <param name="state">A user-defined object that contains information about the connect operation. This object is passed to the <paramref name="requestCallback" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> object that references the asynchronous connection.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="host" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller higher in the call stack does not have permission for the requested operation.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The port number is not valid.</exception>
    </member>
    <member name="P:System.Net.Sockets.TcpClient.Client">
      <summary>Gets or sets the underlying <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>The underlying network <see cref="T:System.Net.Sockets.Socket" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.Close">
      <summary>Disposes this <see cref="T:System.Net.Sockets.TcpClient" /> instance and requests that the underlying TCP connection be closed.</summary>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.Connect(System.Net.IPAddress,System.Int32)">
      <summary>Connects the client to a remote TCP host using the specified IP address and port number.</summary>
      <param name="address">The <see cref="T:System.Net.IPAddress" /> of the host to which you intend to connect.</param>
      <param name="port">The port number to which you intend to connect.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="address" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="port" /> is not between <see cref="F:System.Net.IPEndPoint.MinPort" /> and <see cref="F:System.Net.IPEndPoint.MaxPort" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.TcpClient" /> is closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.Connect(System.Net.IPAddress[],System.Int32)">
      <summary>Connects the client to a remote TCP host using the specified IP addresses and port number.</summary>
      <param name="ipAddresses">The <see cref="T:System.Net.IPAddress" /> array of the host to which you intend to connect.</param>
      <param name="port">The port number to which you intend to connect.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="ipAddresses" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The port number is not valid.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller higher in the call stack does not have permission for the requested operation.</exception>
      <exception cref="T:System.NotSupportedException">This method is valid for sockets that use the <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> flag or the <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" /> flag.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.Connect(System.Net.IPEndPoint)">
      <summary>Connects the client to a remote TCP host using the specified remote network endpoint.</summary>
      <param name="remoteEP">The <see cref="T:System.Net.IPEndPoint" /> to which you intend to connect.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="remoteEp" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.TcpClient" /> is closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.Connect(System.String,System.Int32)">
      <summary>Connects the client to the specified port on the specified host.</summary>
      <param name="hostname">The DNS name of the remote host to which you intend to connect.</param>
      <param name="port">The port number of the remote host to which you intend to connect.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="hostname" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="port" /> parameter is not between <see cref="F:System.Net.IPEndPoint.MinPort" /> and <see cref="F:System.Net.IPEndPoint.MaxPort" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.TcpClient" /> is closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.ConnectAsync(System.Net.IPAddress,System.Int32)">
      <summary>Connects the client to a remote TCP host using the specified IP address and port number as an asynchronous operation.</summary>
      <param name="address">The <see cref="T:System.Net.IPAddress" /> of the host to which you intend to connect.</param>
      <param name="port">The port number to which you intend to connect.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="address" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="port" /> is not between <see cref="F:System.Net.IPEndPoint.MinPort" /> and <see cref="F:System.Net.IPEndPoint.MaxPort" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.TcpClient" /> is closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.ConnectAsync(System.Net.IPAddress[],System.Int32)">
      <summary>Connects the client to a remote TCP host using the specified IP addresses and port number as an asynchronous operation.</summary>
      <param name="addresses">The <see cref="T:System.Net.IPAddress" /> array of the host to which you intend to connect.</param>
      <param name="port">The port number to which you intend to connect.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="ipAddresses" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The port number is not valid.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">A caller higher in the call stack does not have permission for the requested operation.</exception>
      <exception cref="T:System.NotSupportedException">This method is valid for sockets that use the <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> flag or the <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" /> flag.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.ConnectAsync(System.String,System.Int32)">
      <summary>Connects the client to the specified TCP port on the specified host as an asynchronous operation.</summary>
      <param name="host">The DNS name of the remote host to which you intend to connect.</param>
      <param name="port">The port number of the remote host to which you intend to connect.</param>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="hostname" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="port" /> parameter is not between <see cref="F:System.Net.IPEndPoint.MinPort" /> and <see cref="F:System.Net.IPEndPoint.MaxPort" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.TcpClient" /> is closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.TcpClient.Connected">
      <summary>Gets a value indicating whether the underlying <see cref="T:System.Net.Sockets.Socket" /> for a <see cref="T:System.Net.Sockets.TcpClient" /> is connected to a remote host.</summary>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Net.Sockets.TcpClient.Client" /> socket was connected to a remote resource as of the most recent operation; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.Dispose">
      <summary>Releases the managed and unmanaged resources used by the <see cref="T:System.Net.Sockets.TcpClient" />.</summary>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Sockets.TcpClient" /> and optionally releases the managed resources.</summary>
      <param name="disposing">Set to <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.EndConnect(System.IAsyncResult)">
      <summary>Ends a pending asynchronous connection attempt.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> object returned by a call to <see cref="Overload:System.Net.Sockets.TcpClient.BeginConnect" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="asyncResult" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="asyncResult" /> parameter was not returned by a call to a <see cref="Overload:System.Net.Sockets.TcpClient.BeginConnect" /> method.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Net.Sockets.TcpClient.EndConnect(System.IAsyncResult)" /> method was previously called for the asynchronous connection.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the <see cref="T:System.Net.Sockets.Socket" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.TcpClient.ExclusiveAddressUse">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that specifies whether the <see cref="T:System.Net.Sockets.TcpClient" /> allows only one client to use a port.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.TcpClient" /> allows only one client to use a specific port; otherwise, <see langword="false" />. The default is <see langword="true" /> for Windows Server 2003 and Windows XP Service Pack 2 and later, and <see langword="false" /> for all other versions.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the underlying socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.Finalize">
      <summary>Frees resources used by the <see cref="T:System.Net.Sockets.TcpClient" /> class.</summary>
    </member>
    <member name="M:System.Net.Sockets.TcpClient.GetStream">
      <summary>Returns the <see cref="T:System.Net.Sockets.NetworkStream" /> used to send and receive data.</summary>
      <returns>The underlying <see cref="T:System.Net.Sockets.NetworkStream" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.TcpClient" /> is not connected to a remote host.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.TcpClient" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.TcpClient.LingerState">
      <summary>Gets or sets information about the linger state of the associated socket.</summary>
      <returns>A <see cref="T:System.Net.Sockets.LingerOption" />. By default, lingering is disabled.</returns>
    </member>
    <member name="P:System.Net.Sockets.TcpClient.NoDelay">
      <summary>Gets or sets a value that disables a delay when send or receive buffers are not full.</summary>
      <returns>
        <see langword="true" /> if the delay is disabled; otherwise, <see langword="false" />. The default value is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Sockets.TcpClient.ReceiveBufferSize">
      <summary>Gets or sets the size of the receive buffer.</summary>
      <returns>The size of the receive buffer, in bytes. The default value is 8192 bytes.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when setting the buffer size.
-or-
In .NET Compact Framework applications, you cannot set this property. For a workaround, see the Platform Note in Remarks.</exception>
    </member>
    <member name="P:System.Net.Sockets.TcpClient.ReceiveTimeout">
      <summary>Gets or sets the amount of time a <see cref="T:System.Net.Sockets.TcpClient" /> will wait to receive data once a read operation is initiated.</summary>
      <returns>The time-out value of the connection in milliseconds. The default value is 0.</returns>
    </member>
    <member name="P:System.Net.Sockets.TcpClient.SendBufferSize">
      <summary>Gets or sets the size of the send buffer.</summary>
      <returns>The size of the send buffer, in bytes. The default value is 8192 bytes.</returns>
    </member>
    <member name="P:System.Net.Sockets.TcpClient.SendTimeout">
      <summary>Gets or sets the amount of time a <see cref="T:System.Net.Sockets.TcpClient" /> will wait for a send operation to complete successfully.</summary>
      <returns>The send time-out value, in milliseconds. The default is 0.</returns>
    </member>
    <member name="T:System.Net.Sockets.TcpListener">
      <summary>Listens for connections from TCP network clients.</summary>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.TcpListener" /> class that listens on the specified port.</summary>
      <param name="port">The port on which to listen for incoming connection attempts.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> is not between <see cref="F:System.Net.IPEndPoint.MinPort" /> and <see cref="F:System.Net.IPEndPoint.MaxPort" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.#ctor(System.Net.IPAddress,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.TcpListener" /> class that listens for incoming connection attempts on the specified local IP address and port number.</summary>
      <param name="localaddr">An <see cref="T:System.Net.IPAddress" /> that represents the local IP address.</param>
      <param name="port">The port on which to listen for incoming connection attempts.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="localaddr" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> is not between <see cref="F:System.Net.IPEndPoint.MinPort" /> and <see cref="F:System.Net.IPEndPoint.MaxPort" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.#ctor(System.Net.IPEndPoint)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.TcpListener" /> class with the specified local endpoint.</summary>
      <param name="localEP">An <see cref="T:System.Net.IPEndPoint" /> that represents the local endpoint to which to bind the listener <see cref="T:System.Net.Sockets.Socket" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="localEP" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.AcceptSocket">
      <summary>Accepts a pending connection request.</summary>
      <returns>A <see cref="T:System.Net.Sockets.Socket" /> used to send and receive data.</returns>
      <exception cref="T:System.InvalidOperationException">The listener has not been started with a call to <see cref="M:System.Net.Sockets.TcpListener.Start" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.AcceptSocketAsync">
      <summary>Accepts a pending connection request as an asynchronous operation.</summary>
      <returns>The task object representing the asynchronous operation. The <see cref="P:System.Threading.Tasks.Task`1.Result" /> property on the task object returns a <see cref="T:System.Net.Sockets.Socket" /> used to send and receive data.</returns>
      <exception cref="T:System.InvalidOperationException">The listener has not been started with a call to <see cref="M:System.Net.Sockets.TcpListener.Start" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.AcceptTcpClient">
      <summary>Accepts a pending connection request.</summary>
      <returns>A <see cref="T:System.Net.Sockets.TcpClient" /> used to send and receive data.</returns>
      <exception cref="T:System.InvalidOperationException">The listener has not been started with a call to <see cref="M:System.Net.Sockets.TcpListener.Start" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Use the <see cref="P:System.Net.Sockets.SocketException.ErrorCode" /> property to obtain the specific error code. When you have obtained this code, you can refer to the Windows Sockets version 2 API error code documentation for a detailed description of the error.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.AcceptTcpClientAsync">
      <summary>Accepts a pending connection request as an asynchronous operation.</summary>
      <returns>The task object representing the asynchronous operation. The <see cref="P:System.Threading.Tasks.Task`1.Result" /> property on the task object returns a <see cref="T:System.Net.Sockets.TcpClient" /> used to send and receive data.</returns>
      <exception cref="T:System.InvalidOperationException">The listener has not been started with a call to <see cref="M:System.Net.Sockets.TcpListener.Start" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Use the <see cref="P:System.Net.Sockets.SocketException.ErrorCode" /> property to obtain the specific error code. When you have obtained this code, you can refer to the Windows Sockets version 2 API error code documentation for a detailed description of the error.</exception>
    </member>
    <member name="P:System.Net.Sockets.TcpListener.Active">
      <summary>Gets a value that indicates whether <see cref="T:System.Net.Sockets.TcpListener" /> is actively listening for client connections.</summary>
      <returns>
        <see langword="true" /> if <see cref="T:System.Net.Sockets.TcpListener" /> is actively listening; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.AllowNatTraversal(System.Boolean)">
      <summary>Enables or disables Network Address Translation (NAT) traversal on a <see cref="T:System.Net.Sockets.TcpListener" /> instance.</summary>
      <param name="allowed">A Boolean value that specifies whether to enable or disable NAT traversal.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Net.Sockets.TcpListener.AllowNatTraversal(System.Boolean)" /> method was called after calling the <see cref="M:System.Net.Sockets.TcpListener.Start" /> method</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.BeginAcceptSocket(System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation to accept an incoming connection attempt.</summary>
      <param name="callback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the operation is complete.</param>
      <param name="state">A user-defined object containing information about the accept operation. This object is passed to the <paramref name="callback" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous creation of the <see cref="T:System.Net.Sockets.Socket" />.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred while attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.BeginAcceptTcpClient(System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous operation to accept an incoming connection attempt.</summary>
      <param name="callback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the operation is complete.</param>
      <param name="state">A user-defined object containing information about the accept operation. This object is passed to the <paramref name="callback" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous creation of the <see cref="T:System.Net.Sockets.TcpClient" />.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred while attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.Create(System.Int32)">
      <summary>Creates a new <see cref="T:System.Net.Sockets.TcpListener" /> instance to listen on the specified port.</summary>
      <param name="port">The port on which to listen for incoming connection attempts.</param>
      <returns>A new <see cref="T:System.Net.Sockets.TcpListener" /> instance to listen on the specified port.</returns>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.EndAcceptSocket(System.IAsyncResult)">
      <summary>Asynchronously accepts an incoming connection attempt and creates a new <see cref="T:System.Net.Sockets.Socket" /> to handle remote host communication.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> returned by a call to the <see cref="M:System.Net.Sockets.TcpListener.BeginAcceptSocket(System.AsyncCallback,System.Object)" /> method.</param>
      <returns>A <see cref="T:System.Net.Sockets.Socket" />.
The <see cref="T:System.Net.Sockets.Socket" /> used to send and receive data.</returns>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="asyncResult" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="asyncResult" /> parameter was not created by a call to the <see cref="M:System.Net.Sockets.TcpListener.BeginAcceptSocket(System.AsyncCallback,System.Object)" /> method.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Net.Sockets.TcpListener.EndAcceptSocket(System.IAsyncResult)" /> method was previously called.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred while attempting to access the <see cref="T:System.Net.Sockets.Socket" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.EndAcceptTcpClient(System.IAsyncResult)">
      <summary>Asynchronously accepts an incoming connection attempt and creates a new <see cref="T:System.Net.Sockets.TcpClient" /> to handle remote host communication.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> returned by a call to the <see cref="M:System.Net.Sockets.TcpListener.BeginAcceptTcpClient(System.AsyncCallback,System.Object)" /> method.</param>
      <returns>A <see cref="T:System.Net.Sockets.TcpClient" />.
The <see cref="T:System.Net.Sockets.TcpClient" /> used to send and receive data.</returns>
    </member>
    <member name="P:System.Net.Sockets.TcpListener.ExclusiveAddressUse">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that specifies whether the <see cref="T:System.Net.Sockets.TcpListener" /> allows only one underlying socket to listen to a specific port.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.TcpListener" /> allows only one <see cref="T:System.Net.Sockets.TcpListener" /> to listen to a specific port; otherwise, <see langword="false" />. . The default is <see langword="true" /> for Windows Server 2003 and Windows XP Service Pack 2 and later, and <see langword="false" /> for all other versions.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.TcpListener" /> has been started. Call the <see cref="M:System.Net.Sockets.TcpListener.Stop" /> method and then set the <see cref="P:System.Net.Sockets.Socket.ExclusiveAddressUse" /> property.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the underlying socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.TcpListener.LocalEndpoint">
      <summary>Gets the underlying <see cref="T:System.Net.EndPoint" /> of the current <see cref="T:System.Net.Sockets.TcpListener" />.</summary>
      <returns>The <see cref="T:System.Net.EndPoint" /> to which the <see cref="T:System.Net.Sockets.Socket" /> is bound.</returns>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.Pending">
      <summary>Determines if there are pending connection requests.</summary>
      <returns>
        <see langword="true" /> if connections are pending; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The listener has not been started with a call to <see cref="M:System.Net.Sockets.TcpListener.Start" />.</exception>
    </member>
    <member name="P:System.Net.Sockets.TcpListener.Server">
      <summary>Gets the underlying network <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>The underlying <see cref="T:System.Net.Sockets.Socket" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.Start">
      <summary>Starts listening for incoming connection requests.</summary>
      <exception cref="T:System.Net.Sockets.SocketException">Use the <see cref="P:System.Net.Sockets.SocketException.ErrorCode" /> property to obtain the specific error code. When you have obtained this code, you can refer to the Windows Sockets version 2 API error code documentation for a detailed description of the error.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.Start(System.Int32)">
      <summary>Starts listening for incoming connection requests with a maximum number of pending connection.</summary>
      <param name="backlog">The maximum length of the pending connections queue.</param>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred while accessing the socket.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="backlog" /> parameter is less than zero or exceeds the maximum number of permitted connections.</exception>
      <exception cref="T:System.InvalidOperationException">The underlying <see cref="T:System.Net.Sockets.Socket" /> is null.</exception>
    </member>
    <member name="M:System.Net.Sockets.TcpListener.Stop">
      <summary>Closes the listener.</summary>
      <exception cref="T:System.Net.Sockets.SocketException">Use the <see cref="P:System.Net.Sockets.SocketException.ErrorCode" /> property to obtain the specific error code. When you have obtained this code, you can refer to the Windows Sockets version 2 API error code documentation for a detailed description of the error.</exception>
    </member>
    <member name="T:System.Net.Sockets.TransmitFileOptions">
      <summary>The <see cref="T:System.Net.Sockets.TransmitFileOptions" /> enumeration defines values used in file transfer requests.</summary>
    </member>
    <member name="F:System.Net.Sockets.TransmitFileOptions.Disconnect">
      <summary>Start a transport-level disconnect after all the file data has been queued for transmission. When used with <see cref="F:System.Net.Sockets.TransmitFileOptions.ReuseSocket" />, these flags return the socket to a disconnected, reusable state after the file has been transmitted.</summary>
    </member>
    <member name="F:System.Net.Sockets.TransmitFileOptions.ReuseSocket">
      <summary>The socket handle may be reused when the request completes. This flag is valid only if <see cref="F:System.Net.Sockets.TransmitFileOptions.Disconnect" /> is also specified. When used with <see cref="F:System.Net.Sockets.TransmitFileOptions.Disconnect" />, these flags return the socket to a disconnected, reusable state after the file has been transmitted.</summary>
    </member>
    <member name="F:System.Net.Sockets.TransmitFileOptions.UseDefaultWorkerThread">
      <summary>Use the default thread to process long file transfer requests.</summary>
    </member>
    <member name="F:System.Net.Sockets.TransmitFileOptions.UseKernelApc">
      <summary>Use kernel asynchronous procedure calls (APCs) instead of worker threads to process long file transfer requests. Long requests are defined as requests that require more than a single read from the file or a cache; the request therefore depends on the size of the file and the specified length of the send packet.</summary>
    </member>
    <member name="F:System.Net.Sockets.TransmitFileOptions.UseSystemThread">
      <summary>Use system threads to process long file transfer requests.</summary>
    </member>
    <member name="F:System.Net.Sockets.TransmitFileOptions.WriteBehind">
      <summary>Complete the file transfer request immediately, without pending. If this flag is specified and the file transfer succeeds, the data has been accepted by the system but not necessarily acknowledged by the remote end. Do not use this flag with the <see cref="F:System.Net.Sockets.TransmitFileOptions.Disconnect" /> and <see cref="F:System.Net.Sockets.TransmitFileOptions.ReuseSocket" /> flags.</summary>
    </member>
    <member name="T:System.Net.Sockets.UdpClient">
      <summary>Provides User Datagram Protocol (UDP) network services.</summary>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.UdpClient" /> class.</summary>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.UdpClient" /> class and binds it to the local port number provided.</summary>
      <param name="port">The local port number from which you intend to communicate.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="port" /> parameter is greater than <see cref="F:System.Net.IPEndPoint.MaxPort" /> or less than <see cref="F:System.Net.IPEndPoint.MinPort" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.#ctor(System.Int32,System.Net.Sockets.AddressFamily)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.UdpClient" /> class and binds it to the local port number provided.</summary>
      <param name="port">The port on which to listen for incoming connection attempts.</param>
      <param name="family">One of the <see cref="T:System.Net.Sockets.AddressFamily" /> values that specifies the addressing scheme of the socket.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="family" /> is not <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> or <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> is greater than <see cref="F:System.Net.IPEndPoint.MaxPort" /> or less than <see cref="F:System.Net.IPEndPoint.MinPort" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.#ctor(System.Net.IPEndPoint)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.UdpClient" /> class and binds it to the specified local endpoint.</summary>
      <param name="localEP">An <see cref="T:System.Net.IPEndPoint" /> that represents the local endpoint to which you bind the UDP connection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="localEP" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.#ctor(System.Net.Sockets.AddressFamily)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.UdpClient" /> class.</summary>
      <param name="family">One of the <see cref="T:System.Net.Sockets.AddressFamily" /> values that specifies the addressing scheme of the socket.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="family" /> is not <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> or <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.#ctor(System.String,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.UdpClient" /> class and establishes a default remote host.</summary>
      <param name="hostname">The name of the remote DNS host to which you intend to connect.</param>
      <param name="port">The remote port number to which you intend to connect.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="hostname" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> is not between <see cref="F:System.Net.IPEndPoint.MinPort" /> and <see cref="F:System.Net.IPEndPoint.MaxPort" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="P:System.Net.Sockets.UdpClient.Active">
      <summary>Gets or sets a value indicating whether a default remote host has been established.</summary>
      <returns>
        <see langword="true" /> if a connection is active; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.AllowNatTraversal(System.Boolean)">
      <summary>Enables or disables Network Address Translation (NAT) traversal on a <see cref="T:System.Net.Sockets.UdpClient" /> instance.</summary>
      <param name="allowed">A Boolean value that specifies whether to enable or disable NAT traversal.</param>
    </member>
    <member name="P:System.Net.Sockets.UdpClient.Available">
      <summary>Gets the amount of data received from the network that is available to read.</summary>
      <returns>The number of bytes of data received from the network.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred while attempting to access the socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.BeginReceive(System.AsyncCallback,System.Object)">
      <summary>Receives a datagram from a remote host asynchronously.</summary>
      <param name="requestCallback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the operation is complete.</param>
      <param name="state">A user-defined object that contains information about the receive operation. This object is passed to the <paramref name="requestCallback" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> object that references the asynchronous receive.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.BeginSend(System.Byte[],System.Int32,System.AsyncCallback,System.Object)">
      <summary>Sends a datagram to a remote host asynchronously. The destination was specified previously by a call to <see cref="Overload:System.Net.Sockets.UdpClient.Connect" />.</summary>
      <param name="datagram">A <see cref="T:System.Byte" /> array that contains the data to be sent.</param>
      <param name="bytes">The number of bytes to send.</param>
      <param name="requestCallback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the operation is complete.</param>
      <param name="state">A user-defined object that contains information about the send operation. This object is passed to the <paramref name="requestCallback" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> object that references the asynchronous send.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.BeginSend(System.Byte[],System.Int32,System.Net.IPEndPoint,System.AsyncCallback,System.Object)">
      <summary>Sends a datagram to a destination asynchronously. The destination is specified by a <see cref="T:System.Net.EndPoint" />.</summary>
      <param name="datagram">A <see cref="T:System.Byte" /> array that contains the data to be sent.</param>
      <param name="bytes">The number of bytes to send.</param>
      <param name="endPoint">The <see cref="T:System.Net.EndPoint" /> that represents the destination for the data.</param>
      <param name="requestCallback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the operation is complete.</param>
      <param name="state">A user-defined object that contains information about the send operation. This object is passed to the <paramref name="requestCallback" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> object that references the asynchronous send.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.BeginSend(System.Byte[],System.Int32,System.String,System.Int32,System.AsyncCallback,System.Object)">
      <summary>Sends a datagram to a destination asynchronously. The destination is specified by the host name and port number.</summary>
      <param name="datagram">A <see cref="T:System.Byte" /> array that contains the data to be sent.</param>
      <param name="bytes">The number of bytes to send.</param>
      <param name="hostname">The destination host.</param>
      <param name="port">The destination port number.</param>
      <param name="requestCallback">An <see cref="T:System.AsyncCallback" /> delegate that references the method to invoke when the operation is complete.</param>
      <param name="state">A user-defined object that contains information about the send operation. This object is passed to the <paramref name="requestCallback" /> delegate when the operation is complete.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> object that references the asynchronous send.</returns>
    </member>
    <member name="P:System.Net.Sockets.UdpClient.Client">
      <summary>Gets or sets the underlying network <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>The underlying Network <see cref="T:System.Net.Sockets.Socket" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.Close">
      <summary>Closes the UDP connection.</summary>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.Connect(System.Net.IPAddress,System.Int32)">
      <summary>Establishes a default remote host using the specified IP address and port number.</summary>
      <param name="addr">The <see cref="T:System.Net.IPAddress" /> of the remote host to which you intend to send data.</param>
      <param name="port">The port number to which you intend send data.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.UdpClient" /> is closed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="addr" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> is not between <see cref="F:System.Net.IPEndPoint.MinPort" /> and <see cref="F:System.Net.IPEndPoint.MaxPort" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.Connect(System.Net.IPEndPoint)">
      <summary>Establishes a default remote host using the specified network endpoint.</summary>
      <param name="endPoint">An <see cref="T:System.Net.IPEndPoint" /> that specifies the network endpoint to which you intend to send data.</param>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="endPoint" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.UdpClient" /> is closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.Connect(System.String,System.Int32)">
      <summary>Establishes a default remote host using the specified host name and port number.</summary>
      <param name="hostname">The DNS name of the remote host to which you intend send data.</param>
      <param name="port">The port number on the remote host to which you intend to send data.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.UdpClient" /> is closed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> is not between <see cref="F:System.Net.IPEndPoint.MinPort" /> and <see cref="F:System.Net.IPEndPoint.MaxPort" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.Dispose">
      <summary>Releases the managed and unmanaged resources used by the <see cref="T:System.Net.Sockets.UdpClient" />.</summary>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.Sockets.UdpClient" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="P:System.Net.Sockets.UdpClient.DontFragment">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that specifies whether the <see cref="T:System.Net.Sockets.UdpClient" /> allows Internet Protocol (IP) datagrams to be fragmented.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.UdpClient" /> allows datagram fragmentation; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
      <exception cref="T:System.NotSupportedException">This property can be set only for sockets that use the <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> flag or the <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" /> flag.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.DropMulticastGroup(System.Net.IPAddress)">
      <summary>Leaves a multicast group.</summary>
      <param name="multicastAddr">The <see cref="T:System.Net.IPAddress" /> of the multicast group to leave.</param>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ArgumentException">The IP address is not compatible with the <see cref="T:System.Net.Sockets.AddressFamily" /> value that defines the addressing scheme of the socket.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="multicastAddr" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.DropMulticastGroup(System.Net.IPAddress,System.Int32)">
      <summary>Leaves a multicast group.</summary>
      <param name="multicastAddr">The <see cref="T:System.Net.IPAddress" /> of the multicast group to leave.</param>
      <param name="ifindex">The local address of the multicast group to leave.</param>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ArgumentException">The IP address is not compatible with the <see cref="T:System.Net.Sockets.AddressFamily" /> value that defines the addressing scheme of the socket.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="multicastAddr" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Net.Sockets.UdpClient.EnableBroadcast">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that specifies whether the <see cref="T:System.Net.Sockets.UdpClient" /> may send or receive broadcast packets.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.UdpClient" /> allows broadcast packets; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.EndReceive(System.IAsyncResult,System.Net.IPEndPoint@)">
      <summary>Ends a pending asynchronous receive.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> object returned by a call to <see cref="M:System.Net.Sockets.UdpClient.BeginReceive(System.AsyncCallback,System.Object)" />.</param>
      <param name="remoteEP">The specified remote endpoint.</param>
      <returns>If successful, an array of bytes that contains datagram data.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by a call to the <see cref="M:System.Net.Sockets.UdpClient.BeginReceive(System.AsyncCallback,System.Object)" /> method.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.UdpClient.EndReceive(System.IAsyncResult,System.Net.IPEndPoint@)" /> was previously called for the asynchronous read.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the underlying <see cref="T:System.Net.Sockets.Socket" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.EndSend(System.IAsyncResult)">
      <summary>Ends a pending asynchronous send.</summary>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> object returned by a call to <see cref="Overload:System.Net.Sockets.UdpClient.BeginSend" />.</param>
      <returns>If successful, the number of bytes sent to the <see cref="T:System.Net.Sockets.UdpClient" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by a call to the <see cref="M:System.Net.Sockets.Socket.BeginSend(System.Byte[],System.Int32,System.Int32,System.Net.Sockets.SocketFlags,System.AsyncCallback,System.Object)" /> method.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Net.Sockets.Socket.EndSend(System.IAsyncResult)" /> was previously called for the asynchronous read.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the underlying socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="P:System.Net.Sockets.UdpClient.ExclusiveAddressUse">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that specifies whether the <see cref="T:System.Net.Sockets.UdpClient" /> allows only one client to use a port.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.UdpClient" /> allows only one client to use a specific port; otherwise, <see langword="false" />. The default is <see langword="true" /> for Windows Server 2003 and Windows XP Service Pack 2 and later, and <see langword="false" /> for all other versions.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when attempting to access the underlying socket.</exception>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.JoinMulticastGroup(System.Int32,System.Net.IPAddress)">
      <summary>Adds a <see cref="T:System.Net.Sockets.UdpClient" /> to a multicast group.</summary>
      <param name="ifindex">The interface index associated with the local IP address on which to join the multicast group.</param>
      <param name="multicastAddr">The multicast <see cref="T:System.Net.IPAddress" /> of the group you want to join.</param>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.JoinMulticastGroup(System.Net.IPAddress)">
      <summary>Adds a <see cref="T:System.Net.Sockets.UdpClient" /> to a multicast group.</summary>
      <param name="multicastAddr">The multicast <see cref="T:System.Net.IPAddress" /> of the group you want to join.</param>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ArgumentException">The IP address is not compatible with the <see cref="T:System.Net.Sockets.AddressFamily" /> value that defines the addressing scheme of the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.JoinMulticastGroup(System.Net.IPAddress,System.Int32)">
      <summary>Adds a <see cref="T:System.Net.Sockets.UdpClient" /> to a multicast group with the specified Time to Live (TTL).</summary>
      <param name="multicastAddr">The <see cref="T:System.Net.IPAddress" /> of the multicast group to join.</param>
      <param name="timeToLive">The Time to Live (TTL), measured in router hops.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The TTL provided is not between 0 and 255</exception>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="multicastAddr" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The IP address is not compatible with the <see cref="T:System.Net.Sockets.AddressFamily" /> value that defines the addressing scheme of the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.JoinMulticastGroup(System.Net.IPAddress,System.Net.IPAddress)">
      <summary>Adds a <see cref="T:System.Net.Sockets.UdpClient" /> to a multicast group.</summary>
      <param name="multicastAddr">The multicast <see cref="T:System.Net.IPAddress" /> of the group you want to join.</param>
      <param name="localAddress">The local <see cref="T:System.Net.IPAddress" />.</param>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="P:System.Net.Sockets.UdpClient.MulticastLoopback">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that specifies whether outgoing multicast packets are delivered to the sending application.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Sockets.UdpClient" /> receives outgoing multicast packets; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.Receive(System.Net.IPEndPoint@)">
      <summary>Returns a UDP datagram that was sent by a remote host.</summary>
      <param name="remoteEP">An <see cref="T:System.Net.IPEndPoint" /> that represents the remote host from which the data was sent.</param>
      <returns>An array of type <see cref="T:System.Byte" /> that contains datagram data.</returns>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.ReceiveAsync">
      <summary>Returns a UDP datagram asynchronously that was sent by a remote host.</summary>
      <returns>The task object representing the asynchronous operation.</returns>
      <exception cref="T:System.ObjectDisposedException">The underlying <see cref="T:System.Net.Sockets.Socket" /> has been closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.Send(System.Byte[],System.Int32)">
      <summary>Sends a UDP datagram to a remote host.</summary>
      <param name="dgram">An array of type <see cref="T:System.Byte" /> that specifies the UDP datagram that you intend to send represented as an array of bytes.</param>
      <param name="bytes">The number of bytes in the datagram.</param>
      <returns>The number of bytes sent.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dgram" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.UdpClient" /> has already established a default remote host.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.UdpClient" /> is closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.Send(System.Byte[],System.Int32,System.Net.IPEndPoint)">
      <summary>Sends a UDP datagram to the host at the specified remote endpoint.</summary>
      <param name="dgram">An array of type <see cref="T:System.Byte" /> that specifies the UDP datagram that you intend to send, represented as an array of bytes.</param>
      <param name="bytes">The number of bytes in the datagram.</param>
      <param name="endPoint">An <see cref="T:System.Net.IPEndPoint" /> that represents the host and port to which to send the datagram.</param>
      <returns>The number of bytes sent.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dgram" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Net.Sockets.UdpClient" /> has already established a default remote host.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.UdpClient" /> is closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.Send(System.Byte[],System.Int32,System.String,System.Int32)">
      <summary>Sends a UDP datagram to a specified port on a specified remote host.</summary>
      <param name="dgram">An array of type <see cref="T:System.Byte" /> that specifies the UDP datagram that you intend to send represented as an array of bytes.</param>
      <param name="bytes">The number of bytes in the datagram.</param>
      <param name="hostname">The name of the remote host to which you intend to send the datagram.</param>
      <param name="port">The remote port number with which you intend to communicate.</param>
      <returns>The number of bytes sent.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dgram" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.UdpClient" /> has already established a default remote host.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.UdpClient" /> is closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.SendAsync(System.Byte[],System.Int32)">
      <summary>Sends a UDP datagram asynchronously to a remote host.</summary>
      <param name="datagram">An array of type <see cref="T:System.Byte" /> that specifies the UDP datagram that you intend to send represented as an array of bytes.</param>
      <param name="bytes">The number of bytes in the datagram.</param>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dgram" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.UdpClient" /> has already established a default remote host.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.UdpClient" /> is closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.SendAsync(System.Byte[],System.Int32,System.Net.IPEndPoint)">
      <summary>Sends a UDP datagram asynchronously to a remote host.</summary>
      <param name="datagram">An array of type <see cref="T:System.Byte" /> that specifies the UDP datagram that you intend to send represented as an array of bytes.</param>
      <param name="bytes">The number of bytes in the datagram.</param>
      <param name="endPoint">An <see cref="T:System.Net.IPEndPoint" /> that represents the host and port to which to send the datagram.</param>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dgram" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Net.Sockets.UdpClient" /> has already established a default remote host.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.UdpClient" /> is closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="M:System.Net.Sockets.UdpClient.SendAsync(System.Byte[],System.Int32,System.String,System.Int32)">
      <summary>Sends a UDP datagram asynchronously to a remote host.</summary>
      <param name="datagram">An array of type <see cref="T:System.Byte" /> that specifies the UDP datagram that you intend to send represented as an array of bytes.</param>
      <param name="bytes">The number of bytes in the datagram.</param>
      <param name="hostname">The name of the remote host to which you intend to send the datagram.</param>
      <param name="port">The remote port number with which you intend to communicate.</param>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dgram" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Net.Sockets.UdpClient" /> has already established a default remote host.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Net.Sockets.UdpClient" /> is closed.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error occurred when accessing the socket.</exception>
    </member>
    <member name="P:System.Net.Sockets.UdpClient.Ttl">
      <summary>Gets or sets a value that specifies the Time to Live (TTL) value of Internet Protocol (IP) packets sent by the <see cref="T:System.Net.Sockets.UdpClient" />.</summary>
      <returns>The TTL value.</returns>
    </member>
    <member name="T:System.Net.Sockets.UdpReceiveResult">
      <summary>Presents UDP receive result information from a call to the <see cref="M:System.Net.Sockets.UdpClient.ReceiveAsync" /> method.</summary>
    </member>
    <member name="M:System.Net.Sockets.UdpReceiveResult.#ctor(System.Byte[],System.Net.IPEndPoint)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.UdpReceiveResult" /> class.</summary>
      <param name="buffer">A buffer for data to receive in the UDP packet.</param>
      <param name="remoteEndPoint">The remote endpoint of the UDP packet.</param>
    </member>
    <member name="P:System.Net.Sockets.UdpReceiveResult.Buffer">
      <summary>Gets a buffer with the data received in the UDP packet.</summary>
      <returns>A <see cref="T:System.Byte" /> array with the data received in the UDP packet.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpReceiveResult.Equals(System.Net.Sockets.UdpReceiveResult)">
      <summary>Returns a value that indicates whether this instance is equal to a specified object.</summary>
      <param name="other">The object to compare with this instance.</param>
      <returns>
        <see langword="true" /> if <paramref name="other" /> is an instance of <see cref="T:System.Net.Sockets.UdpReceiveResult" /> and equals the value of the instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpReceiveResult.Equals(System.Object)">
      <summary>Returns a value that indicates whether this instance is equal to a specified object.</summary>
      <param name="obj">The object to compare with this instance.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> is an instance of <see cref="T:System.Net.Sockets.UdpReceiveResult" /> and equals the value of the instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpReceiveResult.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>The hash code.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpReceiveResult.op_Equality(System.Net.Sockets.UdpReceiveResult,System.Net.Sockets.UdpReceiveResult)">
      <summary>Tests whether two specified <see cref="T:System.Net.Sockets.UdpReceiveResult" /> instances are equivalent.</summary>
      <param name="left">The <see cref="T:System.Net.Sockets.UdpReceiveResult" /> instance that is to the left of the equality operator.</param>
      <param name="right">The <see cref="T:System.Net.Sockets.UdpReceiveResult" /> instance that is to the right of the equality operator.</param>
      <returns>
        <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.UdpReceiveResult.op_Inequality(System.Net.Sockets.UdpReceiveResult,System.Net.Sockets.UdpReceiveResult)">
      <summary>Tests whether two specified <see cref="T:System.Net.Sockets.UdpReceiveResult" /> instances are not equal.</summary>
      <param name="left">The <see cref="T:System.Net.Sockets.UdpReceiveResult" /> instance that is to the left of the not equal operator.</param>
      <param name="right">The <see cref="T:System.Net.Sockets.UdpReceiveResult" /> instance that is to the right of the not equal operator.</param>
      <returns>
        <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are unequal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Sockets.UdpReceiveResult.RemoteEndPoint">
      <summary>Gets the remote endpoint from which the UDP packet was received.</summary>
      <returns>The remote endpoint from which the UDP packet was received.</returns>
    </member>
    <member name="T:System.Net.Sockets.UnixDomainSocketEndPoint">
      <summary>Represents a Unix Domain Socket endpoint as a path.</summary>
    </member>
    <member name="M:System.Net.Sockets.UnixDomainSocketEndPoint.#ctor(System.String)">
      <param name="path" />
    </member>
  </members>
</doc>