﻿using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System;
using Coldairarrow.Entity.Serivces;

namespace Coldairarrow.Business.HolidayManage
{
    public class HR_CalendarBusiness : BaseBusiness<HR_Calendar>, IHR_CalendarBusiness, ITransientDependency
    {
        public HR_CalendarBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_Calendar>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_Calendar>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {

                if (search.Condition == "F_Year")
                {
                    var newWhere = DynamicExpressionParser.ParseLambda<HR_Calendar, bool>(
                   ParsingConfig.Default, false, $@"{search.Condition} = @0", search.Keyword);
                    where = where.And(newWhere);
                }
                else
                {
                    var newWhere = DynamicExpressionParser.ParseLambda<HR_Calendar, bool>(
                       ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                    where = where.And(newWhere);
                }
            }

            var temp = await q.Where(where).GetPageResultAsync(input); 
            return temp;
        }
        public string GetHolidayInfoAsync(ConditionDTO input)
        {
            //过滤sql注入
            input.Keyword.FilterSql();
            input.Condition.FilterSql();
            var temp =  Db.GetListBySql<HR_Calendar>("select* from[dbo].[HR_Calendar] where F_Name like'%" + input.Keyword + "%'and F_Year='" + input.Condition + "'").FirstOrDefault();
            if (temp!=null)
            {
                return temp.F_StartTime.Value.ToString("yyyy-MM-dd");
            }
            return "";
        }
        public async Task<HR_Calendar> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        [DataAddLog(UserLogType.日历管理, "F_Name", "日历")]
        public async Task AddDataAsync(HR_Calendar data)
        {
            await InsertAsync(data);
        }
        [DataEditLog(UserLogType.日历管理, "F_Name", "日历")]
        public async Task UpdateDataAsync(HR_Calendar data)
        {
            await UpdateAsync(data);
        }
        [DataDeleteLog(UserLogType.日历管理, "F_Name", "日历")]
        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        } 
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_Calendar>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_Calendar, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_Calendar, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        public async Task SystemIGetDataAsync(List<HR_Calendar> calendars)
        {
            Insert(calendars);
        }
        [DataDeleteLog(UserLogType.系统角色管理, "Calendar", "删除数据")]
        public void DeleteYear(string year)
        {
            Db.ExecuteSql("delete from [dbo].[HR_Calendar] where F_Year = '" + year + "' ");
        }
        public List<HR_Calendar> GetNowDataListAsync(SystemYearInput input)
        {
            if (string.IsNullOrEmpty(input.Year))
            {
                input.Year = DateTime.Now.Year.ToString();
            }
            if (string.IsNullOrEmpty(input.Month))
            {
                input.Month = DateTime.Now.Month.ToString();
            }
            if (input.Month == "0")
            {
                input.Month = "12";
            }
            var nowMonth = Convert.ToDateTime(input.Year + "-" + input.Month + "-01");
            DateTime startMonth = nowMonth.AddMonths(-1);
            DateTime endMonth = nowMonth.AddMonths(1);
          
            var temp = Db.GetListBySql<HR_Calendar>("select* from[dbo].[HR_Calendar] where F_StartTime>= '" + startMonth + "' and F_StartTime<= '" + endMonth + "'");
            foreach (var item in temp)
            {
                item.F_Remark = item.F_StartTime.Value.ToString("yyyy-M-d");
            }
            return temp;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}