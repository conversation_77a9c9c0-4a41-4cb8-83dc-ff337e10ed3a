﻿using Coldairarrow.Business.Wechat_Go;
using Coldairarrow.Entity.Wechat_Go;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Newtonsoft.Json;

namespace Coldairarrow.Api.Controllers.Wechat_Go
{
    [Route("/Wechat_Go/[controller]/[action]")]
    public class Go_NoticeController : BaseApiController
    {
        #region DI

        public Go_NoticeController(IGo_NoticeBusiness go_NoticeBus)
        {
            _go_NoticeBus = go_NoticeBus;
        }

        IGo_NoticeBusiness _go_NoticeBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Go_Notice>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _go_NoticeBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Go_Notice> GetTheData(IdInputDTO input)
        {
            return await _go_NoticeBus.GetTheDataAsync(input.id);
        }
        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult DeleteNotice()
        {
            try
            {
                string Id = HttpContext.Request.Form["Id"].ToString();
                var list = new List<string>();
                list.Add(Id);
                _go_NoticeBus.DeleteDataAsync(list).Wait();
                return Success("删除成功");
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult GetList()
        {
            try
            {
                string Id = HttpContext.Request.Form["teamId"].ToString();
                var  index = HttpContext.Request.Form["index"].ToString().ToBool();
                var list = _go_NoticeBus.getDataByTeamId(Id, index);
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        /// <summary>
        /// 创建团队
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult CreartData()
        {
            try
            {
                string JsonString = HttpContext.Request.Form["info"].ToString();
                var data = JsonConvert.DeserializeObject<Go_Notice>(JsonString);
                if (data.F_Id.IsNullOrEmpty())
                {
                    data.F_IsAble = 1;
                    data.F_Id = Guid.NewGuid().ToString("N");
                    data.F_CreateTime = DateTime.Now;
                    _go_NoticeBus.AddDataAsync(data).Wait();
                    //新增管理员
                    return Success("新建成功");
                }
                else
                {
                    _go_NoticeBus.UpdateDataAsync(data).Wait();
                    return Success("修改成功");
                }

            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Go_Notice data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _go_NoticeBus.AddDataAsync(data);
            }
            else
            {
                await _go_NoticeBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _go_NoticeBus.DeleteDataAsync(ids);
        }

        #endregion
        

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _go_NoticeBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Go_Notice>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _go_NoticeBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}