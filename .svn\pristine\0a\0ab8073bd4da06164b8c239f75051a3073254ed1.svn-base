<template>
  <div>
    <a-button type="primary" icon="upload" @click="uploadFiles()" v-show="!isNoUploadP">{{ titletxt?titletxt:'上传附件' }}
    </a-button>
    <TempUpload ref="tempUpload" :parentObj="this" @handleUpload="handleUpload"></TempUpload>
    <div v-for="(item,i) in fileListInfo" :key="i">
      <div style="width:80%; float:left;height:35px;line-height:35px;">{{ item.F_FileName }}</div>
      <!-- <div style="width:10%;float:left"><a @click="downloadFileFun(item.uid)">预览</a> </div> -->
      <div style="width:10%;float:left;height:35px;line-height:35px;"><a
          @click="downloadFileFun(item.F_Id,item.F_FileName,item.F_FilePath)">下载</a>
      </div>
      <div style="width:10%; float:left;height:35px;line-height:35px;" v-show="!isNoUploadP"><a
          @click="deleteFun(i)">删除</a> </div>
    </div>
  </div>
</template> 
<script>
import TempUpload from './uploadFile'
import { operateFileFun } from '@/utils/tools.js'
import { downLoadFile } from '@/utils/plugin/axios-plugin.js'

export default {
  components: {
    TempUpload
  },
  props: {
    fileEntity: Object,
    isNoUploadP: Boolean,
    titletxt: String,

  },
  data () {
    return {
      fileListInfo: [],
      ModalText: 'Content of the modal',
      visible: false,
      confirmLoading: false,
      title: '',
      UserId: '',
      fileList: [],
      uploading: false,
      downloadUrl: '', //下载地址
      isNoUpload: false,
      uploadUrl: '' //上传地址
    }
  },
  watch: {
    fileEntity: function (data) {
      this.fileListInfo = data.Files
    },
    deep: true
  },
  methods: {
    downloadFileFun (id, title, url) {
      downLoadFile('Base_Manage/Base_FileInfo/DownloadFiles', { fileId: id, fileFolderId: url },
        function (res) {
          if (res) {
            operateFileFun(res, title)
          } else {
            console.log('失败')
          }
        },
        function (err) {
          console.log(err)
        }
      )
    },
    deleteFun (num) {
      this.$delete(this.fileListInfo, num)
    },
    handleUpload (res) {
      this.$emit('handleUpload', res)
      if (this.fileListInfo === null) {
        this.fileListInfo = res.Files
      } else {
        //res.Files.forEach(e => {
        // this.fileEntity.Files.push(e);
        //this.fileListInfo.push(e);
        //});
      }
      //this.fileListInfo = res.Files;
      // if (res.Success) {
      //   this.getDataList()
      // } else {
      //   if (res.Data) {
      //     this.loading = true
      //     downLoadFile('/HolidayManage/HR_HolidayLine/DownloadErrorExcel',
      //       {
      //         id: res.Data
      //       },
      //       function (res) {
      //         console.log(res)
      //         if (res) {
      //           operateFile(res, '失败清单')
      //         } else {
      //           console.log('失败')
      //         }
      //       },
      //       function (err) {
      //         console.log(err)
      //       }
      //     )
      //   }
      // }
    },
    handleChange (info) {
      let fileList = [...info.fileList]
      info.file.status = 'done'
      // 1. Limit the number of uploaded files
      //    Only to show two recent uploaded files, and old ones will be replaced by the new
      //fileList = fileList.slice(-2); 
      const formData = new FormData()
      //formData.append('id', "32323")
      formData.append('files[]', info.file)
      // formData.append('ID', THI) 
      this.uploading = true
      this.$http.post('Base_Manage/Base_FileInfo/UploadFiles', info).then(resJson => {
        this.uploading = false
        if (resJson.Success) {
          this.$message.success('上传成功')
          this.visible = false
          fileList = fileList.map(file => {
            if (file.response) {
              // Component will show file.url as link
              file.url = file.response.url
            }
            return file
          })
          this.fileList = fileList
        } else {
          this.$message.error('上传失败：' + resJson.Msg)
        }
        this.$emit('handleUpload', resJson)
      })
      // 2. read from response and show file link

    },
    beforeUpload () {
      // console.log(323);
    },
    uploadFiles () {
      const downloadUrl = '/HolidayManage/HR_HolidayLine/DownloadTemplate'
      const uploadUrl = '/Base_Manage/Base_FileInfo/UploadFilesWFQ'
      this.$refs.tempUpload.openForm(this.userId, '上传附件', downloadUrl, uploadUrl)
    },
    downloadInfo () {
      //console.log(1111111111111);
    },
  },
}
</script>

<style scoped>
/* tile uploaded pictures */
.upload-list-inline >>> .ant-upload-list-item {
  float: left;
  width: 200px;
  margin-right: 8px;
}
.upload-list-inline >>> .ant-upload-animate-enter {
  animation-name: uploadAnimateInlineIn;
}
.upload-list-inline >>> .ant-upload-animate-leave {
  animation-name: uploadAnimateInlineOut;
}
</style>