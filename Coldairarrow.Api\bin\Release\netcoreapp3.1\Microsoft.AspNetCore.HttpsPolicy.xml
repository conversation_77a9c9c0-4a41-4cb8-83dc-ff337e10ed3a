<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.HttpsPolicy</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Builder.HstsBuilderExtensions">
            <summary>
            Extension methods for the HSTS middleware.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.HstsBuilderExtensions.UseHsts(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            Adds middleware for using HSTS, which adds the Strict-Transport-Security header.
            </summary>
            <param name="app">The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> instance this method extends.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.HstsServicesExtensions">
            <summary>
            Extension methods for the HSTS middleware.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.HstsServicesExtensions.AddHsts(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Microsoft.AspNetCore.HttpsPolicy.HstsOptions})">
            <summary>
            Adds HSTS services.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> for adding services.</param>
            <param name="configureOptions">A delegate to configure the <see cref="T:Microsoft.AspNetCore.HttpsPolicy.HstsOptions"/>.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.HttpsPolicyBuilderExtensions">
            <summary>
            Extension methods for the HttpsRedirection middleware.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.HttpsPolicyBuilderExtensions.UseHttpsRedirection(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            Adds middleware for redirecting HTTP Requests to HTTPS.
            </summary>
            <param name="app">The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> instance this method extends.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> for HttpsRedirection.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.HttpsRedirectionServicesExtensions">
            <summary>
            Extension methods for the HttpsRedirection middleware.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.HttpsRedirectionServicesExtensions.AddHttpsRedirection(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionOptions})">
            <summary>
            Adds HTTPS redirection services.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> for adding services.</param>
            <param name="configureOptions">A delegate to configure the <see cref="T:Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionOptions"/>.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.HttpsPolicy.HstsMiddleware">
            <summary>
            Enables HTTP Strict Transport Security (HSTS)
            See https://tools.ietf.org/html/rfc6797.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.HttpsPolicy.HstsMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.Extensions.Options.IOptions{Microsoft.AspNetCore.HttpsPolicy.HstsOptions},Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Initialize the HSTS middleware.
            </summary>
            <param name="next"></param>
            <param name="options"></param>
            <param name="loggerFactory"></param>
        </member>
        <member name="M:Microsoft.AspNetCore.HttpsPolicy.HstsMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.Extensions.Options.IOptions{Microsoft.AspNetCore.HttpsPolicy.HstsOptions})">
            <summary>
            Initialize the HSTS middleware.
            </summary>
            <param name="next"></param>
            <param name="options"></param>
        </member>
        <member name="M:Microsoft.AspNetCore.HttpsPolicy.HstsMiddleware.Invoke(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Invoke the middleware.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.HttpsPolicy.HstsOptions">
            <summary>
            Options for the Hsts Middleware
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.HttpsPolicy.HstsOptions.MaxAge">
            <summary>
            Sets the max-age parameter of the Strict-Transport-Security header.
            </summary>
            <remarks>
            Max-age is required; defaults to 30 days.
            See: https://tools.ietf.org/html/rfc6797#section-6.1.1
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.HttpsPolicy.HstsOptions.IncludeSubDomains">
            <summary>
            Enables includeSubDomain parameter of the Strict-Transport-Security header.
            </summary>
            <remarks>
            See: https://tools.ietf.org/html/rfc6797#section-6.1.2
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.HttpsPolicy.HstsOptions.Preload">
            <summary>
            Sets the preload parameter of the Strict-Transport-Security header.
            </summary>
            <remarks>
            Preload is not part of the RFC specification, but is supported by web browsers
            to preload HSTS sites on fresh install. See https://hstspreload.org/.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.HttpsPolicy.HstsOptions.ExcludedHosts">
            <summary>
            A list of host names that will not add the HSTS header.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.Extensions.Options.IOptions{Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionOptions},Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Initializes the HttpsRedirectionMiddleware
            </summary>
            <param name="next"></param>
            <param name="options"></param>
            <param name="config"></param>
            <param name="loggerFactory"></param>
        </member>
        <member name="M:Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.Extensions.Options.IOptions{Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionOptions},Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.Logging.ILoggerFactory,Microsoft.AspNetCore.Hosting.Server.Features.IServerAddressesFeature)">
            <summary>
            Initializes the HttpsRedirectionMiddleware
            </summary>
            <param name="next"></param>
            <param name="options"></param>
            <param name="config"></param>
            <param name="loggerFactory"></param>
            <param name="serverAddressesFeature">The</param>
        </member>
        <member name="M:Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.Invoke(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Invokes the HttpsRedirectionMiddleware
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionOptions">
            <summary>
            Options for the HttpsRedirection middleware
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionOptions.RedirectStatusCode">
            <summary>
            The status code used for the redirect response. The default is 307.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionOptions.HttpsPort">
            <summary>
            The HTTPS port to be added to the redirected URL.
            </summary>
            <remarks>
            If the HttpsPort is not set, we will try to get the HttpsPort from the following:
            1. HTTPS_PORT environment variable
            2. IServerAddressesFeature
            If that fails then the middleware will log a warning and turn off.
            </remarks>
        </member>
    </members>
</doc>
