export function getLocalTime (time, cFormat) {
  if (!time) {
    return null
  }
  var time = new Date(parseInt(time.replace('/Date(', '').replace(')/', ''), 10))
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

export function parseTime (time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    time = time.replace(/\-/g, '/')
    if (('' + time).length === 10) time = parseInt(time) * 1000
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    if (key === 'a') return ['一', '二', '三', '四', '五', '六', '日'][value - 1]
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

export function timeFix () {
  const time = new Date()
  const hour = time.getHours()
  return hour < 9 ? '早上好' : hour <= 11 ? '上午好' : hour <= 13 ? '中午好' : hour < 20 ? '下午好' : '晚上好'
}

export function welcome () {
  const arr = ['百尺竿头须进步，十方世界是全身', '只要工夫深，铁杵磨成针', '不登高山，不知天之高也；不临深溪，不知地之厚也', '业精于勤，荒于嬉；行成于思，毁于随',
    '欲穷千里目，更上一层楼', '锲而舍之，朽木不折；锲而不舍，金石可镂', '准备吃什么呢?', '放松一下，来首音乐吧']
  const index = Math.floor(Math.random() * arr.length)
  return arr[index]
}

/**
 * 触发 window.resize
 */
export function triggerWindowResizeEvent () {
  const event = document.createEvent('HTMLEvents')
  event.initEvent('resize', true, true)
  event.eventType = 'message'
  window.dispatchEvent(event)
}

export function handleScrollHeader (callback) {
  let timer = 0

  let beforeScrollTop = window.pageYOffset
  callback = callback || function () { }
  window.addEventListener(
    'scroll',
    event => {
      clearTimeout(timer)
      timer = setTimeout(() => {
        let direction = 'up'
        const afterScrollTop = window.pageYOffset
        const delta = afterScrollTop - beforeScrollTop
        if (delta === 0) {
          return false
        }
        direction = delta > 0 ? 'down' : 'up'
        callback(direction)
        beforeScrollTop = afterScrollTop
      }, 50)
    },
    false
  )
}

/**
 * Remove loading animate
 * @param id parent element id or class
 * @param timeout
 */
export function removeLoadingAnimate (id = '', timeout = 1500) {
  if (id === '') {
    return
  }
  setTimeout(() => {
    document.body.removeChild(document.getElementById(id))
  }, timeout)
}
//默认3秒后关闭页面
export function closePage (timeout = 3000) {
  setTimeout(() => {
    var userAgent = navigator.userAgent
    if (userAgent.indexOf('Firefox') != -1 || userAgent.indexOf('Chrome') != -1) {
      window.location.href = 'about:blank'
      window.opener = null
      window.open('', '_self')
      window.close()
    } else {
      window.opener = null
      window.open('', '_self')
      window.close()
    }
  }, timeout)
}


