<?xml version="1.0"?>
<doc>
    <assembly>
        <name>NetTopologySuite.IO.SqlServerBytes</name>
    </assembly>
    <members>
        <member name="T:NetTopologySuite.IO.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.Properties.Resources.InvalidGeographyHoleOrientation">
            <summary>
              Looks up a localized string similar to When writing a SQL Server geography value, the holes of a polygon must be oriented clockwise..
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.Properties.Resources.InvalidGeographyShellOrientation">
            <summary>
              Looks up a localized string similar to When writing a SQL Server geography value, the shell of a polygon must be oriented counter-clockwise..
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.Properties.Resources.UnexpectedEndOfStream">
            <summary>
              Looks up a localized string similar to The GEOGRAPHY structure received is incomplete and cannot be read..
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.Properties.Resources.UnexpectedGeographyType">
            <summary>
              Looks up a localized string similar to Unsupported type: {0}.
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.Properties.Resources.UnexpectedGeographyVersion">
            <summary>
              Looks up a localized string similar to Version {0} of the GEOGRAPHY structure received. This version isn&apos;t supported..
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.Properties.Resources.UnexpectedGeometryType">
            <summary>
              Looks up a localized string similar to Unsupported Geometry implementation: {0}.
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.Properties.Resources.UnexpectedOgcGeometryType">
            <summary>
              Looks up a localized string similar to Unsupported type: {0}.
            </summary>
        </member>
        <member name="T:NetTopologySuite.IO.SqlServerBytesReader">
            <summary>
                Reads geography or geometry data in the SQL Server serialization format (described in MS-SSCLRT) into
                <see cref="T:NetTopologySuite.Geometries.Geometry"/> instances.
            </summary>
        </member>
        <member name="M:NetTopologySuite.IO.SqlServerBytesReader.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:NetTopologySuite.IO.SqlServerBytesReader"/> class.
            </summary>
        </member>
        <member name="M:NetTopologySuite.IO.SqlServerBytesReader.#ctor(NetTopologySuite.NtsGeometryServices)">
            <summary>
                Initializes a new instance of the <see cref="T:NetTopologySuite.IO.SqlServerBytesReader"/> class.
            </summary>
            <param name="services"> The geometry services used to create <see cref="T:NetTopologySuite.Geometries.Geometry"/> instances. </param>
        </member>
        <member name="P:NetTopologySuite.IO.SqlServerBytesReader.RepairRings">
            <summary>
                Gets or sets whether invalid linear rings should be fixed. Returns false since invalid rings are
                disallowed. Setting does nothing.
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.SqlServerBytesReader.HandleSRID">
            <summary>
                Gets or sets whether the SpatialReference ID must be handled. Returns true since it's always handled.
                Setting does nothing.
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.SqlServerBytesReader.AllowedOrdinates">
            <summary>
                Gets an <see cref="T:NetTopologySuite.Geometries.Ordinates"/> flag that indicate which ordinates can be handled.
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.SqlServerBytesReader.HandleOrdinates">
            <summary>
                Gets and sets <see cref="T:NetTopologySuite.Geometries.Ordinates"/> flag that indicate which ordinates shall be handled.
            </summary>
            <remarks>
                No matter which <see cref="T:NetTopologySuite.Geometries.Ordinates"/> flag you supply, <see cref="F:NetTopologySuite.Geometries.Ordinates.XY"/> are always
                processed, the rest is binary and 'ed with <see cref="P:NetTopologySuite.IO.SqlServerBytesReader.AllowedOrdinates"/>.
            </remarks>
        </member>
        <member name="P:NetTopologySuite.IO.SqlServerBytesReader.IsGeography">
            <summary>
                Gets or sets a value indicating whether to read geography data. If not, geometry data will be read.
            </summary>
        </member>
        <member name="M:NetTopologySuite.IO.SqlServerBytesReader.Read(System.Byte[])">
            <summary>
                Reads a geometry representation from a <see cref="T:byte[]"/> to a Geometry.
            </summary>
            <param name="source"> The source to read the geometry from </param>
            <returns> A Geometry </returns>
        </member>
        <member name="M:NetTopologySuite.IO.SqlServerBytesReader.Read(System.IO.Stream)">
            <summary>
                Reads a geometry representation from a <see cref="T:System.IO.Stream"/> to a Geometry.
            </summary>
            <param name="stream"> The stream to read from. </param>
            <returns> A geometry. </returns>
        </member>
        <member name="T:NetTopologySuite.IO.SqlServerBytesWriter">
            <summary>
                Writes <see cref="T:NetTopologySuite.Geometries.Geometry"/> instances into geography or geometry data in the SQL Server serialization
                format (described in MS-SSCLRT).
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.SqlServerBytesWriter.ByteOrder">
            <summary>
                Gets or sets the desired <see cref="P:NetTopologySuite.IO.SqlServerBytesWriter.ByteOrder"/>. Returns <see cref="F:NetTopologySuite.IO.ByteOrder.LittleEndian"/> since
                it's required. Setting does nothing.
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.SqlServerBytesWriter.HandleSRID">
            <summary>
                Gets or sets whether the SpatialReference ID must be handled. Returns true since it's required. Setting
                does nothing.
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.SqlServerBytesWriter.AllowedOrdinates">
            <summary>
                Gets and <see cref="T:NetTopologySuite.Geometries.Ordinates"/> flag that indicate which ordinates can be handled.
            </summary>
        </member>
        <member name="P:NetTopologySuite.IO.SqlServerBytesWriter.HandleOrdinates">
            <summary>
                Gets and sets <see cref="T:NetTopologySuite.Geometries.Ordinates"/> flag that indicate which ordinates shall be handled.
            </summary>
            <remarks>
                No matter which <see cref="T:NetTopologySuite.Geometries.Ordinates"/> flag you supply, <see cref="F:NetTopologySuite.Geometries.Ordinates.XY"/> are always
                processed, the rest is binary and 'ed with <see cref="P:NetTopologySuite.IO.SqlServerBytesWriter.AllowedOrdinates"/>.
            </remarks>
        </member>
        <member name="P:NetTopologySuite.IO.SqlServerBytesWriter.IsGeography">
            <summary>
                Gets or sets a value indicating whether to write geography data. If not, geometry data will be written.
            </summary>
        </member>
        <member name="M:NetTopologySuite.IO.SqlServerBytesWriter.Write(NetTopologySuite.Geometries.Geometry)">
            <summary>
                Writes a binary representation of a given geometry.
            </summary>
            <param name="geometry"> The geometry </param>
            <returns> The binary representation of geometry </returns>
        </member>
        <member name="M:NetTopologySuite.IO.SqlServerBytesWriter.Write(NetTopologySuite.Geometries.Geometry,System.IO.Stream)">
            <summary>
                Writes a binary representation of a given geometry.
            </summary>
            <param name="geometry"> The geometry </param>
            <param name="stream"> The stream to write to. </param>
        </member>
    </members>
</doc>
