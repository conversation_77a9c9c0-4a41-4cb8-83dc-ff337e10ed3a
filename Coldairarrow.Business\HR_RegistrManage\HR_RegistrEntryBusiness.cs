﻿using Coldairarrow.Entity.HR_RegistrManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.IBusiness;
using Coldairarrow.Entity;
using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Util.Helper;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using System;
using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Business.HolidayManage;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using AutoMapper.Configuration;

namespace Coldairarrow.Business.HR_RegistrManage
{
    public class HR_RegistrEntryBusiness : BaseBusiness<HR_RegistrEntry>, IHR_RegistrEntryBusiness, ITransientDependency
    {
        IOperator _operator;
        private IHR_InterviewRecordBusiness _iHR_InterviewRecordBusiness;
        private IBase_PostBusiness _base_PostBusiness;
        IHR_HolidayLineBusiness _iHR_HolidayLineBusiness;
        IHR_FormalEmployeesBusiness _iHR_Formal;
        IHR_PrepareRecruitsBusiness _PrepareRecruitsBusiness;
        public HR_RegistrEntryBusiness(IDbAccessor db, IOperator @operator,
                IHR_InterviewRecordBusiness iHR_InterviewRecordBusiness,
                IBase_PostBusiness base_PostBusiness, IHR_HolidayLineBusiness iHR_HolidayLineBusiness, IHR_FormalEmployeesBusiness iHR_Formal, IHR_PrepareRecruitsBusiness iHR_PrepareRecruitsBusiness)
            : base(db)
        {
            _operator = @operator;
            _base_PostBusiness = base_PostBusiness;
            _iHR_HolidayLineBusiness = iHR_HolidayLineBusiness;
            _iHR_InterviewRecordBusiness = iHR_InterviewRecordBusiness;
            _iHR_Formal = iHR_Formal;
            _PrepareRecruitsBusiness = iHR_PrepareRecruitsBusiness;
        }

        #region 外部接口

        public async Task<PageResult<HR_RegistrEntry>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RegistrEntry>();
            var search = input.Search;
            if (!search.EmployRelStatus.IsNullOrEmpty() && search.EmployRelStatus == "未入职")
            {
                q = q.Where(i => i.F_BusState == (int)InductionBusState.未入职);
            }
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RegistrEntry, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            return await q.Where(where).GetPageResultAsync(input);
        }
        /// <summary>
        /// 获取未入职员工的数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<HR_RegistrEntry>> GetNoInductionListAsync()
        {
            return await GetIQueryable()
                   .Where(i => i.F_BusState == (int)InductionBusState.未入职)
                   .ToListAsync();
        }
        public async Task<HR_RegistrEntry> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_RegistrEntry data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_RegistrEntry data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }
        [Transactional]
        /// <summary>
        /// 数据保存
        /// </summary>
        /// <param name="data"></param>
        public void SaveFormData(EmployeeRegistrationDTO data)
        {
            ///员工基础信息
            var hR_Entry = data.hR_RegistrEntry;
            if (!hR_Entry.HeadPortrait.IsNullOrEmpty())
            {
                //截取
                var index = hR_Entry.HeadPortrait.IndexOf("/Upload");
                var headpo = hR_Entry.HeadPortrait.Substring(index);
                hR_Entry.HeadPortrait = headpo;
            }
            //员工登记证书
            var hR_RegistrCertificates = data.hR_RegistrCertificates;
            //员工教育背景
            var hR_RegistrEduBacks = data.hR_RegistrEduBacks;
            //员工登记家庭背景
            var hR_RegistrFamilyRelats = data.hR_RegistrFamilyRelats;
            //员工登记关系申明
            var hR_RegistrRelationships = data.hR_RegistrRelationships;
            //员工登记检验结果
            var hR_RegistrResults = data.hR_RegistrResults;
            //员工登记工作经历
            var hR_RegistrWorkExpes = data.hR_RegistrWorkExpes;

            if (data.hR_RegistrEntry.F_Id.IsNullOrEmpty())
            {
                //新增

                InitEntity(hR_Entry, _operator);
                hR_Entry.F_BusState = (int)InductionBusState.未入职;
                this.Db.Insert(hR_Entry);
                if (hR_RegistrCertificates.Count() > 0)
                {
                    //hR_RegistrCertificates.ForEach(item => InitEntity(item));
                    hR_RegistrCertificates.ForEach(item =>
                    {
                        InitEntity(item, _operator);
                        item.F_UserId = hR_Entry.F_Id;
                        item.F_BusState = (int)ASKBusState.正常;
                    });
                    this.Db.Insert(hR_RegistrCertificates);
                }

                if (hR_RegistrEduBacks.Count() > 0)
                {
                    hR_RegistrEduBacks.ForEach(item => InitEntity(item, _operator));
                    hR_RegistrEduBacks.ForEach(item => { item.UserId = hR_Entry.F_Id; item.F_BusState = (int)ASKBusState.正常; });
                    this.Db.Insert(hR_RegistrEduBacks);
                }

                if (hR_RegistrFamilyRelats.Count() > 0)
                {
                    hR_RegistrFamilyRelats.ForEach(item => InitEntity(item, _operator));
                    hR_RegistrFamilyRelats.ForEach(item => { item.F_UserId = hR_Entry.F_Id; item.F_BusState = (int)ASKBusState.正常; });
                    this.Db.Insert(hR_RegistrFamilyRelats);
                }

                if (hR_RegistrRelationships.Count() > 0)
                {
                    hR_RegistrRelationships.ForEach(item => InitEntity(item, _operator));
                    hR_RegistrRelationships.ForEach(item => { item.F_UserId = hR_Entry.F_Id; item.F_BusState = (int)ASKBusState.正常; });
                    this.Db.Insert(hR_RegistrRelationships);
                }

                if (hR_RegistrResults.Count() > 0)
                {
                    hR_RegistrResults.ForEach(item => InitEntity(item, _operator));
                    hR_RegistrResults.ForEach(item => { item.F_UserId = hR_Entry.F_Id; item.F_BusState = (int)ASKBusState.正常; });
                    this.Db.Insert(hR_RegistrResults);
                }

                if (hR_RegistrWorkExpes.Count() > 0)
                {
                    hR_RegistrWorkExpes.ForEach(item => InitEntity(item, _operator));
                    hR_RegistrWorkExpes.ForEach(item => { item.F_UserId = hR_Entry.F_Id; item.F_BusState = (int)ASKBusState.正常; });
                }
            }
            else
            {
                //编辑
                ///员工基础信息
                UpdateEntity(hR_Entry, _operator);
                Update(hR_Entry);

                //员工登记证书
                #region 员工登记证书
                hR_RegistrCertificates = data.hR_RegistrCertificates;
                var r_RegistrCertificates = this.Db.GetIQueryable<HR_RegistrCertificate>()
                                            .Where(i => i.F_UserId == hR_Entry.F_Id).ToList();
                if (r_RegistrCertificates.Count > 0)
                {
                    this.Db.Delete(r_RegistrCertificates);
                }
                if (hR_RegistrCertificates.Count() > 0)
                {
                    //hR_RegistrCertificates.ForEach(item => InitEntity(item));
                    hR_RegistrCertificates.ForEach(item =>
                    {
                        InitEntity(item, _operator);
                        item.F_UserId = hR_Entry.F_Id;
                        item.F_BusState = (int)ASKBusState.正常;
                    });
                    this.Db.Insert(hR_RegistrCertificates);
                }
                #endregion 
                //员工教育背景
                #region 员工教育背景
                hR_RegistrEduBacks = data.hR_RegistrEduBacks;
                var regids = this.Db.GetIQueryable<HR_RegistrEduBack>()
                                       .Where(i => i.UserId == hR_Entry.F_Id).ToList();
                if (regids.Count > 0)
                {
                    this.Db.Delete(regids);
                }
                if (hR_RegistrEduBacks.Count() > 0)
                {
                    hR_RegistrEduBacks.ForEach(item => InitEntity(item, _operator));
                    hR_RegistrEduBacks.ForEach(item => { item.UserId = hR_Entry.F_Id; item.F_BusState = (int)ASKBusState.正常; });
                    this.Db.Insert(hR_RegistrEduBacks);
                }
                #endregion

                //员工登记家庭背景
                #region 员工登记家庭背景
                hR_RegistrFamilyRelats = data.hR_RegistrFamilyRelats;
                var FamilyRelatids = this.Db.GetIQueryable<HR_RegistrFamilyRelat>()
                                       .Where(i => i.F_UserId == hR_Entry.F_Id).ToList();
                if (FamilyRelatids.Count > 0)
                {
                    this.Db.Delete(FamilyRelatids);
                }
                if (hR_RegistrFamilyRelats.Count() > 0)
                {
                    hR_RegistrFamilyRelats.ForEach(item => InitEntity(item, _operator));
                    hR_RegistrFamilyRelats.ForEach(item => { item.F_UserId = hR_Entry.F_Id; item.F_BusState = (int)ASKBusState.正常; });
                    this.Db.Insert(hR_RegistrFamilyRelats);
                }
                #endregion

                //员工登记关系申明
                #region 员工登记关系申明
                hR_RegistrRelationships = data.hR_RegistrRelationships;
                var Relaids = this.Db.GetIQueryable<HR_RegistrRelationship>()
                                        .Where(i => i.F_UserId == hR_Entry.F_Id).ToList();
                if (Relaids.Count > 0)
                {
                    this.Db.Delete(Relaids);
                }
                if (hR_RegistrRelationships.Count() > 0)
                {
                    hR_RegistrRelationships.ForEach(item => InitEntity(item, _operator));
                    hR_RegistrRelationships.ForEach(item => { item.F_UserId = hR_Entry.F_Id; item.F_BusState = (int)ASKBusState.正常; });
                    this.Db.Insert(hR_RegistrRelationships);
                }
                #endregion

                //员工登记检验结果
                #region 员工登记检验结果
                hR_RegistrResults = data.hR_RegistrResults;
                var Resultsids = this.Db.GetIQueryable<HR_RegistrResults>()
                                       .Where(i => i.F_UserId == hR_Entry.F_Id).ToList();
                if (Resultsids.Count > 0)
                {
                    this.Db.Delete(Resultsids);
                }
                if (hR_RegistrResults.Count() > 0)
                {
                    hR_RegistrResults.ForEach(item => InitEntity(item, _operator));
                    hR_RegistrResults.ForEach(item => { item.F_UserId = hR_Entry.F_Id; item.F_BusState = (int)ASKBusState.正常; });
                    this.Db.Insert(hR_RegistrResults);
                }
                #endregion

                //员工登记工作经历
                #region 员工登记工作经历
                hR_RegistrWorkExpes = data.hR_RegistrWorkExpes;
                var WorkExpeids = this.Db.GetIQueryable<HR_RegistrWorkExpe>()
                                           .Where(i => i.F_UserId == hR_Entry.F_Id).ToList();
                if (WorkExpeids.Count > 0)
                {
                    this.Db.Delete(WorkExpeids);
                }
                if (hR_RegistrWorkExpes.Count() > 0)
                {
                    hR_RegistrWorkExpes.ForEach(item => InitEntity(item, _operator));
                    hR_RegistrWorkExpes.ForEach(item => { item.F_UserId = hR_Entry.F_Id; item.F_BusState = (int)ASKBusState.正常; });
                    this.Db.Insert(hR_RegistrWorkExpes);
                }
                #endregion

               

            }

        }
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RegistrEntry>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RegistrEntry, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_RegistrEntry, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        /// <summary>
        /// 获取表单详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public EmployeeRegistrationDTO GetFormData(string id)
        {
            EmployeeRegistrationDTO employeeRegistrationDTO = new EmployeeRegistrationDTO();
            employeeRegistrationDTO.hR_RegistrEntry = GetEntity(id);
            if (employeeRegistrationDTO.hR_RegistrEntry != null)
            {
                var RegEntity = employeeRegistrationDTO.hR_RegistrEntry;
                employeeRegistrationDTO.hR_RegistrCertificates = this.Db.GetIQueryable<HR_RegistrCertificate>().Where(i => i.F_UserId == id).ToList();
                employeeRegistrationDTO.hR_RegistrEduBacks = this.Db.GetIQueryable<HR_RegistrEduBack>().Where(i => i.UserId == id).ToList();
                employeeRegistrationDTO.hR_RegistrFamilyRelats = this.Db.GetIQueryable<HR_RegistrFamilyRelat>().Where(i => i.F_UserId == id).ToList();
                employeeRegistrationDTO.hR_RegistrRelationships = this.Db.GetIQueryable<HR_RegistrRelationship>().Where(i => i.F_UserId == id).ToList();
                employeeRegistrationDTO.hR_RegistrResults = this.Db.GetIQueryable<HR_RegistrResults>().Where(i => i.F_UserId == id).ToList();
                employeeRegistrationDTO.hR_RegistrWorkExpes = this.Db.GetIQueryable<HR_RegistrWorkExpe>().Where(i => i.F_UserId == id).ToList();
            }
            return employeeRegistrationDTO;
        }
        /// <summary>
        /// 是否存在该员工()
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <param name="IdCard">身份证</param>
        /// <returns></returns>

        public string GetIsExist(string userName, string IdCard)
        {
            return GetIQueryable().Where(i => i.NameUser == userName && i.IdCardNumber == IdCard).FirstOrDefault()?.F_Id;
        }
        #endregion

        #region 员工登记与招聘列表对比保存
        /// <summary>
        /// 员工登记与应聘对比并入职保存正式
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [Transactional]
        public async Task SaveFormal(List<SelectData> data, string id)
        {
            if (!id.IsNullOrEmpty())
            {
                //查询员工登记表数据
                var employeeRegistrationDTO = GetFormData(id);
                if (employeeRegistrationDTO != null)
                {
                    CardName cardName = new CardName()
                    {
                        userName = employeeRegistrationDTO.hR_RegistrEntry.NameUser,
                        IdCard = employeeRegistrationDTO.hR_RegistrEntry.IdCardNumber,
                    };
                    var hR_InterviewRecordFromDTO = await _iHR_InterviewRecordBusiness.
                                                  GetKeyTheDataAsync(cardName.userName, cardName.IdCard);
                    var employee = this.SetEmployee(data, employeeRegistrationDTO, hR_InterviewRecordFromDTO);
                    var post = new Base_Post();
                    ///获取他的岗位信息
                    if (hR_InterviewRecordFromDTO != null && hR_InterviewRecordFromDTO.hR_Entry != null)
                    {
                        var q = this.Db.GetIQueryable<HR_RecruitmentCandidates>()
                                    .FirstOrDefault(i => i.F_UserId == hR_InterviewRecordFromDTO.hR_Entry.F_Id);
                        var hR_Recruit = q != null ? this.Db.GetIQueryable<HR_Recruit>()
                                     .FirstOrDefault(i => i.F_Id == q.F_RecruitId) : new HR_Recruit();
                        post = await _base_PostBusiness.GetTheDataAsync(hR_Recruit?.F_Role);
                    }
                    //新增正式员工
                    var hR_FormalEmployees = MapHelper.Mapping<HR_FormalEmployees, HR_RegistrEntry>(employee.hR_RegistrEntry);
                    if (hR_FormalEmployees != null)
                    {
                        InitEntity(hR_FormalEmployees, _operator);
                        hR_FormalEmployees.EmployRelStatus = "试用员工";
                        hR_FormalEmployees.EmployeesCode = _iHR_Formal.GetMaxCode();
                        hR_FormalEmployees.IsSigningContract = 0;
                        hR_FormalEmployees.F_PositionId = post?.F_Id;
                        hR_FormalEmployees.F_BusState = (int)ASKBusState.正常;
                        await this.Db.InsertAsync(hR_FormalEmployees);
                        //新增入职数据
                        HR_Induction hR_Induction = new HR_Induction()
                        {
                            F_UserId = hR_FormalEmployees?.F_Id,
                            F_InductionDate = DateTime.Now,
                            F_ProbationPeriod = 3,
                            F_InductionPosition = post?.F_Name,
                            F_InductionOrg = _base_PostBusiness.GetOrgName(post?.F_Id),
                            F_ChangesOperating = "",
                            F_ChangesType = "雇佣入职",
                            F_ChangesReason = "派驻",
                            F_ChangesDate = DateTime.Now,
                            F_InductionEmployRelStatus = "试用员工",
                            F_BusState = (int)ASKBusState.正常,
                            F_WFState = (int)WFStates.草稿,
                        };
                        InitEntity(hR_Induction, _operator);
                        await this.Db.InsertAsync(hR_Induction);
                        //生成病假
                        await Task.Run(() => _iHR_HolidayLineBusiness.GenerateLeaveSickLeave(hR_FormalEmployees.F_Id, DateTime.Now.Year.ToString(), _operator));

                        //新增教育经历表
                        if (employee.hR_RegistrEduBacks.Count > 0)
                        {
                            List<HR_EmploEducationExp> hR_EmploEducationExps = new List<HR_EmploEducationExp>();
                            foreach (var item in employee.hR_RegistrEduBacks)
                            {
                                HR_EmploEducationExp hR_EmploEducation = new HR_EmploEducationExp();
                                hR_EmploEducation = MapHelper.Mapping<HR_EmploEducationExp, HR_RegistrEduBack>(item);
                                hR_EmploEducation.UserId = hR_FormalEmployees?.F_Id;
                                hR_EmploEducation.F_BusState = (int)ASKBusState.正常;
                                InitEntity(hR_EmploEducation, _operator);
                                hR_EmploEducationExps.Add(hR_EmploEducation);
                            }
                            await Task.Run(() => this.Db.BulkInsert(hR_EmploEducationExps));
                        }
                        //新增工作经历表
                        if (employee.hR_RegistrWorkExpes.Count > 0)
                        {
                            List<HR_SocialWorkExp> hR_EmploEducationExps = new List<HR_SocialWorkExp>();
                            foreach (var item in employee.hR_RegistrWorkExpes)
                            {
                                HR_SocialWorkExp hR_EmploEducation = new HR_SocialWorkExp();
                                hR_EmploEducation = MapHelper.Mapping<HR_SocialWorkExp, HR_RegistrWorkExpe>(item);
                                hR_EmploEducation.F_UserId = hR_FormalEmployees?.F_Id;
                                hR_EmploEducation.F_BusState = (int)ASKBusState.正常;
                                InitEntity(hR_EmploEducation, _operator);
                                hR_EmploEducationExps.Add(hR_EmploEducation);
                            }
                            await Task.Run(() => this.Db.BulkInsert(hR_EmploEducationExps));
                        }
                        //新增登记证书表
                        if (employee.hR_RegistrCertificates.Count > 0)
                        {
                            List<HR_CertificateInfo> hR_EmploEducationExps = new List<HR_CertificateInfo>();
                            foreach (var item in employee.hR_RegistrCertificates)
                            {
                                HR_CertificateInfo hR_EmploEducation = new HR_CertificateInfo();
                                hR_EmploEducation = MapHelper.Mapping<HR_CertificateInfo, HR_RegistrCertificate>(item);
                                hR_EmploEducation.F_UserId = hR_FormalEmployees?.F_Id;
                                hR_EmploEducation.F_BusState = (int)ASKBusState.正常;
                                InitEntity(hR_EmploEducation, _operator);
                                hR_EmploEducationExps.Add(hR_EmploEducation);
                            }
                            await Task.Run(() => this.Db.BulkInsert(hR_EmploEducationExps));
                        }
                        //修改员工登记与应聘的状态
                        if (employeeRegistrationDTO.hR_RegistrEntry != null)
                        {
                            employeeRegistrationDTO.hR_RegistrEntry.F_BusState = (int)InductionBusState.入职;
                            this.Db.Update(employeeRegistrationDTO.hR_RegistrEntry);
                        }
                        if (hR_InterviewRecordFromDTO != null && hR_InterviewRecordFromDTO.hR_Entry != null)
                        {
                            hR_InterviewRecordFromDTO.hR_Entry.F_BusState = (int)InductionBusState.入职;
                            this.Db.Update(hR_InterviewRecordFromDTO.hR_Entry);
                        }
                    }
                }
            }
        }
        /// <summary>
        /// 获取数据实体
        /// </summary>
        /// <param name="data"></param>
        /// <param name="employyDTO"></param>
        /// <param name="InRecordFromDTO"></param>
        /// <returns></returns>
        public EmployeeRegistrationDTO SetEmployee(List<SelectData> data, EmployeeRegistrationDTO employyDTO
                                                    , HR_InterviewRecordFromDTO InRecordFromDTO)
        {
            EmployeeRegistrationDTO employeeRegistrationDTO = new EmployeeRegistrationDTO();
            data = data.OrderBy(i => i.id).ToList();
            var hR_RegistrEntry = employyDTO.hR_RegistrEntry;
            var hR_Entry = InRecordFromDTO.hR_Entry;
            int index = 0;
            employeeRegistrationDTO.hR_RegistrEntry = new HR_RegistrEntry()
            {
                F_CompanyName = data.FirstOrDefault(i => i.id.Contains("所属公司")).value == "1"
                            ? hR_RegistrEntry.F_CompanyName : hR_Entry?.F_CompanyName,
                NameUser = data.FirstOrDefault(i => i.id.Contains("员工姓名")).value == "1"
              ? hR_RegistrEntry.NameUser : hR_Entry?.NameUser,
                Sex = data.FirstOrDefault(i => i.id.Contains("性别")).value == "1"
              ? hR_RegistrEntry.Sex : hR_Entry?.Sex,
                DirthDate = data.FirstOrDefault(i => i.id.Contains("出生日期")).value == "1"
              ? hR_RegistrEntry.DirthDate : hR_Entry?.DirthDate,
                NationalInfo = data.FirstOrDefault(i => i.id.Contains("民族")).value == "1"
              ? hR_RegistrEntry.NationalInfo : hR_Entry?.NationalInfo,
                NativePlace = data.FirstOrDefault(i => i.id.Contains("籍贯")).value == "1"
              ? hR_RegistrEntry.NativePlace : hR_Entry?.NativePlace,
                MaritalStatus = data.FirstOrDefault(i => i.id.Contains("婚姻状况")).value == "1"
             ? hR_RegistrEntry.MaritalStatus : hR_Entry?.MaritalStatus,
                F_Stature = data.FirstOrDefault(i => i.id.Contains("身高")).value == "1"
              ? hR_RegistrEntry.F_Stature : hR_Entry?.F_Stature,
                F_BloodType = data.FirstOrDefault(i => i.id.Contains("血型")).value == "1"
              ? hR_RegistrEntry.F_BloodType : hR_Entry?.F_BloodType,
                F_HealthCondition = data.FirstOrDefault(i => i.id.Contains("健康状况")).value == "1"
              ? hR_RegistrEntry.F_HealthCondition : hR_Entry?.F_HealthCondition,
                F_ForeignLevel = data.FirstOrDefault(i => i.id.Contains("外语水平")).value == "1"
              ? hR_RegistrEntry.F_ForeignLevel : hR_Entry?.F_ForeignLevel,
                F_ProfessionalQualification = data.FirstOrDefault(i => i.id.Contains("职业资格")).value == "1"
              ? hR_RegistrEntry.F_ProfessionalQualification : hR_Entry?.F_ProfessionalQualification,
                PoliticalLandscape = data.FirstOrDefault(i => i.id.Contains("政治面貌")).value == "1"
              ? hR_RegistrEntry.PoliticalLandscape : hR_Entry?.PoliticalLandscape,
                IdCardNumber = data.FirstOrDefault(i => i.id.Contains("身份证号码")).value == "1"
              ? hR_RegistrEntry.IdCardNumber : hR_Entry?.IdCardNumber,
                F_StartWorkTime = hR_RegistrEntry.F_StartWorkTime,
                HomeAddress = data.FirstOrDefault(i => i.id.Contains("目前住址")).value == "1"
              ? hR_RegistrEntry.HomeAddress : hR_Entry?.HomeAddress,
                RegisteredResidence = data.FirstOrDefault(i => i.id.Contains("户口所在地")).value == "1"
              ? hR_RegistrEntry.RegisteredResidence : hR_Entry?.RegisteredResidence,
                MobilePhone = data.FirstOrDefault(i => i.id.Contains("联系电话")).value == "1"
              ? hR_RegistrEntry.MobilePhone : hR_Entry?.MobilePhone,
                EmergencyContact = data.FirstOrDefault(i => i.id.Contains("紧急联系人1")).value == "1"
              ? hR_RegistrEntry.EmergencyContact : hR_Entry?.EmergencyContact,
                EmergencyContactNumber = data.FirstOrDefault(i => i.id.Contains("紧急联系电话1")).value == "1"
              ? hR_RegistrEntry.EmergencyContactNumber : hR_Entry?.EmergencyContactNumber,
                EmergencyContact2 = data.FirstOrDefault(i => i.id.Contains("紧急联系人2")).value == "1"
              ? hR_RegistrEntry.EmergencyContact2 : hR_Entry?.EmergencyContact2,
                EmergencyContactNumber2 = data.FirstOrDefault(i => i.id.Contains("紧急联系电话2")).value == "1"
              ? hR_RegistrEntry.EmergencyContactNumber2 : hR_Entry?.EmergencyContactNumber2,
            };
            //员工登记证书表
            employeeRegistrationDTO.hR_RegistrCertificates = new List<HR_RegistrCertificate>();
            if (data.Find(i => i.id.Contains("zgzs")).value == "1")
            {
                if (employyDTO.hR_RegistrCertificates.Count > 0)
                {
                    foreach (var item in employyDTO.hR_RegistrCertificates)
                    {
                        HR_RegistrCertificate hR_RegistrCertificate = new HR_RegistrCertificate();
                        hR_RegistrCertificate = MapHelper.Mapping<HR_RegistrCertificate, HR_RegistrCertificate>(item);
                        employeeRegistrationDTO.hR_RegistrCertificates.Add(hR_RegistrCertificate);
                    }
                }
            }
            else
            {
                if (InRecordFromDTO.hR_RecruitCertificates.Count > 0)
                {
                    foreach (var item in InRecordFromDTO.hR_RecruitCertificates)
                    {
                        HR_RegistrCertificate hR_RegistrCertificate = new HR_RegistrCertificate();
                        hR_RegistrCertificate = MapHelper.Mapping<HR_RegistrCertificate, HR_RecruitCertificate>(item);
                        employeeRegistrationDTO.hR_RegistrCertificates.Add(hR_RegistrCertificate);
                    }
                }
            }
            //教育经历表
            employeeRegistrationDTO.hR_RegistrEduBacks = new List<HR_RegistrEduBack>();
            if (data.Find(i => i.id.Contains("jyjl")).value == "1")
            {
                if (employyDTO.hR_RegistrEduBacks.Count > 0)
                {
                    foreach (var item in employyDTO.hR_RegistrEduBacks)
                    {
                        HR_RegistrEduBack hR_Registr = new HR_RegistrEduBack();
                        hR_Registr = MapHelper.Mapping<HR_RegistrEduBack, HR_RegistrEduBack>(item);
                        employeeRegistrationDTO.hR_RegistrEduBacks.Add(hR_Registr);
                    }
                }
            }
            else
            {
                if (InRecordFromDTO.hR_RecruitEduBacks.Count > 0)
                {
                    foreach (var item in InRecordFromDTO.hR_RecruitEduBacks)
                    {
                        HR_RegistrEduBack hR_RecruitEdu = new HR_RegistrEduBack();
                        hR_RecruitEdu = MapHelper.Mapping<HR_RegistrEduBack, HR_RecruitEduBack>(item);
                        employeeRegistrationDTO.hR_RegistrEduBacks.Add(hR_RecruitEdu);
                    }
                }
            }
            //工作经历表
            employeeRegistrationDTO.hR_RegistrWorkExpes = new List<HR_RegistrWorkExpe>();
            if (data.Find(i => i.id.Contains("gzjl")).value == "1")
            {
                if (employyDTO.hR_RegistrWorkExpes.Count > 0)
                {
                    foreach (var item in employyDTO.hR_RegistrWorkExpes)
                    {
                        HR_RegistrWorkExpe hR_Registr = new HR_RegistrWorkExpe();
                        hR_Registr = MapHelper.Mapping<HR_RegistrWorkExpe, HR_RegistrWorkExpe>(item);
                        employeeRegistrationDTO.hR_RegistrWorkExpes.Add(hR_Registr);
                    }
                }
            }
            else
            {
                if (InRecordFromDTO.hR_RecruitWorkExpes.Count > 0)
                {
                    foreach (var item in InRecordFromDTO.hR_RecruitWorkExpes)
                    {
                        HR_RegistrWorkExpe hR_RecruitEdu = new HR_RegistrWorkExpe();
                        hR_RecruitEdu = MapHelper.Mapping<HR_RegistrWorkExpe, HR_RecruitWorkExpe>(item);
                        employeeRegistrationDTO.hR_RegistrWorkExpes.Add(hR_RecruitEdu);
                    }
                }
            }
            //
            return employeeRegistrationDTO;
        }
        //public  List<R> GetHR_Registr<R,T>(List<T> Model)
        //{
        //    List<R> = new List<R>();
        //    foreach (var item in Model)
        //    {
        //        R hR_RegistrCertificate = new R();
        //        hR_RegistrCertificate = MapHelper.Mapping<HR_RegistrCertificate, HR_RecruitCertificate>(item);
        //        employeeRegistrationDTO.hR_RegistrCertificates.Add(hR_RegistrCertificate);
        //    }
        //}
        #endregion

        #region 私有成员
        public class CardName
        {
            /// <summary>
            /// 用户名
            /// </summary>
            public string userName { get; set; }
            /// <summary>
            /// 身份证
            /// </summary>
            public string IdCard { get; set; }
        }
        #endregion
    }
}