﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Castle.Core.Internal;
using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Coldairarrow.Api.Controllers.HR_Wechat
{
    [Route("/HR_Manage/[controller]/[action]")]
    public class EntryController : BaseApiController
    {
        #region DI
        public EntryController(IHR_EntryBusiness hR_EntryBus, IHR_RecruitCertificateBusiness hR_RecruitCertificateBus,
            IHR_RecruitWorkExpeBusiness hR_RecruitWorkExpeBus, IHR_RecruitEduBackBusiness hR_RecruitEduBackBus,
            IHR_RecruitmentCandidatesBusiness hR_RecruitmentCandidatesBus,
            IHR_InterviewEvaluationBusiness hR_InterviewEvaluationBus, IHR_InterviewPlanBusiness hR_InterviewPlanBus)
        {
            _hR_EntryBus = hR_EntryBus;
            _hR_RecruitCertificateBus = hR_RecruitCertificateBus;
            _hR_RecruitWorkExpeBus = hR_RecruitWorkExpeBus;
            _hR_RecruitEduBackBus = hR_RecruitEduBackBus;
            _hR_RecruitmentCandidatesBus = hR_RecruitmentCandidatesBus;
            _hR_InterviewEvaluationBus = hR_InterviewEvaluationBus;
            _hR_InterviewPlanBus = hR_InterviewPlanBus;
        }

        IHR_RecruitmentCandidatesBusiness _hR_RecruitmentCandidatesBus { get; }
        IHR_RecruitEduBackBusiness _hR_RecruitEduBackBus { get; }
        IHR_RecruitWorkExpeBusiness _hR_RecruitWorkExpeBus { get; }
        IHR_RecruitCertificateBusiness _hR_RecruitCertificateBus { get; }
        IHR_InterviewEvaluationBusiness _hR_InterviewEvaluationBus { get; }
        IHR_EntryBusiness _hR_EntryBus { get; }
        IHR_InterviewPlanBusiness _hR_InterviewPlanBus { get; }
        #endregion

        /// <summary>
        /// 通过微信ID和电话号码登录并获取信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public HR_InterviewRecordFromDTO GetEntry()
        {
            string JsonString = HttpContext.Request.Form["input"].ToString();
            var input = JsonConvert.DeserializeObject<inputDTO>(JsonString);
            return _hR_EntryBus.GetEntry(input.Id, input.Keyword);
        }

        [HttpPost]
        [NoCheckJWT]
        public HR_InterviewRecordFromDTO GetEntryById()
        {
            string JsonString = HttpContext.Request.Form["input"].ToString();
            var input = JsonConvert.DeserializeObject<inputDTO>(JsonString);
            return _hR_EntryBus.GetEntryById(input.Id);
        }
        /*
         1、编辑个人信息（4个接口、个人信息、证书、社会经历、）

         */

        /// <summary>
        /// 保存个人信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task EntrySaveData()
        {
            string JsonString = HttpContext.Request.Form["data"].ToString();
            var data = JsonConvert.DeserializeObject<HR_Entry>(JsonString);
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);
                data.F_CreateUserName = "小程序";
                data.F_CreateUserId = "小程序";
                await _hR_EntryBus.AddDataAsync(data);
            }
            else
            {
                await _hR_EntryBus.UpdateDataAsync(data);
            }
        }

        /// <summary>
        /// 保存应聘者证书信息
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task RecruitCertificateSaveData()
        {
            string JsonString = HttpContext.Request.Form["data"].ToString();
            var data = JsonConvert.DeserializeObject<HR_RecruitCertificate>(JsonString);
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);
                data.F_CreateUserName = "小程序";
                data.F_CreateUserId = "小程序";
                await _hR_RecruitCertificateBus.AddDataAsync(data);
            }
            else
            {
                await _hR_RecruitCertificateBus.UpdateDataAsync(data);
            }
        }

        /// <summary>
        /// 保存招聘者工作经历
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task RecruitWorkExpeSaveData()
        {
            string JsonString = HttpContext.Request.Form["data"].ToString();
            var data = JsonConvert.DeserializeObject<HR_RecruitWorkExpe>(JsonString);
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);
                data.F_CreateUserName = "小程序";
                data.F_CreateUserId = "小程序";
                await _hR_RecruitWorkExpeBus.AddDataAsync(data);
            }
            else
            {
                await _hR_RecruitWorkExpeBus.UpdateDataAsync(data);
            }
        }
        /// <summary>
        /// 保存应聘者教育背景
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task RecruitEduBackSaveData()
        {
            string JsonString = HttpContext.Request.Form["data"].ToString();
            var data = JsonConvert.DeserializeObject<HR_RecruitEduBack>(JsonString);
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);
                data.F_CreateUserName = "小程序";
                data.F_CreateUserId = "小程序";
                await _hR_RecruitEduBackBus.AddDataAsync(data);
            }
            else
            {
                await _hR_RecruitEduBackBus.UpdateDataAsync(data);
            }
        }
        /// <summary>
        /// 删除工作经历
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task DeleteRecruitWorkExpe()
        {
            string JsonString = HttpContext.Request.Form["data"].ToString();
            var data = new List<string>();
            data.Add(JsonString);
            await _hR_RecruitWorkExpeBus.DeleteDataAsync(data);
        }
        /// <summary>
        /// 删除证书
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task DeleteRecruitCertificate()
        {
            string JsonString = HttpContext.Request.Form["data"].ToString();
            var data = new List<string>();
            data.Add(JsonString);
            await _hR_RecruitCertificateBus.DeleteDataAsync(data);
        }
        /// <summary>
        /// 删除教育背景
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task DeleteRecruitEduBack()
        {
            string JsonString = HttpContext.Request.Form["data"].ToString();
            var data = new List<string>();
            data.Add(JsonString);
            await _hR_RecruitEduBackBus.DeleteDataAsync(data);
        }
        // 2、投简历    1、判断是否投过，没投时投递简历、2、根据投递者ID获取招聘信息

        /// <summary>
        /// 判断是否投过，没投时投递简历
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task SubmiResume()
        {
            string JsonString = HttpContext.Request.Form["data"].ToString();
            var data = JsonConvert.DeserializeObject<SubmiResumeInput>(JsonString);
            await _hR_RecruitmentCandidatesBus.SubmiResume(data);
        }
        /// <summary>
        /// 根据投递者ID获取招聘信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<List<HR_Recruit>> GetRecruitListByUserId()
        {
            string JsonString = HttpContext.Request.Form["input"].ToString();
            var input = JsonConvert.DeserializeObject<ConditionDTO>(JsonString);
            return await _hR_RecruitmentCandidatesBus.GetRecruitListByUserId(input);
        }
        /// <summary>
        /// 面试者的信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public List<ApplicationInfoDTO> GetApplicationInfo()
        {
            ///var userId = "6a4b436524034995a9310a02c347528f";
            string input = HttpContext.Request.Form["input"].ToString();
            //var input = JsonConvert.DeserializeObject<inputDTO>(JsonString);
            return _hR_EntryBus.GetApplicationInfo(input, "");//应聘者ID
        }
        /// <summary>
        /// 面试官查看面试者的信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public List<ApplicationInfoDTO> GetInterviewerCandidates()
        {
            // var userId = "6a4b436524034995a9310a02c347528f";
            //return _hR_EntryBus.GetApplicationInfo(userId, "");
            string JsonString = HttpContext.Request.Form["input"].ToString();
            var input = JsonConvert.DeserializeObject<inputDTO>(JsonString);
            return _hR_EntryBus.GetApplicationInfo(input.Id, input.PostId);//应聘者ID
        }
        /// <summary>
        /// 小程序提交面试评价信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task SaveInterview()
        {
            string JsonString = HttpContext.Request.Form["input"].ToString();
            var input = JsonConvert.DeserializeObject<InterviewModel>(JsonString);
            await _hR_InterviewEvaluationBus.SaveInterview(input);//应聘者ID
        }
        /// <summary>
        /// 小程序通过电话号码获取她需要评价的人员信息
        /// </summary>
        /// <returns></returns>
        /// 
        [HttpPost]
        [NoCheckJWT]
        public List<HR_InterviewEvaluation> EvaluationList()
        {
            string JsonString = HttpContext.Request.Form["input"].ToString();
            var input = JsonConvert.DeserializeObject<InputInteEvaModel>(JsonString);
            return _hR_InterviewEvaluationBus.EvaluationList(input);
        }
        /// <summary>
        /// 放弃面试
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task<string> GiveUpInterview()
        {
            string id = HttpContext.Request.Form["id"].ToString();
            if (!string.IsNullOrEmpty(id))
            {
                await _hR_InterviewPlanBus.GiveUpInterview(new List<string> { id });
            }

            return "成功";
        }
        /// <summary>
        /// 确认入职
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public string ConfirmEntry()
        {
            string input = HttpContext.Request.Form["input"].ToString();
            string postId = HttpContext.Request.Form["postId"].ToString();
            _hR_EntryBus.ConfirmEntry(input, postId);
            return "成功";
        }
        /// <summary>
        /// 放弃入职
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task<string> AbandonEntry()
        {
            string id = HttpContext.Request.Form["id"].ToString();
            await _hR_EntryBus.AbandonEntry(new List<string> { id });
            return "成功";
        }
    }

    public class inputDTO
    {
        /// <summary>
        /// 应聘者ID
        /// </summary>
        public string Id { get; set; }
        public string Keyword { get; set; }
        /// <summary>
        /// 岗位ID
        /// </summary>
        public string PostId { get; set; }
    }
}
