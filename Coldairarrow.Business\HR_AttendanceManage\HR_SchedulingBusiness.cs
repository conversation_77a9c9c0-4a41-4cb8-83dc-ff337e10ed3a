﻿using Coldairarrow.Entity.HR_AttendanceManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.IBusiness;
using System;
using Coldairarrow.Entity.HR_EmployeeInfoManage;

namespace Coldairarrow.Business.HR_AttendanceManage
{
    public class HR_SchedulingBusiness : BaseBusiness<HR_Scheduling>, IHR_SchedulingBusiness, ITransientDependency
    {
        public HR_SchedulingBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_Scheduling>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_Scheduling>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_Scheduling, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_Scheduling> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_Scheduling data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_Scheduling data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_Scheduling>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_Scheduling, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_Scheduling, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }


        /// <summary>
        /// 导入并保存数据
        /// </summary>
        /// <param name="physicPath">物理路径</param>
        /// <returns></returns>
        /// 

        public AjaxResult<DataTable> ImportSaveData(string physicPath, IOperator op, string year)
        {
            AjaxResult<DataTable> ajaxResult = new AjaxResult<DataTable>();
            var importId = Guid.NewGuid().ToString("N");
            DataTable dt = ExcelHelper.ExcelImport(physicPath,0);
            if (dt == null || dt.Rows.Count == 0)
            {
                ajaxResult.Success = false;
                ajaxResult.Msg = "上传数据错误或不能为空";
            }
            else
            {
                dt.Columns.Add("导入错误", typeof(string));
                bool isError = false;
                List<HR_Scheduling> records = new List<HR_Scheduling>();
                //var userEntity = Db.GetIQueryable<HR_FormalEmployees>().ToList();
                //var entity = Db.GetIQueryable<HR_Scheduling>().Where(i => i.F_Year == year).ToList();
                //Db.Delete(entity);
                List<string> yearInfo = new List<string>();
                List<string> NameList = new List<string>();
                int num = 0;
                string month = "";
                foreach (DataRow dr in dt.Rows)
                {
                    num++;
                    if (num == 1)
                    {
                        for (int i = 0; i < dr.ItemArray.Length - 1; i++)
                        {
                            var temp = dr[i].ToString();
                            if (!string.IsNullOrEmpty(temp))
                            {
                                month = temp;
                            }
                            yearInfo.Add(month);
                        }
                    }
                    if (num == 2)
                    {
                        continue;
                    }
                    if (num == 3)
                    {
                        for (int i = 0; i < dr.ItemArray.Length - 1; i++)
                        {
                            if (i != 0)
                            {
                                var temp = dr[i].ToString();
                                yearInfo[i] = year + "-" + yearInfo[i] + "-" + temp;
                            }
                        }
                    }
                    if (num > 3)
                    {

                        string Name = dr[0].ToString();
                        NameList.Add(Name);
                        for (int i = 0; i < dr.ItemArray.Length - 1; i++)
                        {
                            if (i != 0)
                            {
                                var dateTime = Convert.ToDateTime(yearInfo[i]);
                                HR_Scheduling model = new HR_Scheduling()
                                {
                                    F_Id = Guid.NewGuid().ToString("N"),
                                    F_CreateDate = DateTime.Now,
                                    F_CreateUserId = op.UserId,
                                    F_CreateUserName = op.RealName,
                                    F_ImportId = importId,
                                    F_Month = dateTime.Month,
                                    F_Year = dateTime.Year,
                                    F_SchedulingTime = dateTime,
                                    F_UserName = Name,
                                    F_WorkState = dr[i].ToString(),
                                };
                                records.Add(model);
                            }
                        }
                    }


                    //var emp = userEntity.FirstOrDefault(x => x.EmployeesCode == dr["员工编码"].ToString());
                    //if (emp == null)
                    //{
                    //    dr["导入错误"] += "员工不存在;";
                    //    isError = true;
                    //}
                    //else
                    //{
                    //    //model.F_BusState = (int)ASKBusState.正常;
                    //    model.F_UserId = emp.F_Id;

                    //}
                    //if (!isError)
                    //{
                    //    model.F_Id = IdHelper.GetId();
                    //    model.F_CreateDate = DateTime.Now;
                    //    model.F_CreateUserId = op.UserId;
                    //    model.F_CreateUserName = op.RealName;
                    //    //model.F_HolidayTypes = dr["假期类型"].ToString();
                    //    //model.F_EffectTime = dr["开始/生效时间"].ToString().ToDateTime();
                    //    //model.F_EndTime = dr["结束时间"].ToString().ToDateTime();
                    //    //model.F_VacationUnit = dr["假期单位"].ToString();
                    //    //model.F_StandardLine = Convert.ToDecimal(dr["标准额度"].ToString());
                    //    //model.F_LncDecLine = Convert.ToDecimal(dr["增减额度"].ToString());
                    //    //model.F_UsedLine = Convert.ToDecimal(dr["已用额度"].ToString());
                    //    //model.F_ActualAmount = model.F_StandardLine + model.F_LncDecLine;
                    //    //model.F_StandardLineLast = 0;
                    //    //model.F_ActualAmountLast = 0;
                    //    //model.F_Year = year;
                    //    records.Add(model);
                    //}
                }
                var userEntity = Db.GetIQueryable<HR_FormalEmployees>().Where(i => NameList.Contains(i.NameUser)).ToList();
                var userE = records.Select(s => s.F_UserName).ToList();
                var timemin = records.Min(s => s.F_SchedulingTime);
                var timeMax = records.Max(s => s.F_SchedulingTime);
                var recEntity = Db.GetIQueryable<HR_Scheduling>().Where(i => userE.Contains(i.F_UserName) && i.F_SchedulingTime >= timemin && i.F_SchedulingTime <= timeMax).ToList();
                List<HR_Scheduling> delEntity = new List<HR_Scheduling>();
                foreach (var item in records)
                {
                    var userModel = userEntity.FirstOrDefault(i => i.NameUser == item.F_UserName);
                    item.F_UserId = userModel.F_Id;
                    var delmodel = recEntity.Where(i => i.F_UserName == item.F_UserName && i.F_SchedulingTime == item.F_SchedulingTime).ToList();
                    if (delmodel != null)
                    {
                        delEntity.AddRange(delmodel);
                    }
                }
                Db.Delete(delEntity);
                if (isError)
                {
                    ajaxResult.Data = dt;
                    ajaxResult.Success = false;
                    ajaxResult.Msg = "导入失败";

                    return ajaxResult;
                }
                else
                {
                    Insert(records);
                    ajaxResult.Success = true;
                    ajaxResult.Msg = "导入成功";
                    ajaxResult.ErrorCode = 0;
                }
            }
            return ajaxResult;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="baseUserId"></param>
        /// <returns></returns>
        public List<HR_Scheduling> GetWorkInfo(string userId, DateTime startTime, DateTime endTime, string baseUserId)
        {
            if (string.IsNullOrEmpty(userId))
            {
                var userModel = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(i => i.BaseUserId == baseUserId);
                if (userModel == null)
                {
                    return new List<HR_Scheduling>();
                }
                userId = userModel.F_Id;
            }
            var list = Db.GetIQueryable<HR_Scheduling>().Where(i => i.F_UserId == userId && i.F_SchedulingTime >= startTime && i.F_SchedulingTime <= endTime).ToList();
            return list;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}