﻿using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Coldairarrow.Entity;
using Coldairarrow.Business.Base_Manage;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Caching.Distributed;

namespace Coldairarrow.Api.Controllers.HR_EmployeeInfoManage
{
    [Route("/HR_EmployeeInfoManage/[controller]/[action]")]
    public class HR_CompanyEmployController : BaseApiController
    {
        #region DI

        public HR_CompanyEmployController(IHR_CompanyEmployBusiness hR_CompanyEmployBus, IDistributedCache cache)
        {
            _hR_CompanyEmployBus = hR_CompanyEmployBus;
            _cache = cache;
        }
        private readonly IDistributedCache _cache;
        IHR_CompanyEmployBusiness _hR_CompanyEmployBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_CompanyEmploy>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_CompanyEmployBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_CompanyEmploy> GetTheData(IdInputDTO input)
        {
            return await _hR_CompanyEmployBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        //员工变动
        [HttpPost]
        public AjaxResult EmployeeChange(HR_CompanyEmploy data)
        {
            _hR_CompanyEmployBus.EmployeeChange(data);
            return Success();
        }

        [HttpPost]
        public async Task SaveData(HR_CompanyEmploy data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_CompanyEmployBus.AddDataAsync(data);
            }
            else
            {
                await _hR_CompanyEmployBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_CompanyEmployBus.DeleteDataAsync(ids);
        }

        /// <summary>
        /// 企业任职经历撤销撤销
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult CheXiao(IdInputDTO input)
        {
            _hR_CompanyEmployBus.CheXiao(input.id, this.GetOperator());
            return Success();
        }
        #endregion



        #region 导入数据

        /// <summary>
        /// 导入人员变动
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return Error("上传文件不能为空");

            if (Request.Form.Files.Count > 1)
                return Error("只能上传一个文件");

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            var res = _hR_CompanyEmployBus.ImportSaveData(physicPath, this.GetOperator());
            if (res.Data != null)
            {
                string id = GuidHelper.GenerateKey();
                _cache.SetObject(id, res.Data);

                AjaxResult<string> ajaxResult = new AjaxResult<string>();
                ajaxResult.Success = false;
                ajaxResult.Msg = "保存失败";
                ajaxResult.ErrorCode = 3;
                ajaxResult.Data = id;
                return ajaxResult;
            }
            return res;
        }
        #endregion

        #region 导出Excel
        /// <summary>
        /// 模板下载
        /// </summary>
        [HttpPost]
        public FileContentResult DownloadTemplate()
        {
            string filePath = Directory.GetCurrentDirectory() + "/BusinessTemplate/员工变动模板.xls";
            if (FileHelper.Exists(filePath))
            {
                var bys = System.IO.File.ReadAllBytes(filePath);//excel表转换成流
                return File(bys, "application/vnd.ms-excel");
            }
            else
            {
                throw new BusException("找不到模板");
            }

        }
        /// <summary>
        /// 下载错误Excel
        /// </summary>
        [HttpPost]
        public FileContentResult DownloadErrorExcel(IdInputDTO input)
        {
            DataTable dt = _cache.GetObject<DataTable>(input.id);
            if (dt != null)
            {
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "错误清单";
                excelconfig.Title = "错误清单";
                excelconfig.IsAllSizeColumn = true;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                int sort = 0;
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "员工编码", ExcelColumn = "员工编码", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "姓名", ExcelColumn = "姓名", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "变动操作", ExcelColumn = "变动操作", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "目标职位", ExcelColumn = "目标职位", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "目标用工状态", ExcelColumn = "目标用工状态", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "变动类型", ExcelColumn = "变动类型", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "变动原因", ExcelColumn = "变动原因", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "生效时间", ExcelColumn = "生效时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "备注", ExcelColumn = "备注", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "导入错误", ExcelColumn = "导入错误", Alignment = "left", Sort = sort++ });

                var t = ExcelHelper.ExportMemoryStream(dt, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");
                _cache.Remove(input.id);

                return file;
            }
            else
            {
                throw new BusException("找不到文件");
            }

        }
        #endregion

    }
}