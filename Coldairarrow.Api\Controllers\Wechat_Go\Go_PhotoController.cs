﻿using Coldairarrow.Business.Wechat_Go;
using Coldairarrow.Entity.Wechat_Go;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;

namespace Coldairarrow.Api.Controllers.Wechat_Go
{
    [Route("/Wechat_Go/[controller]/[action]")]
    public class Go_PhotoController : BaseApiController
    {
        #region DI

        public Go_PhotoController(IGo_PhotoBusiness go_PhotoBus, IHostingEnvironment hostingEnvironment, IConfiguration configuration)
        {
            _go_PhotoBus = go_PhotoBus;
            _hostingEnvironment = hostingEnvironment;
            _configuration = configuration;
        }

        IGo_PhotoBusiness _go_PhotoBus { get; }
        private static IHostingEnvironment _hostingEnvironment;
        private static IConfiguration _configuration;

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Go_Photo>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _go_PhotoBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Go_Photo> GetTheData(IdInputDTO input)
        {
            return await _go_PhotoBus.GetTheDataAsync(input.id);
        }
        /// <summary>
        /// 获取列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult GetList()
        {
            try
            {
                string Id = HttpContext.Request.Form["teamId"].ToString();
                var index = HttpContext.Request.Form["index"].ToString().ToBool();
                var list = _go_PhotoBus.getDataByTeamId(Id, index);
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        /// <summary>
        /// 创建团队
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult CreartData()
        {
            try
            {
                string JsonString = HttpContext.Request.Form["info"].ToString();
                string openId = HttpContext.Request.Form["openId"].ToString();
                var data = JsonConvert.DeserializeObject<Go_Photo>(JsonString);
                if (data.F_Id.IsNullOrEmpty())
                {
                    data.F_IsAble = 1;
                    data.F_Id = Guid.NewGuid().ToString("N");
                    data.F_CreateTime = DateTime.Now;
                    _go_PhotoBus.AddDataAsync(data).Wait();
                    //新增管理员
                    return Success("新建成功");
                }
                else
                {
                    _go_PhotoBus.UpdateDataAsync(data).Wait();
                    return Success("修改成功");
                }

            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Go_Photo data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _go_PhotoBus.AddDataAsync(data);
            }
            else
            {
                await _go_PhotoBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _go_PhotoBus.DeleteDataAsync(ids);
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult DeletePhoto()
        {
            try
            {
                string id = HttpContext.Request.Form["id"].ToString();
                var ids = new List<string>();
                ids.Add(id);
                _go_PhotoBus.DeleteDataAsync(ids).Wait();
                return Success("删除成功");
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _go_PhotoBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Go_Photo>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _go_PhotoBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion

        #region 二次开发
        /// <summary>
        /// 微信小程序上传图片
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult SingleFileUpload()
        {
            var formFile = Request.Form.Files["file"];//获取请求发送过来的文件
            var currentDate = DateTime.Now;
            var webRootPath = _hostingEnvironment.WebRootPath;//>>>相当于HttpContext.Current.Server.MapPath("") 
            string openId = HttpContext.Request.Form["openId"].ToString();
            string teamId = HttpContext.Request.Form["teamId"].ToString();
            try
            {
                var filePath = $"/UploadFile/{currentDate:yyyyMMdd}/";
                //创建每日存储文件夹
                if (!Directory.Exists(webRootPath + filePath))
                {
                    Directory.CreateDirectory(webRootPath + filePath);
                }
                if (formFile != null)
                {
                    //文件后缀
                    var fileExtension = Path.GetExtension(formFile.FileName);//获取文件格式，拓展名
                    if (fileExtension != ".xls" && fileExtension != ".xlsx" && fileExtension != ".pdf" && fileExtension != ".jpg" && fileExtension != ".png" && fileExtension != ".doc" && fileExtension != ".docx")
                    {
                        return Error("只支持上传execl、pdf、word、图片文件");
                    }
                    //判断文件大小
                    var fileSize = formFile.Length;
                    if (fileSize > 1024 * 1024 * 10) //10M TODO:(1mb=1024X1024b)
                    {
                        //return new JsonResult(new { isSuccess = false, resultMsg = "上传的文件不能大于10M" });
                        return Error("上传的文件不能大于10M");
                    }
                    //保存的文件名称(以名称和保存时间命名)
                    var saveName = currentDate.ToString("HHmmss") + "_" + fileExtension;
                    //文件保存
                    using (var fs = System.IO.File.Create(webRootPath + filePath + saveName))
                    {
                        formFile.CopyTo(fs);
                        fs.Flush();
                    }
                    //完整的文件路径
                    var completeFilePath = Path.Combine(filePath, saveName);
                    var data = new Go_Photo
                    {
                        F_PhotoUrl = $"{_configuration["WebRootUrl"]}{completeFilePath}",
                        F_OpenId = openId,
                        F_TeamId = teamId,
                        F_CreatorId = openId,
                        F_IsAble = 1,
                        F_CreateTime = DateTime.Now,
                        F_Id = Guid.NewGuid().ToString("N")
                    };
                    _go_PhotoBus.AddDataAsync(data).Wait();
                    return Success("上传成功");
                }
                else
                {
                    //return new JsonResult(new { isSuccess = false, resultMsg = "上传失败，未检测上传的文件信息~" });
                    return Error("上传失败，未检测上传的文件信息~");
                }

            }
            catch (Exception ex)
            {
                //return new JsonResult(new { isSuccess = false, resultMsg = "文件保存失败，异常信息为：" + ex.Message });
                return Error("文件保存失败，异常信息为：" + ex.Message);
            }
        }
        #endregion
    }
}