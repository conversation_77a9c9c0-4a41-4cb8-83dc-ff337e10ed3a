﻿
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage.Extensions;
using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Transactions;

namespace Coldairarrow.Api.Controllers.HR_EmployeeInfoManage
{
    [Route("/HR_EmployeeInfoManage/[controller]/[action]")]
    public class HR_InductionController : BaseApiController
    {
        #region DI

        public HR_InductionController(IHR_InductionBusiness hR_InductionBus, IHR_FormalEmployeesBusiness hR_FormalEmployeesBus, IConfiguration configuration, IHR_LaborContractInfoBusiness hR_LaborContractInfoBusiness)
        {
            _configuration = configuration;
            _hR_InductionBus = hR_InductionBus;
            _hR_FormalEmployeesBus = hR_FormalEmployeesBus;
            _hR_LaborContractInfoBusiness = hR_LaborContractInfoBusiness;
        }
        readonly IConfiguration _configuration;
        IHR_InductionBusiness _hR_InductionBus { get; }
        IHR_LaborContractInfoBusiness _hR_LaborContractInfoBusiness;
        IHR_FormalEmployeesBusiness _hR_FormalEmployeesBus { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_InductionDTO>> GetDataList(PageInput<InductionDTO> input)
        {
            return await _hR_InductionBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_InductionThenDTO> GetTheData(IdInputDTO input)
        {
            return await _hR_InductionBus.GetFormDataAsync(input.id);
        }

        #endregion

        #region 提交
        /// <summary>
        /// 业务删除入职
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task BusDeleteData(List<string> ids)
        {
            await _hR_InductionBus.BusDeleteDataAsync(ids);
        }
        public void SaveMethod(HR_InductionThenDTO data)
        {

        }
        [HttpPost]
        public AjaxResult SaveData(HR_InductionThenDTO data)
        {
            try
            {
                var @operator = GetOperator();
                _hR_InductionBus.SaveMethod(data, @operator);
                return Success();
            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }
        }


        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_InductionBus.DeleteDataAsync(ids);
        }

        #endregion

        #region 流程
        /// <summary>
        /// 保存并创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult SaveAndCreateFlow(HR_InductionThenDTO data)
        {
            try
            {
                //using (TransactionScope scope = new TransactionScope())
                //{
                    //SaveMethod(data);
                //    scope.Complete();
                //}
                var ret = _hR_InductionBus.CreateFlow(data, _configuration["OAUrl"] + "rest/archives/");
                if (ret)
                {
                    return Success();
                }
                else
                {
                    return Error("创建流程失败");
                }
            }
            catch (Exception ex)
            {
                return Error("创建流程失败");
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        ///提交退回流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ActWorkflow(HR_InductionThenDTO data)
        {
            try
            {
                using (TransactionScope scope = new TransactionScope())
                {
                    SaveMethod(data);
                    scope.Complete();
                }
                var ret = _hR_InductionBus.ActWorkflow(data, _configuration["OAUrl"] + "rest/archives/");
                if (ret)
                {
                    return Success();
                }
                else
                {
                    return Error("创建流程失败");
                }
            }
            catch (Exception ex)
            {
                return Error("创建流程失败");
                throw new Exception(ex.ToString());
            }

        }


        /// <summary>
        /// 流程编辑
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult Edit(HR_InductionThenDTO data)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(data.F_Id))
                {
                    using (TransactionScope scope = new TransactionScope())
                    {
                        var induction = _hR_InductionBus.GetTheData(data.F_Id);
                        if (induction != null)
                        {
                            induction.F_InductionDate = data.F_InductionDate;
                            induction.F_InductionEmployRelStatus = data.EmployRelStatus;
                            induction.F_ProbationPeriod = data.F_ProbationPeriod;
                            induction.F_ChangesOperating = data.F_ChangesOperating;
                            induction.F_ChangesType = data.F_ChangesType;
                            induction.F_ChangesReason = data.F_ChangesReason;
                            induction.F_ChangesDate = data.F_ChangesDate;
                            induction.F_InductionPosition = data.F_InductionPosition;
                            induction.F_InductionOrg = data.F_InductionOrg;
                            induction.F_InductionRank = data.F_InductionRank;
                            _hR_InductionBus.UpdateData(induction);
                            if (!string.IsNullOrWhiteSpace(induction.F_UserId))
                            {
                                var formalEmployees = _hR_FormalEmployeesBus.GetTheData(induction.F_UserId);
                                if (formalEmployees != null)
                                {
                                    formalEmployees.EmployeesCode = data.EmployeesCode;
                                    formalEmployees.NameUser = data.NameUser;
                                    formalEmployees.IdCardNumber = data.IdCardNumber;
                                    formalEmployees.PassportNo = data.PassportNo;
                                    formalEmployees.Sex = data.Sex;
                                    formalEmployees.DirthDate = data.DirthDate;
                                    formalEmployees.F_DepartmentId = data.F_DepartmentId;
                                    formalEmployees.NationalInfo = data.NationalInfo;
                                    formalEmployees.NativePlace = data.NativePlace;
                                    formalEmployees.F_PositionId = data.F_PositionId;
                                    formalEmployees.EmployRelStatus = data.EmployRelStatus;
                                    formalEmployees.MobilePhone = data.MobilePhone;
                                    formalEmployees.IdCardAddress = data.IdCardAddress;
                                    formalEmployees.F_CompanyId = data.F_CompanyId;
                                    formalEmployees.Remark = data.Remark;
                                    formalEmployees.F_Rank = data.F_InductionRank;
                                    formalEmployees.EncryptForm();
                                    _hR_FormalEmployeesBus.UpdateData(formalEmployees);
                                }
                            }
                        }
                        scope.Complete();
                    }
                }
                return Success();
            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult CreateFlow(HR_InductionThenDTO data)
        {
            var ret = _hR_InductionBus.CreateFlow(data, _configuration["OAUrl"] + "rest/archives/");
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("创建流程失败");
            }
        }

        /// <summary>
        /// 流程回调方法
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult FlowCallBack(FlowInputDTO input)
        {
            _hR_InductionBus.FlowCallBack(input);

            return Success();
        }

        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ArchiveWorkflow(HR_InductionThenDTO data)
        {
            var ret = _hR_InductionBus.ArchiveWorkflow(data, _configuration["OAUrl"] + "rest/archives/");
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("强制归档失败");
            }
        }
        #endregion
    }
}