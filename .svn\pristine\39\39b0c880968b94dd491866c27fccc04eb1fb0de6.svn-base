﻿<template>
  <a-card :bordered="false">
    <a-alert message="面试评价列表显示已被安排面试的应聘者，未安排面试的，请到面试计划中操作；面试官可以在小程序上看到应聘者信息并填写评价。" type="info" show-icon />
    <br />
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="10">
          <a-col :md="4" :sm="24">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="queryParam.condition">
                <a-select-option key="PostName">岗位名称</a-select-option>
                <a-select-option key="NameUser">应聘者</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item>
              <a-input v-model="queryParam.keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-button type="primary" icon="search" @click="
                () => {
                  this.pagination.current = 1
                  this.getDataList()
                }
              ">查询</a-button>
            <a-button style="margin-left: 8px" icon="sync" @click="() => (queryParam = {})">重置</a-button>
            <a-button style="margin-left: 8px" type="primary" icon="redo" @click="getDataList()">刷新</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table" :columns="columns" :rowKey="row => row.F_Id" :dataSource="data" :pagination="false"
      :loading="loading" @change="handleTableChange"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true" size="small">

      <a slot="ApplicantName" slot-scope="text, record"
        @click="openInfo(record.F_RecCanId)">{{ record.ApplicantName }}</a>
      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record.F_Id,false)">评论详情</a>
          <!--  <a @click="handleDelete([record.F_Id])">删除</a> -->
        </template>
      </span>
      <span slot="F_IsThrough" slot-scope="text, record">
        <template>
          <div v-if="record.F_IsThrough!=null && record.F_IsThrough!=''"><b>{{ record.F_IsThrough == 1 ? '是' :'否' }}</b>
          </div>
          <div v-else>
            <a @click="handleEdit(record.F_Id,true)" title="立即为当前面试官填写面试评价">未评价请填写</a>
          </div>
        </template>
      </span>
      <span slot="F_CreateDate" slot-scope="text">
        <template>
          <span>{{ text | dayjs('YYYY-MM-DD')}}</span>
        </template>
      </span>
    </a-table>
    <a-pagination show-size-changer :default-current="pagination.current" :defaultPageSize="pagination.pageSize"
      :showTotal="pagination.showTotal" :total="pagination.total" @showSizeChange="onShowSizeChange"
      @change="onChangeCurrent" style="margin: 5px 0;text-align: center;" />

    <edit-form ref="editForm" :parentObj="this"></edit-form>
  </a-card>
</template>

<script>
import EditForm from './EditForm'

const columns = [
  { title: '招聘名称', dataIndex: 'PostName', width: '10%' },
  { title: '应聘者', dataIndex: 'ApplicantName', width: '10%', scopedSlots: { customRender: 'ApplicantName' } },
  { title: '面试官', dataIndex: 'Interviewer', width: '20%', ellipsis: true },
  { title: '创建时间', dataIndex: 'F_CreateDate', width: '10%', scopedSlots: { customRender: 'F_CreateDate' } },
  {
    title: '面试名称', dataIndex: 'F_RoundInterview', width: '10%', filters: [
      {
        text: '初试',
        value: '1',
      },
      {
        text: '复试',
        value: '2',
      },
      {
        text: '终试',
        value: '3',
      },
    ],
    filterMultiple: true,
    //onFilter: (value, record) => { 
    //record.F_RoundInterview.indexOf(value) === 0;
    // },
    onFilterDropdownVisibleChange: visible => {
    },
    customRender: (text, row, index) => { // 在此处可以修改单元格中的内容包一层div，给div加样式
      if (text == 1) {
        return "初试"
      } else if (text == 2) {
        return "复试"
      } else if (text == 3) {
        return "终试"
      }
    }
  },
  {
    title: '是否通过', dataIndex: 'F_IsThrough', width: '10%', scopedSlots: { customRender: 'F_IsThrough' }, filters: [
      {
        text: '是',
        value: '是',
      },
      {
        text: '否',
        value: '否',
      },
    ],
    filterMultiple: false,
    // onFilter: (value, record) => {
    //   console.log(value), 
    //   if (record.F_IsThrough == "否") {
    //     return true;
    //   }
    // },
    onFilterDropdownVisibleChange: visible => {
      console.log(visible);
    }
  },
  { title: '操作', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
]

export default {
  components: {
    EditForm
  },
  mounted () {
    this.getDataList()
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 15,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      filters: {},
      sorter: { field: 'F_CreateDate', order: 'desc' },
      loading: false,
      columns,
      queryParam: { condition: "NameUser" },
      selectedRowKeys: [],
      roundInterview: [],
      isInterview: ['否'],
    }
  },
  methods: {
    onChangeCurrent (current, pageSize) {
      this.pagination.current = current
      this.getDataList()
    },
    onShowSizeChange (current, pageSize) {
      this.pagination.current = current
      this.pagination.pageSize = pageSize
      this.getDataList()
    },
    handleTableChange (pagination, filters, sorter) {
      this.roundInterview = filters['F_RoundInterview'];
      this.isInterview = filters['F_IsThrough'];
      //this.pagination = { ...pagination }
      this.pagination.current = 1;
      this.filters = { ...filters }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    openInfo (id) {
      this.$router.push({
        path: './../HR_Resume/Info',
        query: {
          id: id
        }
      })
    },
    exportExcel () { },
    getDataList () {
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/HR_Manage/HR_InterviewEvaluation/GetDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'F_CreateDate',
          SortType: this.sorter.order,
          Search: this.queryParam,
          RoundInterview: this.roundInterview,
          IsInterview: this.isInterview,
          ...this.filters
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange (selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    hasSelected () {
      return this.selectedRowKeys.length > 0
    },
    hanldleAdd () {
      this.$refs.editForm.openForm(0, '新建')
    },
    handleEdit (id, bools) {
      var name = '详情'
      if (bools) {
        name = "评价"
      }
      this.$refs.editForm.openForm(id, name, bools)
    },
    handleDelete (ids) {
      var thisObj = this
      this.$confirm({
        title: '确认删除吗?',
        onOk () {
          return new Promise((resolve, reject) => {
            thisObj.$http.post('/HR_Manage/HR_InterviewEvaluation/DeleteData', ids).then(resJson => {
              resolve()

              if (resJson.Success) {
                thisObj.$message.success('操作成功!')

                thisObj.getDataList()
              } else {
                thisObj.$message.error(resJson.Msg)
              }
            })
          })
        }
      })
    }
  }
}
</script>
