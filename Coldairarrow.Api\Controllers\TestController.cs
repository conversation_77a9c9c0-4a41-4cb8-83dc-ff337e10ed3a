﻿using Coldairarrow.Api.Word;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.ERP_CRM;
using Coldairarrow.Util;
using EFCore.Sharding;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers
{
    [Route("/[controller]/[action]")]
    public class TestController : BaseController
    {
        readonly IDbAccessor _repository;

        public TestController(IDbAccessor repository)
        {
            _repository = repository;
        }

        [HttpGet]
        public async Task PressTest()
        {
            Base_User base_User = new Base_User
            {
                Id = Guid.NewGuid().ToString(),
                Birthday = DateTime.Now,
                CreateTime = DateTime.Now,
                CreatorId = Guid.NewGuid().ToString(),
                DepartmentId = Guid.NewGuid().ToString(),
                Password = Guid.NewGuid().ToString(),
                RealName = Guid.NewGuid().ToString(),
                Sex = Sex.Man,
                UserName = Guid.NewGuid().ToString()
            };

            await _repository.InsertAsync(base_User);
            await _repository.UpdateAsync(base_User);
            await _repository.GetIQueryable<Base_User>().Where(x => x.Id == base_User.Id).FirstOrDefaultAsync();
            await _repository.DeleteAsync(base_User);
        }

        [HttpGet]
        public async Task<PageResult<Base_UserLog>> GetLogList()
        {
            return await _repository.GetIQueryable<Base_UserLog>().GetPageResultAsync(new PageInput());
        }


        /// <summary>
        /// 合同套打访问
        /// </summary>
        /// <param name="guid">合同Guid</param>
        /// <param name="category">合同类型,催款函,合同解除函,一次性告知函,催办函,草签合同</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult SetContractGUID(string guid, string category)
        {
            //查询是否有处理完毕，返回url
            var msg = RedisHelper.Get(category + guid);
            //过滤sql注入
            guid.FilterSql();
            category.FilterSql();
            if (string.IsNullOrEmpty(msg))
            {
                //查询是否已经添加过数据
                msg = RedisHelper.Get(guid);
                if (string.IsNullOrEmpty(msg))
                {
                    //第一步，写入guid
                    RedisHelper.LPush(category, guid);
                    switch (category)
                    {
                        case "催款函":
                        case "合同解除函":
                        case "一次性告知函":
                        case "催办函":
                        case "认购解除函":
                        case "违约金催告函":
                            string sqlPart = string.Format(@"                          
                        SELECT DISTINCT pr.CstName AS 'Cstname',pp.SpreadName AS 'ProjName', DATENAME(year,ISNULL(sc.QSDate,so.QSDate)) AS 'bgyear' , 
                        DATENAME(month,ISNULL(sc.QSDate,so.QSDate)) AS 'bgmonth' , right('00'+convert(varchar(2),DATENAME(DAY,ISNULL(sc.QSDate,so.QSDate))),2) AS 'bgday' ,
						 ISNULL(pr.RoomInfo,'') AS 'Room_Address',
						 ISNULL(pr.RoomNewCode,'') AS 'ContractNO',
						 ISNULL(ISNULL(lc.Situation,lo.Situation),'') AS 'Situation',
						  DATENAME(year,DATEADD(DAY,7,GETDATE())) AS 'limityear' , DATENAME(month,DATEADD(DAY,7,GETDATE())) AS 'limitmonth' ,  
						  RIGHT('00'+convert(varchar(2),DATENAME(DAY,DATEADD(DAY,7,GETDATE()))),2) AS 'limitday', 
						  DATENAME(year,GETDATE()) AS 'curyear' , DATENAME(month,GETDATE()) AS 'curmonth' ,  right('00'+convert(varchar(2),DATENAME(DAY,GETDATE())),2) AS 'curday' ,
						  ISNULL(DATENAME(year,so.EndDate),'') AS 'endyear' , ISNULL(DATENAME(month,so.EndDate),'') AS 'endmonth' ,  ISNULL(right('00'+convert(varchar(2),DATENAME(DAY,so.EndDate)),2),'') AS 'endday' ,					 
						 pp.ProjectCompany AS 'ProjectCompany',
						 ISNULL(DATENAME(year,(SELECT TOP 1  case when lastDate='1900-1-1' then null else lastDate end as lastDate FROM s_fee WHERE FeeGUID IN (  SELECT distinct s_Fee.FeeGUID from vs_Trade,s_Fee 
						 WHERE (1=1) AND s_Fee.RmbYe > 0 AND s_Fee.LastDate Is Not Null AND vs_Trade.TradeGUID = s_Fee.TradeGUID AND vs_Trade.Status='激活')  AND (tradeGUID=st.tradeguid AND lastdate < 
						 CONVERT(datetime,convert(char(10),getdate(),20))) AND  Mysoft_ERP25.dbo.s_Fee.ItemType IN ('贷款类房款','非贷款类房款') ORDER BY Sequence,s_fee.feeGUID)),'') AS dueyear,
						 ISNULL(DATENAME(month,(SELECT TOP 1  case when lastDate='1900-1-1' then null else lastDate end as lastDate FROM s_fee WHERE FeeGUID IN (  SELECT distinct s_Fee.FeeGUID from vs_Trade,s_Fee 
						 WHERE (1=1) AND s_Fee.RmbYe > 0 AND s_Fee.LastDate Is Not Null AND vs_Trade.TradeGUID = s_Fee.TradeGUID AND vs_Trade.Status='激活')  AND (tradeGUID=st.tradeguid AND lastdate < 
						 CONVERT(datetime,convert(char(10),getdate(),20))) AND  Mysoft_ERP25.dbo.s_Fee.ItemType IN ('贷款类房款','非贷款类房款') ORDER BY Sequence,s_fee.feeGUID)),'') AS duemonth,
						 ISNULL(right('00'+convert(varchar(2),DATENAME(DAY,(SELECT TOP 1  case when lastDate='1900-1-1' then null else lastDate end as lastDate FROM s_fee WHERE FeeGUID IN (  SELECT distinct s_Fee.FeeGUID 
						 FROM vs_Trade,s_Fee  WHERE (1=1) AND s_Fee.RmbYe > 0 AND s_Fee.LastDate Is Not Null AND vs_Trade.TradeGUID = s_Fee.TradeGUID AND vs_Trade.Status='激活')  AND (tradeGUID=st.tradeguid AND 
						 lastdate < CONVERT(datetime,convert(char(10),getdate(),20))) AND  Mysoft_ERP25.dbo.s_Fee.ItemType IN ('贷款类房款','非贷款类房款') ORDER BY Sequence,s_fee.feeGUID))),2),'') AS dueday 
                        FROM ep_room pr 
						INNER JOIN Mysoft_ERP25.dbo.p_project pp on pr.ProjGUID = pp.ProjGUID 
						INNER JOIN Mysoft_ERP25.dbo.s_Trade st ON st.RoomGUID = pr.RoomGUID
                        LEFT JOIN Mysoft_ERP25.dbo.s_Contract sc ON sc.roomguid = pr.RoomGUID AND sc.ContractGUID = N'{0}'
						LEFT JOIN Mysoft_ERP25.dbo.s_Order so ON so.RoomGUID = pr.RoomGUID  AND so.OrderGUID =  N'{0}'
						LEFT  JOIN Mysoft_ERP25.dbo.LetterInfo lc ON  lc.SaleGUID = sc.ContractGUID 
						LEFT JOIN Mysoft_ERP25.dbo.LetterInfo lo ON lo.SaleGUID = so.OrderGUID 
                        WHERE   sc.ContractGUID =   N'{0}'  OR so.OrderGUID = N'{0}'", guid);
                            List<MortgageUrgingt> mlist = _repository.GetListBySql<MortgageUrgingt>(sqlPart);
                            if (mlist != null && mlist.Count > 0)
                            {
                                RedisHelper.Set(guid, mlist.FirstOrDefault());
                            }
                            break;
                        case "草签合同":
                            string sqlPartc = string.Format(@"select pp.companyguid,pr.Renovation,roomcode,ProjCompany,Zipcode,BusinessNo,QualificationNo,Representative,ContactNumber,RegisteredAddress,Provisionalname,LandArea,
                        DATENAME(yy,ResidentialLife) + '年'+DATENAME(m,ResidentialLife) + '月'+DATENAME(d,ResidentialLife) + '日' as ResidentialLife,
                        DATENAME(yy,NonresidentialLife) + '年'+DATENAME(m,NonresidentialLife) + '月'+DATENAME(d,NonresidentialLife) + '日' as NonresidentialLife,
                        CASE WHEN pb.PeriodNowRoom = 1 THEN NULL ELSE 
						DATENAME(yy,CASE WHEN sc.JFDate IS NOT NULL THEN sc.jfdate ELSE pb.Htjfrq END ) + '年'+DATENAME(m,CASE WHEN sc.JFDate IS NOT NULL THEN sc.jfdate ELSE pb.Htjfrq END ) + '月'+DATENAME(d,CASE WHEN sc.JFDate IS NOT NULL THEN sc.jfdate ELSE pb.Htjfrq END ) + '日' END  as HandoverDate,
                        CASE WHEN pb.PeriodNowRoom = 2 THEN NULL ELSE 
						DATENAME(yy,CASE WHEN sc.JFDate IS NOT NULL THEN sc.jfdate ELSE pb.Htjfrq END ) + '年'+DATENAME(m,CASE WHEN sc.JFDate IS NOT NULL THEN sc.jfdate ELSE pb.Htjfrq END ) + '月'+DATENAME(d,CASE WHEN sc.JFDate IS NOT NULL THEN sc.jfdate ELSE pb.Htjfrq END ) + '日' END  as PeriodDate,
                        DATENAME(yy,pb.Htjfrq) + '年'+DATENAME(m,pb.Htjfrq) + '月'+DATENAME(d,pb.Htjfrq) + '日' as WaterSupplyDate,
                        DATENAME(yy,pb.Htjfrq) + '年'+DATENAME(m,pb.Htjfrq) + '月'+DATENAME(d,pb.Htjfrq) + '日' as PowerSupplyDate,
						CASE WHEN pb.PeriodNowRoom = 1 THEN 2 ELSE 1 END PeriodNowRoom,
                        case when pb.PeriodNowRoom = 2  then PresalePermit else '' end as EstateCertificate,
                        case when pb.PeriodNowRoom = 1 then CertificateNo else '' end as PresalePermit,
                         CertificateNo,pr.Located,Purpose,SupervisionBank,contractNo,stc.cstname, 
                        stc.CardType,stc.CardID,stc.Address, stc.MobileTel,
                        case when Charindex(';',pr.cstname)>0   then 
                        case when PropertyRate between 0.01 and 99.99 then '按份共有' else '共同共有' end
                        else '单独所有' end as OwnershipType,  
                        case when Charindex(';',pr.cstname)>0   then 
                        case when PropertyRate between 0.01 and 99.99 then stc.cstname +':'+ convert(varchar(10),PropertyRate)+ '%' else '' end
                        else '' end as PropertyRate,   
                        case when pr.Renovation = '清水' then sc.HtTotal else 0 end nonRenovationTotal,
                        case when pr.Renovation = '清水' then  Mysoft_ERP25.dbo.fn_ChnMoney(sc.HtTotal) else '0' end nonRenovationTotalUp,
                        case when pr.Renovation = '清水' then sc.BldCjPrice else 0 end nonRenovationBldCjPrice,
                        case when pr.Renovation = '清水' then sc.TnCjPrice else 0 end nonRenovationTnCjPrice,
                        case when pr.Renovation = '装修' then sc.HtTotal else 0 end  RenovationTotal,
                        case when pr.Renovation = '装修' then  Mysoft_ERP25.dbo.fn_ChnMoney(sc.HtTotal) else '0' end RenovationTotalUp,
                        case when pr.Renovation = '装修' then sc.BldCjPrice else 0 end RenovationBldCjPrice,
                        case when pr.Renovation = '装修' then sc.TnCjPrice else 0 end RenovationTnCjPrice, 
                        case  when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then 3
                         when Charindex('一次性',sc.PayformName)>0  then 1
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then 2
                        end as payformType,
                        case  when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then 4
                         when Charindex('一次性',sc.PayformName)>0  then 3
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then 3
                        end as PageNum,
                        case  when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then '、按揭银行'
                         when Charindex('一次性',sc.PayformName)>0  then ''
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then ''
                        end as PageHolder,
                        case  when pr.AreaStatus ='实测' then '不同意' 
                        when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then 'X'
                         when Charindex('一次性',sc.PayformName)>0  then '不同意'
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then '不同意'
                        end as disAgree,
                        case  when pr.AreaStatus ='实测' then 'X' 
                        when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then '90'
                         when Charindex('一次性',sc.PayformName)>0  then 'X'
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then 'X'
                        end as ApplyDay,
                        case   when pr.AreaStatus ='实测' then 'X' 
                        when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then '30'
                         when Charindex('一次性',sc.PayformName)>0  then 'X'
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then 'X'
                        end as PaymentTerm,
                        case   when pr.AreaStatus ='实测' then 'X' 
                        when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then '1'
                         when Charindex('一次性',sc.PayformName)>0  then 'X'
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then 'X'
                        end as Penalty,
                        case   when pr.AreaStatus ='实测' then 'X' 
                        when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then '1'
                         when Charindex('一次性',sc.PayformName)>0  then 'X'
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then 'X'
                        end as Submission,
                        case when Charindex('一次性',sc.PayformName)>0  then  sc.HtTotal else '0' end as Once,
                        case when Charindex('一次性',sc.PayformName)>0  then   Mysoft_ERP25.dbo.fn_ChnMoney(sc.HtTotal)  else '0'  end as OnceUp,
                        case when Charindex('一次性',sc.PayformName)>0  then  '详见本合同附件五'  else ''  end as OnceText,
                        case when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0   then  sc.HtTotal else '0' end as ManyTime,
                        case when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0    then   Mysoft_ERP25.dbo.fn_ChnMoney(sc.HtTotal)  else '0'  end as ManyTimeUp,
                        case when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0    then  '详见本合同附件五'  else ''  end as ManyTimeText,
                        case when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0  then  sc.HtTotal else '0' end as mortgage,
                        case when  Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0   then   Mysoft_ERP25.dbo.fn_ChnMoney(sc.HtTotal)  else '0'  end as mortgageUp, 
                        case when  Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0   then  '详见本合同附件五'  else ''  end as mortgageText ,sc.bldarea,sc.tnarea,sc.bldarea-sc.tnarea as Sharearea
                        from Mysoft_ERP25.dbo.s_contract sc inner join Mysoft_ERP25.dbo.p_room pr on pr.RoomGUID = sc.RoomGUID 
                        left join Mysoft_ERP25.dbo.p_project pp on pr.projguid= pp.projguid
                        left join Mysoft_ERP25.dbo.p_Building pb on pb.BldGUID = pr.BldGUID
                        left join Mysoft_ERP25.dbo.p_projectCompany ppc on ppc.Companyguid = pp.Companyguid
                        left join Mysoft_ERP25.dbo.s_Trade st on st.TradeGUID = sc.TradeGUID
                        left join Mysoft_ERP25.dbo.s_Trade2Cst stc on st.TradeGUID = stc.TradeGUID 
                                     WHERE sc.ContractGUID = N'{0}'", guid);
                            List<InitiateContract> clist = _repository.GetListBySql<InitiateContract>(sqlPartc);

                            if (clist != null && clist.Count > 0)
                            {
                                RedisHelper.Set(guid, clist);
                            }
                            break;
                        default:
                            break;
                    }
                }
                else
                {
                    if (!msg.Contains("http"))
                    {
                        msg = "";
                        if (string.IsNullOrEmpty(msg) && !string.IsNullOrEmpty(guid))
                        {
                            RedisHelper.Del(guid);
                        }
                    }
                }
            }

            var res = new
            {
                name = category,
                status = "done",
                thumbUrl = msg,
                msg
            };

            return JsonContent(res.ToJson());
        }

        /// <summary>
        /// 合同套打访问
        /// </summary>
        /// <param name="guid">合同Guid</param>
        /// <param name="category">合同类型,催款函,合同解除函,一次性告知函,催办函,草签合同</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult SetContractGUIDNew(string guid, string category)
        {
            s_ContractContainer _container = new s_ContractContainer();
            string path = $"/MortgageUrging/"+category + ".docx";
            string physicPath = GetAbsolutePath($"~{path}");

            string newpath = $"/Upload/" + category+DateTime.Now.ToString("yyyyMMddHHmmss") + ".docx";
            string physicNewpath = GetAbsolutePath($"~{newpath}");
            System.IO.File.Copy(physicPath , physicNewpath);
            switch (category)
            {
                case "催款函":
                case "合同解除函":
                case "一次性告知函":
                case "催办函":
                case "认购解除函":
                case "违约金催告函":
                    string sqlPart = string.Format(@"                          
                        SELECT DISTINCT pr.CstName AS 'Cstname',pp.SpreadName AS 'ProjName', DATENAME(year,ISNULL(sc.QSDate,so.QSDate)) AS 'bgyear' , 
                        DATENAME(month,ISNULL(sc.QSDate,so.QSDate)) AS 'bgmonth' , right('00'+convert(varchar(2),DATENAME(DAY,ISNULL(sc.QSDate,so.QSDate))),2) AS 'bgday' ,
						 ISNULL(pr.RoomInfo,'') AS 'Room_Address',
						 ISNULL(pr.RoomNewCode,'') AS 'ContractNO',
						 ISNULL(ISNULL(lc.Situation,lo.Situation),'') AS 'Situation',
						  DATENAME(year,DATEADD(DAY,7,GETDATE())) AS 'limityear' , DATENAME(month,DATEADD(DAY,7,GETDATE())) AS 'limitmonth' ,  
						  RIGHT('00'+convert(varchar(2),DATENAME(DAY,DATEADD(DAY,7,GETDATE()))),2) AS 'limitday', 
						  DATENAME(year,GETDATE()) AS 'curyear' , DATENAME(month,GETDATE()) AS 'curmonth' ,  right('00'+convert(varchar(2),DATENAME(DAY,GETDATE())),2) AS 'curday' ,
						  ISNULL(DATENAME(year,so.EndDate),'') AS 'endyear' , ISNULL(DATENAME(month,so.EndDate),'') AS 'endmonth' ,  ISNULL(right('00'+convert(varchar(2),DATENAME(DAY,so.EndDate)),2),'') AS 'endday' ,					 
						 pp.ProjectCompany AS 'ProjectCompany',
						 ISNULL(DATENAME(year,(SELECT TOP 1  case when lastDate='1900-1-1' then null else lastDate end as lastDate FROM s_fee WHERE FeeGUID IN (  SELECT distinct s_Fee.FeeGUID from vs_Trade,s_Fee 
						 WHERE (1=1) AND s_Fee.RmbYe > 0 AND s_Fee.LastDate Is Not Null AND vs_Trade.TradeGUID = s_Fee.TradeGUID AND vs_Trade.Status='激活')  AND (tradeGUID=st.tradeguid AND lastdate < 
						 CONVERT(datetime,convert(char(10),getdate(),20))) AND  Mysoft_ERP25.dbo.s_Fee.ItemType IN ('贷款类房款','非贷款类房款') ORDER BY Sequence,s_fee.feeGUID)),'') AS dueyear,
						 ISNULL(DATENAME(month,(SELECT TOP 1  case when lastDate='1900-1-1' then null else lastDate end as lastDate FROM s_fee WHERE FeeGUID IN (  SELECT distinct s_Fee.FeeGUID from vs_Trade,s_Fee 
						 WHERE (1=1) AND s_Fee.RmbYe > 0 AND s_Fee.LastDate Is Not Null AND vs_Trade.TradeGUID = s_Fee.TradeGUID AND vs_Trade.Status='激活')  AND (tradeGUID=st.tradeguid AND lastdate < 
						 CONVERT(datetime,convert(char(10),getdate(),20))) AND  Mysoft_ERP25.dbo.s_Fee.ItemType IN ('贷款类房款','非贷款类房款') ORDER BY Sequence,s_fee.feeGUID)),'') AS duemonth,
						 ISNULL(right('00'+convert(varchar(2),DATENAME(DAY,(SELECT TOP 1  case when lastDate='1900-1-1' then null else lastDate end as lastDate FROM s_fee WHERE FeeGUID IN (  SELECT distinct s_Fee.FeeGUID 
						 FROM vs_Trade,s_Fee  WHERE (1=1) AND s_Fee.RmbYe > 0 AND s_Fee.LastDate Is Not Null AND vs_Trade.TradeGUID = s_Fee.TradeGUID AND vs_Trade.Status='激活')  AND (tradeGUID=st.tradeguid AND 
						 lastdate < CONVERT(datetime,convert(char(10),getdate(),20))) AND  Mysoft_ERP25.dbo.s_Fee.ItemType IN ('贷款类房款','非贷款类房款') ORDER BY Sequence,s_fee.feeGUID))),2),'') AS dueday 
                        FROM Mysoft_ERP25.dbo.ep_room pr 
						INNER JOIN Mysoft_ERP25.dbo.p_project pp on pr.ProjGUID = pp.ProjGUID 
						INNER JOIN Mysoft_ERP25.dbo.s_Trade st ON st.RoomGUID = pr.RoomGUID
                        LEFT JOIN Mysoft_ERP25.dbo.s_Contract sc ON sc.roomguid = pr.RoomGUID AND sc.ContractGUID = N'{0}'
						LEFT JOIN Mysoft_ERP25.dbo.s_Order so ON so.RoomGUID = pr.RoomGUID  AND so.OrderGUID =  N'{0}'
						LEFT  JOIN Mysoft_ERP25.dbo.LetterInfo lc ON  lc.SaleGUID = sc.ContractGUID 
						LEFT JOIN Mysoft_ERP25.dbo.LetterInfo lo ON lo.SaleGUID = so.OrderGUID 
                        WHERE   sc.ContractGUID =   N'{0}'  OR so.OrderGUID = N'{0}'", guid);
                    List<MortgageUrgingt> mlist = _repository.GetListBySql<MortgageUrgingt>(sqlPart);
                    _container.AddMortgageUrging(mlist.FirstOrDefault(), physicNewpath);
                    break;
                case "草签合同":
                    string sqlPartc = string.Format(@"select pp.companyguid,pr.Renovation,roomcode,ProjCompany,Zipcode,BusinessNo,QualificationNo,Representative,ContactNumber,RegisteredAddress,Provisionalname,LandArea,
                        DATENAME(yy,ResidentialLife) + '年'+DATENAME(m,ResidentialLife) + '月'+DATENAME(d,ResidentialLife) + '日' as ResidentialLife,
                        DATENAME(yy,NonresidentialLife) + '年'+DATENAME(m,NonresidentialLife) + '月'+DATENAME(d,NonresidentialLife) + '日' as NonresidentialLife,
                        CASE WHEN pb.PeriodNowRoom = 1 THEN NULL ELSE 
						DATENAME(yy,CASE WHEN sc.JFDate IS NOT NULL THEN sc.jfdate ELSE pb.Htjfrq END ) + '年'+DATENAME(m,CASE WHEN sc.JFDate IS NOT NULL THEN sc.jfdate ELSE pb.Htjfrq END ) + '月'+DATENAME(d,CASE WHEN sc.JFDate IS NOT NULL THEN sc.jfdate ELSE pb.Htjfrq END ) + '日' END  as HandoverDate,
                        CASE WHEN pb.PeriodNowRoom = 2 THEN NULL ELSE 
						DATENAME(yy,CASE WHEN sc.JFDate IS NOT NULL THEN sc.jfdate ELSE pb.Htjfrq END ) + '年'+DATENAME(m,CASE WHEN sc.JFDate IS NOT NULL THEN sc.jfdate ELSE pb.Htjfrq END ) + '月'+DATENAME(d,CASE WHEN sc.JFDate IS NOT NULL THEN sc.jfdate ELSE pb.Htjfrq END ) + '日' END  as PeriodDate,
                        DATENAME(yy,pb.Htjfrq) + '年'+DATENAME(m,pb.Htjfrq) + '月'+DATENAME(d,pb.Htjfrq) + '日' as WaterSupplyDate,
                        DATENAME(yy,pb.Htjfrq) + '年'+DATENAME(m,pb.Htjfrq) + '月'+DATENAME(d,pb.Htjfrq) + '日' as PowerSupplyDate,
						CASE WHEN pb.PeriodNowRoom = 1 THEN 2 ELSE 1 END PeriodNowRoom,
                        case when pb.PeriodNowRoom = 2  then PresalePermit else '' end as EstateCertificate,
                        case when pb.PeriodNowRoom = 1 then CertificateNo else '' end as PresalePermit,
                         CertificateNo,pr.Located,Purpose,SupervisionBank,contractNo,stc.cstname, 
                        stc.CardType,stc.CardID,stc.Address, stc.MobileTel,
                        case when Charindex(';',pr.cstname)>0   then 
                        case when PropertyRate between 0.01 and 99.99 then '按份共有' else '共同共有' end
                        else '单独所有' end as OwnershipType,  
                        case when Charindex(';',pr.cstname)>0   then 
                        case when PropertyRate between 0.01 and 99.99 then stc.cstname +':'+ convert(varchar(10),PropertyRate)+ '%' else '' end
                        else '' end as PropertyRate,   
                        case when pr.Renovation = '清水' then sc.HtTotal else 0 end nonRenovationTotal,
                        case when pr.Renovation = '清水' then  Mysoft_ERP25.dbo.fn_ChnMoney(sc.HtTotal) else '0' end nonRenovationTotalUp,
                        case when pr.Renovation = '清水' then sc.BldCjPrice else 0 end nonRenovationBldCjPrice,
                        case when pr.Renovation = '清水' then sc.TnCjPrice else 0 end nonRenovationTnCjPrice,
                        case when pr.Renovation = '装修' then sc.HtTotal else 0 end  RenovationTotal,
                        case when pr.Renovation = '装修' then  Mysoft_ERP25.dbo.fn_ChnMoney(sc.HtTotal) else '0' end RenovationTotalUp,
                        case when pr.Renovation = '装修' then sc.BldCjPrice else 0 end RenovationBldCjPrice,
                        case when pr.Renovation = '装修' then sc.TnCjPrice else 0 end RenovationTnCjPrice, 
                        case  when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then 3
                         when Charindex('一次性',sc.PayformName)>0  then 1
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then 2
                        end as payformType,
                        case  when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then 4
                         when Charindex('一次性',sc.PayformName)>0  then 3
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then 3
                        end as PageNum,
                        case  when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then '、按揭银行'
                         when Charindex('一次性',sc.PayformName)>0  then ''
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then ''
                        end as PageHolder,
                        case  when pr.AreaStatus ='实测' then '不同意' 
                        when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then 'X'
                         when Charindex('一次性',sc.PayformName)>0  then '不同意'
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then '不同意'
                        end as disAgree,
                        case  when pr.AreaStatus ='实测' then 'X' 
                        when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then '90'
                         when Charindex('一次性',sc.PayformName)>0  then 'X'
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then 'X'
                        end as ApplyDay,
                        case   when pr.AreaStatus ='实测' then 'X' 
                        when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then '30'
                         when Charindex('一次性',sc.PayformName)>0  then 'X'
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then 'X'
                        end as PaymentTerm,
                        case   when pr.AreaStatus ='实测' then 'X' 
                        when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then '1'
                         when Charindex('一次性',sc.PayformName)>0  then 'X'
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then 'X'
                        end as Penalty,
                        case   when pr.AreaStatus ='实测' then 'X' 
                        when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0 then '1'
                         when Charindex('一次性',sc.PayformName)>0  then 'X'
                         when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0  then 'X'
                        end as Submission,
                        case when Charindex('一次性',sc.PayformName)>0  then  sc.HtTotal else '0' end as Once,
                        case when Charindex('一次性',sc.PayformName)>0  then   Mysoft_ERP25.dbo.fn_ChnMoney(sc.HtTotal)  else '0'  end as OnceUp,
                        case when Charindex('一次性',sc.PayformName)>0  then  '详见本合同附件五'  else ''  end as OnceText,
                        case when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0   then  sc.HtTotal else '0' end as ManyTime,
                        case when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0    then   Mysoft_ERP25.dbo.fn_ChnMoney(sc.HtTotal)  else '0'  end as ManyTimeUp,
                        case when Charindex('分期',sc.PayformName)>0  and Charindex('按揭',sc.PayformName)=0    then  '详见本合同附件五'  else ''  end as ManyTimeText,
                        case when Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0  then  sc.HtTotal else '0' end as mortgage,
                        case when  Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0   then   Mysoft_ERP25.dbo.fn_ChnMoney(sc.HtTotal)  else '0'  end as mortgageUp, 
                        case when  Charindex('按揭',sc.PayformName)>0 or Charindex('公积金',sc.PayformName)>0 or Charindex('组合',sc.PayformName)>0   then  '详见本合同附件五'  else ''  end as mortgageText ,sc.bldarea,sc.tnarea,sc.bldarea-sc.tnarea as Sharearea
                        from Mysoft_ERP25.dbo.s_contract sc inner join Mysoft_ERP25.dbo.p_room pr on pr.RoomGUID = sc.RoomGUID 
                        left join Mysoft_ERP25.dbo.p_project pp on pr.projguid= pp.projguid
                        left join Mysoft_ERP25.dbo.p_Building pb on pb.BldGUID = pr.BldGUID
                        left join Mysoft_ERP25.dbo.p_projectCompany ppc on ppc.Companyguid = pp.Companyguid
                        left join Mysoft_ERP25.dbo.s_Trade st on st.TradeGUID = sc.TradeGUID
                        left join Mysoft_ERP25.dbo.s_Trade2Cst stc on st.TradeGUID = stc.TradeGUID 
                                     WHERE sc.ContractGUID = N'{0}'", guid);
                    List<InitiateContract> clist = _repository.GetListBySql<InitiateContract>(sqlPartc);

                    _container.AddInitiateContract(clist, physicNewpath);
                    break;
                default:
                    break;
            }


            var res = new
            {
                name = category,
                status = "done",

            };

            return JsonContent(res.ToJson());
        }

    }
}