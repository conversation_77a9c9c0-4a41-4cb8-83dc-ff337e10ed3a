<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Colder.Logging.Abstractions</name>
    </assembly>
    <members>
        <member name="T:Colder.Logging.Abstractions.ElasticsearchOption">
            <summary>
            ES配置
            </summary>
        </member>
        <member name="P:Colder.Logging.Abstractions.ElasticsearchOption.Nodes">
            <summary>
            ES节点
            </summary>
        </member>
        <member name="P:Colder.Logging.Abstractions.ElasticsearchOption.IndexFormat">
            <summary>
            索引格式:custom-index-{0:yyyy.MM}
            </summary>
        </member>
        <member name="T:Colder.Logging.Abstractions.EnableOption">
            <summary>
            是否启用定义
            </summary>
        </member>
        <member name="P:Colder.Logging.Abstractions.EnableOption.Enabled">
            <summary>
            是否已启动
            </summary>
        </member>
        <member name="T:Colder.Logging.Abstractions.LogOptions">
            <summary>
            日志配置项
            </summary>
        </member>
        <member name="P:Colder.Logging.Abstractions.LogOptions.MinLevel">
            <summary>
            最低日志输出级别
            </summary>
        </member>
        <member name="P:Colder.Logging.Abstractions.LogOptions.Console">
            <summary>
            输出到控制台
            </summary>
        </member>
        <member name="P:Colder.Logging.Abstractions.LogOptions.Debug">
            <summary>
            输出到调试
            </summary>
        </member>
        <member name="P:Colder.Logging.Abstractions.LogOptions.File">
            <summary>
            输出到文件
            </summary>
        </member>
        <member name="P:Colder.Logging.Abstractions.LogOptions.Elasticsearch">
            <summary>
            输出到ES
            </summary>
        </member>
        <member name="P:Colder.Logging.Abstractions.LogOptions.Overrides">
            <summary>
            重写日志级别 
            </summary>
        </member>
        <member name="T:Colder.Logging.Abstractions.OverrideOption">
            <summary>
            重写
            </summary>
        </member>
        <member name="P:Colder.Logging.Abstractions.OverrideOption.Source">
            <summary>
            源
            </summary>
        </member>
        <member name="P:Colder.Logging.Abstractions.OverrideOption.MinLevel">
            <summary>
            最低级别
            </summary>
        </member>
    </members>
</doc>
