﻿<template>
  <a-card :bordered="false">
    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="hanldleAdd()">新建</a-button>
      <!-- <a-button type="primary" icon="minus" @click="handleDelete(selectedRowKeys)" :disabled="!hasSelected()"
        :loading="loading">删除</a-button> -->
      <!-- <a-button type="primary" icon="pushpin" :disabled="!hasSelected()" @click="hanldleType()">调整商品类型</a-button> -->
      <a-button type="primary" icon="retweet" @click="hanldleTime()">
        商城开放开关</a-button>
      <a-button type="primary" icon="redo" @click="exportExcel()">导出Excel</a-button>
      <a-button type="primary" icon="redo" @click="getDataList()">刷新</a-button>
    </div>

    <div class="table-page-search-wrapper">
      <a-form layout="inline">

        <a-row :gutter="10">
          <a-col :md="4" :sm="6">
            <a-form-item label="商品名称">
              <a-input v-model="queryParam.searchValue" placeholder="商品名称" />
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="6">
            <a-form-item label="商品类别">
              <a-select allowClear v-model="queryParam.typeId">
                <a-select-option v-for="Diction in shopTypeData" :value="Diction.F_Id" :key="Diction.F_Id">
                  {{ Diction.F_Name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="6">
            <a-form-item label="是否上架">
              <a-select allowClear v-model="queryParam.isView">
                <a-select-option value="1" key="1">
                  上架
                </a-select-option>
                <a-select-option value="0" key="0">
                  下架
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="6">
            <a-button type="primary" icon="search" @click="
                () => {
                  this.pagination.current = 1
                  this.getDataList()
                }
              ">查询</a-button>
            <a-button style="margin-left: 8px" icon="sync" @click="() => (queryParam = {})">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table" :columns="columns" :rowKey="row => row.F_Id" :dataSource="data" :pagination="pagination"
      :loading="loading" @change="handleTableChange" :scroll="{ x: 1300, y: 500 }"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true" size="small">
      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record.F_Id)">编辑</a>
          <a-divider type="vertical" />
          <a @click="handleDelete([record.F_Id])">删除</a>
          <a-divider type="vertical" v-if="!!record.F_JDUrl" />
          <a :href="record.F_JDUrl" v-if="!!record.F_JDUrl" target="_blank">京东跳转</a>
        </template>
      </span>
      <span slot="F_Img" slot-scope="text">
        <template v-if="!!text">
          <img :width="100" :height="100" :src="text" />
        </template>
      </span>
      <span slot="F_Price" slot-scope="text,record">
        <template>
          <a-input-number style="width: 100%;" placeholder="请填写商品价格" :min="1" @blur="HandlePriceChange($event,record)"
            v-model="record.F_Price" autocomplete="off" />
        </template>
      </span>
      <span slot="F_TypeName" slot-scope="text,record">
        <template>
          <a-select v-model="record.F_Type" @change="HandleTypeChange($event,record)">
            <a-select-option v-for="Diction2 in shopTypeData" :value="Diction2.F_Id" :key="Diction2.F_Id">
              {{ Diction2.F_Name }}
            </a-select-option>
          </a-select>
        </template>
      </span>
      <span slot="F_IsViewStr" slot-scope="text,record">
        <template>
          <a-switch checked-children="上架" un-checked-children="下架" :checked="text"
            @change="handleSwitchChange($event,record)" />
        </template>
      </span>
      <span slot="F_Sales" slot-scope="text,record">
        <template>
          <a @click="handleOrderList(record.F_Id)">{{ text }}</a>
        </template>
      </span>
    </a-table>

    <edit-form ref="editForm" :parentObj="this"></edit-form>
    <JDProductForm ref="jdForm" :parentObj="this"></JDProductForm>
    <Time ref="time" :parentObj="this"></Time>
    <CList ref="cList" :parentObj="this"></CList>
  </a-card>
</template>

<script>
import EditForm from './EditForm'
import JDProductForm from './JDProductForm'
import CList from '../Z_OrderDetail/components/CList.vue'
import Time from './Time'
import { downLoadFile } from '@/utils/plugin/axios-plugin.js'
import { operateFile } from '@/utils/tools.js'
const columns = [
  {
    title: '商品名称', dataIndex: 'F_Name', width: 300
  },
  {
    title: '商品主图片', dataIndex: 'F_Img',
    align: 'center', width: 150, scopedSlots: { customRender: 'F_Img' }
  },
  {
    title: '商品价格', dataIndex: 'F_Price',
    align: 'center', width: 100, scopedSlots: { customRender: 'F_Price' }
  },
  {
    title: '商品类型', dataIndex: 'F_TypeName',
    align: 'center', width: 150, scopedSlots: { customRender: 'F_TypeName' }
  },
  {
    title: '商品销量', dataIndex: 'F_Sales',
    align: 'center', width: 100, scopedSlots: {
      customRender: 'F_Sales',
    },
    defaultSortOrder: 'descend',
    sorter: (a, b) => {
      // return this.SortOrder(a, b)
    },
    sortDirections: ['descend', 'ascend'],

  },
  {
    title: '是否上架', dataIndex: 'F_IsViewStr',
    align: 'center', width: 150, scopedSlots: { customRender: 'F_IsViewStr' }
  },
  { title: '操作', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
]

export default {
  components: {
    EditForm,
    JDProductForm,
    Time,
    CList
  },
  mounted () {
    this.getDictData()
    this.getDataList()
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 10,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      filters: {},
      sorter: { field: 'F_Sales', order: 'desc' },
      loading: false,
      columns,
      queryParam: {},
      selectedRowKeys: [],
      shopTypeData: []
    }
  },
  methods: {
    SortOrder (t, d) {
      console.log(t, d)
    },
    handleTableChange (pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.filters = { ...filters }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    handleOrderList (id) {
      this.$refs.cList.openForm(id, '商品明细表')
    },
    HandlePriceChange (e, record) {
      console.log(e.target._value, record)
      //更新状态
      this.loading = true
      var _salf = this
      this.$http
        .post('/Shop_Manage/Z_Products/SaveData2', record)
        .then(resJson => {
          _salf.loading = false
          if (resJson.Success) {
            _salf.$message.success('操作成功!')
            _salf.getDataList()
          } else {
            _salf.$message.error('操作失败：' + 'resJson.Msg')
          }
        })
      console.log(this.data)
      this.$forceUpdate()
    },
    HandleTypeChange (e, record) {
      console.log(e, record)
      //更新状态
      this.loading = true
      var _salf = this
      this.$http
        .post('/Shop_Manage/Z_Products/SaveData2', record)
        .then(resJson => {
          _salf.loading = false
          if (resJson.Success) {
            _salf.$message.success('操作成功!')
            _salf.getDataList()
          } else {
            _salf.$message.error('操作失败：' + 'resJson.Msg')
          }
        })
      console.log(this.data)
      this.$forceUpdate()
    },
    hanldleType () {

    },
    hanldleTime () {
      this.$refs.time.openForm()
    },
    exportExcel () {
      var that = this
      this.loading = true
      const data = {
        PageIndex: this.pagination.current,
        PageRows: this.pagination.pageSize,
        SortField: this.sorter.field || 'F_CreateDate',
        SortType: this.sorter.order || 'desc',
        Search: this.queryParam,
        ...this.filters
      }
      const url = '/Shop_Manage/Z_Products/ExcelDownload'
      downLoadFile(
        url,
        data,
        function (res) {
          console.log(res)
          that.loading = false
          if (res) {
            operateFile(res, '订单明细导出')
          } else {
            console.log('失败')
          }
        },
        function (err) {
          console.log(err)
          that.loading = false
        }
      )

    },
    getDictData () {
      this.shopTypeData = []

      this.loading = true
      this.$http
        .post('/Shop_Manage/Z_ProductsType/GetDataList', {
          PageIndex: 1,
          PageRows: 100,
          Search: {},
          SortField: 'F_Id',
          SortType: 'asc',
        })
        .then(resJson => {
          this.loading = false
          this.shopTypeData = resJson.Data
        })
    },
    getDataList () {
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/Shop_Manage/Z_Products/GetDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'F_Id',
          SortType: this.sorter.order,
          Search: this.queryParam,
          ...this.filters
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    getJDProductForm () {
      this.$refs.jdForm.openForm()
    },
    onSelectChange (selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    handleSwitchChange (value, record) {
      console.log(value, record)
      record.F_IsView = value ? 1 : 0
      const viewStr = value ? '上架' : '下架'
      //更新上架状态
      this.loading = true
      //查询商品详情
      this.$http.post('/Shop_Manage/Z_Products/UpdateData', record).then(resJson => {
        this.loading = false
        console.log(resJson)
        if (resJson.Success) {
          this.$message.success(viewStr + '成功')
          this.getDataList()
        } else {
          this.$message.error(viewStr + '失败:' + resJson.Msg)
        }
        // this.entity = resJson.Data

      })
    },
    hasSelected () {
      return this.selectedRowKeys.length > 0
    },
    hanldleAdd () {
      this.$refs.editForm.openForm()
    },
    handleEdit (id) {
      this.$refs.editForm.openForm(id)
    },
    handleDelete (ids) {
      var thisObj = this
      this.$confirm({
        title: '确认删除吗?',
        onOk () {
          return new Promise((resolve, reject) => {
            thisObj.$http.post('/Shop_Manage/Z_Products/DeleteData', ids).then(resJson => {
              resolve()

              if (resJson.Success) {
                thisObj.$message.success('操作成功!')

                thisObj.getDataList()
              } else {
                thisObj.$message.error(resJson.Msg)
              }
            })
          })
        }
      })
    }
  }
}
</script>