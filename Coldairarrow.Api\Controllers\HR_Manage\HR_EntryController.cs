﻿using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Aliyun.Base.xiaoxizn;

namespace Coldairarrow.Api.Controllers.HR_Manage
{
    [Route("/HR_Manage/[controller]/[action]")]
    public class HR_EntryController : BaseApiController
    {
        #region DI

        public HR_EntryController(IHR_EntryBusiness hR_EntryBus)
        {
            _hR_EntryBus = hR_EntryBus;
        }

        IHR_EntryBusiness _hR_EntryBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_Entry>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_EntryBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_Entry> GetTheData(IdInputDTO input)
        {
            return await _hR_EntryBus.GetTheDataAsync(input.id);
        }

        /// <summary>
        /// 用户简历信息
        /// </summary>
        /// <param name="fileFolderId">文件夹ID</param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult GetFileModel(IdInputDTO input)
        {
            if (!string.IsNullOrEmpty(input.id))
            {
                return Success(_hR_EntryBus.GetFileModel(input.id), "解析成功！");
            }
            else
            {
                return Error("未找到相关数据！");
            }
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_Entry data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_EntryBus.AddDataAsync(data);
            }
            else
            {
                await _hR_EntryBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_EntryBus.DeleteDataAsync(ids);
        }

        /// <summary>
        /// 放弃入职
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task AbandonEntry(List<string> ids)
        {
            await _hR_EntryBus.AbandonEntry(ids);
        }
        /// <summary>
        /// 确认入职
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ConfirmEntry(ConfirmModel confirmModel)
        {

            if (_hR_EntryBus.ConfirmEntry(confirmModel.id, confirmModel.postId))
                return Success("确认入职成功！");
            else
                return Error("确认入职失败！");
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_EntryBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<HR_Entry>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _hR_EntryBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
        #region model
        public class ConfirmModel
        {
            /// <summary>
            /// 用户id
            /// </summary>
            public string id { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string postId { get; set; }
        }
        #endregion
    }
}