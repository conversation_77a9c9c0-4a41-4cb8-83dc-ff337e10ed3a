﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System.Linq.Expressions;
using System;
using AutoMapper;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.IBusiness;
using System.IO;
using System.Drawing;
using Coldairarrow.Entity.Enum;
using Coldairarrow.Entity;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_InterviewEvaluationBusiness : BaseBusiness<HR_InterviewEvaluation>, IHR_InterviewEvaluationBusiness, ITransientDependency
    {
        readonly IMapper _mapper;
        IHR_ContentOperationLogBusiness _ContentOperationLogBusiness;
        IHR_InterviewPlanBusiness _InterviewPlanBusiness;
        public HR_InterviewEvaluationBusiness(IDbAccessor db, IMapper mapper, IHR_ContentOperationLogBusiness ContentOperationLogBusiness, IHR_InterviewPlanBusiness InterviewPlanBusiness)
            : base(db)
        {
            _mapper = mapper;
            _ContentOperationLogBusiness = ContentOperationLogBusiness;
            _InterviewPlanBusiness = InterviewPlanBusiness;
        }

        #region 外部接口

        public async Task<PageResult<HR_InterviewEvaluationDTO>> GetDataListAsync(PageInput<ConditionDTO> input, IOperator op = null)
        {
            Expression<Func<HR_InterviewEvaluation, HR_InterviewPlan, HR_Entry, Base_Post, HR_Recruit, HR_InterviewEvaluationDTO>> select = (t, i, e, f, g) => new HR_InterviewEvaluationDTO
            {
                ApplicantName = e.NameUser,
                InterviewName = i.F_InterviewName,
                F_RoundInterview = i.F_RoundInterview.Value,
                //PostName = f.F_Name,
                PostName = g.F_RecruitName,
                F_RecCanId = i.F_RecCanId,
                NameUser = e.NameUser,
                F_IsInterview = i.F_IsInterview,
            };
            select = select.BuildExtendSelectExpre();
            var q = from t in this.Db.GetIQueryable<HR_InterviewEvaluation>().AsExpandable()
                    join i in this.Db.GetIQueryable<HR_InterviewPlan>() on t.F_PlanId equals i.F_Id into plan
                    from i in plan.DefaultIfEmpty()
                    join e in this.Db.GetIQueryable<HR_Entry>() on t.F_ApplicantId equals e.F_Id into entry
                    from e in entry.DefaultIfEmpty()
                    join f in this.Db.GetIQueryable<Base_Post>() on i.F_PostId equals f.F_Id into qtEn
                    from f in qtEn.DefaultIfEmpty()
                    join d in this.Db.GetIQueryable<HR_Recruit>() on f.F_Id equals d.F_Role into recrd
                    from g in recrd.DefaultIfEmpty()
                    select @select.Invoke(t, i, e, f, g);
            var where = LinqHelper.True<HR_InterviewEvaluationDTO>();
            var search = input.Search;
            q = q.Where(i => !i.F_IsDelete.HasValue || i.F_IsDelete == 0);
            if (input.RoundInterview?.Count > 0)
            {
                var interList = input.RoundInterview.Select(s => Convert.ToInt32(s)).ToList();
                q = q.Where(i => interList.Contains(i.F_RoundInterview));
            }

            if (input.PostId?.Count > 0)
            {
                q = q.Where(i => input.PostId.Contains(i.PostId));
            }

            if (input.IsInterview?.Count > 0)
            {
                var interList = input.IsInterview.Select(s => s == "是" ? 1 : 0).ToList();
                var ids = interList.FirstOrDefault();
                if (ids == 1)
                {
                    q = q.Where(i => i.F_IsThrough == 1);
                }
                else
                {
                    q = q.Where(i => !i.F_IsThrough.HasValue || i.F_IsThrough == 0);
                }

            }
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_InterviewEvaluationDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            if (!op.IsAdmin())
            {
                var sqlstr = "select * from [dbo].[Base_UserRole] where RoleId=(select top 1  Id from Base_Role where userId='" + op.UserId + "' and  RoleName like '%招聘管理员%')";
                var entity = Db.GetListBySql<Base_UserRole>(sqlstr).ToList();
                if (entity.Count == 0)
                {
                    var userInfo = Db.GetIQueryable<HR_FormalEmployees>().Where(i => i.BaseUserId == op.UserId).FirstOrDefault();
                    if (userInfo == null)
                    {
                        throw new Exception("当前用户未作用户绑定");
                    }
                    q = q.Where(i => i.F_InterviewerIdAll.Contains(userInfo.F_Id));
                }
            }
            var planIds = q.Select(i => i.F_PlanId).Distinct().ToList();

            var planIdList = (from a in Db.GetIQueryable<HR_InterviewPlan>().Where(i => planIds.Contains(i.F_Id) && i.F_IsGiveUpInterview == 0)
                              join b in Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => i.F_IsGiveUpInterview == 0 && i.F_IsDelete != 1) on a.F_RecCanId equals b.F_Id
                              select new
                              {
                                  id = a.F_Id,
                              }).ToList();
            var idList = planIdList.Select(i => i.id).ToList();
            if (idList.Count > 0)
            {
                q = q.Where(i => idList.Contains(i.F_PlanId));
            }
            var temp = await q.Where(where).GetPageResultAsync(input);
            List<String> userIds = new List<string>();
            foreach (var item in temp.Data)
            {
                var listIds = item.F_InterviewerIdAll.Split(new Char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                userIds.AddRange(listIds);
            }

            userIds = userIds.Distinct().ToList();
            var userList = Db.GetIQueryable<HR_FormalEmployees>().Where(i => userIds.Contains(i.F_Id)).ToList();
            foreach (var plan in temp.Data)
            {
                var listIds = plan.F_InterviewerIdAll.Split(new Char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                var users = userList.Where(i => listIds.Contains(i.F_Id)).Select(s => s.NameUser).ToList();
                if (users.Count > 0)
                {
                    plan.Interviewer = string.Join(",", users.ToArray());
                }

            }
            return temp;
        }

        public HR_InterviewEvaluationDTO GetTheData(string id)
        {
            HR_InterviewEvaluationDTO retValue = null;
            HR_InterviewEvaluation entity = GetEntity(id);
            if (entity != null)
            {
                retValue = _mapper.Map<HR_InterviewEvaluationDTO>(entity);
                HR_InterviewPlan plan = Db.GetIQueryable<HR_InterviewPlan>().FirstOrDefault(x => x.F_Id == entity.F_PlanId);
                if (plan != null)
                {
                    retValue.InterviewName = plan.F_InterviewName;
                }
                retValue.ApplicantName = Db.GetIQueryable<HR_Entry>().Where(x => x.F_Id == entity.F_ApplicantId).Select(s => s.NameUser).FirstOrDefault();
                var post = Db.GetIQueryable<Base_Post>().FirstOrDefault(i => i.F_Id == plan.F_PostId);
                var Repost = Db.GetIQueryable<HR_Recruit>().FirstOrDefault(i => i.F_Role == plan.F_PostId);
                if (Repost != null)
                {
                    retValue.PostName = Repost.F_RecruitName;
                }
                else
                {
                    if (post != null)
                    {
                        retValue.PostName = post.F_Name;
                    }
                }
                if (!string.IsNullOrWhiteSpace(retValue.F_InterviewerIdAll))
                {
                    List<string> list = new List<string>(retValue.F_InterviewerIdAll.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
                    var temp = Db.GetIQueryable<HR_FormalEmployees>().Where(i => list.Contains(i.F_Id)).Select(i => i.NameUser);
                    retValue.InterviewerName = string.Join(",", temp.ToArray());
                }
            }
            return retValue;
        }
        public async Task<HR_InterviewEvaluation> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        /// <summary>
        /// 根据面试流程id获取相关数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<InterViewEvaluationDTO> GetInterViewEvaluateInfo(string id)
        {
            InterViewEvaluationDTO interView = new InterViewEvaluationDTO();
            List<InterViewContent> interViewContents = new List<InterViewContent>();
            if (!string.IsNullOrEmpty(id))
            {
                //获取招聘关系表
                var recruitData = await this.Db.GetEntityAsync<HR_RecruitmentCandidates>(id);
                //用户关系表
                var userList = await Db.GetIQueryable<HR_FormalEmployees>().AsNoTracking().ToListAsync();
                //用户表
                var BaseUserList = Db.GetIQueryable<Base_User>().AsNoTracking().ToList();
                if (recruitData != null)
                {
                    //获取应聘者数据
                    var hR_Entry = this.Db.GetIQueryable<HR_Entry>().AsNoTracking().Where(i => i.F_Id == recruitData.F_UserId).Select(i => new HR_Entry
                    {
                        NameUser = i.NameUser,
                        F_Id = i.F_Id
                    }).FirstOrDefault();
                    interView.UserName = hR_Entry.NameUser;
                    //获取面试的岗位
                    var recruit = await this.Db.GetIQueryable<HR_Recruit>().AsNoTracking()
                                        .FirstOrDefaultAsync(i => i.F_Role == recruitData.F_PostId);
                    if (recruit != null)
                    {
                        interView.PostName = recruit.F_RecruitName;
                    }
                    //获取简历筛选综述
                    InterViewContent JLZZinterViewContent = new InterViewContent()
                    {
                        Type = (int)InterviewTypes.简历筛选综述,
                        BusState = recruitData.F_BusState != 2 ? (int)IsPassTypes.同意 : (int)IsPassTypes.不同意,
                        Content = recruitData.F_Cause,
                        EvaluateUser = recruitData.F_ModifyUserName,
                        EvaluateTime = recruitData.F_ModifyDate
                    };
                    interViewContents.Add(JLZZinterViewContent);
                    //获取面试计划
                    var plans = await Db.GetIQueryable<HR_InterviewPlan>().AsNoTracking()
                                    .Where(i => i.F_ApplicantId == recruitData.F_UserId && i.F_RecCanId == recruitData.F_Id)
                                    .ToListAsync();
                    var planIds = plans.Select(i => i.F_Id).ToList();
                    //获取面试流程
                    var temp = await this.Db.GetIQueryable<HR_InterviewEvaluation>()
                                        .Where(i => i.F_ApplicantId == recruitData.F_UserId && planIds.Contains(i.F_PlanId))
                                        .ToListAsync();
                    //查询
                    for (var index = 1; index <= 3; index++)
                    {
                        var toIndex = index + 1;
                        InterViewContent LCinterViewContent = new InterViewContent()
                        {
                            Type = toIndex
                        };
                        var PlanIds = plans.Where(i => i.F_RoundInterview.HasValue && i.F_RoundInterview.Value == index)
                            .Select(i => i.F_Id).ToList();
                        if (PlanIds.Count > 0)
                        {
                            var hR_Interviews = temp.Where(i => PlanIds.Contains(i.F_PlanId)).ToList();
                            if (hR_Interviews.Count > 0)
                            {
                                var NoInter = hR_Interviews.Where(i => i.F_IsThrough != 1).ToList();
                                if (NoInter.Count > 0)
                                {
                                    LCinterViewContent.BusState = (int)IsPassTypes.不同意;
                                }
                                else
                                {
                                    LCinterViewContent.BusState = (int)IsPassTypes.同意;
                                }
                                foreach (var item in hR_Interviews)
                                {
                                    var userListId = item.F_InterviewerIdAll.Split(new Char[] { ',', '，' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                                    var users = userList.FirstOrDefault(i => userListId.Contains(i.F_Id));
                                    var isPass = item.F_IsThrough.HasValue ? item.F_IsThrough == 1 ? "同意" : "不同意" : "";
                                    if (users != null)
                                    {
                                        if (item.F_ModifyUserId == users.BaseUserId)
                                        {
                                            item.InterviewerName = users.NameUser;
                                        }
                                        else
                                        {
                                            var base_User = BaseUserList.FirstOrDefault(i => i.Id == item.F_ModifyUserId);
                                            if (base_User != null)
                                            {
                                                item.InterviewerName = $"由{base_User.RealName}代替{users.NameUser}";
                                            }
                                            else
                                            {
                                                item.InterviewerName = users.NameUser;
                                            }
                                        }
                                    }


                                    LCinterViewContent.Content += item.InterviewerName+"("+ isPass +")"+ ":" + item.F_Evaluation + ";";
                                    LCinterViewContent.EvaluateUser += $"{item.InterviewerName}:({item.F_CreateDate.ToString("yyyy-MM-dd")});";
                                }
                                LCinterViewContent.Content = LCinterViewContent.Content.Substring(0, LCinterViewContent.Content.Length - 1);
                                LCinterViewContent.EvaluateUser = LCinterViewContent.EvaluateUser.Substring(0, LCinterViewContent.EvaluateUser.Length - 1);
                            }
                        }
                        else
                        {
                            LCinterViewContent.BusState = (int)IsPassTypes.其他;
                        }
                        interViewContents.Add(LCinterViewContent);
                    }
                }
                interView.interViewContents = interViewContents;
            }
            return interView;
        }
        public async Task AddDataAsync(HR_InterviewEvaluation data)
        {
            await InsertAsync(data);
        }
        /// <summary>
        /// 更新数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task UpdateDataAsync(HR_InterviewEvaluation data, IOperator op = null)
        {
            HR_InterviewProcess hR_InterviewProcess = null;
            var entity = new List<HR_InterviewProcess>();
            int count = 0;
            if (data.F_IsThrough != 0)
            {
                var isThoMs = false;
                data.F_BusState = 1;
                var dataData = DateTime.Now;
                var pInter = data.F_InterviewerId;
                var model = Db.GetIQueryable<HR_InterviewPlan>().FirstOrDefault(i => i.F_Id == data.F_PlanId);
                if (model != null)
                {
                    model.F_IsInterview = 1;
                    model.F_PassingTime = dataData;
                    //添加流程
                    //过滤sql注入
                    data.F_Id.FilterSql();
                    var sqlStr = "select * from [dbo].[HR_InterviewProcess] where F_PostId = (select F_PostId from  [dbo].[HR_InterviewProcess] where  F_Id=(select F_InterviewerId from  HR_InterviewEvaluation where F_Id='" + data.F_Id + "'))";
                    entity = Db.GetListBySql<HR_InterviewProcess>(sqlStr);
                    if (data.F_IsThrough == 1)
                    {
                        if (Db.GetIQueryable<HR_InterviewEvaluation>().Count(i => i.F_IsThrough != 1 && i.F_PlanId == data.F_PlanId && i.F_ApplicantId == data.F_ApplicantId && i.F_InterviewerId == data.F_InterviewerId && i.F_Id != data.F_Id) == 0)
                        {
                            model.F_IsPassingInter = data.F_IsThrough;
                            isThoMs = true;

                            while (true)
                            {
                                var proModel = entity.FirstOrDefault(i => i.F_Id == pInter);
                                if (proModel != null)
                                {
                                    if (count == 0)
                                    {
                                        hR_InterviewProcess = proModel;
                                    }
                                    count++;
                                    pInter = proModel.F_ParentId;
                                    if (string.IsNullOrEmpty(proModel.F_ParentId))
                                    {
                                        break;
                                    }
                                }
                                if (count >= 10)
                                {
                                    throw new Exception("流程获取异常，请检查一下流程配置");
                                }
                            }
                        }
                    }

                    var Emodel = Db.GetIQueryable<HR_RecruitmentCandidates>().FirstOrDefault(i => i.F_Id == model.F_RecCanId);
                    Emodel.F_ThroughTime = dataData;
                    if (data.F_IsThrough == 2)
                    {
                        model.F_IsPassingInter = data.F_IsThrough;
                        //添加标签
                        if (Emodel != null)
                        {
                            Emodel.F_Through = data.F_IsThrough;

                            //判断是否是最后一个节点判断
                            var InterId = data.F_InterviewerId;
                            if (!string.IsNullOrEmpty(InterId))
                            {
                                var interviewProcess = this.Db.GetEntity<HR_InterviewProcess>(InterId);
                                if (interviewProcess != null)
                                {
                                    var processes = this.Db.GetIQueryable<HR_InterviewProcess>()
                                                .Where(i => i.F_ParentId == interviewProcess.F_Id)
                                                .ToList();
                                    //保存标签
                                    HR_ContentOperationLog hR_ContentOperation = new HR_ContentOperationLog()
                                    {
                                        F_BusState = (int)ASKBusState.正常,
                                        F_UserId = Emodel.F_UserId,
                                        F_RelationalID = Emodel.F_Id,
                                        F_RelationalType = (int)RecruitType.RelationalType.正常
                                    };
                                    if (processes.Count == 0)
                                    {
                                        if (!Emodel.F_Label.Contains(EnumHelper.GetEnumDescription(RecruitType.TagType.最终面试不同意)))
                                        {
                                            Emodel.F_Label += EnumHelper.GetEnumDescription(RecruitType.TagType.最终面试不同意);
                                            hR_ContentOperation.F_TagContent = EnumHelper.GetEnumDescription(RecruitType.TagType.最终面试不同意);
                                            hR_ContentOperation.F_TagType = (int)RecruitType.TagType.最终面试不同意;
                                            hR_ContentOperation.F_Evaluation = data.F_Evaluation;
                                        }
                                    }
                                    else
                                    {
                                        if (!string.IsNullOrEmpty(Emodel.F_Label))
                                        {
                                            if (!Emodel.F_Label.Contains(EnumHelper.GetEnumDescription(RecruitType.TagType.面试官不同意)))
                                            {
                                                Emodel.F_Label += EnumHelper.GetEnumDescription(RecruitType.TagType.面试官不同意);
                                                hR_ContentOperation.F_TagContent = EnumHelper.GetEnumDescription(RecruitType.TagType.面试官不同意);
                                                hR_ContentOperation.F_TagType = (int)RecruitType.TagType.面试官不同意;
                                                hR_ContentOperation.F_Evaluation = data.F_Evaluation;
                                            }
                                        }
                                        else
                                        {
                                            Emodel.F_Label = EnumHelper.GetEnumDescription(RecruitType.TagType.面试官不同意);
                                            hR_ContentOperation.F_TagContent = EnumHelper.GetEnumDescription(RecruitType.TagType.面试官不同意);
                                            hR_ContentOperation.F_TagType = (int)RecruitType.TagType.面试官不同意;
                                            hR_ContentOperation.F_Evaluation = data.F_Evaluation;
                                        }
                                    }
                                    await _ContentOperationLogBusiness.SaveDataAsync(new List<HR_ContentOperationLog> { hR_ContentOperation });
                                }
                            }
                            Db.Update<HR_RecruitmentCandidates>(Emodel);
                        }
                    }
                    if (isThoMs)
                    {
                        var recPlan = Db.GetIQueryable<HR_InterviewProcess>().FirstOrDefault(i => i.F_ParentId == model.F_InterviewPId);
                        if (recPlan == null)
                        {
                            Emodel.F_Through = data.F_IsThrough;
                            Db.Update<HR_RecruitmentCandidates>(Emodel);
                        }
                    }
                    Db.Update<HR_InterviewPlan>(model);
                }
            }
            await UpdateAsync(data);
            if (data.F_IsThrough == 1 && count > 0 && hR_InterviewProcess != null)
            {
                if (count >= entity.Count)
                {
                    return;
                }
                var sqlStr = "select * from [dbo].[HR_InterviewPlan] where F_Id =(select F_PlanId from  HR_InterviewEvaluation where F_Id= '" + data.F_Id + "')";
                var planEntity = Db.GetListBySql<HR_InterviewPlan>(sqlStr);
                HR_InterviewPlan plan = new HR_InterviewPlan()
                {
                    F_ApplicantId = data.F_ApplicantId,
                    F_InterTime = DateTime.Now.AddDays(1).Date,
                    F_InterviewName = hR_InterviewProcess.F_InterviewName,
                    F_InterviewPId = hR_InterviewProcess.F_Id,
                    F_PostId = planEntity.FirstOrDefault()?.F_PostId,
                    F_RecCanId = planEntity.FirstOrDefault()?.F_RecCanId,
                    F_RoundInterview = count + 1,
                    F_IsPassingInter = 0,
                    F_PId = "",
                };
                InitEntity(plan, op);
                await _InterviewPlanBusiness.AddDataAsync(plan);
            }
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_InterviewEvaluation>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_InterviewEvaluation, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_InterviewEvaluation, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        /// <summary>
        /// 小程序端保存面试评价
        /// </summary>
        /// <param name="model">传入简历Id、面试官openid和手机号、面试评价、评分、是否通过</param>
        /// <returns></returns>
        public async Task SaveInterview(InterviewModel modelInter)
        {
            //更新或保存面试信息
            var data = Db.GetIQueryable<HR_InterviewEvaluation>().Where(i => i.F_Id == modelInter.InterviewId).FirstOrDefault();
            var userModel = Db.GetIQueryable<HR_FormalEmployees>().Where(i => i.MobilePhoneStr == modelInter.Phone).FirstOrDefault();
            if (data == null)
            {
                throw new Exception("找不到当前数据，请联系管理员");
            }

            if (modelInter.InterviewPass)
                data.F_IsThrough = 1;
            else
                data.F_IsThrough = 2;
            data.F_ModifyDate = DateTime.Now;
            data.F_ModifyUserId = userModel == null ? "" : userModel.NameUser;
            data.F_ModifyUserName = userModel == null ? "" : userModel.NameUser;
            data.F_Score = modelInter.InterviewScore;
            data.F_Evaluation = modelInter.InterviewText;
            // await UpdateDataAsync(data); 
            //if (data.F_IsThrough==1)
            //{
            //    var sqlStr = "select * from [dbo].[HR_InterviewPlan] where F_Id =(select F_PlanId from  HR_InterviewEvaluation where F_Id= '" + data.F_Id + "')";
            //    var planEntity = Db.GetListBySql<HR_InterviewPlan>(sqlStr);
            //    HR_InterviewPlan plan = new HR_InterviewPlan()
            //    {
            //        F_ApplicantId = data.F_ApplicantId,
            //        F_InterTime = DateTime.Now.AddDays(1).Date,
            //        F_InterviewName = hR_InterviewProcess.F_InterviewName,
            //        F_InterviewPId = hR_InterviewProcess.F_Id,
            //        F_PostId = planEntity.FirstOrDefault()?.F_PostId,
            //        F_RecCanId = planEntity.FirstOrDefault()?.F_RecCanId,
            //        F_RoundInterview = count + 1,
            //        F_IsPassingInter = 0,
            //        F_PId = "",
            //    };
            //    InitEntity(plan, op);
            //    HR_InterviewPlanBusiness _hR_InterviewPlanBus = new HR_InterviewPlanBusiness(Db, _mapper);
            //    await _hR_InterviewPlanBus.AddDataAsync(plan);

            //}


            HR_InterviewProcess hR_InterviewProcess = null;
            var entity = new List<HR_InterviewProcess>();
            int count = 0;
            //过滤sql注入
            data.F_Id.FilterSql();
            if (data.F_IsThrough != 0)
            {
                var isThoMs = false;
                data.F_BusState = 1;
                var dataData = DateTime.Now;
                var pInter = data.F_InterviewerId;
                var model = Db.GetIQueryable<HR_InterviewPlan>().FirstOrDefault(i => i.F_Id == data.F_PlanId);
                if (model != null)
                {
                    model.F_IsInterview = 1;
                    model.F_PassingTime = dataData;
                    if (data.F_IsThrough == 1)
                    {
                        if (Db.GetIQueryable<HR_InterviewEvaluation>().Count(i => i.F_IsThrough != 1 && i.F_PlanId == data.F_PlanId && i.F_ApplicantId == data.F_ApplicantId && i.F_InterviewerId == data.F_InterviewerId && i.F_Id != data.F_Id) == 0)
                        {
                            model.F_IsPassingInter = data.F_IsThrough;
                            isThoMs = true;

                            //添加流程
                            var sqlStr = "select * from [dbo].[HR_InterviewProcess] where F_PostId = (select F_PostId from  [dbo].[HR_InterviewProcess] where  F_Id=(select F_InterviewerId from  HR_InterviewEvaluation where F_Id='" + data.F_Id + "'))";
                            entity = Db.GetListBySql<HR_InterviewProcess>(sqlStr);
                            while (true)
                            {
                                var proModel = entity.FirstOrDefault(i => i.F_Id == pInter);
                                if (proModel != null)
                                {
                                    if (count == 0)
                                    {
                                        hR_InterviewProcess = proModel;
                                    }
                                    count++;
                                    pInter = proModel.F_ParentId;
                                    if (string.IsNullOrEmpty(proModel.F_ParentId))
                                    {
                                        break;
                                    }
                                }
                                if (count >= 10)
                                {
                                    throw new Exception("流程获取异常，请检查一下流程配置");
                                }
                            }
                        }
                    }

                    var Emodel = Db.GetIQueryable<HR_RecruitmentCandidates>().FirstOrDefault(i => i.F_Id == model.F_RecCanId);
                    Emodel.F_ThroughTime = dataData;
                    if (data.F_IsThrough == 2)
                    {
                        model.F_IsPassingInter = data.F_IsThrough;
                        if (Emodel != null)
                        {
                            Emodel.F_Through = data.F_IsThrough;

                            //判断是否是最后一个节点判断
                            var InterId = data.F_InterviewerId;
                            if (!string.IsNullOrEmpty(InterId))
                            {
                                var interviewProcess = this.Db.GetEntity<HR_InterviewProcess>(InterId);
                                if (interviewProcess != null)
                                {
                                    var processes = this.Db.GetIQueryable<HR_InterviewProcess>()
                                                .Where(i => i.F_ParentId == interviewProcess.F_Id)
                                                .ToList();
                                    //保存标签
                                    HR_ContentOperationLog hR_ContentOperation = new HR_ContentOperationLog()
                                    {
                                        F_BusState = (int)ASKBusState.正常,
                                        F_UserId = Emodel.F_UserId,
                                        F_RelationalID = Emodel.F_Id,
                                        F_RelationalType = (int)RecruitType.RelationalType.正常
                                    };
                                    if (processes.Count == 0)
                                    {
                                        if (!Emodel.F_Label.Contains(EnumHelper.GetEnumDescription(RecruitType.TagType.最终面试不同意)))
                                        {
                                            Emodel.F_Label += EnumHelper.GetEnumDescription(RecruitType.TagType.最终面试不同意);
                                            hR_ContentOperation.F_TagContent = EnumHelper.GetEnumDescription(RecruitType.TagType.最终面试不同意);
                                            hR_ContentOperation.F_TagType = (int)RecruitType.TagType.最终面试不同意;
                                            hR_ContentOperation.F_Evaluation = data.F_Evaluation;
                                        }
                                    }
                                    else
                                    {
                                        if (!string.IsNullOrEmpty(Emodel.F_Label))
                                        {
                                            if (!Emodel.F_Label.Contains(EnumHelper.GetEnumDescription(RecruitType.TagType.面试官不同意)))
                                            {
                                                Emodel.F_Label += EnumHelper.GetEnumDescription(RecruitType.TagType.面试官不同意);
                                                hR_ContentOperation.F_TagContent = EnumHelper.GetEnumDescription(RecruitType.TagType.面试官不同意);
                                                hR_ContentOperation.F_TagType = (int)RecruitType.TagType.面试官不同意;
                                                hR_ContentOperation.F_Evaluation = data.F_Evaluation;
                                            }
                                        }
                                        else
                                        {
                                            Emodel.F_Label = EnumHelper.GetEnumDescription(RecruitType.TagType.面试官不同意);
                                            hR_ContentOperation.F_TagContent = EnumHelper.GetEnumDescription(RecruitType.TagType.面试官不同意);
                                            hR_ContentOperation.F_TagType = (int)RecruitType.TagType.面试官不同意;
                                            hR_ContentOperation.F_Evaluation = data.F_Evaluation;
                                        }
                                    }
                                    await _ContentOperationLogBusiness.SaveDataAsync(new List<HR_ContentOperationLog> { hR_ContentOperation });
                                }
                            }
                            Db.Update<HR_RecruitmentCandidates>(Emodel);
                        }
                    }
                    if (isThoMs)
                    {
                        var recPlan = Db.GetIQueryable<HR_InterviewProcess>().FirstOrDefault(i => i.F_ParentId == model.F_InterviewPId);
                        if (recPlan == null)
                        {
                            Emodel.F_Through = data.F_IsThrough;
                            Db.Update<HR_RecruitmentCandidates>(Emodel);
                        }
                    }
                    Db.Update<HR_InterviewPlan>(model);
                }
            }
            await UpdateAsync(data);
            if (data.F_IsThrough == 1 && count > 0 && hR_InterviewProcess != null)
            {
                if (count >= entity.Count)
                {
                    return;
                }
                var sqlStr = "select * from [dbo].[HR_InterviewPlan] where F_Id =(select F_PlanId from  HR_InterviewEvaluation where F_Id= '" + data.F_Id + "')";
                var planEntity = Db.GetListBySql<HR_InterviewPlan>(sqlStr);
                HR_InterviewPlan plan = new HR_InterviewPlan()
                {
                    F_ApplicantId = data.F_ApplicantId,
                    F_InterTime = DateTime.Now.AddDays(1).Date,
                    F_InterviewName = hR_InterviewProcess.F_InterviewName,
                    F_InterviewPId = hR_InterviewProcess.F_Id,
                    F_PostId = planEntity.FirstOrDefault()?.F_PostId,
                    F_RecCanId = planEntity.FirstOrDefault()?.F_RecCanId,
                    F_RoundInterview = count + 1,
                    F_IsPassingInter = 0,
                    F_PId = "",
                };
                InitEntity(plan, null);
                await _InterviewPlanBusiness.AddDataAsync(plan);
            }

        }
        /// <summary>
        /// 获取面试信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public List<HR_InterviewEvaluation> EvaluationList(InputInteEvaModel model)
        {

            //model.Phone = "***********";
            IQueryable<HR_InterviewEvaluation> data = null;
            if (model.Phone == "***********" || model.Phone == "***********" || model.Phone == "***********" || model.Phone == "***********")
            {
                data = Db.GetIQueryable<HR_InterviewEvaluation>().Where(i => (i.F_IsDelete == 0 || !i.F_IsDelete.HasValue));
            }
            else
            {
                var entity = Db.GetIQueryable<HR_FormalEmployees>().Where(i => i.MobilePhoneStr == model.Phone).FirstOrDefault();
                if (entity == null)
                {
                    return null;
                }
                data = Db.GetIQueryable<HR_InterviewEvaluation>().Where(i => i.F_InterviewerIdAll.Contains(entity.F_Id) && (i.F_IsDelete == 0 || !i.F_IsDelete.HasValue));
            }
            //data = data.Where(f => f.F_ApplicantId == "039FB53A6C149-4A30873B-D8F8-470A-96CB-2267C2C8D984");
            var tempx = (from a in data
                         join b in Db.GetIQueryable<HR_InterviewPlan>().Where(i => i.F_IsDelete == 0) on a.F_PlanId equals b.F_Id into acInfo
                         from ac in acInfo.DefaultIfEmpty()
                         join re in Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => i.F_IsDelete == 0) on ac.F_RecCanId equals re.F_Id
                         select new
                         {
                             a.F_Id,
                             a.F_ApplicantId,
                             a.F_BusState,
                             a.F_Evaluation,
                             a.F_InterviewerId,
                             a.F_InterviewerIdAll,
                             a.F_IsThrough,
                             a.F_PlanId,
                             a.F_Remark,
                             a.F_Score,
                             ac.F_RoundInterview,
                         }).ToList();
            var q = (from d in tempx
                     group d by d.F_ApplicantId into g
                     select new
                     {
                         F_RoundInterview = g.Max(x => x.F_RoundInterview),
                         ApplicantId = g.Key,
                     }).ToList();
            var xtemp = (from a in tempx
                         join aq in q on a.F_ApplicantId equals aq.ApplicantId
                         where aq.F_RoundInterview == a.F_RoundInterview
                         select new
                         {
                             a.F_Id,
                             a.F_ApplicantId,
                             a.F_BusState,
                             a.F_Evaluation,
                             a.F_InterviewerId,
                             a.F_InterviewerIdAll,
                             a.F_IsThrough,
                             a.F_PlanId,
                             a.F_Remark,
                             a.F_Score,
                             aq.F_RoundInterview,
                         }).ToList();
            var ids = xtemp.Select(i => i.F_Id).ToList();
            data = data.Where(i => ids.Contains(i.F_Id));
            switch (model.Type)
            {
                case 0://全部数据
                    break;
                case 1:// 未评价
                    data = data.Where(i => i.F_IsThrough == 0);
                    break;
                case 2://通过
                    data = data.Where(i => i.F_IsThrough == 1);
                    break;
                case 3://未通过
                    data = data.Where(i => i.F_IsThrough == 2);
                    break;
                default:
                    break;
            }
            data = from a in data
                   join b in Db.GetIQueryable<HR_Entry>() on a.F_ApplicantId equals b.F_Id
                   join ac in Db.GetIQueryable<HR_InterviewPlan>().Where(i => i.F_IsDelete == 0 && i.F_IsGiveUpInterview == 0) on a.F_PlanId equals ac.F_Id
                   //into acInfo
                   //from ac in acInfo.DefaultIfEmpty()
                   join e in Db.GetIQueryable<HR_InterviewProcess>() on a.F_InterviewerId equals e.F_Id into aeInfo
                   from ae in aeInfo.DefaultIfEmpty()
                   join f in Db.GetIQueryable<HR_Recruit>() on ac.F_PostId equals f.F_Role into acfInfo
                   from acf in acfInfo.DefaultIfEmpty()
                   select new HR_InterviewEvaluation()
                   {
                       F_Id = a.F_Id,
                       ApplicantName = b.NameUser,
                       F_ApplicantId = a.F_ApplicantId,
                       F_BusState = a.F_BusState,
                       F_Evaluation = a.F_Evaluation,
                       F_InterviewerId = a.F_InterviewerId,
                       F_InterviewerIdAll = a.F_InterviewerIdAll,
                       F_IsThrough = a.F_IsThrough,
                       F_PlanId = a.F_PlanId,
                       F_Remark = a.F_Remark,
                       F_Score = a.F_Score,
                       PostName = acf == null ? "" : acf.F_RecruitName,
                       InterName = ae == null ? "" : ae.F_InterviewName,
                       PlanName = ac == null ? "" : ac.F_InterviewName,
                       Phone = b.MobilePhoneStr,
                       HeadPortrait = b.HeadPortrait,
                       F_CreateDate = a.F_CreateDate,
                       //F_FileBase64Img = !string.IsNullOrEmpty(b.HeadPortrait) ? "" :ImgHelper.SaveBase64Image(b.F_FileBase64Img,b.F_Id),
                       PostId = ac == null ? "" : ac.F_PostId,
                   };
            if (!string.IsNullOrEmpty(model.Name))
            {
                data = data.Where(i => i.ApplicantName.Contains(model.Name));
            }
            //去除放弃面试人员
            var plnaIds = data.Select(s => s.F_PlanId).ToList();
            var planListId = from a in Db.GetIQueryable<HR_InterviewPlan>().Where(i => plnaIds.Contains(i.F_Id))
                             join b in Db.GetIQueryable<HR_RecruitmentCandidates>().Where(i => i.F_IsGiveUpInterview == 0) on a.F_RecCanId equals b.F_Id
                             select new
                             {
                                 Id = a.F_Id
                             };
            var idList = planListId.Select(i => i.Id).Distinct().ToList();
            if (idList.Count() > 0)
            {
                data = data.Where(i => idList.Contains(i.F_PlanId));
            }
            //获取面试官 
            var temp = data.ToList();
            var applicantIds = temp.Select(i => i.F_ApplicantId).ToList();
            //获取教育信息
            var eduEntity = Db.GetIQueryable<HR_RecruitEduBack>().Where(i => applicantIds.Contains(i.UserId)).ToList();
            //获取工作信息
            var workEntity = Db.GetIQueryable<HR_RecruitWorkExpe>().Where(i => applicantIds.Contains(i.F_UserId)).ToList();
            List<String> userIds = new List<string>();
            foreach (var item in temp)
            {
                //如果头像为空
                if (string.IsNullOrEmpty(item.HeadPortrait))
                {
                    //则保存并重新赋值
                    item.HeadPortrait = SaveBase64Image(item.F_FileBase64Img, item.F_Id);
                    Db.Update<HR_Entry>(u => u.F_Id == item.F_ApplicantId, u => u.HeadPortrait = item.HeadPortrait);
                }

                //item.HeadPortrait = string.IsNullOrEmpty(item.HeadPortrait) ? "data:image/png;base64," + item.F_FileBase64Img : item.HeadPortrait;
                //item.F_FileBase64Img = "";

                var listIds = item.F_InterviewerIdAll.Split(new Char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                userIds.AddRange(listIds);
            }
            userIds = userIds.Distinct().ToList();
            var userList = Db.GetIQueryable<HR_FormalEmployees>().Where(i => userIds.Contains(i.F_Id)).ToList();
            foreach (var plan in temp)
            {
                var listIds = plan.F_InterviewerIdAll.Split(new Char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                var users = userList.Where(i => listIds.Contains(i.F_Id)).Select(s => s.NameUser).ToList();
                if (users.Count > 0)
                {
                    plan.InterviewerName = string.Join(",", users.ToArray());
                }
                plan.hR_RecruitEdu = eduEntity.Where(i => i.UserId == plan.F_ApplicantId).OrderByDescending(i => i.F_EndDate)?.FirstOrDefault();
                var workList = workEntity.Where(i => i.F_UserId == plan.F_ApplicantId).ToList();
                if (workList.Count > 0)
                {
                    var zdate = workList.Min(i => i.F_StartTime);
                    if (zdate.HasValue)
                    {
                        plan.WorkYear = DateTime.Now.Year - zdate.Value.Year;
                    }
                    var workD = workList.Select(i => i.F_CompanyName).ToList();
                    plan.WorkCompany = string.Join(",", workD.ToArray());
                }

            }
            return temp.OrderByDescending(i => i.F_CreateDate).ToList();
        }

        /// <summary>
        /// 将base64编码的字符串转为图片并保存
        /// </summary>
        /// <param name="source"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        protected string SaveBase64Image(string source, string id)
        {
            try
            {
                var now = DateTime.Now;
                string filePath = Directory.GetCurrentDirectory() + "/wwwroot/UploadFile/" + DateTime.Now.ToString("yyyyMMdd") + "/";
                string fileName = id + ".png";

                string strbase64 = "";
                //string strbase64 = source.Substring(source.IndexOf(',') + 1);
                //strbase64 = strbase64.Trim('\0');
                //Log.Debug("strbase64:" + strbase64);
                byte[] arr = Convert.FromBase64String(strbase64);
                using (MemoryStream ms = new MemoryStream(arr))
                {
                    Bitmap bmp = new Bitmap(ms);
                    if (!Directory.Exists(filePath))
                    {
                        Directory.CreateDirectory(filePath);
                    }
                    //if (!Directory.Exists(filePath))
                    //    Log.Debug("没有Directory");
                    //Directory.CreateDirectory(filePath);
                    //新建第二个bitmap类型的bmp2变量。
                    Bitmap bmp2 = new Bitmap(bmp, bmp.Width, bmp.Height);
                    //将第一个bmp拷贝到bmp2中
                    Graphics draw = Graphics.FromImage(bmp2);
                    draw.DrawImage(bmp, 0, 0);
                    draw.Dispose();

                    bmp2.Save(filePath + fileName, System.Drawing.Imaging.ImageFormat.Png);

                    ms.Close();
                    return "https://hrapi.cqlandmark.com/UploadFile/" + DateTime.Now.ToString("yyyyMMdd") + "/" + fileName;
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        #endregion

        #region 私有成员

        #endregion
    }
}