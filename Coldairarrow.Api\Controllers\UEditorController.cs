﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Coldairarrow.Util.UEditor;
using System.IO;
using Coldairarrow.Util.UEditor.Handlers;

namespace Coldairarrow.Api.Controllers
{
    [Route("/[controller]/[action]")]
    public class UEditorController : Controller
    {
        private readonly UEditorService _ueditorService;
        private readonly UploadHandler upload;
        public UEditorController(UEditorService ueditorService)
        {
            this._ueditorService = ueditorService;
            upload = new UploadHandler(HttpContext, new UploadConfig
            {
                AllowExtensions = Config.GetStringList("imageAllowFiles"),
                PathFormat = Config.GetString("imagePathFormat"),
                SizeLimit = Config.GetInt("imageMaxSize"),
                UploadFieldName = Config.GetString("imageFieldName")
            });
        }

        [HttpGet, HttpPost]
        public ContentResult Upload()
        {
            var response = _ueditorService.UploadAndGetResponse(HttpContext);
            return Content(response.Result, response.ContentType);
        }

        [HttpPost]
        public string Post()
        {
            var files = Request.Form.Files;
            if (files.Count == 0)
            {
                return "No File";
            }
            var file = files[0];
            var result = upload.Process(file);
            return result.Url;
        }
    }
}