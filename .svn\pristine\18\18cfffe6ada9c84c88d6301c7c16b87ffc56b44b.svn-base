﻿using Coldairarrow.Entity.Wechat_Go;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.Wechat_Go
{
    public interface IGo_NoticeBusiness
    {
        Task<PageResult<Go_Notice>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<Go_Notice> GetTheDataAsync(string id);
        Task AddDataAsync(Go_Notice data);
        Task UpdateDataAsync(Go_Notice data);
        Task DeleteDataAsync(List<string> ids);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);

        List<Go_Notice> getDataByTeamId(string id, bool index);
    }
}