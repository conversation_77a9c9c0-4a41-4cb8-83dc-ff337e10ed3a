﻿using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HR_EmployeeInfoManage
{
    public class HR_PerformanceInfoBusiness : BaseBusiness<HR_PerformanceInfo>, IHR_PerformanceInfoBusiness, ITransientDependency
    {
        public HR_PerformanceInfoBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_PerformanceInfoDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_PerformanceInfo, HR_FormalEmployees, HR_PerformanceInfoDTO>> select = (p, e) => new HR_PerformanceInfoDTO
            {
                EmpCode = e.EmployeesCode,
                EmpName = e.NameUser,
            };
            select = select.BuildExtendSelectExpre();
            var q = from p in this.Db.GetIQueryable<HR_PerformanceInfo>().AsExpandable()
                    join e in this.Db.GetIQueryable<HR_FormalEmployees>() on p.UserId equals e.F_Id into emp
                    from e in emp.DefaultIfEmpty()
                    select @select.Invoke(p, e);

            var where = LinqHelper.True<HR_PerformanceInfoDTO>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_PerformanceInfoDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            where = where.AndIf(!search.EmpId.IsNullOrEmpty(), x => x.UserId == search.EmpId);

            return await q.Where(where).GetPageResultAsync(input);
        }
        public async Task<List<HR_PerformanceInfo>> GetDataListByUserIdAsync(string userId)
        {
            return await GetIQueryable().Where(x => x.UserId == userId).ToListAsync();
        }
        public async Task<HR_PerformanceInfo> GetTheDataAsync(string id)
        {
            var hR_Performance = await GetEntityAsync(id);
            if (hR_Performance != null && !string.IsNullOrWhiteSpace(hR_Performance.UserId))
            {
                //查询用户的信息
                var formalEmployees = await this.Db.GetEntityAsync<HR_FormalEmployees>(hR_Performance.UserId);
                if (formalEmployees != null)
                {
                    hR_Performance.EmpCode = formalEmployees.EmployeesCode;
                    hR_Performance.EmpName = formalEmployees.NameUser;
                }
            }
            return hR_Performance;
        }

        public async Task AddDataListAsync(List<HR_PerformanceInfo> data)
        {
            await InsertAsync(data);
        }
        public async Task AddDataAsync(HR_PerformanceInfo data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_PerformanceInfo data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        /// <summary>
        /// 导入并保存数据
        /// </summary>
        /// <param name="physicPath">物理路径</param>
        /// <returns></returns>
        public AjaxResult<DataTable> ImportSaveData(string physicPath, IOperator op)
        {
            AjaxResult<DataTable> ajaxResult = new AjaxResult<DataTable>();

            DataTable dt = ExcelHelper.ExcelImport(physicPath);
            if (dt == null || dt.Rows.Count == 0)
            {
                ajaxResult.Success = false;
                ajaxResult.Msg = "上传数据错误或不能为空";
            }
            else
            {
                dt.Columns.Add("导入错误", typeof(string));
                bool isError = false;
                List<HR_PerformanceInfo> records = new List<HR_PerformanceInfo>();
                foreach (DataRow dr in dt.Rows)
                {
                    if (dr["姓名"] != null && !dr["姓名"].ToString().IsNullOrEmpty())
                    {
                        HR_PerformanceInfo model = new HR_PerformanceInfo();

                        var emp = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.NameUser == dr["姓名"].ToString());
                        if (emp == null)
                        {
                            dr["导入错误"] += "员工不存在;";
                            isError = true;
                        }
                        else
                        {
                            model.UserId = emp.F_Id;
                        }
                        if (dr["绩效考核年份"] != null)
                        {
                            int year;
                            if (int.TryParse(dr["绩效考核年份"].ToString(), out year))
                            {
                                model.PerformanceAppYear = year;
                            }
                            else
                            {
                                dr["导入错误"] += "绩效考核年份应为数字;";
                                isError = true;
                            }
                        }
                        if (dr["绩效等级"] != null)
                        {
                            if (dr["绩效等级"].ToString().Length < 51)
                            {
                                model.PerformanceRating = dr["绩效等级"]?.ToString();
                            }
                            else
                            {
                                dr["导入错误"] += "绩效等级长度不能大于50个字符;";
                                isError = true;
                            }
                        }
                        if (dr["备注"] != null)
                        {
                            if (dr["备注"].ToString().Length < 1001)
                            {
                                model.Remark = dr["备注"]?.ToString();
                            }
                            else
                            {
                                dr["导入错误"] += "备注长度不能大于1000个字符;";
                                isError = true;
                            }
                        }
                        if (!isError)
                        {
                            model.F_BusState = 1;
                            model.F_Id = GuidHelper.GenerateKey();
                            model.F_CreateDate = DateTime.Now;
                            model.F_CreateUserId = op.UserId;
                            model.F_CreateUserName = op.RealName;

                            records.Add(model);
                        }
                    }
                    else
                    {
                        dr["导入错误"] += "员工编码和姓名不能为空;";
                        isError = true;
                    }
                }
                if (isError)
                {
                    ajaxResult.Data = dt;
                    ajaxResult.Success = false;
                    ajaxResult.Msg = "导入失败";

                    return ajaxResult;
                }
                else
                {
                    Insert(records);
                    ajaxResult.Success = true;
                    ajaxResult.Msg = "导入成功";
                    ajaxResult.ErrorCode = 1;
                }
            }
            return ajaxResult;
        }

        #endregion

        #region 私有成员

        #endregion
    }
}