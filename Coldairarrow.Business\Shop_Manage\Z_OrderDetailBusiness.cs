﻿using Coldairarrow.Entity.Shop_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using static Coldairarrow.Entity.Shop_Manage.Model.AliJDResultModel;
using System;
using org.apache.zookeeper.data;
using Coldairarrow.Entity.Shop_Manage.Enum;
using static Coldairarrow.Entity.Shop_Manage.Enum.ShopEnum;
using Coldairarrow.Entity.HR_Manage;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc;
using Dynamitey.DynamicObjects;
using Coldairarrow.Util.UEditor;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using NPOI.SS.Formula.Functions;
using Coldairarrow.Entity.Base_Manage;
using System.Drawing;
using Coldairarrow.IBusiness;
using NPOI.XWPF.UserModel;
using static Coldairarrow.Entity.Shop_Manage.Model.JDResultModel;
using Coldairarrow.Util.Helper;
using RTools_NTS.Util;
using Coldairarrow.Business.Base_Business;
using Newtonsoft.Json.Linq;

namespace Coldairarrow.Business.Shop_Manage
{
    public class Z_OrderDetailBusiness : BaseBusiness<Z_OrderDetail>, IZ_OrderDetailBusiness, ITransientDependency
    {
        private IHR_FormalEmployeesBusiness _hR_FormalEmployeesBusiness;
        public Z_OrderDetailBusiness(IDbAccessor db, IOperator @operator, IHR_FormalEmployeesBusiness hR_FormalEmployeesBusiness)
            : base(db)
        {
            _operator = @operator;
            _hR_FormalEmployeesBusiness = hR_FormalEmployeesBusiness;

        }
        private readonly IOperator _operator;
        #region 外部接口

        public async Task<PageResult<Z_OrderDetail>> GetDataListAsync(PageInput<ProductConditinDto> input)
        {
            var search = input.Search;
            var baseQuery = GetIQueryable();
            var shopCartList = await GetShopCartPageList();

            // 合并数据源
            var combinedData = shopCartList.Concat(await baseQuery.ToListAsync()).ToList();

            // 构建查询条件
            var predicate = LinqHelper.True<Z_OrderDetail>();
            if (!string.IsNullOrEmpty(search.Condition) && !string.IsNullOrEmpty(search.Keyword))
            {
                var dynamicPredicate = DynamicExpressionParser.ParseLambda<Z_OrderDetail, bool>(
                    ParsingConfig.Default, false, $"{search.Condition}.Contains(@0)", search.Keyword);
                predicate = predicate.And(dynamicPredicate);
            }
            if (search.state.HasValue && search.state != 0)
                predicate = predicate.And(x => x.F_State == (OrderType)search.state.Value);
            if (!string.IsNullOrWhiteSpace(search.userId))
                predicate = predicate.And(x => x.F_UserId == search.userId.Trim());
            if (!string.IsNullOrWhiteSpace(search.userName))
                predicate = predicate.And(x => x.F_UserName.Contains(search.userName.Trim()));
            if (!string.IsNullOrWhiteSpace(search.productId))
                predicate = predicate.And(x => x.F_ProductId == search.productId);
            if (search.isPay.HasValue)
                predicate = predicate.And(x => x.F_State != OrderType.待支付 && x.F_State != OrderType.已取消);
            if (!string.IsNullOrWhiteSpace(search.address))
                predicate = predicate.And(x => x.F_Address == search.address);

            // 应用过滤条件
            var filteredData = combinedData.Where(predicate.Compile()).ToList();
            if (filteredData.Count == 0)
                return new PageResult<Z_OrderDetail> { Data = new List<Z_OrderDetail>(), Total = 0 };

            // 顺序加载关联数据
            var productIds = filteredData
                .Where(x => x.F_Type == ShopType.商城)
                .Select(x => x.F_ProductId)
                .Distinct()
                .ToList();

            List<Z_Products> products = new List<Z_Products>();
            if (productIds.Any())
            {
                products = await Db.GetIQueryable<Z_Products>()
                    .Where(x => productIds.Contains(x.F_Id))
                    .AsNoTracking()
                    .ToListAsync();
            }

            var userIds = filteredData.Select(x => x.F_UserName).Distinct().ToList();

            var feeUsers = await Db.GetIQueryable<HR_FeeUserInfo>()
                .Where(x => userIds.Contains(x.F_UserName))
                .Select(x => new { x.F_UserName, x.F_DeptName, x.F_Company })
                .ToListAsync();

            var employees = await Db.GetIQueryable<HR_FormalEmployees>()
                .Where(x => !x.EmployRelStatus.Contains("离职") && userIds.Contains(x.NameUser))
                .AsNoTracking()
                .ToListAsync();

            // 创建内存查找字典
            var productDict = products.ToDictionary(x => x.F_Id);
            var feeUserDict = feeUsers.GroupBy(x => x.F_UserName)
                                    .ToDictionary(g => g.Key, g => g.First());
            var employeeDict = employees.ToDictionary(x => x.NameUser);


            // 二次过滤
            if (!string.IsNullOrWhiteSpace(search.productName))
            {
                filteredData = filteredData
                    .Where(x => x.ProductDetail?.F_Name?.Contains(search.productName) == true)
                    .ToList();
            }

            // 内存排序分页
            var orderedData = filteredData.AsQueryable()
                .OrderBy($"{input.SortField} {input.SortType}")
                .ToList();

            var pagedData = orderedData
                .Skip((input.PageIndex - 1) * input.PageRows)
                .Take(input.PageRows)
                .ToList();
            if (pagedData.Count > 0)
            {
                // 同步处理数据
                foreach (var item in pagedData)
                {
                    // 费用用户信息
                    if (feeUserDict.TryGetValue(item.F_UserName, out var feeUser))
                    {
                        item.F_DepName = feeUser.F_DeptName;
                        item.F_ComPanyName = feeUser.F_Company;
                    }

                    // 商品详情
                    if (item.F_Type == ShopType.商城 && productDict.TryGetValue(item.F_ProductId, out var product))
                        item.ProductDetail = product;

                    // 正式员工信息（确保使用同步方法）
                    if (employeeDict.TryGetValue(item.F_UserName, out var employee))
                    {
                        // 假设GetProjectDept已改造为同步方法且内部使用独立DbContext
                        var projectDept = _hR_FormalEmployeesBusiness.GetProjectDept(employee.F_Id);
                        item.F_DepName = projectDept?.dept;
                    }
                }
            }
            return new PageResult<Z_OrderDetail>
            {
                Data = pagedData,
                Total = filteredData.Count
            };
        }

        public async Task<PageResult<Z_OrderDetail>> GetDataListByAdressAsync(PageInput<ProductConditinDto> input)
        {
            var search = input.Search;
            var q = GetIQueryable();
            var shopCartList = await GetShopCartPageList();
            shopCartList.AddRange(q);
            var where = LinqHelper.True<Z_OrderDetail>();


            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Z_OrderDetail, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            //判断状态
            if (search.state.HasValue && search.state != 0)
            {
                where = where.And(x => x.F_State == (OrderType)search.state.Value);
            }
            //判断状态
            if (!search.userId.IsNullOrWhiteSpace())
            {
                where = where.And(x => x.F_UserId == search.userId.Trim());
            }
            //判断状态
            if (!search.userName.IsNullOrWhiteSpace())
            {
                where = where.And(x => !x.F_UserName.IsNullOrWhiteSpace() && x.F_UserName.Contains(search.userName.Trim()));
            }
            //判断商品id 
            if (!search.productId.IsNullOrWhiteSpace())
            {
                where = where.And(x => !x.F_ProductId.IsNullOrWhiteSpace() && x.F_ProductId == search.productId);
            }
            //判断商品id 
            if (search.isPay.HasValue)
            {
                where = where.And(x => x.F_State != OrderType.待支付 && x.F_State != OrderType.已取消);
            }
            //收货地址 
            if (!search.address.IsNullOrWhiteSpace())
            {
                where = where.And(x => x.F_Address == search.address);
            }
            var resultData = shopCartList.Where(where.Compile());
            if (resultData != null && resultData.Count() > 0)
            {

                //查询所有的商品数据
                var pId = resultData.Where(x => x.F_Type == ShopType.商城).Select(x => x.F_ProductId).ToList();
                if (pId.Count > 0)
                {
                    var queryProducts = this.Db.GetIQueryable<Z_Products>().AsNoTracking().Where(x => pId.Contains(x.F_Id)).ToList();
                    resultData.ForEach(item =>
                    {
                        if (item.F_Type == ShopType.商城)
                        {
                            item.ProductDetail = queryProducts.FirstOrDefault(x => x.F_Id == item.F_ProductId);
                        }
                    });
                    if (!string.IsNullOrWhiteSpace(search.productName))
                    {
                        resultData = resultData.Where(x => x.ProductDetail != null && x.ProductDetail.F_Name.Contains(search.productName));
                    }
                }
                var list = resultData.AsQueryable()
                .OrderBy($@"{input.SortField} {input.SortType}")
                .GroupBy(x => new { x.F_Address, x.ProductDetail.F_Name, x.F_Price, x.ProductDetail.F_Code })
                .Select((item) => new Z_OrderDetail
                {
                    F_Address = item.Key.F_Address,
                    F_ProductName = item.Key.F_Name,
                    F_ProdNum = item.Sum(x => x.F_ProdNum),
                    F_ProductCode = item.Key.F_Code,
                    F_Price = item.Key.F_Price,
                    F_UseNumber = item.Sum(x => x.F_UseNumber)
                })
                .Skip((input.PageIndex - 1) * input.PageRows)
                .Take(input.PageRows)
                .ToList();
                return new PageResult<Z_OrderDetail> { Data = list, Total = resultData.Count() };
            }
            return new PageResult<Z_OrderDetail> { Data = new List<Z_OrderDetail>(), Total = 0 };
        }

        public async Task<PageResult<Z_OrderDetail>> GetDataListByDepAsync(PageInput<ProductConditinDto> input)
        {
            var search = input.Search;
            var q = GetIQueryable();
            var shopCartList = await GetShopCartPageList();
            shopCartList.AddRange(q);
            var where = LinqHelper.True<Z_OrderDetail>();


            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Z_OrderDetail, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            //判断状态
            if (search.state.HasValue && search.state != 0)
            {
                where = where.And(x => x.F_State == (OrderType)search.state.Value);
            }
            //判断状态
            if (!search.userId.IsNullOrWhiteSpace())
            {
                where = where.And(x => x.F_UserId == search.userId.Trim());
            }
            //判断状态
            if (!search.userName.IsNullOrWhiteSpace())
            {
                where = where.And(x => !x.F_UserName.IsNullOrWhiteSpace() && x.F_UserName.Contains(search.userName.Trim()));
            }
            //判断商品id 
            if (!search.productId.IsNullOrWhiteSpace())
            {
                where = where.And(x => !x.F_ProductId.IsNullOrWhiteSpace() && x.F_ProductId == search.productId);
            }
            //判断商品id 
            if (search.isPay.HasValue)
            {
                where = where.And(x => x.F_State != OrderType.待支付 && x.F_State != OrderType.已取消);
            }
            //收货地址 
            if (!search.address.IsNullOrWhiteSpace())
            {
                where = where.And(x => x.F_Address == search.address);
            }
            var resultData = shopCartList.Where(where.Compile());
            if (resultData != null && resultData.Count() > 0)
            {

                //查询所有的商品数据
                var pId = resultData.Where(x => x.F_Type == ShopType.商城).Select(x => x.F_ProductId).ToList();
                var queryProducts = this.Db.GetIQueryable<Z_Products>().AsNoTracking().Where(x => pId.Contains(x.F_Id)).ToList();
                var formalEmployees = this.Db.GetIQueryable<HR_FormalEmployees>().AsNoTracking().Where(x => !x.EmployRelStatus.Contains("离职") && resultData.Select(item => item.F_UserName).Contains(x.NameUser)).ToList();
                var feeUserInfos = this.Db.GetIQueryable<HR_FeeUserInfo>();
                if (pId.Count > 0)
                {
                    resultData.ForEach(item =>
                    {
                        item.F_DepName = feeUserInfos.FirstOrDefault(x => x.F_UserName == item.F_UserName)?.F_DeptName;
                        item.F_ComPanyName = feeUserInfos.FirstOrDefault(x => x.F_UserName == item.F_UserName)?.F_Company;
                        if (item.F_Type == ShopType.商城)
                        {
                            item.ProductDetail = queryProducts.FirstOrDefault(x => x.F_Id == item.F_ProductId);
                        }
                    });
                    if (!string.IsNullOrWhiteSpace(search.productName))
                    {
                        resultData = resultData.Where(x => x.ProductDetail != null && x.ProductDetail.F_Name.Contains(search.productName));
                    }
                }
                var list = resultData.AsQueryable()
                .OrderBy($@"{input.SortField} {input.SortType}")
                .GroupBy(x => new { x.F_DepName, x.F_ComPanyName, x.F_Address, x.ProductDetail.F_Name, x.F_Price, x.ProductDetail.F_Code })
                .Select((item) => new Z_OrderDetail
                {
                    F_DepName = item.Key.F_DepName,
                    F_ComPanyName = item.Key.F_ComPanyName,
                    F_Address = item.Key.F_Address,
                    F_ProductName = item.Key.F_Name,
                    F_ProductCode = item.Key.F_Code,
                    F_ProdNum = item.Sum(x => x.F_ProdNum),
                    F_Price = item.Key.F_Price,
                    F_UseNumber = item.Sum(x => x.F_UseNumber)
                })
                .Skip((input.PageIndex - 1) * input.PageRows)
                .Take(input.PageRows)
                .ToList();
                return new PageResult<Z_OrderDetail> { Data = list, Total = resultData.Count() };
            }
            return new PageResult<Z_OrderDetail> { Data = new List<Z_OrderDetail>(), Total = 0 };
        }
        public async Task<PageResult<Z_OrderDetail>> GetDataListByGoodsAsync(PageInput<ProductConditinDto> input)
        {
            var search = input.Search;
            var q = GetIQueryable();
            var shopCartList = await GetShopCartPageList();
            shopCartList.AddRange(q);
            var where = LinqHelper.True<Z_OrderDetail>();


            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Z_OrderDetail, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            //判断状态
            if (search.state.HasValue && search.state != 0)
            {
                where = where.And(x => x.F_State == (OrderType)search.state.Value);
            }
            //判断状态
            if (!search.userId.IsNullOrWhiteSpace())
            {
                where = where.And(x => x.F_UserId == search.userId.Trim());
            }
            //判断状态
            if (!search.userName.IsNullOrWhiteSpace())
            {
                where = where.And(x => !x.F_UserName.IsNullOrWhiteSpace() && x.F_UserName.Contains(search.userName.Trim()));
            }
            //判断商品id 
            if (!search.productId.IsNullOrWhiteSpace())
            {
                where = where.And(x => !x.F_ProductId.IsNullOrWhiteSpace() && x.F_ProductId == search.productId);
            }
            //判断商品id 
            if (search.isPay.HasValue)
            {
                where = where.And(x => x.F_State != OrderType.待支付 && x.F_State != OrderType.已取消);
            }
            //收货地址 
            if (!search.address.IsNullOrWhiteSpace())
            {
                where = where.And(x => x.F_Address == search.address);
            }
            var resultData = shopCartList.Where(where.Compile());
            if (resultData != null && resultData.Count() > 0)
            {

                //查询所有的商品数据
                var pId = resultData.Where(x => x.F_Type == ShopType.商城).Select(x => x.F_ProductId).ToList();
                var queryProducts = this.Db.GetIQueryable<Z_Products>().AsNoTracking().Where(x => pId.Contains(x.F_Id)).ToList();
                var formalEmployees = this.Db.GetIQueryable<HR_FormalEmployees>().AsNoTracking().Where(x => !x.EmployRelStatus.Contains("离职") && resultData.Select(item => item.F_UserName).Contains(x.NameUser)).ToList();

                if (pId.Count > 0)
                {
                    resultData.ForEach(item =>
                    {
                        if (formalEmployees.Count > 0)
                        {
                            var hR_Formal = formalEmployees.Find(x => x.NameUser == item.F_UserName);
                            if (hR_Formal != null)
                            {
                                var projectDept = _hR_FormalEmployeesBusiness.GetProjectDept(hR_Formal.F_Id);
                                item.F_DepName = !string.IsNullOrWhiteSpace(projectDept.dept) ? projectDept.dept : "未知部门";
                            }
                        }
                        if (item.F_Type == ShopType.商城)
                        {
                            item.ProductDetail = queryProducts.FirstOrDefault(x => x.F_Id == item.F_ProductId);
                            item.F_ProductName = item.ProductDetail != null ? item.ProductDetail.F_Name : "";
                            item.F_ProductCode = item.ProductDetail != null ? item.ProductDetail.F_Code : "";
                        }
                    });
                    if (!string.IsNullOrWhiteSpace(search.productName))
                    {
                        resultData = resultData.Where(x => x.ProductDetail != null && x.ProductDetail.F_Name.Contains(search.productName));
                    }
                }
                var list = resultData.AsQueryable()
                .OrderBy($@"{input.SortField} {input.SortType}")
                .GroupBy(x => new { x.F_ProductId, x.F_ProductName, x.F_Price, x.F_ProductCode })
                .Select((item) => new Z_OrderDetail
                {
                    F_ProductId = item.Key.F_ProductId,
                    F_ProdNum = item.Sum(x => x.F_ProdNum),
                    F_ProductName = item.Key.F_ProductName,
                    F_ProductCode = item.Key.F_ProductCode,
                    F_Price = item.Key.F_Price,
                    F_UseNumber = item.Sum(x => x.F_UseNumber),
                })
                .Skip((input.PageIndex - 1) * input.PageRows)
                .Take(input.PageRows)
                .ToList();
                return new PageResult<Z_OrderDetail> { Data = list, Total = resultData.Count() };
            }
            return new PageResult<Z_OrderDetail> { Data = new List<Z_OrderDetail>(), Total = 0 };
        }

        public async Task<Z_OrderDetail> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        /// <summary>
        /// 获取我的待收货数量
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<int> GetMyReceiveCount(string userId)
        {
            if (!string.IsNullOrWhiteSpace(userId))
            {
                return await this.GetIQueryable().AsNoTracking().Where(x => x.F_UserId == userId && x.F_State == OrderType.待收货).CountAsync();
            }
            return 0;
        }
        /// <summary>
        /// <summary>
        /// 获取购物车列表
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task<List<Z_OrderDetail>> GetShopCartPageList()
        {
            List<Z_OrderDetail> orderDetails = new List<Z_OrderDetail>();
            Dictionary<string, string> list = new Dictionary<string, string>();
            var keys = await RedisHelper.KeysAsync("*");
            var userKeys = new List<string>();
            if (keys.Length > 0)
            {
                userKeys = keys.ToList().Where(x => x.Contains("SHOPCART:")).ToList();
            }
            List<Z_OrderDetail> z_OrderDetails = new List<Z_OrderDetail>();
            if (userKeys.Count > 0)
            {
                var productList = await this.Db.GetIQueryable<Z_Products>().ToListAsync();
                var userList = await this.Db.GetIQueryable<Base_User>().ToListAsync();
                if (productList.Count > 0)
                {
                    //获取数据
                    userKeys.ForEach(async key =>
                    {
                        //查询
                        var list = await RedisHelper.HGetAllAsync(key);
                        if (list != null && list.Count > 0)
                        {
                            //查询所有的商品
                            var products = productList.Where(x => list.ToList().Select(x => x.Key).ToList().Contains(x.F_Id)).ToList();
                            if (products.Count > 0)
                            {
                                products.ForEach(item =>
                                {
                                    orderDetails.Add(new Z_OrderDetail()
                                    {
                                        F_ProductId = item.F_Id,
                                        F_UserId = key.Replace("SHOPCART:", ""),
                                        ProductDetail = item,
                                        F_ProdNum = list[item.F_Id].ToInt(),
                                        F_Price = item.F_Price,
                                        F_Type = ShopType.商城,
                                        F_CreateDate = DateTime.Now,
                                        F_Id = GuidHelper.GenerateKey(),
                                        F_UserName = userList.Find(x => x.Id == key.Replace("SHOPCART:", "")).RealName,
                                        F_UseNumber = (decimal)list[item.F_Id].ToInt() * item.F_Price,
                                        F_State = OrderType.待支付
                                    });
                                });
                            }
                        }
                    });
                }
            }
            return orderDetails;
        }



        /// <summary>
        /// 创建订单 
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        //[Transactional]
        public async Task SubmitOrder(List<Z_OrderDetail> list)
        {
            //todo 消费积分结算
            var userId = list.FirstOrDefault().F_UserId;
            if (userId != null)
            {
                var total = 0.0;//总数
                list.ForEach(item =>
                {
                    item.F_CreateUserId = item.F_UserId;
                    item.F_CreateUserName = item.F_UserName;
                    item.F_CreateDate = DateTime.Now;
                    item.F_Id = Guid.NewGuid().ToString("N");
                    item.F_OrderNumber = DateTime.Now.Ticks.ToString();
                    item.F_ProdNum = item.F_ProdNum.HasValue ? item.F_ProdNum.Value : 1;
                    total += (double)item.F_ProdNum * (double)item.F_Price.Value;
                    item.F_UseNumber = (decimal)((double)item.F_ProdNum * (double)item.F_Price.Value);
                });
                //获取购买者的积分
                var pointEntity = await this.Db.GetIQueryable<HR_PointEntity>().FirstOrDefaultAsync(x => x.F_Iyear == DateTime.Now.Year && x.F_UserId == userId);
                if (pointEntity != null)
                {
                    //剩余额度
                    var resNum = pointEntity.F_ResNumber;
                    //计算总消费
                    if (resNum >= (decimal)total)
                    {
                        //保存消费记录
                        pointEntity.F_ResNumber = resNum - (decimal)total;
                        //保存消费记录明细
                        HR_PointUseDetail hR_PointUseDetail = new HR_PointUseDetail()
                        {
                            F_CreateUserId = list[0].F_UserId,
                            F_CreateUserName = list[0].F_UserName,
                            F_CreateDate = DateTime.Now,
                            F_Id = Guid.NewGuid().ToString("N"),
                            F_UserId = list[0].F_UserId,
                            F_UserCode = list[0].F_UserCode,
                            F_UserName = list[0].F_UserName,
                            F_UseNumber = (decimal)total,
                            F_ResNumer = resNum,
                            F_BackNumer = pointEntity.F_ResNumber,
                            F_OrderId = string.Join(",", list.Select(x => x.F_Id).ToArray()),
                            F_Describe = "商品购买",
                            F_OrderType = ShopType.商城,
                            F_ResType = PointBuyType.减少,
                            F_Month = DateTime.Now.ToString("yyyy-MM")
                        };
                        await this.Db.InsertAsync(hR_PointUseDetail);
                        await this.Db.UpdateAsync(pointEntity);
                        //判断积分额度
                        await InsertAsync(list);
                        //添加购物车商品购买次数
                        var updateProducts = new List<Z_Products>();
                        var products = this.Db.GetIQueryable<Z_Products>();
                        list.ForEach((pro) =>
                        {
                            if (pro.F_ProductId != null)
                            {
                                var z_Product = products.FirstOrDefault(x => x.F_Id == pro.F_ProductId);
                                z_Product.F_Sales += pro.F_ProdNum;
                                updateProducts.Add(z_Product);
                            }
                        });
                        if (updateProducts.Count > 0)
                        {
                            await this.Db.UpdateAsync(updateProducts);
                        }
                        //删除购物车商品
                        //删除redis购物车
                        await RedisHelper.HDelAsync("SHOPCART:" + list[0].F_UserId, list.Select(x => x.F_ProductId).ToArray());
                    }
                    else
                    {
                        throw new Exception("福豆余额不足，请调整商品数量！");
                    }
                }
                else
                {
                    throw new Exception("福豆余额不足，请调整商品数量！");
                }
            }
            else
            {
                throw new Exception("购买用户异常！");
            }

        }

        public async Task AddDataAsync(Z_OrderDetail data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Z_OrderDetail data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }
        /// <summary>
        /// 批量修改状态
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        //[Transactional]
        public async Task UpdateOrderState(RefundOrderDto dto)
        {
            if (dto.orderType == OrderType.已取消)
            {
                await RefundOrderAsync(dto);
            }
            else
            {
                var updateOrder = new List<Z_OrderDetail>();
                var z_OrderDetails = await GetIQueryable().AsNoTracking().Where(x => dto.ids.Contains(x.F_Id)).ToListAsync();
                if (z_OrderDetails.Count > 0)
                {
                    z_OrderDetails.ForEach(x => x.F_State = dto.orderType);
                    updateOrder = z_OrderDetails;
                    await this.Db.UpdateAsync(updateOrder);
                }
            }

        }
        /// <summary>
        /// 退款功能
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        //[Transactional]
        public async Task RefundOrderAsync(RefundOrderDto dto)
        {
            var orderDetails = await GetIQueryable().AsNoTracking().Where(x => dto.ids.Contains(x.F_Id)).ToListAsync();
            if (orderDetails.Count() > 0)
            {
                //获取当前年的员工积分总额度
                var pointList = await Db.GetIQueryable<HR_PointEntity>().AsNoTracking().Where(i => i.F_Iyear == DateTime.Now.Year).ToListAsync();
                List<HR_PointEntity> addPointList = new List<HR_PointEntity>();
                List<HR_PointEntity> updatePointList = new List<HR_PointEntity>();
                List<HR_PointUseDetail> records = new List<HR_PointUseDetail>();
                //添加购物车商品购买次数
                var updateProducts = new List<Z_Products>();
                var products = await this.Db.GetIQueryable<Z_Products>().AsNoTracking().ToListAsync();
                var base_Users = await this.Db.GetIQueryable<Base_User>().Where(x => !string.IsNullOrWhiteSpace(x.UserName)).AsNoTracking().ToListAsync();
                var base_UserLogs = new List<Base_UserLog>();
                orderDetails.ForEach(async item =>
                {
                    var z_Product = products.FirstOrDefault(x => x.F_Id == item.F_ProductId);
                    item.F_State = OrderType.已取消;
                    //退款
                    var pointEntity = pointList.Find(x => x.F_UserId == item.F_UserId);

                    HR_PointUseDetail hR_PointUseDetail = new HR_PointUseDetail();
                    hR_PointUseDetail.F_UserName = item.F_UserName;
                    hR_PointUseDetail.F_UserId = item.F_UserId;
                    hR_PointUseDetail.F_UserCode = item.F_UserCode;
                    hR_PointUseDetail.F_UseNumber = item.F_UseNumber;
                    hR_PointUseDetail.F_Describe = z_Product.F_Name + (dto.causeStr != "其他" ? dto.causeStr : dto.Remark);
                    hR_PointUseDetail.F_OrderType = ShopEnum.ShopType.商品退款;
                    hR_PointUseDetail.F_ResType = ShopEnum.PointBuyType.增加;
                    hR_PointUseDetail.F_Month = DateTime.Now.ToString("yyyy-MM");
                    //F_ResNumer
                    if (pointEntity != null)
                    {
                        hR_PointUseDetail.F_ResNumer = pointEntity.F_ResNumber;
                        hR_PointUseDetail.F_BackNumer = pointEntity.F_ResNumber + item.F_UseNumber;
                        pointEntity.F_ResNumber = hR_PointUseDetail.F_BackNumer;
                        this.UpdateEntity(pointEntity, _operator);
                        updatePointList.Add(pointEntity);
                    }
                    else
                    {
                        hR_PointUseDetail.F_ResNumer = 0;
                        hR_PointUseDetail.F_BackNumer = item.F_UseNumber;
                        //新增积分总额度表
                        HR_PointEntity hR_Point = new HR_PointEntity()
                        {
                            F_UserName = item.F_UserName?.Trim(),
                            F_UserId = item.F_UserId,
                            F_UserCode = item.F_UserCode,
                            F_Iyear = DateTime.Now.Year,
                            F_ResNumber = item.F_UseNumber,
                            F_StandardNumber = item.F_UseNumber,
                            F_LncDecNumber = 0,
                            F_UsedNumber = 0
                        };
                        this.InitEntity(hR_Point, _operator);
                        addPointList.Add(hR_Point);
                    }

                    z_Product.F_Sales = z_Product.F_Sales > 0 ? z_Product.F_Sales - item.F_ProdNum : 0;
                    updateProducts.Add(z_Product);
                    this.InitEntity(hR_PointUseDetail, _operator);
                    records.Add(hR_PointUseDetail);
                    //退款短信提醒
                    var user = base_Users.FirstOrDefault(x => x.UserName.Contains(item.F_UserCode) && !string.IsNullOrWhiteSpace(x.MobileStr));
                    if (user != null)
                    {
                        try
                        {
                            await Task.Run(() => { SendMessageHelper.SendSMSMsg(user.MobileStr, $"【招商置地】亲爱的小伙伴：您采购的{z_Product.F_Name}为{item.F_UseNumber}福豆,现已为您办理退货,退款理由为：{(dto.causeStr != "其他" ? dto.causeStr : dto.Remark)},福豆已到账,请查收.", "商品退款短信"); });
                            Base_UserLog base_UserLog = new Base_UserLog()
                            {
                                Id = IdHelper.GetId(),
                                CreateTime = DateTime.Now,
                                CreatorId = "admin",
                                CreatorRealName = "超级管理员",
                                LogContent = $"发送{z_Product.F_Name}退款短信,接收方：{item.F_UserName},接收手机号:{user.MobileStr},发送失败",
                                LogType = UserLogType.商品退款短信.ToString(),
                                JsonContent = $"【招商置地】亲爱的小伙伴：您采购的{z_Product.F_Name}为{item.F_UseNumber}福豆,现已为您办理退货,福豆已到账,请查收.",
                            };
                            base_UserLogs.Add(base_UserLog);
                        }
                        catch
                        {
                            Base_UserLog base_UserLog = new Base_UserLog()
                            {
                                Id = IdHelper.GetId(),
                                CreateTime = DateTime.Now,
                                CreatorId = "admin",
                                CreatorRealName = "超级管理员",
                                LogContent = $"发送{z_Product.F_Name}退款短信,接收方：{item.F_UserName},接收手机号:{user.MobileStr},发送失败",
                                LogType = UserLogType.商品退款短信.ToString(),
                                JsonContent = $"【招商置地】亲爱的小伙伴：您采购的{z_Product.F_Name}为{item.F_UseNumber}福豆,现已为您办理退货,福豆已到账,请查收.",
                            };
                            base_UserLogs.Add(base_UserLog);
                        }
                    }
                });
                if (updateProducts.Count > 0)
                {
                    await this.Db.UpdateAsync(updateProducts);
                }
                if (updatePointList.Count > 0)
                {
                    await this.Db.UpdateAsync(updatePointList);
                }

                if (addPointList.Count > 0)
                {
                    this.Db.BulkInsert(addPointList);
                }
                if (records.Count > 0)
                {
                    this.Db.BulkInsert(records);
                }
                if (orderDetails.Count > 0)
                {
                    await this.Db.UpdateAsync(orderDetails);
                }
                if (base_UserLogs.Count > 0)
                {
                    _operator.WriteUserLog(base_UserLogs);
                }
            }
        }

        /// <summary>
        /// 单价退款
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        //[Transactional]
        public async Task RefundOrderByPriceAsync(RefundOrderDto dto)
        {
            var orderDetails = await GetIQueryable().AsNoTracking().Where(x => dto.ids.Contains(x.F_Id)).ToListAsync();
            if (orderDetails.Count() > 0)
            {
                //获取当前年的员工积分总额度
                var pointList = await Db.GetIQueryable<HR_PointEntity>().AsNoTracking().Where(i => i.F_Iyear == DateTime.Now.Year).ToListAsync();
                List<HR_PointEntity> addPointList = new List<HR_PointEntity>();
                List<HR_PointEntity> updatePointList = new List<HR_PointEntity>();
                List<HR_PointUseDetail> records = new List<HR_PointUseDetail>();
                //添加购物车商品购买次数
                var updateProducts = new List<Z_Products>();
                var products = await this.Db.GetIQueryable<Z_Products>().AsNoTracking().ToListAsync();
                var base_Users = await this.Db.GetIQueryable<Base_User>().Where(x => !string.IsNullOrWhiteSpace(x.UserName)).AsNoTracking().ToListAsync();
                var base_UserLogs = new List<Base_UserLog>();
                orderDetails.ForEach(async item =>
                {
                    //退款
                    var pointEntity = pointList.Find(x => x.F_UserId == item.F_UserId);
                    var z_Product = products.FirstOrDefault(x => x.F_Id == item.F_ProductId);
                    var newPrice = item.F_Price - dto.Price;
                    HR_PointUseDetail hR_PointUseDetail = new HR_PointUseDetail();
                    hR_PointUseDetail.F_UserName = item.F_UserName;
                    hR_PointUseDetail.F_UserId = item.F_UserId;
                    hR_PointUseDetail.F_UserCode = item.F_UserCode;
                    hR_PointUseDetail.F_UseNumber = newPrice >= 0 ? newPrice * item.F_ProdNum : Math.Abs(newPrice.Value * item.F_ProdNum.Value);
                    hR_PointUseDetail.F_Describe = z_Product.F_Name + "-" + (dto.Price > item.F_Price ? "商品补款" : "商品退款");
                    hR_PointUseDetail.F_OrderType = dto.Price > item.F_Price ? ShopEnum.ShopType.商品补款 : ShopEnum.ShopType.商品退款;
                    hR_PointUseDetail.F_ResType = dto.Price > item.F_Price ? ShopEnum.PointBuyType.减少 : ShopEnum.PointBuyType.增加;
                    hR_PointUseDetail.F_Month = DateTime.Now.ToString("yyyy-MM");
                    //F_ResNumer
                    if (pointEntity != null)
                    {
                        hR_PointUseDetail.F_ResNumer = pointEntity.F_ResNumber;
                        hR_PointUseDetail.F_BackNumer = hR_PointUseDetail.F_ResType == ShopEnum.PointBuyType.增加 ? pointEntity.F_ResNumber + hR_PointUseDetail.F_UseNumber : pointEntity.F_ResNumber - hR_PointUseDetail.F_UseNumber;
                        pointEntity.F_ResNumber = hR_PointUseDetail.F_BackNumer;
                        if (pointEntity.F_ResNumber < 0)
                        {
                            throw new Exception(hR_PointUseDetail.F_UserName + "积分不足,请协商调整！");
                        }
                        this.UpdateEntity(pointEntity, _operator);
                        updatePointList.Add(pointEntity);
                    }
                    else
                    {
                        hR_PointUseDetail.F_ResNumer = 0;
                        hR_PointUseDetail.F_BackNumer = item.F_UseNumber;
                        //新增积分总额度表
                        HR_PointEntity hR_Point = new HR_PointEntity()
                        {
                            F_UserName = item.F_UserName?.Trim(),
                            F_UserId = item.F_UserId,
                            F_UserCode = item.F_UserCode,
                            F_Iyear = DateTime.Now.Year,
                            F_ResNumber = item.F_UseNumber,
                            F_StandardNumber = item.F_UseNumber,
                            F_LncDecNumber = 0,
                            F_UsedNumber = 0
                        };
                        this.InitEntity(hR_Point, _operator);
                        addPointList.Add(hR_Point);
                    }
                    updateProducts.Add(z_Product);
                    this.InitEntity(hR_PointUseDetail, _operator);
                    records.Add(hR_PointUseDetail);
                    //退款短信提醒
                    var user = base_Users.FirstOrDefault(x => x.UserName.Contains(item.F_UserCode) && !string.IsNullOrWhiteSpace(x.MobileStr));
                    if (user != null)
                    {
                        var msg = "";
                        if (hR_PointUseDetail.F_OrderType == ShopEnum.ShopType.商品退款)
                        {
                            msg = $"【招商置地】亲爱的小伙伴：您采购的{z_Product.F_Name}原单价为{item.F_Price}元，现活动降价单价为{dto.Price}元，现将差额{hR_PointUseDetail.F_UseNumber}元退还到福豆账户，请查收。";
                        }
                        else
                        {
                            msg = $"【招商置地】亲爱的小伙伴：您采购的{z_Product.F_Name}原单价为{item.F_Price}元，现活动涨价单价为{dto.Price}元，现将差额{hR_PointUseDetail.F_UseNumber}元已从福豆账户中扣除，望谅解。";
                        }
                        try
                        {
                            await Task.Run(() => { SendMessageHelper.SendSMSMsg(user.MobileStr, msg, "商品退款短信"); });
                            Base_UserLog base_UserLog = new Base_UserLog()
                            {
                                Id = IdHelper.GetId(),
                                CreateTime = DateTime.Now,
                                CreatorId = "admin",
                                CreatorRealName = "超级管理员",
                                LogContent = $"发送{z_Product.F_Name}退款短信,接收方：{item.F_UserName},接收手机号:{user.MobileStr},发送失败",
                                LogType = UserLogType.商品退款短信.ToString(),
                                JsonContent = msg,
                            };
                            base_UserLogs.Add(base_UserLog);
                        }
                        catch
                        {
                            Base_UserLog base_UserLog = new Base_UserLog()
                            {
                                Id = IdHelper.GetId(),
                                CreateTime = DateTime.Now,
                                CreatorId = "admin",
                                CreatorRealName = "超级管理员",
                                LogContent = $"发送{z_Product.F_Name}退款短信,接收方：{item.F_UserName},接收手机号:{user.MobileStr},发送失败",
                                LogType = UserLogType.商品退款短信.ToString(),
                                JsonContent = msg,
                            };
                            base_UserLogs.Add(base_UserLog);
                        }
                    }
                    //调整订单价格
                    item.F_Price = dto.Price;
                    item.F_UseNumber = dto.Price * item.F_ProdNum;
                });
                if (updateProducts.Count > 0)
                {
                    await this.Db.UpdateAsync(updateProducts);
                }
                if (updatePointList.Count > 0)
                {
                    await this.Db.UpdateAsync(updatePointList);
                }

                if (addPointList.Count > 0)
                {
                    this.Db.BulkInsert(addPointList);
                }
                if (records.Count > 0)
                {
                    this.Db.BulkInsert(records);
                }
                if (orderDetails.Count > 0)
                {
                    await this.Db.UpdateAsync(orderDetails);
                }
                if (base_UserLogs.Count > 0)
                {
                    _operator.WriteUserLog(base_UserLogs);
                }
            }
        }
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Z_OrderDetail>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Z_OrderDetail, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<Z_OrderDetail, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}