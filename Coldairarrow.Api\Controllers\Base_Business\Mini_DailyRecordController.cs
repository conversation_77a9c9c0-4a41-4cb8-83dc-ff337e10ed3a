﻿using Coldairarrow.Business.Base_Business;
using Coldairarrow.Entity.Base_Business;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Base_Business
{
    [Route("/Base_Business/[controller]/[action]")]
    public class Mini_DailyRecordController : BaseApiController
    {
        #region DI

        public Mini_DailyRecordController(IMini_DailyRecordBusiness mini_DailyRecordBus)
        {
            _mini_DailyRecordBus = mini_DailyRecordBus;
        }

        IMini_DailyRecordBusiness _mini_DailyRecordBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Mini_DailyRecord>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _mini_DailyRecordBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Mini_DailyRecord> GetTheData(IdInputDTO input)
        {
            return await _mini_DailyRecordBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Mini_DailyRecord data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _mini_DailyRecordBus.AddDataAsync(data);
            }
            else
            {
                await _mini_DailyRecordBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _mini_DailyRecordBus.DeleteDataAsync(ids);
        }

        #endregion

        #region 二次开发

        [NoCheckJWT]
        [HttpGet]
        public void testData()
        {
            DateTime DateNow = DateTime.Now.AddDays(-1);
            DateTime day = new DateTime(DateNow.Year, DateNow.Month, DateNow.Day);
            for (var i = 0; i < 10; i++)
            {
                getDailySummary(day);
                //getRecentPortrait(day);
                getDailyVisitTrend(day);
                getVisitDistribution(day);
                day = day.AddDays(-1);
            }
        }
        /// <summary>
        /// 获取每日
        /// </summary>
        [NoCheckJWT]
        [HttpGet]
        public void getDayData()
        {
            DateTime DateNow = DateTime.Now.AddDays(-1);
            DateTime day = new DateTime(DateNow.Year, DateNow.Month, DateNow.Day);
            for (var i = 0; i < 2; i++)
            {
                getDailySummary(day);
                getRecentPortrait(day);
                getDailyVisitTrend(day);
                getVisitDistribution(day);
                day = day.AddDays(-1);
            }
        }
        /// <summary>
        /// 获取每日加和
        /// </summary>
        /// <param name="day"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpGet]
        public string getDailySummary(DateTime day)
        {

            try
            {
                var result = WechatMiniHelper.getDailySummary(day);
                if (!result.IsNullOrEmpty())
                {
                    var data = _mini_DailyRecordBus.GetDataByDate(day);
                    if (data.IsNullOrEmpty())
                    {
                        data = new Mini_DailyRecord();
                        data.W_visit_total = result.visit_total;
                        data.W_share_pv = result.share_pv;
                        data.W_share_uv = result.share_uv;
                        data.Id = IdHelper.GetId();
                        data.Date = day;
                        _mini_DailyRecordBus.AddDataAsync(data);
                    }
                    else
                    {
                        data.W_visit_total = result.visit_total;
                        data.W_share_pv = result.share_pv;
                        data.W_share_uv = result.share_uv;
                        _mini_DailyRecordBus.UpdateDataAsync(data);
                    }
                }
                return "1";
            }catch(Exception ex)
            {
                return ex.Message;
            }     
        }
        [NoCheckJWT]
        [HttpGet]
        public string getVisitDistribution(DateTime day)
        {

            try
            {
                var result = WechatMiniHelper.getVisitDistribution(day);
                if (!result.IsNullOrEmpty())
                {
                    var data = _mini_DailyRecordBus.GetDataByDate(day);
                    if (data.IsNullOrEmpty())
                    {
                        data = new Mini_DailyRecord();
                        data.Id = IdHelper.GetId();
                        data.Date = day;
                        _mini_DailyRecordBus.AddDataAsync(data);
                    }
                    else
                    {
                        foreach (var i in result.list)
                        {
                            if (i.index == "access_source_session_cnt")
                            {
                                var number = 0;
                                foreach (var j in i.item_list)
                                {                               
                                    switch (j.key) {
                                        case 4: data.S_source_4 = j.value;break;
                                        case 13: data.S_source_13 = j.value; break;
                                        case 29: data.S_source_29 = j.value; break;
                                        case 3: data.S_source_3 = j.value; break;
                                        case 16: data.S_source_16 = j.value; break;
                                        case 23: data.S_source_23 = j.value; break;
                                        case 2: data.S_source_2 = j.value; break;
                                        default: number = number + j.value;break;
                                    }
                                }
                                data.S_source_other = number;
                                data.S_source_ALL = i.item_list.ToJson();
                            }
                        }
                        if (data.S_source_3.IsNullOrEmpty())
                        {
                            data.S_source_3 = 0;
                        }
                        if (data.S_source_23.IsNullOrEmpty())
                        {
                            data.S_source_23 = 0;
                        }
                        if (data.S_source_16.IsNullOrEmpty())
                        {
                            data.S_source_16 = 0;
                        }
                        _mini_DailyRecordBus.UpdateDataAsync(data);
                    }
                }
                return "1";
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }
        /// <summary>
        /// 获取用户30天画像
        /// </summary>
        /// <param name="day"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpGet]
        public string getRecentPortrait(DateTime day)
        {
            try {
            var result = WechatMiniHelper.getMonthlyUserPortrait(day);
             if (!result.IsNullOrEmpty())
            {
                var data = _mini_DailyRecordBus.GetDataByDate(day);
                if (data.IsNullOrEmpty())
                {
                    data = new Mini_DailyRecord();
                    data.Id = IdHelper.GetId();
                    data.Date = day;
                    data.visit_uv_30_genders = result.visit_uv.genders.ToJson();
                    data.visit_uv_30_devices = result.visit_uv.devices.ToJson();
                    data.visit_uv_30_ages = result.visit_uv.ages.ToJson();
                    data.visit_uv_30_platforms = result.visit_uv.platforms.ToJson();
                    data.visit_uv_30_province = result.visit_uv.province.ToJson();
                    data.visit_uv_30_city = result.visit_uv.city.ToJson();
                    _mini_DailyRecordBus.AddDataAsync(data);
                }
                else
                {
                    data.visit_uv_30_genders = result.visit_uv.genders.ToJson();
                    data.visit_uv_30_devices = result.visit_uv.devices.ToJson();
                    data.visit_uv_30_ages = result.visit_uv.ages.ToJson();
                    data.visit_uv_30_platforms = result.visit_uv.platforms.ToJson();
                    data.visit_uv_30_province = result.visit_uv.province.ToJson();
                    data.visit_uv_30_city = result.visit_uv.city.ToJson();
                    _mini_DailyRecordBus.UpdateDataAsync(data);
                }
            }
            return "1";
        }catch(Exception ex)
            {
                return ex.Message;
            }
         }
        [NoCheckJWT]
        [HttpGet]
        public string getDailyVisitTrend(DateTime day)
        {
            try { 
            var result = WechatMiniHelper.getDailyVisitTrend(day);
            if (!result.IsNullOrEmpty())
            {
                var data = _mini_DailyRecordBus.GetDataByDate(day);
                if (data.IsNullOrEmpty())
                {
                    data = new Mini_DailyRecord();
                    data.W_session_cnt = result.session_cnt;
                    data.W_visit_pv = result.visit_pv;
                    data.W_visit_uv = result.visit_uv;
                    data.W_stay_time_session = result.stay_time_session;
                    data.W_stay_time_uv = result.stay_time_uv;
                    data.W_visit_depth = result.visit_depth;
                    data.Id = IdHelper.GetId();
                    data.Date = day;
                    _mini_DailyRecordBus.AddDataAsync(data);
                }
                else
                {
                    data.W_session_cnt = result.session_cnt;
                    data.W_visit_pv = result.visit_pv;
                    data.W_visit_uv = result.visit_uv;
                    data.W_stay_time_session = result.stay_time_session;
                    data.W_stay_time_uv = result.stay_time_uv;
                    data.W_visit_depth = result.visit_depth;
                    _mini_DailyRecordBus.UpdateDataAsync(data);
                }
            }
            return "1";
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        #endregion
    }
}