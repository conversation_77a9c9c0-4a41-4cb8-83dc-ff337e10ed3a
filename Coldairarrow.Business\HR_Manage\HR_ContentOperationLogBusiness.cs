﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.IBusiness;
using System;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_ContentOperationLogBusiness : BaseBusiness<HR_ContentOperationLog>, IHR_ContentOperationLogBusiness, ITransientDependency
    {
        private IOperator _operator;
        public HR_ContentOperationLogBusiness(IDbAccessor db, IOperator ioperator)
            : base(db)
        {
            _operator = ioperator;
        }

        #region 外部接口

        public async Task<PageResult<HR_ContentOperationLog>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_ContentOperationLog>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_ContentOperationLog, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_ContentOperationLog> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_ContentOperationLog data)
        {
            await InsertAsync(data);
        }
        public async Task SaveDataAsync(List<HR_ContentOperationLog> data)
        {
            if (data.Count > 0)
            {
                data.ForEach((item) =>
                {
                    item.F_CreateDate = DateTime.Now;
                    item.F_CreateUserId = "标签";
                    item.F_CreateUserName = "标签";
                    item.F_Id = Guid.NewGuid().ToString();
                });
            }
            await InsertAsync(data);
        }
        public void SaveData(List<HR_ContentOperationLog> data)
        {
            if (data.Count > 0)
            {
                data.ForEach((item) =>
                {
                    item.F_CreateDate = DateTime.Now;
                    item.F_CreateUserId = "标签";
                    item.F_CreateUserName = "标签";
                    item.F_Id = Guid.NewGuid().ToString();
                });
            }
            Db.Insert(data);
        }
        public async Task UpdateDataAsync(HR_ContentOperationLog data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_ContentOperationLog>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_ContentOperationLog, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_ContentOperationLog, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}