﻿using Coldairarrow.Business.HR_ReportFormsManage;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Plan_Manage
{
    /// <summary>
    /// 运营作战室-市场分析
    /// </summary>
    [Route("/Plan_Manage/[controller]/[action]")]
    public class HousingAnalysisController
    {
        readonly IConfiguration _configuration;
     
        readonly string payUrl = "";
        public HousingAnalysisController(
            IConfiguration configuration,
            IERPBusiness iERPBusiness
            )
        {
            _configuration = configuration;
            payUrl = _configuration["PaySystem:Url"];
        }
        public Dictionary<string, object> GetDictionaryByModel(HAInput input)
        {
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            paramters.Add("City", OAHelper.GetNuLL(input.City));
            paramters.Add("Area",OAHelper.GetNuLL(input.Area) );
            paramters.Add("type", OAHelper.GetNuLL(input.type));
            paramters.Add("clusterArea", OAHelper.GetNuLL(input.clusterArea));
            return paramters;
        }
        #region 新房
        /// <summary>
        /// 获取大组团
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getClusterArea(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getClusterArea", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 新房销售情况-区域
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getNewHouseSalesByArea(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getNewHouseSalesByArea", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 新房销售项目情况-项目
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getNewHouseSalesByProject(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getNewHouseSalesByProject", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 新房销售项目情况-房企
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getNewHouseSalesByCOM(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getNewHouseSalesByCOM", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 新房销售情况-面积
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getNewHouseSalesByHA(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getNewHouseSalesByHA", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 新房销售情况-成交价
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getNewHouseSalesByTurnover(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getNewHouseSalesByTurnover", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 新房销售情况-项目建筑排名
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getNewHouseSalesByProjectArea(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getNewHouseSalesByProjectArea", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 新房销售情况-每月均价分析
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getNewHouseSalesByAvg(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getNewHouseSalesByAvg", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 新房二手房比较
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getNewHouseCompareByOld(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getNewHouseCompareByOld", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 新房销售-每周数据分析
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getNewHouseSalesByWeekData(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getNewHouseSalesByWeekData", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 新房业态占比
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public  dynamic getNewHouseSalesByFormat(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getNewHouseSalesByFormat", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 新房二手房的时间
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public  dynamic getNewTime(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getNewTime", GetDictionaryByModel(input), null);
            return str;
        }

        #endregion

        #region 二手房
        /// <summary>
        /// 获取二手房大组团
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getSecondHouseClusterArea(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getSecondHouseClusterArea", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 二手房销售情况-区域
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getSecondHouseSalesByArea(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getSecondHouseSalesByArea", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 二手房销售情况-成交量
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getSecondHouseSalesByTurnover(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getSecondHouseSalesByTurnover", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 二手房销售情况-挂牌成交量
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getSecondHouseSalesByGPTurnover(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getSecondHouseSalesByGPTurnover", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 二手房销售情况-面积
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getSecondHouseSalesByHA(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getSecondHouseSalesByHA", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 二手房销售情况-每月数据分析
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getSecondHouseSalesByMonthData(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getSecondHouseSalesByMonthData", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 二手房销售情况-近5年成交
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getSecondHouseSalesByRecent(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getSecondHouseSalesByRecent", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 二手房销售情况-近5年近3个月成交
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public  dynamic getSecondHouseSalesByJSRecent(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getSecondHouseSalesByJSRecent", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 二手房销售情况-涨价降价趋势
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public  dynamic getSecondHouseSalesByTrend(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getSecondHouseSalesByTrend", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 二手房销售情况-房源销售情况
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public  dynamic getSecondHouseSalesByHousing(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getSecondHouseSalesByHousing", GetDictionaryByModel(input), null);
            return str;
        }
        #endregion

        #region 全国二手房
        /// <summary>
        /// 获取城市列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getAllCity()
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getAllCity", null, null);
            return str;
        }
        /// <summary>
        /// 获取区域列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getAllAreaByCity(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getAllAreaByCity", GetDictionaryByModel(input), null);
            return str;
        }
        /// <summary>
        /// 获取最新时间
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic getAllSecondNewTime(HAInput input)
        {
            string str = HttpHelper.GetData(payUrl + "HousingAnalysis/getAllSecondNewTime", GetDictionaryByModel(input), null);
            return str;
        }
        #endregion 
    }
    public class HAInput
    {
        /// <summary>
        /// 城市
        /// </summary>
        public virtual string City { get; set; } = "重庆";
        public virtual string Area { get; set; }
        public virtual string type { get; set; }
        /// <summary>
        /// 组团
        /// </summary>
        public virtual string clusterArea { get; set; }
    }
}
