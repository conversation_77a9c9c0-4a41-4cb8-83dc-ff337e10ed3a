﻿<template>
  <a-modal :title="title" width="60%" :visible="visible" :confirmLoading="loading" @cancel="
      () => {
        this.visible = false
      }
    ">
    <template v-if="!isEdit" slot="footer">
      <a-button key="save" @click="save" icon="edit">
        保存草稿
      </a-button>
      <a-button key="createFlow" :loading="loading" @click="createFlow" icon="check-square">
        {{ CreatedName }}
      </a-button>
      <a-button key="cancel" :loading="loading" @click="
          () => {
            this.visible = false
          }
        " icon="close">
        取消
      </a-button>
    </template>
    <template v-else slot="footer">
      <a-button key="cancel" icon="close" :loading="loading" @click="
          () => {
            this.visible = false
          }
        ">
        关闭
      </a-button>
    </template>
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="entity" :rules="rules" v-bind="layout">
        <a-alert message="调任流程:跨部门提交为提交流程走OA面单,部门内或其他为直接提交生效。" type="info" show-icon />

        <br />
        <a-row :gutter="24">
          <a-divider orientation="left">调动人员信息</a-divider>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="调动员工" prop="F_UserId" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input-search v-model="entity.EmpName" :disabled="isEdit" placeholder="" style="width: 100%"
                  @search="onSearch" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="调动员工编码" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.EmpCode" disabled placeholder="" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="联系电话" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.MobilePhone" :disabled="isEdit" placeholder="请输入联系电话" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-divider orientation="left">调动对比信息</a-divider>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="现任职位" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.F_CurrentPosition" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="调动后职位" prop="F_MobilizePositionId" :labelCol="{ span: 7 }"
                :wrapperCol="{ span: 14 }">
                <a-input-search v-model="entity.F_MobilizePosition" placeholder="" style="width: 100%"
                  @search="onPostSearch" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="现任部门" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.F_Department" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="调动后部门" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.F_MobilizeDep" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="现任职级" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.F_CurrentRank" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="调动后职级" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
                <SelectDiction ref="MobilizeRank" :Name="'职级'" :disabled="isEdit" @selectedvalue="selectMobilizeRank"
                  :Value="entity.F_MobilizeRank" style="width: 100%;"></SelectDiction>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="现任项目" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.F_CurrentProject" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="调动后项目" prop="F_MobilizeProject" :labelCol="{ span: 7 }"
                :wrapperCol="{ span: 14 }">
                <SelectDiction ref="MobilizeProject" :Name="'类别归属'" :disabled="isEdit"
                  @selectedvalue="selectMobilizeProject" :Value="entity.F_MobilizeProject" style="width: 100%;">
                </SelectDiction>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="调动生效日期" prop="F_ChangesDate" :labelCol="{ span: 4 }" :wrapperCol="{ span: 14 }">
                <a-date-picker v-model="entity.F_ChangesDate" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="备注" :labelCol="{ span: 4 }" :wrapperCol="{ span: 19 }">
                <a-textarea v-model="entity.F_Remark" placeholder="请输入" :rows="4" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="附件" :labelCol="{ span: 3 }" :wrapperCol="{ span: 19 }">
                <uploadInfo ref="selectDiction" :fileEntity="fileEntity" :isNoUploadP="isEdit"
                  @handleUpload="handleUpload"></uploadInfo>
              </a-form-item>
            </a-col>
          </a-row>
          <a-divider orientation="left">调动事务信息</a-divider>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="原用工状态" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.F_OriginalEmplStatus" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="目标用工状态" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.F_MobilzOriEmplStatus" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="变动操作" prop="F_ChangesOperating" :labelCol="{ span: 8 }"
                :wrapperCol="{ span: 14 }">
                <SelectDiction ref="ChangesOperating" :Name="'调动变动操作'" :disabled="isEdit"
                  @selectedvalue="selectChangesOperating" :Value="entity.F_ChangesOperating" style="width: 100%;">
                </SelectDiction>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="变动类型" prop="F_ChangesType" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
                <SelectDiction ref="ChangesType" :Name="'调动变动类型'" :disabled="isEdit" @selectedvalue="selectChangesType"
                  :Value="entity.F_ChangesType" style="width: 100%;"></SelectDiction>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="变动原因" prop="F_ChangesReason" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <SelectDiction ref="ChangesReason" :Name="'调动变动原因'" :disabled="isEdit"
                  @selectedvalue="selectChangesReason" :Value="entity.F_ChangesReason" style="width: 100%;">
                </SelectDiction>
              </a-form-model-item>
            </a-col>

            <a-col :span="12">
              <a-form-model-item label="任职类型" prop="F_ForType" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
                <a-select v-model="entity.F_ForType" style="width: 100%">
                  <a-select-option v-for="item in forTypeDic" :value="item.F_ItemValue" :key="item.F_ItemValue">
                    {{ item.F_ItemName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-row>
      </a-form-model>
    </a-spin>
    <select-emp ref="selectEmp" :callBack="SeletedEmp" :queryParam="{ EmployRelStatus: '离职' }"></select-emp>
    <select-post ref="selectPost" :callBack="SeletedPost"></select-post>
  </a-modal>
</template>

<script>
import moment from 'moment'
import SelectEmp from '@/components/SelectEmployee/EmpList'
import SelectPost from '@/components/SelectPost/PostList'
import SelectDiction from '@/components/SelectDictionaries/DictionariesList'
import uploadInfo from '@/components/TemplateUpload/upload'

export default {
  components: {
    SelectDiction,
    SelectEmp,
    SelectPost,
    uploadInfo
  },
  props: {
    parentObj: Object
    // userId:String
  },
  data () {
    return {
      layout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 18 }
      },
      visible: false,
      loading: false,
      isEdit: false,
      entity: {},
      rules: {
        F_UserId: [{ required: true, message: '请选择调动员工', trigger: 'blur' }],
        F_MobilizeProject: [{ required: true, message: '请选择调入项目', trigger: 'change' }],
        F_ChangesDate: [{ required: true, message: '请选择调动生效时间', trigger: 'change' }],
        F_ChangesOperating: [{ required: true, message: '请选择变动操作', trigger: 'change' }],
        F_ChangesType: [{ required: true, message: '请选择变动类型', trigger: 'change' }],
        F_ChangesReason: [{ required: true, message: '请选择调动原因', trigger: 'change' }]
      },
      title: '',
      CreatedName: '提交流程',
      forTypeDic: [], //任职类型
      fileEntity: {}
    }
  },
  methods: {
    handleUpload (res) {
      if (this.fileEntity.Files === null) {
        this.fileEntity.Files = res.Files
      } else {
        res.Files.forEach(e => {
          this.fileEntity.Files.push(e)
        })
      }
    },
    //调动后项目
    selectMobilizeProject (value) {
      this.entity.F_MobilizeProject = value
    },
    //离职变动操作
    selectChangesOperating (value) {
      this.entity.F_ChangesOperating = value
    },
    //离职变动类型
    selectChangesType (value) {
      this.entity.F_ChangesType = value
    },
    //调动原因
    selectChangesReason (value) {

      this.CreatedName = (value == '晋升' || value == '降职' || value == '其他' || value == '部门内调动') ? '提交生效' : '提交流程'

      this.entity.F_ChangesReason = value
    },
    //职级
    selectMobilizeRank (value) {
      this.entity.F_MobilizeRank = value
    },
    onSearch () {
      this.$refs.selectEmp.openForm('选择员工')
    },
    onPostSearch () {
      this.$refs.selectPost.openForm('选择职位')
    },
    SeletedEmp (user) {
      this.entity.EmpName = user[0].NameUser
      this.entity.EmpCode = user[0].EmployeesCode
      this.entity.MobilePhone = user[0].MobilePhone
      this.entity.F_CurrentPosition = user[0].PostName
      this.entity.F_CurrentPositionId = user[0].F_PositionId
      this.entity.F_CurrentRank = user[0].F_Rank
      this.entity.F_Department = user[0].DepartmentName
      this.entity.F_DepartmentId = user[0].F_DepartmentId
      this.entity.F_UserId = user[0].F_Id
      this.entity.F_OriginalEmplStatus = user[0].EmployRelStatus
      this.entity.F_MobilzOriEmplStatus = user[0].EmployRelStatus
      this.entity.F_CurrentProject = user[0].ND
    },
    SeletedPost (post) {
      this.entity.F_MobilizePosition = post[0].F_Name
      this.entity.F_MobilizePositionId = post[0].F_Id
      this.entity.F_MobilizeDep = post[0].DepartmentName
      this.entity.F_MobilizeDepId = post[0].F_DepartmentId
    },
    init () {
      this.visible = true
      this.entity = {
        F_UserId: '',
        EmpName: '',
        EmpCode: '',
        MobilePhone: '',
        F_CurrentPosition: '',
        F_MobilizePosition: '',
        F_MobilizePositionId: '',
        F_Department: '',
        F_MobilizeDep: '',
        F_MobilizeDepId: '',
        F_CurrentRank: '',
        F_MobilizeRank: '',
        F_ChangesDate: null,
        F_Remark: '',
        F_OriginalEmplStatus: '',
        F_MobilzOriEmplStatus: '',
        F_ChangesOperating: '',
        F_ChangesType: '',
        F_ChangesReason: '',
        F_MobilizeProject: '',
        F_CurrentProject: ''
      }
      this.commonApi.dictTypeInfo('任职类型').then(res => {
        this.forTypeDic = res
      })
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    openForm (id, title, isedit) {
      this.title = title
      this.isEdit = isedit
      this.init()
      if (id) {
        this.loading = true
        this.$http.post('/HR_EmployeeInfoManage/HR_TransferInfo/GetTransferInfo', { id: id }).then(resJson => {
          this.loading = false
          if (resJson.Data.F_ChangesDate) {
            resJson.Data.F_ChangesDate = moment(resJson.Data.F_ChangesDate)
          }

          this.entity = resJson.Data
          this.$http.post('/Base_Manage/Base_FileInfo/LoadFiles', { FileFolderId: this.entity.F_FileId }).then(data => {
            this.fileEntity = data
          })
        })
      } else {
        this.$http.post('/Base_Manage/Base_FileInfo/LoadFiles', { FileFolderId: '' }).then(data => {
          this.fileEntity = data
        })
      }
    },
    //保存数据
    save () {
      this.$http
        .post('/Base_Manage/Base_FileInfo/UploadFilesSave', { FileInfo: JSON.stringify(this.fileEntity) })
        .then(data => {
          this.entity.F_FileId = data.F_Id
          this.$refs['form'].validate(valid => {
            if (!valid) {
              return
            }
            this.loading = true
            this.entity.UserId = this.userId
            this.$http.post('/HR_EmployeeInfoManage/HR_TransferInfo/SaveData', this.entity).then(resJson => {
              this.loading = false

              if (resJson.Success) {
                this.$message.success('操作成功!')
                this.visible = false

                this.parentObj.getDataList()
              } else {
                this.$message.error(resJson.Msg)
              }
            })
          })
        })
    },
    //保存并创建流程
    createFlow () {
      this.loading = true
      this.$http
        .post('/Base_Manage/Base_FileInfo/UploadFilesSave', { FileInfo: JSON.stringify(this.fileEntity) })
        .then(data => {
          this.entity.F_FileId = data.F_Id
          this.$refs['form'].validate(valid => {
            if (!valid) {
              return
            }

            this.entity.F_Unit = this.unit
            this.$http.post('/HR_EmployeeInfoManage/HR_TransferInfo/SaveAndCreateFlow', this.entity).then(resJson => {
              this.loading = false

              if (resJson.Success) {
                this.$message.success('创建流程成功!')
                this.visible = false

                this.parentObj.getDataList()
              } else {
                this.$message.error(resJson.Msg)
              }
            })
          })
        })
    }
  }
}
</script>
