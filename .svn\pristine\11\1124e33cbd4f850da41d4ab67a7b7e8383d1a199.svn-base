﻿using Coldairarrow.Entity.ERP_Online;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Util.DataAccess;

namespace Coldairarrow.Business.ERP_Online
{
    public class ERP_ContractOnlineBusiness : BaseBusiness<ERP_ContractOnline>, IERP_ContractOnlineBusiness, ITransientDependency
    {
        public ERP_ContractOnlineBusiness(IBusinessDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<ERP_ContractOnline>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<ERP_ContractOnline>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<ERP_ContractOnline, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<ERP_ContractOnline> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(ERP_ContractOnline data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(ERP_ContractOnline data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<ERP_ContractOnline>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<ERP_ContractOnline, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<ERP_ContractOnline, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        public List<ERP_ContractOnline> GetListByName(string name)
        {
            var q = GetIQueryable().Where(x => x.F_UserName.Contains(name)).Select(x => new ERP_ContractOnline() { 
                  F_Id = x.F_Id,
                  F_ProjectName = x.F_ProjectName,
                  F_ContractNumber = x.F_ContractNumber,
                  F_BookingNumber = x.F_BookingNumber,
                  F_UserName = x.F_UserName,
                  F_Unit = x.F_Unit,
                  F_Building = x.F_Building,
                  F_House = x.F_House,
                  F_Datetime = x.F_Datetime,
                  F_Address = x.F_Address,
                  F_Number = x.F_Number,
                  F_ProjectId = x.F_ProjectId
            }).ToList();
            return q;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}