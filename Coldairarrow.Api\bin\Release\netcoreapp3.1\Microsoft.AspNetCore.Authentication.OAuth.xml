<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Authentication.OAuth</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimAction">
            <summary>
            Infrastructure for mapping user data from a json structure to claims on the ClaimsIdentity.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimAction.#ctor(System.String,System.String)">
            <summary>
            Create a new claim manipulation action.
            </summary>
            <param name="claimType">The value to use for Claim.Type when creating a Claim.</param>
            <param name="valueType">The value to use for Claim.ValueType when creating a Claim.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimAction.ClaimType">
            <summary>
            The value to use for Claim.Type when creating a Claim.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimAction.Run(System.Text.Json.JsonElement,System.Security.Claims.ClaimsIdentity,System.String)">
            <summary>
            Examine the given userData json, determine if the requisite data is present, and optionally add it
            as a new Claim on the ClaimsIdentity.
            </summary>
            <param name="userData">The source data to examine. This value may be null.</param>
            <param name="identity">The identity to add Claims to.</param>
            <param name="issuer">The value to use for Claim.Issuer when creating a Claim.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimActionCollection">
            <summary>
            A collection of ClaimActions used when mapping user data to Claims.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimActionCollection.Clear">
            <summary>
            Remove all claim actions.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimActionCollection.Remove(System.String)">
            <summary>
            Remove all claim actions for the given ClaimType.
            </summary>
            <param name="claimType">The ClaimType of maps to remove.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimActionCollection.Add(Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimAction)">
            <summary>
            Add a claim action to the collection.
            </summary>
            <param name="action">The claim action to add.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authentication.OAuth.Claims.CustomJsonClaimAction">
            <summary>
            A ClaimAction that selects the value from the json user data by running the given Func resolver.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.Claims.CustomJsonClaimAction.#ctor(System.String,System.String,System.Func{System.Text.Json.JsonElement,System.String})">
            <summary>
            Creates a new CustomJsonClaimAction.
            </summary>
            <param name="claimType">The value to use for Claim.Type when creating a Claim.</param>
            <param name="valueType">The value to use for Claim.ValueType when creating a Claim.</param>
            <param name="resolver">The Func that will be called to select value from the given json user data.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.Claims.CustomJsonClaimAction.Resolver">
            <summary>
            The Func that will be called to select value from the given json user data.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.Claims.CustomJsonClaimAction.Run(System.Text.Json.JsonElement,System.Security.Claims.ClaimsIdentity,System.String)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Authentication.OAuth.Claims.DeleteClaimAction">
            <summary>
            A ClaimAction that deletes all claims from the given ClaimsIdentity with the given ClaimType.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.Claims.DeleteClaimAction.#ctor(System.String)">
            <summary>
            Creates a new DeleteClaimAction.
            </summary>
            <param name="claimType">The ClaimType of Claims to delete.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.Claims.DeleteClaimAction.Run(System.Text.Json.JsonElement,System.Security.Claims.ClaimsIdentity,System.String)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Authentication.OAuth.Claims.JsonKeyClaimAction">
            <summary>
            A ClaimAction that selects a top level value from the json user data with the given key name and adds it as a Claim.
            This no-ops if the key is not found or the value is empty.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.Claims.JsonKeyClaimAction.#ctor(System.String,System.String,System.String)">
            <summary>
            Creates a new JsonKeyClaimAction.
            </summary>
            <param name="claimType">The value to use for Claim.Type when creating a Claim.</param>
            <param name="valueType">The value to use for Claim.ValueType when creating a Claim.</param>
            <param name="jsonKey">The top level key to look for in the json user data.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.Claims.JsonKeyClaimAction.JsonKey">
            <summary>
            The top level key to look for in the json user data.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.Claims.JsonKeyClaimAction.Run(System.Text.Json.JsonElement,System.Security.Claims.ClaimsIdentity,System.String)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Authentication.OAuth.Claims.JsonSubKeyClaimAction">
            <summary>
            A ClaimAction that selects a second level value from the json user data with the given top level key
            name and second level sub key name and add it as a Claim.
            This no-ops if the keys are not found or the value is empty.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.Claims.JsonSubKeyClaimAction.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            Creates a new JsonSubKeyClaimAction.
            </summary>
            <param name="claimType">The value to use for Claim.Type when creating a Claim.</param>
            <param name="valueType">The value to use for Claim.ValueType when creating a Claim.</param>
            <param name="jsonKey">The top level key to look for in the json user data.</param>
            <param name="subKey">The second level key to look for in the json user data.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.Claims.JsonSubKeyClaimAction.SubKey">
            <summary>
            The second level key to look for in the json user data.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.Claims.JsonSubKeyClaimAction.Run(System.Text.Json.JsonElement,System.Security.Claims.ClaimsIdentity,System.String)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Authentication.OAuth.Claims.MapAllClaimsAction">
            <summary>
            A ClaimAction that selects all top level values from the json user data and adds them as Claims.
            This excludes duplicate sets of names and values.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Authentication.OAuth.OAuthCreatingTicketContext">
            <summary>
            Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.OAuthCreatingTicketContext.#ctor(System.Security.Claims.ClaimsPrincipal,Microsoft.AspNetCore.Authentication.AuthenticationProperties,Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Authentication.AuthenticationScheme,Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions,System.Net.Http.HttpClient,Microsoft.AspNetCore.Authentication.OAuth.OAuthTokenResponse,System.Text.Json.JsonElement)">
            <summary>
            Initializes a new <see cref="T:Microsoft.AspNetCore.Authentication.OAuth.OAuthCreatingTicketContext"/>.
            </summary>
            <param name="principal">The <see cref="T:System.Security.Claims.ClaimsPrincipal"/>.</param>
            <param name="properties">The <see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/>.</param>
            <param name="context">The HTTP environment.</param>
            <param name="scheme">The authentication scheme.</param>
            <param name="options">The options used by the authentication middleware.</param>
            <param name="backchannel">The HTTP client used by the authentication middleware</param>
            <param name="tokens">The tokens returned from the token endpoint.</param>
            <param name="user">The JSON-serialized user.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthCreatingTicketContext.User">
            <summary>
            Gets the JSON-serialized user or an empty
            <see cref="T:System.Text.Json.JsonElement"/> if it is not available.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthCreatingTicketContext.TokenResponse">
            <summary>
            Gets the token response returned by the authentication service.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthCreatingTicketContext.AccessToken">
            <summary>
            Gets the access token provided by the authentication service.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthCreatingTicketContext.TokenType">
            <summary>
            Gets the access token type provided by the authentication service.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthCreatingTicketContext.RefreshToken">
            <summary>
            Gets the refresh token provided by the authentication service.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthCreatingTicketContext.ExpiresIn">
            <summary>
            Gets the access token expiration time.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthCreatingTicketContext.Backchannel">
            <summary>
            Gets the backchannel used to communicate with the provider.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthCreatingTicketContext.Identity">
            <summary>
            Gets the main identity exposed by the authentication ticket.
            This property returns <c>null</c> when the ticket is <c>null</c>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Authentication.OAuth.OAuthEvents">
            <summary>
            Default implementation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthEvents.OnCreatingTicket">
            <summary>
            Gets or sets the function that is invoked when the CreatingTicket method is invoked.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthEvents.OnRedirectToAuthorizationEndpoint">
            <summary>
            Gets or sets the delegate that is invoked when the RedirectToAuthorizationEndpoint method is invoked.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.OAuthEvents.CreatingTicket(Microsoft.AspNetCore.Authentication.OAuth.OAuthCreatingTicketContext)">
            <summary>
            Invoked after the provider successfully authenticates a user.
            </summary>
            <param name="context">Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.OAuthEvents.RedirectToAuthorizationEndpoint(Microsoft.AspNetCore.Authentication.RedirectContext{Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions})">
            <summary>
            Called when a Challenge causes a redirect to authorize endpoint in the OAuth handler.
            </summary>
            <param name="context">Contains redirect URI and <see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> of the challenge.</param>
        </member>
        <member name="F:Microsoft.AspNetCore.Authentication.OAuth.OAuthChallengeProperties.ScopeKey">
            <summary>
            The parameter key for the "scope" argument being used for a challenge request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthChallengeProperties.Scope">
            <summary>
            The "scope" parameter value being used for a challenge request.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.OAuthChallengeProperties.SetScope(System.String[])">
            <summary>
            Set the "scope" parameter value.
            </summary>
            <param name="scopes">List of scopes.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authentication.OAuth.OAuthCodeExchangeContext">
            <summary>
            Contains information used to perform the code exchange.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.OAuthCodeExchangeContext.#ctor(Microsoft.AspNetCore.Authentication.AuthenticationProperties,System.String,System.String)">
            <summary>
            Initializes a new <see cref="T:Microsoft.AspNetCore.Authentication.OAuth.OAuthCodeExchangeContext"/>.
            </summary>
            <param name="properties">The <see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/>.</param>
            <param name="code">The code returned from the authorization endpoint.</param>
            <param name="redirectUri">The redirect uri used in the authorization request.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthCodeExchangeContext.Properties">
            <summary>
            State for the authentication flow.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthCodeExchangeContext.Code">
            <summary>
            The code returned from the authorization endpoint.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthCodeExchangeContext.RedirectUri">
            <summary>
            The redirect uri used in the authorization request.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Authentication.OAuth.OAuthConstants">
            <summary>
            Constants used in the OAuth protocol
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Authentication.OAuth.OAuthConstants.CodeVerifierKey">
            <summary>
            code_verifier defined in https://tools.ietf.org/html/rfc7636
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Authentication.OAuth.OAuthConstants.CodeChallengeKey">
            <summary>
            code_challenge defined in https://tools.ietf.org/html/rfc7636
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Authentication.OAuth.OAuthConstants.CodeChallengeMethodKey">
            <summary>
            code_challenge_method defined in https://tools.ietf.org/html/rfc7636
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Authentication.OAuth.OAuthConstants.CodeChallengeMethodS256">
            <summary>
            S256 defined in https://tools.ietf.org/html/rfc7636
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthHandler`1.Events">
            <summary>
            The handler calls methods on the events which give the application control at certain points where processing is occurring. 
            If it is not provided a default instance is supplied which does nothing when the methods are called.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.OAuthHandler`1.CreateEventsAsync">
            <summary>
            Creates a new instance of the events instance.
            </summary>
            <returns>A new instance of the events instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.OAuthHandler`1.FormatScope(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Format a list of OAuth scopes.
            </summary>
            <param name="scopes">List of scopes.</param>
            <returns>Formatted scopes.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.OAuthHandler`1.FormatScope">
            <summary>
            Format the <see cref="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions.Scope"/> property.
            </summary>
            <returns>Formatted scopes.</returns>
            <remarks>Subclasses should rather override <see cref="M:Microsoft.AspNetCore.Authentication.OAuth.OAuthHandler`1.FormatScope(System.Collections.Generic.IEnumerable{System.String})"/>.</remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions">
            <summary>
            Configuration options OAuth.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions.Validate">
            <summary>
            Check that the options are valid.  Should throw an exception if things are not ok.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions.ClientId">
            <summary>
            Gets or sets the provider-assigned client id.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions.ClientSecret">
            <summary>
            Gets or sets the provider-assigned client secret.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions.AuthorizationEndpoint">
            <summary>
            Gets or sets the URI where the client will be redirected to authenticate.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions.TokenEndpoint">
            <summary>
            Gets or sets the URI the middleware will access to exchange the OAuth token.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions.UserInformationEndpoint">
            <summary>
            Gets or sets the URI the middleware will access to obtain the user information.
            This value is not used in the default implementation, it is for use in custom implementations of
            IOAuthAuthenticationEvents.Authenticated or OAuthAuthenticationHandler.CreateTicketAsync.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions.Events">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Authentication.OAuth.OAuthEvents"/> used to handle authentication events.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions.ClaimActions">
            <summary>
            A collection of claim actions used to select values from the json user data and create Claims.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions.Scope">
            <summary>
            Gets the list of permissions to request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions.StateDataFormat">
            <summary>
            Gets or sets the type used to secure data handled by the middleware.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.OAuthOptions.UsePkce">
            <summary>
            Enables or disables the use of the Proof Key for Code Exchange (PKCE) standard. See https://tools.ietf.org/html/rfc7636.
            The default value is `false` but derived handlers should enable this if their provider supports it.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.Resources.Exception_OptionMustBeProvided">
            <summary>The '{0}' option must be provided.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.OAuth.Resources.FormatException_OptionMustBeProvided(System.Object)">
            <summary>The '{0}' option must be provided.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authentication.OAuth.Resources.Exception_ValidatorHandlerMismatch">
            <summary>An ICertificateValidator cannot be specified at the same time as an HttpMessageHandler unless it is a WebRequestHandler.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.ClaimActionCollectionMapExtensions.MapJsonKey(Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimActionCollection,System.String,System.String)">
            <summary>
            Select a top level value from the json user data with the given key name and add it as a Claim.
            This no-ops if the key is not found or the value is empty.
            </summary>
            <param name="collection"></param>
            <param name="claimType">The value to use for Claim.Type when creating a Claim.</param>
            <param name="jsonKey">The top level key to look for in the json user data.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.ClaimActionCollectionMapExtensions.MapJsonKey(Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimActionCollection,System.String,System.String,System.String)">
            <summary>
            Select a top level value from the json user data with the given key name and add it as a Claim.
            This no-ops if the key is not found or the value is empty.
            </summary>
            <param name="collection"></param>
            <param name="claimType">The value to use for Claim.Type when creating a Claim.</param>
            <param name="jsonKey">The top level key to look for in the json user data.</param>
            <param name="valueType">The value to use for Claim.ValueType when creating a Claim.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.ClaimActionCollectionMapExtensions.MapJsonSubKey(Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimActionCollection,System.String,System.String,System.String)">
            <summary>
            Select a second level value from the json user data with the given top level key name and second level sub key name and add it as a Claim.
            This no-ops if the keys are not found or the value is empty.
            </summary>
            <param name="collection"></param>
            <param name="claimType">The value to use for Claim.Type when creating a Claim.</param>
            <param name="jsonKey">The top level key to look for in the json user data.</param>
            <param name="subKey">The second level key to look for in the json user data.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.ClaimActionCollectionMapExtensions.MapJsonSubKey(Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimActionCollection,System.String,System.String,System.String,System.String)">
            <summary>
            Select a second level value from the json user data with the given top level key name and second level sub key name and add it as a Claim.
            This no-ops if the keys are not found or the value is empty.
            </summary>
            <param name="collection"></param>
            <param name="claimType">The value to use for Claim.Type when creating a Claim.</param>
            <param name="jsonKey">The top level key to look for in the json user data.</param>
            <param name="subKey">The second level key to look for in the json user data.</param>
            <param name="valueType">The value to use for Claim.ValueType when creating a Claim.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.ClaimActionCollectionMapExtensions.MapCustomJson(Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimActionCollection,System.String,System.Func{System.Text.Json.JsonElement,System.String})">
            <summary>
            Run the given resolver to select a value from the json user data to add as a claim.
            This no-ops if the returned value is empty.
            </summary>
            <param name="collection"></param>
            <param name="claimType">The value to use for Claim.Type when creating a Claim.</param>
            <param name="resolver">The Func that will be called to select value from the given json user data.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.ClaimActionCollectionMapExtensions.MapCustomJson(Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimActionCollection,System.String,System.String,System.Func{System.Text.Json.JsonElement,System.String})">
            <summary>
            Run the given resolver to select a value from the json user data to add as a claim.
            This no-ops if the returned value is empty.
            </summary>
            <param name="collection"></param>
            <param name="claimType">The value to use for Claim.Type when creating a Claim.</param>
            <param name="valueType">The value to use for Claim.ValueType when creating a Claim.</param>
            <param name="resolver">The Func that will be called to select value from the given json user data.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.ClaimActionCollectionMapExtensions.MapAll(Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimActionCollection)">
            <summary>
            Clears any current ClaimsActions and maps all values from the json user data as claims, excluding duplicates.
            </summary>
            <param name="collection"></param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.ClaimActionCollectionMapExtensions.MapAllExcept(Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimActionCollection,System.String[])">
            <summary>
            Clears any current ClaimsActions and maps all values from the json user data as claims, excluding the specified types.
            </summary>
            <param name="collection"></param>
            <param name="exclusions"></param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.ClaimActionCollectionMapExtensions.DeleteClaim(Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimActionCollection,System.String)">
            <summary>
            Delete all claims from the given ClaimsIdentity with the given ClaimType.
            </summary>
            <param name="collection"></param>
            <param name="claimType"></param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authentication.ClaimActionCollectionMapExtensions.DeleteClaims(Microsoft.AspNetCore.Authentication.OAuth.Claims.ClaimActionCollection,System.String[])">
            <summary>
            Delete all claims from the ClaimsIdentity with the given claimTypes.
            </summary>
            <param name="collection"></param>
            <param name="claimTypes"></param>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.OAuthPostConfigureOptions`2">
            <summary>
            Used to setup defaults for the OAuthOptions.
            </summary>
        </member>
    </members>
</doc>
