<template>
  <a-modal
    :title="title"
    width="60%"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="
      () => {
        this.visible = false
      }
    ">
    <a-spin :spinning="loading">
      <a-table
        ref="table"
        :columns="columns"
        :rowKey="row => row.F_Id"
        :dataSource="data"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :scroll="{ x: 1300, y: 500 }"
        :bordered="true">
        <span slot="F_FoundedTime" slot-scope="text">
          <template>
            <span>{{ text | dayjs('YYYY-MM-DD') }}</span>
          </template>
        </span>
      </a-table>
    </a-spin>
  </a-modal>
</template>

<script>
const columns = [
  { title: '公司名称', dataIndex: 'F_FullName', width: 120, ellipsis: true },
  { title: '公司代码', dataIndex: 'F_EnCode', width: 100, ellipsis: true },
  { title: '公司简称', dataIndex: 'F_ShortName', width: 100 },
  { title: '公司性质', dataIndex: 'F_Nature', width: 100 },
  { title: '负责人', dataIndex: 'F_Manager', width: 100 },
  { title: '成立时间', dataIndex: 'F_FoundedTime', width: 120, scopedSlots: { customRender: 'F_FoundedTime' } },
]
export default {
  props: {
    //回调方法，返回选中的数据
    callBack: {
      type: Function,
      default: null
    },
    //是否多选
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 10,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      sorter: { field: 'F_SortCode', order: 'asc' },
      columns,
      queryParam: {},
      visible: false,
      loading: false,
      selectedRowKeys: [],
      selectedRows: [], //选择用户
      entity: {},
      rules: {},
      title: ''
    }
  },
  methods: {
    init () {
      this.visible = true
      this.selectUser = []
    },
    openForm (title) {
      this.title = title
      this.init()
      //查询员工信息
      this.getDataList()
    },
    handleTableChange (pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    //查询信息
    getDataList () {
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/Base_Manage/Base_Company/GetDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'Id',
          SortType: this.sorter.order,
          Search: this.queryParam
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      console.log(selectedRows)
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    handleSubmit () {
      if (this.selectedRows.length == 0) {
        this.$message.warning('必须选中一行')
        return
      }
      if (!this.multiple && this.selectedRows.length > 1) {
        this.$message.warning('只能选择一条数据')
        return
      }
      if (this.callBack) {
        this.visible = false
        this.callBack(this.selectedRows)
      }
    }
  }
}
</script>
