﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Coldairarrow.Entity.Shop_Manage.Model
{
    /// <summary>
    /// 阿里京东详情接口返回
    /// </summary>
    public class AliJDResultModel
    {
        //如果好用，请收藏地址，帮忙分享。
        public class Cf
        {
            /// <summary>
            /// 
            /// </summary>
            public string spl { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string space { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bgc { get; set; }
        }

        public class IconData
        {
            /// <summary>
            /// 
            /// </summary>
            public string isIcon { get; set; }
        }

        public class FloorsItem
        {
            /// <summary>
            /// 
            /// </summary>
            public string floorId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Cf cf { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public IconData data { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int mainFloor { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int sortId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string mId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bId { get; set; }
        }

        public class StockParam
        {
            /// <summary>
            /// 
            /// </summary>
            public int dcId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int sid { get; set; }
        }

        public class PriceInfo
        {
            /// <summary>
            /// 
            /// </summary>
            public string originalPrice { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int priceType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string jprice { get; set; }
        }

        public class ButtonsItem
        {
            /// <summary>
            /// 
            /// </summary>
            public string no { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<string> skuList { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string text { get; set; }
        }

        public class ColorSizeItem
        {
            /// <summary>
            /// 
            /// </summary>
            public List<ButtonsItem> buttons { get; set; }
            /// <summary>
            /// 颜色
            /// </summary>
            public string title { get; set; }
        }

        public class ColorSizeInfo
        {
            /// <summary>
            /// 
            /// </summary>
            public List<ColorSizeItem> colorSize { get; set; }
            /// <summary>
            /// #与其他已选项无法组成可售商品，请重选
            /// </summary>
            public string colorSizeTips { get; set; }
        }

        public class Shop
        {
            /// <summary>
            /// 
            /// </summary>
            public int followCount { get; set; }
            /// <summary>
            /// 高
            /// </summary>
            public string logisticsGrade { get; set; }
            /// <summary>
            /// 高
            /// </summary>
            public string evaluateGrade { get; set; }
            /// <summary>
            /// 评价
            /// </summary>
            public string evaluateTxt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string evaluateScore { get; set; }
            /// <summary>
            /// 售后
            /// </summary>
            public string afterSaleTxt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string afterSaleScore { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isFocusShop { get; set; }
            /// <summary>
            /// 物流
            /// </summary>
            public string logisticsText { get; set; }
            /// <summary>
            /// 高
            /// </summary>
            public string afterSaleGrade { get; set; }
            /// <summary>
            /// 关注人数
            /// </summary>
            public string followText { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int totalNum { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string logisticsScore { get; set; }
            /// <summary>
            /// 盒马官方旗舰店
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string logo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int shopId { get; set; }
            /// <summary>
            /// 全部商品
            /// </summary>
            public string skuCntText { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string squareLogo { get; set; }
        }

        public class CustomerService
        {
            /// <summary>
            /// 
            /// </summary>
            public string hasChat { get; set; }
        }

        public class ShopInfo
        {
            /// <summary>
            /// 
            /// </summary>
            public Shop shop { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public CustomerService customerService { get; set; }
        }

        public class EventParam
        {
            /// <summary>
            /// {"area":"1_72_4137","sku":[["10084690595993","39.90","现货，现在下单，预计12月27日发货","33","0"]]}
            /// </summary>
            public string sep { get; set; }
        }

        public class YuyueInfo
        {
            /// <summary>
            /// 
            /// </summary>
            public string isYuYue { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int buyEndDate { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int yuyueNum { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isbuyTime { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int plusType { get; set; }
        }

        public class WareImageItem
        {
            /// <summary>
            /// 
            /// </summary>
            public string small { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string big { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string share { get; set; }
        }

        public class AddAndSubToast
        {
            /// <summary>
            /// 最少购买1件哦！
            /// </summary>
            public string lowestToastText { get; set; }
        }

        public class Property
        {
            /// <summary>
            /// 
            /// </summary>
            public string isJzfp { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int suitABTest { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string venderStore { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int buyMaxNum { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string easyBuy { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string msbybt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isBuyCode { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isRegisterUser { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isXnzt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isShowShopNameB { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isSpellPurchase { get; set; }
            /// <summary>
            /// https://gamerecg.m.jd.com?skuId=10084690595993&chargeType=31648&skuName=盒马MAX咸蛋黄肉松锅巴 1kg /袋&skuPrice=39.90
            /// </summary>
            public string virtualCardUrl { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public AddAndSubToast addAndSubToast { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isEasyBuyPrice { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isCollect { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string recTabEnable { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isJx { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string cartFlag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int lowestBuyNum { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isFQY { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isPop { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string chatUrl { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string shareUrl { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isOP { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string category { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isEncrypt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isRegularPrice { get; set; }
        }

        public class WareInfo
        {
            /// <summary>
            /// 盒马MAX咸蛋黄肉松锅巴 1kg /袋
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string venderId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string skuId { get; set; }
        }

        public class PromotionInfo
        {
            /// <summary>
            /// 
            /// </summary>
            public string prompt { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bargain { get; set; }
        }

        public class Others
        {
            /// <summary>
            /// 
            /// </summary>
            public StockParam stockParam { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public PriceInfo priceInfo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public ColorSizeInfo colorSizeInfo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public ShopInfo shopInfo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public EventParam eventParam { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public YuyueInfo yuyueInfo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<WareImageItem> wareImage { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Property property { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public WareInfo wareInfo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public PromotionInfo promotionInfo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string priceLabel { get; set; }
        }

        public class WareData
        {
            /// <summary>
            /// 
            /// </summary>
            public string code { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public List<FloorsItem> floors { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Others others { get; set; }
        }

        public class WareConfig
        {
            /// <summary>
            /// 
            /// </summary>
            public string phoneCodeType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string okType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isGameCharge { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string fqyType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string bybtType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string yuYueType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string hyjType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string jingXuanType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string locTypeValue { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isJdOtc { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string yuShouType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string otcType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string samType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string maintainFlag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string joinBuyType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isOtc { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string showNinePictures { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string timeOrderType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string actualServType { get; set; }
            /// <summary>
            /// 立即购买
            /// </summary>
            public string kpl_cart_msg { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string miaoShaType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string miniProductSwitch { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isCustomize { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string kpl_cart_flag { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string isOilCard { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int isRealShowGLB { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string locType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string flashPurchaseType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string numberCardType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string jdNumberCardType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string buyCodeType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string xnztType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string hideCart { get; set; }
        }

        public class WData
        {
            /// <summary>
            /// 
            /// </summary>
            public WareData wareData { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public WareConfig wareConfig { get; set; }
        }

        public class Data
        {
            /// <summary>
            /// 
            /// </summary>
            public string code { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public WData data { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string desPin { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string sid { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string tracerId { get; set; }
        }

        public class JDResultRoot
        {
            /// <summary>
            /// 
            /// </summary>
            public Data data { get; set; }
            /// <summary>
            /// 成功
            /// </summary>
            public string msg { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string success { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int code { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string taskNo { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string charge { get; set; }
        }

    }
}
