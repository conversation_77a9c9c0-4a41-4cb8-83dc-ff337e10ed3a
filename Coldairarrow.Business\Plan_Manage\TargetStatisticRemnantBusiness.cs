﻿using Coldairarrow.Entity.Plan_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Util.DataAccess;
using System;

namespace Coldairarrow.Business.Plan_Manage
{
    public class TargetStatisticRemnantBusiness : BaseBusiness<TargetStatisticRemnant>, ITargetStatisticRemnantBusiness, ITransientDependency
    {
        public TargetStatisticRemnantBusiness(IERPDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<TargetStatisticRemnant>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<TargetStatisticRemnant>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<TargetStatisticRemnant, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<TargetStatisticRemnant> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(TargetStatisticRemnant data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(TargetStatisticRemnant data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<TargetStatisticRemnant>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<TargetStatisticRemnant, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<A01_TargetStatisticRemnant, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 运营驾驶舱数据
        public async Task<TargetStatisticRemnantDTO> GetTargetStatisticRemnant(DateTime? date)
        {
            date = date ?? DateTime.Now;
            var dateStr = date.Value.ToString("yyyy-MM-dd");
            TargetStatisticRemnantDTO targetStatistic = new TargetStatisticRemnantDTO();
            //获取各项目得数据
            targetStatistic.projectDatas = await this.Db.GetListBySqlAsync<TargetStatisticRemnant>(@$"SELECT TOP 3 *
              FROM [dbo].[A01_TargetStatisticRemnant]
            WHERE ID IN (
             SELECT * FROM ( SELECT TOP 1 ID 
              FROM [dbo].[A01_TargetStatisticRemnant]
            WHERE TeamProjGUID =N'8baf9a96-1647-43cd-9bcd-067448bd10c9' 
            and CONVERT(varchar,[CreateDate],23)='{dateStr}'
            ORDER BY ID DESC) t1
              UNION ALL 
             SELECT * FROM  ( SELECT TOP 1 ID 
              FROM [dbo].[A01_TargetStatisticRemnant] 
            WHERE TeamProjGUID =N'1980EA50-A241-4096-8057-18A97E605DA9'
             and CONVERT(varchar,[CreateDate],23)='{dateStr}'
            ORDER BY ID DESC) t2
                UNION ALL
              SELECT * FROM  (  SELECT TOP 1 ID 
              FROM [dbo].[A01_TargetStatisticRemnant]
            WHERE TeamProjGUID =N'DACC9453-86DA-4D09-BA92-621B0EC33967' 
            and CONVERT(varchar,[CreateDate],23)='{dateStr}'
            ORDER BY ID DESC) t3)");
            //获取全项目得数据
            var targets = await this.Db.GetListBySqlAsync<TargetStatisticRemnant>(@$"SELECT  max(CreateDate) CreateDate,
   sum([可研均价])  [可研均价]
      , sum([年初均价])  [年初均价]
      , sum([成交均价])  [成交均价]
      , sum([最低价均价])  [最低价均价]
      , sum([可研均价车])  [可研均价车]
      , sum([年初均价车])  [年初均价车]
      , sum([成交均价车])  [成交均价车]
      , sum([最低价均价车])  [最低价均价车]
      , sum([可研均价商业])  [可研均价商业]
      , sum([年初均价商业])  [年初均价商业]
      , sum([成交均价商业])  [成交均价商业]
      , sum([最低价均价商业])  [最低价均价商业]
      , sum([可研均价住宅])  [可研均价住宅]
      , sum([年初均价住宅])  [年初均价住宅]
      , sum([成交均价住宅])  [成交均价住宅]
      , sum([最低价均价住宅])  [最低价均价住宅]
      , sum([可研均价写字楼])  [可研均价写字楼]
      , sum([年初均价写字楼])  [年初均价写字楼]
      , sum([成交均价写字楼])  [成交均价写字楼]
      , sum([最低价均价写字楼])  [最低价均价写字楼]
        ,sum([在售货值])  [在售货值]
      ,sum([在售货值车])  [在售货值车]
      ,sum([签约金额车]) [签约金额车]
      ,sum([在售货值商业]) [在售货值商业]
      ,sum([签约金额商业])  [签约金额商业]
      ,sum([在售货值住宅]) [在售货值住宅]
      ,sum([签约金额住宅]) [签约金额住宅] 
 ,sum([在售货值写字楼]) [在售货值写字楼] 
 ,sum([签约金额写字楼]) [签约金额写字楼] 
FROM [dbo].[A01_TargetStatisticRemnant]
WHERE ID IN (
 SELECT * FROM ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticRemnant] 
WHERE TeamProjGUID =N'8baf9a96-1647-43cd-9bcd-067448bd10c9' 
      and CONVERT(varchar,[CreateDate],23)='{dateStr}'
ORDER BY ID DESC) t1
  UNION ALL 
 SELECT * FROM  ( SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticRemnant] 
WHERE TeamProjGUID =N'1980EA50-A241-4096-8057-18A97E605DA9' 
      and CONVERT(varchar,[CreateDate],23)='{dateStr}'
ORDER BY ID DESC) t2
    UNION ALL
  SELECT * FROM  (  SELECT TOP 1 ID 
  FROM [dbo].[A01_TargetStatisticRemnant] 
WHERE TeamProjGUID =N'DACC9453-86DA-4D09-BA92-621B0EC33967' 
      and CONVERT(varchar,[CreateDate],23)='{dateStr}'
ORDER BY ID DESC) t3)");
            targetStatistic.allProjectData = targets.FirstOrDefault();
            return targetStatistic;
        }
        #endregion

        #region 私有成员

        #endregion

    }
}