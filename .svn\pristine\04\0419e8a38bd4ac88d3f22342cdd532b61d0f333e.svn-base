﻿using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Util.Helper;
using System;
using System.Collections.Concurrent;
using NPOI.SS.Formula.Functions;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;
using Castle.Core.Internal;
using Coldairarrow.Util.UEditor;
using static Microsoft.AspNetCore.Hosting.Internal.HostingApplication;
using System.Reflection;
using static Coldairarrow.Entity.Enum.HWEnum;

namespace Coldairarrow.Business.Base_Manage
{
    public class Base_HWIpDetailBusiness : BaseBusiness<Base_HWIpDetail>, IBase_HWIpDetailBusiness, ITransientDependency
    {
        public Base_HWIpDetailBusiness(IDbAccessor db, IBase_HWIpAddressBusiness base_HWIpAddressBus)
            : base(db)
        {
            _base_HWIpAddressBus = base_HWIpAddressBus;

        }
        IBase_HWIpAddressBusiness _base_HWIpAddressBus { get; }
        #region 外部接口

        public async Task<PageResult<Base_HWIpDetail>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Base_HWIpDetail>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Base_HWIpDetail, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Base_HWIpDetail> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(Base_HWIpDetail data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Base_HWIpDetail data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Base_HWIpDetail>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Base_HWIpDetail, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<Base_HWIpDetail, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        /// <summary>
        /// 保存excel数据
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public async Task SaveDataByExcel(List<Base_HWIpDetail> list)
        {
            try
            {
                //近5000条数据去重
                //var recentRecords = await GetRecentRecordsAsync(5000).ConfigureAwait(false);

                //var concurrentBase_HWIpDetails = new ConcurrentDictionary<(DateTime? F_StrartTime, string F_SA, string F_TA, string F_Apply), Base_HWIpDetail>();

                //foreach (var item in recentRecords)
                //{
                //    concurrentBase_HWIpDetails.TryAdd((item.F_StrartTime, item.F_SA, item.F_TA, item.F_Apply), item);
                //}

                //var filteredList = list.AsParallel()
                //                       .Where(item => !concurrentBase_HWIpDetails.ContainsKey((item.F_StrartTime, item.F_SA, item.F_TA, item.F_Apply)))
                //                       .ToList();

                //if (filteredList.Any())
                //{
                //    //filteredList.ForEach(item =>
                //    //{
                //    //    item.F_Id = GuidHelper.GenerateKey();
                //    //    item.F_CreateDate = DateTime.Now;

                //    //});

                //     this.Db.BulkInsert(filteredList);

                //    // 清理过期数据
                //    //var cutoffDate = DateTime.Now.AddMonths(-1);
                //    //await this.DeleteAsync(x => x.F_CreateDate < cutoffDate).ConfigureAwait(false);
                //    //await this.Db.DeleteAsync<Base_HistoryLogRecord>(x => x.OpTime < cutoffDate).ConfigureAwait(false);
                //}
                //判断某些特定的规则直接黑名单拉黑
                var ruleList = list.Where(x => x.F_Region == "阿里云规则触发").ToList();
                if (ruleList.Any())
                {
                    await this.getRuleLockIps(ruleList);
                }

                await this.Db.InsertAsync(list).ConfigureAwait(false);
                //判断是否有黑名单里的数据
                await getLockIpByBlackList().ConfigureAwait(false); ;
                //await this.UpdateByNoCityData();
            }
            catch (Exception ex)
            {
                throw new Exception("保存excel数据异常:" + ex.Message);
                throw;
            }
        }
        /// <summary>
        /// 阿里云防护规则判断特定状态拉黑
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task getRuleLockIps(List<Base_HWIpDetail> list)
        {
            var lockRuleIds = await this.Db.GetIQueryable<Base_HWAliYunRule>()
                                     .Where(x => x.F_RuleLock == "拉黑")
                                     .Select(x => x.F_RuleID)
                                     .ToListAsync()
                                     .ConfigureAwait(false);
            if (lockRuleIds.Any())
            {
               var whiteIps =await this.Db.GetIQueryable<Base_HWWhiteIp>().ToListAsync().ConfigureAwait(false);
                var lockIps = list.Where(x => x.F_Region == "阿里云规则触发"  && lockRuleIds.Contains(x.F_Action)&& !whiteIps.Select(x=>x.F_Ip).Contains(x.F_SA))
                          .Select(x => x.F_SA)
                          .Distinct()
                          .ToList();
                if (lockIps.Any())
                {
                    //查询是否有这些封禁ip
                    var baseIpLocks = await this.Db.GetIQueryable<Base_IpLock>()
                                        //.Where(x =>!(x.F_LockTime.HasValue&&x.F_LockTime.Value.ToString("yyyy-MM-dd")==DateTime.Now.ToString("yyyy-MM-dd")))
                                         .ToListAsync()
                                         .ConfigureAwait(false);
                    //如果有就更新状态
                    var updateLockIps = baseIpLocks.Where(x => lockIps.Contains(x.F_IP))
                                             .Select(x => new Base_IpLock
                                             {
                                                 F_Id = x.F_Id,
                                                 F_IsLock = 0,
                                                 F_IP=x.F_IP,
                                                 F_CreateDate=DateTime.Now,
                                                 F_Ctity=x.F_Ctity,
                                                 F_Type= HWTypeEnum.阿里云规则防护,
                                                 F_LockTime = null,
                                                 F_System = "阿里云规则触发"
                                             })
                                             .ToList();
                    if (updateLockIps.Any())
                    {
                        await this.Db.UpdateAsync(updateLockIps).ConfigureAwait(false);
                    }
                    //如果没有就新增
                    var addIpLocks = lockIps.Except(baseIpLocks.Select(x => x.F_IP))
                                           .Where(IpHelper.IsValidIPv4Address)
                                           .Select(ip => new Base_IpLock
                                           {
                                               F_Id = GuidHelper.GenerateKey(),
                                               F_CreateDate = DateTime.Now,
                                               F_Type = HWTypeEnum.阿里云规则防护,
                                               F_IP = ip,
                                               F_IsLock = 0,
                                               F_LockTime = null,
                                               F_System = "阿里云规则触发"
                                           })
                                           .ToList();

                    if (addIpLocks.Any())
                    {
                        await this.Db.InsertAsync(addIpLocks).ConfigureAwait(false);
                    }
                }
            }
        }
        /// <summary>
        /// 更新未绑定城市经纬度的ip
        /// </summary>
        /// <returns></returns>
        public async Task UpdateByNoCityData()
        {
            // 查询未有城市的ip
            var base_HWIpDetails = await GetIQueryable()
                .Where(x => string.IsNullOrEmpty(x.F_SACtity) || string.IsNullOrWhiteSpace(x.F_SACtity))
                .OrderByDescending(x => x.F_CreateDate)
                .Take(5000)
                .ToListAsync()
                .ConfigureAwait(false);

            // 查询安全告警未有城市的ip
            var base_HWIpSafeDetails = await this.Db.GetIQueryable<Base_HWIpSafeDetail>()
                .Where(x => string.IsNullOrEmpty(x.F_Region) || string.IsNullOrWhiteSpace(x.F_Region))
                .OrderByDescending(x => x.F_CreateDate)
                .Take(1000)
                .ToListAsync()
                .ConfigureAwait(false);
            // 先在 ip 库里面查询
            var ipAddresses = this.Db.GetIQueryable<Base_HWIpAddress>();
            if (base_HWIpDetails.Count > 0)
            {
                var details = await GetIpAddressInfo(base_HWIpDetails).ConfigureAwait(false);
                var ipSafedetails = await GetIpAddressInfoBySafe(base_HWIpSafeDetails).ConfigureAwait(false);
                List<Base_HWIpAddress> _HWIpAddresses = new List<Base_HWIpAddress>();
                // 添加延迟
                //await Task.Delay(50000); // 等待 5 秒
                await this.Db.UpdateAsync(details._HWIpDetails);
                // 添加延迟
                await this.Db.UpdateAsync(ipSafedetails._HWIpDetails);
                _HWIpAddresses.AddRange(details._HWIpAddresses);
                _HWIpAddresses.AddRange(ipSafedetails._HWIpAddresses);
                if (_HWIpAddresses.Count > 0)
                {
                    _HWIpAddresses = _HWIpAddresses.Distinct().ToList();
                    _HWIpAddresses = _HWIpAddresses.Where(x => !_HWIpAddresses.Select(ipitem => ipitem.F_IP).Contains(x.F_IP)).ToList();
                    if (_HWIpAddresses.Count > 0)
                    {
                        _HWIpAddresses.ForEach(item =>
                            {
                                item.F_Id = Guid.NewGuid().ToString();
                                item.F_Time = DateTime.Now;
                            });
                        //    // 新增 IP 地址
                        await _base_HWIpAddressBus.BatchInsertDataAsync(_HWIpAddresses).ConfigureAwait(false);
                    }
                }
            }
        }
        /// <summary>
        /// 批量获取 ip 地址信息
        /// </summary>
        /// <param name="base_HWIps"></param>
        /// <returns></returns>
        public async Task<Base_HWIpDetailAddressInfo> GetIpAddressInfo(List<Base_HWIpDetail> base_HWIps)
        {
            Base_HWIpDetailAddressInfo base_HWIpDetail = new Base_HWIpDetailAddressInfo
            {
                _HWIpDetails = new List<Base_HWIpDetail>(),
                _HWIpAddresses = new List<Base_HWIpAddress>()
            };
            if (base_HWIps.Any())
            {

                // 先在 ip 库里面查询
                var ipAddresses = this.Db.GetIQueryable<Base_HWIpAddress>();

                // 收集需要查询的 IP 地址
                var ipsToQuery = base_HWIps
                    .SelectMany(item => new[] { item.F_SA, item.F_TA })
                    .Where(ip => !ip.IsNullOrWhiteSpace())
                    .Distinct()
                    .ToList();

                // 一次性查询所有 IP 地址的信息
                var ipInfos = await ipAddresses
                    .Where(ip => ipsToQuery.Contains(ip.F_IP))
                    .ToListAsync()
                    .ConfigureAwait(false);

                // 创建一个字典，用于快速查找 IP 地址信息
                var uniqueIpInfos = ipInfos.GroupBy(ip => ip.F_IP).Select(g => g.First()).ToList();
                var ipInfoDictionary = uniqueIpInfos.ToDictionary(ip => ip.F_IP, ip => ip);

                // 更新每个 IP 地址的信息
                var ipAddressDetails = new List<Base_HWIpAddress>(); // 初始化列表

                var tasks = base_HWIps.Select(async item =>
                {
                    var saDynamic = await UpdateIpInfo(item, ipInfoDictionary, item.F_SA, "F_SACtity", "F_SALat", "F_SALng");
                    var taDynamic = await UpdateIpInfo(item, ipInfoDictionary, item.F_TA, "F_TACtity", "F_TALat", "F_TALng");

                    if (saDynamic != null)
                    {
                        ipAddressDetails.Add(saDynamic);
                    }
                    if (taDynamic != null)
                    {
                        ipAddressDetails.Add(taDynamic);
                    }

                    return item;
                });

                var details = await Task.WhenAll(tasks).ConfigureAwait(false);
                base_HWIpDetail._HWIpAddresses = ipAddressDetails;
                base_HWIpDetail._HWIpDetails = details.ToList();
                //if (ipAddressDetails.Count > 0)
                //{

                //    // 新增 IP 地址
                //    await _base_HWIpAddressBus.BatchInsertDataAsync(ipAddressDetails).ConfigureAwait(false);
                //}

                return base_HWIpDetail;
            }

            return base_HWIpDetail;
        }
        /// <summary>
        /// 更新单个ip
        /// </summary>
        /// <param name="item"></param>
        /// <param name="ipInfoDictionary"></param>
        /// <param name="ip"></param>
        /// <param name="cityPropertyName"></param>
        /// <param name="latPropertyName"></param>
        /// <param name="lngPropertyName"></param>
        /// <returns></returns>
        private async Task<Base_HWIpAddress?> UpdateIpInfo(Base_HWIpDetail item, Dictionary<string, Base_HWIpAddress> ipInfoDictionary, string ip, string cityPropertyName, string latPropertyName, string lngPropertyName)
        {
            if (string.IsNullOrWhiteSpace(ip) || IpHelper.IsValidIPv4Address(ip))
            {
                return null;
            }

            // 获取属性信息
            PropertyInfo? cityProperty = item.GetType().GetProperty(cityPropertyName);
            PropertyInfo? latProperty = item.GetType().GetProperty(latPropertyName);
            PropertyInfo? lngProperty = item.GetType().GetProperty(lngPropertyName);

            if (ipInfoDictionary.TryGetValue(ip, out var ipAddress))
            {
                // 更新实体属性
                cityProperty?.SetValue(item, ipAddress.F_Ctity);
                latProperty?.SetValue(item, ipAddress.F_Lat);
                lngProperty?.SetValue(item, ipAddress.F_Lng);
                return null;
            }
            else
            {
                var ipInfo = await IpHelper.GetCityModelByIpAsync(ip);
                if (ipInfo != null)
                {
                    // 更新实体属性
                    cityProperty?.SetValue(item, ipInfo.city);
                    latProperty?.SetValue(item, ipInfo.lat);
                    lngProperty?.SetValue(item, ipInfo.lng);

                    // 创建并返回新的 Base_HWIpAddress 实体
                    return new Base_HWIpAddress
                    {
                        //F_Id = Guid.NewGuid().ToString(),
                        F_IP = ip,
                        F_Ctity = ipInfo.city,
                        F_Lat = ipInfo.lat,
                        F_Lng = ipInfo.lng,
                        //F_Time = DateTime.Now
                    };
                }
            }

            return null;
        }    /// <summary>
             /// <summary>
             /// 批量获取 ip 地址信息
             /// </summary>
             /// <param name="base_HWIps"></param>
             /// <returns></returns>
        public async Task<Base_HWIpSafeDetailInfo> GetIpAddressInfoBySafe(List<Base_HWIpSafeDetail> base_HWIps)
        {
            Base_HWIpSafeDetailInfo base_HWIpDetail = new Base_HWIpSafeDetailInfo
            {
                _HWIpDetails = new List<Base_HWIpSafeDetail>(),
                _HWIpAddresses = new List<Base_HWIpAddress>()
            };
            if (base_HWIps.Any())
            {
                // 先在 ip 库里面查询
                var ipAddresses = this.Db.GetIQueryable<Base_HWIpAddress>();

                // 收集需要查询的 IP 地址
                var ipsToQuery = base_HWIps
                    .SelectMany(item => new[] { item.F_SA, item.F_TA })
                    .Where(ip => !ip.IsNullOrWhiteSpace())
                    .Distinct()
                    .ToList();

                // 一次性查询所有 IP 地址的信息
                var ipInfos = await ipAddresses
                    .Where(ip => ipsToQuery.Contains(ip.F_IP))
                    .ToListAsync()
                    .ConfigureAwait(false);

                // 创建一个字典，用于快速查找 IP 地址信息
                var uniqueIpInfos = ipInfos.GroupBy(ip => ip.F_IP).Select(g => g.First()).ToList();
                var ipInfoDictionary = uniqueIpInfos.ToDictionary(ip => ip.F_IP, ip => ip);

                // 更新每个 IP 地址的信息
                var ipAddressDetails = new List<Base_HWIpAddress>(); // 初始化列表

                var tasks = base_HWIps.Select(async item =>
                {
                    var saDynamic = await UpdateIpInfoBySafe(item, ipInfoDictionary, item.F_SA, "F_Region", "F_WD", "F_JD");
                    var taDynamic = await UpdateIpInfoBySafe(item, ipInfoDictionary, item.F_TA, "F_TA_Region", "F_TA_WD", "F_TA_JD");

                    if (saDynamic != null)
                    {
                        ipAddressDetails.Add(saDynamic);
                    }
                    if (taDynamic != null)
                    {
                        ipAddressDetails.Add(taDynamic);
                    }

                    return item;
                });

                var details = await Task.WhenAll(tasks).ConfigureAwait(false);

                base_HWIpDetail._HWIpAddresses = ipAddressDetails;
                base_HWIpDetail._HWIpDetails = details.ToList();

                return base_HWIpDetail;
            }

            return base_HWIpDetail;
        }
        /// <summary>
        /// 更新单个ip
        /// </summary>
        /// <param name="item"></param>
        /// <param name="ipInfoDictionary"></param>
        /// <param name="ip"></param>
        /// <param name="cityPropertyName"></param>
        /// <param name="latPropertyName"></param>
        /// <param name="lngPropertyName"></param>
        /// <returns></returns>
        private async Task<Base_HWIpAddress?> UpdateIpInfoBySafe(Base_HWIpSafeDetail item, Dictionary<string, Base_HWIpAddress> ipInfoDictionary, string ip, string cityPropertyName, string latPropertyName, string lngPropertyName)
        {
            if (string.IsNullOrWhiteSpace(ip) || IpHelper.IsValidIPv4Address(ip))
            {
                return null;
            }

            // 获取属性信息
            PropertyInfo? cityProperty = item.GetType().GetProperty(cityPropertyName);
            PropertyInfo? latProperty = item.GetType().GetProperty(latPropertyName);
            PropertyInfo? lngProperty = item.GetType().GetProperty(lngPropertyName);

            if (ipInfoDictionary.TryGetValue(ip, out var ipAddress))
            {
                // 更新实体属性
                cityProperty?.SetValue(item, ipAddress.F_Ctity);
                latProperty?.SetValue(item, ipAddress.F_Lat);
                lngProperty?.SetValue(item, ipAddress.F_Lng);
                return null;
            }
            else
            {
                var ipInfo = await IpHelper.GetCityModelByIpAsync(ip);
                if (ipInfo != null)
                {
                    // 更新实体属性
                    cityProperty?.SetValue(item, ipInfo.city);
                    latProperty?.SetValue(item, ipInfo.lat);
                    lngProperty?.SetValue(item, ipInfo.lng);

                    // 创建并返回新的 Base_HWIpAddress 实体
                    return new Base_HWIpAddress
                    {
                        //F_Id = Guid.NewGuid().ToString(),
                        F_IP = ip,
                        F_Ctity = ipInfo.city,
                        F_Lat = ipInfo.lat,
                        F_Lng = ipInfo.lng,
                        //F_Time = DateTime.Now
                    };
                }
            }

            return null;
        }


        /// <summary>
        ///批量获取ip地址信息
        /// </summary>
        /// <param name="base_HWIps"></param>
        /// <returns></returns>
        public async Task getLockIpByBlackList()
        {
            var sql = @"
              SELECT ip.F_IP 
        FROM Base_IpLock ip
        WHERE ip.F_IsLock = 1
          AND ip.F_System LIKE '%手动上传封禁%'
          AND EXISTS (
              SELECT 1
              FROM Base_HWIpDetail hw
              WHERE hw.F_SA = ip.F_IP
                AND hw.F_CreateDate >= DATEADD(minute, -30, GETDATE())
                AND hw.F_IsRead = 0
                AND hw.[F_Action] LIKE '%允许%'
          )
          AND CONVERT(varchar(10), ip.F_LockTime, 121) != CONVERT(varchar(10), GETDATE(), 121);
            ";

            //var ipLocks = await this.Db.GetIQueryable<Base_IpLock>().Where(x => x.F_IsLock == 1 && x.F_System.Contains("手动上传封禁")).Distinct().ToListAsync().ConfigureAwait(false);
            //var base_HWIpDetails = await GetIQueryable()
            //               .Where(x => x.F_CreateDate >= DateTime.Now.AddMinutes(-30) && (x.F_IsRead == 0) && ipLocks.Select(item => item.F_IP).Contains(x.F_SA))
            //               .Select(x => x.F_SA)
            //               .Distinct()
            //               .ToListAsync()
            //               .ConfigureAwait(false);

            var blackListIps = this.Db.GetListBySql<Base_IpLock>(sql)
                               .Distinct()
                                .ToList();

            if (blackListIps.Any())
            {
                var ipLocksToUpdate = await this.Db.GetIQueryable<Base_IpLock>()
                                             .Where(item => blackListIps.Select(x => x.F_IP).Contains(item.F_IP))
                                             .Select(x => new Base_IpLock
                                             {
                                                 F_Id = x.F_Id,
                                                 F_IsLock = 0,
                                                 F_IP = x.F_IP,
                                                 F_CreateDate = DateTime.Now,
                                                 F_Ctity = x.F_Ctity,
                                                 F_Type =x.F_Type,
                                                 F_LockTime = null,
                                                 F_System = x.F_System
                                             })
                                             .ToListAsync()
                                             .ConfigureAwait(false);

                await this.Db.UpdateAsync(ipLocksToUpdate).ConfigureAwait(false);
            }
        }
        private async Task<IEnumerable<Base_HWIpDetail>> GetRecentRecordsAsync(int count)
        {
            return await GetIQueryable()
                          .Where(x => x.F_Region == "阿里云防护")
                          .OrderByDescending(x => x.F_CreateDate)
                          .Take(count)
                          .ToListAsync()
                          .ConfigureAwait(false);
        }
        private async Task<IEnumerable<Base_HWIpDetail>> GetRecordsAllAsync(int count)
        {
            return await GetIQueryable()
                          .OrderByDescending(x => x.F_CreateDate)
                          .Take(count)
                          .ToListAsync()
                          .ConfigureAwait(false);
        }
        #endregion

        #region 私有成员

        #endregion
    }
    public class Base_HWIpDetailAddressInfo
    {
        public List<Base_HWIpDetail> _HWIpDetails { get; set; }
        public List<Base_HWIpAddress> _HWIpAddresses { get; set; }
    }
    public class Base_HWIpSafeDetailInfo
    {
        public List<Base_HWIpSafeDetail> _HWIpDetails { get; set; }
        public List<Base_HWIpAddress> _HWIpAddresses { get; set; }
    }
}