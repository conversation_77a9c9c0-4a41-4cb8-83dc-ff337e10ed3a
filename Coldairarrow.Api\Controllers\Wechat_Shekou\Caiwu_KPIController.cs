﻿using Coldairarrow.Business.Wechat_Shekou;
using Coldairarrow.Entity.Wechat_Shekou;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;

namespace Coldairarrow.Api.Controllers.Wechat_Shekou
{
    [Route("/Wechat_Shekou/[controller]/[action]")]
    public class Caiwu_KPIController : BaseApiController
    {
        #region DI

        public Caiwu_KPIController(ICaiwu_KPIBusiness caiwu_KPIBus, ICaiwu_ThreeBusiness caiwu_ThreeBus, ICaiwu_BankBusiness caiwu_BankBus,ICaiwu_RecoveryBusiness caiwu_RecoveryBus)
        {
            _caiwu_KPIBus = caiwu_KPIBus;
            _caiwu_ThreeBus = caiwu_ThreeBus;
            _caiwu_BankBus = caiwu_BankBus;
            _caiwu_RecoveryBus = caiwu_RecoveryBus;
        }

        ICaiwu_KPIBusiness _caiwu_KPIBus { get; }
        ICaiwu_ThreeBusiness _caiwu_ThreeBus { get; }
        ICaiwu_BankBusiness _caiwu_BankBus { get; }

        ICaiwu_RecoveryBusiness _caiwu_RecoveryBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Caiwu_KPI>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _caiwu_KPIBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Caiwu_KPI> GetTheData(IdInputDTO input)
        {
            return await _caiwu_KPIBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Caiwu_KPI data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _caiwu_KPIBus.AddDataAsync(data);
            }
            else
            {
                await _caiwu_KPIBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _caiwu_KPIBus.DeleteDataAsync(ids);
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _caiwu_KPIBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Caiwu_KPI>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _caiwu_KPIBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion

        #region 二开
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetMiniData()
        {
            var kpilist =_caiwu_KPIBus.GetCaiwu_KPIs();
            var banklist = _caiwu_BankBus.GetCQBank();
            var threelist = _caiwu_ThreeBus.GetCaiwuThree();
            var recoverylist = _caiwu_RecoveryBus.GetCaiwu_Recovery();
            var quanyilist = new List<Caiwu_KPIDTO>();
            var jingshlist = new List<Caiwu_KPIDTO>();
            var quanyi = new Caiwu_KPI();
            var jingsh = new Caiwu_KPI();
            foreach (var i in kpilist)
            {
                var item = new Caiwu_KPIDTO() {
                    F_YearGap = i.F_YearKPI - i.F_YearValue,
                    F_YearRate = Math.Round((Double)i.F_YearValue * 100 / (Double)i.F_YearKPI, 2),
                    F_QuarterGap = i.F_QuarterKPI - i.F_QuarterValue,
                    F_QuarterRate = Math.Round((Double)i.F_QuarterValue * 100 / (Double)i.F_QuarterKPI, 2) ,
                    F_MonthGap = i.F_MonthKPI - i.F_MonthValue,
                    F_MonthRate = Math.Round((Double)i.F_MonthValue * 100 / (Double)i.F_MonthKPI, 2) ,
                    F_MonthExpRate = Math.Round((Double)i.F_MonthValue * 100 / (Double)i.F_MonthExp, 2),
                    F_Company = i.F_Company,
                    F_Type = i.F_Type,
                    F_YearKPI = i.F_YearKPI,
                    F_YearValue = i.F_YearValue,
                    F_QuarterKPI = i.F_QuarterKPI,
                    F_QuarterValue = i.F_QuarterValue,
                    F_MonthKPI = i.F_MonthKPI,
                    F_MonthValue = i.F_MonthValue,
                    F_MonthExp = i.F_MonthExp
            };          
                if (i.F_Company=="重庆公司")
                {
                    if (i.F_Type == "权益回款")
                    {
                        quanyi = item;
                    }
                    else
                    {
                        jingsh = item;
                    }
                }
                else
                {
                    if (i.F_Type == "权益回款")
                    {
                        quanyilist.Add(item);
                    }
                    else
                    {
                        jingshlist.Add(item);
                    }
                    
                }
            }
            var data = new {
                quanyilist,
                jingshlist,
                 banklist,
                 threelist,
                 quanyi,
                 jingsh,
                recoverylist
            };
            return Success(data);
        }
        #endregion
    }
}