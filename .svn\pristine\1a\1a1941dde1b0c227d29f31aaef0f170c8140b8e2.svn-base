﻿using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HolidayManage
{
    public interface IHR_AskLeaveBusiness
    {
        /// <summary>
        /// 获取年假剩余信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        string GetAnnualLeaveSYInfo(string userId, DateTime? time);
        DataTable GetExcelEmpDataList(PageInput<ConditionDTO> input, string userId);
        /// <summary>
        /// 获得假期台账报表
        /// </summary>
        /// <param name="reportCondition"></param>
        /// <returns></returns>
        PageResult<HolidayLedgerDTO> GetHolidayLedger(PageInput<ReportConditionDTO> input);
        /// <summary>
        /// 面向员工查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PageResult<HR_AskLeaveDTO>> GetEmpDataListAsync(PageInput<ConditionDTO> input, string userId);
        Task<PageResult<HR_AskLeaveDTO>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<HR_AskLeave> GetTheDataAsync(string id, string userId);
        string GetTimeAsync(string userId);
        HR_AskLeave GetById(string id);
        Task AddDataAsync(HR_AskLeave data);
        Task UpdateDataAsync(HR_AskLeave data);
        Task DeleteDataAsync(List<string> ids);
        void Add(HR_AskLeave data);
        void Edit(HR_AskLeave data);

        /// <summary>
        /// 修改数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        bool UpdateData(HR_AskLeave data);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
        /// <summary>
        /// 请假管理员下载
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        DataTable GetExcelManagerList(ConditionDTO input);
        /// <summary>
        /// 流程回调方法
        /// </summary>
        /// <param name="input"></param>
        void FlowCallBack(FlowInputDTO input);
        /// <summary>
        /// 提交流程
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <returns>是否创建成功</returns>
        bool CreateFlow(HR_AskLeave data, string url, IOperator op);

        /// <summary>
        /// 提交，退回流程
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <param name="act">submit:提交  reject：退回</param>
        /// <returns>是否创建成功</returns>
        bool ActWorkflow(HR_AskLeave data, string url, IOperator op, string act = "submit");

        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <returns>是否成功</returns>
        bool ArchiveWorkflow(HR_AskLeave data, string url);
        List<HR_Calendar> GetCalender(DateTime startTime, DateTime endTime);

    }
    [Map(typeof(HR_AskLeave))]
    public class HR_AskLeaveDTO : HR_AskLeave
    {
        /// <summary>
        /// 员工名称
        /// </summary>
        public String UserName { get; set; }
        /// <summary>
        /// 销假时长
        /// </summary>
        public decimal TermLeaveTime { get; set; }
    }
    /// <summary>
    /// 假期台账
    /// </summary>
    public class HolidayLedgerDTO
    {
        public string F_Id { get; set; }
        /// <summary>
        /// 部门
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 工作年限
        /// </summary>
        public string WorkYear { get; set; }
        /// <summary>
        /// 入职日期
        /// </summary>
        public DateTime? EntryDate { get; set; }
        /// <summary>
        /// 法定年休假
        /// </summary>
        public decimal StateAnnualLeave { get; set; }
        /// <summary>
        /// 公司福利年休假
        /// </summary>
        public decimal ComAnnualLeave { get; set; }
        /// <summary>
        /// 本年可休年假天数
        /// </summary>
        public decimal StateAnnualLeaveDay { get; set; }
        /// <summary>
        /// 已休法定年假天数
        /// </summary>
        public decimal StateAnnualLeaveDayed { get; set; }
        /// <summary>
        /// 本年剩余法定年假天数
        /// </summary>
        [NotMapped]
        public decimal RemStateAnnualLeaveDayed { get; set; }
        /// <summary>
        /// 已休福利年假天数
        /// </summary>
        public decimal ComAnnualLeaveDayed { get; set; }
        /// <summary>
        /// 本年剩余福利年假天数
        /// </summary>
        [NotMapped]
        public decimal RemComAnnualLeaveDayed { get; set; }
        /// <summary>
        /// 本年剩余年假天数
        /// </summary>
        public decimal RemAnnualLeaveDay { get; set; }
        /// <summary>
        /// 本年病假天数
        /// </summary>
        public decimal SickLeaveDay { get; set; }
        /// <summary>
        /// 已休病假天数
        /// </summary>
        public decimal SickLeaveDayed { get; set; }
        /// <summary>
        /// 本年剩余病假天数
        /// </summary>
        public decimal RemSickLeaveDay { get; set; }
        /// <summary>
        /// 本年产假天数
        /// </summary>
        public decimal MaternityLeaveDay { get; set; }
        /// <summary>
        /// 已休产假天数
        /// </summary>
        public decimal MaternityLeaveDayed { get; set; }
        /// <summary>
        /// 本年剩余产假天数
        /// </summary>
        public decimal RemMaternityLeaveDay { get; set; }
        /// <summary>
        /// 本年丧假天数
        /// </summary>
        public decimal FuneralLeaveDay { get; set; }
        /// <summary>
        /// 已休丧假天数
        /// </summary>
        public decimal FuneralLeaveDayed { get; set; }
        /// <summary>
        /// 本年剩余丧假天数
        /// </summary>
        public decimal RemFuneralLeaveDay { get; set; }
        /// <summary>
        /// 本年婚假天数
        /// </summary>
        public decimal MarriageLeaveDay { get; set; }
        /// <summary>
        /// 已休婚假天数
        /// </summary>
        public decimal MarriageLeaveDayed { get; set; }
        /// <summary>
        /// 本年剩余婚假天数
        /// </summary>
        public decimal RemMarriageLeaveDay { get; set; }
        /// <summary>
        /// 事假
        /// </summary>
        public decimal Leave { get; set; }
        /// <summary>
        /// 陪产假
        /// </summary>
        public decimal PaternityLeave { get; set; }
        /// <summary>
        /// 旷工
        /// </summary>
        public decimal Absenteeism { get; set; }
        /// <summary>
        /// 会务培训其他假期
        /// </summary>
        public decimal OtherHolidays { get; set; }
    }
}