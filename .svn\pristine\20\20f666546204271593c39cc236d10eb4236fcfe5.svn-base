﻿using Coldairarrow.Entity.Wechat_Go;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Util.DataAccess;

namespace Coldairarrow.Business.Wechat_Go
{
    public class Go_PhotoBusiness : BaseBusiness<Go_Photo>, IGo_PhotoBusiness, ITransientDependency
    {
        public Go_PhotoBusiness(IGoDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<Go_Photo>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Go_Photo>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Go_Photo, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public string GetTeamByOpenId(string id)
        {
            var q = (from a in Db.GetIQueryable<Go_TeamUser>()
                     where a.F_OpenId == id && a.F_IsAble == 1   
                     select a.F_TeamId).FirstOrDefault();
            return q;
        }

        public async Task<Go_Photo> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(Go_Photo data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Go_Photo data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public List<Go_PhotoUrl> getDataByTeamId(string id, bool index)
        {
            if (index)
            {
                var q = (from a in Db.GetIQueryable<Go_Photo>()
                        where a.F_TeamId == id && a.F_IsAble == 1
                        orderby a.F_CreateTime descending
                        select new Go_PhotoUrl
                        {
                            url = a.F_PhotoUrl,
                            id = a.F_Id
                        }).Take(5).ToList();
                return q;
            }
            else
            {
                var q = (from a in Db.GetIQueryable<Go_Photo>()
                         where a.F_TeamId == id && a.F_IsAble == 1
                         orderby a.F_CreateTime descending
                         select new Go_PhotoUrl
                         {
                             url = a.F_PhotoUrl,
                             id = a.F_Id
                         }).ToList();
                return q;
            }
        }
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Go_Photo>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Go_Photo, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<Go_Photo, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}