﻿using Coldairarrow.Entity.Shop_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Castle.Core.Internal;

namespace Coldairarrow.Business.Shop_Manage
{
    public class Z_MyCollectBusiness : BaseBusiness<Z_MyCollect>, IZ_MyCollectBusiness, ITransientDependency
    {
        public Z_MyCollectBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<Z_MyCollect>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Z_MyCollect>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Z_MyCollect, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Z_MyCollect> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(Z_MyCollect data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Z_MyCollect data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Z_MyCollect>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Z_MyCollect, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<Z_MyCollect, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        /// <summary>
        /// 查询是否关注
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task<Z_MyCollect> IsCollection(Z_MyCollect data)
        {
            if (!string.IsNullOrEmpty(data.F_UserCode) && !string.IsNullOrEmpty(data.F_ProductId))
            {
                return await this.GetIQueryable().Where(item => item.F_UserCode == data.F_UserCode && item.F_ProductId == data.F_ProductId).FirstOrDefaultAsync();
            }
            else
            {
                return new Z_MyCollect();
            }

        }
        #endregion

        #region 私有成员

        #endregion
    }
}