﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Coldairarrow.Entity.HolidayManage
{
    /// <summary>
    /// 工作日历表
    /// </summary>
    [Table("HR_Calendar")]
    public class HR_Calendar
    {

        /// <summary>
        /// F_Id
        /// </summary>
        [Key, Column(Order = 1)]
        public String F_Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime F_CreateDate { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public String F_CreateUserId { get; set; }

        /// <summary>
        /// 创建人名
        /// </summary>
        public String F_CreateUserName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? F_ModifyDate { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public String F_ModifyUserId { get; set; }

        /// <summary>
        /// 修改人名
        /// </summary>
        public String F_ModifyUserName { get; set; }

        /// <summary>
        /// 流程Guid
        /// </summary>
        public String F_WFId { get; set; }

        /// <summary>
        /// 业务状态
        /// </summary>
        public Int32? F_BusState { get; set; }

        /// <summary>
        /// 流程状态
        /// </summary>
        public Int32? F_WFState { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public String F_Remark { get; set; }

        /// <summary>
        /// 编码
        /// </summary>
        public String F_Code { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public String F_Name { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? F_StartTime { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? F_EndTime { get; set; }

        /// <summary>
        /// 日历模板
        /// </summary>
        public String F_CalendarTemplate { get; set; }

        /// <summary>
        /// 是否默认
        /// </summary>
        public Int32? F_Default { get; set; }

        /// <summary>
        /// F_Year
        /// </summary>
        public Int32? F_Year { get; set; }

        /// <summary>
        /// F_Month
        /// </summary>
        public Int32? F_Month { get; set; }

    }
}