﻿using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Newtonsoft.Json;
using System.Runtime.InteropServices;
using Coldairarrow.Util.Helper;
using Coldairarrow.Entity.Enum;
using Aliyun.Base.xiaoxizn;
using Coldairarrow.Business.HR_Manage;
using Microsoft.Extensions.Configuration;


namespace Coldairarrow.Api.Controllers.Base_Manage
{
    [Route("/Base_Manage/[controller]/[action]")]
    public class Base_FileInfoController : BaseApiController
    {
        #region DI
        readonly IConfiguration _configuration;
        public Base_FileInfoController(IBase_FileInfoBusiness base_FileInfoBus, IHR_EntryBusiness hR_EntryBus, IConfiguration configuration)
        {
            _base_FileInfoBus = base_FileInfoBus;
            _hR_EntryBus = hR_EntryBus;
            _configuration = configuration;
        }

        IBase_FileInfoBusiness _base_FileInfoBus { get; }
        IHR_EntryBusiness _hR_EntryBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Base_FileInfo>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _base_FileInfoBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Base_FileInfo> GetTheData(IdInputDTO input)
        {
            return await _base_FileInfoBus.GetTheDataAsync(input.id);
        }
        //上传文件
        /// <summary>
        /// 导入资料
        /// </summary>
        /// <returns></returns>
        [DisableRequestSizeLimit]
        [HttpPost]
        public AjaxResult UploadFilesWFQ()
        {
            //BaseResultsModel<FileModel> baseResults = new BaseResultsModel<FileModel>();
            var userInfo = GetOperator();
            var files = Request.Form.Files;
            if (files[0] == null)
            {
                return Error("文件不能为空");
            }
            var key = Request.Form["id"];
            if (files == null)
            {
                return Error("文件不能为空");
            }
            List<Base_FileInfo> fileInfoList = new List<Base_FileInfo>();
            foreach (var file in files)
            {
                var month = DateTime.Now.ToString("MM");
                var index = file.FileName.LastIndexOf("\\");
                var FileName = file.FileName.Substring(index + 1, file.FileName.Length - index - 1);
                var fileNameOld = FileName;
                var fileEx = fileNameOld.Substring(fileNameOld.LastIndexOf('.') + 1, fileNameOld.Length - fileNameOld.LastIndexOf('.') - 1);
                var fileExFull = "." + fileEx;
                var fileName = Guid.NewGuid().ToString("N") + fileExFull;
                string path = $"/Upload/{month}/{fileName}";
                string physicPath = GetAbsolutePath($"~{path}");
                string dir = Path.GetDirectoryName(physicPath);
                if (!FileHelper.IsFileSafe(file))
                {
                    return Error("上传文件不符合规范,请重新上传!");
                }
                if (!Directory.Exists(dir))
                    Directory.CreateDirectory(dir);
                using (FileStream fs = new FileStream(physicPath, FileMode.Create))
                {
                    file.CopyTo(fs);
                }
                Base_FileInfo fileInfo = new Base_FileInfo()
                {
                    F_FileName = FileName,
                    F_FilePath = path,
                    F_FileType = fileExFull,
                    F_FileSize = file.Length.ToString(),
                    F_FileExtensions = fileEx,
                    F_BusState = 0,
                };
                fileInfoList.Add(fileInfo);
            }

            FileModel fileModel = new FileModel()
            {
                Files = fileInfoList,
            };
            //var model = _base_FileInfoBus.UploadFilesAsync(fileModel, userInfo);
            return Success(fileModel);

        }
        //上传文件简历
        /// <summary>
        /// 导入资料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult UploadFilesJL()
        {
            //BaseResultsModel<FileModel> baseResults = new BaseResultsModel<FileModel>();
            var userInfo = GetOperator();
            var files = Request.Form.Files;
            if (files[0] == null)
            {
                return Error("文件不能为空");
            }
            var key = Request.Form["id"];
            if (files == null)
            {
                return Error("文件不能为空");
            }
            List<Base_FileInfo> fileInfoList = new List<Base_FileInfo>();
            foreach (var file in files)
            {
                var month = DateTime.Now.ToString("MM");
                var index = file.FileName.LastIndexOf("\\");
                var FileName = file.FileName.Substring(index + 1, file.FileName.Length - index - 1);
                var fileNameOld = FileName;
                var fileEx = fileNameOld.Substring(fileNameOld.LastIndexOf('.') + 1, fileNameOld.Length - fileNameOld.LastIndexOf('.') - 1);
                var fileExFull = "." + fileEx;
                var fileName = Guid.NewGuid().ToString("N") + fileExFull;
                string path = $"/Upload/{month}/{fileName}";
                string physicPath = GetAbsolutePath($"~{path}");
                string dir = Path.GetDirectoryName(physicPath);
                if (!FileHelper.IsFileSafe(file))
                {
                    return Error("上传文件不符合规范,请重新上传!");
                }
                if (!Directory.Exists(dir))
                    Directory.CreateDirectory(dir);
                string Md5 = "";
                using (FileStream fs = new FileStream(physicPath, FileMode.Create))
                {
                    file.CopyTo(fs);

                }
                using (FileStream fs = new FileStream(physicPath, FileMode.Open))
                {
                    Md5 = HashHelper.ComputeMD5(fs);
                }
                if (fileInfoList.Count(i => i.F_FileHash == Md5) > 0)
                {
                    throw new Exception("上传文件有重复！");
                }
                Base_FileInfo fileInfo = new Base_FileInfo()
                {
                    F_FileName = FileName,
                    F_FileHash = Md5,
                    F_FilePath = path,
                    F_FileType = fileExFull,
                    F_FileSize = file.Length.ToString(),
                    F_BusState = 1,
                    F_DeleteMark = 0,
                    F_LocationType = (int)FileTypes.招聘简历,
                };
                InitEntity(fileInfo);
                fileInfoList.Add(fileInfo);
            }

            FileModel fileModel = new FileModel()
            {
                Files = fileInfoList,
            };
            var model = _base_FileInfoBus.resumeUploadFiles(fileModel, "", userInfo);
            if (model.Count > 0)
            {
                return Success(model);
            }
            else
            {
                return Error("不能上传重复文件！");
            }
        }
        /// <summary>
        /// 小程序上传
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult UploadFilesMini()
        {
            //BaseResultsModel<FileModel> baseResults = new BaseResultsModel<FileModel>();
            var userInfo = GetOperator();
            var file = Request.Form.Files["file"];
            //hR_Entry的id
            var id = Request.Form["id"];
            if (file == null)
            {
                return Error("文件不能为空");
            }
            //获取文件后缀
            var fileExtension = Path.GetExtension(file.FileName);//获取文件格式，拓展名
            if (!FileHelper.IsFileSafe(file))
            {
                return Error("上传文件不符合规范,请重新上传!");
            }
            List<Base_FileInfo> fileInfoList = new List<Base_FileInfo>();
            var month = DateTime.Now.ToString("MM");
            var index = file.FileName.LastIndexOf("\\");
            var FileName = file.FileName.Substring(index + 1, file.FileName.Length - index - 1);
            var fileNameOld = FileName;
            var fileEx = fileNameOld.Substring(fileNameOld.LastIndexOf('.') + 1, fileNameOld.Length - fileNameOld.LastIndexOf('.') - 1);
            var fileExFull = "." + fileEx;
            var fileName = Guid.NewGuid().ToString("N") + fileExFull;
            string path = $"/Upload/{month}/{fileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            string Md5 = "";
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);

            }
            using (FileStream fs = new FileStream(physicPath, FileMode.Open))
            {
                Md5 = HashHelper.ComputeMD5(fs);
            }
            if (fileInfoList.Count(i => i.F_FileHash == Md5) > 0)
            {
                throw new Exception("上传文件有重复！");
            }
            Base_FileInfo fileInfo = new Base_FileInfo()
            {
                F_FileName = FileName,
                F_FileHash = Md5,
                F_FilePath = path,
                F_FileType = fileExFull,
                F_FileSize = file.Length.ToString(),
                F_DeleteMark = 0,
                F_BusState = 0,
                F_LocationType = (int)FileTypes.招聘简历,
            };
            InitEntity(fileInfo);
            fileInfo.F_CreateUserId = "小程序";
            fileInfo.F_CreateUserName = "小程序";
            fileInfoList.Add(fileInfo);

            FileModel fileModel = new FileModel()
            {
                Files = fileInfoList,
            };
            var model = _base_FileInfoBus.resumeUploadFiles(fileModel, id, userInfo);
            IdInputDTO idInputDTO = new IdInputDTO();
            //GetFileInfo(idInputDTO);
            if (model.Count > 0)
            {
                idInputDTO.id = model.FirstOrDefault().base_FileFolder.F_Id;
                PlanInputDTO planInputDTO = new PlanInputDTO()
                {
                    id = idInputDTO.id,
                    userId = id
                };
                SaveEntry(planInputDTO);
                return Success(model);
            }
            else
            {
                return Error("不能上传重复文件!");
            }


        }
        /// <summary>
        /// 用户简历信息
        /// </summary>
        /// <param name="fileFolderId">文件夹ID</param>
        /// <returns></returns>
        public PersonModel GetFileInfo(IdInputDTO input)
        {
            try
            {
                var filepath = _base_FileInfoBus.GetFileInfo(input.id);
                if (string.IsNullOrEmpty(filepath))
                {
                    throw new Exception("文件不存在");
                }
                var path = Directory.GetCurrentDirectory() + "\\wwwroot";
                var modelPath = path + GetAbsolutePath(filepath);
                var model = Aliyun.Base.xiaoxizn.analyze_base.Main(modelPath);
                return model;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }

        }
        /// <summary>
        /// 开始解析的方法..
        /// </summary>
        /// <param name="input">文件夹Id</param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult SaveEntry(PlanInputDTO input)
        {
            if (!string.IsNullOrEmpty(input.id))
            {
                IdInputDTO idInputDTO = new IdInputDTO()
                {
                    id = input.id
                };
                var personModel = GetFileInfo(idInputDTO);
                if (personModel.errormessage != "succeed")
                {
                    return Error("上传解析失败！未能解析到人员信息！");
                }
                var msg = _base_FileInfoBus.SaveEntry(input, personModel);
                //上传后同步简历

                return Success(msg, "上传解析成功！");
            }
            else
            {
                return Error("请上传简历文件！");
            }
        }
        /// <summary>
        /// 同步简历解析内容..
        /// </summary>
        /// <param name="input">文件夹Id</param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult synchronization(PlanInputDTO input)
        {
            if (!string.IsNullOrEmpty(input.id))
            {
                var msg = "";
                var obj = _hR_EntryBus.GetTheDataAsync(input.id).Result;
                if (obj != null)
                {
                    var personModel = JsonConvert.DeserializeObject<PersonModel>(obj.F_FileModel);
                    input.userId = input.id;
                    input.id = obj.F_FileId;
                    msg = _base_FileInfoBus.SaveEntry(input, personModel, false);

                }
                return Success(msg, "上传解析成功！");
            }
            else
            {
                return Error("请上传简历文件！");
            }
        }
        //上传文件
        /// <summary>
        /// 导入资料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult UploadFiles()
        {
            var userInfo = GetOperator();
            var files = Request.Form.Files;
            var key = Request.Form["id"];
            if (files == null)
                return Error("文件不能为空");
            List<Base_FileInfo> fileInfoList = new List<Base_FileInfo>();
            foreach (var file in files)
            {
                var month = DateTime.Now.ToString("MM");
                var fileEx = file.ContentType;
                var fileName = Guid.NewGuid().ToString("N");
                string path = $"/Upload/{month}/{file.FileName}";
                string physicPath = GetAbsolutePath($"~{path}");
                string dir = Path.GetDirectoryName(physicPath);
                //获取文件后缀
                var fileExtension = Path.GetExtension(file.FileName);//获取文件格式，拓展名
                if (!FileHelper.IsFileSafe(file))
                {
                    return Error("上传文件不符合规范,请重新上传!");
                }
                if (!Directory.Exists(dir))
                    Directory.CreateDirectory(dir);
                using (FileStream fs = new FileStream(physicPath, FileMode.Create))
                {
                    file.CopyTo(fs);
                }
                Base_FileInfo fileInfo = new Base_FileInfo()
                {
                    F_FileName = file.Name,
                    F_FilePath = path,
                    F_FileType = "",
                    F_FileSize = "",
                    F_FileExtensions = "",
                    F_BusState = 0,
                    F_Id = Guid.NewGuid().ToString("N"),
                };
                fileInfoList.Add(fileInfo);
            }

            FileModel fileModel = new FileModel()
            {
                Files = fileInfoList,
            };
            var model = _base_FileInfoBus.UploadFiles(fileModel, userInfo);
            return Success(model);
        }

        //上传文件保存
        /// <summary>
        /// 导入资料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFilesSave(FileInfoModel fileInfo)
        {
            var modelInfo = JsonConvert.DeserializeObject<FileModel>(fileInfo.FileInfo);
            var userInfo = GetOperator();
            if (string.IsNullOrWhiteSpace(modelInfo.F_Id))
            {
                if (modelInfo.Files == null || modelInfo.Files.Count == 0)
                {
                    return JsonContent(new { status = "error", message = "上传数据为空" }.ToJson());
                }
            }
            var model = _base_FileInfoBus.UploadFiles(modelInfo, userInfo);
            return JsonContent(model.ToJson());
        }
        //读取文件
        [HttpPost]
        public IActionResult LoadFiles(FileIdType typeId)
        {
            var model = _base_FileInfoBus.GetFiles(typeId.FileFolderId, typeId.FileId);
            return JsonContent(model.ToJson());
        }
        //下载文件
        [HttpPost]
        public FileContentResult DownloadFiles(FileIdType typeId)
        {
            string filePath = "";
            if (!string.IsNullOrWhiteSpace(typeId.FileId))
            {
                var model = _base_FileInfoBus.DownLoadFilesAsync(typeId.FileId);
                filePath = Directory.GetCurrentDirectory() + "/wwwroot/" + model.Files.FirstOrDefault()?.F_FilePath;
            }
            else
            {
                filePath = Directory.GetCurrentDirectory() + "/wwwroot/" + typeId.FileFolderId;
            }
            if (FileHelper.Exists(filePath))
            {
                var bys = System.IO.File.ReadAllBytes(filePath);//excel表转换成流
                return File(bys, "application/vnd.ms-excel");
            }
            else
            {
                throw new BusException("找不到文件");
            }
        }
        //下载文件
        [HttpPost]
        [NoCheckJWT]
        public FileContentResult DownloadFilesApi(FileIdType typeId)
        {
            string filePath = "";
            if (!string.IsNullOrWhiteSpace(typeId.FileId))
            {
                var model = _base_FileInfoBus.DownLoadFilesAsync(typeId.FileId);
                filePath = Directory.GetCurrentDirectory() + "/wwwroot/" + model.Files.FirstOrDefault()?.F_FilePath;
            }
            else
            {
                filePath = Directory.GetCurrentDirectory() + "/wwwroot/" + typeId.FileFolderId;
            }
            if (FileHelper.Exists(filePath))
            {
                var bys = System.IO.File.ReadAllBytes(filePath);//excel表转换成流
                //var leng = Marshal.SizeOf(bys.GetType());
                return File(bys, "application/vnd.ms-excel");
            }
            else
            {
                throw new BusException("找不到文件");
            }
        }
        //删除文件
        [HttpPost]
        public IActionResult DeleteFile(List<string> ids)
        {
            _base_FileInfoBus.DeleteDataAsync(ids);
            return JsonContent("删除成功");
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Base_FileInfo data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _base_FileInfoBus.AddDataAsync(data);
            }
            else
            {
                await _base_FileInfoBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _base_FileInfoBus.DeleteDataAsync(ids);
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _base_FileInfoBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            //获取文件后缀
            var fileExtension = Path.GetExtension(file.FileName);//获取文件格式，拓展名
            if (!FileHelper.IsFileSafe(file))
            {
                return JsonContent(new { status = "error" }.ToJson());
            }
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Base_FileInfo>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _base_FileInfoBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion


    }
}