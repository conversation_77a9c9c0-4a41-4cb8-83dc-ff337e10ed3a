﻿using Coldairarrow.Business.Base_Business;
using Coldairarrow.Entity.Base_Business;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Base_Business
{
    [Route("/Base_Business/[controller]/[action]")]
    public class Tencent_WeekMarketController : BaseApiController
    {
        #region DI

        public Tencent_WeekMarketController(ITencent_WeekMarketBusiness tencent_WeekMarketBus)
        {
            _tencent_WeekMarketBus = tencent_WeekMarketBus;
        }

        ITencent_WeekMarketBusiness _tencent_WeekMarketBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Tencent_WeekMarket>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _tencent_WeekMarketBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Tencent_WeekMarket> GetTheData(IdInputDTO input)
        {
            return await _tencent_WeekMarketBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Tencent_WeekMarket data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _tencent_WeekMarketBus.AddDataAsync(data);
            }
            else
            {
                await _tencent_WeekMarketBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _tencent_WeekMarketBus.DeleteDataAsync(ids);
        }

        #endregion
    }
}