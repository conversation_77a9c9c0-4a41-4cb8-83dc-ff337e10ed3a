﻿<template>
  <a-modal
    :title="title"
    width="40%"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="()=>{this.visible=false}"
  >
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="entity" :rules="rules" v-bind="layout">
        <a-form-model-item label="UserId" prop="UserId">
          <a-input v-model="entity.UserId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="Module" prop="Module">
          <a-input v-model="entity.Module" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="OperateId" prop="OperateId">
          <a-input v-model="entity.OperateId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="OperateContent" prop="OperateContent">
          <a-input v-model="entity.OperateContent" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="Mode" prop="Mode">
          <a-input v-model="entity.Mode" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="OperateTime" prop="OperateTime">
          <a-input v-model="entity.OperateTime" autocomplete="off" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      layout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 18 }
      },
      visible: false,
      loading: false,
      entity: {},
      rules: {},
      title: ''
    }
  },
  methods: {
    init() {
      this.visible = true
      this.entity = {}
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    openForm(id, title) {
      this.init()

      if (id) {
        this.loading = true
        this.$http.post('/Base_Manage/UserOperate/GetTheData', { id: id }).then(resJson => {
          this.loading = false

          this.entity = resJson.Data
        })
      }
    },
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.loading = true
        this.$http.post('/Base_Manage/UserOperate/SaveData', this.entity).then(resJson => {
          this.loading = false

          if (resJson.Success) {
            this.$message.success('操作成功!')
            this.visible = false

            this.parentObj.getDataList()
          } else {
            this.$message.error(resJson.Msg)
          }
        })
      })
    }
  }
}
</script>
