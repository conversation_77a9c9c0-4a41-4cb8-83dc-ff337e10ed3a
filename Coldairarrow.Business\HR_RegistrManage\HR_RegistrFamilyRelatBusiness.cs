﻿using Coldairarrow.Entity.HR_RegistrManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.HR_RegistrManage
{
    public class HR_RegistrFamilyRelatBusiness : BaseBusiness<HR_RegistrFamilyRelat>, IHR_RegistrFamilyRelatBusiness, ITransientDependency
    {
        public HR_RegistrFamilyRelatBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_RegistrFamilyRelat>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RegistrFamilyRelat>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RegistrFamilyRelat, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_RegistrFamilyRelat> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_RegistrFamilyRelat data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_RegistrFamilyRelat data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RegistrFamilyRelat>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RegistrFamilyRelat, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_RegistrFamilyRelat, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        public async Task AddDataListAsync(List<HR_RegistrFamilyRelat> data)
        {
            await Task.Run(() => BulkInsert(data));
        }

        public async Task UpdateDataListAsync(List<HR_RegistrFamilyRelat> data)
        {
            await UpdateAsync(data);
        }

        public int AddData(HR_RegistrFamilyRelat data)
        {
            return Insert(data);
        }

        public void AddListData(List<HR_RegistrFamilyRelat> data)
        {
            BulkInsert(data);
        }

        public int UpdatListeData(List<HR_RegistrFamilyRelat> data)
        {
            return Update(data);
        }

        public int UpdateData(HR_RegistrFamilyRelat data)
        {
            return Update(data);
        }

        public int DeleteData(HR_RegistrFamilyRelat data)
        {
            return Delete(data);
        }

        public int DeleteDataListeData(List<HR_RegistrFamilyRelat> data)
        {
            return Delete(data);
        }
        public async Task DeleteDataAsync(string id)
        {
            await DeleteAsync(this.GetIQueryable().Where(i => i.F_UserId == id).ToList());
        }
        #endregion

        #region 私有成员

        #endregion
    }
}