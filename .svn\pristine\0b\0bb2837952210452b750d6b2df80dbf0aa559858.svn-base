﻿using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HR_ReportFormsManage;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HR_ReportFormsManage
{
    public interface IHR_PersonnelChangeDetailsBusiness
    {
        Task<PageResult<HR_PersonnelChangeDetailsDTO>> GetDataListAsync(PageInput<ConditionDTO> input);
        PageResult<HR_PersonnelChangeDetailsDTO> GetPerDataListAsync(PageInput<ConditionDTO> input);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
    }
}