﻿using Coldairarrow.Business.S_School;
using Coldairarrow.Entity.S_School;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Newtonsoft.Json;

namespace Coldairarrow.Api.Controllers.S_School
{
    [Route("/S_School/[controller]/[action]")]
    public class S_ContentPageController : BaseApiController
    {
        #region DI

        public S_ContentPageController(IS_ContentPageBusiness s_ContentPageBus, IS_ContentPageExBusiness s_ContentPageExBus, IS_ContentReadBusiness s_ContentReadBusiness)
        {
            _s_ContentPageBus = s_ContentPageBus;
            s_ContentPageExBusiness = s_ContentPageExBus;
            _s_ContentReadBusiness = s_ContentReadBusiness;
        }

        IS_ContentPageBusiness _s_ContentPageBus { get; }
        IS_ContentPageExBusiness s_ContentPageExBusiness { get; }
        IS_ContentReadBusiness _s_ContentReadBusiness { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<S_ContentPage>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _s_ContentPageBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<S_ContentPage> GetTheData(IdInputDTO input)
        {
            S_ContentRead data = new S_ContentRead();
            InitEntity(data);
            await _s_ContentPageBus.Forward(data, input.id, "Read");
            return await _s_ContentPageBus.GetTheDataAsync(input.id);
        }
        /// <summary>
        /// 获取我的收藏
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IQueryable<S_ContentPage> GetMyCollect(PageInput<ConditionDTO> input)
        {
            return _s_ContentPageBus.GetMyCollect(input);
        }
        /// <summary>
        /// 获取近期热门
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public Task<List<S_ContentPage>> GetHotContent(PageInput<ConditionDTO> input)
        {
            return _s_ContentPageBus.GetHotContent(input);
        }


        /// <summary>
        /// 获取文章所在系列的其他文章
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<List<S_ContentPage>> GetClassfiyContent(IdInputDTO input)
        {
            return await _s_ContentPageBus.GetClassfiyContent(input.id);
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(S_ContentPage data)
        {
            //自然语言算法添加标签
            try
            {
                string msg = Aliyun.Base.Nlpwordpos.Main(data.F_Title);
                if (!string.IsNullOrEmpty(msg))
                {
                    var obj = JsonConvert.DeserializeObject<Aliyun.Base.NPLModel>(msg);
                    if (obj.data.Count > 0)
                    {
                        data.F_Tag = string.Join(",", (from a in obj.data where ( a.pos == "JJ" || a.pos == "NN" ) && !string.IsNullOrEmpty(a.word) && a.word.Length>1 select a.word).ToArray());
                    }
                }
            }
            catch
            {

            }
            //处理F_FilePath
            if (!data.F_FileId.IsNullOrEmpty() && data.F_FilePath.IsNullOrEmpty())
            {
                data.F_FilePath = _s_ContentPageBus.GetFirstFile(data);
            }
            //处理内容
            if (data.F_Id.IsNullOrEmpty())
            {
                data.F_IsHot = 0;
                data.F_IsDiscuss = 0;
                data.F_Hits = 0;
                InitEntity(data);
                S_ContentPageEx s_ContentPageEx = new S_ContentPageEx();
                s_ContentPageEx.F_Id = data.F_Id;
                s_ContentPageEx.F_Content = data.F_Content;

                await _s_ContentPageBus.AddDataAsync(data);
                await s_ContentPageExBusiness.AddDataAsync(s_ContentPageEx);
            }
            else
            {
                //临时处理
                var oldobj = _s_ContentPageBus.GetTheDataAsync(data.F_Id).Result;
                if (!string.IsNullOrEmpty(oldobj.F_FilePath) && string.IsNullOrEmpty(data.F_FilePath))
                {
                    data.F_IsHot = oldobj.F_IsHot;
                    data.F_IsDiscuss = oldobj.F_IsDiscuss;
                    data.F_Hits = oldobj.F_Hits;
                    data.F_FilePath = oldobj.F_FilePath;
                }

                S_ContentPageEx s_ContentPageEx = new S_ContentPageEx();
                s_ContentPageEx.F_Id = data.F_Id;
                s_ContentPageEx.F_Content = data.F_Content;
                await _s_ContentPageBus.UpdateDataAsync(data);
                await s_ContentPageExBusiness.UpdateDataAsync(s_ContentPageEx);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _s_ContentPageBus.DeleteDataAsync(ids);
        }

        #endregion


        #region 操作
        /// <summary>
        /// 文章评论增加评论数
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task Comment(IdInputDTO idInputDTO)
        {
            S_ContentRead data = new S_ContentRead();
            InitEntity(data);
            await _s_ContentPageBus.Forward(data, idInputDTO.id, "Comment");
            await _s_ContentPageBus.Comment(idInputDTO.id);
        }
        /// <summary>
        /// 文章点赞增加点赞数,收藏，比心公共方法
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>  
        public async Task GiveUp(ActionInputDTO idInputDTO)
        {
            //新增
            if (idInputDTO.type == 1)
            {
                S_ContentRead data = new S_ContentRead();
                InitEntity(data);
                await _s_ContentPageBus.Forward(data, idInputDTO.id, idInputDTO.action);
                if (idInputDTO.action == "Finger")
                {
                    await _s_ContentPageBus.GiveUp(idInputDTO.id, 1);
                }
            }
            //取消
            if (idInputDTO.type == 2)
            {
                await _s_ContentReadBusiness.DeleteDataAsync(idInputDTO.userId, idInputDTO.id, idInputDTO.action);
                if (idInputDTO.action == "Finger")
                {
                    await _s_ContentPageBus.GiveUp(idInputDTO.id, 2);
                }
            }
        }
        /// <summary>
        /// 评论比心增加
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>  
        public async Task CommentFinger(ActionInputDTO idInputDTO)
        {
            //新增
            if (idInputDTO.type == 1)
            {
                S_ContentRead data = new S_ContentRead();
                InitEntity(data);
                await _s_ContentPageBus.Forward(data, idInputDTO.id, "CFinger");
            }
            if (idInputDTO.type == 2)
            {
                await _s_ContentReadBusiness.DeleteDataAsync(idInputDTO.userId, idInputDTO.id, "CFinger");
            }
        }
        /// <summary>
        /// 评论点赞增加
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>  
        public async Task CommentGiveUp(ActionInputDTO idInputDTO)
        {
            //新增
            if (idInputDTO.type == 1)
            {
                S_ContentRead data = new S_ContentRead();
                InitEntity(data);
                await _s_ContentPageBus.Forward(data, idInputDTO.id, "CUp");
            }
            if (idInputDTO.type == 2)
            {
                await _s_ContentReadBusiness.DeleteDataAsync(idInputDTO.userId, idInputDTO.id, "CUp");
            }
        }
        /// <summary>
        /// 阅读、点赞、评论、转发的公共方法，保存用户记录
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="id"></param>
        /// <param name="actiontxt"></param>
        /// <returns></returns>
        public async Task Forward(ForWardData forWardData)
        {
            S_ContentRead data = new S_ContentRead();
            InitEntity(data);
            await _s_ContentPageBus.Forward(data, forWardData.id);
        }
        #endregion

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _s_ContentPageBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<S_ContentPage>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _s_ContentPageBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion

        public class ForWardData
        {
            public string id { get; set; }
            public string actiontxt { get => "Forward"; }
        }
    }
}