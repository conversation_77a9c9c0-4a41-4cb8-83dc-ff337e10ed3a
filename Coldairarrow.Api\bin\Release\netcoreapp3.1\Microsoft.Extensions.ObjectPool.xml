<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.ObjectPool</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.ObjectPool.DefaultObjectPool`1">
            <summary>
            Default implementation of <see cref="T:Microsoft.Extensions.ObjectPool.ObjectPool`1"/>.
            </summary>
            <typeparam name="T">The type to pool objects for.</typeparam>
            <remarks>This implementation keeps a cache of retained objects. This means that if objects are returned when the pool has already reached "maximumRetained" objects they will be available to be Garbage Collected.</remarks>
        </member>
        <member name="M:Microsoft.Extensions.ObjectPool.DefaultObjectPool`1.#ctor(Microsoft.Extensions.ObjectPool.IPooledObjectPolicy{`0})">
            <summary>
            Creates an instance of <see cref="T:Microsoft.Extensions.ObjectPool.DefaultObjectPool`1"/>.
            </summary>
            <param name="policy">The pooling policy to use.</param>
        </member>
        <member name="M:Microsoft.Extensions.ObjectPool.DefaultObjectPool`1.#ctor(Microsoft.Extensions.ObjectPool.IPooledObjectPolicy{`0},System.Int32)">
            <summary>
            Creates an instance of <see cref="T:Microsoft.Extensions.ObjectPool.DefaultObjectPool`1"/>.
            </summary>
            <param name="policy">The pooling policy to use.</param>
            <param name="maximumRetained">The maximum number of objects to retain in the pool.</param>
        </member>
        <member name="T:Microsoft.Extensions.ObjectPool.DefaultObjectPoolProvider">
            <summary>
            The default <see cref="T:Microsoft.Extensions.ObjectPool.ObjectPoolProvider"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.ObjectPool.DefaultObjectPoolProvider.MaximumRetained">
            <summary>
            The maximum number of objects to retain in the pool.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.ObjectPool.DefaultObjectPoolProvider.Create``1(Microsoft.Extensions.ObjectPool.IPooledObjectPolicy{``0})">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.Extensions.ObjectPool.IPooledObjectPolicy`1">
            <summary>
            Represents a policy for managing pooled objects.
            </summary>
            <typeparam name="T">The type of object which is being pooled.</typeparam>
        </member>
        <member name="M:Microsoft.Extensions.ObjectPool.IPooledObjectPolicy`1.Create">
            <summary>
            Create a <typeparamref name="T"/>.
            </summary>
            <returns>The <typeparamref name="T"/> which was created.</returns>
        </member>
        <member name="M:Microsoft.Extensions.ObjectPool.IPooledObjectPolicy`1.Return(`0)">
            <summary>
            Runs some processing when an object was returned to the pool. Can be used to reset the state of an object and indicate if the object should be returned to the pool.
            </summary>
            <param name="obj">The object to return to the pool.</param>
            <returns><code>true</code> if the object should be returned to the pool. <code>false</code> if it's not possible/desirable for the pool to keep the object.</returns>
        </member>
        <member name="T:Microsoft.Extensions.ObjectPool.ObjectPool`1">
            <summary>
            A pool of objects.
            </summary>
            <typeparam name="T">The type of objects to pool.</typeparam>
        </member>
        <member name="M:Microsoft.Extensions.ObjectPool.ObjectPool`1.Get">
            <summary>
            Gets an object from the pool if one is available, otherwise creates one.
            </summary>
            <returns>A <typeparamref name="T"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.ObjectPool.ObjectPool`1.Return(`0)">
            <summary>
            Return an object to the pool.
            </summary>
            <param name="obj">The object to add to the pool.</param>
        </member>
        <member name="T:Microsoft.Extensions.ObjectPool.ObjectPool">
            <summary>
            Methods for creating <see cref="T:Microsoft.Extensions.ObjectPool.ObjectPool`1"/> instances.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.ObjectPool.ObjectPool.Create``1(Microsoft.Extensions.ObjectPool.IPooledObjectPolicy{``0})">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.ObjectPool.ObjectPoolProvider">
            <summary>
            A provider of <see cref="T:Microsoft.Extensions.ObjectPool.ObjectPool`1"/> instances.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.ObjectPool.ObjectPoolProvider.Create``1">
            <summary>
            Creates an <see cref="T:Microsoft.Extensions.ObjectPool.ObjectPool"/>.
            </summary>
            <typeparam name="T">The type to create a pool for.</typeparam>
        </member>
        <member name="M:Microsoft.Extensions.ObjectPool.ObjectPoolProvider.Create``1(Microsoft.Extensions.ObjectPool.IPooledObjectPolicy{``0})">
            <summary>
            Creates an <see cref="T:Microsoft.Extensions.ObjectPool.ObjectPool"/> with the given <see cref="T:Microsoft.Extensions.ObjectPool.IPooledObjectPolicy`1"/>.
            </summary>
            <typeparam name="T">The type to create a pool for.</typeparam>
        </member>
    </members>
</doc>
