	--ÿ�����
	WITH TodayVisits AS (
    SELECT
        F_TA,
			F_SA,
		CONVERT(varchar(10), [F_Time],121) ʱ��,
        COUNT(*) AS Visits
    FROM [HRSystem].[dbo].[Base_HWIpDetail]
    WHERE     F_Region = '�����Ʒ���'
    AND CONVERT(varchar(10), [F_Time],121) >= '2024-08-08'
	and F_TA in ('cost.cqlandmark.com','cost.letyy.com','qiniudns.cqlandmark.com','plan.letyy.com','bjca.cqlandmark.com','hrapi.letyy.com','kfc.cqlandmark.com','weixin.cqlandmark.com','www.cqlandmark.com')
    GROUP BY
        F_TA,
		F_SA,
		CONVERT(varchar(10), [F_Time],121)
		order  by ʱ�� asc
)

SELECT
 CONCAT('�����ܷ�������',(SELECT sum(Visits) FROM TodayVisits),',�������ipΪ:', (SELECT F_SA FROM TodayVisits WHERE Visits = (SELECT MAX(Visits) FROM TodayVisits)),',������������',(SELECT MAX(Visits) FROM TodayVisits)) txt



     SELECT
        F_TA,
        F_SA,
		CONVERT(varchar(10), [F_Time],121) ʱ��,
        COUNT(*) AS Visits
    FROM [HRSystem].[dbo].[Base_HWIpDetail]
    WHERE     F_Region = '�����Ʒ���'
    AND CONVERT(varchar(10), [F_Time],121) >= '2024-08-08'
	and F_TA='weixin.cqlandmark.com'
    GROUP BY
        F_TA,
        F_SA,
		CONVERT(varchar(10), [F_Time],121)

		select F_TA
		    FROM [HRSystem].[dbo].[Base_HWIpDetail]
    WHERE     F_Region = '�����Ʒ���'
    AND CONVERT(varchar(10), [F_Time],121) >= '2024-08-08'
	group by F_TA



 --ÿһ�������
 WITH MinuteVisits AS (
    SELECT
        DATEPART(YEAR, [F_Time]) AS Year,
        DATEPART(MONTH, [F_Time]) AS Month,
        DATEPART(DAY, [F_Time]) AS Day,
        DATEPART(HOUR, [F_Time]) AS Hour,
        DATEPART(MINUTE, [F_Time]) AS Minute,
        F_SA,
        COUNT(*) AS Visits
    FROM [HRSystem].[dbo].[Base_HWIpDetail]
  WHERE     F_Region = '�����Ʒ���'
    AND CONVERT(varchar(10), [F_Time],121) = '2024-08-01'
	and F_TA='weixin.cqlandmark.com'
    GROUP BY
        DATEPART(YEAR, [F_Time]),
        DATEPART(MONTH, [F_Time]),
        DATEPART(DAY, [F_Time]),
        DATEPART(HOUR, [F_Time]),
        DATEPART(MINUTE, [F_Time]),
        F_SA
)

SELECT
    (SELECT COUNT(*) FROM [HRSystem].[dbo].[Base_HWIpDetail]
   WHERE     F_Region = '�����Ʒ���'
    AND CONVERT(varchar(10), [F_Time],121) = '2024-08-01'
	and F_TA='weixin.cqlandmark.com'
   ) AS TotalVisitsToday,

    (SELECT F_SA FROM MinuteVisits
     WHERE Visits = (SELECT MAX(Visits) FROM MinuteVisits)
     AND Minute = (SELECT Minute FROM MinuteVisits
                   WHERE Visits = (SELECT MAX(Visits) FROM MinuteVisits))
     ORDER BY Visits DESC
     OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY) AS MaxVisitedIP,

    (SELECT MAX(Visits) FROM MinuteVisits) AS MaxVisits,

    (SELECT CONCAT(CAST(Year AS varchar(4)), '-', CAST(Month AS varchar(2)), '-', CAST(Day AS varchar(2)), ' ', CAST(Hour AS varchar(2)), ':', CAST(Minute AS varchar(2))) FROM MinuteVisits
     WHERE Visits = (SELECT MAX(Visits) FROM MinuteVisits)
     AND Minute = (SELECT Minute FROM MinuteVisits
                   WHERE Visits = (SELECT MAX(Visits) FROM MinuteVisits))) AS MaxVisitTime

/****** ��ȡ�����ƹ���δ�����ip  ******/
SELECT  F_SA
  FROM [HRSystem].[dbo].[Base_HWIpDetail] H
  WHERE 1=1   and F_Region='�����ƹ��򴥷�'  AND  EXISTs (select F_RuleID from  Base_HWAliYunRule r where 1=1 and r.F_RuleLock='����' and r.F_RuleID=h.F_Action )
  AND  NOT EXISTs (SELECT F_IP FROM [dbo].[Base_IpLock] ips where h.[F_SA]=ips.F_IP )
 group by F_SA 


--���������ƹ��򴥷�

INSERT INTO [dbo].[Base_IpLock]
           ([F_Id]
           ,[F_IP]
           ,[F_CreateDate]
           ,[F_IsLock]
           ,[F_LockTime]
           ,[F_System]
           ,[F_Ctity]
           ,[F_Type])
		 select newid(),F_SA,getdate(),0,NULL,'�����ƹ��򴥷�','',4 from (
SELECT  F_SA
  FROM [HRSystem].[dbo].[Base_HWIpDetail] H
  WHERE 1=1   and F_Region='�����ƹ��򴥷�'  AND  EXISTs (select F_RuleID from  Base_HWAliYunRule r where 1=1 and r.F_RuleLock='����' and r.F_RuleID=h.F_Action )
  AND  NOT EXISTs (SELECT F_IP FROM [dbo].[Base_IpLock] ips where h.[F_SA]=ips.F_IP )
 group by F_SA 
		   ) dat 

