<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.FileHandleType">
            <summary>
            Enumerates the <see cref="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.IEndPointInformation.FileHandle"/> types.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.IEndPointInformation.Type">
            <summary>
            The type of interface being described: either an <see cref="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.IEndPointInformation.IPEndPoint"/>, Unix domain socket path, or a file descriptor.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.IEndPointInformation.IPEndPoint">
            <summary>
            The <see cref="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.IEndPointInformation.IPEndPoint"/> to bind to.
            Only set if <see cref="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.IEndPointInformation.Type"/> is <see cref="F:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.ListenType.IPEndPoint"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.IEndPointInformation.SocketPath">
            <summary>
            The absolute path to a Unix domain socket to bind to.
            Only set if <see cref="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.IEndPointInformation.Type"/> is <see cref="F:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.ListenType.SocketPath"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.IEndPointInformation.FileHandle">
            <summary>
            A file descriptor for the socket to open.
            Only set if <see cref="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.IEndPointInformation.Type"/> is <see cref="F:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.ListenType.FileHandle"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.IEndPointInformation.HandleType">
            <summary>
            The type of file descriptor being used.
            Only set if <see cref="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.IEndPointInformation.Type"/> is <see cref="F:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.ListenType.FileHandle"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.IEndPointInformation.NoDelay">
            <summary>
            Set to false to enable Nagle's algorithm for all connections.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.ListenType">
            <summary>
            Enumerates the <see cref="T:Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.Internal.IEndPointInformation"/> types.
            </summary>
        </member>
        <member name="T:System.Buffers.DiagnosticMemoryPool">
            <summary>
            Used to allocate and distribute re-usable blocks of memory.
            </summary>
        </member>
        <member name="F:System.Buffers.DiagnosticMemoryPool.AnySize">
            <summary>
            This default value passed in to Rent to use the default value for the pool.
            </summary>
        </member>
        <member name="T:System.Buffers.DiagnosticPoolBlock">
            <summary>
            Block tracking object used by the byte buffer memory pool. A slab is a large allocation which is divided into smaller blocks. The
            individual blocks are then treated as independent array segments.
            </summary>
        </member>
        <member name="F:System.Buffers.DiagnosticPoolBlock._pool">
            <summary>
            Back-reference to the memory pool which this block was allocated from. It may only be returned to this pool.
            </summary>
        </member>
        <member name="M:System.Buffers.DiagnosticPoolBlock.#ctor(System.Buffers.DiagnosticMemoryPool,System.Buffers.IMemoryOwner{System.Byte})">
            <summary>
            This object cannot be instantiated outside of the static Create method
            </summary>
        </member>
        <member name="T:System.Buffers.MemoryPoolBlock">
            <summary>
            Block tracking object used by the byte buffer memory pool. A slab is a large allocation which is divided into smaller blocks. The
            individual blocks are then treated as independent array segments.
            </summary>
        </member>
        <member name="M:System.Buffers.MemoryPoolBlock.#ctor(System.Buffers.SlabMemoryPool,System.Buffers.MemoryPoolSlab,System.Int32,System.Int32)">
            <summary>
            This object cannot be instantiated outside of the static Create method
            </summary>
        </member>
        <member name="P:System.Buffers.MemoryPoolBlock.Pool">
            <summary>
            Back-reference to the memory pool which this block was allocated from. It may only be returned to this pool.
            </summary>
        </member>
        <member name="P:System.Buffers.MemoryPoolBlock.Slab">
            <summary>
            Back-reference to the slab from which this block was taken, or null if it is one-time-use memory.
            </summary>
        </member>
        <member name="T:System.Buffers.MemoryPoolSlab">
            <summary>
            Slab tracking object used by the byte buffer memory pool. A slab is a large allocation which is divided into smaller blocks. The
            individual blocks are then treated as independent array segments.
            </summary>
        </member>
        <member name="F:System.Buffers.MemoryPoolSlab._gcHandle">
            <summary>
            This handle pins the managed array in memory until the slab is disposed. This prevents it from being
            relocated and enables any subsections of the array to be used as native memory pointers to P/Invoked API calls.
            </summary>
        </member>
        <member name="P:System.Buffers.MemoryPoolSlab.IsActive">
            <summary>
            True as long as the blocks from this slab are to be considered returnable to the pool. In order to shrink the
            memory pool size an entire slab must be removed. That is done by (1) setting IsActive to false and removing the
            slab from the pool's _slabs collection, (2) as each block currently in use is Return()ed to the pool it will
            be allowed to be garbage collected rather than re-pooled, and (3) when all block tracking objects are garbage
            collected and the slab is no longer references the slab will be garbage collected and the memory unpinned will
            be unpinned by the slab's Dispose.
            </summary>
        </member>
        <member name="T:System.Buffers.SlabMemoryPool">
            <summary>
            Used to allocate and distribute re-usable blocks of memory.
            </summary>
        </member>
        <member name="F:System.Buffers.SlabMemoryPool._blockSize">
            <summary>
            The size of a block. 4096 is chosen because most operating systems use 4k pages.
            </summary>
        </member>
        <member name="F:System.Buffers.SlabMemoryPool._blockCount">
            <summary>
            Allocating 32 contiguous blocks per slab makes the slab size 128k. This is larger than the 85k size which will place the memory
            in the large object heap. This means the GC will not try to relocate this array, so the fact it remains pinned does not negatively
            affect memory management's compactification.
            </summary>
        </member>
        <member name="P:System.Buffers.SlabMemoryPool.MaxBufferSize">
            <summary>
            Max allocation block size for pooled blocks,
            larger values can be leased but they will be disposed after use rather than returned to the pool.
            </summary>
        </member>
        <member name="F:System.Buffers.SlabMemoryPool._slabLength">
            <summary>
            4096 * 32 gives you a slabLength of 128k contiguous bytes allocated per slab
            </summary>
        </member>
        <member name="F:System.Buffers.SlabMemoryPool._blocks">
            <summary>
            Thread-safe collection of blocks which are currently in the pool. A slab will pre-allocate all of the block tracking objects
            and add them to this collection. When memory is requested it is taken from here first, and when it is returned it is re-added.
            </summary>
        </member>
        <member name="F:System.Buffers.SlabMemoryPool._slabs">
            <summary>
            Thread-safe collection of slabs which have been allocated by this pool. As long as a slab is in this collection and slab.IsActive,
            the blocks will be added to _blocks when returned.
            </summary>
        </member>
        <member name="F:System.Buffers.SlabMemoryPool._isDisposed">
            <summary>
            This is part of implementing the IDisposable pattern.
            </summary>
        </member>
        <member name="F:System.Buffers.SlabMemoryPool.AnySize">
            <summary>
            This default value passed in to Rent to use the default value for the pool.
            </summary>
        </member>
        <member name="M:System.Buffers.SlabMemoryPool.Lease">
            <summary>
            Called to take a block from the pool.
            </summary>
            <returns>The block that is reserved for the called. It must be passed to Return when it is no longer being used.</returns>
        </member>
        <member name="M:System.Buffers.SlabMemoryPool.AllocateSlab">
            <summary>
            Internal method called when a block is requested and the pool is empty. It allocates one additional slab, creates all of the
            block tracking objects, and adds them all to the pool.
            </summary>
        </member>
        <member name="M:System.Buffers.SlabMemoryPool.Return(System.Buffers.MemoryPoolBlock)">
            <summary>
            Called to return a block to the pool. Once Return has been called the memory no longer belongs to the caller, and
            Very Bad Things will happen if the memory is read of modified subsequently. If a caller fails to call Return and the
            block tracking object is garbage collected, the block tracking object's finalizer will automatically re-create and return
            a new tracking object into the pool. This will only happen if there is a bug in the server, however it is necessary to avoid
            leaving "dead zones" in the slab due to lost block tracking objects.
            </summary>
            <param name="block">The block to return. It must have been acquired by calling Lease on the same memory pool instance.</param>
        </member>
    </members>
</doc>
