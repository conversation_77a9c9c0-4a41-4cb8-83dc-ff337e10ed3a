﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Coldairarrow.Entity.Base_Manage
{
    /// <summary>
    /// Base_HWIpAddress
    /// </summary>
    [Table("Base_HWIpAddress")]
    public class Base_HWIpAddress
    {

        /// <summary>
        /// F_Id
        /// </summary>
        [Key, Column(Order = 1)]
        public String F_Id { get; set; }

        /// <summary>
        /// ip
        /// </summary>
        public String F_IP { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
        public String F_Ctity { get; set; }

        /// <summary>
        /// 维度
        /// </summary>
        public String F_Lat { get; set; }

        /// <summary>
        /// 经度
        /// </summary>
        public String F_Lng { get; set; }
        /// <summary>
        /// 时间
        /// </summary>
        public DateTime? F_Time { get; set; }
    }
}