﻿using Coldairarrow.Entity.HR_RegistrManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.HR_RegistrManage
{
    public class HR_RegistrResultsBusiness : BaseBusiness<HR_RegistrResults>, IHR_RegistrResultsBusiness, ITransientDependency
    {
        public HR_RegistrResultsBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_RegistrResults>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RegistrResults>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RegistrResults, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_RegistrResults> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_RegistrResults data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_RegistrResults data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RegistrResults>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RegistrResults, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_RegistrResults, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        public int AddData(HR_RegistrResults data)
        {
            return Insert(data);
        }

        public void AddListData(List<HR_RegistrResults> data)
        {
            BulkInsert(data);
        }

        public int UpdatListeData(List<HR_RegistrResults> data)
        {
            return Update(data);
        }

        public int UpdateData(HR_RegistrResults data)
        {
            return Update(data);
        }

        public int DeleteData(HR_RegistrResults data)
        {
            return Delete(data);
        }

        public int DeleteDataListeData(List<HR_RegistrResults> data)
        {
            return Delete(data);
        }
        public async Task AddDataListAsync(List<HR_RegistrResults> data)
        {
            await Task.Run(() => BulkInsert(data));
        }

        public async Task UpdateDataListAsync(List<HR_RegistrResults> data)
        {
            await UpdateAsync(data);
        }
        public async Task DeleteDataAsync(string id)
        {
            await DeleteAsync(this.GetIQueryable().Where(i => i.F_UserId == id).ToList());
        }
        #endregion

        #region 私有成员

        #endregion
    }
}