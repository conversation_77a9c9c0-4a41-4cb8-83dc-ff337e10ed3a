<?xml version="1.0"?>
<doc>
    <assembly>
        <name>EFCore.Sharding</name>
    </assembly>
    <members>
        <member name="M:EFCore.Sharding.GenericDbAccessor.#ctor(EFCore.Sharding.GenericDbContext)">
            <summary>
            构造函数
            </summary>
            <param name="baseDbContext">BaseDbContext</param>
        </member>
        <member name="T:EFCore.Sharding.LogicDeleteDbAccessor">
            <summary>
            软删除访问接口
            软删除:查询:获取Deleted=false,删除:更新Deleted=true
            </summary>
        </member>
        <member name="T:EFCore.Sharding.DbContextParamters">
            <summary>
            通用DbContext参数
            </summary>
        </member>
        <member name="P:EFCore.Sharding.DbContextParamters.ConnectionString">
            <summary>
            连接字符串
            </summary>
        </member>
        <member name="P:EFCore.Sharding.DbContextParamters.DbType">
            <summary>
            数据库类型
            </summary>
        </member>
        <member name="P:EFCore.Sharding.DbContextParamters.EntityNamespace">
            <summary>
            实体命名空间
            </summary>
        </member>
        <member name="P:EFCore.Sharding.DbContextParamters.EntityTypes">
            <summary>
            自定义实体类
            </summary>
        </member>
        <member name="P:EFCore.Sharding.DbContextParamters.Suffix">
            <summary>
            表名后缀(分表时需要)
            </summary>
        </member>
        <member name="T:EFCore.Sharding.GenericDbContext">
            <summary>
            通用DbContext
            </summary>
        </member>
        <member name="P:EFCore.Sharding.GenericDbContext.DbContextOption">
            <summary>
            DbContext原生配置
            </summary>
        </member>
        <member name="P:EFCore.Sharding.GenericDbContext.ShardingOption">
            <summary>
            全局自定义配置
            </summary>
        </member>
        <member name="P:EFCore.Sharding.GenericDbContext.Paramter">
            <summary>
            构建参数
            </summary>
        </member>
        <member name="M:EFCore.Sharding.GenericDbContext.#ctor(Microsoft.EntityFrameworkCore.DbContextOptions,EFCore.Sharding.DbContextParamters,EFCore.Sharding.EFCoreShardingOptions)">
            <summary>
            
            </summary>
            <param name="contextOptions"></param>
            <param name="paramter"></param>
            <param name="shardingOptions"></param>
        </member>
        <member name="M:EFCore.Sharding.GenericDbContext.#ctor(EFCore.Sharding.GenericDbContext)">
            <summary>
            
            </summary>
            <param name="dbContext"></param>
        </member>
        <member name="M:EFCore.Sharding.GenericDbContext.OnModelCreating(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            模型构建
            </summary>
            <param name="modelBuilder"></param>
        </member>
        <member name="M:EFCore.Sharding.GenericDbContext.Detach">
            <summary>
            取消跟踪
            </summary>
        </member>
        <member name="T:EFCore.Sharding.EFCoreShardingBootstrapper">
            <summary>
            EFCoreSharding初始化加载
            注：非Host环境需要手动调用
            </summary>
        </member>
        <member name="M:EFCore.Sharding.EFCoreShardingBootstrapper.#ctor(System.IServiceProvider)">
            <summary>
            构造函数
            </summary>
            <param name="serviceProvider"></param>
        </member>
        <member name="M:EFCore.Sharding.EFCoreShardingBootstrapper.ExecuteAsync(System.Threading.CancellationToken)">
            <summary>
            加载
            </summary>
            <param name="stoppingToken"></param>
            <returns></returns>
        </member>
        <member name="T:EFCore.Sharding.EFCoreShardingExtensions">
            <summary>
            拓展
            </summary>
        </member>
        <member name="M:EFCore.Sharding.EFCoreShardingExtensions.AddEFCoreSharding(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{EFCore.Sharding.IShardingBuilder})">
            <summary>
            注入EFCoreSharding
            </summary>
            <param name="services">服务集合</param>
            <param name="shardingBuilder">配置项</param>
            <returns></returns>
        </member>
        <member name="T:EFCore.Sharding.IShardingBuilder">
            <summary>
            构建者
            </summary>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.SetEntityAssemblyPath(System.String[])">
            <summary>
            设置实体的程序集路径
            </summary>
            <param name="entityAssemblyPaths">程序集路径</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.SetCommandTimeout(System.Int32)">
            <summary>
            设置SQL执行超时时间(单位秒,默认30)
            </summary>
            <param name="timeout">超时时间</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.AddEntityTypeBuilderFilter(System.Action{Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder})">
            <summary>
            添加实体模型构建过滤器
            </summary>
            <param name="filter">过滤器</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.MigrationsWithoutForeignKey">
            <summary>
            使用Code First进行迁移时忽略外键
            </summary>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.CreateShardingTableOnStarting(System.Boolean)">
            <summary>
            是否在启动时自动创建分表,默认true
            </summary>
            <param name="enable">是否启用</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.EnableShardingMigration(System.Boolean)">
            <summary>
            是否启用分表数据库迁移,默认false
            </summary>
            <param name="enable">是否启用</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.EnableComments(System.Boolean)">
            <summary>
            是否启用注释,默认true
            </summary>
            <param name="enable">是否启用</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.UseLogicDelete(System.String,System.String)">
            <summary>
            使用逻辑删除
            </summary>
            <param name="keyField">主键字段,字段类型为string</param>
            <param name="deletedField">已删除标志字段,字段类型为bool</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.UseDatabase(System.String,EFCore.Sharding.DatabaseType,System.String)">
            <summary>
            使用默认数据库
            注入IDbAccessor
            </summary>
            <param name="conString">连接字符串</param>
            <param name="dbType">数据库类型</param>
            <param name="entityNamespace">实体命名空间</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.UseDatabase``1(System.String,EFCore.Sharding.DatabaseType,System.String)">
            <summary>
            使用数据库
            </summary>
            <typeparam name="TDbAccessor">自定义的IDbAccessor</typeparam>
            <param name="conString">连接字符串</param>
            <param name="dbType">数据库类型</param>
            <param name="entityNamespace">实体命名空间</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.UseDatabase(System.ValueTuple{System.String,EFCore.Sharding.ReadWriteType}[],EFCore.Sharding.DatabaseType,System.String)">
            <summary>
            使用默认数据库
            注入IDbAccessor
            </summary>
            <param name="dbs">读写数据库配置</param>
            <param name="dbType">数据库类型</param>
            <param name="entityNamespace">实体命名空间</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.UseDatabase``1(System.ValueTuple{System.String,EFCore.Sharding.ReadWriteType}[],EFCore.Sharding.DatabaseType,System.String)">
            <summary>
            使用数据库
            </summary>
            <typeparam name="TDbAccessor">自定义的IDbAccessor</typeparam>
            <param name="dbs">读写数据库配置</param>
            <param name="dbType">数据库类型</param>
            <param name="entityNamespace">实体命名空间</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.AddDataSource(System.String,EFCore.Sharding.ReadWriteType,EFCore.Sharding.DatabaseType,System.String)">
            <summary>
            添加数据源
            </summary>
            <param name="connectionString">连接字符串</param>
            <param name="readWriteType">读写模式</param>
            <param name="dbType">数据库类型</param>
            <param name="sourceName">数据源名</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.AddDataSource(System.ValueTuple{System.String,EFCore.Sharding.ReadWriteType}[],EFCore.Sharding.DatabaseType,System.String)">
            <summary>
            添加数据源
            </summary>
            <param name="dbs">数据库组</param>
            <param name="dbType">数据库类型</param>
            <param name="sourceName">数据源名</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.SetHashModSharding``1(System.String,System.Int32,System.String)">
            <summary>
            设置分表规则(哈希取模)
            注:默认自动创建分表(若分表不存在)
            </summary>
            <typeparam name="TEntity">对应抽象表类型</typeparam>
            <param name="shardingField">分表字段</param>
            <param name="mod">取模</param>
            <param name="sourceName">数据源名</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.SetHashModSharding``1(System.String,System.Int32,System.ValueTuple{System.Int32,System.Int32,System.String}[])">
            <summary>
            设置分表规则(哈希取模)
            注:默认自动创建分表(若分表不存在)
            </summary>
            <typeparam name="TEntity">对应抽象表类型</typeparam>
            <param name="shardingField">分表字段</param>
            <param name="mod">取模</param>
            <param name="ranges">分库配置</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.SetDateSharding``1(System.String,EFCore.Sharding.ExpandByDateMode,System.DateTime,System.String)">
            <summary>
            设置分表规则(按日期)
            </summary>
            <typeparam name="TEntity">对应抽象表类型</typeparam>
            <param name="shardingField">分表字段</param>
            <param name="expandByDateMode">扩容模式</param>
            <param name="startTime">开始时间</param>
            <param name="sourceName">数据源名</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingBuilder.SetDateSharding``1(System.String,EFCore.Sharding.ExpandByDateMode,System.ValueTuple{System.DateTime,System.DateTime,System.String}[])">
            <summary>
            设置分表规则(按日期)
            </summary>
            <typeparam name="TEntity">对应抽象表类型</typeparam>
            <param name="shardingField">分表字段</param>
            <param name="expandByDateMode">扩容模式</param>
            <param name="ranges">分库配置</param>
            <returns></returns>
        </member>
        <member name="T:EFCore.Sharding.ShardingConstant">
            <summary>
            静态量
            </summary>
        </member>
        <member name="F:EFCore.Sharding.ShardingConstant.DefaultSource">
            <summary>
            默认数据源名
            </summary>
        </member>
        <member name="T:EFCore.Sharding.IBaseDbAccessor">
            <summary>
            通用基接口
            </summary>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.Insert``1(``0,System.Boolean)">
            <summary>
            添加单条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entity">实体对象</param>
            <param name="tracking">是否开启实体追踪</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.InsertAsync``1(``0,System.Boolean)">
            <summary>
            添加单条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entity">实体对象</param>
            <param name="tracking">是否开启实体追踪</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.Insert``1(System.Collections.Generic.List{``0},System.Boolean)">
            <summary>
            添加多条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entities">实体对象集合</param>
            <param name="tracking">是否开启实体追踪</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.InsertAsync``1(System.Collections.Generic.List{``0},System.Boolean)">
            <summary>
            添加多条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entities">实体对象集合</param>
            <param name="tracking">是否开启实体追踪</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.DeleteAll``1">
            <summary>
            删除所有记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.DeleteAllAsync``1">
            <summary>
            删除所有记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.Delete``1(``0)">
            <summary>
            删除单条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entity">实体对象</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.DeleteAsync``1(``0)">
            <summary>
            删除单条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entity">实体对象</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.Delete``1(System.Collections.Generic.List{``0})">
            <summary>
            删除多条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entities">实体对象集合</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.DeleteAsync``1(System.Collections.Generic.List{``0})">
            <summary>
            删除多条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entities">实体对象集合</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.Delete``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            按条件删除记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="condition">筛选条件</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.DeleteAsync``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            按条件删除记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="condition">筛选条件</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.DeleteSql``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            使用SQL语句按照条件删除数据
            用法:Delete_Sql"Base_User"(x=>x.Id == "Admin")
            注：生成的SQL类似于DELETE FROM [Base_User] WHERE [Name] = 'xxx' WHERE [Id] = 'Admin'
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="where">条件</param>
            <returns>影响条数</returns>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.DeleteSqlAsync``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            使用SQL语句按照条件删除数据
            用法:Delete_Sql"Base_User"(x=>x.Id == "Admin")
            注：生成的SQL类似于DELETE FROM [Base_User] WHERE [Name] = 'xxx' WHERE [Id] = 'Admin'
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="where">条件</param>
            <returns>影响条数</returns>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.Update``1(``0,System.Boolean)">
            <summary>
            更新单条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entity">实体对象</param>
            <param name="tracking">是否开启实体追踪</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.UpdateAsync``1(``0,System.Boolean)">
            <summary>
            更新单条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entity">实体对象</param>
            <param name="tracking">是否开启实体追踪</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.Update``1(System.Collections.Generic.List{``0},System.Boolean)">
            <summary>
            更新多条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entities">实体对象集合</param>
            <param name="tracking">是否开启实体追踪</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.UpdateAsync``1(System.Collections.Generic.List{``0},System.Boolean)">
            <summary>
            更新多条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entities">实体对象集合</param>
            <param name="tracking">是否开启实体追踪</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.Update``1(``0,System.Collections.Generic.List{System.String},System.Boolean)">
            <summary>
            更新单条记录的某些属性
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entity">实体对象</param>
            <param name="properties">属性</param>
            <param name="tracking">是否开启实体追踪</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.UpdateAsync``1(``0,System.Collections.Generic.List{System.String},System.Boolean)">
            <summary>
            更新单条记录的某些属性
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entity">实体对象</param>
            <param name="properties">属性</param>
            <param name="tracking">是否开启实体追踪</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.Update``1(System.Collections.Generic.List{``0},System.Collections.Generic.List{System.String},System.Boolean)">
            <summary>
            更新多条记录的某些属性
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entities">实体对象集合</param>
            <param name="properties">属性</param>
            <param name="tracking">是否开启实体追踪</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.UpdateAsync``1(System.Collections.Generic.List{``0},System.Collections.Generic.List{System.String},System.Boolean)">
            <summary>
            更新多条记录的某些属性
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entities">实体对象集合</param>
            <param name="properties">属性</param>
            <param name="tracking">是否开启实体追踪</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.Update``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Action{``0},System.Boolean)">
            <summary>
            按照条件更新记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="whereExpre">筛选条件</param>
            <param name="set">更新操作</param>
            <param name="tracking">是否开启实体追踪</param>
        </member>
        <member name="M:EFCore.Sharding.IBaseDbAccessor.UpdateAsync``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Action{``0},System.Boolean)">
            <summary>
            按照条件更新记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="whereExpre">筛选条件</param>
            <param name="set">更新操作</param>
            <param name="tracking">是否开启实体追踪</param>
        </member>
        <member name="T:EFCore.Sharding.IDbAccessor">
            <summary>
            操作接口
            </summary>
        </member>
        <member name="P:EFCore.Sharding.IDbAccessor.ConnectionString">
            <summary>
            连接字符串
            </summary>
        </member>
        <member name="P:EFCore.Sharding.IDbAccessor.DbType">
            <summary>
            数据库类型
            </summary>
        </member>
        <member name="P:EFCore.Sharding.IDbAccessor.FullDbAccessor">
            <summary>
            获取完整DbAccessor,通过此接口可以操作逻辑删除的数据
            </summary>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.SaveChanges(System.Boolean)">
            <summary>
            保存修改到数据库(需要GetIQueryable开启实体追踪)
            </summary>
            <param name="tracking">是否开启实体追踪</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.SaveChangesAsync(System.Boolean)">
            <summary>
            保存修改到数据库(需要GetIQueryable开启实体追踪)
            </summary>
            <param name="tracking">是否开启实体追踪</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.Entry(System.Object)">
            <summary>
            跟踪
            </summary>
            <param name="entity">实体</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.BulkInsert``1(System.Collections.Generic.List{``0},System.String)">
            <summary>
            使用Bulk批量导入,速度快
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="entities">实体集合</param>
            <param name="tableName">自定义表名</param>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.Delete``1(System.String)">
            <summary>
            删除单条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="key">主键</param>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.DeleteAsync``1(System.String)">
            <summary>
            删除单条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="key">主键</param>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.Delete``1(System.Collections.Generic.List{System.String})">
            <summary>
            删除多条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="keys">多条记录主键集合</param>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.DeleteAsync``1(System.Collections.Generic.List{System.String})">
            <summary>
            删除多条记录
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="keys">多条记录主键集合</param>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.DeleteSql(System.Linq.IQueryable)">
            <summary>
            删除指定数据源
            </summary>
            <param name="source">数据源</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.DeleteSqlAsync(System.Linq.IQueryable)">
            <summary>
            删除指定数据源
            </summary>
            <param name="source">数据源</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.UpdateSql``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.ValueTuple{System.String,EFCore.Sharding.UpdateType,System.Object}[])">
            <summary>
            使用SQL语句按照条件更新
            用法:UpdateWhere_Sql"Base_User"(x=>x.Id == "Admin",("Name",UpdateType.Equal,"小明"))
            注：生成的SQL类似于UPDATE [TABLE] SET [Name] = 'xxx' WHERE [Id] = 'Admin'
            </summary>
            <typeparam name="T">实体类型</typeparam>
            <param name="where">筛选条件</param>
            <param name="values">字段值设置</param>
            <returns>影响条数</returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.UpdateSqlAsync``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.ValueTuple{System.String,EFCore.Sharding.UpdateType,System.Object}[])">
            <summary>
            使用SQL语句按照条件更新
            用法:UpdateWhere_Sql"Base_User"(x=>x.Id == "Admin",("Name",UpdateType.Equal,"小明"))
            注：生成的SQL类似于UPDATE [TABLE] SET [Name] = 'xxx' WHERE [Id] = 'Admin'
            </summary>
            <typeparam name="T">实体类型</typeparam>
            <param name="where">筛选条件</param>
            <param name="values">字段值设置</param>
            <returns>影响条数</returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.UpdateSql(System.Linq.IQueryable,System.ValueTuple{System.String,EFCore.Sharding.UpdateType,System.Object}[])">
            <summary>
            使用SQL语句按照条件更新
            用法:UpdateWhere_Sql"Base_User"(x=>x.Id == "Admin",("Name",UpdateType.Equal,"小明"))
            注：生成的SQL类似于UPDATE [TABLE] SET [Name] = 'xxx' WHERE [Id] = 'Admin'
            </summary>
            <param name="source">数据源</param>
            <param name="values">字段值设置</param>
            <returns>影响条数</returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.UpdateSqlAsync(System.Linq.IQueryable,System.ValueTuple{System.String,EFCore.Sharding.UpdateType,System.Object}[])">
            <summary>
            使用SQL语句按照条件更新
            用法:UpdateWhere_Sql"Base_User"(x=>x.Id == "Admin",("Name",UpdateType.Equal,"小明"))
            注：生成的SQL类似于UPDATE [TABLE] SET [Name] = 'xxx' WHERE [Id] = 'Admin'
            </summary>
            <param name="source">数据源</param>
            <param name="values">字段值设置</param>
            <returns>影响条数</returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.GetEntity``1(System.Object[])">
            <summary>
            获取单条记录
            注:无实体跟踪
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="keyValue">主键</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.GetEntityAsync``1(System.Object[])">
            <summary>
            获取单条记录
            注:无实体跟踪
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="keyValue">主键</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.GetIQueryable``1(System.Boolean)">
            <summary>
            获取IQueryable
            注:默认取消实体追踪
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="tracking">是否开启实体追踪</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.GetDataTableWithSql(System.String,System.ValueTuple{System.String,System.Object}[])">
            <summary>
            通过SQL获取DataTable
            </summary>
            <param name="sql">SQL语句</param>
            <param name="parameters">SQL参数</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.GetDataTableWithSqlAsync(System.String,System.ValueTuple{System.String,System.Object}[])">
            <summary>
            通过SQL获取DataTable
            </summary>
            <param name="sql">SQL语句</param>
            <param name="parameters">SQL参数</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.GetListBySql``1(System.String,System.ValueTuple{System.String,System.Object}[])">
            <summary>
            通过SQL获取List
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="sqlStr">SQL语句</param>
            <param name="parameters">SQL参数</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.GetListBySqlAsync``1(System.String,System.ValueTuple{System.String,System.Object}[])">
            <summary>
            通过SQL获取List
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <param name="sqlStr">SQL语句</param>
            <param name="parameters">SQL参数</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.ExecuteSql(System.String,System.ValueTuple{System.String,System.Object}[])">
            <summary>
            执行SQL语句
            </summary>
            <param name="sql">SQL语句</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:EFCore.Sharding.IDbAccessor.ExecuteSqlAsync(System.String,System.ValueTuple{System.String,System.Object}[])">
            <summary>
            执行SQL语句
            </summary>
            <param name="sql">SQL语句</param>
            <param name="parameters">SQL参数</param>
        </member>
        <member name="T:EFCore.Sharding.IShardingDbAccessor">
            <summary>
            数据库分表操作接口
            </summary>
        </member>
        <member name="M:EFCore.Sharding.IShardingDbAccessor.GetIShardingQueryable``1">
            <summary>
            获取IShardingQueryable
            </summary>
            <typeparam name="T">实体泛型</typeparam>
            <returns></returns>
        </member>
        <member name="T:EFCore.Sharding.IDbFactory">
            <summary>
            数据库工厂
            </summary>
        </member>
        <member name="M:EFCore.Sharding.IDbFactory.GetDbAccessor(System.String,EFCore.Sharding.DatabaseType,System.String,System.String)">
            <summary>
            根据配置文件获取数据库类型，并返回对应的工厂接口
            </summary>
            <param name="conString">完整数据库链接字符串</param>
            <param name="dbType">数据库类型</param>
            <param name="entityNamespace">实体命名空间</param>
            <param name="suffix">表明后缀</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IDbFactory.GetDbContext(EFCore.Sharding.DbContextParamters)">
            <summary>
            获取DbContext
            </summary>
            <param name="options">选项</param>
            <returns></returns>
        </member>
        <member name="T:EFCore.Sharding.EFCoreShardingOptions">
            <summary>
            EFCoreSharding配置参数
            </summary>
        </member>
        <member name="P:EFCore.Sharding.EFCoreShardingOptions.CommandTimeout">
            <summary>
            SQL执行超时时间,默认30S,单位（秒）
            </summary>
        </member>
        <member name="P:EFCore.Sharding.EFCoreShardingOptions.LogicDelete">
            <summary>
            是否使用逻辑删除,默认否
            </summary>
        </member>
        <member name="P:EFCore.Sharding.EFCoreShardingOptions.KeyField">
            <summary>
            主键字段名
            </summary>
        </member>
        <member name="P:EFCore.Sharding.EFCoreShardingOptions.DeletedField">
            <summary>
            标记已删除字段
            </summary>
        </member>
        <member name="F:EFCore.Sharding.EFCoreShardingOptions.AssemblyPaths">
            <summary>
            程序集路径
            </summary>
        </member>
        <member name="P:EFCore.Sharding.EFCoreShardingOptions.EntityTypeBuilderFilter">
            <summary>
            实体模型构建过滤器
            </summary>
        </member>
        <member name="P:EFCore.Sharding.EFCoreShardingOptions.MigrationsWithoutForeignKey">
            <summary>
            使用Code First进行迁移时是否忽略外键（即不生成数据库外键）,默认为false(即默认生成外键)
            </summary>
        </member>
        <member name="P:EFCore.Sharding.EFCoreShardingOptions.CreateShardingTableOnStarting">
            <summary>
            是否在启动时自动创建分表,默认true
            </summary>
        </member>
        <member name="P:EFCore.Sharding.EFCoreShardingOptions.EnableShardingMigration">
            <summary>
            是否启用分表数据库迁移,默认false
            </summary>
        </member>
        <member name="P:EFCore.Sharding.EFCoreShardingOptions.EnableComments">
            <summary>
            是否启用注释
            </summary>
        </member>
        <member name="T:EFCore.Sharding.DatabaseType">
            <summary>
            数据库类型
            </summary>
        </member>
        <member name="F:EFCore.Sharding.DatabaseType.SqlServer">
            <summary>
            SqlServer数据库
            </summary>
        </member>
        <member name="F:EFCore.Sharding.DatabaseType.MySql">
            <summary>
            MySql数据库
            </summary>
        </member>
        <member name="F:EFCore.Sharding.DatabaseType.Oracle">
            <summary>
            Oracle数据库
            </summary>
        </member>
        <member name="F:EFCore.Sharding.DatabaseType.PostgreSql">
            <summary>
            PostgreSql数据库
            </summary>
        </member>
        <member name="F:EFCore.Sharding.DatabaseType.SQLite">
            <summary>
            SQLite数据库
            </summary>
        </member>
        <member name="T:EFCore.Sharding.ExpandByDateMode">
            <summary>
            按时间扩容模式
            </summary>
        </member>
        <member name="F:EFCore.Sharding.ExpandByDateMode.PerMinute">
            <summary>
            每分钟
            </summary>
        </member>
        <member name="F:EFCore.Sharding.ExpandByDateMode.PerHour">
            <summary>
            每小时
            </summary>
        </member>
        <member name="F:EFCore.Sharding.ExpandByDateMode.PerDay">
            <summary>
            每天
            </summary>
        </member>
        <member name="F:EFCore.Sharding.ExpandByDateMode.PerMonth">
            <summary>
            每月
            </summary>
        </member>
        <member name="F:EFCore.Sharding.ExpandByDateMode.PerYear">
            <summary>
            每年
            </summary>
        </member>
        <member name="T:EFCore.Sharding.ReadWriteType">
            <summary>
            读写模式
            </summary>
        </member>
        <member name="F:EFCore.Sharding.ReadWriteType.Read">
            <summary>
            只读
            </summary>
        </member>
        <member name="F:EFCore.Sharding.ReadWriteType.Write">
            <summary>
            只写
            </summary>
        </member>
        <member name="T:EFCore.Sharding.ShardingType">
            <summary>
            分表类型
            </summary>
        </member>
        <member name="F:EFCore.Sharding.ShardingType.HashMod">
            <summary>
            通过哈希取模分表
            </summary>
        </member>
        <member name="F:EFCore.Sharding.ShardingType.Date">
            <summary>
            按日期分表
            </summary>
        </member>
        <member name="T:EFCore.Sharding.UpdateType">
            <summary>
            更新模式
            注:[Field]=[Field] {UpdateType} value
            </summary>
        </member>
        <member name="F:EFCore.Sharding.UpdateType.Equal">
            <summary>
            等,即赋值,[Field]= value
            </summary>
        </member>
        <member name="F:EFCore.Sharding.UpdateType.Add">
            <summary>
            自增,[Field]=[Field] + value
            </summary>
        </member>
        <member name="F:EFCore.Sharding.UpdateType.Minus">
            <summary>
            自减,[Field]=[Field] - value
            </summary>
        </member>
        <member name="F:EFCore.Sharding.UpdateType.Multiply">
            <summary>
            自乘,[Field]=[Field] * value
            </summary>
        </member>
        <member name="F:EFCore.Sharding.UpdateType.Divide">
            <summary>
            自除,[Field]=[Field] / value
            </summary>
        </member>
        <member name="T:EFCore.Sharding.IShardingQueryable`1">
            <summary>
            IShardingQueryable
            </summary>
            <typeparam name="T">逻辑表泛型</typeparam>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Where(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            筛选
            </summary>
            <param name="predicate">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Where(System.String,System.Object[])">
            <summary>
            动态筛选
            </summary>
            <param name="predicate">动态表达式</param>
            <param name="values">参数</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Skip(System.Int32)">
            <summary>
            SKip
            </summary>
            <param name="count">数量</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Take(System.Int32)">
            <summary>
            Take
            </summary>
            <param name="count">数量</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.OrderBy``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            顺序排序
            </summary>
            <typeparam name="TKey">返回类型</typeparam>
            <param name="keySelector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.OrderByDescending``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            倒序排序
            </summary>
            <typeparam name="TKey">返回类型</typeparam>
            <param name="keySelector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.OrderBy(System.String,System.Object[])">
            <summary>
            动态排序
            </summary>
            <param name="ordering">动态表达式</param>
            <param name="values">参数</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Count">
            <summary>
            获取数量
            </summary>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.CountAsync">
            <summary>
            异步获取数量
            </summary>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.ToList">
            <summary>
            获取列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.ToListAsync">
            <summary>
            异步获取列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.FirstOrDefault">
            <summary>
            获取第一个,若不存在则返回默认值
            </summary>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.FirstOrDefaultAsync">
            <summary>
            获取第一个,若不存在则返回默认值
            </summary>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Any(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            判断是否存在
            </summary>
            <param name="predicate">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.AnyAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            异步判断是否存在
            </summary>
            <param name="predicate">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Distinct``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            去重
            </summary>
            <typeparam name="TResult">数据类型</typeparam>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.DistinctAsync``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            去重
            </summary>
            <typeparam name="TResult">数据类型</typeparam>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Max``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            计算最大值
            </summary>
            <typeparam name="TResult">数据类型</typeparam>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.MaxAsync``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            异步计算最大值
            </summary>
            <typeparam name="TResult">数据类型</typeparam>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Min``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            计算最小值
            </summary>
            <typeparam name="TResult">数据类型</typeparam>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.MinAsync``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            异步计算最小值
            </summary>
            <typeparam name="TResult">数据类型</typeparam>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Average(System.Linq.Expressions.Expression{System.Func{`0,System.Int32}})">
            <summary>
            计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.AverageAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Int32}})">
            <summary>
            异步计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Average(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Int32}}})">
            <summary>
            计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.AverageAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Int32}}})">
            <summary>
            异步计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Average(System.Linq.Expressions.Expression{System.Func{`0,System.Single}})">
            <summary>
            计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.AverageAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Single}})">
            <summary>
            异步计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Average(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Single}}})">
            <summary>
            计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.AverageAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Single}}})">
            <summary>
            异步计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Average(System.Linq.Expressions.Expression{System.Func{`0,System.Int64}})">
            <summary>
            计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.AverageAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Int64}})">
            <summary>
            异步计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Average(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Int64}}})">
            <summary>
            计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.AverageAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Int64}}})">
            <summary>
            异步计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Average(System.Linq.Expressions.Expression{System.Func{`0,System.Double}})">
            <summary>
            计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.AverageAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Double}})">
            <summary>
            异步计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Average(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Double}}})">
            <summary>
            计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.AverageAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Double}}})">
            <summary>
            异步计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Average(System.Linq.Expressions.Expression{System.Func{`0,System.Decimal}})">
            <summary>
            计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.AverageAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Decimal}})">
            <summary>
            异步计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Average(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Decimal}}})">
            <summary>
            计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.AverageAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Decimal}}})">
            <summary>
            异步计算平均值
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Sum(System.Linq.Expressions.Expression{System.Func{`0,System.Decimal}})">
            <summary>
            求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.SumAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Decimal}})">
            <summary>
            异步求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Sum(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Decimal}}})">
            <summary>
            求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.SumAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Decimal}}})">
            <summary>
            异步求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Sum(System.Linq.Expressions.Expression{System.Func{`0,System.Double}})">
            <summary>
            求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.SumAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Double}})">
            <summary>
            异步求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Sum(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Double}}})">
            <summary>
            求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.SumAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Double}}})">
            <summary>
            异步求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Sum(System.Linq.Expressions.Expression{System.Func{`0,System.Single}})">
            <summary>
            求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.SumAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Single}})">
            <summary>
            异步求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Sum(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Single}}})">
            <summary>
            求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.SumAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Single}}})">
            <summary>
            异步求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Sum(System.Linq.Expressions.Expression{System.Func{`0,System.Int32}})">
            <summary>
            求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.SumAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Int32}})">
            <summary>
            异步求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Sum(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Int32}}})">
            <summary>
            求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.SumAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Int32}}})">
            <summary>
            异步求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Sum(System.Linq.Expressions.Expression{System.Func{`0,System.Int64}})">
            <summary>
            求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.SumAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Int64}})">
            <summary>
            异步求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.Sum(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Int64}}})">
            <summary>
            求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.IShardingQueryable`1.SumAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Nullable{System.Int64}}})">
            <summary>
            异步求和
            </summary>
            <param name="selector">表达式</param>
            <returns></returns>
        </member>
        <member name="T:EFCore.Sharding.DistributedTransaction">
            <summary>
            数据库分布式事务,跨库事务
            </summary>
        </member>
        <member name="T:EFCore.Sharding.DistributedTransactionFactory">
            <summary>
            分布式事务工厂
            </summary>
        </member>
        <member name="M:EFCore.Sharding.DistributedTransactionFactory.GetDistributedTransaction">
            <summary>
            获取分布式事务
            </summary>
            <returns></returns>
        </member>
        <member name="T:EFCore.Sharding.IDistributedTransaction">
            <summary>
            分布式
            </summary>
        </member>
        <member name="M:EFCore.Sharding.IDistributedTransaction.AddDbAccessor(EFCore.Sharding.IDbAccessor[])">
            <summary>
            添加Db
            </summary>
            <param name="repositories"></param>
        </member>
        <member name="T:EFCore.Sharding.ITransaction">
            <summary>
            食物操作接口
            </summary>
        </member>
        <member name="M:EFCore.Sharding.ITransaction.RunTransaction(System.Action,System.Data.IsolationLevel)">
            <summary>
            执行事务,具体执行操作包括在action中
            注:支持自定义事务级别,默认为ReadCommitted
            </summary>
            <param name="action">执行操作</param>
            <param name="isolationLevel">事务级别,默认为ReadCommitted</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.ITransaction.RunTransactionAsync(System.Func{System.Threading.Tasks.Task},System.Data.IsolationLevel)">
            <summary>
            执行事务,具体执行操作包括在action中
            注:支持自定义事务级别,默认为ReadCommitted
            </summary>
            <param name="action">执行操作</param>
            <param name="isolationLevel">事务级别,默认为ReadCommitted</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.ITransaction.BeginTransaction(System.Data.IsolationLevel)">
            <summary>
            开始事务
            </summary>
            <param name="isolationLevel"></param>
        </member>
        <member name="M:EFCore.Sharding.ITransaction.BeginTransactionAsync(System.Data.IsolationLevel)">
            <summary>
            开始事务
            </summary>
            <param name="isolationLevel"></param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.ITransaction.CommitTransaction">
            <summary>
            提交事务
            </summary>
        </member>
        <member name="M:EFCore.Sharding.ITransaction.RollbackTransaction">
            <summary>
            回滚事务
            </summary>
        </member>
        <member name="M:EFCore.Sharding.ITransaction.DisposeTransaction">
            <summary>
            释放事务
            </summary>
        </member>
        <member name="T:EFCore.Sharding.AsyncHelper">
            <summary>
            异步转同步,防止ASP.NET中死锁
            https://cpratt.co/async-tips-tricks/
            </summary>
        </member>
        <member name="M:EFCore.Sharding.AsyncHelper.RunSync(System.Func{System.Threading.Tasks.Task})">
            <summary>
            同步执行
            </summary>
            <param name="func">任务</param>
        </member>
        <member name="M:EFCore.Sharding.AsyncHelper.RunSync``1(System.Func{System.Threading.Tasks.Task{``0}})">
            <summary>
            同步执行
            </summary>
            <typeparam name="TResult">返回类型</typeparam>
            <param name="func">任务</param>
            <returns></returns>
        </member>
        <member name="T:EFCore.Sharding.Extention">
            <summary>
            IQueryable"T"的拓展操作
            </summary>
        </member>
        <member name="M:EFCore.Sharding.Extention.GetIQueryable(Microsoft.EntityFrameworkCore.DbContext,System.Type)">
            <summary>
            获取IQueryable
            </summary>
            <param name="context">上下文</param>
            <param name="entityType">实体类型</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.ToList``1(System.Data.DataTable)">
            <summary>
            DataTable转List
            </summary>
            <typeparam name="T">转换类型</typeparam>
            <param name="dt">数据源</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.ToCsvStr(System.Data.DataTable)">
             <summary>
            将DataTable转换为标准的CSV字符串
             </summary>
             <param name="dt">数据表</param>
             <returns>返回标准的CSV</returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.And``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            连接表达式与运算
            </summary>
            <typeparam name="T">参数</typeparam>
            <param name="one">原表达式</param>
            <param name="another">新的表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.Or``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            连接表达式或运算
            </summary>
            <typeparam name="T">参数</typeparam>
            <param name="one">原表达式</param>
            <param name="another">新表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0})">
            <summary>
            给IEnumerable拓展ForEach方法
            </summary>
            <typeparam name="T">模型类</typeparam>
            <param name="iEnumberable">数据源</param>
            <param name="func">方法</param>
        </member>
        <member name="M:EFCore.Sharding.Extention.CastToList``1(System.Collections.IEnumerable)">
            <summary>
            IEnumerable转换为List'T'
            </summary>
            <typeparam name="T">参数</typeparam>
            <param name="source">数据源</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.ToDataTable``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            将IEnumerable'T'转为对应的DataTable
            </summary>
            <typeparam name="T">数据模型</typeparam>
            <param name="iEnumberable">数据源</param>
            <returns>DataTable</returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.RemoveSkip``1(System.Linq.IQueryable{``0})">
            <summary>
            删除Skip表达式
            </summary>
            <typeparam name="T">实体类型</typeparam>
            <param name="source">数据源</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.RemoveSkip(System.Linq.IQueryable)">
            <summary>
            删除Skip表达式
            </summary>
            <param name="source">数据源</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.RemoveTake``1(System.Linq.IQueryable{``0})">
            <summary>
            删除Take表达式
            </summary>
            <typeparam name="T">实体类型</typeparam>
            <param name="source">数据源</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.RemoveTake(System.Linq.IQueryable)">
            <summary>
            删除Take表达式
            </summary>
            <param name="source">数据源</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.GetSkipCount(System.Linq.IQueryable)">
            <summary>
            获取Skip数量
            </summary>
            <param name="source">数据源</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.GetTakeCount(System.Linq.IQueryable)">
            <summary>
            获取Take数量
            </summary>
            <param name="source">数据源</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.GetOrderBy(System.Linq.IQueryable)">
            <summary>
            获取排序参数
            </summary>
            <param name="source">数据源</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.ReplaceQueryable(System.Linq.IQueryable,System.Linq.IQueryable)">
            <summary>
            切换数据源,保留原数据源中的Expression
            </summary>
            <param name="source">原数据源</param>
            <param name="newSource">新数据源</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.ToSql(System.Linq.IQueryable)">
            <summary>
            转为SQL语句，包括参数
            </summary>
            <param name="query">查询原源</param>
            <returns></returns>
        </member>
        <member name="T:EFCore.Sharding.Extention.RemoveSkipVisitor">
            <summary>
            删除Skip表达式
            </summary>
        </member>
        <member name="T:EFCore.Sharding.Extention.RemoveTakeVisitor">
            <summary>
            删除Take表达式
            </summary>
        </member>
        <member name="M:EFCore.Sharding.Extention.IsNullOrEmpty(System.Object)">
            <summary>
            判断是否为Null或者空
            </summary>
            <param name="obj">对象</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.ToJson(System.Object)">
            <summary>
            将对象序列化成Json字符串
            </summary>
            <param name="obj">需要序列化的对象</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.GetPropertyValue(System.Object,System.String)">
            <summary>
            获取某属性值
            </summary>
            <param name="obj">对象</param>
            <param name="propertyName">属性名</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.SetPropertyValue(System.Object,System.String,System.Object)">
            <summary>
            设置某属性值
            </summary>
            <param name="obj">对象</param>
            <param name="propertyName">属性名</param>
            <param name="value">属性值</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.GetGetFieldValue(System.Object,System.String)">
            <summary>
            获取某字段值
            </summary>
            <param name="obj">对象</param>
            <param name="fieldName">字段名</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.SetFieldValue(System.Object,System.String,System.Object)">
            <summary>
            设置某字段值
            </summary>
            <param name="obj">对象</param>
            <param name="fieldName">字段名</param>
            <param name="value">值</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.ChangeType(System.Object,System.Type)">
            <summary>
            改变实体类型
            </summary>
            <param name="obj">对象</param>
            <param name="targetType">目标类型</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.ChangeType``1(System.Object)">
            <summary>
            改变实体类型
            </summary>
            <typeparam name="T">目标泛型</typeparam>
            <param name="obj">对象</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.ChangeType_ByConvert(System.Object,System.Type)">
            <summary>
            改变类型
            </summary>
            <param name="obj">原对象</param>
            <param name="targetType">目标类型</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.ActLike``1(System.Object)">
            <summary>
            生成代理
            </summary>
            <typeparam name="T">代理类型</typeparam>
            <param name="obj">实际类型</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.ToObject``1(System.String)">
            <summary>
            将Json字符串反序列化为对象
            </summary>
            <typeparam name="T">对象类型</typeparam>
            <param name="jsonStr">Json字符串</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.ToObject(System.String,System.Type)">
            <summary>
            将Json字符串反序列化为对象
            </summary>
            <param name="jsonStr">json字符串</param>
            <param name="type">对象类型</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Extention.ToDataTable(System.String)">
            <summary>
            将Json字符串转为DataTable
            </summary>
            <param name="jsonStr">Json字符串</param>
            <returns></returns>
        </member>
        <member name="T:EFCore.Sharding.ParameterReplaceVisitor">
            <summary>
            继承ExpressionVisitor类，实现参数替换统一
            </summary>
        </member>
        <member name="T:EFCore.Sharding.JobHelper">
            <summary>
            任务帮助类
            </summary>
        </member>
        <member name="M:EFCore.Sharding.JobHelper.SetIntervalJob(System.Action,System.TimeSpan)">
            <summary>
            设置一个时间间隔的循环操作
            </summary>
            <param name="action">执行的操作</param>
            <param name="timeSpan">时间间隔</param>
            <returns>任务标识Id</returns>
        </member>
        <member name="M:EFCore.Sharding.JobHelper.SetDailyJob(System.Action,System.Int32,System.Int32,System.Int32)">
            <summary>
            设置每天定时任务
            </summary>
            <param name="action">执行的任务</param>
            <param name="h">时</param>
            <param name="m">分</param>
            <param name="s">秒</param>
            <returns>任务标识Id</returns>
        </member>
        <member name="M:EFCore.Sharding.JobHelper.SetDelayJob(System.Action,System.TimeSpan)">
            <summary>
            设置延时任务,仅执行一次
            </summary>
            <param name="action">执行的操作</param>
            <param name="delay">延时时间</param>
            <returns>任务标识Id</returns>
        </member>
        <member name="M:EFCore.Sharding.JobHelper.SetCronJob(System.Action,System.String)">
            <summary>
            通过表达式创建任务
            表达式规则参考:http://www.jsons.cn/quartzcron/
            </summary>
            <param name="action">执行的操作</param>
            <param name="cronExpression">表达式</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.JobHelper.RemoveJob(System.String)">
            <summary>
            删除任务
            </summary>
            <param name="jobId">任务标识Id</param>
        </member>
        <member name="T:EFCore.Sharding.RandomHelper">
            <summary>
            Random随机数帮助类
            </summary>
        </member>
        <member name="M:EFCore.Sharding.RandomHelper.Next(System.Int32,System.Int32)">
            <summary>
            下一个随机数
            </summary>
            <param name="minValue">最小值</param>
            <param name="maxValue">最大值</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.RandomHelper.Next``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            下一个随机值
            </summary>
            <typeparam name="T">值类型</typeparam>
            <param name="source">值的集合</param>
            <returns></returns>
        </member>
        <member name="M:EFCore.Sharding.Util.XmlHelper.GetProperyCommentBySummary(System.Type)">
            <summary>
            通过XML获取类属性注释
            参考:https://github.com/dotnetcore/FreeSql/blob/d266446062b1dfcd694f7d213191cd2383410025/FreeSql/Internal/CommonUtils.cs
            </summary>
            <param name="type">类型</param>
            <returns>Dict：key=属性名，value=注释</returns>
        </member>
    </members>
</doc>
