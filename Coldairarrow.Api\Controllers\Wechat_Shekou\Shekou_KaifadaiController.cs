﻿using Coldairarrow.Business.Wechat_Shekou;
using Coldairarrow.Entity.Wechat_Shekou;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Microsoft.AspNetCore.Authorization;
using Coldairarrow.Util.Helper;
using Newtonsoft.Json;

namespace Coldairarrow.Api.Controllers.Wechat_Shekou
{
    [Route("/Wechat_Shekou/[controller]/[action]")]
    public class Shekou_KaifadaiController : BaseApiController
    {
        #region DI

        public Shekou_KaifadaiController(IShekou_KaifadaiBusiness shekou_KaifadaiBus)
        {
            _shekou_KaifadaiBus = shekou_KaifadaiBus;
        }

        IShekou_KaifadaiBusiness _shekou_KaifadaiBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Shekou_Kaifadai>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _shekou_KaifadaiBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Shekou_Kaifadai> GetTheData(IdInputDTO input)
        {
            return await _shekou_KaifadaiBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Shekou_Kaifadai data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _shekou_KaifadaiBus.AddDataAsync(data);
            }
            else
            {
                await _shekou_KaifadaiBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _shekou_KaifadaiBus.DeleteDataAsync(ids);
        }

        #endregion
        

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _shekou_KaifadaiBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Shekou_Kaifadai>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _shekou_KaifadaiBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        //查询某日期前未还款的
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult sendMessage()
        {
            var datetimeNow = DateTime.Now;
            var listNow = _shekou_KaifadaiBus.GetListByDate(datetimeNow);
            var listRecent = _shekou_KaifadaiBus.GetListByRecentDate(datetimeNow);
            var numberNow = listNow.Count();
            var numberRecent = listRecent.Count();
            var strNow = "";
            foreach(var i in listNow)
            {
                strNow = strNow + i.Id + ",";
            }
            var strRecent = "";
            foreach (var i in listRecent)
            {
                strRecent = strRecent + i.Id + ",";
            }
            var str = "【招商蛇口重庆公司】开发贷提醒:您有" + numberNow + "份开发贷已过期,编号分别为" + strNow + numberRecent + "份即将到期，编号分别为" + strRecent;
            //var param = new
            //{
            //    Mobile = "18502343023",
            //    Content = str,
            //    Platform = "HR系统",
            //    SMSFrom = "蛇口短信"
            //};
            //var data = JsonConvert.SerializeObject(param);
            //var myUrl = $"http://finetest.cqlandmark.com/CarpApiMsg/sendMsg";
            //var myUrl = $"https://plan.cqlandmark.com/Ext_Base/Helper/SendMessage";
            //string gethtml = MyHttpHelper.HttpPost(myUrl, data);
            var gethtml = SendMessageHelper.SendSMSMsg("18083032050", str);
            var gethtml1 = SendMessageHelper.SendSMSMsg("18502343023", str);
            return Success(gethtml);
        }
        #endregion
    }
}