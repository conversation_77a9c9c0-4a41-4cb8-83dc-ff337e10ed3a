﻿using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Microsoft.Extensions.Logging;
using static org.apache.zookeeper.ZooDefs;
using System.Security.AccessControl;
using Newtonsoft.Json;
using Quartz.Util;
using System.ComponentModel.DataAnnotations.Schema;
using org.omg.PortableInterceptor;
using sun.swing;
using System.Threading;

namespace Coldairarrow.Api.Controllers.Base_Manage
{
    [Route("/Base_Manage/[controller]/[action]")]
    public class Base_HWIpDetailController : BaseApiController
    {
        #region DI

        public Base_HWIpDetailController(IBase_HWIpDetailBusiness base_HWIpDetailBus, ILogger<GlobalExceptionFilter> logger)
        {
            _base_HWIpDetailBus = base_HWIpDetailBus;
            _logger = logger;
        }
        readonly ILogger _logger;
        IBase_HWIpDetailBusiness _base_HWIpDetailBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Base_HWIpDetail>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _base_HWIpDetailBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Base_HWIpDetail> GetTheData(IdInputDTO input)
        {
            return await _base_HWIpDetailBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交
        [NoCheckJWT]
        [HttpPost]
        public async Task SaveData(Base_HWIpDetail data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _base_HWIpDetailBus.AddDataAsync(data);
            }
            else
            {
                await _base_HWIpDetailBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _base_HWIpDetailBus.DeleteDataAsync(ids);
        }
        /// <summary>
        /// 批量获取流量日志是否有黑名单活动异样
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NoCheckJWT]
        public async Task getLockIpByBlackList()
        {
            await _base_HWIpDetailBus.getLockIpByBlackList();
        }
        /// <summary>
        /// 识别exel保存数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task SaveDataByExcel2()
        {
            var directoryPath = @"D:\aliyun";

            try
            {
                var fileTasks = Directory.EnumerateFiles(directoryPath, "*.xlsx")
                    .Select(filePath => Task.Run(async () =>
                    {
                        var fileData = await ProcessExcelFile(filePath);
                        // Apply transformations to fileData here
                        return fileData;
                    }));

                var allExcelData = (await Task.WhenAll(fileTasks))
                    .SelectMany(x => x)
                    .Distinct()
                    .Select(item =>
                    {
                        item.F_StrartTime = DateTime.ParseExact(DateTime.Now.Year.ToString() + "-" + item.F_StrartTimeStr, "yyyy-MM-dd HH:mm:ss", null);
                        item.F_Time = item.F_StrartTime; // Reuse F_StrartTime value
                        item.F_Action = !string.IsNullOrWhiteSpace(item.F_Action) ? item.F_Action : "允许";
                        item.F_Region = "阿里云防护";
                        return item;
                    })
                    .ToList();

                if (allExcelData.Any())
                {
                    await _base_HWIpDetailBus.SaveDataByExcel(allExcelData);

                    var deleteTasks = Directory.EnumerateFiles(directoryPath, "*.xlsx")
                        .Select(filePath => Task.Run(() =>
                        {
                            _logger.LogInformation("删除excel文件,excel地址：" + filePath);
                            System.IO.File.Delete(filePath);
                        }));

                    await Task.WhenAll(deleteTasks);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理Excel文件时发生错误");
            }
        }

        private async Task<List<Base_HWIpDetail>> ProcessExcelFile(string filePath)
        {
            _logger.LogInformation("开始处理文件：" + filePath);

            // 创建一个Hashtable来存储列标题和对应的属性名
            System.Collections.Hashtable headerMap = new System.Collections.Hashtable
            {
                { "F_StrartTimeStr", "时间" },
                { "F_SA", "源地址" },
                { "F_TA", "目的地址" },
                { "F_Action", "动作" },
                { "F_Apply", "URL" }
            };

            List<Base_HWIpDetail> resultList;

            try
            {
                // 如果ExcelImport不是异步的，使用Task.Run
                resultList = await Task.Run(() => new ExcelHelper<Base_HWIpDetail>().ExcelImport(headerMap, filePath));

                if (!resultList.Any())
                {
                    _logger.LogInformation("处理完成：内容为空");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理文件{filePath}时发生错误");
                return new List<Base_HWIpDetail>(); // 直接返回空列表
            }

            if (resultList.Any())
            {
                _logger.LogInformation($"处理完成：解析数据{resultList.Count}条");
            }

            return resultList;
        }
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(10); // 限制并发任务数量为10
        /// <summary>
        /// 识别txt保存数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task SaveDataByExcel()
        {
            var directoryPath = @"D:\aliyun";
            //_logger.LogInformation("识别txt保存数据");

            try
            {
                var fileTasks = Directory.EnumerateFiles(directoryPath, "*.txt")
                   .Where(filePath => new FileInfo(filePath).Length > 0)
                   .Select(filePath => ProcessTxtFileWithSemaphore(filePath));

                var allExcelData = (await Task.WhenAll(fileTasks))
                    .SelectMany(x => x)
                    .Distinct()
                    .ToList();

                if (allExcelData.Any())
                {
                    List<Base_HWIpDetail> base_HWIps = new List<Base_HWIpDetail>();
                    allExcelData.ForEach(item =>
                    {
                        base_HWIps.AddRange(item.details);
                    });
                    await _base_HWIpDetailBus.SaveDataByExcel(base_HWIps);
                    var deleteTasks = allExcelData.Select(x => x.filePath).Select(filePath => DeleteFileWithSemaphore(filePath));
                    await Task.WhenAll(deleteTasks);
                }
                else
                {
                    string[] txtFiles = Directory.GetFiles(directoryPath, "*.txt");

                    // 遍历并删除每个 .txt 文件
                    foreach (string file in txtFiles)
                    {
                        // 获取文件长度
                        long fileLength = new FileInfo(file).Length;
                        if (fileLength == 0)
                        {
                            System.IO.File.Delete(file);
                            Console.WriteLine($"已删除文件: {file}");
                        }
                    }
                }
                //删除所有空白文件
                Directory.EnumerateFiles(directoryPath, "*.txt")
                  .Where(filePath => new FileInfo(filePath).Length <= 0)
                  .ForEach(filePath =>
                  {
                      System.IO.File.Delete(filePath);
                  });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理Excel文件时发生错误");
            }
        }
        private async Task DeleteFileWithSemaphore(string filePath)
        {
            await _semaphore.WaitAsync();
            try
            {
                System.IO.File.Delete(filePath);
            }
            finally
            {
                _semaphore.Release();
            }
        }
        private async Task<List<Base_HWIpDetailTxtDto>> ProcessTxtFileWithSemaphore(string filePath)
        {
            await _semaphore.WaitAsync();
            try
            {
                return await ProcessTxtFile(filePath);
            }
            finally
            {
                _semaphore.Release();
            }
        }
        private async Task<List<Base_HWIpDetailTxtDto>> ProcessTxtFile(string filePath)
        {
            List<Base_HWIpDetailTxtDto> newList = new List<Base_HWIpDetailTxtDto>();

            if (filePath.Contains("CDN-"))
            {
                // 处理CDN日志
                await ProcessLogFile(filePath, newList, ParseCDNLog);
            }
            else if (filePath.Contains("WAF-"))
            {
                // 处理阿里云Waf日志
                await ProcessLogFile(filePath, newList, ParseWafLog);
            }
            else if (filePath.Contains("规则防护-"))
            {
                // 处理阿里云Waf日志
                await ProcessLogFile(filePath, newList, ParseFHLog);
            }
            return newList;
        }

        private async Task ProcessLogFile(string filePath, List<Base_HWIpDetailTxtDto> list, Func<string, string, Base_HWIpDetail, Base_HWIpDetail> parser)
        {
            try
            {
                using (StreamReader reader = new StreamReader(filePath))
                {
                    string line;
                    while ((line = await reader.ReadLineAsync()) != null)
                    {
                        try
                        {
                            Base_HWIpDetail item = parser(filePath, line, new Base_HWIpDetail());
                            Base_HWIpDetailTxtDto dto = new Base_HWIpDetailTxtDto
                            {
                                details = new List<Base_HWIpDetail> { item },
                                filePath = filePath
                            };
                            list.Add(dto);
                        }
                        catch (JsonException ex)
                        {
                            _logger.LogError(ex, $"处理文件{filePath}时发生错误");
                        }
                    }

                }

                if (list.Any())
                {
                    //_logger.LogInformation($"处理完成：解析数据{list.Count}条");
                }
                else
                {
                    //_logger.LogInformation("处理完成：内容为空");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理文件{filePath}时发生错误");
            }
        }

        private Base_HWIpDetail ParseCDNLog(string filePath, string jsonStr, Base_HWIpDetail detail)
        {
            CDNHttpRequestInfo item = JsonConvert.DeserializeObject<CDNHttpRequestInfo>(jsonStr);
            detail.F_Region = "CDN全站加速";
            detail.F_ProtocolType = item.refer_protocol;
            detail.F_SA = item.remote_ip;
            detail.F_Sport = item.remote_port;
            detail.F_TA = item.domain;
            detail.F_Dprot = item.server_port;
            detail.F_Service = item.refer_protocol;
            detail.F_Apply = "全部";
            detail.F_Action = "允许";
            detail.F_StrartTime = detail.F_Time = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddSeconds(item.__time__.ToDouble());
            detail.F_Id = Guid.NewGuid().ToString();
            detail.F_Time = DateTime.Now;
            detail.F_CreateDate = DateTime.Now;
            return detail;
        }

        private Base_HWIpDetail ParseWafLog(string filePath, string jsonStr, Base_HWIpDetail detail)
        {
            WafRequestInfo item = JsonConvert.DeserializeObject<WafRequestInfo>(jsonStr);
            detail.F_Region = "阿里云防护";
            detail.F_ProtocolType = item.querystring.Length > 140 ? item.querystring.Substring(0, 140) : item.querystring;
            detail.F_SA = item.real_client_ip;
            detail.F_Sport = item.remote_port;
            detail.F_TA = item.host.Length > 48 ? item.host.Substring(0, 48) : item.host;
            detail.F_Dprot = item.request_method;
            detail.F_Service = item.request_path.Length > 48 ? item.request_path.Substring(0, 48) : item.request_path;
            detail.F_Apply = "全部";
            detail.F_Action = "允许";
            detail.F_StrartTime = detail.F_Time = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddSeconds(item.__time__.ToDouble());
            detail.F_Id = Guid.NewGuid().ToString();
            detail.F_Time = DateTime.Now;
            detail.F_CreateDate = DateTime.Now;
            return detail;
        }
        private Base_HWIpDetail ParseFHLog(string filePath, string jsonStr, Base_HWIpDetail detail)
        {
            Console.WriteLine("D:\\aliyun\\规则防护-2024-08-16-15-56-00-20468414-信息防泄漏-402.txt");
            var ruleId = ExtractSubstringHyphens(filePath, 2, 3);
            var ruleName = ExtractSubstringHyphens(filePath, 1, 2);
            if (ruleId != null)
            {
                WafRequestInfo item = JsonConvert.DeserializeObject<WafRequestInfo>(jsonStr);
                detail.F_Region = "阿里云规则触发";
                detail.F_ProtocolType = item.querystring.Length > 140 ? item.querystring.Substring(0, 140) : item.querystring;
                detail.F_SA = item.real_client_ip;
                detail.F_Sport = item.remote_port;
                detail.F_TA = item.host.Length > 48 ? item.host.Substring(0, 48) : item.host;
                detail.F_Dprot = item.request_method;
                detail.F_Service = item.request_path.Length > 48 ? item.request_path.Substring(0, 48) : item.request_path;
                detail.F_Apply = ruleName;
                detail.F_Action = ruleId;
                detail.F_StrartTime = detail.F_Time = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddSeconds(item.__time__.ToDouble());
                detail.F_Id = Guid.NewGuid().ToString();
                detail.F_Time = DateTime.Now;
                detail.F_CreateDate = DateTime.Now;
            }

            return detail;
        }
        /// <summary>
        /// 截图字符串
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public string ExtractSubstringHyphens(string input, int startIndex = 0, int endIndex = 0)
        {
            List<int> hyphenPositions = new List<int>();
            for (int i = 0; i < input.Length; i++)
            {
                if (input[i] == '-')
                {
                    hyphenPositions.Add(i);
                }
            }

            int secondLastHyphenPosition = hyphenPositions[hyphenPositions.Count - startIndex];
            int thirdLastHyphenPosition = hyphenPositions[hyphenPositions.Count - endIndex];
            string result = input.Substring(thirdLastHyphenPosition + 1, secondLastHyphenPosition - (thirdLastHyphenPosition + 1));

            return result;
        }
        [HttpPost]
        [NoCheckJWT]
        public async Task UpdateByNoCityData()
        {
            await _base_HWIpDetailBus.UpdateByNoCityData();
        }
        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _base_HWIpDetailBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Base_HWIpDetail>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _base_HWIpDetailBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
    public class Base_HWIpDetailTxtDto
    {
        public List<Base_HWIpDetail> details { get; set; }
        public string filePath { get; set; }
    }
    public class CDNHttpRequestInfo
    {
        /// <summary>
        /// 
        /// </summary>
        public string body_bytes_sent { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string client_ip { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string content_type { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string domain { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string hit_info { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string http_range { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string method { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string proxy_ip { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string refer_domain { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string refer_param { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string refer_protocol { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string refer_uri { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string remote_ip { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string remote_port { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string request_size { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string request_time { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string response_size { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string return_code { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string scheme { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string sent_http_content_range { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string server_addr { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string server_port { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string unixtime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string uri { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string uri_param { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string user_agent { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string user_info { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string uuid { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string via_info { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string xforwordfor { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string __topic__ { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string __source__ { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string __time__ { get; set; }
    }

    public class WafRequestInfo
    {
        /// <summary>
        /// 
        /// </summary>
        public string ssl_protocol { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string request_time_msec { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string matched_host { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string request_method { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string request_traceid { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string status { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string https { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string querystring { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string remote_addr { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string region { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ssl_cipher { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string remote_port { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string request_path { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string host { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string body_bytes_sent { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string content_type { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string real_client_ip { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string http_referer { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string request_length { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string http_x_forwarded_for { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string http_user_agent { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string user_id { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string upstream_response_time { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string server_protocol { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string upstream_status { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string upstream_addr { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string time { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string http_cookie { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string __topic__ { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string __source__ { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string __time__ { get; set; }
    }
}