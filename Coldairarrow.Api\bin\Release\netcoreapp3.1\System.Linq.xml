﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Linq</name>
  </assembly>
  <members>
    <member name="T:System.Linq.Enumerable">
      <summary>Provides a set of <see langword="static" /> (<see langword="Shared" /> in Visual Basic) methods for querying objects that implement <see cref="T:System.Collections.Generic.IEnumerable`1" />.</summary>
    </member>
    <member name="M:System.Linq.Enumerable.Aggregate``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``0,``0})">
      <summary>Applies an accumulator function over a sequence.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to aggregate over.</param>
      <param name="func">An accumulator function to be invoked on each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The final accumulator value.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="func" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Aggregate``2(System.Collections.Generic.IEnumerable{``0},``1,System.Func{``1,``0,``1})">
      <summary>Applies an accumulator function over a sequence. The specified seed value is used as the initial accumulator value.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to aggregate over.</param>
      <param name="seed">The initial accumulator value.</param>
      <param name="func">An accumulator function to be invoked on each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
      <returns>The final accumulator value.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="func" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Aggregate``3(System.Collections.Generic.IEnumerable{``0},``1,System.Func{``1,``0,``1},System.Func{``1,``2})">
      <summary>Applies an accumulator function over a sequence. The specified seed value is used as the initial accumulator value, and the specified function is used to select the result value.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to aggregate over.</param>
      <param name="seed">The initial accumulator value.</param>
      <param name="func">An accumulator function to be invoked on each element.</param>
      <param name="resultSelector">A function to transform the final accumulator value into the result value.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
      <typeparam name="TResult">The type of the resulting value.</typeparam>
      <returns>The transformed final accumulator value.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="func" /> or <paramref name="resultSelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.All``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>Determines whether all elements of a sequence satisfy a condition.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains the elements to apply the predicate to.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="true" /> if every element of the source sequence passes the test in the specified predicate, or if the sequence is empty; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Any``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Determines whether a sequence contains any elements.</summary>
      <param name="source">The <see cref="T:System.Collections.Generic.IEnumerable`1" /> to check for emptiness.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="true" /> if the source sequence contains any elements; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Any``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>Determines whether any element of a sequence satisfies a condition.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements to apply the predicate to.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="true" /> if any elements in the source sequence pass the test in the specified predicate; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Append``1(System.Collections.Generic.IEnumerable{``0},``0)">
      <summary>Appends a value to the end of the sequence.</summary>
      <param name="source">A sequence of values.</param>
      <param name="element">The value to append to <paramref name="source" />.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A new sequence that ends with <paramref name="element" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.AsEnumerable``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns the input typed as <see cref="T:System.Collections.Generic.IEnumerable`1" />.</summary>
      <param name="source">The sequence to type as <see cref="T:System.Collections.Generic.IEnumerable`1" />.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The input sequence typed as <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Decimal})">
      <summary>Computes the average of a sequence of <see cref="T:System.Decimal" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Decimal" /> values to calculate the average of.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Double})">
      <summary>Computes the average of a sequence of <see cref="T:System.Double" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Double" /> values to calculate the average of.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Int32})">
      <summary>Computes the average of a sequence of <see cref="T:System.Int32" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Int32" /> values to calculate the average of.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Int64})">
      <summary>Computes the average of a sequence of <see cref="T:System.Int64" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Int64" /> values to calculate the average of.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Nullable{System.Decimal}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Decimal" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Decimal" /> values to calculate the average of.</param>
      <returns>The average of the sequence of values, or <see langword="null" /> if the source sequence is empty or contains only values that are <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum of the elements in the sequence is larger than <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Nullable{System.Double}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Double" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Double" /> values to calculate the average of.</param>
      <returns>The average of the sequence of values, or <see langword="null" /> if the source sequence is empty or contains only values that are <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Nullable{System.Int32}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Int32" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Int32" /> values to calculate the average of.</param>
      <returns>The average of the sequence of values, or <see langword="null" /> if the source sequence is empty or contains only values that are <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum of the elements in the sequence is larger than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Nullable{System.Int64}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Int64" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Int64" /> values to calculate the average of.</param>
      <returns>The average of the sequence of values, or <see langword="null" /> if the source sequence is empty or contains only values that are <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum of the elements in the sequence is larger than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Nullable{System.Single}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Single" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Single" /> values to calculate the average of.</param>
      <returns>The average of the sequence of values, or <see langword="null" /> if the source sequence is empty or contains only values that are <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average(System.Collections.Generic.IEnumerable{System.Single})">
      <summary>Computes the average of a sequence of <see cref="T:System.Single" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Single" /> values to calculate the average of.</param>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Decimal})">
      <summary>Computes the average of a sequence of <see cref="T:System.Decimal" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
      <exception cref="T:System.OverflowException">The sum of the elements in the sequence is larger than <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Double})">
      <summary>Computes the average of a sequence of <see cref="T:System.Double" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32})">
      <summary>Computes the average of a sequence of <see cref="T:System.Int32" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
      <exception cref="T:System.OverflowException">The sum of the elements in the sequence is larger than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int64})">
      <summary>Computes the average of a sequence of <see cref="T:System.Int64" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of source.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
      <exception cref="T:System.OverflowException">The sum of the elements in the sequence is larger than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Decimal}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Decimal" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values, or <see langword="null" /> if the source sequence is empty or contains only values that are <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum of the elements in the sequence is larger than <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Double}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Double" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values, or <see langword="null" /> if the source sequence is empty or contains only values that are <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int32}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Int32" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values, or <see langword="null" /> if the source sequence is empty or contains only values that are <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum of the elements in the sequence is larger than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int64}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Int64" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values, or <see langword="null" /> if the source sequence is empty or contains only values that are <see langword="null" />.</returns>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Single}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Single" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values, or <see langword="null" /> if the source sequence is empty or contains only values that are <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Average``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Single})">
      <summary>Computes the average of a sequence of <see cref="T:System.Single" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The average of the sequence of values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Cast``1(System.Collections.IEnumerable)">
      <summary>Casts the elements of an <see cref="T:System.Collections.IEnumerable" /> to the specified type.</summary>
      <param name="source">The <see cref="T:System.Collections.IEnumerable" /> that contains the elements to be cast to type <paramref name="TResult" />.</param>
      <typeparam name="TResult">The type to cast the elements of <paramref name="source" /> to.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains each element of the source sequence cast to the specified type.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidCastException">An element in the sequence cannot be cast to type <paramref name="TResult" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Concat``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Concatenates two sequences.</summary>
      <param name="first">The first sequence to concatenate.</param>
      <param name="second">The sequence to concatenate to the first sequence.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains the concatenated elements of the two input sequences.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Contains``1(System.Collections.Generic.IEnumerable{``0},``0)">
      <summary>Determines whether a sequence contains a specified element by using the default equality comparer.</summary>
      <param name="source">A sequence in which to locate a value.</param>
      <param name="value">The value to locate in the sequence.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="true" /> if the source sequence contains an element that has the specified value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Contains``1(System.Collections.Generic.IEnumerable{``0},``0,System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Determines whether a sequence contains a specified element by using a specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <param name="source">A sequence in which to locate a value.</param>
      <param name="value">The value to locate in the sequence.</param>
      <param name="comparer">An equality comparer to compare values.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="true" /> if the source sequence contains an element that has the specified value; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Count``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns the number of elements in a sequence.</summary>
      <param name="source">A sequence that contains elements to be counted.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The number of elements in the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The number of elements in <paramref name="source" /> is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Count``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>Returns a number that represents how many elements in the specified sequence satisfy a condition.</summary>
      <param name="source">A sequence that contains elements to be tested and counted.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A number that represents how many elements in the sequence satisfy the condition in the predicate function.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The number of elements in <paramref name="source" /> is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.DefaultIfEmpty``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns the elements of the specified sequence or the type parameter's default value in a singleton collection if the sequence is empty.</summary>
      <param name="source">The sequence to return a default value for if it is empty.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> object that contains the default value for the <paramref name="TSource" /> type if <paramref name="source" /> is empty; otherwise, <paramref name="source" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.DefaultIfEmpty``1(System.Collections.Generic.IEnumerable{``0},``0)">
      <summary>Returns the elements of the specified sequence or the specified value in a singleton collection if the sequence is empty.</summary>
      <param name="source">The sequence to return the specified value for if it is empty.</param>
      <param name="defaultValue">The value to return if the sequence is empty.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains <paramref name="defaultValue" /> if <paramref name="source" /> is empty; otherwise, <paramref name="source" />.</returns>
    </member>
    <member name="M:System.Linq.Enumerable.Distinct``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns distinct elements from a sequence by using the default equality comparer to compare values.</summary>
      <param name="source">The sequence to remove duplicate elements from.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains distinct elements from the source sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Distinct``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Returns distinct elements from a sequence by using a specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</summary>
      <param name="source">The sequence to remove duplicate elements from.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains distinct elements from the source sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ElementAt``1(System.Collections.Generic.IEnumerable{``0},System.Int32)">
      <summary>Returns the element at a specified index in a sequence.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return an element from.</param>
      <param name="index">The zero-based index of the element to retrieve.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The element at the specified position in the source sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0 or greater than or equal to the number of elements in <paramref name="source" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ElementAtOrDefault``1(System.Collections.Generic.IEnumerable{``0},System.Int32)">
      <summary>Returns the element at a specified index in a sequence or a default value if the index is out of range.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return an element from.</param>
      <param name="index">The zero-based index of the element to retrieve.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="default" />(<paramref name="TSource" />) if the index is outside the bounds of the source sequence; otherwise, the element at the specified position in the source sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Empty``1">
      <summary>Returns an empty <see cref="T:System.Collections.Generic.IEnumerable`1" /> that has the specified type argument.</summary>
      <typeparam name="TResult">The type to assign to the type parameter of the returned generic <see cref="T:System.Collections.Generic.IEnumerable`1" />.</typeparam>
      <returns>An empty <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose type argument is <paramref name="TResult" />.</returns>
    </member>
    <member name="M:System.Linq.Enumerable.Except``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Produces the set difference of two sequences by using the default equality comparer to compare values.</summary>
      <param name="first">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements that are not also in <paramref name="second" /> will be returned.</param>
      <param name="second">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements that also occur in the first sequence will cause those elements to be removed from the returned sequence.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>A sequence that contains the set difference of the elements of two sequences.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Except``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Produces the set difference of two sequences by using the specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</summary>
      <param name="first">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements that are not also in <paramref name="second" /> will be returned.</param>
      <param name="second">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements that also occur in the first sequence will cause those elements to be removed from the returned sequence.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>A sequence that contains the set difference of the elements of two sequences.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.First``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns the first element of a sequence.</summary>
      <param name="source">The <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return the first element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The first element in the specified sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The source sequence is empty.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.First``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the first element in a sequence that satisfies a specified condition.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return an element from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The first element in the sequence that passes the test in the specified predicate function.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">No element satisfies the condition in <paramref name="predicate" />.
-or-
The source sequence is empty.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.FirstOrDefault``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns the first element of a sequence, or a default value if the sequence contains no elements.</summary>
      <param name="source">The <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return the first element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="default" />(<paramref name="TSource" />) if <paramref name="source" /> is empty; otherwise, the first element in <paramref name="source" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.FirstOrDefault``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the first element of the sequence that satisfies a condition or a default value if no such element is found.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return an element from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="default" />(<paramref name="TSource" />) if <paramref name="source" /> is empty or if no element passes the test specified by <paramref name="predicate" />; otherwise, the first element in <paramref name="source" /> that passes the test specified by <paramref name="predicate" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>Groups the elements of a sequence according to a specified key selector function.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An <c>IEnumerable&lt;IGrouping&lt;TKey, TSource&gt;&gt;</c> in C# or <c>IEnumerable(Of IGrouping(Of TKey, TSource))</c> in Visual Basic where each <see cref="T:System.Linq.IGrouping`2" /> object contains a sequence of objects and a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Groups the elements of a sequence according to a specified key selector function and compares the keys by using a specified comparer.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An <c>IEnumerable&lt;IGrouping&lt;TKey, TSource&gt;&gt;</c> in C# or <c>IEnumerable(Of IGrouping(Of TKey, TSource))</c> in Visual Basic where each <see cref="T:System.Linq.IGrouping`2" /> object contains a collection of objects and a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2})">
      <summary>Groups the elements of a sequence according to a specified key selector function and projects the elements for each group by using a specified function.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="elementSelector">A function to map each source element to an element in the <see cref="T:System.Linq.IGrouping`2" />.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the elements in the <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <returns>An <c>IEnumerable&lt;IGrouping&lt;TKey, TElement&gt;&gt;</c> in C# or <c>IEnumerable(Of IGrouping(Of TKey, TElement))</c> in Visual Basic where each <see cref="T:System.Linq.IGrouping`2" /> object contains a collection of objects of type <paramref name="TElement" /> and a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="elementSelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Groups the elements of a sequence according to a key selector function. The keys are compared by using a comparer and each group's elements are projected by using a specified function.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="elementSelector">A function to map each source element to an element in an <see cref="T:System.Linq.IGrouping`2" />.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the elements in the <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <returns>An <c>IEnumerable&lt;IGrouping&lt;TKey, TElement&gt;&gt;</c> in C# or <c>IEnumerable(Of IGrouping(Of TKey, TElement))</c> in Visual Basic where each <see cref="T:System.Linq.IGrouping`2" /> object contains a collection of objects of type <paramref name="TElement" /> and a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="elementSelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2})">
      <summary>Groups the elements of a sequence according to a specified key selector function and creates a result value from each group and its key.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="resultSelector">A function to create a result value from each group.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TResult">The type of the result value returned by <paramref name="resultSelector" />.</typeparam>
      <returns>A collection of elements of type <paramref name="TResult" /> where each element represents a projection over a group and its key.</returns>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Groups the elements of a sequence according to a specified key selector function and creates a result value from each group and its key. The keys are compared by using a specified comparer.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="resultSelector">A function to create a result value from each group.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys with.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TResult">The type of the result value returned by <paramref name="resultSelector" />.</typeparam>
      <returns>A collection of elements of type <paramref name="TResult" /> where each element represents a projection over a group and its key.</returns>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``4(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2},System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3})">
      <summary>Groups the elements of a sequence according to a specified key selector function and creates a result value from each group and its key. The elements of each group are projected by using a specified function.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="elementSelector">A function to map each source element to an element in an <see cref="T:System.Linq.IGrouping`2" />.</param>
      <param name="resultSelector">A function to create a result value from each group.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the elements in each <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <typeparam name="TResult">The type of the result value returned by <paramref name="resultSelector" />.</typeparam>
      <returns>A collection of elements of type <paramref name="TResult" /> where each element represents a projection over a group and its key.</returns>
    </member>
    <member name="M:System.Linq.Enumerable.GroupBy``4(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2},System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Groups the elements of a sequence according to a specified key selector function and creates a result value from each group and its key. Key values are compared by using a specified comparer, and the elements of each group are projected by using a specified function.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="elementSelector">A function to map each source element to an element in an <see cref="T:System.Linq.IGrouping`2" />.</param>
      <param name="resultSelector">A function to create a result value from each group.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys with.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the elements in each <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <typeparam name="TResult">The type of the result value returned by <paramref name="resultSelector" />.</typeparam>
      <returns>A collection of elements of type <paramref name="TResult" /> where each element represents a projection over a group and its key.</returns>
    </member>
    <member name="M:System.Linq.Enumerable.GroupJoin``4(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3})">
      <summary>Correlates the elements of two sequences based on equality of keys and groups the results. The default equality comparer is used to compare keys.</summary>
      <param name="outer">The first sequence to join.</param>
      <param name="inner">The sequence to join to the first sequence.</param>
      <param name="outerKeySelector">A function to extract the join key from each element of the first sequence.</param>
      <param name="innerKeySelector">A function to extract the join key from each element of the second sequence.</param>
      <param name="resultSelector">A function to create a result element from an element from the first sequence and a collection of matching elements from the second sequence.</param>
      <typeparam name="TOuter">The type of the elements of the first sequence.</typeparam>
      <typeparam name="TInner">The type of the elements of the second sequence.</typeparam>
      <typeparam name="TKey">The type of the keys returned by the key selector functions.</typeparam>
      <typeparam name="TResult">The type of the result elements.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains elements of type <paramref name="TResult" /> that are obtained by performing a grouped join on two sequences.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> or <paramref name="inner" /> or <paramref name="outerKeySelector" /> or <paramref name="innerKeySelector" /> or <paramref name="resultSelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.GroupJoin``4(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>Correlates the elements of two sequences based on key equality and groups the results. A specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> is used to compare keys.</summary>
      <param name="outer">The first sequence to join.</param>
      <param name="inner">The sequence to join to the first sequence.</param>
      <param name="outerKeySelector">A function to extract the join key from each element of the first sequence.</param>
      <param name="innerKeySelector">A function to extract the join key from each element of the second sequence.</param>
      <param name="resultSelector">A function to create a result element from an element from the first sequence and a collection of matching elements from the second sequence.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to hash and compare keys.</param>
      <typeparam name="TOuter">The type of the elements of the first sequence.</typeparam>
      <typeparam name="TInner">The type of the elements of the second sequence.</typeparam>
      <typeparam name="TKey">The type of the keys returned by the key selector functions.</typeparam>
      <typeparam name="TResult">The type of the result elements.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains elements of type <paramref name="TResult" /> that are obtained by performing a grouped join on two sequences.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> or <paramref name="inner" /> or <paramref name="outerKeySelector" /> or <paramref name="innerKeySelector" /> or <paramref name="resultSelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Intersect``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Produces the set intersection of two sequences by using the default equality comparer to compare values.</summary>
      <param name="first">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose distinct elements that also appear in <paramref name="second" /> will be returned.</param>
      <param name="second">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose distinct elements that also appear in the first sequence will be returned.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>A sequence that contains the elements that form the set intersection of two sequences.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Intersect``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Produces the set intersection of two sequences by using the specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</summary>
      <param name="first">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose distinct elements that also appear in <paramref name="second" /> will be returned.</param>
      <param name="second">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose distinct elements that also appear in the first sequence will be returned.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>A sequence that contains the elements that form the set intersection of two sequences.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Join``4(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,``1,``3})">
      <summary>Correlates the elements of two sequences based on matching keys. The default equality comparer is used to compare keys.</summary>
      <param name="outer">The first sequence to join.</param>
      <param name="inner">The sequence to join to the first sequence.</param>
      <param name="outerKeySelector">A function to extract the join key from each element of the first sequence.</param>
      <param name="innerKeySelector">A function to extract the join key from each element of the second sequence.</param>
      <param name="resultSelector">A function to create a result element from two matching elements.</param>
      <typeparam name="TOuter">The type of the elements of the first sequence.</typeparam>
      <typeparam name="TInner">The type of the elements of the second sequence.</typeparam>
      <typeparam name="TKey">The type of the keys returned by the key selector functions.</typeparam>
      <typeparam name="TResult">The type of the result elements.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that has elements of type <paramref name="TResult" /> that are obtained by performing an inner join on two sequences.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> or <paramref name="inner" /> or <paramref name="outerKeySelector" /> or <paramref name="innerKeySelector" /> or <paramref name="resultSelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Join``4(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``2},System.Func{``1,``2},System.Func{``0,``1,``3},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>Correlates the elements of two sequences based on matching keys. A specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> is used to compare keys.</summary>
      <param name="outer">The first sequence to join.</param>
      <param name="inner">The sequence to join to the first sequence.</param>
      <param name="outerKeySelector">A function to extract the join key from each element of the first sequence.</param>
      <param name="innerKeySelector">A function to extract the join key from each element of the second sequence.</param>
      <param name="resultSelector">A function to create a result element from two matching elements.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to hash and compare keys.</param>
      <typeparam name="TOuter">The type of the elements of the first sequence.</typeparam>
      <typeparam name="TInner">The type of the elements of the second sequence.</typeparam>
      <typeparam name="TKey">The type of the keys returned by the key selector functions.</typeparam>
      <typeparam name="TResult">The type of the result elements.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that has elements of type <paramref name="TResult" /> that are obtained by performing an inner join on two sequences.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> or <paramref name="inner" /> or <paramref name="outerKeySelector" /> or <paramref name="innerKeySelector" /> or <paramref name="resultSelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Last``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns the last element of a sequence.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return the last element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The value at the last position in the source sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The source sequence is empty.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Last``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the last element of a sequence that satisfies a specified condition.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return an element from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The last element in the sequence that passes the test in the specified predicate function.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">No element satisfies the condition in <paramref name="predicate" />.
-or-
The source sequence is empty.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.LastOrDefault``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns the last element of a sequence, or a default value if the sequence contains no elements.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return the last element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="default" />(<paramref name="TSource" />) if the source sequence is empty; otherwise, the last element in the <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.LastOrDefault``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the last element of a sequence that satisfies a condition or a default value if no such element is found.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return an element from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>
        <see langword="default" />(<paramref name="TSource" />) if the sequence is empty or if no elements pass the test in the predicate function; otherwise, the last element that passes the test in the predicate function.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.LongCount``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns an <see cref="T:System.Int64" /> that represents the total number of elements in a sequence.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains the elements to be counted.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The number of elements in the source sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The number of elements exceeds <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.LongCount``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>Returns an <see cref="T:System.Int64" /> that represents how many elements in a sequence satisfy a condition.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains the elements to be counted.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A number that represents how many elements in the sequence satisfy the condition in the predicate function.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The number of matching elements exceeds <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Decimal})">
      <summary>Returns the maximum value in a sequence of <see cref="T:System.Decimal" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Decimal" /> values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Double})">
      <summary>Returns the maximum value in a sequence of <see cref="T:System.Double" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Double" /> values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Int32})">
      <summary>Returns the maximum value in a sequence of <see cref="T:System.Int32" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Int32" /> values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Int64})">
      <summary>Returns the maximum value in a sequence of <see cref="T:System.Int64" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Int64" /> values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Nullable{System.Decimal}})">
      <summary>Returns the maximum value in a sequence of nullable <see cref="T:System.Decimal" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Decimal" /> values to determine the maximum value of.</param>
      <returns>A value of type <c>Nullable&lt;Decimal&gt;</c> in C# or <c>Nullable(Of Decimal)</c> in Visual Basic that corresponds to the maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Nullable{System.Double}})">
      <summary>Returns the maximum value in a sequence of nullable <see cref="T:System.Double" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Double" /> values to determine the maximum value of.</param>
      <returns>A value of type <c>Nullable&lt;Double&gt;</c> in C# or <c>Nullable(Of Double)</c> in Visual Basic that corresponds to the maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Nullable{System.Int32}})">
      <summary>Returns the maximum value in a sequence of nullable <see cref="T:System.Int32" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Int32" /> values to determine the maximum value of.</param>
      <returns>A value of type <c>Nullable&lt;Int32&gt;</c> in C# or <c>Nullable(Of Int32)</c> in Visual Basic that corresponds to the maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Nullable{System.Int64}})">
      <summary>Returns the maximum value in a sequence of nullable <see cref="T:System.Int64" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Int64" /> values to determine the maximum value of.</param>
      <returns>A value of type <c>Nullable&lt;Int64&gt;</c> in C# or <c>Nullable(Of Int64)</c> in Visual Basic that corresponds to the maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Nullable{System.Single}})">
      <summary>Returns the maximum value in a sequence of nullable <see cref="T:System.Single" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Single" /> values to determine the maximum value of.</param>
      <returns>A value of type <c>Nullable&lt;Single&gt;</c> in C# or <c>Nullable(Of Single)</c> in Visual Basic that corresponds to the maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max(System.Collections.Generic.IEnumerable{System.Single})">
      <summary>Returns the maximum value in a sequence of <see cref="T:System.Single" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Single" /> values to determine the maximum value of.</param>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns the maximum value in a generic sequence.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">No object in <paramref name="source" /> implements the <see cref="T:System.IComparable" /> or <see cref="T:System.IComparable`1" /> interface.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Decimal})">
      <summary>Invokes a transform function on each element of a sequence and returns the maximum <see cref="T:System.Decimal" /> value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Double})">
      <summary>Invokes a transform function on each element of a sequence and returns the maximum <see cref="T:System.Double" /> value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32})">
      <summary>Invokes a transform function on each element of a sequence and returns the maximum <see cref="T:System.Int32" /> value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int64})">
      <summary>Invokes a transform function on each element of a sequence and returns the maximum <see cref="T:System.Int64" /> value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Decimal}})">
      <summary>Invokes a transform function on each element of a sequence and returns the maximum nullable <see cref="T:System.Decimal" /> value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The value of type <c>Nullable&lt;Decimal&gt;</c> in C# or <c>Nullable(Of Decimal)</c> in Visual Basic that corresponds to the maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Double}})">
      <summary>Invokes a transform function on each element of a sequence and returns the maximum nullable <see cref="T:System.Double" /> value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The value of type <c>Nullable&lt;Double&gt;</c> in C# or <c>Nullable(Of Double)</c> in Visual Basic that corresponds to the maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int32}})">
      <summary>Invokes a transform function on each element of a sequence and returns the maximum nullable <see cref="T:System.Int32" /> value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The value of type <c>Nullable&lt;Int32&gt;</c> in C# or <c>Nullable(Of Int32)</c> in Visual Basic that corresponds to the maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int64}})">
      <summary>Invokes a transform function on each element of a sequence and returns the maximum nullable <see cref="T:System.Int64" /> value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The value of type <c>Nullable&lt;Int64&gt;</c> in C# or <c>Nullable(Of Int64)</c> in Visual Basic that corresponds to the maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Single}})">
      <summary>Invokes a transform function on each element of a sequence and returns the maximum nullable <see cref="T:System.Single" /> value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The value of type <c>Nullable&lt;Single&gt;</c> in C# or <c>Nullable(Of Single)</c> in Visual Basic that corresponds to the maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Single})">
      <summary>Invokes a transform function on each element of a sequence and returns the maximum <see cref="T:System.Single" /> value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Max``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>Invokes a transform function on each element of a generic sequence and returns the maximum resulting value.</summary>
      <param name="source">A sequence of values to determine the maximum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the value returned by <paramref name="selector" />.</typeparam>
      <returns>The maximum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Decimal})">
      <summary>Returns the minimum value in a sequence of <see cref="T:System.Decimal" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Decimal" /> values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Double})">
      <summary>Returns the minimum value in a sequence of <see cref="T:System.Double" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Double" /> values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Int32})">
      <summary>Returns the minimum value in a sequence of <see cref="T:System.Int32" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Int32" /> values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Int64})">
      <summary>Returns the minimum value in a sequence of <see cref="T:System.Int64" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Int64" /> values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Nullable{System.Decimal}})">
      <summary>Returns the minimum value in a sequence of nullable <see cref="T:System.Decimal" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Decimal" /> values to determine the minimum value of.</param>
      <returns>A value of type <c>Nullable&lt;Decimal&gt;</c> in C# or <c>Nullable(Of Decimal)</c> in Visual Basic that corresponds to the minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Nullable{System.Double}})">
      <summary>Returns the minimum value in a sequence of nullable <see cref="T:System.Double" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Double" /> values to determine the minimum value of.</param>
      <returns>A value of type <c>Nullable&lt;Double&gt;</c> in C# or <c>Nullable(Of Double)</c> in Visual Basic that corresponds to the minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Nullable{System.Int32}})">
      <summary>Returns the minimum value in a sequence of nullable <see cref="T:System.Int32" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Int32" /> values to determine the minimum value of.</param>
      <returns>A value of type <c>Nullable&lt;Int32&gt;</c> in C# or <c>Nullable(Of Int32)</c> in Visual Basic that corresponds to the minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Nullable{System.Int64}})">
      <summary>Returns the minimum value in a sequence of nullable <see cref="T:System.Int64" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Int64" /> values to determine the minimum value of.</param>
      <returns>A value of type <c>Nullable&lt;Int64&gt;</c> in C# or <c>Nullable(Of Int64)</c> in Visual Basic that corresponds to the minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Nullable{System.Single}})">
      <summary>Returns the minimum value in a sequence of nullable <see cref="T:System.Single" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Single" /> values to determine the minimum value of.</param>
      <returns>A value of type <c>Nullable&lt;Single&gt;</c> in C# or <c>Nullable(Of Single)</c> in Visual Basic that corresponds to the minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min(System.Collections.Generic.IEnumerable{System.Single})">
      <summary>Returns the minimum value in a sequence of <see cref="T:System.Single" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Single" /> values to determine the minimum value of.</param>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns the minimum value in a generic sequence.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">No object in <paramref name="source" /> implements the <see cref="T:System.IComparable" /> or <see cref="T:System.IComparable`1" /> interface.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Decimal})">
      <summary>Invokes a transform function on each element of a sequence and returns the minimum <see cref="T:System.Decimal" /> value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Double})">
      <summary>Invokes a transform function on each element of a sequence and returns the minimum <see cref="T:System.Double" /> value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32})">
      <summary>Invokes a transform function on each element of a sequence and returns the minimum <see cref="T:System.Int32" /> value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int64})">
      <summary>Invokes a transform function on each element of a sequence and returns the minimum <see cref="T:System.Int64" /> value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Decimal}})">
      <summary>Invokes a transform function on each element of a sequence and returns the minimum nullable <see cref="T:System.Decimal" /> value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The value of type <c>Nullable&lt;Decimal&gt;</c> in C# or <c>Nullable(Of Decimal)</c> in Visual Basic that corresponds to the minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Double}})">
      <summary>Invokes a transform function on each element of a sequence and returns the minimum nullable <see cref="T:System.Double" /> value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The value of type <c>Nullable&lt;Double&gt;</c> in C# or <c>Nullable(Of Double)</c> in Visual Basic that corresponds to the minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int32}})">
      <summary>Invokes a transform function on each element of a sequence and returns the minimum nullable <see cref="T:System.Int32" /> value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The value of type <c>Nullable&lt;Int32&gt;</c> in C# or <c>Nullable(Of Int32)</c> in Visual Basic that corresponds to the minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int64}})">
      <summary>Invokes a transform function on each element of a sequence and returns the minimum nullable <see cref="T:System.Int64" /> value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The value of type <c>Nullable&lt;Int64&gt;</c> in C# or <c>Nullable(Of Int64)</c> in Visual Basic that corresponds to the minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Single}})">
      <summary>Invokes a transform function on each element of a sequence and returns the minimum nullable <see cref="T:System.Single" /> value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The value of type <c>Nullable&lt;Single&gt;</c> in C# or <c>Nullable(Of Single)</c> in Visual Basic that corresponds to the minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Single})">
      <summary>Invokes a transform function on each element of a sequence and returns the minimum <see cref="T:System.Single" /> value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Min``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>Invokes a transform function on each element of a generic sequence and returns the minimum resulting value.</summary>
      <param name="source">A sequence of values to determine the minimum value of.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the value returned by <paramref name="selector" />.</typeparam>
      <returns>The minimum value in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.OfType``1(System.Collections.IEnumerable)">
      <summary>Filters the elements of an <see cref="T:System.Collections.IEnumerable" /> based on a specified type.</summary>
      <param name="source">The <see cref="T:System.Collections.IEnumerable" /> whose elements to filter.</param>
      <typeparam name="TResult">The type to filter the elements of the sequence on.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains elements from the input sequence of type <paramref name="TResult" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.OrderBy``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>Sorts the elements of a sequence in ascending order according to a key.</summary>
      <param name="source">A sequence of values to order.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An <see cref="T:System.Linq.IOrderedEnumerable`1" /> whose elements are sorted according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.OrderBy``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IComparer{``1})">
      <summary>Sorts the elements of a sequence in ascending order by using a specified comparer.</summary>
      <param name="source">A sequence of values to order.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An <see cref="T:System.Linq.IOrderedEnumerable`1" /> whose elements are sorted according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.OrderByDescending``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>Sorts the elements of a sequence in descending order according to a key.</summary>
      <param name="source">A sequence of values to order.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An <see cref="T:System.Linq.IOrderedEnumerable`1" /> whose elements are sorted in descending order according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.OrderByDescending``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IComparer{``1})">
      <summary>Sorts the elements of a sequence in descending order by using a specified comparer.</summary>
      <param name="source">A sequence of values to order.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An <see cref="T:System.Linq.IOrderedEnumerable`1" /> whose elements are sorted in descending order according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Prepend``1(System.Collections.Generic.IEnumerable{``0},``0)">
      <summary>Adds a value to the beginning of the sequence.</summary>
      <param name="source">A sequence of values.</param>
      <param name="element">The value to prepend to <paramref name="source" />.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A new sequence that begins with <paramref name="element" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Range(System.Int32,System.Int32)">
      <summary>Generates a sequence of integral numbers within a specified range.</summary>
      <param name="start">The value of the first integer in the sequence.</param>
      <param name="count">The number of sequential integers to generate.</param>
      <returns>An <c>IEnumerable&lt;Int32&gt;</c> in C# or <c>IEnumerable(Of Int32)</c> in Visual Basic that contains a range of sequential integral numbers.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is less than 0.
-or-
<paramref name="start" /> + <paramref name="count" /> -1 is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Repeat``1(``0,System.Int32)">
      <summary>Generates a sequence that contains one repeated value.</summary>
      <param name="element">The value to be repeated.</param>
      <param name="count">The number of times to repeat the value in the generated sequence.</param>
      <typeparam name="TResult">The type of the value to be repeated in the result sequence.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains a repeated value.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is less than 0.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Reverse``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Inverts the order of the elements in a sequence.</summary>
      <param name="source">A sequence of values to reverse.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A sequence whose elements correspond to those of the input sequence in reverse order.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Select``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>Projects each element of a sequence into a new form.</summary>
      <param name="source">A sequence of values to invoke a transform function on.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the value returned by <paramref name="selector" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements are the result of invoking the transform function on each element of <paramref name="source" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Select``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32,``1})">
      <summary>Projects each element of a sequence into a new form by incorporating the element's index.</summary>
      <param name="source">A sequence of values to invoke a transform function on.</param>
      <param name="selector">A transform function to apply to each source element; the second parameter of the function represents the index of the source element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the value returned by <paramref name="selector" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements are the result of invoking the transform function on each element of <paramref name="source" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SelectMany``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Collections.Generic.IEnumerable{``1}})">
      <summary>Projects each element of a sequence to an <see cref="T:System.Collections.Generic.IEnumerable`1" /> and flattens the resulting sequences into one sequence.</summary>
      <param name="source">A sequence of values to project.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the elements of the sequence returned by <paramref name="selector" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements are the result of invoking the one-to-many transform function on each element of the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SelectMany``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}})">
      <summary>Projects each element of a sequence to an <see cref="T:System.Collections.Generic.IEnumerable`1" />, and flattens the resulting sequences into one sequence. The index of each source element is used in the projected form of that element.</summary>
      <param name="source">A sequence of values to project.</param>
      <param name="selector">A transform function to apply to each source element; the second parameter of the function represents the index of the source element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the elements of the sequence returned by <paramref name="selector" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements are the result of invoking the one-to-many transform function on each element of an input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SelectMany``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Collections.Generic.IEnumerable{``1}},System.Func{``0,``1,``2})">
      <summary>Projects each element of a sequence to an <see cref="T:System.Collections.Generic.IEnumerable`1" />, flattens the resulting sequences into one sequence, and invokes a result selector function on each element therein.</summary>
      <param name="source">A sequence of values to project.</param>
      <param name="collectionSelector">A transform function to apply to each element of the input sequence.</param>
      <param name="resultSelector">A transform function to apply to each element of the intermediate sequence.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TCollection">The type of the intermediate elements collected by <paramref name="collectionSelector" />.</typeparam>
      <typeparam name="TResult">The type of the elements of the resulting sequence.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements are the result of invoking the one-to-many transform function <paramref name="collectionSelector" /> on each element of <paramref name="source" /> and then mapping each of those sequence elements and their corresponding source element to a result element.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="collectionSelector" /> or <paramref name="resultSelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SelectMany``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}},System.Func{``0,``1,``2})">
      <summary>Projects each element of a sequence to an <see cref="T:System.Collections.Generic.IEnumerable`1" />, flattens the resulting sequences into one sequence, and invokes a result selector function on each element therein. The index of each source element is used in the intermediate projected form of that element.</summary>
      <param name="source">A sequence of values to project.</param>
      <param name="collectionSelector">A transform function to apply to each source element; the second parameter of the function represents the index of the source element.</param>
      <param name="resultSelector">A transform function to apply to each element of the intermediate sequence.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TCollection">The type of the intermediate elements collected by <paramref name="collectionSelector" />.</typeparam>
      <typeparam name="TResult">The type of the elements of the resulting sequence.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements are the result of invoking the one-to-many transform function <paramref name="collectionSelector" /> on each element of <paramref name="source" /> and then mapping each of those sequence elements and their corresponding source element to a result element.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="collectionSelector" /> or <paramref name="resultSelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SequenceEqual``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Determines whether two sequences are equal by comparing the elements by using the default equality comparer for their type.</summary>
      <param name="first">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to compare to <paramref name="second" />.</param>
      <param name="second">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to compare to the first sequence.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>
        <see langword="true" /> if the two source sequences are of equal length and their corresponding elements are equal according to the default equality comparer for their type; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SequenceEqual``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Determines whether two sequences are equal by comparing their elements by using a specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <param name="first">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to compare to <paramref name="second" />.</param>
      <param name="second">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to compare to the first sequence.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to use to compare elements.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>
        <see langword="true" /> if the two source sequences are of equal length and their corresponding elements compare equal according to <paramref name="comparer" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Single``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns the only element of a sequence, and throws an exception if there is not exactly one element in the sequence.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return the single element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The single element of the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The input sequence contains more than one element.
-or-
The input sequence is empty.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Single``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the only element of a sequence that satisfies a specified condition, and throws an exception if more than one such element exists.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return a single element from.</param>
      <param name="predicate">A function to test an element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The single element of the input sequence that satisfies a condition.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">No element satisfies the condition in <paramref name="predicate" />.
-or-
More than one element satisfies the condition in <paramref name="predicate" />.
-or-
The source sequence is empty.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SingleOrDefault``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns the only element of a sequence, or a default value if the sequence is empty; this method throws an exception if there is more than one element in the sequence.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return the single element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The single element of the input sequence, or <see langword="default" />(<paramref name="TSource" />) if the sequence contains no elements.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The input sequence contains more than one element.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SingleOrDefault``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>Returns the only element of a sequence that satisfies a specified condition or a default value if no such element exists; this method throws an exception if more than one element satisfies the condition.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return a single element from.</param>
      <param name="predicate">A function to test an element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The single element of the input sequence that satisfies the condition, or <see langword="default" />(<paramref name="TSource" />) if no such element is found.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">More than one element satisfies the condition in <paramref name="predicate" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Skip``1(System.Collections.Generic.IEnumerable{``0},System.Int32)">
      <summary>Bypasses a specified number of elements in a sequence and then returns the remaining elements.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return elements from.</param>
      <param name="count">The number of elements to skip before returning the remaining elements.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains the elements that occur after the specified index in the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SkipLast``1(System.Collections.Generic.IEnumerable{``0},System.Int32)">
      <param name="source" />
      <param name="count" />
      <typeparam name="TSource" />
    </member>
    <member name="M:System.Linq.Enumerable.SkipWhile``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>Bypasses elements in a sequence as long as a specified condition is true and then returns the remaining elements.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return elements from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains the elements from the input sequence starting at the first element in the linear series that does not pass the test specified by <paramref name="predicate" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.SkipWhile``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32,System.Boolean})">
      <summary>Bypasses elements in a sequence as long as a specified condition is true and then returns the remaining elements. The element's index is used in the logic of the predicate function.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to return elements from.</param>
      <param name="predicate">A function to test each source element for a condition; the second parameter of the function represents the index of the source element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains the elements from the input sequence starting at the first element in the linear series that does not pass the test specified by <paramref name="predicate" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Decimal})">
      <summary>Computes the sum of a sequence of <see cref="T:System.Decimal" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Decimal" /> values to calculate the sum of.</param>
      <returns>The sum of the values in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Double})">
      <summary>Computes the sum of a sequence of <see cref="T:System.Double" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Double" /> values to calculate the sum of.</param>
      <returns>The sum of the values in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Int32})">
      <summary>Computes the sum of a sequence of <see cref="T:System.Int32" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Int32" /> values to calculate the sum of.</param>
      <returns>The sum of the values in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Int64})">
      <summary>Computes the sum of a sequence of <see cref="T:System.Int64" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Int64" /> values to calculate the sum of.</param>
      <returns>The sum of the values in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Nullable{System.Decimal}})">
      <summary>Computes the sum of a sequence of nullable <see cref="T:System.Decimal" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Decimal" /> values to calculate the sum of.</param>
      <returns>The sum of the values in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Nullable{System.Double}})">
      <summary>Computes the sum of a sequence of nullable <see cref="T:System.Double" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Double" /> values to calculate the sum of.</param>
      <returns>The sum of the values in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Nullable{System.Int32}})">
      <summary>Computes the sum of a sequence of nullable <see cref="T:System.Int32" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Int32" /> values to calculate the sum of.</param>
      <returns>The sum of the values in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Nullable{System.Int64}})">
      <summary>Computes the sum of a sequence of nullable <see cref="T:System.Int64" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Int64" /> values to calculate the sum of.</param>
      <returns>The sum of the values in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Nullable{System.Single}})">
      <summary>Computes the sum of a sequence of nullable <see cref="T:System.Single" /> values.</summary>
      <param name="source">A sequence of nullable <see cref="T:System.Single" /> values to calculate the sum of.</param>
      <returns>The sum of the values in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum(System.Collections.Generic.IEnumerable{System.Single})">
      <summary>Computes the sum of a sequence of <see cref="T:System.Single" /> values.</summary>
      <param name="source">A sequence of <see cref="T:System.Single" /> values to calculate the sum of.</param>
      <returns>The sum of the values in the sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Decimal})">
      <summary>Computes the sum of the sequence of <see cref="T:System.Decimal" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate a sum.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Double})">
      <summary>Computes the sum of the sequence of <see cref="T:System.Double" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate a sum.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32})">
      <summary>Computes the sum of the sequence of <see cref="T:System.Int32" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate a sum.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int64})">
      <summary>Computes the sum of the sequence of <see cref="T:System.Int64" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate a sum.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Decimal}})">
      <summary>Computes the sum of the sequence of nullable <see cref="T:System.Decimal" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate a sum.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Double}})">
      <summary>Computes the sum of the sequence of nullable <see cref="T:System.Double" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate a sum.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int32}})">
      <summary>Computes the sum of the sequence of nullable <see cref="T:System.Int32" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate a sum.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Int64}})">
      <summary>Computes the sum of the sequence of nullable <see cref="T:System.Int64" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate a sum.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Nullable{System.Single}})">
      <summary>Computes the sum of the sequence of nullable <see cref="T:System.Single" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate a sum.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Sum``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Single})">
      <summary>Computes the sum of the sequence of <see cref="T:System.Single" /> values that are obtained by invoking a transform function on each element of the input sequence.</summary>
      <param name="source">A sequence of values that are used to calculate a sum.</param>
      <param name="selector">A transform function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>The sum of the projected values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Take``1(System.Collections.Generic.IEnumerable{``0},System.Int32)">
      <summary>Returns a specified number of contiguous elements from the start of a sequence.</summary>
      <param name="source">The sequence to return elements from.</param>
      <param name="count">The number of elements to return.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains the specified number of elements from the start of the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.TakeLast``1(System.Collections.Generic.IEnumerable{``0},System.Int32)">
      <param name="source" />
      <param name="count" />
      <typeparam name="TSource" />
    </member>
    <member name="M:System.Linq.Enumerable.TakeWhile``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>Returns elements from a sequence as long as a specified condition is true.</summary>
      <param name="source">A sequence to return elements from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains the elements from the input sequence that occur before the element at which the test no longer passes.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.TakeWhile``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32,System.Boolean})">
      <summary>Returns elements from a sequence as long as a specified condition is true. The element's index is used in the logic of the predicate function.</summary>
      <param name="source">The sequence to return elements from.</param>
      <param name="predicate">A function to test each source element for a condition; the second parameter of the function represents the index of the source element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains elements from the input sequence that occur before the element at which the test no longer passes.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ThenBy``2(System.Linq.IOrderedEnumerable{``0},System.Func{``0,``1})">
      <summary>Performs a subsequent ordering of the elements in a sequence in ascending order according to a key.</summary>
      <param name="source">An <see cref="T:System.Linq.IOrderedEnumerable`1" /> that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An <see cref="T:System.Linq.IOrderedEnumerable`1" /> whose elements are sorted according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ThenBy``2(System.Linq.IOrderedEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IComparer{``1})">
      <summary>Performs a subsequent ordering of the elements in a sequence in ascending order by using a specified comparer.</summary>
      <param name="source">An <see cref="T:System.Linq.IOrderedEnumerable`1" /> that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An <see cref="T:System.Linq.IOrderedEnumerable`1" /> whose elements are sorted according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ThenByDescending``2(System.Linq.IOrderedEnumerable{``0},System.Func{``0,``1})">
      <summary>Performs a subsequent ordering of the elements in a sequence in descending order, according to a key.</summary>
      <param name="source">An <see cref="T:System.Linq.IOrderedEnumerable`1" /> that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An <see cref="T:System.Linq.IOrderedEnumerable`1" /> whose elements are sorted in descending order according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ThenByDescending``2(System.Linq.IOrderedEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IComparer{``1})">
      <summary>Performs a subsequent ordering of the elements in a sequence in descending order by using a specified comparer.</summary>
      <param name="source">An <see cref="T:System.Linq.IOrderedEnumerable`1" /> that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>An <see cref="T:System.Linq.IOrderedEnumerable`1" /> whose elements are sorted in descending order according to a key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToArray``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Creates an array from a <see cref="T:System.Collections.Generic.IEnumerable`1" />.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to create an array from.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>An array that contains the elements from the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToDictionary``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>Creates a <see cref="T:System.Collections.Generic.Dictionary`2" /> from an <see cref="T:System.Collections.Generic.IEnumerable`1" /> according to a specified key selector function.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to create a <see cref="T:System.Collections.Generic.Dictionary`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>A <see cref="T:System.Collections.Generic.Dictionary`2" /> that contains keys and values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is <see langword="null" />.
-or-
<paramref name="keySelector" /> produces a key that is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keySelector" /> produces duplicate keys for two elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToDictionary``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Creates a <see cref="T:System.Collections.Generic.Dictionary`2" /> from an <see cref="T:System.Collections.Generic.IEnumerable`1" /> according to a specified key selector function and key comparer.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to create a <see cref="T:System.Collections.Generic.Dictionary`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the keys returned by <paramref name="keySelector" />.</typeparam>
      <returns>A <see cref="T:System.Collections.Generic.Dictionary`2" /> that contains keys and values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is <see langword="null" />.
-or-
<paramref name="keySelector" /> produces a key that is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keySelector" /> produces duplicate keys for two elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToDictionary``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2})">
      <summary>Creates a <see cref="T:System.Collections.Generic.Dictionary`2" /> from an <see cref="T:System.Collections.Generic.IEnumerable`1" /> according to specified key selector and element selector functions.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to create a <see cref="T:System.Collections.Generic.Dictionary`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="elementSelector">A transform function to produce a result element value from each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the value returned by <paramref name="elementSelector" />.</typeparam>
      <returns>A <see cref="T:System.Collections.Generic.Dictionary`2" /> that contains values of type <paramref name="TElement" /> selected from the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="elementSelector" /> is <see langword="null" />.
-or-
<paramref name="keySelector" /> produces a key that is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keySelector" /> produces duplicate keys for two elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToDictionary``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Creates a <see cref="T:System.Collections.Generic.Dictionary`2" /> from an <see cref="T:System.Collections.Generic.IEnumerable`1" /> according to a specified key selector function, a comparer, and an element selector function.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to create a <see cref="T:System.Collections.Generic.Dictionary`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="elementSelector">A transform function to produce a result element value from each element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the value returned by <paramref name="elementSelector" />.</typeparam>
      <returns>A <see cref="T:System.Collections.Generic.Dictionary`2" /> that contains values of type <paramref name="TElement" /> selected from the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="elementSelector" /> is <see langword="null" />.
-or-
<paramref name="keySelector" /> produces a key that is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keySelector" /> produces duplicate keys for two elements.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToHashSet``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Creates a <see cref="T:System.Collections.Generic.HashSet`1" /> from an <see cref="T:System.Collections.Generic.IEnumerable`1" />.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to create a <see cref="T:System.Collections.Generic.HashSet`1" /> from.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A <see cref="T:System.Collections.Generic.HashSet`1" /> that contains values of type TSource selected from the input sequence.</returns>
    </member>
    <member name="M:System.Linq.Enumerable.ToHashSet``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Creates a <see cref="T:System.Collections.Generic.HashSet`1" /> from an <see cref="T:System.Collections.Generic.IEnumerable`1" /> using the <paramref name="comparer" /> to compare keys.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to create a <see cref="T:System.Collections.Generic.HashSet`1" /> from.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A <see cref="T:System.Collections.Generic.HashSet`1" /> that contains values of type <paramref name="TSource" /> selected from the input sequence.</returns>
    </member>
    <member name="M:System.Linq.Enumerable.ToList``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Creates a <see cref="T:System.Collections.Generic.List`1" /> from an <see cref="T:System.Collections.Generic.IEnumerable`1" />.</summary>
      <param name="source">The <see cref="T:System.Collections.Generic.IEnumerable`1" /> to create a <see cref="T:System.Collections.Generic.List`1" /> from.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>A <see cref="T:System.Collections.Generic.List`1" /> that contains elements from the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToLookup``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1})">
      <summary>Creates a <see cref="T:System.Linq.Lookup`2" /> from an <see cref="T:System.Collections.Generic.IEnumerable`1" /> according to a specified key selector function.</summary>
      <param name="source">The <see cref="T:System.Collections.Generic.IEnumerable`1" /> to create a <see cref="T:System.Linq.Lookup`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>A <see cref="T:System.Linq.Lookup`2" /> that contains keys and values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToLookup``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Creates a <see cref="T:System.Linq.Lookup`2" /> from an <see cref="T:System.Collections.Generic.IEnumerable`1" /> according to a specified key selector function and key comparer.</summary>
      <param name="source">The <see cref="T:System.Collections.Generic.IEnumerable`1" /> to create a <see cref="T:System.Linq.Lookup`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <returns>A <see cref="T:System.Linq.Lookup`2" /> that contains keys and values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToLookup``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2})">
      <summary>Creates a <see cref="T:System.Linq.Lookup`2" /> from an <see cref="T:System.Collections.Generic.IEnumerable`1" /> according to specified key selector and element selector functions.</summary>
      <param name="source">The <see cref="T:System.Collections.Generic.IEnumerable`1" /> to create a <see cref="T:System.Linq.Lookup`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="elementSelector">A transform function to produce a result element value from each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the value returned by <paramref name="elementSelector" />.</typeparam>
      <returns>A <see cref="T:System.Linq.Lookup`2" /> that contains values of type <paramref name="TElement" /> selected from the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="elementSelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.ToLookup``3(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``2},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Creates a <see cref="T:System.Linq.Lookup`2" /> from an <see cref="T:System.Collections.Generic.IEnumerable`1" /> according to a specified key selector function, a comparer and an element selector function.</summary>
      <param name="source">The <see cref="T:System.Collections.Generic.IEnumerable`1" /> to create a <see cref="T:System.Linq.Lookup`2" /> from.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="elementSelector">A transform function to produce a result element value from each element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the value returned by <paramref name="elementSelector" />.</typeparam>
      <returns>A <see cref="T:System.Linq.Lookup`2" /> that contains values of type <paramref name="TElement" /> selected from the input sequence.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="elementSelector" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Union``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Produces the set union of two sequences by using the default equality comparer.</summary>
      <param name="first">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose distinct elements form the first set for the union.</param>
      <param name="second">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose distinct elements form the second set for the union.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains the elements from both input sequences, excluding duplicates.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Union``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Produces the set union of two sequences by using a specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <param name="first">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose distinct elements form the first set for the union.</param>
      <param name="second">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose distinct elements form the second set for the union.</param>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains the elements from both input sequences, excluding duplicates.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Where``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Boolean})">
      <summary>Filters a sequence of values based on a predicate.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to filter.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains elements from the input sequence that satisfy the condition.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Where``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32,System.Boolean})">
      <summary>Filters a sequence of values based on a predicate. Each element's index is used in the logic of the predicate function.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> to filter.</param>
      <param name="predicate">A function to test each source element for a condition; the second parameter of the function represents the index of the source element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains elements from the input sequence that satisfy the condition.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Linq.Enumerable.Zip``2(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``1})">
      <summary>Produces a sequence of tuples with elements from the two specified sequences.</summary>
      <param name="first">The first sequence to merge.</param>
      <param name="second">The second sequence to merge.</param>
      <typeparam name="TFirst">The type of the elements of the first input sequence.</typeparam>
      <typeparam name="TSecond">The type of the elements of the second input sequence.</typeparam>
      <returns>A sequence of tuples with elements taken from the first and second sequences, in that order.</returns>
    </member>
    <member name="M:System.Linq.Enumerable.Zip``3(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``1},System.Func{``0,``1,``2})">
      <summary>Applies a specified function to the corresponding elements of two sequences, producing a sequence of the results.</summary>
      <param name="first">The first sequence to merge.</param>
      <param name="second">The second sequence to merge.</param>
      <param name="resultSelector">A function that specifies how to merge the elements from the two sequences.</param>
      <typeparam name="TFirst">The type of the elements of the first input sequence.</typeparam>
      <typeparam name="TSecond">The type of the elements of the second input sequence.</typeparam>
      <typeparam name="TResult">The type of the elements of the result sequence.</typeparam>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains merged elements of two input sequences.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="first" /> or <paramref name="second" /> is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Linq.IGrouping`2">
      <summary>Represents a collection of objects that have a common key.</summary>
      <typeparam name="TKey">The type of the key of the <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <typeparam name="TElement">The type of the values in the <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
    </member>
    <member name="P:System.Linq.IGrouping`2.Key">
      <summary>Gets the key of the <see cref="T:System.Linq.IGrouping`2" />.</summary>
      <returns>The key of the <see cref="T:System.Linq.IGrouping`2" />.</returns>
    </member>
    <member name="T:System.Linq.ILookup`2">
      <summary>Defines an indexer, size property, and Boolean search method for data structures that map keys to <see cref="T:System.Collections.Generic.IEnumerable`1" /> sequences of values.</summary>
      <typeparam name="TKey">The type of the keys in the <see cref="T:System.Linq.ILookup`2" />.</typeparam>
      <typeparam name="TElement">The type of the elements in the <see cref="T:System.Collections.Generic.IEnumerable`1" /> sequences that make up the values in the <see cref="T:System.Linq.ILookup`2" />.</typeparam>
    </member>
    <member name="M:System.Linq.ILookup`2.Contains(`0)">
      <summary>Determines whether a specified key exists in the <see cref="T:System.Linq.ILookup`2" />.</summary>
      <param name="key">The key to search for in the <see cref="T:System.Linq.ILookup`2" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="key" /> is in the <see cref="T:System.Linq.ILookup`2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Linq.ILookup`2.Count">
      <summary>Gets the number of key/value collection pairs in the <see cref="T:System.Linq.ILookup`2" />.</summary>
      <returns>The number of key/value collection pairs in the <see cref="T:System.Linq.ILookup`2" />.</returns>
    </member>
    <member name="P:System.Linq.ILookup`2.Item(`0)">
      <summary>Gets the <see cref="T:System.Collections.Generic.IEnumerable`1" /> sequence of values indexed by a specified key.</summary>
      <param name="key">The key of the desired sequence of values.</param>
      <returns>The <see cref="T:System.Collections.Generic.IEnumerable`1" /> sequence of values indexed by the specified key.</returns>
    </member>
    <member name="T:System.Linq.IOrderedEnumerable`1">
      <summary>Represents a sorted sequence.</summary>
      <typeparam name="TElement">The type of the elements of the sequence.</typeparam>
    </member>
    <member name="M:System.Linq.IOrderedEnumerable`1.CreateOrderedEnumerable``1(System.Func{`0,``0},System.Collections.Generic.IComparer{``0},System.Boolean)">
      <summary>Performs a subsequent ordering on the elements of an <see cref="T:System.Linq.IOrderedEnumerable`1" /> according to a key.</summary>
      <param name="keySelector">The <see cref="T:System.Func`2" /> used to extract the key for each element.</param>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> used to compare keys for placement in the returned sequence.</param>
      <param name="descending">
        <see langword="true" /> to sort the elements in descending order; <see langword="false" /> to sort the elements in ascending order.</param>
      <typeparam name="TKey">The type of the key produced by <paramref name="keySelector" />.</typeparam>
      <returns>An <see cref="T:System.Linq.IOrderedEnumerable`1" /> whose elements are sorted according to a key.</returns>
    </member>
    <member name="T:System.Linq.Lookup`2">
      <summary>Represents a collection of keys each mapped to one or more values.</summary>
      <typeparam name="TKey">The type of the keys in the <see cref="T:System.Linq.Lookup`2" />.</typeparam>
      <typeparam name="TElement">The type of the elements of each <see cref="T:System.Collections.Generic.IEnumerable`1" /> value in the <see cref="T:System.Linq.Lookup`2" />.</typeparam>
    </member>
    <member name="M:System.Linq.Lookup`2.ApplyResultSelector``1(System.Func{`0,System.Collections.Generic.IEnumerable{`1},``0})">
      <summary>Applies a transform function to each key and its associated values and returns the results.</summary>
      <param name="resultSelector">A function to project a result value from each key and its associated values.</param>
      <typeparam name="TResult">The type of the result values produced by <paramref name="resultSelector" />.</typeparam>
      <returns>A collection that contains one value for each key/value collection pair in the <see cref="T:System.Linq.Lookup`2" />.</returns>
    </member>
    <member name="M:System.Linq.Lookup`2.Contains(`0)">
      <summary>Determines whether a specified key is in the <see cref="T:System.Linq.Lookup`2" />.</summary>
      <param name="key">The key to find in the <see cref="T:System.Linq.Lookup`2" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="key" /> is in the <see cref="T:System.Linq.Lookup`2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Linq.Lookup`2.Count">
      <summary>Gets the number of key/value collection pairs in the <see cref="T:System.Linq.Lookup`2" />.</summary>
      <returns>The number of key/value collection pairs in the <see cref="T:System.Linq.Lookup`2" />.</returns>
    </member>
    <member name="M:System.Linq.Lookup`2.GetEnumerator">
      <summary>Returns a generic enumerator that iterates through the <see cref="T:System.Linq.Lookup`2" />.</summary>
      <returns>An enumerator for the <see cref="T:System.Linq.Lookup`2" />.</returns>
    </member>
    <member name="P:System.Linq.Lookup`2.Item(`0)">
      <summary>Gets the collection of values indexed by the specified key.</summary>
      <param name="key">The key of the desired collection of values.</param>
      <returns>The collection of values indexed by the specified key.</returns>
    </member>
    <member name="M:System.Linq.Lookup`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Linq.Lookup`2" />. This class cannot be inherited.</summary>
      <returns>An enumerator for the <see cref="T:System.Linq.Lookup`2" />.</returns>
    </member>
  </members>
</doc>