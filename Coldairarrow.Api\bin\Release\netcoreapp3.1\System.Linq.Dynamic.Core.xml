<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Linq.Dynamic.Core</name>
    </assembly>
    <members>
        <member name="M:System.Linq.Dynamic.Core.AssemblyBuilderFactory.DefineDynamicAssembly(System.Reflection.AssemblyName,System.Reflection.Emit.AssemblyBuilderAccess)">
            <summary>
            Defines a dynamic assembly that has the specified name and access rights.
            </summary>
            <param name="name">The name of the assembly.</param>
            <param name="access">The access rights of the assembly.</param>
            <returns>An object that represents the new assembly.</returns>
        </member>
        <member name="T:System.Linq.Dynamic.Core.CustomTypeProviders.AbstractDynamicLinqCustomTypeProvider">
            <summary>
            The abstract DynamicLinqCustomTypeProvider which is used by the DefaultDynamicLinqCustomTypeProvider and can be used by a custom TypeProvider like in .NET Core.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.CustomTypeProviders.AbstractDynamicLinqCustomTypeProvider.FindTypesMarkedWithDynamicLinqTypeAttribute(System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Finds the unique types marked with DynamicLinqTypeAttribute.
            </summary>
            <param name="assemblies">The assemblies to process.</param>
            <returns><see cref="T:System.Collections.Generic.IEnumerable`1" /></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.CustomTypeProviders.AbstractDynamicLinqCustomTypeProvider.ResolveType(System.Collections.Generic.IEnumerable{System.Reflection.Assembly},System.String)">
            <summary>
            Resolve any type which is registered in the current application domain.
            </summary>
            <param name="assemblies">The assemblies to inspect.</param>
            <param name="typeName">The typename to resolve.</param>
            <returns>A resolved <see cref="T:System.Type"/> or null when not found.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.CustomTypeProviders.AbstractDynamicLinqCustomTypeProvider.ResolveTypeBySimpleName(System.Collections.Generic.IEnumerable{System.Reflection.Assembly},System.String)">
            <summary>
            Resolve a type by the simple name which is registered in the current application domain.
            </summary>
            <param name="assemblies">The assemblies to inspect.</param>
            <param name="simpleTypeName">The simple typename to resolve.</param>
            <returns>A resolved <see cref="T:System.Type"/> or null when not found.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.CustomTypeProviders.AbstractDynamicLinqCustomTypeProvider.GetAssemblyTypesWithDynamicLinqTypeAttribute(System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Gets the assembly types annotated with <see cref="T:System.Linq.Dynamic.Core.CustomTypeProviders.DynamicLinqTypeAttribute"/> in an Exception friendly way.
            </summary>
            <param name="assemblies">The assemblies to process.</param>
            <returns>Array of <see cref="T:System.Type" /></returns>
        </member>
        <member name="T:System.Linq.Dynamic.Core.CustomTypeProviders.DefaultDynamicLinqCustomTypeProvider">
             <summary>
             The default implementation for <see cref="T:System.Linq.Dynamic.Core.CustomTypeProviders.DefaultDynamicLinqCustomTypeProvider"/>.
             
             Scans the current AppDomain for all types marked with <see cref="T:System.Linq.Dynamic.Core.CustomTypeProviders.DynamicLinqTypeAttribute"/>, and adds them as custom Dynamic Link types.
            
             Also provides functionality to resolve a Type in the current Application Domain.
            
             This class is used as default for full .NET Framework, so not for .NET Core
             </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.CustomTypeProviders.DefaultDynamicLinqCustomTypeProvider.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Linq.Dynamic.Core.CustomTypeProviders.DefaultDynamicLinqCustomTypeProvider"/> class.
            </summary>
            <param name="cacheCustomTypes">Defines whether to cache the CustomTypes (including extension methods) which are found in the Application Domain. Default set to 'true'.</param>
        </member>
        <member name="M:System.Linq.Dynamic.Core.CustomTypeProviders.DefaultDynamicLinqCustomTypeProvider.GetCustomTypes">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.CustomTypeProviders.IDynamicLinqCustomTypeProvider.GetCustomTypes"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.CustomTypeProviders.DefaultDynamicLinqCustomTypeProvider.GetExtensionMethods">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.CustomTypeProviders.IDynamicLinqCustomTypeProvider.GetExtensionMethods"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.CustomTypeProviders.DefaultDynamicLinqCustomTypeProvider.ResolveType(System.String)">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.CustomTypeProviders.IDynamicLinqCustomTypeProvider.ResolveType(System.String)"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.CustomTypeProviders.DefaultDynamicLinqCustomTypeProvider.ResolveTypeBySimpleName(System.String)">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.CustomTypeProviders.IDynamicLinqCustomTypeProvider.ResolveTypeBySimpleName(System.String)"/>
        </member>
        <member name="T:System.Linq.Dynamic.Core.CustomTypeProviders.DynamicLinqTypeAttribute">
            <summary>
            Indicates to Dynamic Linq to consider the Type as a valid dynamic linq type.
            </summary>
        </member>
        <member name="T:System.Linq.Dynamic.Core.CustomTypeProviders.IDynamicLinkCustomTypeProvider">
            <summary>
            Interface for providing functionality to find custom types for or resolve any type.
            Note that this interface will be marked obsolete in the next version. Use <see cref="T:System.Linq.Dynamic.Core.CustomTypeProviders.IDynamicLinqCustomTypeProvider"/> instead.
            </summary>
        </member>
        <member name="T:System.Linq.Dynamic.Core.CustomTypeProviders.IDynamicLinqCustomTypeProvider">
            <summary>
            Interface for providing functionality to find custom types for or resolve any type.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.CustomTypeProviders.IDynamicLinqCustomTypeProvider.GetCustomTypes">
            <summary>
            Returns a list of custom types that System.Linq.Dynamic.Core will understand.
            </summary>
            <returns>A <see cref="T:System.Collections.Generic.HashSet`1" /> list of custom types.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.CustomTypeProviders.IDynamicLinqCustomTypeProvider.GetExtensionMethods">
            <summary>
            Returns a list of custom extension methods that System.Linq.Dynamic.Core will understand.
            </summary>
            <returns>A list of custom extension methods that System.Linq.Dynamic.Core will understand.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.CustomTypeProviders.IDynamicLinqCustomTypeProvider.ResolveType(System.String)">
            <summary>
            Resolve any type by fullname which is registered in the current application domain.
            </summary>
            <param name="typeName">The typename to resolve.</param>
            <returns>A resolved <see cref="T:System.Type"/> or null when not found.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.CustomTypeProviders.IDynamicLinqCustomTypeProvider.ResolveTypeBySimpleName(System.String)">
            <summary>
            Resolve any type by the simple name which is registered in the current application domain.
            </summary>
            <param name="simpleTypeName">The typename to resolve.</param>
            <returns>A resolved <see cref="T:System.Type"/> or null when not found.</returns>
        </member>
        <member name="T:System.Linq.Dynamic.Core.DefaultQueryableAnalyzer">
            <summary>
            Default implementation.
            </summary>
            <seealso cref="T:System.Linq.Dynamic.Core.IQueryableAnalyzer" />
        </member>
        <member name="M:System.Linq.Dynamic.Core.DefaultQueryableAnalyzer.SupportsLinqToObjects(System.Linq.IQueryable,System.Linq.IQueryProvider)">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.IQueryableAnalyzer.SupportsLinqToObjects(System.Linq.IQueryable,System.Linq.IQueryProvider)"/>
        </member>
        <member name="T:System.Linq.Dynamic.Core.DynamicClass">
            <summary>
            Provides a base class for dynamic objects.
            
            In addition to the methods defined here, the following items are added using reflection:
            - default constructor
            - constructor with all the properties as parameters (if not linq-to-entities)
            - all properties (also with getter and setters)
            - ToString() method
            - Equals() method
            - GetHashCode() method
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicClass.GetDynamicPropertyValue``1(System.String)">
            <summary>
            Gets the dynamic property by name.
            </summary>
            <typeparam name="T">The type.</typeparam>
            <param name="propertyName">Name of the property.</param>
            <returns>T</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicClass.GetDynamicPropertyValue(System.String)">
            <summary>
            Gets the dynamic property value by name.
            </summary>
            <param name="propertyName">Name of the property.</param>
            <returns>value</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicClass.SetDynamicPropertyValue``1(System.String,``0)">
            <summary>
            Sets the dynamic property value by name.
            </summary>
            <typeparam name="T">The type.</typeparam>
            <param name="propertyName">Name of the property.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicClass.SetDynamicPropertyValue(System.String,System.Object)">
            <summary>
            Sets the dynamic property value by name.
            </summary>
            <param name="propertyName">Name of the property.</param>
            <param name="value">The value.</param>
        </member>
        <member name="P:System.Linq.Dynamic.Core.DynamicClass.Item(System.String)">
            <summary>
            Gets or sets the <see cref="T:System.Object"/> with the specified name.
            </summary>
            <value>The <see cref="T:System.Object"/>.</value>
            <param name="name">The name.</param>
            <returns>Value from the property.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicClass.GetDynamicMemberNames">
            <summary>
            Returns the enumeration of all dynamic member names.
            </summary>
            <returns>
            A sequence that contains dynamic member names.
            </returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicClass.TryGetMember(System.Dynamic.GetMemberBinder,System.Object@)">
            <summary>
            Provides the implementation for operations that get member values. Classes derived from the <see cref="T:System.Dynamic.DynamicObject" /> class can override this method to specify dynamic behavior for operations such as getting a value for a property.
            </summary>
            <param name="binder">Provides information about the object that called the dynamic operation. The binder.Name property provides the name of the member on which the dynamic operation is performed. For example, for the Console.WriteLine(sampleObject.SampleProperty) statement, where sampleObject is an instance of the class derived from the <see cref="T:System.Dynamic.DynamicObject" /> class, binder.Name returns "SampleProperty". The binder.IgnoreCase property specifies whether the member name is case-sensitive.</param>
            <param name="result">The result of the get operation. For example, if the method is called for a property, you can assign the property value to <paramref name="result" />.</param>
            <returns>
            true if the operation is successful; otherwise, false. If this method returns false, the run-time binder of the language determines the behavior. (In most cases, a run-time exception is thrown.)
            </returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicClass.TrySetMember(System.Dynamic.SetMemberBinder,System.Object)">
            <summary>
            Provides the implementation for operations that set member values. Classes derived from the <see cref="T:System.Dynamic.DynamicObject" /> class can override this method to specify dynamic behavior for operations such as setting a value for a property.
            </summary>
            <param name="binder">Provides information about the object that called the dynamic operation. The binder.Name property provides the name of the member to which the value is being assigned. For example, for the statement sampleObject.SampleProperty = "Test", where sampleObject is an instance of the class derived from the <see cref="T:System.Dynamic.DynamicObject" /> class, binder.Name returns "SampleProperty". The binder.IgnoreCase property specifies whether the member name is case-sensitive.</param>
            <param name="value">The value to set to the member. For example, for sampleObject.SampleProperty = "Test", where sampleObject is an instance of the class derived from the <see cref="T:System.Dynamic.DynamicObject" /> class, the <paramref name="value" /> is "Test".</param>
            <returns>
            true if the operation is successful; otherwise, false. If this method returns false, the run-time binder of the language determines the behavior. (In most cases, a language-specific run-time exception is thrown.)
            </returns>
        </member>
        <member name="T:System.Linq.Dynamic.Core.DynamicClassFactory">
            <summary>
            A factory to create dynamic classes, based on <see href="http://stackoverflow.com/questions/29413942/c-sharp-anonymous-object-with-properties-from-dictionary" />.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicClassFactory.#cctor">
            <summary>
            Initializes the <see cref="T:System.Linq.Dynamic.Core.DynamicClassFactory"/> class.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicClassFactory.CreateGenericComparerType(System.Type,System.Type)">
            <summary>
            Create a GenericComparerType based on the GenericType and an instance of a <see cref="T:System.Collections.IComparer"/>.
            </summary>
            <param name="comparerGenericType">The GenericType</param>
            <param name="comparerType">The <see cref="T:System.Collections.IComparer"/> instance</param>
            <returns>Type</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicClassFactory.CreateType(System.Collections.Generic.IList{System.Linq.Dynamic.Core.DynamicProperty},System.Boolean)">
            <summary>
            The CreateType method creates a new data class with a given set of public properties and returns the System.Type object for the newly created class. If a data class with an identical sequence of properties has already been created, the System.Type object for this class is returned.        
            Data classes implement private instance variables and read/write property accessors for the specified properties.Data classes also override the Equals and GetHashCode members to implement by-value equality.
            Data classes are created in an in-memory assembly in the current application domain. All data classes inherit from <see cref="T:System.Linq.Dynamic.Core.DynamicClass"/> and are given automatically generated names that should be considered private (the names will be unique within the application domain but not across multiple invocations of the application). Note that once created, a data class stays in memory for the lifetime of the current application domain. There is currently no way to unload a dynamically created data class.
            The dynamic expression parser uses the CreateClass methods to generate classes from data object initializers. This feature in turn is often used with the dynamic Select method to create projections.
            </summary>
            <param name="properties">The DynamicProperties</param>
            <param name="createParameterCtor">Create a constructor with parameters. Default set to true. Note that for Linq-to-Database objects, this needs to be set to false.</param>
            <returns>Type</returns>
            <example>
            <code>
            <![CDATA[
            DynamicProperty[] props = new DynamicProperty[] { new DynamicProperty("Name", typeof(string)), new DynamicProperty("Birthday", typeof(DateTime)) };
            Type type = DynamicClassFactory.CreateType(props);
            DynamicClass dynamicClass = Activator.CreateInstance(type) as DynamicClass;
            dynamicClass.SetDynamicProperty("Name", "Albert");
            dynamicClass.SetDynamicProperty("Birthday", new DateTime(1879, 3, 14));
            Console.WriteLine(dynamicClass);
            ]]>
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicClassFactory.ClearGeneratedTypes">
            <summary>
            Used for unit-testing
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicClassFactory.GenerateKey(System.Collections.Generic.IEnumerable{System.Linq.Dynamic.Core.DynamicProperty},System.Boolean)">
            <summary>
            Generates the key.
            Anonymous classes are generics based. The generic classes are distinguished by number of parameters and name of parameters.
            The specific types of the parameters are the generic arguments.
            </summary>
            <param name="dynamicProperties">The dynamic properties.</param>
            <param name="createParameterCtor">if set to <c>true</c> [create parameter ctor].</param>
            <returns></returns>
        </member>
        <member name="T:System.Linq.Dynamic.Core.DynamicEnumerableAsyncExtensions">
            <summary>
            Define async extensions on <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicEnumerableAsyncExtensions.ToDynamicArrayAsync(System.Collections.IEnumerable,System.Type,System.Threading.CancellationToken)">
            <summary>
            Async creates an array of dynamic objects from a <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
            <param name="source">A <see cref="T:System.Collections.IEnumerable"/> to create an array from.</param>
            <param name="type">A <see cref="T:System.Type"/> cast to.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> (optional).</param>
            <returns>An Array that contains the elements from the input sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicEnumerableAsyncExtensions.ToDynamicArrayAsync(System.Collections.IEnumerable,System.Threading.CancellationToken)">
            <summary>
            Async creates an array of dynamic objects from a <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
            <param name="source">A <see cref="T:System.Collections.IEnumerable"/> to create an array from.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> (optional).</param>
            <returns>An array that contains the elements from the input sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicEnumerableAsyncExtensions.ToDynamicArrayAsync``1(System.Collections.IEnumerable,System.Threading.CancellationToken)">
            <summary>
            Async creates an array of dynamic objects from a <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
            <typeparam name="T">The generic type.</typeparam>
            <param name="source">A <see cref="T:System.Collections.IEnumerable"/> to create an array from.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> (optional).</param>
            <returns>An Array{T} that contains the elements from the input sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicEnumerableAsyncExtensions.ToDynamicListAsync(System.Collections.IEnumerable,System.Type,System.Threading.CancellationToken)">
            <summary>
            Async creates a list of dynamic objects from a <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
            <param name="source">A <see cref="T:System.Collections.IEnumerable"/> to create an array from.</param>
            <param name="type">A <see cref="T:System.Type"/> cast to.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> (optional).</param>
            <returns>An List that contains the elements from the input sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicEnumerableAsyncExtensions.ToDynamicListAsync(System.Collections.IEnumerable,System.Threading.CancellationToken)">
            <summary>
            Async creates a list of dynamic objects from a <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
            <param name="source">A <see cref="T:System.Collections.IEnumerable"/> to create a list from.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> (optional).</param>
            <returns>A List that contains the elements from the input sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicEnumerableAsyncExtensions.ToDynamicListAsync``1(System.Collections.IEnumerable,System.Threading.CancellationToken)">
            <summary>
            Async creates a list of dynamic objects from an <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
            <typeparam name="T">Generic Type</typeparam>
            <param name="source">A <see cref="T:System.Collections.IEnumerable"/> to create a list from.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> (optional).</param>
            <returns>A List{T} that contains the elements from the input sequence.</returns>
        </member>
        <member name="T:System.Linq.Dynamic.Core.DynamicEnumerableExtensions">
            <summary>
            Define extensions on <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicEnumerableExtensions.ToDynamicArray(System.Collections.IEnumerable)">
            <summary>
            Creates an array of dynamic objects from a <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
            <param name="source">A <see cref="T:System.Collections.IEnumerable"/> to create an array from.</param>
            <returns>An array that contains the elements from the input sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicEnumerableExtensions.ToDynamicArray``1(System.Collections.IEnumerable)">
            <summary>
            Creates an array of dynamic objects from a <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
            <typeparam name="T">The generic type.</typeparam>
            <param name="source">A <see cref="T:System.Collections.IEnumerable"/> to create an array from.</param>
            <returns>An Array{T} that contains the elements from the input sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicEnumerableExtensions.ToDynamicArray(System.Collections.IEnumerable,System.Type)">
            <summary>
            Creates an array of dynamic objects from a <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
            <param name="source">A <see cref="T:System.Collections.IEnumerable"/> to create an array from.</param>
            <param name="type">A <see cref="T:System.Type"/> cast to.</param>
            <returns>An Array that contains the elements from the input sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicEnumerableExtensions.ToDynamicList(System.Collections.IEnumerable)">
            <summary>
            Creates a list of dynamic objects from a <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
            <param name="source">A <see cref="T:System.Collections.IEnumerable"/> to create an array from.</param>
            <returns>A List that contains the elements from the input sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicEnumerableExtensions.ToDynamicList(System.Collections.IEnumerable,System.Type)">
            <summary>
            Creates a list of dynamic objects from a <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
            <param name="source">A <see cref="T:System.Collections.IEnumerable"/> to create an array from.</param>
            <param name="type">A <see cref="T:System.Type"/> cast to.</param>
            <returns>A List that contains the elements from the input sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicEnumerableExtensions.ToDynamicList``1(System.Collections.IEnumerable)">
            <summary>
            Creates a list of dynamic objects from a <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
            <typeparam name="T">Generic Type</typeparam>
            <param name="source">A <see cref="T:System.Collections.IEnumerable"/> to create an array from.</param>
            <returns>A List{T} that contains the elements from the input sequence.</returns>
        </member>
        <member name="T:System.Linq.Dynamic.Core.DynamicExpressionParser">
            <summary>
            Helper class to convert an expression into an LambdaExpression
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Linq.Dynamic.Core.ParsingConfig,System.Boolean,System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression.
            </summary>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="createParameterCtor">if set to <c>true</c> then also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Type,System.Linq.Dynamic.Core.ParsingConfig,System.Boolean,System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression.
            </summary>
            <param name="delegateType">The delegate type.</param>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="createParameterCtor">if set to <c>true</c> then also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda``1(System.Linq.Dynamic.Core.ParsingConfig,System.Boolean,System.String,System.Object[])">
            <summary>
            Parses an expression into a Typed Expression.
            </summary>
            <typeparam name="TResult">The type of the result.</typeparam>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="createParameterCtor">if set to <c>true</c> then also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.Expression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda``1(System.Type,System.Linq.Dynamic.Core.ParsingConfig,System.Boolean,System.String,System.Object[])">
            <summary>
            Parses an expression into a Typed Expression.
            </summary>
            <typeparam name="TResult">The type of the result.</typeparam>
            <param name="delegateType">The delegate type.</param>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="createParameterCtor">if set to <c>true</c> then also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.Expression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Linq.Dynamic.Core.ParsingConfig,System.Boolean,System.Linq.Expressions.ParameterExpression[],System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression.
            </summary>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="createParameterCtor">if set to <c>true</c> then also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.</param>
            <param name="parameters">A array from ParameterExpressions.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Type,System.Linq.Dynamic.Core.ParsingConfig,System.Boolean,System.Linq.Expressions.ParameterExpression[],System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression.
            </summary>
            <param name="delegateType">The delegate type.</param>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="createParameterCtor">if set to <c>true</c> then also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.</param>
            <param name="parameters">A array from ParameterExpressions.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda``1(System.Linq.Dynamic.Core.ParsingConfig,System.Boolean,System.Linq.Expressions.ParameterExpression[],System.String,System.Object[])">
            <summary>
            Parses an expression into a Typed Expression.
            </summary>
            <typeparam name="TResult">The type of the result.</typeparam>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="createParameterCtor">if set to <c>true</c> then also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.</param>
            <param name="parameters">A array from ParameterExpressions.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.Expression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda``1(System.Type,System.Linq.Dynamic.Core.ParsingConfig,System.Boolean,System.Linq.Expressions.ParameterExpression[],System.String,System.Object[])">
            <summary>
            Parses an expression into a Typed Expression.
            </summary>
            <typeparam name="TResult">The type of the result.</typeparam>
            <param name="delegateType">The delegate type.</param>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="createParameterCtor">if set to <c>true</c> then also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.</param>
            <param name="parameters">A array from ParameterExpressions.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.Expression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Boolean,System.Type,System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression.
            </summary>
            <param name="createParameterCtor">if set to <c>true</c> then also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.</param>
            <param name="itType">The main type from the dynamic class expression.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda``2(System.Linq.Dynamic.Core.ParsingConfig,System.Boolean,System.String,System.Object[])">
            <summary>
            Parses an expression into a Typed Expression.
            </summary>
            <typeparam name="T">The `it`-Type.</typeparam>
            <typeparam name="TResult">The type of the result.</typeparam>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="createParameterCtor">if set to <c>true</c> then also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.Expression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda``2(System.Type,System.Linq.Dynamic.Core.ParsingConfig,System.Boolean,System.String,System.Object[])">
            <summary>
            Parses an expression into a Typed Expression.
            </summary>
            <typeparam name="T">The `it`-Type.</typeparam>
            <typeparam name="TResult">The type of the result.</typeparam>
            <param name="delegateType">The delegate type.</param>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="createParameterCtor">if set to <c>true</c> then also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.Expression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Linq.Dynamic.Core.ParsingConfig,System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression. (Also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.)
            </summary>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Type,System.Linq.Dynamic.Core.ParsingConfig,System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression. (Also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.)
            </summary>
            <param name="delegateType">The delegate type.</param>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression. (Also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.)
            </summary>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Type,System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression. (Also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.)
            </summary>
            <param name="itType">The main type from the dynamic class expression.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Linq.Dynamic.Core.ParsingConfig,System.Type,System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression. (Also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.)
            </summary>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="itType">The main type from the dynamic class expression.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Type,System.Linq.Dynamic.Core.ParsingConfig,System.Type,System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression. (Also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.)
            </summary>
            <param name="delegateType">The delegate type.</param>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="itType">The main type from the dynamic class expression.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Linq.Dynamic.Core.ParsingConfig,System.Boolean,System.Type,System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression.
            </summary>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="createParameterCtor">if set to <c>true</c> then also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.</param>
            <param name="itType">The main type from the dynamic class expression.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Type,System.Linq.Dynamic.Core.ParsingConfig,System.Boolean,System.Type,System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression.
            </summary>
            <param name="delegateType">The delegate type.</param>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="createParameterCtor">if set to <c>true</c> then also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.</param>
            <param name="itType">The main type from the dynamic class expression.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Linq.Expressions.ParameterExpression[],System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression. (Also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.)
            </summary>
            <param name="parameters">A array from ParameterExpressions.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Type,System.Linq.Expressions.ParameterExpression[],System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression. (Also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.)
            </summary>
            <param name="delegateType">The delegate type.</param>
            <param name="parameters">A array from ParameterExpressions.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Linq.Dynamic.Core.ParsingConfig,System.Linq.Expressions.ParameterExpression[],System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression. (Also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.)
            </summary>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="parameters">A array from ParameterExpressions.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Type,System.Linq.Dynamic.Core.ParsingConfig,System.Linq.Expressions.ParameterExpression[],System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression. (Also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.)
            </summary>
            <param name="delegateType">The delegate type.</param>
            <param name="parsingConfig">The Configuration for the parsing.</param>
            <param name="parameters">A array from ParameterExpressions.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicExpressionParser.ParseLambda(System.Boolean,System.Linq.Expressions.ParameterExpression[],System.Type,System.String,System.Object[])">
            <summary>
            Parses an expression into a LambdaExpression.
            </summary>
            <param name="createParameterCtor">if set to <c>true</c> then also create a constructor for all the parameters. Note that this doesn't work for Linq-to-Database entities.</param>
            <param name="parameters">A array from ParameterExpressions.</param>
            <param name="resultType">Type of the result. If not specified, it will be generated dynamically.</param>
            <param name="expression">The expression.</param>
            <param name="values">An object array that contains zero or more objects which are used as replacement values.</param>
            <returns>The generated <see cref="T:System.Linq.Expressions.LambdaExpression"/></returns>
        </member>
        <member name="T:System.Linq.Dynamic.Core.DynamicGetMemberBinder">
            <summary>
            Code is based on SqlLinq by dkackman (https://github.com/dkackman/SqlLinq/blob/210b594e37f14061424397368ed750ce547c21e7/License.md) however it's modified to solve several issues.
            </summary>
            <seealso cref="T:System.Dynamic.GetMemberBinder" />
        </member>
        <member name="T:System.Linq.Dynamic.Core.DynamicProperty">
            <summary>
            DynamicProperty
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicProperty.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Linq.Dynamic.Core.DynamicProperty"/> class.
            </summary>
            <param name="name">The name from the property.</param>
            <param name="type">The type from the property.</param>
        </member>
        <member name="P:System.Linq.Dynamic.Core.DynamicProperty.Name">
            <summary>
            Gets the name from the property.
            </summary>
            <value>
            The name from the property.
            </value>
        </member>
        <member name="P:System.Linq.Dynamic.Core.DynamicProperty.Type">
            <summary>
            Gets the type from the property.
            </summary>
            <value>
            The type from the property.
            </value>
        </member>
        <member name="T:System.Linq.Dynamic.Core.DynamicQueryableExtensions">
            <summary>
            Provides a set of static (Shared in Visual Basic) methods for querying data structures that implement <see cref="T:System.Linq.IQueryable"/>.
            It allows dynamic string based querying. Very handy when, at compile time, you don't know the type of queries that will be generated,
            or when downstream components only return column names to sort and filter by.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Aggregate(System.Linq.IQueryable,System.String,System.String)">
            <summary>
            Dynamically runs an aggregate function on the IQueryable.
            </summary>
            <param name="source">The IQueryable data source.</param>
            <param name="function">The name of the function to run. Can be Sum, Average, Min or Max.</param>
            <param name="member">The name of the property to aggregate over.</param>
            <returns>The value of the aggregate function run over the specified property.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.All(System.Linq.IQueryable,System.String,System.Object[])">
            <summary>Determines whether all the elements of a sequence satisfy a condition.</summary>
            <param name="source">A sequence whose elements to test for a condition.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>true if every element of the source sequence passes the test in the specified predicate, or if the sequence is empty; otherwise, false.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.All(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>Determines whether all the elements of a sequence satisfy a condition.</summary>
            <param name="source">A sequence whose elements to test for a condition.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>true if every element of the source sequence passes the test in the specified predicate, or if the sequence is empty; otherwise, false.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Any(System.Linq.IQueryable)">
            <summary>
            Determines whether a sequence contains any elements.
            </summary>
            <param name="source">A sequence to check for being empty.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result = queryable.Any();
            </code>
            </example>
            <returns>true if the source sequence contains any elements; otherwise, false.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Any(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Determines whether a sequence contains any elements.
            </summary>
            <param name="source">A sequence to check for being empty.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result1 = queryable.Any("Income > 50");
            var result2 = queryable.Any("Income > @0", 50);
            var result3 = queryable.Select("Roles.Any()");
            </code>
            </example>
            <returns>true if the source sequence contains any elements; otherwise, false.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Any(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Any(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Any(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Determines whether a sequence contains any elements.
            </summary>
            <param name="source">A sequence to check for being empty.</param>
            <param name="lambda">A cached Lambda Expression.</param>
            <returns>true if the source sequence contains any elements; otherwise, false.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Average(System.Linq.IQueryable)">
            <summary>
            Computes the average of a sequence of numeric values.
            </summary>
            <param name="source">A sequence of numeric values to calculate the average of.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result1 = queryable.Average();
            var result2 = queryable.Select("Roles.Average()");
            </code>
            </example>
            <returns>The average of the values in the sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Average(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Computes the average of a sequence of numeric values.
            </summary>
            <param name="source">A sequence of numeric values to calculate the average of.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result = queryable.Average("Income");
            </code>
            </example>
            <returns>The average of the values in the sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Average(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Average(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Average(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Computes the average of a sequence of numeric values.
            </summary>
            <param name="source">A sequence of numeric values to calculate the average of.</param>
            <param name="lambda">A Lambda Expression.</param>
            <returns>The average of the values in the sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.AsDynamicEnumerable(System.Linq.IQueryable)">
            <summary>
            Returns the input typed as <see cref="T:System.Collections.Generic.IEnumerable`1"/> of dynamic.
            </summary>
            <param name="source">The sequence to type as <see cref="T:System.Collections.Generic.IEnumerable`1"/> of dynamic.</param>
            <returns>The input typed as <see cref="T:System.Collections.Generic.IEnumerable`1"/> of dynamic.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Cast(System.Linq.IQueryable,System.Type)">
            <summary>
            Converts the elements of an <see cref="T:System.Linq.IQueryable"/> to the specified type.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> that contains the elements to be converted.</param>
            <param name="type">The type to convert the elements of source to.</param>
            <returns>An <see cref="T:System.Linq.IQueryable"/> that contains each element of the source sequence converted to the specified type.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Cast(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String)">
            <summary>
            Converts the elements of an <see cref="T:System.Linq.IQueryable"/> to the specified type.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> that contains the elements to be converted.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="typeName">The type to convert the elements of source to.</param>
            <returns>An <see cref="T:System.Linq.IQueryable"/> that contains each element of the source sequence converted to the specified type.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Cast(System.Linq.IQueryable,System.String)">
            <summary>
            Converts the elements of an <see cref="T:System.Linq.IQueryable"/> to the specified type.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> that contains the elements to be converted.</param>
            <param name="typeName">The type to convert the elements of source to.</param>
            <returns>An <see cref="T:System.Linq.IQueryable"/> that contains each element of the source sequence converted to the specified type.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Count(System.Linq.IQueryable)">
            <summary>
            Returns the number of elements in a sequence.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> that contains the elements to be counted.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result = queryable.Count();
            </code>
            </example>
            <returns>The number of elements in the input sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Count(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Returns the number of elements in a sequence.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> that contains the elements to be counted.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result1 = queryable.Count("Income > 50");
            var result2 = queryable.Count("Income > @0", 50);
            var result3 = queryable.Select("Roles.Count()");
            </code>
            </example>
            <returns>The number of elements in the specified sequence that satisfies a condition.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Count(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Count(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Count(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Returns the number of elements in a sequence.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> that contains the elements to be counted.</param>
            <param name="lambda">A cached Lambda Expression.</param>
            <returns>The number of elements in the specified sequence that satisfies a condition.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.DefaultIfEmpty(System.Linq.IQueryable)">
            <summary>
            Returns the elements of the specified sequence or the type parameter's default value in a singleton collection if the sequence is empty.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return a default value for if empty.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.DefaultIfEmpty();
            </code>
            </example>
            <returns>An <see cref="T:System.Linq.IQueryable"/> that contains default if source is empty; otherwise, source.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.DefaultIfEmpty(System.Linq.IQueryable,System.Object)">
            <summary>
            Returns the elements of the specified sequence or the type parameter's default value in a singleton collection if the sequence is empty.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return a default value for if empty.</param>
            <param name="defaultValue">The value to return if the sequence is empty.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.DefaultIfEmpty(new Employee());
            </code>
            </example>
            <returns>An <see cref="T:System.Linq.IQueryable"/> that contains defaultValue if source is empty; otherwise, source.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Distinct(System.Linq.IQueryable)">
            <summary>
            Returns distinct elements from a sequence by using the default equality comparer to compare values.
            </summary>
            <param name="source">The sequence to remove duplicate elements from.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result1 = queryable.Distinct();
            var result2 = queryable.Select("Roles.Distinct()");
            </code>
            </example>
            <returns>An <see cref="T:System.Linq.IQueryable"/> that contains distinct elements from the source sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.First(System.Linq.IQueryable)">
            <summary>
            Returns the first element of a sequence.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the first element of.</param>
            <returns>The first element in source.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.First(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Returns the first element of a sequence that satisfies a specified condition.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the first element of.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>The first element in source that passes the test in predicate.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.First(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.First(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.First(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Returns the first element of a sequence that satisfies a specified condition.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the first element of.</param>
            <param name="lambda">A cached Lambda Expression.</param>
            <returns>The first element in source that passes the test in predicate.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.FirstOrDefault(System.Linq.IQueryable)">
            <summary>
            Returns the first element of a sequence, or a default value if the sequence contains no elements.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the first element of.</param>
            <returns>default if source is empty; otherwise, the first element in source.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.FirstOrDefault(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Returns the first element of a sequence that satisfies a specified condition or a default value if no such element is found.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the first element of.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>default if source is empty or if no element passes the test specified by predicate; otherwise, the first element in source that passes the test specified by predicate.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.FirstOrDefault(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.FirstOrDefault(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.FirstOrDefault(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Returns the first element of a sequence that satisfies a specified condition or a default value if no such element is found.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the first element of.</param>
            <param name="lambda">A cached Lambda Expression.</param>
            <returns>default if source is empty or if no element passes the test specified by predicate; otherwise, the first element in source that passes the test specified by predicate.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.String,System.Object[])">
            <summary>
            Groups the elements of a sequence according to a specified key string function 
            and creates a result value from each group and its key.
            </summary>
            <param name="source">A <see cref="T:System.Linq.IQueryable"/> whose elements to group.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="keySelector">A string expression to specify the key for each element.</param>
            <param name="resultSelector">A string expression to specify a result value from each group.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters.  Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> where each element represents a projection over a group and its key.</returns>
            <example>
            <code>
            var groupResult1 = queryable.GroupBy("NumberPropertyAsKey", "StringProperty");
            var groupResult2 = queryable.GroupBy("new (NumberPropertyAsKey, StringPropertyAsKey)", "new (StringProperty1, StringProperty2)");
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.String,System.Collections.IEqualityComparer,System.Object[])">
            <summary>
            Groups the elements of a sequence according to a specified key string function 
            and creates a result value from each group and its key.
            </summary>
            <param name="source">A <see cref="T:System.Linq.IQueryable"/> whose elements to group.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="keySelector">A string expression to specify the key for each element.</param>
            <param name="resultSelector">A string expression to specify a result value from each group.</param>
            <param name="equalityComparer">The comparer to use.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters.  Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> where each element represents a projection over a group and its key.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.String,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.String,System.String,System.Collections.IEqualityComparer,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.String,System.Collections.IEqualityComparer,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.String)">
            <summary>
            Groups the elements of a sequence according to a specified key string function 
            and creates a result value from each group and its key.
            </summary>
            <param name="source">A <see cref="T:System.Linq.IQueryable"/> whose elements to group.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="keySelector">A string expression to specify the key for each element.</param>
            <param name="resultSelector">A string expression to specify a result value from each group.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> where each element represents a projection over a group and its key.</returns>
            <example>
            <code>
            var groupResult1 = queryable.GroupBy("NumberPropertyAsKey", "StringProperty");
            var groupResult2 = queryable.GroupBy("new (NumberPropertyAsKey, StringPropertyAsKey)", "new (StringProperty1, StringProperty2)");
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.String,System.String)">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.String)"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.String,System.Collections.IEqualityComparer)">
            <summary>
            Groups the elements of a sequence according to a specified key string function 
            and creates a result value from each group and its key.
            </summary>
            <param name="source">A <see cref="T:System.Linq.IQueryable"/> whose elements to group.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="keySelector">A string expression to specify the key for each element.</param>
            <param name="resultSelector">A string expression to specify a result value from each group.</param>
            <param name="equalityComparer">The comparer to use.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> where each element represents a projection over a group and its key.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.String,System.String,System.Collections.IEqualityComparer)">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.String,System.Collections.IEqualityComparer)"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Groups the elements of a sequence according to a specified key string function 
            and creates a result value from each group and its key.
            </summary>
            <param name="source">A <see cref="T:System.Linq.IQueryable"/> whose elements to group.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="keySelector">A string expression to specify the key for each element.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> where each element represents a projection over a group and its key.</returns>
            <example>
            <code>
            var groupResult1 = queryable.GroupBy("NumberPropertyAsKey");
            var groupResult2 = queryable.GroupBy("new (NumberPropertyAsKey, StringPropertyAsKey)");
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Collections.IEqualityComparer,System.Object[])">
            <summary>
            Groups the elements of a sequence according to a specified key string function 
            and creates a result value from each group and its key.
            </summary>
            <param name="source">A <see cref="T:System.Linq.IQueryable"/> whose elements to group.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="keySelector">A string expression to specify the key for each element.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <param name="equalityComparer">The comparer to use.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> where each element represents a projection over a group and its key.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.String,System.Collections.IEqualityComparer,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Collections.IEqualityComparer,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupByMany``1(System.Collections.Generic.IEnumerable{``0},System.Linq.Dynamic.Core.ParsingConfig,System.String[])">
            <summary>
            Groups the elements of a sequence according to multiple specified key string functions 
            and creates a result value from each group (and subgroups) and its key.
            </summary>
            <typeparam name="TElement"></typeparam>
            <param name="source">A <see cref="T:System.Collections.Generic.IEnumerable`1"/> whose elements to group.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="keySelectors"><see cref="T:System.String"/> expressions to specify the keys for each element.</param>
            <returns>A <see cref="T:System.Collections.Generic.IEnumerable`1"/> of type <see cref="T:System.Linq.Dynamic.Core.GroupResult"/> where each element represents a projection over a group, its key, and its subgroups.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupByMany``1(System.Collections.Generic.IEnumerable{``0},System.String[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupByMany``1(System.Collections.Generic.IEnumerable{``0},System.Linq.Dynamic.Core.ParsingConfig,System.String[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupByMany``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Object}[])">
            <summary>
            Groups the elements of a sequence according to multiple specified key functions 
            and creates a result value from each group (and subgroups) and its key.
            </summary>
            <typeparam name="TElement"></typeparam>
            <param name="source">A <see cref="T:System.Collections.Generic.IEnumerable`1"/> whose elements to group.</param>
            <param name="keySelectors">Lambda expressions to specify the keys for each element.</param>
            <returns>A <see cref="T:System.Collections.Generic.IEnumerable`1"/> of type <see cref="T:System.Linq.Dynamic.Core.GroupResult"/> where each element represents a projection over a group, its key, and its subgroups.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupJoin(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.Collections.IEnumerable,System.String,System.String,System.String,System.Object[])">
            <summary>
            Correlates the elements of two sequences based on equality of keys and groups the results. The default equality comparer is used to compare keys.
            </summary>
            <param name="outer">The first sequence to join.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="inner">The sequence to join to the first sequence.</param>
            <param name="outerKeySelector">A dynamic function to extract the join key from each element of the first sequence.</param>
            <param name="innerKeySelector">A dynamic function to extract the join key from each element of the second sequence.</param>
            <param name="resultSelector">A dynamic function to create a result element from an element from the first sequence and a collection of matching elements from the second sequence.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicates as parameters. Similar to the way String.Format formats strings.</param>
            <returns>An <see cref="T:System.Linq.IQueryable"/> obtained by performing a grouped join on two sequences.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupJoin(System.Linq.IQueryable,System.Collections.IEnumerable,System.String,System.String,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.GroupJoin(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.Collections.IEnumerable,System.String,System.String,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Join(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.Collections.IEnumerable,System.String,System.String,System.String,System.Object[])">
            <summary>
            Correlates the elements of two sequences based on matching keys. The default equality comparer is used to compare keys.
            </summary>
            <param name="outer">The first sequence to join.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="inner">The sequence to join to the first sequence.</param>
            <param name="outerKeySelector">A dynamic function to extract the join key from each element of the first sequence.</param>
            <param name="innerKeySelector">A dynamic function to extract the join key from each element of the second sequence.</param>
            <param name="resultSelector">A dynamic function to create a result element from two matching elements.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicates as parameters.  Similar to the way String.Format formats strings.</param>
            <returns>An <see cref="T:System.Linq.IQueryable"/> obtained by performing an inner join on two sequences.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Join(System.Linq.IQueryable,System.Collections.IEnumerable,System.String,System.String,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Join(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.Collections.IEnumerable,System.String,System.String,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Join``1(System.Linq.IQueryable{``0},System.Linq.Dynamic.Core.ParsingConfig,System.Collections.Generic.IEnumerable{``0},System.String,System.String,System.String,System.Object[])">
            <summary>
            Correlates the elements of two sequences based on matching keys. The default equality comparer is used to compare keys.
            </summary>
            <typeparam name="TElement">The type of the elements of both sequences, and the result.</typeparam>
            <param name="outer">The first sequence to join.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="inner">The sequence to join to the first sequence.</param>
            <param name="outerKeySelector">A dynamic function to extract the join key from each element of the first sequence.</param>
            <param name="innerKeySelector">A dynamic function to extract the join key from each element of the second sequence.</param>
            <param name="resultSelector">A dynamic function to create a result element from two matching elements.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicates as parameters.  Similar to the way String.Format formats strings.</param>
            <remarks>This overload only works on elements where both sequences and the resulting element match.</remarks>
            <returns>An <see cref="T:System.Linq.IQueryable`1"/> that has elements of type TResult obtained by performing an inner join on two sequences.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Join``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.String,System.String,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Join``1(System.Linq.IQueryable{``0},System.Linq.Dynamic.Core.ParsingConfig,System.Collections.Generic.IEnumerable{``0},System.String,System.String,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Last(System.Linq.IQueryable)">
            <summary>
            Returns the last element of a sequence.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the last element of.</param>
            <returns>The last element in source.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Last(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Returns the last element of a sequence that satisfies a specified condition.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the last element of.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>The first element in source that passes the test in predicate.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Last(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Last(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Last(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Returns the last element of a sequence that satisfies a specified condition.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the last element of.</param>
            <param name="lambda">A cached Lambda Expression.</param>
            <returns>The first element in source that passes the test in predicate.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.LastOrDefault(System.Linq.IQueryable)">
            <summary>
            Returns the last element of a sequence, or a default value if the sequence contains no elements.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the last element of.</param>
            <returns>default if source is empty; otherwise, the last element in source.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.LastOrDefault(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Returns the last element of a sequence that satisfies a specified condition, or a default value if the sequence contains no elements.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the last element of.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>The first element in source that passes the test in predicate.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.LastOrDefault(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.LastOrDefault(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.LastOrDefault(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Returns the last element of a sequence that satisfies a specified condition, or a default value if the sequence contains no elements.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the last element of.</param>
            <param name="lambda">A cached Lambda Expression.</param>
            <returns>The first element in source that passes the test in predicate.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.LongCount(System.Linq.IQueryable)">
            <summary>
            Returns the number of elements in a sequence.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> that contains the elements to be counted.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result = queryable.LongCount();
            </code>
            </example>
            <returns>The number of elements in the input sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.LongCount(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Returns the number of elements in a sequence.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> that contains the elements to be counted.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result1 = queryable.LongCount("Income > 50");
            var result2 = queryable.LongCount("Income > @0", 50);
            var result3 = queryable.Select("Roles.LongCount()");
            </code>
            </example>
            <returns>The number of elements in the specified sequence that satisfies a condition.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.LongCount(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.LongCount(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.LongCount(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Returns the number of elements in a sequence.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> that contains the elements to be counted.</param>
            <param name="lambda">A cached Lambda Expression.</param>
            <returns>The number of elements in the specified sequence that satisfies a condition.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Max(System.Linq.IQueryable)">
            <summary>
            Computes the max element of a sequence.
            </summary>
            <param name="source">A sequence of values to calculate find the max for.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result1 = queryable.Max();
            var result2 = queryable.Select("Roles.Max()");
            </code>
            </example>
            <returns>The max element in the sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Max(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Computes the max element of a sequence.
            </summary>
            <param name="source">A sequence of values to calculate find the max for.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result = queryable.Max("Income");
            </code>
            </example>
            <returns>The max element in the sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Max(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Max(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Max(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Computes the max element of a sequence.
            </summary>
            <param name="source">A sequence of values to calculate find the max for.</param>
            <param name="lambda">A Lambda Expression.</param>
            <returns>The max element in the sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Min(System.Linq.IQueryable)">
            <summary>
            Computes the min element of a sequence.
            </summary>
            <param name="source">A sequence of values to calculate find the min for.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result1 = queryable.Min();
            var result2 = queryable.Select("Roles.Min()");
            </code>
            </example>
            <returns>The min element in the sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Min(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Computes the min element of a sequence.
            </summary>
            <param name="source">A sequence of values to calculate find the min for.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result = queryable.Min("Income");
            </code>
            </example>
            <returns>The min element in the sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Min(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Min(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Min(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Computes the min element of a sequence.
            </summary>
            <param name="source">A sequence of values to calculate find the min for.</param>
            <param name="lambda">A Lambda Expression.</param>
            <returns>The min element in the sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.OfType(System.Linq.IQueryable,System.Type)">
            <summary>
            Filters the elements of an <see cref="T:System.Linq.IQueryable"/> based on a specified type.
            </summary>
            <param name="source">An <see cref="T:System.Linq.IQueryable"/> whose elements to filter.</param>
            <param name="type">The type to filter the elements of the sequence on.</param>
            <returns>A collection that contains the elements from source that have the type.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.OfType(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String)">
            <summary>
            Filters the elements of an <see cref="T:System.Linq.IQueryable"/> based on a specified type.
            </summary>
            <param name="source">An <see cref="T:System.Linq.IQueryable"/> whose elements to filter.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="typeName">The type to filter the elements of the sequence on.</param>
            <returns>A collection that contains the elements from source that have the type.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.OfType(System.Linq.IQueryable,System.String)">
            <summary>
            Filters the elements of an <see cref="T:System.Linq.IQueryable"/> based on a specified type.
            </summary>
            <param name="source">An <see cref="T:System.Linq.IQueryable"/> whose elements to filter.</param>
            <param name="typeName">The type to filter the elements of the sequence on.</param>
            <returns>A collection that contains the elements from source that have the type.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.OrderBy``1(System.Linq.IQueryable{``0},System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Sorts the elements of a sequence in ascending or descending order according to a key.
            </summary>
            <typeparam name="TSource">The type of the elements of source.</typeparam>
            <param name="source">A sequence of values to order.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="ordering">An expression string to indicate values to order by.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IQueryable`1"/> whose elements are sorted according to the specified <paramref name="ordering"/>.</returns>
            <example>
            <code>
            <![CDATA[
            var resultSingle = queryable.OrderBy<User>("NumberProperty");
            var resultSingleDescending = queryable.OrderBy<User>("NumberProperty DESC");
            var resultMultiple = queryable.OrderBy<User>("NumberProperty, StringProperty");
            ]]>
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.OrderBy``1(System.Linq.IQueryable{``0},System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.OrderBy``1(System.Linq.IQueryable{``0},System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.OrderBy``1(System.Linq.IQueryable{``0},System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Collections.IComparer,System.Object[])">
            <summary>
            Sorts the elements of a sequence in ascending or descending order according to a key.
            </summary>
            <typeparam name="TSource">The type of the elements of source.</typeparam>
            <param name="source">A sequence of values to order.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="ordering">An expression string to indicate values to order by.</param>
            <param name="comparer">The comparer to use.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IQueryable`1"/> whose elements are sorted according to the specified <paramref name="ordering"/>.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.OrderBy``1(System.Linq.IQueryable{``0},System.String,System.Collections.IComparer,System.Object[])">
            <summary>
            Sorts the elements of a sequence in ascending or descending order according to a key.
            </summary>
            <typeparam name="TSource">The type of the elements of source.</typeparam>
            <param name="source">A sequence of values to order.</param>
            <param name="ordering">An expression string to indicate values to order by.</param>
            <param name="comparer">The comparer to use.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IQueryable`1"/> whose elements are sorted according to the specified <paramref name="ordering"/>.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.OrderBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Sorts the elements of a sequence in ascending or descending order according to a key.
            </summary>
            <param name="source">A sequence of values to order.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="ordering">An expression string to indicate values to order by.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters.  Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> whose elements are sorted according to the specified <paramref name="ordering"/>.</returns>
            <example>
            <code>
            var resultSingle = queryable.OrderBy("NumberProperty");
            var resultSingleDescending = queryable.OrderBy("NumberProperty DESC");
            var resultMultiple = queryable.OrderBy("NumberProperty, StringProperty DESC");
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.OrderBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Collections.IComparer,System.Object[])">
            <summary>
            Sorts the elements of a sequence in ascending or descending order according to a key.
            </summary>
            <param name="source">A sequence of values to order.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="ordering">An expression string to indicate values to order by.</param>
            <param name="comparer">The comparer to use.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters.  Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> whose elements are sorted according to the specified <paramref name="ordering"/>.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.OrderBy(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.OrderBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.OrderBy(System.Linq.IQueryable,System.String,System.Collections.IComparer,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.OrderBy(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Collections.IComparer,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Page(System.Linq.IQueryable,System.Int32,System.Int32)">
            <summary>
            Returns the elements as paged.
            </summary>
            <param name="source">The IQueryable to return elements from.</param>
            <param name="page">The page to return.</param>
            <param name="pageSize">The number of elements per page.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> that contains the paged elements.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Page``1(System.Linq.IQueryable{``0},System.Int32,System.Int32)">
            <summary>
            Returns the elements as paged.
            </summary>
            <typeparam name="TSource">The type of the source.</typeparam>
            <param name="source">The IQueryable to return elements from.</param>
            <param name="page">The page to return.</param>
            <param name="pageSize">The number of elements per page.</param>
            <returns>A <see cref="T:System.Linq.IQueryable`1"/> that contains the paged elements.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.PageResult(System.Linq.IQueryable,System.Int32,System.Int32,System.Nullable{System.Int32})">
            <summary>
            Returns the elements as paged and include the CurrentPage, PageCount, PageSize and RowCount.
            </summary>
            <param name="source">The IQueryable to return elements from.</param>
            <param name="page">The page to return.</param>
            <param name="pageSize">The number of elements per page.</param>
            <param name="rowCount">If this optional parameter has been defined, this value is used as the RowCount instead of executing a Linq `Count()`.</param>
            <returns>PagedResult</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.PageResult``1(System.Linq.IQueryable{``0},System.Int32,System.Int32,System.Nullable{System.Int32})">
            <summary>
            Returns the elements as paged and include the CurrentPage, PageCount, PageSize and RowCount.
            </summary>
            <typeparam name="TSource">The type of the source.</typeparam>
            <param name="source">The IQueryable to return elements from.</param>
            <param name="page">The page to return.</param>
            <param name="pageSize">The number of elements per page.</param>
            <param name="rowCount">If this optional parameter has been defined, this value is used as the RowCount instead of executing a Linq `Count()`.</param>
            <returns>PagedResult{TSource}</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Reverse(System.Linq.IQueryable)">
            <summary>
            Inverts the order of the elements in a sequence.
            </summary>
            <param name="source">A sequence of values to reverse.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> whose elements correspond to those of the input sequence in reverse order.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Select(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Projects each element of a sequence into a new form.
            </summary>
            <param name="source">A sequence of values to project.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="selector">A projection string expression to apply to each element.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters.  Similar to the way String.Format formats strings.</param>
            <returns>An <see cref="T:System.Linq.IQueryable"/> whose elements are the result of invoking a projection string on each element of source.</returns>
            <example>
            <code>
            var singleField = queryable.Select("StringProperty");
            var dynamicObject = queryable.Select("new (StringProperty1, StringProperty2 as OtherStringPropertyName)");
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Select(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Select(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Select``1(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Projects each element of a sequence into a new class of type TResult.
            Details see <see href="http://solutionizing.net/category/linq/"/>.
            </summary>
            <typeparam name="TResult">The type of the result.</typeparam>
            <param name="source">A sequence of values to project.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="selector">A projection string expression to apply to each element.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters.</param>
            <returns>An <see cref="T:System.Linq.IQueryable`1"/> whose elements are the result of invoking a projection string on each element of source.</returns>
            <example>
            <code language="cs">
            <![CDATA[
            var users = queryable.Select<User>("new (Username, Pwd as Password)");
            ]]>
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Select``1(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Select``1(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Select(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.Type,System.String,System.Object[])">
            <summary>
            Projects each element of a sequence into a new class of type TResult.
            Details see http://solutionizing.net/category/linq/ 
            </summary>
            <param name="source">A sequence of values to project.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="resultType">The result type.</param>
            <param name="selector">A projection string expression to apply to each element.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters.</param>
            <returns>An <see cref="T:System.Linq.IQueryable"/> whose elements are the result of invoking a projection string on each element of source.</returns>
            <example>
            <code>
            var users = queryable.Select(typeof(User), "new (Username, Pwd as Password)");
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Select(System.Linq.IQueryable,System.Type,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Select(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.Type,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Projects each element of a sequence to an <see cref="T:System.Linq.IQueryable"/> and combines the resulting sequences into one sequence.
            </summary>
            <param name="source">A sequence of values to project.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="selector">A projection string expression to apply to each element.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters.  Similar to the way String.Format formats strings.</param>
            <returns>An <see cref="T:System.Linq.IQueryable"/> whose elements are the result of invoking a one-to-many projection function on each element of the input sequence.</returns>
            <example>
            <code>
            var roles = users.SelectMany("Roles");
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.Type,System.String,System.Object[])">
            <summary>
            Projects each element of a sequence to an <see cref="T:System.Linq.IQueryable"/> and combines the resulting sequences into one sequence.
            </summary>
            <param name="source">A sequence of values to project.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="selector">A projection string expression to apply to each element.</param>
            <param name="resultType">The result type.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>An <see cref="T:System.Linq.IQueryable"/> whose elements are the result of invoking a one-to-many projection function on each element of the input sequence.</returns>
            <example>
            <code>
            var permissions = users.SelectMany(typeof(Permission), "Roles.SelectMany(Permissions)");
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany(System.Linq.IQueryable,System.Type,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.Type,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany``1(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Projects each element of a sequence to an <see cref="T:System.Linq.IQueryable`1"/> and combines the resulting sequences into one sequence.
            </summary>
            <typeparam name="TResult">The type of the result.</typeparam>
            <param name="source">A sequence of values to project.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="selector">A projection string expression to apply to each element.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>An <see cref="T:System.Linq.IQueryable`1"/> whose elements are the result of invoking a one-to-many projection function on each element of the input sequence.</returns>
            <example>
            <code>
            <![CDATA[
            var permissions = users.SelectMany<Permission>("Roles.SelectMany(Permissions)");
            ]]>
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany``1(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany``1(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.String,System.Object[],System.Object[])">
            <summary>
            Projects each element of a sequence to an <see cref="T:System.Linq.IQueryable"/>
            and invokes a result selector function on each element therein. The resulting
            values from each intermediate sequence are combined into a single, one-dimensional
            sequence and returned.
            </summary>
            <param name="source">A sequence of values to project.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="collectionSelector">A projection function to apply to each element of the input sequence.</param>
            <param name="resultSelector">A projection function to apply to each element of each intermediate sequence. Should only use x and y as parameter names.</param>
            <param name="collectionSelectorArgs">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <param name="resultSelectorArgs">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>
            An <see cref="T:System.Linq.IQueryable"/> whose elements are the result of invoking the one-to-many 
            projection function <paramref name="collectionSelector"/> on each element of source and then mapping
            each of those sequence elements and their corresponding source element to a result element.
            </returns>
            <example>
            <code>
            <![CDATA[
            // TODO
            ]]>
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany(System.Linq.IQueryable,System.String,System.String,System.Object[],System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.String,System.String,System.String,System.Object[],System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.String,System.String,System.String,System.Object[],System.Object[])">
            <summary>
            Projects each element of a sequence to an <see cref="T:System.Linq.IQueryable"/>
            and invokes a result selector function on each element therein. The resulting
            values from each intermediate sequence are combined into a single, one-dimensional
            sequence and returned.
            </summary>
            <param name="source">A sequence of values to project.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="collectionSelector">A projection function to apply to each element of the input sequence.</param>
            <param name="collectionParameterName">The name from collectionParameter to use. Default is x.</param>
            <param name="resultSelector">A projection function to apply to each element of each intermediate sequence.</param>
            <param name="resultParameterName">The name from resultParameterName to use. Default is y.</param>
            <param name="collectionSelectorArgs">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <param name="resultSelectorArgs">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>
            An <see cref="T:System.Linq.IQueryable"/> whose elements are the result of invoking the one-to-many 
            projection function <paramref name="collectionSelector"/> on each element of source and then mapping
            each of those sequence elements and their corresponding source element to a result element.
            </returns>
            <example>
            <code>
            <![CDATA[
            // TODO
            ]]>
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany(System.Linq.IQueryable,System.String,System.String,System.String,System.String,System.Object[],System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SelectMany(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.String,System.String,System.String,System.Object[],System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Single(System.Linq.IQueryable)">
            <summary>
            Returns the only element of a sequence, and throws an exception if there
            is not exactly one element in the sequence.
            </summary>
            <param name="source">A <see cref="T:System.Linq.IQueryable"/> to return the single element of.</param>
            <returns>The single element of the input sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Single(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Returns the only element of a sequence that satisfies a specified condition, and throws an exception if there
            is not exactly one element in the sequence.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the last element of.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>The first element in source that passes the test in predicate.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Single(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Single(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Single(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Returns the only element of a sequence that satisfies a specified condition, and throws an exception if there
            is not exactly one element in the sequence.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the last element of.</param>
            <param name="lambda">A cached Lambda Expression.</param>
            <returns>The first element in source that passes the test in predicate.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SingleOrDefault(System.Linq.IQueryable)">
            <summary>
            Returns the only element of a sequence, or a default value if the sequence
            is empty; this method throws an exception if there is more than one element
            in the sequence.
            </summary>
            <param name="source">A <see cref="T:System.Linq.IQueryable"/> to return the single element of.</param>
            <returns>The single element of the input sequence, or default if the sequence contains no elements.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SingleOrDefault(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Returns the only element of a sequence that satisfies a specified condition or a default value if the sequence
            is empty; and throws an exception if there is not exactly one element in the sequence.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the last element of.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>The first element in source that passes the test in predicate.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SingleOrDefault(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SingleOrDefault(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SingleOrDefault(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Returns the only element of a sequence that satisfies a specified condition or a default value if the sequence
            is empty; and throws an exception if there is not exactly one element in the sequence.
            </summary>
            <param name="source">The <see cref="T:System.Linq.IQueryable"/> to return the last element of.</param>
            <param name="lambda">A cached Lambda Expression.</param>
            <returns>The first element in source that passes the test in predicate.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Skip(System.Linq.IQueryable,System.Int32)">
            <summary>
            Bypasses a specified number of elements in a sequence and then returns the remaining elements.
            </summary>
            <param name="source">A <see cref="T:System.Linq.IQueryable"/> to return elements from.</param>
            <param name="count">The number of elements to skip before returning the remaining elements.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> that contains elements that occur after the specified index in the input sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SkipWhile(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Bypasses elements in a sequence as long as a specified condition is true and then returns the remaining elements.
            </summary>
            <param name="source">A sequence to check for being empty.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result1 = queryable.SkipWhile("Income > 50");
            var result2 = queryable.SkipWhile("Income > @0", 50);
            </code>
            </example>
            <returns>An <see cref="T:System.Linq.IQueryable"/> that contains elements from source starting at the first element in the linear series that does not pass the test specified by predicate.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SkipWhile(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.SkipWhile(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Sum(System.Linq.IQueryable)">
            <summary>
            Computes the sum of a sequence of numeric values.
            </summary>
            <param name="source">A sequence of numeric values to calculate the sum of.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result1 = queryable.Sum();
            var result2 = queryable.Select("Roles.Sum()");
            </code>
            </example>
            <returns>The sum of the values in the sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Sum(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Computes the sum of a sequence of numeric values.
            </summary>
            <param name="source">A sequence of numeric values to calculate the sum of.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result = queryable.Sum("Income");
            </code>
            </example>
            <returns>The sum of the values in the sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Sum(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Sum(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Sum(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Computes the sum of a sequence of numeric values.
            </summary>
            <param name="source">A sequence of numeric values to calculate the sum of.</param>
            <param name="lambda">A Lambda Expression.</param>
            <returns>The sum of the values in the sequence.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Take(System.Linq.IQueryable,System.Int32)">
            <summary>
            Returns a specified number of contiguous elements from the start of a sequence.
            </summary>
            <param name="source">The sequence to return elements from.</param>
            <param name="count">The number of elements to return.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> that contains the specified number of elements from the start of source.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.TakeWhile(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Returns elements from a sequence as long as a specified condition is true.
            </summary>
            <param name="source">A sequence to check for being empty.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">A function to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <example>
            <code language="cs">
            IQueryable queryable = employees.AsQueryable();
            var result1 = queryable.TakeWhile("Income > 50");
            var result2 = queryable.TakeWhile("Income > @0", 50);
            </code>
            </example>
            <returns>An <see cref="T:System.Linq.IQueryable"/> that contains elements from the input sequence occurring before the element at which the test specified by predicate no longer passes.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.TakeWhile(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.TakeWhile(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.ThenBy``1(System.Linq.IOrderedQueryable{``0},System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Performs a subsequent ordering of the elements in a sequence in ascending order according to a key.
            </summary>
            <typeparam name="TSource">The type of the elements of source.</typeparam>
            <param name="source">A sequence of values to order.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="ordering">An expression string to indicate values to order by.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IOrderedQueryable`1"/> whose elements are sorted according to the specified <paramref name="ordering"/>.</returns>
            <example>
            <code>
            <![CDATA[
            var result = queryable.OrderBy<User>("LastName");
            var resultSingle = result.ThenBy<User>("NumberProperty");
            var resultSingleDescending = result.ThenBy<User>("NumberProperty DESC");
            var resultMultiple = result.ThenBy<User>("NumberProperty, StringProperty");
            ]]>
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.ThenBy``1(System.Linq.IOrderedQueryable{``0},System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.ThenBy``1(System.Linq.IOrderedQueryable{``0},System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.ThenBy``1(System.Linq.IOrderedQueryable{``0},System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Collections.IComparer,System.Object[])">
            <summary>
            Performs a subsequent ordering of the elements in a sequence in ascending order according to a key.
            </summary>
            <typeparam name="TSource">The type of the elements of source.</typeparam>
            <param name="source">A sequence of values to order.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="ordering">An expression string to indicate values to order by.</param>
            <param name="comparer">The comparer to use.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IOrderedQueryable`1"/> whose elements are sorted according to the specified <paramref name="ordering"/>.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.ThenBy``1(System.Linq.IOrderedQueryable{``0},System.String,System.Collections.IComparer,System.Object[])">
            <summary>
            Performs a subsequent ordering of the elements in a sequence in ascending order according to a key.
            </summary>
            <typeparam name="TSource">The type of the elements of source.</typeparam>
            <param name="source">A sequence of values to order.</param>
            <param name="ordering">An expression string to indicate values to order by.</param>
            <param name="comparer">The comparer to use.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IOrderedQueryable`1"/> whose elements are sorted according to the specified <paramref name="ordering"/>.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.ThenBy(System.Linq.IOrderedQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Performs a subsequent ordering of the elements in a sequence in ascending order according to a key.
            </summary>
            <param name="source">A sequence of values to order.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="ordering">An expression string to indicate values to order by.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters.  Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> whose elements are sorted according to the specified <paramref name="ordering"/>.</returns>
            <example>
            <code>
            var result = queryable.OrderBy("LastName");
            var resultSingle = result.OrderBy("NumberProperty");
            var resultSingleDescending = result.OrderBy("NumberProperty DESC");
            var resultMultiple = result.OrderBy("NumberProperty, StringProperty DESC");
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.ThenBy(System.Linq.IOrderedQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Collections.IComparer,System.Object[])">
            <summary>
            Performs a subsequent ordering of the elements in a sequence in ascending order according to a key.
            </summary>
            <param name="source">A sequence of values to order.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="ordering">An expression string to indicate values to order by.</param>
            <param name="comparer">The comparer to use.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters.  Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> whose elements are sorted according to the specified <paramref name="ordering"/>.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.ThenBy(System.Linq.IOrderedQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.ThenBy(System.Linq.IOrderedQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.ThenBy(System.Linq.IOrderedQueryable,System.String,System.Collections.IComparer,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.ThenBy(System.Linq.IOrderedQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Collections.IComparer,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Where``1(System.Linq.IQueryable{``0},System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Filters a sequence of values based on a predicate.
            </summary>
            <typeparam name="TSource">The type of the elements of source.</typeparam>
            <param name="source">A <see cref="T:System.Linq.IQueryable`1"/> to filter.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">An expression string to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IQueryable`1"/> that contains elements from the input sequence that satisfy the condition specified by predicate.</returns>
            <example>
            <code language="cs">
            var result1 = queryable.Where("NumberProperty = 1");
            var result2 = queryable.Where("NumberProperty = @0", 1);
            var result3 = queryable.Where("StringProperty = null");
            var result4 = queryable.Where("StringProperty = \"abc\"");
            var result5 = queryable.Where("StringProperty = @0", "abc");
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Where``1(System.Linq.IQueryable{``0},System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Where``1(System.Linq.IQueryable{``0},System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Where(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])">
            <summary>
            Filters a sequence of values based on a predicate.
            </summary>
            <param name="source">A <see cref="T:System.Linq.IQueryable"/> to filter.</param>
            <param name="config">The <see cref="T:System.Linq.Dynamic.Core.ParsingConfig"/>.</param>
            <param name="predicate">An expression string to test each element for a condition.</param>
            <param name="args">An object array that contains zero or more objects to insert into the predicate as parameters. Similar to the way String.Format formats strings.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> that contains elements from the input sequence that satisfy the condition specified by predicate.</returns>
            <example>
            <code>
            var result1 = queryable.Where("NumberProperty = 1");
            var result2 = queryable.Where("NumberProperty = @0", 1);
            var result3 = queryable.Where("StringProperty = null");
            var result4 = queryable.Where("StringProperty = \"abc\"");
            var result5 = queryable.Where("StringProperty = @0", "abc");
            </code>
            </example>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Where(System.Linq.IQueryable,System.String,System.Object[])">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Where(System.Linq.IQueryable,System.Linq.Dynamic.Core.ParsingConfig,System.String,System.Object[])"/>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Where(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)">
            <summary>
            Filters a sequence of values based on a predicate.
            </summary>
            <param name="source">A <see cref="T:System.Linq.IQueryable"/> to filter.</param>
            <param name="lambda">A cached Lambda Expression.</param>
            <returns>A <see cref="T:System.Linq.IQueryable"/> that contains elements from the input sequence that satisfy the condition specified by LambdaExpression.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Where``1(System.Linq.IQueryable{``0},System.Linq.Expressions.LambdaExpression)">
            <inheritdoc cref="M:System.Linq.Dynamic.Core.DynamicQueryableExtensions.Where(System.Linq.IQueryable,System.Linq.Expressions.LambdaExpression)"/>
        </member>
        <member name="T:System.Linq.Dynamic.Core.DynamicQueryableWithFormattableStringExtensions">
            <summary>
            Provides a set of static extension methods for querying data structures that implement <see cref="T:System.Linq.IQueryable"/>.
            It supports a FormattableString string as predicate.
            <seealso cref="T:System.Linq.Dynamic.Core.DynamicQueryableExtensions"/>
            </summary>
        </member>
        <member name="T:System.Linq.Dynamic.Core.Exceptions.ParseException">
            <summary>
            Represents errors that occur while parsing dynamic linq string expressions.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.Exceptions.ParseException.Position">
            <summary>
            The location in the parsed string that produced the <see cref="T:System.Linq.Dynamic.Core.Exceptions.ParseException"/>.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Exceptions.ParseException.#ctor(System.String,System.Int32,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Linq.Dynamic.Core.Exceptions.ParseException"/> class with a specified error message and position.
            </summary>
            <param name="message">The message that describes the error.</param>
            <param name="position">The location in the parsed string that produced the <see cref="T:System.Linq.Dynamic.Core.Exceptions.ParseException"/></param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Exceptions.ParseException.ToString">
            <summary>
            Creates and returns a string representation of the current exception.
            </summary>
            <returns>A string representation of the current exception.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Exceptions.ParseException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            When overridden in a derived class, sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with information about the exception.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
            <PermissionSet>
              <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="*AllFiles*" PathDiscovery="*AllFiles*" />
              <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="SerializationFormatter" />
            </PermissionSet>
        </member>
        <member name="T:System.Linq.Dynamic.Core.ExtensibilityPoint">
            <summary>
            Extensibility point: If you want to modify expanded queries before executing them
            set your own functionality to override empty QueryOptimizer
            </summary>
        </member>
        <member name="F:System.Linq.Dynamic.Core.ExtensibilityPoint.QueryOptimizer">
            <summary>
            Place to optimize your queries. Example: Add a reference to Nuget package Linq.Expression.Optimizer 
            and in your program initializers set Extensibility.QueryOptimizer = ExpressionOptimizer.visit;
            </summary>
        </member>
        <member name="T:System.Linq.Dynamic.Core.GroupResult">
            <summary>
            The result of a call to a <see cref="T:System.Linq.Dynamic.Core.DynamicQueryableExtensions"/>.GroupByMany() overload.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.GroupResult.Key">
            <summary>
            The key value of the group.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.GroupResult.Count">
            <summary>
            The number of resulting elements in the group.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.GroupResult.Items">
            <summary>
            The resulting elements in the group.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.GroupResult.Subgroups">
            <summary>
            The resulting subgroups in the group.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.GroupResult.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> showing the key of the group and the number of items in the group.
            </summary>
            <returns>
            A <see cref="T:System.String" /> that represents this instance.
            </returns>
        </member>
        <member name="T:System.Linq.Dynamic.Core.IAssemblyHelper">
            <summary>
            IAssemblyHelper interface which is used to retrieve assemblies that have been loaded into the execution context of this application domain.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.IAssemblyHelper.GetAssemblies">
            <summary>
            Gets the assemblies that have been loaded into the execution context of this application domain.
            </summary>
            
            <returns>
            An array of assemblies in this application domain.
            </returns>
        </member>
        <member name="T:System.Linq.Dynamic.Core.IQueryableAnalyzer">
            <summary>
            Interface for QueryableAnalyzer.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.IQueryableAnalyzer.SupportsLinqToObjects(System.Linq.IQueryable,System.Linq.IQueryProvider)">
            <summary>
            Determines whether the specified query (and provider) supports LinqToObjects.
            </summary>
            <param name="query">The query to check.</param>
            <param name="provider">The provider to check (can be null).</param>
            <returns>true/false</returns>
        </member>
        <member name="T:System.Linq.Dynamic.Core.PagedResult">
            <summary>
            PagedResult
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.PagedResult.Queryable">
            <summary>
            Gets or sets the queryable.
            </summary>
            <value>
            The queryable.
            </value>
        </member>
        <member name="P:System.Linq.Dynamic.Core.PagedResult.CurrentPage">
            <summary>
            Gets or sets the current page.
            </summary>
            <value>
            The current page.
            </value>
        </member>
        <member name="P:System.Linq.Dynamic.Core.PagedResult.PageCount">
            <summary>
            Gets or sets the page count.
            </summary>
            <value>
            The page count.
            </value>
        </member>
        <member name="P:System.Linq.Dynamic.Core.PagedResult.PageSize">
            <summary>
            Gets or sets the size of the page.
            </summary>
            <value>
            The size of the page.
            </value>
        </member>
        <member name="P:System.Linq.Dynamic.Core.PagedResult.RowCount">
            <summary>
            Gets or sets the row count.
            </summary>
            <value>
            The row count.
            </value>
        </member>
        <member name="T:System.Linq.Dynamic.Core.PagedResult`1">
            <summary>
            PagedResult{TSource}
            </summary>
            <typeparam name="TSource">The type of the source.</typeparam>
        </member>
        <member name="P:System.Linq.Dynamic.Core.PagedResult`1.Queryable">
            <summary>
            Gets or sets the queryable.
            </summary>
            <value>
            The queryable.
            </value>
        </member>
        <member name="M:System.Linq.Dynamic.Core.ParameterExpressionHelper.GenerateRandomWord">
            <summary>
            Generates a random 16 character word derived from a Guid value.
            </summary>
        </member>
        <member name="T:System.Linq.Dynamic.Core.Parser.ConstantExpressionWrapper">
            <summary>
            Based on gblog by graeme-hill. https://github.com/graeme-hill/gblog/blob/master/source_content/articles/2014.139_entity-framework-dynamic-queries-and-parameterization.mkd
            </summary>
        </member>
        <member name="F:System.Linq.Dynamic.Core.Parser.EnumerationsFromMscorlib.PredefinedEnumerationTypes">
            <summary>
            All Enum types from mscorlib/netstandard.
            </summary>
        </member>
        <member name="T:System.Linq.Dynamic.Core.Parser.ExpressionParser">
            <summary>
            ExpressionParser
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.Parser.ExpressionParser.ItName">
            <summary>
            Gets name for the `it` field. By default this is set to the KeyWord value "it".
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.Parser.ExpressionParser.LastLambdaItName">
            <summary>
            There was a problem when an expression contained multiple lambdas where the ItName was not cleared and freed for the next lambda.
            This variable stores the ItName of the last parsed lambda.
            Not used internally by ExpressionParser, but used to preserve compatibility of parsingConfig.RenameParameterExpression
            which was designed to only work with mono-lambda expressions.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Parser.ExpressionParser.#ctor(System.Linq.Expressions.ParameterExpression[],System.String,System.Object[],System.Linq.Dynamic.Core.ParsingConfig)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Linq.Dynamic.Core.Parser.ExpressionParser"/> class.
            </summary>
            <param name="parameters">The parameters.</param>
            <param name="expression">The expression.</param>
            <param name="values">The values.</param>
            <param name="parsingConfig">The parsing configuration.</param>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Parser.ExpressionParser.Parse(System.Type,System.Boolean)">
            <summary>
            Uses the TextParser to parse the string into the specified result type.
            </summary>
            <param name="resultType">Type of the result.</param>
            <param name="createParameterCtor">if set to <c>true</c> [create parameter ctor].</param>
            <returns>Expression</returns>
        </member>
        <member name="T:System.Linq.Dynamic.Core.Parser.ExpressionPromoter">
            <summary>
            ExpressionPromoter
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Parser.ExpressionPromoter.#ctor(System.Linq.Dynamic.Core.ParsingConfig)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Linq.Dynamic.Core.Parser.ExpressionPromoter"/> class.
            </summary>
            <param name="config">The ParsingConfig.</param>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Parser.ExpressionPromoter.Promote(System.Linq.Expressions.Expression,System.Type,System.Boolean,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="T:System.Linq.Dynamic.Core.Parser.IExpressionPromoter">
            <summary>
            Expression promoter is used to promote object or value types
            to their destination type when an automatic promotion is available
            such as: int to int?
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Parser.IExpressionPromoter.Promote(System.Linq.Expressions.Expression,System.Type,System.Boolean,System.Boolean)">
            <summary>
            Promote an expression
            </summary>
            <param name="expr">Source expression</param>
            <param name="type">Destination data type to promote</param>
            <param name="exact">If the match must be exact</param>
            <param name="convertExpr">Convert expression</param>
            <returns>The promoted <see cref="T:System.Linq.Expressions.Expression"/> or null.</returns>
        </member>
        <member name="T:System.Linq.Dynamic.Core.Parser.NumberParser">
            <summary>
            NumberParser
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Parser.NumberParser.#ctor(System.Linq.Dynamic.Core.ParsingConfig)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Linq.Dynamic.Core.Parser.NumberParser"/> class.
            </summary>
            <param name="config">The ParsingConfig.</param>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Parser.NumberParser.ParseIntegerLiteral(System.Int32,System.String)">
            <summary>
            Tries to parse the text into a IntegerLiteral ConstantExpression.
            </summary>
            <param name="tokenPosition">The current token position (needed for error reporting).</param>
            <param name="text">The text.</param>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Parser.NumberParser.ParseRealLiteral(System.String,System.Char,System.Boolean)">
            <summary>
            Parse the text into a Real ConstantExpression.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Parser.NumberParser.TryParseNumber(System.String,System.Type,System.Object@)">
            <summary>
            Tries to parse the number (text) into the specified type.
            </summary>
            <param name="text">The text.</param>
            <param name="type">The type.</param>
            <param name="result">The result.</param>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Parser.NumberParser.ParseNumber(System.String,System.Type)">
            <summary>
            Parses the number (text) into the specified type.
            </summary>
            <param name="text">The text.</param>
            <param name="type">The type.</param>
        </member>
        <member name="T:System.Linq.Dynamic.Core.Parser.StringParser">
            <summary>
            Parse a Double and Single Quoted string.
            Some parts of the code is based on https://github.com/zzzprojects/Eval-Expression.NET
            </summary>
        </member>
        <member name="T:System.Linq.Dynamic.Core.Parser.SupportedMethods.MethodFinder.IAggregateSignatures">
            <summary>
            #794
            </summary>
        </member>
        <member name="T:System.Linq.Dynamic.Core.ParsingConfig">
            <summary>
            Configuration class for System.Linq.Dynamic.Core.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.Default">
            <summary>
            Default ParsingConfig
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.DefaultEFCore21">
            <summary>
            Default ParsingConfig for EntityFramework Core 2.1 and higher
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.IsCaseSensitive">
            <summary>Gets or sets if parameter, method, and properties resolution should be case sensitive or not (false by default).</summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.DefaultCosmosDb">
            <summary>
            Default ParsingConfig for CosmosDb
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.CustomTypeProvider">
            <summary>
            Gets or sets the <see cref="T:System.Linq.Dynamic.Core.CustomTypeProviders.IDynamicLinkCustomTypeProvider"/>.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.ExpressionPromoter">
            <summary>
            Gets or sets the <see cref="T:System.Linq.Dynamic.Core.Parser.IExpressionPromoter"/>.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.QueryableAnalyzer">
            <summary>
            Gets or sets the <see cref="T:System.Linq.Dynamic.Core.IQueryableAnalyzer"/>.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.AreContextKeywordsEnabled">
            <summary>
            Determines if the context keywords (it, parent, and root) are valid and usable inside a Dynamic Linq string expression.  
            Does not affect the usability of the equivalent context symbols ($, ^ and ~).
            
            Default value is <c>false</c>.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.EvaluateGroupByAtDatabase">
             <summary>
             Gets or sets a value indicating whether the EntityFramework version supports evaluating GroupBy at database level.
             See https://docs.microsoft.com/en-us/ef/core/what-is-new/ef-core-2.1#linq-groupby-translation
             
             Remark: when this setting is set to 'true', make sure to supply this ParsingConfig as first parameter on the extension methods.
            
             Default value is <c>false</c>.
             </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.UseParameterizedNamesInDynamicQuery">
             <summary>
             Use Parameterized Names in generated dynamic SQL query.
             See https://github.com/graeme-hill/gblog/blob/master/source_content/articles/2014.139_entity-framework-dynamic-queries-and-parameterization.mkd
            
             Default value is <c>false</c>.
             </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.AllowNewToEvaluateAnyType">
             <summary>
             Allows the New() keyword to evaluate any available Type.
            
             Default value is <c>false</c>.
             </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.RenameParameterExpression">
             <summary>
             Renames the (Typed)ParameterExpression empty Name to a the correct supplied name from `it`.
            
             Default value is <c>false</c>.
             </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.RenameEmptyParameterExpressionNames">
            <summary>
            Prevents any System.Linq.Expressions.ParameterExpression.Name value from being empty by substituting a random 16 character word.
            
            Default value is <c>false</c>.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.DisableMemberAccessToIndexAccessorFallback">
             <summary>
             By default when a member is not found in a type and the type has a string based index accessor it will be parsed as an index accessor. Use
             this flag to disable this behaviour and have parsing fail when parsing an expression
             where a member access on a non existing member happens.
            
             Default value is <c>false</c>.
             </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.ResolveTypesBySimpleName">
            <summary>
            By default finding types by a simple name is not supported.
            Use this flag to use the CustomTypeProvider to resolve types by a simple name like "Employee" instead of "MyDatabase.Entities.Employee".
            Note that a first matching type is returned and this functionality needs to scan all types from all assemblies, so use with caution.
            
            Default value is <c>false</c>.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.SupportEnumerationsFromSystemNamespace">
            <summary>
            Support enumeration-types from the System namespace in mscorlib. An example could be "StringComparison".
            
            Default value is true.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.DateTimeIsParsedAsUTC">
             <summary>
             By default DateTime (like 'Fri, 10 May 2019 11:03:17 GMT') is parsed as local time.
             Use this flag to parse all DateTime strings as UTC.
            
             Default value is <c>false</c>.
             </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.NumberParseCulture">
             <summary>
             The number parsing culture.
            
             Default value is CultureInfo.InvariantCulture
             </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.TypeConverters">
            <summary>
            Additional TypeConverters
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.NullPropagatingUseDefaultValueForNonNullableValueTypes">
            <summary>
            When using the NullPropagating function np(...), use a "default value" for non-nullable value types instead of "null value".
            
            Default value is <c>false</c>.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.SupportCastingToFullyQualifiedTypeAsString">
            <summary>
            Support casting to a full qualified type using a string (double quoted value).
            <code>
            var result = queryable.Select($"\"System.DateTime\"(LastUpdate)");
            </code>
            
            Default value is <c>true</c>.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.PrioritizePropertyOrFieldOverTheType">
             <summary>
             When the type and property have the same name the parser takes the property instead of type when this setting is set to <c>true</c>.
            
             This setting is also used for calling ExtensionMethods.
            
             Default value is <c>true</c>.
             </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.SupportDotInPropertyNames">
            <summary>
            Support a "." in a property-name. Used in the 'new (a.b as a.b)' syntax.
            
            Default value is <c>false</c>.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.DisallowNewKeyword">
             <summary>
             Disallows the New() keyword to be used to construct a class.
            
             Default value is <c>false</c>.
             </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.ParsingConfig.ConstantExpressionCacheConfig">
            <summary>
            Caches constant expressions to enhance performance. Periodic cleanup is performed to manage cache size, governed by this configuration.
            </summary>
        </member>
        <member name="T:System.Linq.Dynamic.Core.Tokenizer.TextParser">
            <summary>
            TextParser which can be used to parse a text into tokens.
            </summary>
        </member>
        <member name="F:System.Linq.Dynamic.Core.Tokenizer.TextParser.CurrentToken">
            <summary>
            The current parsed <see cref="T:System.Linq.Dynamic.Core.Tokenizer.Token"/>.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Tokenizer.TextParser.#ctor(System.Linq.Dynamic.Core.ParsingConfig,System.String)">
            <summary>
            Constructor for TextParser
            </summary>
            <param name="config"></param>
            <param name="text"></param>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Tokenizer.TextParser.Clone">
            <summary>
            This method is used to clone the current <see cref="T:System.Linq.Dynamic.Core.Tokenizer.TextParser"/>.
            </summary>
            <returns>Cloned <see cref="T:System.Linq.Dynamic.Core.Tokenizer.TextParser"/></returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Tokenizer.TextParser.PeekNextChar">
            <summary>
            Peek the next character.
            </summary>
            <returns>The next character, or \0 if end of string.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Tokenizer.TextParser.NextToken">
            <summary>
            Go to the next token.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Tokenizer.TextParser.ValidateToken(System.Linq.Dynamic.Core.Tokenizer.TokenId,System.String)">
            <summary>
            Check if the current token is the specified <see cref="T:System.Linq.Dynamic.Core.Tokenizer.TokenId"/>.
            </summary>
            <param name="tokenId">The tokenId to check.</param>
            <param name="errorMessage">The (optional) error message.</param>
        </member>
        <member name="T:System.Linq.Dynamic.Core.Tokenizer.Token">
            <summary>
            Token
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.Tokenizer.Token.Id">
            <summary>
            The TokenId.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.Tokenizer.Token.OriginalId">
            <summary>
            The Original TokenId.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.Tokenizer.Token.Text">
            <summary>
            The text.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.Tokenizer.Token.Pos">
            <summary>
            The position.
            </summary>
        </member>
        <member name="T:System.Linq.Dynamic.Core.Tokenizer.TokenId">
            <summary>
            TokenId which defines the text which is parsed.
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.TypeConverters.CustomDateTimeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the specified object to a <see cref="T:System.DateTime"></see>.
            </summary>
            <param name="context">The date format context.</param>
            <param name="culture">The date culture.</param>
            <param name="value">The object to be converted.</param>
            <returns>A <see cref="T:System.Nullable`1"></see> that represents the specified object.</returns>
            <exception cref="T:System.NotSupportedException">The conversion cannot be performed.</exception>
        </member>
        <member name="M:System.Linq.Dynamic.Core.TypeConverters.ITypeConverterFactory.GetConverter(System.Type)">
            <summary>
            Returns a type converter for the specified type.
            </summary>
            <param name="type">The System.Type of the target component.</param>
            <returns>A System.ComponentModel.TypeConverter for the specified type.</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.TypeConverters.TypeConverterFactory.GetConverter(System.Type)">
            <see cref="M:System.Linq.Dynamic.Core.TypeConverters.ITypeConverterFactory.GetConverter(System.Type)"/>
        </member>
        <member name="T:System.Linq.Dynamic.Core.Util.Cache.CacheConfig">
            <summary>
            Cache Configuration Options
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.Util.Cache.CacheConfig.TimeToLive">
            <summary>
            Sets a Time-To-Live (TTL) for items in the constant expression cache to prevent uncontrolled growth. 
            Items not accessed within this TTL will be expired, allowing garbage collection to reclaim the memory.
            Default is 10 minutes.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.Util.Cache.CacheConfig.MinItemsTrigger">
            <summary>
            Configures the minimum number of items required in the constant expression cache before triggering cleanup. 
            This prevents frequent cleanups, especially in caches with few items. 
            A default value of null implies that cleanup is always allowed to run, helping in timely removal of unused cache items.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.Util.Cache.CacheConfig.CleanupFrequency">
            <summary>
            Sets the frequency for running the cleanup process in the Constant Expression cache. 
            By default, cleanup occurs every 10 minutes.
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.Util.Cache.CacheConfig.ReturnExpiredItems">
            <summary>
            Enables returning expired cache items in scenarios where cleanup, running on a separate thread, 
            has not yet removed them. This allows for the retrieval of an expired object without needing to 
            clear and recreate it if a request is made concurrently with cleanup. Particularly useful 
            when cached items are deterministic, ensuring consistent results even from expired entries.
            Default true;
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Util.Cache.SlidingCache`2.#ctor(System.TimeSpan,System.Nullable{System.TimeSpan},System.Nullable{System.Int64},System.Linq.Dynamic.Core.Util.IDateTimeUtils,System.Boolean)">
            <summary>
                Sliding Thread Safe Cache
            </summary>
            <param name="timeToLive">The length of time any object would survive before being removed</param>
            <param name="cleanupFrequency">Only look for expired objects over specific periods</param>
            <param name="minCacheItemsBeforeCleanup">
                If defined, only allow the cleanup process after x number of cached items have
                been stored
            </param>
            <param name="dateTimeProvider">
                Provides the Time for the Caching object. Default will be created if not supplied. Used
                for Testing classes
            </param>
            <param name="returnExpiredItems">If a request for an item happens to be expired, but is still
            in known, don't expire it and return it instead.</param>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Util.Cache.SlidingCache`2.#ctor(System.Linq.Dynamic.Core.Util.Cache.CacheConfig,System.Linq.Dynamic.Core.Util.IDateTimeUtils)">
            <summary>
            Sliding Thread Safe Cache
            </summary>
            <param name="cacheConfig">The <see cref="T:System.Linq.Dynamic.Core.Util.Cache.CacheConfig"/> to use.</param>
            <param name="dateTimeProvider">
                Provides the Time for the Caching object. Default will be created if not supplied. Used
                for Testing classes
            </param>
        </member>
        <member name="P:System.Linq.Dynamic.Core.Util.Cache.SlidingCache`2.TimeToLive">
            <summary>
            Cache TTL value
            </summary>
        </member>
        <member name="P:System.Linq.Dynamic.Core.Util.Cache.SlidingCache`2.Count">
            <summary>
                Provide the number of items in the cache
            </summary>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Util.Cache.SlidingCache`2.AddOrUpdate(`0,`1)">
            <summary>
            Add or update the item in the cache, at the same time update the expiration time
            </summary>
            <param name="key"></param>
            <param name="value"></param>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Util.Cache.SlidingCache`2.TryGetValue(`0,`1@)">
            <summary>
            Attempt to get the value from the cache. This will extend the cache expiration time if the item is found
            </summary>
            <param name="key">Key</param>
            <param name="value">Value</param>
            <returns></returns>
        </member>
        <member name="T:System.Linq.Dynamic.Core.Util.ParameterExpressionRenamer">
            <summary>
            Renames a single (Typed)ParameterExpression in an Expression.
            </summary>
            <seealso cref="T:System.Linq.Expressions.ExpressionVisitor" />
        </member>
        <member name="M:System.Linq.Dynamic.Core.Util.ParameterExpressionRenamer.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Linq.Dynamic.Core.Util.ParameterExpressionRenamer"/> class.
            </summary>
            <param name="newName">The new name (the oldName is assumed to be "").</param>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Util.ParameterExpressionRenamer.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Linq.Dynamic.Core.Util.ParameterExpressionRenamer"/> class.
            </summary>
            <param name="oldName">The old name.</param>
            <param name="newName">The new name.</param>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Util.ParameterExpressionRenamer.Rename(System.Linq.Expressions.Expression,System.Linq.Expressions.ParameterExpression@)">
            <summary>
            Renames a single (Typed)ParameterExpression from specified expression.
            </summary>
            <param name="expression">The expression.</param>
            <param name="parameterExpression">The new generated (Typed)ParameterExpression.</param>
            <returns>Renamed Expression</returns>
        </member>
        <member name="M:System.Linq.Dynamic.Core.Util.ParameterExpressionRenamer.VisitParameter(System.Linq.Expressions.ParameterExpression)">
            <inheritdoc cref="M:System.Linq.Expressions.ExpressionVisitor.VisitParameter(System.Linq.Expressions.ParameterExpression)"/>
        </member>
        <member name="T:System.Linq.Dynamic.Core.Validation.ValidatedNotNullAttribute">
            <summary>
            To fix 'xxx' is null on at least one execution path. See also https://rules.sonarsource.com/csharp/RSPEC-3900.
            </summary>
        </member>
        <member name="T:System.Reflection.CustomIntrospectionExtensions">
            <summary>
            https://github.com/castleproject/Core/blob/netcore/src/Castle.Core/Compatibility/IntrospectionExtensions.cs
            </summary>
        </member>
        <member name="M:System.StringExtensions.IsNullOrWhiteSpace(System.String)">
            <summary>
            Indicates whether a specified string is null, empty, or consists only of white-space
            characters.
            
            Recreates the same functionality as System.String.IsNullOrWhiteSpace but included here
            for compatibility with net35.
            </summary>
            <param name="value">The string to test.</param>
            <returns>
            true if the value parameter is null or System.String.Empty, or if value consists
            exclusively of white-space characters.
            </returns>
        </member>
    </members>
</doc>
