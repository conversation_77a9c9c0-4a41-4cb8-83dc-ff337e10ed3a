﻿<Project>
  <ItemGroup>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\JL\20240914163324.docx))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>JL\20240914163324.docx</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\JL\20240914163324.docx))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\JL\20240914163455.docx))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>JL\20240914163455.docx</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\JL\20240914163455.docx))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\JL\20240914170550.docx))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>JL\20240914170550.docx</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\JL\20240914170550.docx))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\JL\20240914170759.docx))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>JL\20240914170759.docx</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\JL\20240914170759.docx))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\JL\20240914174555.docx))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>JL\20240914174555.docx</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\JL\20240914174555.docx))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\JL\20240918103959.docx))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>JL\20240918103959.docx</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\JL\20240918103959.docx))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\02288f93765d4169806e1f790838985e_161205.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20231220\02288f93765d4169806e1f790838985e_161205.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\02288f93765d4169806e1f790838985e_161205.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\16e7df7379804c878ee1e27c3e9a2478_152719.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20231220\16e7df7379804c878ee1e27c3e9a2478_152719.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\16e7df7379804c878ee1e27c3e9a2478_152719.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\1900c5ef53114bdd82528d84b479f37a_161138.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20231220\1900c5ef53114bdd82528d84b479f37a_161138.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\1900c5ef53114bdd82528d84b479f37a_161138.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\19573f0beb2d4f6f8eb187846c3dac33_160802.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20231220\19573f0beb2d4f6f8eb187846c3dac33_160802.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\19573f0beb2d4f6f8eb187846c3dac33_160802.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\1b692d3ce298456581e34ec7e191158c_144612.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20231220\1b692d3ce298456581e34ec7e191158c_144612.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\1b692d3ce298456581e34ec7e191158c_144612.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\27c572b081464b2fb4a1b26dbd8ecfd1_145114.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20231220\27c572b081464b2fb4a1b26dbd8ecfd1_145114.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\27c572b081464b2fb4a1b26dbd8ecfd1_145114.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\3a1aac411481483fad1a6f5aee08e2d6_143614.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20231220\3a1aac411481483fad1a6f5aee08e2d6_143614.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\3a1aac411481483fad1a6f5aee08e2d6_143614.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\423a93f979304bbb90efdc4dcdc6fd14_144507.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20231220\423a93f979304bbb90efdc4dcdc6fd14_144507.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\423a93f979304bbb90efdc4dcdc6fd14_144507.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\4d37104fe75048a2af6451fa625081ae_152515.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20231220\4d37104fe75048a2af6451fa625081ae_152515.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\4d37104fe75048a2af6451fa625081ae_152515.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\6300ebdea32a412cba44fbec39dae429_145151.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20231220\6300ebdea32a412cba44fbec39dae429_145151.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\6300ebdea32a412cba44fbec39dae429_145151.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\8924bd1bf5f34577b2780fd756bda866_152618.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20231220\8924bd1bf5f34577b2780fd756bda866_152618.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\8924bd1bf5f34577b2780fd756bda866_152618.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\8c281371824e4e21afee872b772d4b76_145659.png))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20231220\8c281371824e4e21afee872b772d4b76_145659.png</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\8c281371824e4e21afee872b772d4b76_145659.png))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\aa118acfca5b431fab331ea4f39bc052_160558.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20231220\aa118acfca5b431fab331ea4f39bc052_160558.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\aa118acfca5b431fab331ea4f39bc052_160558.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\ef55c4a1fc2249918fad9c34e18b9f5f_150821.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20231220\ef55c4a1fc2249918fad9c34e18b9f5f_150821.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20231220\ef55c4a1fc2249918fad9c34e18b9f5f_150821.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\0baa3c04a63a4684989fc3e6b8f81af8_114218.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\0baa3c04a63a4684989fc3e6b8f81af8_114218.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\0baa3c04a63a4684989fc3e6b8f81af8_114218.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\0e6f3d8a23074b11bc7a4ac6cdb7ac49_113439.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\0e6f3d8a23074b11bc7a4ac6cdb7ac49_113439.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\0e6f3d8a23074b11bc7a4ac6cdb7ac49_113439.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\0f2fa61bf9e44334b9d1c4dc09a08bb2_114220.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\0f2fa61bf9e44334b9d1c4dc09a08bb2_114220.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\0f2fa61bf9e44334b9d1c4dc09a08bb2_114220.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\11a4832ca1384338a621d3279705317d_114437.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\11a4832ca1384338a621d3279705317d_114437.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\11a4832ca1384338a621d3279705317d_114437.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\151e97120dbc4e28969609d01a62fdf3_112357.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\151e97120dbc4e28969609d01a62fdf3_112357.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\151e97120dbc4e28969609d01a62fdf3_112357.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\1585ca399d8e4cb7991fe7dc689bd7d5_113201.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\1585ca399d8e4cb7991fe7dc689bd7d5_113201.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\1585ca399d8e4cb7991fe7dc689bd7d5_113201.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\1b9682a42dc8449dba5825850ee0427e_112344.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\1b9682a42dc8449dba5825850ee0427e_112344.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\1b9682a42dc8449dba5825850ee0427e_112344.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\1c35f36ee93e44b88b8d264c0cfec33e_113432.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\1c35f36ee93e44b88b8d264c0cfec33e_113432.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\1c35f36ee93e44b88b8d264c0cfec33e_113432.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\2ae59f5751fb4d3e906d4349aef42d04_114212.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\2ae59f5751fb4d3e906d4349aef42d04_114212.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\2ae59f5751fb4d3e906d4349aef42d04_114212.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\2b00935d5dc24859bbd0d073c0a6dc6b_112341.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\2b00935d5dc24859bbd0d073c0a6dc6b_112341.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\2b00935d5dc24859bbd0d073c0a6dc6b_112341.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\2feaad1e9fd44e30ab022bd47a786685_112719.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\2feaad1e9fd44e30ab022bd47a786685_112719.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\2feaad1e9fd44e30ab022bd47a786685_112719.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\34713b6679914950b155bc6d268da15d_112606.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\34713b6679914950b155bc6d268da15d_112606.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\34713b6679914950b155bc6d268da15d_112606.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\3813bbde224541f893f0edac24202481_114227.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\3813bbde224541f893f0edac24202481_114227.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\3813bbde224541f893f0edac24202481_114227.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\39ee1a3792b64b04831acd1888b99f05_113437.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\39ee1a3792b64b04831acd1888b99f05_113437.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\39ee1a3792b64b04831acd1888b99f05_113437.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\3e88840d89a24e51866554855fedde38_170629.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\3e88840d89a24e51866554855fedde38_170629.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\3e88840d89a24e51866554855fedde38_170629.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\416415eec9b348f3ba347e2707dcab2d_114433.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\416415eec9b348f3ba347e2707dcab2d_114433.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\416415eec9b348f3ba347e2707dcab2d_114433.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\63d2e9aebacb42269deacaf5c716af96_112359.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\63d2e9aebacb42269deacaf5c716af96_112359.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\63d2e9aebacb42269deacaf5c716af96_112359.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\6d9b2becca024755ac0fcf34a292958f_114440.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\6d9b2becca024755ac0fcf34a292958f_114440.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\6d9b2becca024755ac0fcf34a292958f_114440.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\8f520cd405744cdfa7e3c80444a5d4e5_114223.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\8f520cd405744cdfa7e3c80444a5d4e5_114223.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\8f520cd405744cdfa7e3c80444a5d4e5_114223.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\9d8ae517dd4f404b8f41338bf30bb64b_114215.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\9d8ae517dd4f404b8f41338bf30bb64b_114215.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\9d8ae517dd4f404b8f41338bf30bb64b_114215.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\a02025fbc37d4e59abf51319174ccb3d_114446.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\a02025fbc37d4e59abf51319174ccb3d_114446.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\a02025fbc37d4e59abf51319174ccb3d_114446.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\ba8ad2524c40463da4cfe408d21dc0aa_170608.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\ba8ad2524c40463da4cfe408d21dc0aa_170608.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\ba8ad2524c40463da4cfe408d21dc0aa_170608.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\db0a7153f5104732b3ca0a6e9d8e9562_112353.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\db0a7153f5104732b3ca0a6e9d8e9562_112353.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\db0a7153f5104732b3ca0a6e9d8e9562_112353.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\ef85351747d847959b6bc5af704ed6c6_114443.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240104\ef85351747d847959b6bc5af704ed6c6_114443.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240104\ef85351747d847959b6bc5af704ed6c6_114443.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117.zip))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117.zip</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117.zip))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\00e7adf51af242cd839cc62657e78f46_162649.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\00e7adf51af242cd839cc62657e78f46_162649.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\00e7adf51af242cd839cc62657e78f46_162649.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\048a610638b346e1b984f5ef432e5e26_161459.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\048a610638b346e1b984f5ef432e5e26_161459.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\048a610638b346e1b984f5ef432e5e26_161459.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\052a8790995e467a8f1077a15dbd52a9_174421.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\052a8790995e467a8f1077a15dbd52a9_174421.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\052a8790995e467a8f1077a15dbd52a9_174421.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\07931b79887d464f96e082361288f52c_161538.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\07931b79887d464f96e082361288f52c_161538.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\07931b79887d464f96e082361288f52c_161538.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\08f507c6342d44d1845796b571b729bb_165729.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\08f507c6342d44d1845796b571b729bb_165729.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\08f507c6342d44d1845796b571b729bb_165729.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\0c3642e4405a4b30a2e820040090c67d_175027.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\0c3642e4405a4b30a2e820040090c67d_175027.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\0c3642e4405a4b30a2e820040090c67d_175027.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\0db2c486fcd445c2ba5d6c4bdf929dba_175012.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\0db2c486fcd445c2ba5d6c4bdf929dba_175012.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\0db2c486fcd445c2ba5d6c4bdf929dba_175012.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\10cf25ea4bb24e5982d07264a74016aa_142259.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\10cf25ea4bb24e5982d07264a74016aa_142259.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\10cf25ea4bb24e5982d07264a74016aa_142259.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\175329dfeb38470a8708341c647ee6a0_163929.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\175329dfeb38470a8708341c647ee6a0_163929.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\175329dfeb38470a8708341c647ee6a0_163929.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\1a5ac4f0fcca484c980841dc222ab1dc_164158.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\1a5ac4f0fcca484c980841dc222ab1dc_164158.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\1a5ac4f0fcca484c980841dc222ab1dc_164158.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\1aae90d217644cd6a81b8bf6d6c48581_164137.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\1aae90d217644cd6a81b8bf6d6c48581_164137.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\1aae90d217644cd6a81b8bf6d6c48581_164137.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\1ace6ff1089d4e279d25c9812b6b1b15_170824.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\1ace6ff1089d4e279d25c9812b6b1b15_170824.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\1ace6ff1089d4e279d25c9812b6b1b15_170824.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\1f147c15e62540c1a9604a58cc6e8f01_171456.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\1f147c15e62540c1a9604a58cc6e8f01_171456.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\1f147c15e62540c1a9604a58cc6e8f01_171456.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\00e7adf51af242cd839cc62657e78f46_162649.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\00e7adf51af242cd839cc62657e78f46_162649.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\00e7adf51af242cd839cc62657e78f46_162649.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\048a610638b346e1b984f5ef432e5e26_161459.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\048a610638b346e1b984f5ef432e5e26_161459.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\048a610638b346e1b984f5ef432e5e26_161459.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\052a8790995e467a8f1077a15dbd52a9_174421.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\052a8790995e467a8f1077a15dbd52a9_174421.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\052a8790995e467a8f1077a15dbd52a9_174421.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\07931b79887d464f96e082361288f52c_161538.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\07931b79887d464f96e082361288f52c_161538.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\07931b79887d464f96e082361288f52c_161538.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\08f507c6342d44d1845796b571b729bb_165729.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\08f507c6342d44d1845796b571b729bb_165729.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\08f507c6342d44d1845796b571b729bb_165729.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\0c3642e4405a4b30a2e820040090c67d_175027.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\0c3642e4405a4b30a2e820040090c67d_175027.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\0c3642e4405a4b30a2e820040090c67d_175027.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\0db2c486fcd445c2ba5d6c4bdf929dba_175012.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\0db2c486fcd445c2ba5d6c4bdf929dba_175012.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\0db2c486fcd445c2ba5d6c4bdf929dba_175012.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\10cf25ea4bb24e5982d07264a74016aa_142259.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\10cf25ea4bb24e5982d07264a74016aa_142259.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\10cf25ea4bb24e5982d07264a74016aa_142259.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\175329dfeb38470a8708341c647ee6a0_163929.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\175329dfeb38470a8708341c647ee6a0_163929.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\175329dfeb38470a8708341c647ee6a0_163929.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\1a5ac4f0fcca484c980841dc222ab1dc_164158.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\1a5ac4f0fcca484c980841dc222ab1dc_164158.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\1a5ac4f0fcca484c980841dc222ab1dc_164158.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\1aae90d217644cd6a81b8bf6d6c48581_164137.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\1aae90d217644cd6a81b8bf6d6c48581_164137.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\1aae90d217644cd6a81b8bf6d6c48581_164137.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\1ace6ff1089d4e279d25c9812b6b1b15_170824.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\1ace6ff1089d4e279d25c9812b6b1b15_170824.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\1ace6ff1089d4e279d25c9812b6b1b15_170824.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\1d57f19a61e24097a7a380e6fed9f746_153931.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\1d57f19a61e24097a7a380e6fed9f746_153931.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\1d57f19a61e24097a7a380e6fed9f746_153931.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\1f147c15e62540c1a9604a58cc6e8f01_171456.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\1f147c15e62540c1a9604a58cc6e8f01_171456.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\1f147c15e62540c1a9604a58cc6e8f01_171456.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\217044135e9d48f9bb4450842c3b38be_142034.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\217044135e9d48f9bb4450842c3b38be_142034.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\217044135e9d48f9bb4450842c3b38be_142034.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\22df7af7e8c64460813af6cad1851f7b_150508.pdf))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\22df7af7e8c64460813af6cad1851f7b_150508.pdf</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\22df7af7e8c64460813af6cad1851f7b_150508.pdf))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\298603771366462bb78e20eaac9f286c_171510.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\298603771366462bb78e20eaac9f286c_171510.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\298603771366462bb78e20eaac9f286c_171510.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\2ef1e6f7bbcb4d0b991e5ae15f7910b9_142316.png))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\2ef1e6f7bbcb4d0b991e5ae15f7910b9_142316.png</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\2ef1e6f7bbcb4d0b991e5ae15f7910b9_142316.png))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\2f55ec0e7dfb419e8f740868e98d0d68_162629.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\2f55ec0e7dfb419e8f740868e98d0d68_162629.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\2f55ec0e7dfb419e8f740868e98d0d68_162629.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\3450d04df9b640c599de2dfa00e79f44_172952.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\3450d04df9b640c599de2dfa00e79f44_172952.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\3450d04df9b640c599de2dfa00e79f44_172952.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\3513354c9a0041849dbfb8ca0dd4e22a_155451.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\3513354c9a0041849dbfb8ca0dd4e22a_155451.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\3513354c9a0041849dbfb8ca0dd4e22a_155451.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\374375a36d97436c85e28d681e494ed3_174442.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\374375a36d97436c85e28d681e494ed3_174442.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\374375a36d97436c85e28d681e494ed3_174442.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\37d67aa058244cdd9cc4bb042b5e2597_174433.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\37d67aa058244cdd9cc4bb042b5e2597_174433.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\37d67aa058244cdd9cc4bb042b5e2597_174433.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\3b6a4e093720423f999befb594cbd933_165744.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\3b6a4e093720423f999befb594cbd933_165744.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\3b6a4e093720423f999befb594cbd933_165744.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\416d0b411a9c4d708c91905a152d406f_161503.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\416d0b411a9c4d708c91905a152d406f_161503.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\416d0b411a9c4d708c91905a152d406f_161503.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\42388c1475824848b3e5cfb6a3156ea7_172234.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\42388c1475824848b3e5cfb6a3156ea7_172234.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\42388c1475824848b3e5cfb6a3156ea7_172234.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\45ded14eff484114aa53029d610b6c62_161525.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\45ded14eff484114aa53029d610b6c62_161525.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\45ded14eff484114aa53029d610b6c62_161525.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\47dd0a53b68344a58375d4f5851d5707_173642.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\47dd0a53b68344a58375d4f5851d5707_173642.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\47dd0a53b68344a58375d4f5851d5707_173642.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\48db77efa1ee4c70a7df88ec26a6e829_143539.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\48db77efa1ee4c70a7df88ec26a6e829_143539.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\48db77efa1ee4c70a7df88ec26a6e829_143539.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\52638d621e27413aaffe856aaddbe3c2_165737.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\52638d621e27413aaffe856aaddbe3c2_165737.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\52638d621e27413aaffe856aaddbe3c2_165737.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\52b7d034ddba413490d63f93455fefa4_173638.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\52b7d034ddba413490d63f93455fefa4_173638.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\52b7d034ddba413490d63f93455fefa4_173638.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\52c34602c348430892561eda4cc33a6f_175023.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\52c34602c348430892561eda4cc33a6f_175023.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\52c34602c348430892561eda4cc33a6f_175023.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\5ae50a807ef644b78fb33fcd7cc1372a_150717.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\5ae50a807ef644b78fb33fcd7cc1372a_150717.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\5ae50a807ef644b78fb33fcd7cc1372a_150717.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\5f804cedaa8e4699ab8661813abe4708_164132.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\5f804cedaa8e4699ab8661813abe4708_164132.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\5f804cedaa8e4699ab8661813abe4708_164132.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\6064d451265a4b1b937470e8b65e294c_173631.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\6064d451265a4b1b937470e8b65e294c_173631.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\6064d451265a4b1b937470e8b65e294c_173631.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\62020d3ce55c453cb5f8bf6a619c3261_171505.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\62020d3ce55c453cb5f8bf6a619c3261_171505.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\62020d3ce55c453cb5f8bf6a619c3261_171505.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\6652916c36c8454ebd1503847f9420ff_170336.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\6652916c36c8454ebd1503847f9420ff_170336.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\6652916c36c8454ebd1503847f9420ff_170336.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\713d2c2930fe4e6583f5b8618899984c_174447.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\713d2c2930fe4e6583f5b8618899984c_174447.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\713d2c2930fe4e6583f5b8618899984c_174447.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\742ebfe2a3c347dea35562550a53c6db_173634.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\742ebfe2a3c347dea35562550a53c6db_173634.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\742ebfe2a3c347dea35562550a53c6db_173634.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\7476b2dfe8234c128d8720aca24bcfb9_173006.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\7476b2dfe8234c128d8720aca24bcfb9_173006.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\7476b2dfe8234c128d8720aca24bcfb9_173006.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\776926c993534ce496633bf0c7e47155_172216.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\776926c993534ce496633bf0c7e47155_172216.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\776926c993534ce496633bf0c7e47155_172216.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\79c3a744613c4e2d9fa930ee59131632_175032.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\79c3a744613c4e2d9fa930ee59131632_175032.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\79c3a744613c4e2d9fa930ee59131632_175032.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\7cfc0eea06724043a21ac4cea73893d9_170352.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\7cfc0eea06724043a21ac4cea73893d9_170352.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\7cfc0eea06724043a21ac4cea73893d9_170352.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\7d4a9dc732b44561a3fcb5e5c828088e_162639.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\7d4a9dc732b44561a3fcb5e5c828088e_162639.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\7d4a9dc732b44561a3fcb5e5c828088e_162639.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\7ebbe688e8314f02a488c256dbbeceb8_162633.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\7ebbe688e8314f02a488c256dbbeceb8_162633.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\7ebbe688e8314f02a488c256dbbeceb8_162633.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\7f7f18cee6e84eaf9df95a55e5a8a7d5_173617.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\7f7f18cee6e84eaf9df95a55e5a8a7d5_173617.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\7f7f18cee6e84eaf9df95a55e5a8a7d5_173617.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\843f894b417a405ebe9835375f6f3cec_172957.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\843f894b417a405ebe9835375f6f3cec_172957.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\843f894b417a405ebe9835375f6f3cec_172957.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\85fb68e021084a3aae98c52b30d3e3c0_162612.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\85fb68e021084a3aae98c52b30d3e3c0_162612.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\85fb68e021084a3aae98c52b30d3e3c0_162612.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\8b1cdbbb80d54599aa13f475ac4c26e4_164142.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\8b1cdbbb80d54599aa13f475ac4c26e4_164142.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\8b1cdbbb80d54599aa13f475ac4c26e4_164142.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\8ce74ed93f07410d9337280c34d24e12_171500.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\8ce74ed93f07410d9337280c34d24e12_171500.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\8ce74ed93f07410d9337280c34d24e12_171500.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\8fad3a52ffd34c53a6a41e366ad7d3fa_142329.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\8fad3a52ffd34c53a6a41e366ad7d3fa_142329.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\8fad3a52ffd34c53a6a41e366ad7d3fa_142329.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\8fb78f85d553432ebc1e784cf5f6bee2_153944.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\8fb78f85d553432ebc1e784cf5f6bee2_153944.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\8fb78f85d553432ebc1e784cf5f6bee2_153944.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\992650e344cd44dab67521ed148e2923_164151.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\992650e344cd44dab67521ed148e2923_164151.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\992650e344cd44dab67521ed148e2923_164151.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\99933e31c2cc45cb86e389810d71cc78_163914.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\99933e31c2cc45cb86e389810d71cc78_163914.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\99933e31c2cc45cb86e389810d71cc78_163914.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\999d956d9df1463ab3210ba4e7cd1da7_171452.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\999d956d9df1463ab3210ba4e7cd1da7_171452.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\999d956d9df1463ab3210ba4e7cd1da7_171452.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\a5fd4094f6a6473595ae2533c5bb26ba_163002.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\a5fd4094f6a6473595ae2533c5bb26ba_163002.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\a5fd4094f6a6473595ae2533c5bb26ba_163002.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\ac6b64dced7a41a8a457af85f7965e96_172942.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\ac6b64dced7a41a8a457af85f7965e96_172942.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\ac6b64dced7a41a8a457af85f7965e96_172942.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\b27d74b855504d5dad5244d1d20585c5_162554.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\b27d74b855504d5dad5244d1d20585c5_162554.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\b27d74b855504d5dad5244d1d20585c5_162554.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\b6946022117e4b799ec4756d8b5ecd94_172239.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\b6946022117e4b799ec4756d8b5ecd94_172239.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\b6946022117e4b799ec4756d8b5ecd94_172239.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\b83e78a661ee44438068e81aa0dc5edb_170831.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\b83e78a661ee44438068e81aa0dc5edb_170831.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\b83e78a661ee44438068e81aa0dc5edb_170831.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\c03c2c322b00463ca3e6baf2da8de95d_170348.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\c03c2c322b00463ca3e6baf2da8de95d_170348.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\c03c2c322b00463ca3e6baf2da8de95d_170348.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\c071400752fc4de3a6f3a7e61fb2bc0a_161513.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\c071400752fc4de3a6f3a7e61fb2bc0a_161513.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\c071400752fc4de3a6f3a7e61fb2bc0a_161513.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\c46f418306764492a1a36108cbeec9e0_165724.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\c46f418306764492a1a36108cbeec9e0_165724.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\c46f418306764492a1a36108cbeec9e0_165724.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\c490a85cbde842dda7f459f02be3f306_172221.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\c490a85cbde842dda7f459f02be3f306_172221.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\c490a85cbde842dda7f459f02be3f306_172221.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\c640737304dd4a5fbd4530a39c030fca_175018.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\c640737304dd4a5fbd4530a39c030fca_175018.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\c640737304dd4a5fbd4530a39c030fca_175018.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\c965b1e1d2094648a723bc2a0efe7bd5_153935.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\c965b1e1d2094648a723bc2a0efe7bd5_153935.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\c965b1e1d2094648a723bc2a0efe7bd5_153935.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\c988ce65e00740e5944910332658c852_165712.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\c988ce65e00740e5944910332658c852_165712.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\c988ce65e00740e5944910332658c852_165712.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\ccef49bd9c784ab6bda65f78ed234287_173001.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\ccef49bd9c784ab6bda65f78ed234287_173001.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\ccef49bd9c784ab6bda65f78ed234287_173001.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\cef3df33167948bbae28754dc7c70055_170820.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\cef3df33167948bbae28754dc7c70055_170820.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\cef3df33167948bbae28754dc7c70055_170820.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\cf870dced76140a19cee4994b8a441f6_162604.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\cf870dced76140a19cee4994b8a441f6_162604.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\cf870dced76140a19cee4994b8a441f6_162604.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\d19acd6b749245af919ad53d9288587d_161643.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\d19acd6b749245af919ad53d9288587d_161643.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\d19acd6b749245af919ad53d9288587d_161643.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\d39a6c4eb2164626bb518a5f64e51889_162653.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\d39a6c4eb2164626bb518a5f64e51889_162653.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\d39a6c4eb2164626bb518a5f64e51889_162653.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\d45b453778ff473e9b231a44f9e20ab6_141826.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\d45b453778ff473e9b231a44f9e20ab6_141826.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\d45b453778ff473e9b231a44f9e20ab6_141826.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\d66c8c974fa84eb198c3a5987c4765ce_170901.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\d66c8c974fa84eb198c3a5987c4765ce_170901.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\d66c8c974fa84eb198c3a5987c4765ce_170901.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\d7d1b819d30c4e82abe9abf533e43b6c_141727.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\d7d1b819d30c4e82abe9abf533e43b6c_141727.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\d7d1b819d30c4e82abe9abf533e43b6c_141727.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\dbea52d2a7714a4390933c63891eac64_172230.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\dbea52d2a7714a4390933c63891eac64_172230.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\dbea52d2a7714a4390933c63891eac64_172230.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\df403e126f9f451597eec42354157019_162931.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\df403e126f9f451597eec42354157019_162931.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\df403e126f9f451597eec42354157019_162931.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\e03590085af6411ea44e284223eed29f_165452.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\e03590085af6411ea44e284223eed29f_165452.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\e03590085af6411ea44e284223eed29f_165452.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\e1522968fa154f589cb2ece6d0989017_170815.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\e1522968fa154f589cb2ece6d0989017_170815.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\e1522968fa154f589cb2ece6d0989017_170815.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\e24959a892d546f1bc82f730eb10749d_170340.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\e24959a892d546f1bc82f730eb10749d_170340.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\e24959a892d546f1bc82f730eb10749d_170340.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\e3c14bcc21eb48828b94d1c71be62979_144152.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\e3c14bcc21eb48828b94d1c71be62979_144152.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\e3c14bcc21eb48828b94d1c71be62979_144152.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\e3e73b611a894e62a319640b71729573_170344.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\e3e73b611a894e62a319640b71729573_170344.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\e3e73b611a894e62a319640b71729573_170344.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\e63315e252c14407bdcb4c53c3d4c145_160252.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\e63315e252c14407bdcb4c53c3d4c145_160252.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\e63315e252c14407bdcb4c53c3d4c145_160252.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\e651feadb3fb48d78cd20c6780707f92_170827.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\e651feadb3fb48d78cd20c6780707f92_170827.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\e651feadb3fb48d78cd20c6780707f92_170827.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\ea42f9cca0554eb4b7bfecadf9851e5f_162644.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\ea42f9cca0554eb4b7bfecadf9851e5f_162644.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\ea42f9cca0554eb4b7bfecadf9851e5f_162644.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\eb79ecd57a6243c5899c97da9fbd94d4_174438.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\eb79ecd57a6243c5899c97da9fbd94d4_174438.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\eb79ecd57a6243c5899c97da9fbd94d4_174438.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\efe55fb559e844d78f0b0a186f70af51_153940.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\efe55fb559e844d78f0b0a186f70af51_153940.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\efe55fb559e844d78f0b0a186f70af51_153940.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f00fbdc893384b79bb7d5e7d944e31e6_174428.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\f00fbdc893384b79bb7d5e7d944e31e6_174428.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f00fbdc893384b79bb7d5e7d944e31e6_174428.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f268d4495a4349479688e243d911227a_161531.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\f268d4495a4349479688e243d911227a_161531.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f268d4495a4349479688e243d911227a_161531.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f5464c34ba1a4ab7897b625f79832d69_175037.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\f5464c34ba1a4ab7897b625f79832d69_175037.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f5464c34ba1a4ab7897b625f79832d69_175037.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f5b5c18f635e4131bba3ff49debabd22_173647.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\f5b5c18f635e4131bba3ff49debabd22_173647.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f5b5c18f635e4131bba3ff49debabd22_173647.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f81fe81c94ed4cbdaaa6a1e7c16c8e06_161518.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\f81fe81c94ed4cbdaaa6a1e7c16c8e06_161518.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f81fe81c94ed4cbdaaa6a1e7c16c8e06_161518.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f8d4c2de882043e2993b9f6ea4a09c9b_172226.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\f8d4c2de882043e2993b9f6ea4a09c9b_172226.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f8d4c2de882043e2993b9f6ea4a09c9b_172226.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f8e8254d307d42cfafe1f3213da82788_160831.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\f8e8254d307d42cfafe1f3213da82788_160831.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f8e8254d307d42cfafe1f3213da82788_160831.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f97e356ae3394dafb8d587f8ac4ec992_172948.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\f97e356ae3394dafb8d587f8ac4ec992_172948.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\f97e356ae3394dafb8d587f8ac4ec992_172948.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\faff463398a04b47bf8e0143b3d5009c_164147.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\faff463398a04b47bf8e0143b3d5009c_164147.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\faff463398a04b47bf8e0143b3d5009c_164147.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\ffa5d6c64fc048abbc06243fc740bc89_165717.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\20240117\ffa5d6c64fc048abbc06243fc740bc89_165717.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\20240117\ffa5d6c64fc048abbc06243fc740bc89_165717.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\217044135e9d48f9bb4450842c3b38be_142034.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\217044135e9d48f9bb4450842c3b38be_142034.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\217044135e9d48f9bb4450842c3b38be_142034.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\22df7af7e8c64460813af6cad1851f7b_150508.pdf))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\22df7af7e8c64460813af6cad1851f7b_150508.pdf</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\22df7af7e8c64460813af6cad1851f7b_150508.pdf))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\298603771366462bb78e20eaac9f286c_171510.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\298603771366462bb78e20eaac9f286c_171510.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\298603771366462bb78e20eaac9f286c_171510.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\2ef1e6f7bbcb4d0b991e5ae15f7910b9_142316.png))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\2ef1e6f7bbcb4d0b991e5ae15f7910b9_142316.png</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\2ef1e6f7bbcb4d0b991e5ae15f7910b9_142316.png))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\2f55ec0e7dfb419e8f740868e98d0d68_162629.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\2f55ec0e7dfb419e8f740868e98d0d68_162629.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\2f55ec0e7dfb419e8f740868e98d0d68_162629.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\3450d04df9b640c599de2dfa00e79f44_172952.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\3450d04df9b640c599de2dfa00e79f44_172952.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\3450d04df9b640c599de2dfa00e79f44_172952.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\3513354c9a0041849dbfb8ca0dd4e22a_155451.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\3513354c9a0041849dbfb8ca0dd4e22a_155451.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\3513354c9a0041849dbfb8ca0dd4e22a_155451.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\374375a36d97436c85e28d681e494ed3_174442.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\374375a36d97436c85e28d681e494ed3_174442.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\374375a36d97436c85e28d681e494ed3_174442.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\37d67aa058244cdd9cc4bb042b5e2597_174433.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\37d67aa058244cdd9cc4bb042b5e2597_174433.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\37d67aa058244cdd9cc4bb042b5e2597_174433.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\3b6a4e093720423f999befb594cbd933_165744.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\3b6a4e093720423f999befb594cbd933_165744.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\3b6a4e093720423f999befb594cbd933_165744.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\416d0b411a9c4d708c91905a152d406f_161503.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\416d0b411a9c4d708c91905a152d406f_161503.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\416d0b411a9c4d708c91905a152d406f_161503.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\42388c1475824848b3e5cfb6a3156ea7_172234.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\42388c1475824848b3e5cfb6a3156ea7_172234.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\42388c1475824848b3e5cfb6a3156ea7_172234.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\45ded14eff484114aa53029d610b6c62_161525.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\45ded14eff484114aa53029d610b6c62_161525.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\45ded14eff484114aa53029d610b6c62_161525.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\47dd0a53b68344a58375d4f5851d5707_173642.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\47dd0a53b68344a58375d4f5851d5707_173642.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\47dd0a53b68344a58375d4f5851d5707_173642.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\48db77efa1ee4c70a7df88ec26a6e829_143539.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\48db77efa1ee4c70a7df88ec26a6e829_143539.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\48db77efa1ee4c70a7df88ec26a6e829_143539.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\52638d621e27413aaffe856aaddbe3c2_165737.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\52638d621e27413aaffe856aaddbe3c2_165737.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\52638d621e27413aaffe856aaddbe3c2_165737.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\52b7d034ddba413490d63f93455fefa4_173638.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\52b7d034ddba413490d63f93455fefa4_173638.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\52b7d034ddba413490d63f93455fefa4_173638.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\52c34602c348430892561eda4cc33a6f_175023.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\52c34602c348430892561eda4cc33a6f_175023.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\52c34602c348430892561eda4cc33a6f_175023.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\5ae50a807ef644b78fb33fcd7cc1372a_150717.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\5ae50a807ef644b78fb33fcd7cc1372a_150717.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\5ae50a807ef644b78fb33fcd7cc1372a_150717.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\5f804cedaa8e4699ab8661813abe4708_164132.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\5f804cedaa8e4699ab8661813abe4708_164132.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\5f804cedaa8e4699ab8661813abe4708_164132.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\6064d451265a4b1b937470e8b65e294c_173631.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\6064d451265a4b1b937470e8b65e294c_173631.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\6064d451265a4b1b937470e8b65e294c_173631.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\62020d3ce55c453cb5f8bf6a619c3261_171505.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\62020d3ce55c453cb5f8bf6a619c3261_171505.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\62020d3ce55c453cb5f8bf6a619c3261_171505.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\6652916c36c8454ebd1503847f9420ff_170336.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\6652916c36c8454ebd1503847f9420ff_170336.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\6652916c36c8454ebd1503847f9420ff_170336.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\713d2c2930fe4e6583f5b8618899984c_174447.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\713d2c2930fe4e6583f5b8618899984c_174447.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\713d2c2930fe4e6583f5b8618899984c_174447.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\742ebfe2a3c347dea35562550a53c6db_173634.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\742ebfe2a3c347dea35562550a53c6db_173634.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\742ebfe2a3c347dea35562550a53c6db_173634.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\7476b2dfe8234c128d8720aca24bcfb9_173006.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\7476b2dfe8234c128d8720aca24bcfb9_173006.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\7476b2dfe8234c128d8720aca24bcfb9_173006.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\776926c993534ce496633bf0c7e47155_172216.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\776926c993534ce496633bf0c7e47155_172216.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\776926c993534ce496633bf0c7e47155_172216.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\79c3a744613c4e2d9fa930ee59131632_175032.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\79c3a744613c4e2d9fa930ee59131632_175032.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\79c3a744613c4e2d9fa930ee59131632_175032.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\8fad3a52ffd34c53a6a41e366ad7d3fa_142329.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240117\8fad3a52ffd34c53a6a41e366ad7d3fa_142329.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240117\8fad3a52ffd34c53a6a41e366ad7d3fa_142329.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240318\1c26ed817d26403f96160f71aa31025d_153704.png))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240318\1c26ed817d26403f96160f71aa31025d_153704.png</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240318\1c26ed817d26403f96160f71aa31025d_153704.png))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240318\1fdc41878ffd4818b42e997667130d79_153515.png))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240318\1fdc41878ffd4818b42e997667130d79_153515.png</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240318\1fdc41878ffd4818b42e997667130d79_153515.png))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240318\2583f36a323f47358d576d83068e7dd8_154345.png))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240318\2583f36a323f47358d576d83068e7dd8_154345.png</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240318\2583f36a323f47358d576d83068e7dd8_154345.png))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240318\d63cb91080eb4ce8ae46442173a24f6f_153746.png))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240318\d63cb91080eb4ce8ae46442173a24f6f_153746.png</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240318\d63cb91080eb4ce8ae46442173a24f6f_153746.png))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\06591c30a3444d5b94ecf217968a5133_141309.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240319\06591c30a3444d5b94ecf217968a5133_141309.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\06591c30a3444d5b94ecf217968a5133_141309.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\3b8efe26828747ccb62aadd5084d93cc_141321.png))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240319\3b8efe26828747ccb62aadd5084d93cc_141321.png</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\3b8efe26828747ccb62aadd5084d93cc_141321.png))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\5fc422d78987451aab206ea3913af5a8_141030.png))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240319\5fc422d78987451aab206ea3913af5a8_141030.png</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\5fc422d78987451aab206ea3913af5a8_141030.png))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\5ff5f3f035c6435dabcd2e4a4e113f54_141226.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240319\5ff5f3f035c6435dabcd2e4a4e113f54_141226.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\5ff5f3f035c6435dabcd2e4a4e113f54_141226.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\6448ea4c80d94648ac1dd94d67a67b20_140535.png))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240319\6448ea4c80d94648ac1dd94d67a67b20_140535.png</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\6448ea4c80d94648ac1dd94d67a67b20_140535.png))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\9397e102114d44409e4ba3c9d78323ab_140540.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240319\9397e102114d44409e4ba3c9d78323ab_140540.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\9397e102114d44409e4ba3c9d78323ab_140540.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\e4c0efe35afe41f5851535d1f3b6a43d_141221.png))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240319\e4c0efe35afe41f5851535d1f3b6a43d_141221.png</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\e4c0efe35afe41f5851535d1f3b6a43d_141221.png))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\f225a54f7ed54d75afe0064221109eec_141040.png))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240319\f225a54f7ed54d75afe0064221109eec_141040.png</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240319\f225a54f7ed54d75afe0064221109eec_141040.png))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240320\50735db06cd541339a3195903b79d9f0_162240.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240320\50735db06cd541339a3195903b79d9f0_162240.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240320\50735db06cd541339a3195903b79d9f0_162240.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240320\5a0d8bbb0a86421aa899782d5d0315ac_162235.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240320\5a0d8bbb0a86421aa899782d5d0315ac_162235.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240320\5a0d8bbb0a86421aa899782d5d0315ac_162235.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240320\7a402de19bac4f21873989f09817d486_162224.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240320\7a402de19bac4f21873989f09817d486_162224.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240320\7a402de19bac4f21873989f09817d486_162224.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240320\7afaf933e8684f819c37b920b0092146_162251.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240320\7afaf933e8684f819c37b920b0092146_162251.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240320\7afaf933e8684f819c37b920b0092146_162251.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240320\ad9c88c6fb224115b490da92ea213a4f_162245.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240320\ad9c88c6fb224115b490da92ea213a4f_162245.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240320\ad9c88c6fb224115b490da92ea213a4f_162245.jpg))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240320\f797ff12ab954572bca8c33b9e7367f3_162230.jpg))">
      <SourceType>Package</SourceType>
      <SourceId>Coldairarrow.Api</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/Coldairarrow.Api</BasePath>
      <RelativePath>UploadFile\20240320\f797ff12ab954572bca8c33b9e7367f3_162230.jpg</RelativePath>
      <AssetKind></AssetKind>
      <AssetMode></AssetMode>
      <AssetRole></AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath($(MSBuildThisFileDirectory)..\staticwebassets\UploadFile\20240320\f797ff12ab954572bca8c33b9e7367f3_162230.jpg))</OriginalItemSpec>
    </StaticWebAsset>
  </ItemGroup>
</Project>