<template>
  <a-card :bordered="false"
    style="position: fixed; height: calc(100% - 150px); overflow-y: auto; width: calc(100% - -189px);">
    <div>
      <div class="Dis_Left" style="position: fixed; height: calc(100% - 200px); overflow-y: auto; ">
        <!-- <a-input-search style="margin-bottom: 8px" placeholder="Search" @change="onInputTreeChange" />-->
        <teleport name="tree" v-if="ParentIdTreeData!=null&&ParentIdTreeData.length>0">
          <a-tree @select="TreeSelect" :defaultExpandAll="true" :tree-data="ParentIdTreeData" />
        </teleport>

      </div>
      <div class="Dis_Right">
        <div class="table-operator">
          <a-button type="primary" icon="plus" @click="hanldleAdd()">新建</a-button>
          <a-button type="primary" icon="minus" @click="handleDelete(selectedRowKeys)" :disabled="!hasSelected()"
            :loading="loading">删除</a-button>
          <a-button type="primary" icon="redo" @click="getDataList()">刷新</a-button>
        </div>

        <a-table ref="table" :columns="columns" :rowKey="row => row.Id" :dataSource="data" :loading="loading"
          :pagination="false" @change="handleTableChange" :scroll="{ x: 1300, y: 500 }"
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true"
          :defaultExpandAllRows="true">
          <span slot="action" slot-scope="text, record">
            <template>
              <a @click="handleEdit(record.Id)">编辑</a>
              <a-divider type="vertical" />
              <a @click="handleDelete([record.Id])">删除</a>
            </template>
          </span>
        </a-table>
        <!-- <div v-else>
          <a-empty :image="simpleImage" />
        </div> -->
      </div>
    </div>
    <edit-form ref="editForm" :afterSubmit="init"></edit-form>
  </a-card>
</template>

<script>
import EditForm from './EditForm'
import { Empty } from 'ant-design-vue'
const columns = [
  { title: '部门名', dataIndex: 'Text', width: '20%' },
  { title: '操作', dataIndex: 'action', scopedSlots: { customRender: 'action' } }
]

export default {
  components: {
    EditForm
  },
  mounted () {
    this.getDataList()
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 10,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      simpleImage: Empty.PRESENTED_IMAGE_SIMPLE,
      filters: {},
      sorter: { field: 'Id', order: 'asc' },
      loading: false,
      columns,
      queryParam: {},
      visible: false,
      selectedRowKeys: [],
      treeData: [
        {
          title: 'Expand to load',
          key: '0'
        },
        {
          title: 'Expand to load',
          key: '1'
        },
        {
          title: 'Tree Node',
          key: '2',
          isLeaf: true
        }
      ],

      ParentIdTreeData: [],
      expandedKeys: [],
      TreeIds: '' //树形主键id
    }
  },
  mounted () {
    this.getDataList()
  },
  methods: {
    GetTree () {
      this.$http.post('/Base_Manage/Base_Company/GetTreeDataList', {}).then(resJson => {
        console.log(resJson.Data)
        if (resJson.Success) {
          this.ParentIdTreeData = resJson.Data
          if (this.ParentIdTreeData != null && this.ParentIdTreeData.length > 0) {
            const Treekeys = this.ParentIdTreeData.map(i => {
              return i.key
            })
            this.expandedKeys.push(...Treekeys)
            console.log(this.expandedKeys)
          }
        }
      })
    },
    TreeSelect (selectedKeys, info) {
      //  console.log('selected', selectedKeys[0]);
      this.TreeIds = selectedKeys[0]
      this.init()
    },
    handleTableChange (pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.filters = { ...filters }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    getDataList () {
      this.selectedRowKeys = []
      this.GetTree()
      this.TreeIds = '7a579db2-f4e3-489c-aed9-d39cf78a1cfe'
      this.init()
    },
    init () {
      this.data = []
      if (!!this.TreeIds) {
        this.loading = true
        this.$http
          .post('/Base_Manage/Base_Department/GetTreeDataList', {
            'companyId': this.TreeIds
          })
          .then(resJson => {
            this.loading = false
            this.data = resJson.Data
            const pagination = { ...this.pagination }
            pagination.total = resJson.Total
            this.pagination = pagination
          })
      }
      else {
        this.$message.warning('请选择所属公司!')
      }
    },
    onSelectChange (selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    hasSelected () {
      return this.selectedRowKeys.length > 0
    },
    hanldleAdd () {
      this.$refs.editForm.openForm()
    },
    handleEdit (id) {
      this.$refs.editForm.openForm(id)
    },
    handleDelete (ids) {
      var thisObj = this
      this.$confirm({
        title: '确认删除吗?',
        onOk () {
          return new Promise((resolve, reject) => {
            thisObj.submitDelete(ids, resolve, reject)
          })
        }
      })
    },
    submitDelete (ids, resolve, reject) {
      this.$http.post('/Base_Manage/Base_Department/DeleteData', ids).then(resJson => {
        resolve()

        if (resJson.Success) {
          this.$message.success('操作成功!')

          this.init()
        } else {
          this.$message.error(resJson.Msg)
        }
      })
    }
  }
}
</script>
<style>
.Dis_Left {
  float: left;
}

.Dis_Right {
  float: right;
  width: 85%;
}
</style>