﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System;
using Coldairarrow.Util.UEditor;
using Newtonsoft.Json;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_FeePointEntityBusiness : BaseBusiness<HR_FeePointEntity>, IHR_FeePointEntityBusiness, ITransientDependency
    {
        public HR_FeePointEntityBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_FeePointEntity>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_FeePointEntity>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_FeePointEntity, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }
        public async Task<List<HR_FeePointDto>> GetAllDataList(ProductConditinDto input)
        {
            List<HR_FeePointDto> hR_FeePoints = new List<HR_FeePointDto>();
            var list =await GetIQueryable().ToListAsync();
            var where = LinqHelper.True<HR_FeePointEntity>();
            if (!input.userAd.IsNullOrWhiteSpace())
            {
                list = list.Where(x => x.F_UserCode == input.userAd).ToList();
            }
            if (!input.userName.IsNullOrWhiteSpace())
            {
                list = list.Where(x => x.F_UserName == input.userName).ToList();
               
            }
            if (input.year.HasValue)
            {
                list = list.Where(x => x.F_Iyear == input.year.Value).ToList();
            }
            if (list != null && list.Count > 0)
            {
                hR_FeePoints = JsonConvert.DeserializeObject<List<HR_FeePointDto>>(JsonConvert.SerializeObject(list));
            }
            return hR_FeePoints;
        }
        public async Task<HR_FeePointEntity> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_FeePointEntity data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_FeePointEntity data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_FeePointEntity>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_FeePointEntity, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_FeePointEntity, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        public HR_FeePointEntity GetThePoint(string id)
        {
            var entity = GetIQueryable().FirstOrDefault(x => x.F_UserId == id && x.F_Iyear == DateTime.Now.Year);
            return entity;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}