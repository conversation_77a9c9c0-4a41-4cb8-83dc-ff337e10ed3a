﻿using Coldairarrow.Entity;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using Dynamitey.DynamicObjects;
using System.Collections.Generic;

namespace Coldairarrow.IBusiness
{
    /// <summary>
    /// 操作者
    /// </summary>
    public interface IOperator
    {
        /// <summary>
        /// 当前操作者UserId
        /// </summary>
        string UserId { get; }
        /// <summary>
        /// 当前操作者RealName
        /// </summary>
        string RealName { get; }

        string ADName { get; set; }

        Base_UserDTO Property { get; }

        #region 操作方法

        /// <summary>
        /// 判断是否为超级管理员
        /// </summary>
        /// <returns></returns>
        bool IsAdmin();

        /// <summary>
        /// 记录操作日志
        /// </summary>
        /// <param name="userLogType">用户日志类型</param>
        /// <param name="msg">内容</param>
        void WriteUserLog(UserLogType userLogType, string msg, string json = "");
        /// <summary>
        /// 记录操作日志
        /// </summary>
        /// <param name="log">用户日志</param>
        void WriteUserLog(Base_UserLog log);
        /// <summary>
        /// 记录操作日志
        /// </summary>
        /// <param name="log">用户日志</param>
        void WriteUserLog(List<Base_UserLog> log);
        #endregion
    }
}
