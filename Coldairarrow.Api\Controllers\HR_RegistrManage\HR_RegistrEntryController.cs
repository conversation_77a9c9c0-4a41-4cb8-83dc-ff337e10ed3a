﻿using Coldairarrow.Business.HR_RegistrManage;
using Coldairarrow.Entity.HR_RegistrManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Coldairarrow.Entity;
using Newtonsoft.Json;

namespace Coldairarrow.Api.Controllers.HR_RegistrManage
{
    [Route("/HR_RegistrManage/[controller]/[action]")]
    public class HR_RegistrEntryController : BaseApiController
    {
        #region DI

        public HR_RegistrEntryController(IHR_RegistrEntryBusiness hR_RegistrEntryBus,
                    IHR_RegistrCertificateBusiness hR_RegistrCertificateBus,
                    IHR_RegistrEduBackBusiness hR_RegistrEduBackBus,
                    IHR_RegistrFamilyRelatBusiness hR_RegistrFamilyRelatBus,
                    IHR_RegistrRelationshipBusiness hR_RegistrRelationshipBus,
                    IHR_RegistrResultsBusiness hR_RegistrResultsBus,
                    IHR_RegistrWorkExpeBusiness hR_RegistrWorkExpeBus)
        {
            _hR_RegistrEntryBus = hR_RegistrEntryBus;
            _hR_RegistrCertificateBus = hR_RegistrCertificateBus;
            _hR_RegistrEduBackBus = hR_RegistrEduBackBus;
            _hR_RegistrFamilyRelatBus = hR_RegistrFamilyRelatBus;
            _hR_RegistrRelationshipBus = hR_RegistrRelationshipBus;
            _hR_RegistrResultsBus = hR_RegistrResultsBus;
            _hR_RegistrWorkExpeBus = hR_RegistrWorkExpeBus;
        }

        IHR_RegistrEntryBusiness _hR_RegistrEntryBus { get; }
        IHR_RegistrCertificateBusiness _hR_RegistrCertificateBus { get; }
        IHR_RegistrEduBackBusiness _hR_RegistrEduBackBus { get; }
        IHR_RegistrFamilyRelatBusiness _hR_RegistrFamilyRelatBus { get; }
        IHR_RegistrRelationshipBusiness _hR_RegistrRelationshipBus { get; }
        IHR_RegistrResultsBusiness _hR_RegistrResultsBus { get; }
        IHR_RegistrWorkExpeBusiness _hR_RegistrWorkExpeBus { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_RegistrEntry>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_RegistrEntryBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_RegistrEntry> GetTheData(IdInputDTO input)
        {
            return await _hR_RegistrEntryBus.GetTheDataAsync(input.id);
        }

        /// <summary>
        /// 获取表单详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public EmployeeRegistrationDTO GetFormData(IdInputDTO input)
        {
            return _hR_RegistrEntryBus.GetFormData(input.id);
        }
        /// <summary>
        /// 获取表单详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public EmployeeRegistrationDTO GetThenFormData(IdInputDTO input)
        {
            return _hR_RegistrEntryBus.GetFormData(input.id);
        }
        /// <summary>
        /// 是否存在该员工
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <param name="IdCard">身份证</param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public string GetIsExist(NameCard nameCard)
        {
            return _hR_RegistrEntryBus.GetIsExist(nameCard.userName, nameCard.IdCard);
        }

        /// <summary>
        /// 获取未入职员工的数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<List<HR_RegistrEntry>> GetNoInductionList()
        {
            return await _hR_RegistrEntryBus.GetNoInductionListAsync();
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_RegistrEntry data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_RegistrEntryBus.AddDataAsync(data);
            }
            else
            {
                await _hR_RegistrEntryBus.UpdateDataAsync(data);
            }
        }
        /// <summary>
        /// 员工登记与应聘对比并入职保存正式
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task SaveFormal(DataandId dataandId)
        {
            List<SelectData> selectDatas = JsonConvert.DeserializeObject<List<SelectData>>(dataandId.data);
            await _hR_RegistrEntryBus.SaveFormal(selectDatas, dataandId.id);
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_RegistrEntryBus.DeleteDataAsync(ids);
        }
        /// <summary>
        /// 员工登记保存
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>

        [HttpPost]
        [Transactional]
        public async Task<AjaxResult> SaveFormData(EmployeeRegistrationDTO data)
        {
            try
            {
                _hR_RegistrEntryBus.SaveFormData(data);
                return Success();
            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }
        }
        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_RegistrEntryBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<HR_RegistrEntry>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _hR_RegistrEntryBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion

    }
    #region 类
    public class NameCard
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string userName { get; set; }
        /// <summary>
        /// 身份证
        /// </summary>
        public string IdCard { get; set; }
    }
    public class DataandId
    {
        /// <summary>
        /// 对比最终数据
        /// </summary>
        public string data { get; set; }
        /// <summary>
        /// 员工id
        /// </summary>
        public string id { get; set; }
    }
    #endregion
}