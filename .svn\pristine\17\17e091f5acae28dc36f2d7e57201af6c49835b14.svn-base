﻿using Coldairarrow.Entity.Wechat_CostDept;
using Coldairarrow.Util;
using Coldairarrow.Util.DTO;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

namespace Coldairarrow.Business.Wechat_CostDept
{
    public class Wechat_FileBusiness : BaseBusiness<Wechat_File>, IWechat_FileBusiness, ITransientDependency
    {
        public Wechat_FileBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<Wechat_File>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Wechat_File>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Wechat_File, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Wechat_File> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(Wechat_File data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Wechat_File data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }
        public  List<Wechat_File> GetSystemList()
        {
            var q = GetIQueryable();
            var list = q.Where(o => o.W_Module == 0).ToList().Distinct(new Wechat_FileNameComparer()).ToList();
            //var list = q.Where(o => o.W_Module == 0).GroupBy(p => p.W_Name).Select(g => g.First()).ToList();
            return list;
        }
        public List<Wechat_File> GetListByGuideId(string index)
        {
            var q = GetIQueryable();
            var list = q.Where(o => o.W_GuideId == index).ToList();
            return list;
        }
        //查
        public Wechat_File GetDataById(string index)
        {
            var q = GetIQueryable();
            var data = q.Where(o => o.Id == index).FirstOrDefault();
            return data;
        }
        //比较新旧文件进行更新
        public string UpdateFils(string Id, List<FileDTO> oldFiles, List<FileDTO> newFiles)
        {
            var result = "";
            foreach (var i in newFiles)
            {
                i.useStatus = 0;
                foreach (var j in oldFiles)
                {
                    if (i.uid == j.uid)
                    {
                        i.useStatus = 1;
                    }
                }
                if (i.useStatus == 0)
                {
                    //为0的即是新的 增加
                    var file = new Wechat_File
                    {
                        Id = i.uid,
                        W_Name = i.name,
                        W_Url = i.url,
                        W_ThumbUrl = i.thumbUrl,
                        W_Status = i.status,
                        W_GuideId = Id
                    };
                     AddDataAsync(file).Wait();
                     result = result +","+file.Id;
                }else
                {
                    result = result + "," + i.uid;
                }
            }
            foreach (var i in oldFiles)
            {
                i.useStatus = 2;
                foreach (var j in newFiles)
                {
                    if (i.uid == j.uid)
                    {
                        i.useStatus = 1;
                    }
                }
                if (i.useStatus == 2)
                {
                    DeleteAsync(i.uid).Wait();
                }
            }
            //返回新的
            return result;
        }
        #endregion

        #region 私有成员

        #endregion

        #region 去重标准
        public class Wechat_FileNameComparer : IEqualityComparer<Wechat_File>
        {
            public bool Equals(Wechat_File x, Wechat_File y)
            {
                if (x == null)
                    return y == null;
                return x.W_Name == y.W_Name;
            }


            public int GetHashCode(Wechat_File obj)
            {
                if (obj == null)
                    return 0;
                return obj.W_Name.GetHashCode();
            }
        }
        #endregion

    }
}