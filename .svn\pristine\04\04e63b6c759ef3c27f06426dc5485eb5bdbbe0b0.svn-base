﻿using Coldairarrow.Entity.Plan_Manage;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.Plan_Manage
{
    public interface ITargetStatisticSubscribeBusiness
    {
        Task<PageResult<TargetStatisticSubscribe>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<TargetStatisticSubscribe> GetTheDataAsync(string id);
        Task AddDataAsync(TargetStatisticSubscribe data);
        Task UpdateDataAsync(TargetStatisticSubscribe data);
        Task DeleteDataAsync(List<string> ids);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
        Task<TargetStatisticSubscribeDTO> GetTargetStatisticSubscribe();
    }
    /// <summary>
    /// 认购未签DTO
    /// </summary>
    public class TargetStatisticSubscribeDTO
    {
        /// <summary>
        /// 各项目得数据
        /// </summary>
        public List<TargetStatisticSubscribe> projectDatas { get; set; }
        /// <summary>
        /// 全项目得汇总数据
        /// </summary>
        public TargetStatisticSubscribe allProjectData { get; set; }
    }
}