﻿
using Coldairarrow.Business.HR_ReportFormsManage;
using Coldairarrow.Entity.HR_ReportFormsManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.HR_ReportFormsManage
{
    [Route("/HR_ReportFormsManage/[controller]/[action]")]
    public class HR_EmployeesBasicInfoController : BaseApiController
    {
        #region DI

        public HR_EmployeesBasicInfoController(IHR_EmployeesBasicInfoBusiness hR_EmployeesBasicInfoBusiness)
        {
            _hR_EmployeesBasicInfoBusiness = hR_EmployeesBasicInfoBusiness;
        }

        IHR_EmployeesBasicInfoBusiness _hR_EmployeesBasicInfoBusiness { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_EmployeesBasicInfoDTO>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_EmployeesBasicInfoBusiness.GetDataListAsync(input);
        }

        //[HttpPost]
        //public async Task<HR_FormalEmployeesTheDTO> GetTheData(IdInputDTO input)
        //{
        //    return await _hR_FormalEmployeesBus.GetFormDataAsync(input.id);
        //}
        #endregion

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_EmployeesBasicInfoBusiness.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "员工基础信息.xlsx";
                excelconfig.Title = "员工基础信息";
                excelconfig.IsAllSizeColumn = true;
                int sort = 1;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                {
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "employeescode", ExcelColumn = "员工编码", Alignment = "left" ,Sort= sort ++});
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "nameuser".ToLower(), ExcelColumn = "姓名", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "email2", ExcelColumn = "邮箱", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "postname".ToLower(), ExcelColumn = "岗位", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "departmentname", ExcelColumn = "部门", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "contractcompany", ExcelColumn = "合同劳动签订公司", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "nd", ExcelColumn = "服务项目", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "nd2".ToLower(), ExcelColumn = "职能归属", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "nd3".ToLower(), ExcelColumn = "类别归属", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_rank", ExcelColumn = "职级", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "sextext", ExcelColumn = "性别", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "idcardnumber", ExcelColumn = "身份证号码", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "dirthdate", ExcelColumn = "出生年月", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "age", ExcelColumn = "年龄", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_attacktime", ExcelColumn = "开始工作时间", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_yearsorking", ExcelColumn = "工作年限", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "employrelstatus", ExcelColumn = "员工状态", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_inductiondate", ExcelColumn = "入职日期", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "commander", ExcelColumn = "司龄", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_positivedate", ExcelColumn = "转正日期", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_truedeparturedate", ExcelColumn = "离职日期", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "signingdate", ExcelColumn = "首次合同签订日期", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "contractenddate", ExcelColumn = "首次合同到期日期", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "fristrenewsigningdate", ExcelColumn = "第一次续签合同日期", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "fristrncontractenddate", ExcelColumn = "第一次续签合同到期日期", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "secondrnsigningdate", ExcelColumn = "第二次续签合同日期", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "secondrncontractenddate", ExcelColumn = "第二次续签合同到期日期", Alignment = "left", Sort = sort++ }); 
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "thirdrnsigningdate", ExcelColumn = "第三次续签", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "recordschooling", ExcelColumn = "学历", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "graduatedschool", ExcelColumn = "毕业院校", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "professional", ExcelColumn = "专业", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_title", ExcelColumn = "职称", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "rworkexperience", ExcelColumn = "过往工作经验最近", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "pworkexperience", ExcelColumn = "过往工作经验次近", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "nationalinfo", ExcelColumn = "民族", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "politicallandscape", ExcelColumn = "政治面貌", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "mobilephone", ExcelColumn = "手机号码", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "nativeplace", ExcelColumn = "籍贯", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "idcardaddress", ExcelColumn = "户籍地址", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "registeredresidence", ExcelColumn = "档案所在地", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "homeaddress", ExcelColumn = "家庭地址", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "maritalstatustext", ExcelColumn = "婚姻状况", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "fertilitystatus", ExcelColumn = "生育状况", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "accounttype", ExcelColumn = "户口类型", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "emergencycontact", ExcelColumn = "紧急联系人", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "emergencycontactnumber", ExcelColumn = "紧急联系人电话", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "iswhethercartext", ExcelColumn = "是否自驾车", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "pastperformance3", ExcelColumn = "过往三年绩效3", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "pastperformance2", ExcelColumn = "过往三年绩效2", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "pastperformance1", ExcelColumn = "过往三年绩效1", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "disciplinaryrecord", ExcelColumn = "过往奖惩记录", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "promotionrecord", ExcelColumn = "晋升记录", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "postchangerecord", ExcelColumn = "岗位变动记录", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "contractperiod", ExcelColumn = "当期劳动合同期限", Alignment = "left", Sort = sort++ });
                    excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "cumcontractsnumber", ExcelColumn = "累计签定合同的次数", Alignment = "left", Sort = sort++ });
                }
                excelconfig.ColumnEntity.ForEach(i => i.Column.ToLower());
                //调用导出方法
                //ExcelHelper.ExcelDownload(exportTable, excelconfig, true, headerMultis);
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
    }
}