﻿using Coldairarrow.Entity.Plan_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System;
using Coldairarrow.Util.DataAccess;

namespace Coldairarrow.Business.Plan_Manage
{
    public class TargetStatisticNoSalesBusiness : BaseBusiness<TargetStatisticNoSales>, ITargetStatisticNoSalesBusiness, ITransientDependency
    {
        public TargetStatisticNoSalesBusiness(IERPDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<TargetStatisticNoSales>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<TargetStatisticNoSales>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<TargetStatisticNoSales, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<TargetStatisticNoSales> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(TargetStatisticNoSales data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(TargetStatisticNoSales data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<TargetStatisticNoSales>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<TargetStatisticNoSales, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<A01_TargetStatisticNoSales, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 运营驾驶舱数据
        public async Task<TargetStatisticNoSalesDTO> GetTargetStatisticNoSales()
        {
            TargetStatisticNoSalesDTO targetStatistic = new TargetStatisticNoSalesDTO();
            //获取各项目得数据
            targetStatistic.projectDatas = await this.Db.GetListBySqlAsync<TargetStatisticNoSales>(@$"SELECT TOP 3 *
  FROM [dbo].A01_TargetStatisticNoSales
WHERE ID IN (
 SELECT * FROM ( SELECT TOP 1 ID 
  FROM [dbo].A01_TargetStatisticNoSales WHERE TeamProjGUID =N'8baf9a96-1647-43cd-9bcd-067448bd10c9' ORDER BY ID DESC) t1
  UNION ALL 
 SELECT * FROM  ( SELECT TOP 1 ID 
  FROM [dbo].A01_TargetStatisticNoSales WHERE TeamProjGUID =N'1980EA50-A241-4096-8057-18A97E605DA9' ORDER BY ID DESC) t2
    UNION ALL
  SELECT * FROM  (  SELECT TOP 1 ID 
  FROM [dbo].A01_TargetStatisticNoSales WHERE TeamProjGUID =N'DACC9453-86DA-4D09-BA92-621B0EC33967' ORDER BY ID DESC) t3)");
            //获取全项目得数据
            targetStatistic.allProjectData = (await this.Db.GetListBySqlAsync<TargetStatisticNoSales>(@$"
 SELECT top 1 * FROM [dbo].A01_TargetStatisticNoSales
WHERE TeamProject is null or  TeamProject =''
and CreateDate=(select MAX(CreateDate) from [dbo].A01_TargetStatisticNoSales 
WHERE 1=1
 and (
TeamProject is null or  TeamProject =''))
order  by Id desc")).FirstOrDefault();
            return targetStatistic;
        }

        #endregion

        #region 私有成员

        #endregion
    }
}