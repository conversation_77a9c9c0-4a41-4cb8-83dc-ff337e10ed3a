<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Authorization</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Authorization.AllowAnonymousAttribute">
            <summary>
            Specifies that the class or method that this attribute is applied to does not require authorization.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.Infrastructure.AssertionRequirement">
            <summary>
            Implements an <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationHandler"/> and <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement"/>
            that takes a user specified assertion.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.Infrastructure.AssertionRequirement.Handler">
            <summary>
            Function that is called to handle this requirement.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Infrastructure.AssertionRequirement.#ctor(System.Func{Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,System.Boolean})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Authorization.Infrastructure.AssertionRequirement"/>.
            </summary>
            <param name="handler">Function that is called to handle this requirement.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Infrastructure.AssertionRequirement.#ctor(System.Func{Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,System.Threading.Tasks.Task{System.Boolean}})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Authorization.Infrastructure.AssertionRequirement"/>.
            </summary>
            <param name="handler">Function that is called to handle this requirement.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Infrastructure.AssertionRequirement.HandleAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext)">
            <summary>
            Calls <see cref="P:Microsoft.AspNetCore.Authorization.Infrastructure.AssertionRequirement.Handler"/> to see if authorization is allowed.
            </summary>
            <param name="context">The authorization information.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.Infrastructure.ClaimsAuthorizationRequirement">
            <summary>
            Implements an <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationHandler"/> and <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement"/>
            which requires at least one instance of the specified claim type, and, if allowed values are specified, 
            the claim value must be any of the allowed values.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Infrastructure.ClaimsAuthorizationRequirement.#ctor(System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Authorization.Infrastructure.ClaimsAuthorizationRequirement"/>.
            </summary>
            <param name="claimType">The claim type that must be present.</param>
            <param name="allowedValues">The optional list of claim values, which, if present, 
            the claim must match.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.Infrastructure.ClaimsAuthorizationRequirement.ClaimType">
            <summary>
            Gets the claim type that must be present.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.Infrastructure.ClaimsAuthorizationRequirement.AllowedValues">
            <summary>
            Gets the optional list of claim values, which, if present, 
            the claim must match.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Infrastructure.ClaimsAuthorizationRequirement.HandleRequirementAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,Microsoft.AspNetCore.Authorization.Infrastructure.ClaimsAuthorizationRequirement)">
            <summary>
            Makes a decision if authorization is allowed based on the claims requirements specified.
            </summary>
            <param name="context">The authorization context.</param>
            <param name="requirement">The requirement to evaluate.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.Infrastructure.DenyAnonymousAuthorizationRequirement">
            <summary>
            Implements an <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationHandler"/> and <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement"/>
            which requires the current user must be authenticated.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Infrastructure.DenyAnonymousAuthorizationRequirement.HandleRequirementAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,Microsoft.AspNetCore.Authorization.Infrastructure.DenyAnonymousAuthorizationRequirement)">
            <summary>
            Makes a decision if authorization is allowed based on a specific requirement.
            </summary>
            <param name="context">The authorization context.</param>
            <param name="requirement">The requirement to evaluate.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.Infrastructure.NameAuthorizationRequirement">
            <summary>
            Implements an <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationHandler"/> and <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement"/>
            which requires the current user name must match the specified value.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Infrastructure.NameAuthorizationRequirement.#ctor(System.String)">
            <summary>
            Constructs a new instance of <see cref="T:Microsoft.AspNetCore.Authorization.Infrastructure.NameAuthorizationRequirement"/>.
            </summary>
            <param name="requiredName">The required name that the current user must have.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.Infrastructure.NameAuthorizationRequirement.RequiredName">
            <summary>
            Gets the required name that the current user must have.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Infrastructure.NameAuthorizationRequirement.HandleRequirementAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,Microsoft.AspNetCore.Authorization.Infrastructure.NameAuthorizationRequirement)">
            <summary>
            Makes a decision if authorization is allowed based on a specific requirement.
            </summary>
            <param name="context">The authorization context.</param>
            <param name="requirement">The requirement to evaluate.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.Infrastructure.OperationAuthorizationRequirement">
            <summary>
            A helper class to provide a useful <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement"/> which
            contains a name.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.Infrastructure.OperationAuthorizationRequirement.Name">
            <summary>
            The name of this instance of <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.Infrastructure.PassThroughAuthorizationHandler">
            <summary>
            Infrastructure class which allows an <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement"/> to
            be its own <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationHandler"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Infrastructure.PassThroughAuthorizationHandler.HandleAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext)">
            <summary>
            Makes a decision if authorization is allowed.
            </summary>
            <param name="context">The authorization context.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.Infrastructure.RolesAuthorizationRequirement">
            <summary>
            Implements an <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationHandler"/> and <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement"/>
            which requires at least one role claim whose value must be any of the allowed roles.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Infrastructure.RolesAuthorizationRequirement.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Authorization.Infrastructure.RolesAuthorizationRequirement"/>.
            </summary>
            <param name="allowedRoles">A collection of allowed roles.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.Infrastructure.RolesAuthorizationRequirement.AllowedRoles">
            <summary>
            Gets the collection of allowed roles.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Infrastructure.RolesAuthorizationRequirement.HandleRequirementAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,Microsoft.AspNetCore.Authorization.Infrastructure.RolesAuthorizationRequirement)">
            <summary>
            Makes a decision if authorization is allowed based on a specific requirement.
            </summary>
            <param name="context">The authorization context.</param>
            <param name="requirement">The requirement to evaluate.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.AuthorizationFailure">
            <summary>
            Encapsulates a failure result of <see cref="M:Microsoft.AspNetCore.Authorization.IAuthorizationService.AuthorizeAsync(System.Security.Claims.ClaimsPrincipal,System.Object,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Authorization.IAuthorizationRequirement})"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationFailure.FailCalled">
            <summary>
            Failure was due to <see cref="M:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.Fail"/> being called.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationFailure.FailedRequirements">
            <summary>
            Failure was due to these requirements not being met via <see cref="M:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.Succeed(Microsoft.AspNetCore.Authorization.IAuthorizationRequirement)"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationFailure.ExplicitFail">
            <summary>
            Return a failure due to <see cref="M:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.Fail"/> being called.
            </summary>
            <returns>The failure.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationFailure.Failed(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Authorization.IAuthorizationRequirement})">
            <summary>
            Return a failure due to some requirements not being met via <see cref="M:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.Succeed(Microsoft.AspNetCore.Authorization.IAuthorizationRequirement)"/>.
            </summary>
            <param name="failed">The requirements that were not met.</param>
            <returns>The failure.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.AuthorizationHandler`1">
            <summary>
            Base class for authorization handlers that need to be called for a specific requirement type.
            </summary>
            <typeparam name="TRequirement">The type of the requirement to handle.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationHandler`1.HandleAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext)">
            <summary>
            Makes a decision if authorization is allowed.
            </summary>
            <param name="context">The authorization context.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationHandler`1.HandleRequirementAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,`0)">
            <summary>
            Makes a decision if authorization is allowed based on a specific requirement.
            </summary>
            <param name="context">The authorization context.</param>
            <param name="requirement">The requirement to evaluate.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.AuthorizationHandler`2">
            <summary>
            Base class for authorization handlers that need to be called for specific requirement and
            resource types.
            </summary>
            <typeparam name="TRequirement">The type of the requirement to evaluate.</typeparam>
            <typeparam name="TResource">The type of the resource to evaluate.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationHandler`2.HandleAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext)">
            <summary>
            Makes a decision if authorization is allowed.
            </summary>
            <param name="context">The authorization context.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationHandler`2.HandleRequirementAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,`0,`1)">
            <summary>
            Makes a decision if authorization is allowed based on a specific requirement and resource.
            </summary>
            <param name="context">The authorization context.</param>
            <param name="requirement">The requirement to evaluate.</param>
            <param name="resource">The resource to evaluate.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext">
            <summary>
            Contains authorization information used by <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationHandler"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.#ctor(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Authorization.IAuthorizationRequirement},System.Security.Claims.ClaimsPrincipal,System.Object)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext"/>.
            </summary>
            <param name="requirements">A collection of all the <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement"/> for the current authorization action.</param>
            <param name="user">A <see cref="T:System.Security.Claims.ClaimsPrincipal"/> representing the current user.</param>
            <param name="resource">An optional resource to evaluate the <paramref name="requirements"/> against.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.Requirements">
            <summary>
            The collection of all the <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement"/> for the current authorization action.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.User">
            <summary>
            The <see cref="T:System.Security.Claims.ClaimsPrincipal"/> representing the current user.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.Resource">
            <summary>
            The optional resource to evaluate the <see cref="P:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.Requirements"/> against.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.PendingRequirements">
            <summary>
            Gets the requirements that have not yet been marked as succeeded.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.HasFailed">
            <summary>
            Flag indicating whether the current authorization processing has failed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.HasSucceeded">
            <summary>
            Flag indicating whether the current authorization processing has succeeded.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.Fail">
            <summary>
            Called to indicate <see cref="P:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.HasSucceeded"/> will
            never return true, even if all requirements are met.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext.Succeed(Microsoft.AspNetCore.Authorization.IAuthorizationRequirement)">
            <summary>
            Called to mark the specified <paramref name="requirement"/> as being
            successfully evaluated.
            </summary>
            <param name="requirement">The requirement whose evaluation has succeeded.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.AuthorizationOptions">
            <summary>
            Provides programmatic configuration used by <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationService"/> and <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationPolicyProvider"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationOptions.InvokeHandlersAfterFailure">
            <summary>
            Determines whether authentication handlers should be invoked after a failure.
            Defaults to true.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationOptions.DefaultPolicy">
            <summary>
            Gets or sets the default authorization policy. Defaults to require authenticated users.
            </summary>
            <remarks>
            The default policy used when evaluating <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizeData"/> with no policy name specified.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationOptions.FallbackPolicy">
            <summary>
            Gets or sets the fallback authorization policy used by <see cref="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicy.CombineAsync(Microsoft.AspNetCore.Authorization.IAuthorizationPolicyProvider,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Authorization.IAuthorizeData})"/>
            when no IAuthorizeData have been provided. As a result, the AuthorizationMiddleware uses the fallback policy
            if there are no <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizeData"/> instances for a resource. If a resource has any <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizeData"/>
            then they are evaluated instead of the fallback policy. By default the fallback policy is null, and usually will have no 
            effect unless you have the AuthorizationMiddleware in your pipeline. It is not used in any way by the 
            default <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationService"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationOptions.AddPolicy(System.String,Microsoft.AspNetCore.Authorization.AuthorizationPolicy)">
            <summary>
            Add an authorization policy with the provided name.
            </summary>
            <param name="name">The name of the policy.</param>
            <param name="policy">The authorization policy.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationOptions.AddPolicy(System.String,System.Action{Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder})">
            <summary>
            Add a policy that is built from a delegate with the provided name.
            </summary>
            <param name="name">The name of the policy.</param>
            <param name="configurePolicy">The delegate that will be used to build the policy.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationOptions.GetPolicy(System.String)">
            <summary>
            Returns the policy for the specified name, or null if a policy with the name does not exist.
            </summary>
            <param name="name">The name of the policy to return.</param>
            <returns>The policy for the specified name, or null if a policy with the name does not exist.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy">
            <summary>
            Represents a collection of authorization requirements and the scheme or 
            schemes they are evaluated against, all of which must succeed
            for authorization to succeed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicy.#ctor(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Authorization.IAuthorizationRequirement},System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/>.
            </summary>
            <param name="requirements">
            The list of <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement"/>s which must succeed for
            this policy to be successful.
            </param>
            <param name="authenticationSchemes">
            The authentication schemes the <paramref name="requirements"/> are evaluated against.
            </param>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationPolicy.Requirements">
            <summary>
            Gets a readonly list of <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement"/>s which must succeed for
            this policy to be successful.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationPolicy.AuthenticationSchemes">
            <summary>
            Gets a readonly list of the authentication schemes the <see cref="P:Microsoft.AspNetCore.Authorization.AuthorizationPolicy.Requirements"/> 
            are evaluated against.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicy.Combine(Microsoft.AspNetCore.Authorization.AuthorizationPolicy[])">
            <summary>
            Combines the specified <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> into a single policy.
            </summary>
            <param name="policies">The authorization policies to combine.</param>
            <returns>
            A new <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> which represents the combination of the
            specified <paramref name="policies"/>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicy.Combine(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Authorization.AuthorizationPolicy})">
            <summary>
            Combines the specified <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> into a single policy.
            </summary>
            <param name="policies">The authorization policies to combine.</param>
            <returns>
            A new <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> which represents the combination of the
            specified <paramref name="policies"/>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicy.CombineAsync(Microsoft.AspNetCore.Authorization.IAuthorizationPolicyProvider,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Authorization.IAuthorizeData})">
            <summary>
            Combines the <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> provided by the specified
            <paramref name="policyProvider"/>.
            </summary>
            <param name="policyProvider">A <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationPolicyProvider"/> which provides the policies to combine.</param>
            <param name="authorizeData">A collection of authorization data used to apply authorization to a resource.</param>
            <returns>
            A new <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> which represents the combination of the
            authorization policies provided by the specified <paramref name="policyProvider"/>.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder">
            <summary>
            Used for building policies during application startup.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.#ctor(System.String[])">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder"/>
            </summary>
            <param name="authenticationSchemes">An array of authentication schemes the policy should be evaluated against.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.#ctor(Microsoft.AspNetCore.Authorization.AuthorizationPolicy)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder"/>.
            </summary>
            <param name="policy">The <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> to build.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.Requirements">
            <summary>
            Gets or sets a list of <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement"/>s which must succeed for
            this policy to be successful.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.AuthenticationSchemes">
            <summary>
            Gets or sets a list authentication schemes the <see cref="P:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.Requirements"/> 
            are evaluated against.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.AddAuthenticationSchemes(System.String[])">
            <summary>
            Adds the specified authentication <paramref name="schemes"/> to the
            <see cref="P:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.AuthenticationSchemes"/> for this instance.
            </summary>
            <param name="schemes">The schemes to add.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.AddRequirements(Microsoft.AspNetCore.Authorization.IAuthorizationRequirement[])">
            <summary>
            Adds the specified <paramref name="requirements"/> to the
            <see cref="P:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.Requirements"/> for this instance.
            </summary>
            <param name="requirements">The authorization requirements to add.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.Combine(Microsoft.AspNetCore.Authorization.AuthorizationPolicy)">
            <summary>
            Combines the specified <paramref name="policy"/> into the current instance.
            </summary>
            <param name="policy">The <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> to combine.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.RequireClaim(System.String,System.String[])">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Authorization.Infrastructure.ClaimsAuthorizationRequirement"/>
            to the current instance.
            </summary>
            <param name="claimType">The claim type required.</param>
            <param name="allowedValues">Values the claim must process one or more of for evaluation to succeed.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.RequireClaim(System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Authorization.Infrastructure.ClaimsAuthorizationRequirement"/>
            to the current instance.
            </summary>
            <param name="claimType">The claim type required.</param>
            <param name="allowedValues">Values the claim must process one or more of for evaluation to succeed.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.RequireClaim(System.String)">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Authorization.Infrastructure.ClaimsAuthorizationRequirement"/>
            to the current instance.
            </summary>
            <param name="claimType">The claim type required, which no restrictions on claim value.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.RequireRole(System.String[])">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Authorization.Infrastructure.RolesAuthorizationRequirement"/>
            to the current instance.
            </summary>
            <param name="roles">The allowed roles.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.RequireRole(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Authorization.Infrastructure.RolesAuthorizationRequirement"/>
            to the current instance.
            </summary>
            <param name="roles">The allowed roles.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.RequireUserName(System.String)">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Authorization.Infrastructure.NameAuthorizationRequirement"/>
            to the current instance.
            </summary>
            <param name="userName">The user name the current user must possess.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.RequireAuthenticatedUser">
            <summary>
            Adds a <see cref="T:Microsoft.AspNetCore.Authorization.Infrastructure.DenyAnonymousAuthorizationRequirement"/> to the current instance.
            </summary>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.RequireAssertion(System.Func{Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,System.Boolean})">
            <summary>
            Adds an <see cref="T:Microsoft.AspNetCore.Authorization.Infrastructure.AssertionRequirement"/> to the current instance.
            </summary>
            <param name="handler">The handler to evaluate during authorization.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.RequireAssertion(System.Func{Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,System.Threading.Tasks.Task{System.Boolean}})">
            <summary>
            Adds an <see cref="T:Microsoft.AspNetCore.Authorization.Infrastructure.AssertionRequirement"/> to the current instance.
            </summary>
            <param name="handler">The handler to evaluate during authorization.</param>
            <returns>A reference to this instance after the operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder.Build">
            <summary>
            Builds a new <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> from the requirements 
            in this instance.
            </summary>
            <returns>
            A new <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> built from the requirements in this instance.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.AuthorizationResult">
            <summary>
            Encapsulates the result of <see cref="M:Microsoft.AspNetCore.Authorization.IAuthorizationService.AuthorizeAsync(System.Security.Claims.ClaimsPrincipal,System.Object,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Authorization.IAuthorizationRequirement})"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationResult.Succeeded">
            <summary>
            True if authorization was successful.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizationResult.Failure">
            <summary>
            Contains information about why authorization failed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationResult.Success">
            <summary>
            Returns a successful result.
            </summary>
            <returns>A successful result.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.AuthorizationServiceExtensions">
            <summary>
            Extension methods for <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationService"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationServiceExtensions.AuthorizeAsync(Microsoft.AspNetCore.Authorization.IAuthorizationService,System.Security.Claims.ClaimsPrincipal,System.Object,Microsoft.AspNetCore.Authorization.IAuthorizationRequirement)">
            <summary>
            Checks if a user meets a specific requirement for the specified resource
            </summary>
            <param name="service">The <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationService"/> providing authorization.</param>
            <param name="user">The user to evaluate the policy against.</param>
            <param name="resource">The resource to evaluate the policy against.</param>
            <param name="requirement">The requirement to evaluate the policy against.</param>
            <returns>
            A flag indicating whether requirement evaluation has succeeded or failed.
            This value is <value>true</value> when the user fulfills the policy, otherwise <value>false</value>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationServiceExtensions.AuthorizeAsync(Microsoft.AspNetCore.Authorization.IAuthorizationService,System.Security.Claims.ClaimsPrincipal,System.Object,Microsoft.AspNetCore.Authorization.AuthorizationPolicy)">
            <summary>
            Checks if a user meets a specific authorization policy against the specified resource.
            </summary>
            <param name="service">The <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationService"/> providing authorization.</param>
            <param name="user">The user to evaluate the policy against.</param>
            <param name="resource">The resource to evaluate the policy against.</param>
            <param name="policy">The policy to evaluate.</param>
            <returns>
            A flag indicating whether policy evaluation has succeeded or failed.
            This value is <value>true</value> when the user fulfills the policy, otherwise <value>false</value>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationServiceExtensions.AuthorizeAsync(Microsoft.AspNetCore.Authorization.IAuthorizationService,System.Security.Claims.ClaimsPrincipal,Microsoft.AspNetCore.Authorization.AuthorizationPolicy)">
            <summary>
            Checks if a user meets a specific authorization policy against the specified resource.
            </summary>
            <param name="service">The <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationService"/> providing authorization.</param>
            <param name="user">The user to evaluate the policy against.</param>
            <param name="policy">The policy to evaluate.</param>
            <returns>
            A flag indicating whether policy evaluation has succeeded or failed.
            This value is <value>true</value> when the user fulfills the policy, otherwise <value>false</value>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationServiceExtensions.AuthorizeAsync(Microsoft.AspNetCore.Authorization.IAuthorizationService,System.Security.Claims.ClaimsPrincipal,System.String)">
            <summary>
            Checks if a user meets a specific authorization policy against the specified resource.
            </summary>
            <param name="service">The <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationService"/> providing authorization.</param>
            <param name="user">The user to evaluate the policy against.</param>
            <param name="policyName">The name of the policy to evaluate.</param>
            <returns>
            A flag indicating whether policy evaluation has succeeded or failed.
            This value is <value>true</value> when the user fulfills the policy, otherwise <value>false</value>.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.AuthorizeAttribute">
            <summary>
            Specifies that the class or method that this attribute is applied to requires the specified authorization.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizeAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizeAttribute"/> class. 
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizeAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizeAttribute"/> class with the specified policy. 
            </summary>
            <param name="policy">The name of the policy to require for authorization.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizeAttribute.Policy">
            <summary>
            Gets or sets the policy name that determines access to the resource.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizeAttribute.Roles">
            <summary>
            Gets or sets a comma delimited list of roles that are allowed to access the resource.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.AuthorizeAttribute.AuthenticationSchemes">
            <summary>
            Gets or sets a comma delimited list of schemes from which user information is constructed.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.DefaultAuthorizationEvaluator">
            <summary>
            Determines whether an authorization request was successful or not.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.DefaultAuthorizationEvaluator.Evaluate(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext)">
            <summary>
            Determines whether the authorization result was successful or not.
            </summary>
            <param name="context">The authorization information.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationResult"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.DefaultAuthorizationHandlerContextFactory">
            <summary>
            A type used to provide a <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext"/> used for authorization.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.DefaultAuthorizationHandlerContextFactory.CreateContext(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Authorization.IAuthorizationRequirement},System.Security.Claims.ClaimsPrincipal,System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext"/> used for authorization.
            </summary>
            <param name="requirements">The requirements to evaluate.</param>
            <param name="user">The user to evaluate the requirements against.</param>
            <param name="resource">
            An optional resource the policy should be checked with.
            If a resource is not required for policy evaluation you may pass null as the value.
            </param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.DefaultAuthorizationHandlerProvider">
            <summary>
            The default implementation of a handler provider,
            which provides the <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationHandler"/>s for an authorization request.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.DefaultAuthorizationHandlerProvider.#ctor(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Authorization.IAuthorizationHandler})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Authorization.DefaultAuthorizationHandlerProvider"/>.
            </summary>
            <param name="handlers">The <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationHandler"/>s.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.DefaultAuthorizationPolicyProvider">
            <summary>
            The default implementation of a policy provider,
            which provides a <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> for a particular name.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.DefaultAuthorizationPolicyProvider.#ctor(Microsoft.Extensions.Options.IOptions{Microsoft.AspNetCore.Authorization.AuthorizationOptions})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Authorization.DefaultAuthorizationPolicyProvider"/>.
            </summary>
            <param name="options">The options used to configure this instance.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.DefaultAuthorizationPolicyProvider.GetDefaultPolicyAsync">
            <summary>
            Gets the default authorization policy.
            </summary>
            <returns>The default authorization policy.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.DefaultAuthorizationPolicyProvider.GetFallbackPolicyAsync">
            <summary>
            Gets the fallback authorization policy.
            </summary>
            <returns>The fallback authorization policy.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.DefaultAuthorizationPolicyProvider.GetPolicyAsync(System.String)">
            <summary>
            Gets a <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> from the given <paramref name="policyName"/>
            </summary>
            <param name="policyName">The policy name to retrieve.</param>
            <returns>The named <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.DefaultAuthorizationService">
            <summary>
            The default implementation of an <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationService"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.DefaultAuthorizationService.#ctor(Microsoft.AspNetCore.Authorization.IAuthorizationPolicyProvider,Microsoft.AspNetCore.Authorization.IAuthorizationHandlerProvider,Microsoft.Extensions.Logging.ILogger{Microsoft.AspNetCore.Authorization.DefaultAuthorizationService},Microsoft.AspNetCore.Authorization.IAuthorizationHandlerContextFactory,Microsoft.AspNetCore.Authorization.IAuthorizationEvaluator,Microsoft.Extensions.Options.IOptions{Microsoft.AspNetCore.Authorization.AuthorizationOptions})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Authorization.DefaultAuthorizationService"/>.
            </summary>
            <param name="policyProvider">The <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationPolicyProvider"/> used to provide policies.</param>
            <param name="handlers">The handlers used to fulfill <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement"/>s.</param>
            <param name="logger">The logger used to log messages, warnings and errors.</param>  
            <param name="contextFactory">The <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationHandlerContextFactory"/> used to create the context to handle the authorization.</param>  
            <param name="evaluator">The <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationEvaluator"/> used to determine if authorization was successful.</param>  
            <param name="options">The <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationOptions"/> used.</param>  
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.DefaultAuthorizationService.AuthorizeAsync(System.Security.Claims.ClaimsPrincipal,System.Object,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Authorization.IAuthorizationRequirement})">
            <summary>
            Checks if a user meets a specific set of requirements for the specified resource.
            </summary>
            <param name="user">The user to evaluate the requirements against.</param>
            <param name="resource">The resource to evaluate the requirements against.</param>
            <param name="requirements">The requirements to evaluate.</param>
            <returns>
            A flag indicating whether authorization has succeeded.
            This value is <value>true</value> when the user fulfills the policy otherwise <value>false</value>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.DefaultAuthorizationService.AuthorizeAsync(System.Security.Claims.ClaimsPrincipal,System.Object,System.String)">
            <summary>
            Checks if a user meets a specific authorization policy.
            </summary>
            <param name="user">The user to check the policy against.</param>
            <param name="resource">The resource the policy should be checked with.</param>
            <param name="policyName">The name of the policy to check against a specific context.</param>
            <returns>
            A flag indicating whether authorization has succeeded.
            This value is <value>true</value> when the user fulfills the policy otherwise <value>false</value>.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.IAuthorizationEvaluator">
            <summary>
            Determines whether an authorization request was successful or not.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.IAuthorizationEvaluator.Evaluate(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext)">
            <summary>
            Determines whether the authorization result was successful or not.
            </summary>
            <param name="context">The authorization information.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationResult"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.IAuthorizationHandler">
            <summary>
            Classes implementing this interface are able to make a decision if authorization is allowed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.IAuthorizationHandler.HandleAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext)">
            <summary>
            Makes a decision if authorization is allowed.
            </summary>
            <param name="context">The authorization information.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.IAuthorizationHandlerContextFactory">
            <summary>
            A type used to provide a <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext"/> used for authorization.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.IAuthorizationHandlerContextFactory.CreateContext(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Authorization.IAuthorizationRequirement},System.Security.Claims.ClaimsPrincipal,System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext"/> used for authorization.
            </summary>
            <param name="requirements">The requirements to evaluate.</param>
            <param name="user">The user to evaluate the requirements against.</param>
            <param name="resource">
            An optional resource the policy should be checked with.
            If a resource is not required for policy evaluation you may pass null as the value.
            </param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.IAuthorizationHandlerProvider">
            <summary>
            A type which can provide the <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationHandler"/>s for an authorization request.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.IAuthorizationHandlerProvider.GetHandlersAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext)">
            <summary>
            Return the handlers that will be called for the authorization request.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext"/>.</param>
            <returns>The list of handlers.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.IAuthorizationPolicyProvider">
            <summary>
            A type which can provide a <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> for a particular name.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.IAuthorizationPolicyProvider.GetPolicyAsync(System.String)">
            <summary>
            Gets a <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> from the given <paramref name="policyName"/>
            </summary>
            <param name="policyName">The policy name to retrieve.</param>
            <returns>The named <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.IAuthorizationPolicyProvider.GetDefaultPolicyAsync">
            <summary>
            Gets the default authorization policy.
            </summary>
            <returns>The default authorization policy.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.IAuthorizationPolicyProvider.GetFallbackPolicyAsync">
            <summary>
            Gets the fallback authorization policy.
            </summary>
            <returns>The fallback authorization policy.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.IAuthorizationRequirement">
            <summary>
            Represents an authorization requirement.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.IAuthorizationService">
            <summary>
            Checks policy based permissions for a user
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.IAuthorizationService.AuthorizeAsync(System.Security.Claims.ClaimsPrincipal,System.Object,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Authorization.IAuthorizationRequirement})">
            <summary>
            Checks if a user meets a specific set of requirements for the specified resource
            </summary>
            <param name="user">The user to evaluate the requirements against.</param>
            <param name="resource">
            An optional resource the policy should be checked with.
            If a resource is not required for policy evaluation you may pass null as the value.
            </param>
            <param name="requirements">The requirements to evaluate.</param>
            <returns>
            A flag indicating whether authorization has succeeded.
            This value is <value>true</value> when the user fulfills the policy; otherwise <value>false</value>.
            </returns>
            <remarks>
            Resource is an optional parameter and may be null. Please ensure that you check it is not 
            null before acting upon it.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.IAuthorizationService.AuthorizeAsync(System.Security.Claims.ClaimsPrincipal,System.Object,System.String)">
            <summary>
            Checks if a user meets a specific authorization policy
            </summary>
            <param name="user">The user to check the policy against.</param>
            <param name="resource">
            An optional resource the policy should be checked with.
            If a resource is not required for policy evaluation you may pass null as the value.
            </param>
            <param name="policyName">The name of the policy to check against a specific context.</param>
            <returns>
            A flag indicating whether authorization has succeeded.
            Returns a flag indicating whether the user, and optional resource has fulfilled the policy.    
            <value>true</value> when the policy has been fulfilled; otherwise <value>false</value>.
            </returns>
            <remarks>
            Resource is an optional parameter and may be null. Please ensure that you check it is not 
            null before acting upon it.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.Resources.Exception_AuthorizationPolicyEmpty">
            <summary>AuthorizationPolicy must have at least one requirement.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.Resources.Exception_AuthorizationPolicyNotFound">
            <summary>The AuthorizationPolicy named: '{0}' was not found.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Resources.FormatException_AuthorizationPolicyNotFound(System.Object)">
            <summary>The AuthorizationPolicy named: '{0}' was not found.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.Resources.Exception_RoleRequirementEmpty">
            <summary>At least one role must be specified.</summary>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.AuthorizationServiceCollectionExtensions">
            <summary>
            Extension methods for setting up authorization services in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.AuthorizationServiceCollectionExtensions.AddAuthorizationCore(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds authorization services to the specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />. 
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.AuthorizationServiceCollectionExtensions.AddAuthorizationCore(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Microsoft.AspNetCore.Authorization.AuthorizationOptions})">
            <summary>
            Adds authorization services to the specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />. 
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
            <param name="configure">An action delegate to configure the provided <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationOptions"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
    </members>
</doc>
