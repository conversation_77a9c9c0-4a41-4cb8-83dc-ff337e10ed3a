﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Coldairarrow.Entity.HR_Manage
{
    /// <summary>
    /// 标签内容操作日志
    /// </summary>
    [Table("HR_ContentOperationLog")]
    public class HR_ContentOperationLog
    {

        /// <summary>
        /// F_Id
        /// </summary>
        [Key, Column(Order = 1)]
        public String F_Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime F_CreateDate { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public String F_CreateUserId { get; set; }

        /// <summary>
        /// 创建人名
        /// </summary>
        public String F_CreateUserName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? F_ModifyDate { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public String F_ModifyUserId { get; set; }

        /// <summary>
        /// 修改人名
        /// </summary>
        public String F_ModifyUserName { get; set; }

        /// <summary>
        /// 业务状态
        /// </summary>
        public Int32? F_BusState { get; set; }

        /// <summary>
        /// 流程状态
        /// </summary>
        public Int32? F_WFState { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public String F_Remark { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        public String F_UserId { get; set; }

        /// <summary>
        /// 关系表ID
        /// </summary>
        public String F_RelationalID { get; set; }

        /// <summary>
        /// 关系表类型
        /// </summary>
        public Int32? F_RelationalType { get; set; }

        /// <summary>
        /// 标签类型
        /// </summary>
        public Int32? F_TagType { get; set; }

        /// <summary>
        /// 标签内容
        /// </summary>
        public String F_TagContent { get; set; }

        /// <summary>
        /// 评价
        /// </summary>
        public String F_Evaluation { get; set; }

    }
}