
--�ж���ʽԱ����ϵ���û�id�Ƿ���ȷ
select 
(
select top 1 [Id] from Base_User u where 1=1 and u.[RealName]=f.NameUser 
  order  by  CreateTime asc
) userid
,BaseUserId,* from [dbo].[HR_FormalEmployees] f
where  1=1 and [EmployRelStatus] not like '%��ְ%'
and BaseUserId <> (
select top 1 [Id] from Base_User u where 1=1 and u.[RealName]=f.NameUser 
  order  by  CreateTime asc
)
and BaseUserId is not null




--����δ���û�id��ֵ
select * from 
 [dbo].[HR_FormalEmployees] f
where  1=1 and [EmployRelStatus] not like '%��ְ%'
and f.BaseUserId is null


update [dbo].[HR_FormalEmployees] set  BaseUserId=(
select top 1 [Id] from Base_User u where 1=1 and u.[RealName]=[dbo].[HR_FormalEmployees].NameUser 
  order  by  CreateTime asc
)
where 1=1 and F_Id in (
select 
f.F_Id from [dbo].[HR_FormalEmployees] f
where  1=1 and [EmployRelStatus] not like '%��ְ%'

and BaseUserId is  null

)

--��ѯ�Ͷ���ͬ��Ա����ְ�Ƿ�ƥ��
  select f.NameUser ����,f.F_InductionDate ��ְ����,f.F_InductionDatestr ��ְ����STR,
  l.ContractEffectDate ��ͬ��Чʱ��,
  CONVERT(VARCHAR(5), l.ContractEffectDate, 101) ContractEffectDatestr,
  f.F_InductionDate ��ְʱ��,l.ContractEffectDate,l.F_WFId ����oAId,l.F_Id HR��������,
  case when  CONVERT(VARCHAR(5), l.ContractEffectDate, 101) = f.F_InductionDatestr then '��ȷ' else '����' end  �Ƿ���ȷ,
  case when f.EmployRelStatusStr='��ְ' then '��' else '��' end  '�Ƿ���ְ'
  from HR_LaborContractInfo  l 
  inner join (
SELECT TOP (1000) [F_Id]
      ,CONVERT(VARCHAR(5), [F_InductionDate], 101) [F_InductionDatestr],
	  [F_InductionDate]
      ,[NameUser],EmployRelStatusStr
  FROM [HRSystem].[dbo].[view_EmployeesBasicInfo]
  where 1=1  and F_InductionDate is not null
  ) f on l.UserId=f.F_Id 
  where 1=1 and l.F_WFId is not null   and CONVERT(VARCHAR(5), l.ContractEffectDate, 101) <> f.F_InductionDatestr


  select *  from HR_LaborContractInfo  l 
  where 1=1 and  CONVERT(VARCHAR(5), l.ContractEffectDate, 101) = CONVERT(VARCHAR(5), l.ContractEndDate, 101)

  update  HR_LaborContractInfo  set ContractEndDate=DATEADD(DAY,-1,ContractEndDate)
  where 1=1 and F_Id in (
   select F_Id  from HR_LaborContractInfo  l 
  where 1=1 and  CONVERT(VARCHAR(5), l.ContractEffectDate, 101) = CONVERT(VARCHAR(5), l.ContractEndDate, 101)
  )



select * from [dbo].[Base_User]
where 1=1 and [RealName] not  in (select NameUser from [dbo].[HR_FormalEmployees] )



--��������ȷ��Ա����ϵid
update [dbo].[HR_FormalEmployees] set  BaseUserId=(
select top 1 [Id] from Base_User u where 1=1 and u.[RealName]=[dbo].[HR_FormalEmployees].NameUser 
  order  by  CreateTime asc
)
where 1=1 and F_Id in (
select 
f.F_Id from [dbo].[HR_FormalEmployees] f
where  1=1 and [EmployRelStatus] not like '%��ְ%'
and BaseUserId <> (
select top 1 [Id] from Base_User u where 1=1 and u.[RealName]=f.NameUser 
  order  by  CreateTime asc
)
and BaseUserId is not null

)

----------------------------------------
--ʳ�ð�
/****** SSMS �� SelectTopNRows ����Ľű�  ******/
SELECT TOP (1000) [Id]
      ,[UnionId]
      ,[Mobile]
      ,[MobileSecurity]
      ,[W_ADNumber]
      ,[HR_UserId]
      ,[HR_FormalId]
      ,[W_Email]
      ,[W_UserType]
      ,[W_Realname]
      ,[W_Gender]
      ,[W_NickName]
      ,[W_City]
      ,[W_Province]
      ,[W_Country]
      ,[W_Icon]
      ,[W_Address]
      ,[W_Language]
      ,[W_IsRegister]
      ,[W_LastLogin]
      ,[W_FirstLogin]
      ,[F_CreateDate]
      ,[F_CreateUserId]
      ,[F_CreateUserName]
      ,[F_ModifyDate]
      ,[F_ModifyUserId]
      ,[F_ModifyUserName]
      ,[HR_DeptId]
  FROM [HRSystem].[dbo].[Base_WechatUser]
  where  1=1 and W_Realname is  not null
  and [W_LastLogin]>='2024-06-01'
  and HR_UserId <> (select top 1 Id from Base_User u where 1=1 and u.RealName=W_Realname order  by u.CreateTime asc)
  order   by  [W_LastLogin] desc

  --��baseԱ����

  update [HRSystem].[dbo].[Base_WechatUser] set HR_UserId=(select top 1 Id from Base_User u where 1=1 and u.RealName=[Base_WechatUser].W_Realname order  by u.CreateTime asc)
  where 1=1 and Id in (
  select Id
    FROM [HRSystem].[dbo].[Base_WechatUser]
  where  1=1 and W_Realname is  not null
  and [W_LastLogin]>='2024-06-01'
  and HR_UserId <> (select top 1 Id from Base_User u where 1=1 and u.RealName=W_Realname order  by u.CreateTime asc)
  )

----------------------------------
SELECT TOP (1000) [Id]
      ,[UnionId]
      ,[Mobile]
      ,[MobileSecurity]
      ,[W_ADNumber]
      ,[HR_UserId]
      ,[HR_FormalId]
      ,[W_Email]
      ,[W_UserType]
      ,[W_Realname]
      ,[W_Gender]
      ,[W_NickName]
      ,[W_City]
      ,[W_Province]
      ,[W_Country]
      ,[W_Icon]
      ,[W_Address]
      ,[W_Language]
      ,[W_IsRegister]
      ,[W_LastLogin]
      ,[W_FirstLogin]
      ,[F_CreateDate]
      ,[F_CreateUserId]
      ,[F_CreateUserName]
      ,[F_ModifyDate]
      ,[F_ModifyUserId]
      ,[F_ModifyUserName]
      ,[HR_DeptId]
  FROM [HRSystem].[dbo].[Base_WechatUser]
  where  1=1 and W_Realname is  not null
  and [W_LastLogin]>='2024-06-01'
  and HR_FormalId <> (select top 1 u.F_Id from [dbo].[HR_FormalEmployees] u where 1=1 and u.NameUser=W_Realname order  by u.F_CreateDate desc)
  order   by  [W_LastLogin] desc


  --����ʽԱ����
  update [HRSystem].[dbo].[Base_WechatUser] set HR_FormalId= (select top 1 u.F_Id from [dbo].[HR_FormalEmployees] u where 1=1 and u.NameUser=W_Realname order  by u.F_CreateDate desc)
  where 1=1 and Id in (
  select Id
  FROM [HRSystem].[dbo].[Base_WechatUser]
  where  1=1 and W_Realname is  not null
  and [W_LastLogin]>='2024-06-01'
  and HR_FormalId <> (select top 1 u.F_Id from [dbo].[HR_FormalEmployees] u where 1=1 and u.NameUser=W_Realname order  by u.F_CreateDate desc)
  )
------------------------------------------------------
--��ѯʳ���ֻ������û��Ƿ�ƥ��
select w.W_Realname,b.RealName FROM [HRSystem].[dbo].[Base_WechatUser] w
inner join [HRSystem].[dbo].Base_User b on w.MobileSecurity=b.Mobile 
  where  1=1 and W_Realname is  not null
  and [W_LastLogin]>='2024-06-01' 

  select w.W_Realname,w.Id ,w.MobileSecurity ,w.HR_UserId,(select top 1 u2.Id from Base_User u2
  where u2.RealName=(select top 1 RealName from Base_User u where 1=1 and u.Mobile=w.MobileSecurity ) order  by u2.CreateTime asc) userid FROM [HRSystem].[dbo].[Base_WechatUser] w
  where  1=1   and [W_LastLogin]>='2024-06-01'  and w.MobileSecurity is not null
  and w.HR_UserId <>(select top 1 u2.Id from Base_User u2
  where u2.RealName=(select top 1 RealName from Base_User u where 1=1 and u.Mobile=w.MobileSecurity ) order  by u2.CreateTime asc )



  select top 1 u2.Id from Base_User u2
  where u2.RealName=(select top 1 RealName from Base_User u where 1=1 and u.Mobile='JZfk8FPmfoUPrhg1IBwD5w==' ) order  by u2.CreateTime asc