﻿using Coldairarrow.Business.Wechat_Go;
using Coldairarrow.Entity.Wechat_Go;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;

namespace Coldairarrow.Api.Controllers.Wechat_Go
{
    [Route("/Wechat_Go/[controller]/[action]")]
    public class Go_GroupUserController : BaseApiController
    {
        #region DI

        public Go_GroupUserController(IGo_GroupUserBusiness go_GroupUserBus)
        {
            _go_GroupUserBus = go_GroupUserBus;
        }

        IGo_GroupUserBusiness _go_GroupUserBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Go_GroupUser>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _go_GroupUserBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Go_GroupUser> GetTheData(IdInputDTO input)
        {
            return await _go_GroupUserBus.GetTheDataAsync(input.id);
        }

        /// <summary>
        /// 加入团队
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult JoinTeam()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var result = _go_GroupUserBus.GetDataByOpenId(openId);
                if (result.IsNullOrEmpty())
                {
                    var data = new Go_GroupUser
                    {
                        F_Id = Guid.NewGuid().ToString("N"),
                        F_CreateTime = DateTime.Now,
                        F_OpenId = openId,
                        F_TeamId = teamId,
                        F_IsAble = 1,
                        F_UserType = 1
                    };
                    _go_GroupUserBus.AddDataAsync(data).Wait();
                    return Success("加入团队成功");
                }
                else
                {
                    return Success("您已加入过团队");
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 查询状态
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetType()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var result = _go_GroupUserBus.GetUserType(openId, teamId);
                return Success(result);

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 改变状态
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult ChangeUserType()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var type = int.Parse(HttpContext.Request.Form["type"].ToString());
                var data = _go_GroupUserBus.GetDataByOpenId(openId);
                if (!data.IsNullOrEmpty())
                {
                    data.F_UserType = type;
                    _go_GroupUserBus.UpdateDataAsync(data).Wait();
                }
                return Success("修改成功");

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 退出团队
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult leftTeam()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var result = _go_GroupUserBus.GetDataByMini(openId, teamId);
                _go_GroupUserBus.DeleteDataAsync(result).Wait();
                return Success("退出团队成功");

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Go_GroupUser data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _go_GroupUserBus.AddDataAsync(data);
            }
            else
            {
                await _go_GroupUserBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _go_GroupUserBus.DeleteDataAsync(ids);
        }

        #endregion
        

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _go_GroupUserBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Go_GroupUser>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _go_GroupUserBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}