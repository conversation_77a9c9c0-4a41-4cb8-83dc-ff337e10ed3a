﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Coldairarrow.Util
{
    /// <summary>
    /// AES加密解密
    /// </summary>
    public class AESHelper
    {
        /// <summary>
        /// Aes加解密钥必须32位
        /// </summary>
        public static string AesKey = "ae125efkk4_54eeff444ferfkny6ox22";

        /// <summary>
        /// 加密
        /// </summary>
        /// <param name="plainSourceStringToEncrypt">a plain, Framework string (ASCII, null terminated)</param>
        /// <param name="passPhrase">The pass phrase.</param>
        /// <returns>
        /// returns an Aes encrypted, BASE64 encoded string
        /// </returns>
        public static string EncryptString(string plainSourceStringToEncrypt, string passPhrase)
        {
            if (!string.IsNullOrWhiteSpace(plainSourceStringToEncrypt))
            {
                try
                {
                    // Set up the encryption objects
                    using (var acsp = GetProvider(Encoding.UTF8.GetBytes(passPhrase)))
                    {
                        byte[] sourceBytes = Encoding.UTF8.GetBytes(plainSourceStringToEncrypt);
                        ICryptoTransform ictE = acsp.CreateEncryptor();

                        // Set up stream to contain the encryption
                        using (var msS = new MemoryStream())
                        {
                            // Perform the encryption, storing output into the stream
                            using (var csS = new CryptoStream(msS, ictE, CryptoStreamMode.Write))
                            {
                                csS.Write(sourceBytes, 0, sourceBytes.Length);
                                csS.FlushFinalBlock();
                            }

                            // Source bytes are now encrypted as an array of secure bytes
                            byte[] encryptedBytes = msS.ToArray();

                            // Return the encrypted bytes as a Base64 encoded string
                            return Convert.ToBase64String(encryptedBytes);
                        }
                    }
                }
                catch (Exception ex)
                {
                    return "";
                }
            }
            return "";
        }


        /// <summary>
        /// 解密
        /// </summary>
        /// <param name="base64StringToDecrypt">an Aes encrypted AND base64 encoded string</param>
        /// <param name="passphrase">The passphrase.</param>
        /// <returns>returns a plain string</returns>
        public static string DecryptString(string base64StringToDecrypt, string passphrase)
        {
            if (!string.IsNullOrWhiteSpace(base64StringToDecrypt))
            {
                try
                {
                    // Set up the encryption objects
                    using (var acsp = GetProvider(Encoding.Default.GetBytes(passphrase)))
                    {
                        byte[] rawBytes = Convert.FromBase64String(base64StringToDecrypt);
                        ICryptoTransform ictD = acsp.CreateDecryptor();

                        // Decrypt into a stream
                        using (var msD = new MemoryStream(rawBytes, 0, rawBytes.Length))
                        using (var csD = new CryptoStream(msD, ictD, CryptoStreamMode.Read))
                        using (var reader = new StreamReader(csD, Encoding.Default, true, 4096)) // Use StreamReader with a buffer size
                        {
                            StringBuilder decryptedStringBuilder = new StringBuilder();

                            string line;
                            while ((line = reader.ReadLine()) != null)
                            {
                                decryptedStringBuilder.Append(line);
                            }

                            return decryptedStringBuilder.ToString();
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Log the error and handle it appropriately
                    // Log.Error(ex, "An error occurred during decryption");
                    return "";
                }
            }
            return "";
        }
        /// <summary>
        /// 解密异步
        /// </summary>
        /// <param name="base64StringToDecrypt"></param>
        /// <param name="passphrase"></param>
        /// <returns></returns>
        public static async Task<string> DecryptStringAsync(string base64StringToDecrypt, string passphrase)
        {
            if (!string.IsNullOrWhiteSpace(base64StringToDecrypt))
            {
                try
                {
                    // Set up the encryption objects
                    using (var acsp = GetProvider(Encoding.Default.GetBytes(passphrase)))
                    {
                        byte[] rawBytes = Convert.FromBase64String(base64StringToDecrypt);
                        ICryptoTransform ictD = acsp.CreateDecryptor();

                        // Decrypt into a stream
                        using (var msD = new MemoryStream(rawBytes, 0, rawBytes.Length))
                        using (var csD = new CryptoStream(msD, ictD, CryptoStreamMode.Read))
                        using (var reader = new StreamReader(csD, Encoding.Default, true, 4096)) // Use StreamReader with a buffer size
                        {
                            StringBuilder decryptedStringBuilder = new StringBuilder();

                            string line;
                            while ((line = await reader.ReadLineAsync()) != null)
                            {
                                decryptedStringBuilder.Append(line);
                            }

                            return decryptedStringBuilder.ToString();
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Log the error and handle it appropriately
                    // Log.Error(ex, "An error occurred during decryption");
                    return "";
                }
            }
            return "";
        }
        private static AesCryptoServiceProvider GetProvider(byte[] key)
        {
            AesCryptoServiceProvider result = new AesCryptoServiceProvider();
            result.BlockSize = 128;
            result.KeySize = 128;
            result.Mode = CipherMode.CBC;
            result.Padding = PaddingMode.PKCS7;

            result.GenerateIV();
            result.IV = Convert.FromBase64String("Rkb4jvUy/ye7Cd7k89QQgQ==");

            byte[] RealKey = key;
            result.Key = RealKey;
            // result.IV = RealKey;
            return result;
        }

        private static byte[] GetKey(byte[] suggestedKey, SymmetricAlgorithm p)
        {
            byte[] kRaw = suggestedKey;
            List<byte> kList = new List<byte>();

            for (int i = 0; i < p.LegalKeySizes[0].MinSize; i += 8)
            {
                kList.Add(kRaw[(i / 8) % kRaw.Length]);
            }
            byte[] k = kList.ToArray();
            return k;
        }
    }
}
