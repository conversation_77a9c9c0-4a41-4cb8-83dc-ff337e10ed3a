﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Primitives</name>
  </assembly>
  <members>
    <member name="T:System.Net.AuthenticationSchemes">
      <summary>Specifies protocols for authentication.</summary>
    </member>
    <member name="F:System.Net.AuthenticationSchemes.Anonymous">
      <summary>Specifies anonymous authentication.</summary>
    </member>
    <member name="F:System.Net.AuthenticationSchemes.Basic">
      <summary>Specifies basic authentication.</summary>
    </member>
    <member name="F:System.Net.AuthenticationSchemes.Digest">
      <summary>Specifies digest authentication.</summary>
    </member>
    <member name="F:System.Net.AuthenticationSchemes.IntegratedWindowsAuthentication">
      <summary>Specifies Windows authentication.</summary>
    </member>
    <member name="F:System.Net.AuthenticationSchemes.Negotiate">
      <summary>Negotiates with the client to determine the authentication scheme. If both client and server support Kerberos, it is used; otherwise, NTLM is used.</summary>
    </member>
    <member name="F:System.Net.AuthenticationSchemes.None">
      <summary>No authentication is allowed. A client requesting an <see cref="T:System.Net.HttpListener" /> object with this flag set will always receive a 403 Forbidden status. Use this flag when a resource should never be served to a client.</summary>
    </member>
    <member name="F:System.Net.AuthenticationSchemes.Ntlm">
      <summary>Specifies NTLM authentication.</summary>
    </member>
    <member name="T:System.Net.Cache.RequestCacheLevel">
      <summary>Specifies caching behavior for resources obtained using <see cref="T:System.Net.WebRequest" /> and its derived classes.</summary>
    </member>
    <member name="F:System.Net.Cache.RequestCacheLevel.BypassCache">
      <summary>Satisfies a request by using the server. No entries are taken from caches, added to caches, or removed from caches between the client and server. This is the default cache behavior specified in the machine configuration file that ships with the .NET Framework.</summary>
    </member>
    <member name="F:System.Net.Cache.RequestCacheLevel.CacheIfAvailable">
      <summary>Satisfies a request for a resource from the cache, if the resource is available; otherwise, sends a request for a resource to the server. If the requested item is available in any cache between the client and the server, the request might be satisfied by the intermediate cache.</summary>
    </member>
    <member name="F:System.Net.Cache.RequestCacheLevel.CacheOnly">
      <summary>Satisfies a request using the locally cached resource; does not send a request for an item that is not in the cache. When this cache policy level is specified, a <see cref="T:System.Net.WebException" /> exception is thrown if the item is not in the client cache.</summary>
    </member>
    <member name="F:System.Net.Cache.RequestCacheLevel.Default">
      <summary>Satisfies a request for a resource either by using the cached copy of the resource or by sending a request for the resource to the server. The action taken is determined by the current cache policy and the age of the content in the cache. This is the cache level that should be used by most applications.</summary>
    </member>
    <member name="F:System.Net.Cache.RequestCacheLevel.NoCacheNoStore">
      <summary>Never satisfies a request by using resources from the cache and does not cache resources. If the resource is present in the local cache, it is removed. This policy level indicates to intermediate caches that they should remove the resource. In the HTTP caching protocol, this is achieved using the <see langword="no-cache" /> cache control directive.</summary>
    </member>
    <member name="F:System.Net.Cache.RequestCacheLevel.Reload">
      <summary>Satisfies a request by using the server. The response might be saved in the cache. In the HTTP caching protocol, this is achieved using the <see langword="no-cache" /> cache control directive and the no-cache <see langword="Pragma" /> header.</summary>
    </member>
    <member name="F:System.Net.Cache.RequestCacheLevel.Revalidate">
      <summary>Satisfies a request by using the cached copy of the resource if the timestamp is the same as the timestamp of the resource on the server; otherwise, the resource is downloaded from the server, presented to the caller, and stored in the cache.</summary>
    </member>
    <member name="T:System.Net.Cache.RequestCachePolicy">
      <summary>Defines an application's caching requirements for resources obtained by using <see cref="T:System.Net.WebRequest" /> objects.</summary>
    </member>
    <member name="M:System.Net.Cache.RequestCachePolicy.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Cache.RequestCachePolicy" /> class.</summary>
    </member>
    <member name="M:System.Net.Cache.RequestCachePolicy.#ctor(System.Net.Cache.RequestCacheLevel)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Cache.RequestCachePolicy" /> class. using the specified cache policy.</summary>
      <param name="level">A <see cref="T:System.Net.Cache.RequestCacheLevel" /> that specifies the cache behavior for resources obtained using <see cref="T:System.Net.WebRequest" /> objects.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">level is not a valid <see cref="T:System.Net.Cache.RequestCacheLevel" />.value.</exception>
    </member>
    <member name="P:System.Net.Cache.RequestCachePolicy.Level">
      <summary>Gets the <see cref="T:System.Net.Cache.RequestCacheLevel" /> value specified when this instance was constructed.</summary>
      <returns>A <see cref="T:System.Net.Cache.RequestCacheLevel" /> value that specifies the cache behavior for resources obtained using <see cref="T:System.Net.WebRequest" /> objects.</returns>
    </member>
    <member name="M:System.Net.Cache.RequestCachePolicy.ToString">
      <summary>Returns a string representation of this instance.</summary>
      <returns>A <see cref="T:System.String" /> containing the <see cref="P:System.Net.Cache.RequestCachePolicy.Level" /> for this instance.</returns>
    </member>
    <member name="T:System.Net.Cookie">
      <summary>Provides a set of properties and methods that are used to manage cookies. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Net.Cookie.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Cookie" /> class.</summary>
    </member>
    <member name="M:System.Net.Cookie.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Cookie" /> class with a specified <see cref="P:System.Net.Cookie.Name" /> and <see cref="P:System.Net.Cookie.Value" />.</summary>
      <param name="name">The name of a <see cref="T:System.Net.Cookie" />. The following characters must not be used inside <paramref name="name" />: equal sign, semicolon, comma, newline (\n), return (\r), tab (\t), and space character. The dollar sign character ("$") cannot be the first character.</param>
      <param name="value">The value of a <see cref="T:System.Net.Cookie" />. The following characters must not be used inside <paramref name="value" />: semicolon, comma.</param>
      <exception cref="T:System.Net.CookieException">The <paramref name="name" /> parameter is <see langword="null" />.
-or-
The <paramref name="name" /> parameter is of zero length.
-or-
The <paramref name="name" /> parameter contains an invalid character.
-or-
The <paramref name="value" /> parameter is <see langword="null" /> .
-or -
The <paramref name="value" /> parameter contains a string not enclosed in quotes that contains an invalid character.</exception>
    </member>
    <member name="M:System.Net.Cookie.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Cookie" /> class with a specified <see cref="P:System.Net.Cookie.Name" />, <see cref="P:System.Net.Cookie.Value" />, and <see cref="P:System.Net.Cookie.Path" />.</summary>
      <param name="name">The name of a <see cref="T:System.Net.Cookie" />. The following characters must not be used inside <paramref name="name" />: equal sign, semicolon, comma, newline (\n), return (\r), tab (\t), and space character. The dollar sign character ("$") cannot be the first character.</param>
      <param name="value">The value of a <see cref="T:System.Net.Cookie" />. The following characters must not be used inside <paramref name="value" />: semicolon, comma.</param>
      <param name="path">The subset of URIs on the origin server to which this <see cref="T:System.Net.Cookie" /> applies. The default value is "/".</param>
      <exception cref="T:System.Net.CookieException">The <paramref name="name" /> parameter is <see langword="null" />.
-or-
The <paramref name="name" /> parameter is of zero length.
-or-
The <paramref name="name" /> parameter contains an invalid character.
-or-
The <paramref name="value" /> parameter is <see langword="null" /> .
-or -
The <paramref name="value" /> parameter contains a string not enclosed in quotes that contains an invalid character.</exception>
    </member>
    <member name="M:System.Net.Cookie.#ctor(System.String,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Cookie" /> class with a specified <see cref="P:System.Net.Cookie.Name" />, <see cref="P:System.Net.Cookie.Value" />, <see cref="P:System.Net.Cookie.Path" />, and <see cref="P:System.Net.Cookie.Domain" />.</summary>
      <param name="name">The name of a <see cref="T:System.Net.Cookie" />. The following characters must not be used inside <paramref name="name" />: equal sign, semicolon, comma, newline (\n), return (\r), tab (\t), and space character. The dollar sign character ("$") cannot be the first character.</param>
      <param name="value">The value of a <see cref="T:System.Net.Cookie" /> object. The following characters must not be used inside <paramref name="value" />: semicolon, comma.</param>
      <param name="path">The subset of URIs on the origin server to which this <see cref="T:System.Net.Cookie" /> applies. The default value is "/".</param>
      <param name="domain">The optional internet domain for which this <see cref="T:System.Net.Cookie" /> is valid. The default value is the host this <see cref="T:System.Net.Cookie" /> has been received from.</param>
      <exception cref="T:System.Net.CookieException">The <paramref name="name" /> parameter is <see langword="null" />.
-or-
The <paramref name="name" /> parameter is of zero length.
-or-
The <paramref name="name" /> parameter contains an invalid character.
-or-
The <paramref name="value" /> parameter is <see langword="null" /> .
-or -
The <paramref name="value" /> parameter contains a string not enclosed in quotes that contains an invalid character.</exception>
    </member>
    <member name="P:System.Net.Cookie.Comment">
      <summary>Gets or sets a comment that the server can add to a <see cref="T:System.Net.Cookie" />.</summary>
      <returns>An optional comment to document intended usage for this <see cref="T:System.Net.Cookie" />.</returns>
    </member>
    <member name="P:System.Net.Cookie.CommentUri">
      <summary>Gets or sets a URI comment that the server can provide with a <see cref="T:System.Net.Cookie" />.</summary>
      <returns>An optional comment that represents the intended usage of the URI reference for this <see cref="T:System.Net.Cookie" />. The value must conform to URI format.</returns>
    </member>
    <member name="P:System.Net.Cookie.Discard">
      <summary>Gets or sets the discard flag set by the server.</summary>
      <returns>
        <see langword="true" /> if the client is to discard the <see cref="T:System.Net.Cookie" /> at the end of the current session; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Cookie.Domain">
      <summary>Gets or sets the URI for which the <see cref="T:System.Net.Cookie" /> is valid.</summary>
      <returns>The URI for which the <see cref="T:System.Net.Cookie" /> is valid.</returns>
    </member>
    <member name="M:System.Net.Cookie.Equals(System.Object)">
      <summary>Overrides the <see cref="M:System.Object.Equals(System.Object)" /> method.</summary>
      <param name="comparand">A reference to a <see cref="T:System.Net.Cookie" />.</param>
      <returns>Returns <see langword="true" /> if the <see cref="T:System.Net.Cookie" /> is equal to <paramref name="comparand" />. Two <see cref="T:System.Net.Cookie" /> instances are equal if their <see cref="P:System.Net.Cookie.Name" />, <see cref="P:System.Net.Cookie.Value" />, <see cref="P:System.Net.Cookie.Path" />, <see cref="P:System.Net.Cookie.Domain" />, and <see cref="P:System.Net.Cookie.Version" /> properties are equal. <see cref="P:System.Net.Cookie.Name" /> and <see cref="P:System.Net.Cookie.Domain" /> string comparisons are case-insensitive.</returns>
    </member>
    <member name="P:System.Net.Cookie.Expired">
      <summary>Gets or sets the current state of the <see cref="T:System.Net.Cookie" />.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.Cookie" /> has expired; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Cookie.Expires">
      <summary>Gets or sets the expiration date and time for the <see cref="T:System.Net.Cookie" /> as a <see cref="T:System.DateTime" />.</summary>
      <returns>The expiration date and time for the <see cref="T:System.Net.Cookie" /> as a <see cref="T:System.DateTime" /> instance.</returns>
    </member>
    <member name="M:System.Net.Cookie.GetHashCode">
      <summary>Overrides the <see cref="M:System.Object.GetHashCode" /> method.</summary>
      <returns>The 32-bit signed integer hash code for this instance.</returns>
    </member>
    <member name="P:System.Net.Cookie.HttpOnly">
      <summary>Determines whether a page script or other active content can access this cookie.</summary>
      <returns>Boolean value that determines whether a page script or other active content can access this cookie.</returns>
    </member>
    <member name="P:System.Net.Cookie.Name">
      <summary>Gets or sets the name for the <see cref="T:System.Net.Cookie" />.</summary>
      <returns>The name for the <see cref="T:System.Net.Cookie" />.</returns>
      <exception cref="T:System.Net.CookieException">The value specified for a set operation is <see langword="null" /> or the empty string
-or-
The value specified for a set operation contained an illegal character. The following characters must not be used inside the <see cref="P:System.Net.Cookie.Name" /> property: equal sign, semicolon, comma, newline (\n), return (\r), tab (\t), and space character. The dollar sign character ("$") cannot be the first character.</exception>
    </member>
    <member name="P:System.Net.Cookie.Path">
      <summary>Gets or sets the URIs to which the <see cref="T:System.Net.Cookie" /> applies.</summary>
      <returns>The URIs to which the <see cref="T:System.Net.Cookie" /> applies.</returns>
    </member>
    <member name="P:System.Net.Cookie.Port">
      <summary>Gets or sets a list of TCP ports that the <see cref="T:System.Net.Cookie" /> applies to.</summary>
      <returns>The list of TCP ports that the <see cref="T:System.Net.Cookie" /> applies to.</returns>
      <exception cref="T:System.Net.CookieException">The value specified for a set operation could not be parsed or is not enclosed in double quotes.</exception>
    </member>
    <member name="P:System.Net.Cookie.Secure">
      <summary>Gets or sets the security level of a <see cref="T:System.Net.Cookie" />.</summary>
      <returns>
        <see langword="true" /> if the client is only to return the cookie in subsequent requests if those requests use Secure Hypertext Transfer Protocol (HTTPS); otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.Cookie.TimeStamp">
      <summary>Gets the time when the cookie was issued as a <see cref="T:System.DateTime" />.</summary>
      <returns>The time when the cookie was issued as a <see cref="T:System.DateTime" />.</returns>
    </member>
    <member name="M:System.Net.Cookie.ToString">
      <summary>Overrides the <see cref="M:System.Object.ToString" /> method.</summary>
      <returns>Returns a string representation of this <see cref="T:System.Net.Cookie" /> object that is suitable for including in a HTTP Cookie: request header.</returns>
    </member>
    <member name="P:System.Net.Cookie.Value">
      <summary>Gets or sets the <see cref="P:System.Net.Cookie.Value" /> for the <see cref="T:System.Net.Cookie" />.</summary>
      <returns>The <see cref="P:System.Net.Cookie.Value" /> for the <see cref="T:System.Net.Cookie" />.</returns>
    </member>
    <member name="P:System.Net.Cookie.Version">
      <summary>Gets or sets the version of HTTP state maintenance to which the cookie conforms.</summary>
      <returns>The version of HTTP state maintenance to which the cookie conforms.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified for a version is not allowed.</exception>
    </member>
    <member name="T:System.Net.CookieCollection">
      <summary>Provides a collection container for instances of the <see cref="T:System.Net.Cookie" /> class.</summary>
    </member>
    <member name="M:System.Net.CookieCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.CookieCollection" /> class.</summary>
    </member>
    <member name="M:System.Net.CookieCollection.Add(System.Net.Cookie)">
      <summary>Adds a <see cref="T:System.Net.Cookie" /> to a <see cref="T:System.Net.CookieCollection" />.</summary>
      <param name="cookie">The <see cref="T:System.Net.Cookie" /> to be added to a <see cref="T:System.Net.CookieCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="cookie" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.CookieCollection.Add(System.Net.CookieCollection)">
      <summary>Adds the contents of a <see cref="T:System.Net.CookieCollection" /> to the current instance.</summary>
      <param name="cookies">The <see cref="T:System.Net.CookieCollection" /> to be added.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="cookies" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.CookieCollection.Clear">
      <summary>Removes all elements from the <see cref="T:System.Net.CookieCollection" /> object.</summary>
    </member>
    <member name="M:System.Net.CookieCollection.Contains(System.Net.Cookie)">
      <summary>Determines whether the specified cookie is in the <see cref="T:System.Net.CookieCollection" />.</summary>
      <param name="cookie">The cookie to locate in the <see cref="T:System.Net.CookieCollection" />.</param>
      <returns>
        <see langword="true" /> if the specified cookie is found in the <see cref="T:System.Net.CookieCollection" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.CookieCollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of a <see cref="T:System.Net.CookieCollection" /> to the specified array, starting at a particular index.</summary>
      <param name="array">The target array to which the <see cref="T:System.Net.CookieCollection" /> will be copied.</param>
      <param name="index">The zero-based index in the target array where copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.
-or-
The number of elements in this <see cref="T:System.Net.CookieCollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
      <exception cref="T:System.InvalidCastException">The elements in this <see cref="T:System.Net.CookieCollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Net.CookieCollection.CopyTo(System.Net.Cookie[],System.Int32)">
      <summary>Copies the elements of this <see cref="T:System.Net.CookieCollection" /> to a <see cref="T:System.Net.Cookie" /> array starting at the specified index of the target array.</summary>
      <param name="array">The target <see cref="T:System.Net.Cookie" /> array to which the <see cref="T:System.Net.CookieCollection" /> will be copied.</param>
      <param name="index">The zero-based index in the target array where copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.
-or-
The number of elements in this <see cref="T:System.Net.CookieCollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
      <exception cref="T:System.InvalidCastException">The elements in this <see cref="T:System.Net.CookieCollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Net.CookieCollection.Count">
      <summary>Gets the number of cookies contained in a <see cref="T:System.Net.CookieCollection" />.</summary>
      <returns>The number of cookies contained in a <see cref="T:System.Net.CookieCollection" />.</returns>
    </member>
    <member name="M:System.Net.CookieCollection.GetEnumerator">
      <summary>Gets an enumerator that can iterate through a <see cref="T:System.Net.CookieCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for this collection.</returns>
    </member>
    <member name="P:System.Net.CookieCollection.IsReadOnly">
      <summary>Gets a value that indicates whether a <see cref="T:System.Net.CookieCollection" /> is read-only.</summary>
      <returns>
        <see langword="true" /> if this is a read-only <see cref="T:System.Net.CookieCollection" />; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Net.CookieCollection.IsSynchronized">
      <summary>Gets a value that indicates whether access to a <see cref="T:System.Net.CookieCollection" /> is thread safe.</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Net.CookieCollection" /> is thread safe; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.CookieCollection.Item(System.Int32)">
      <summary>Gets the <see cref="T:System.Net.Cookie" /> with a specific index from a <see cref="T:System.Net.CookieCollection" />.</summary>
      <param name="index">The zero-based index of the <see cref="T:System.Net.Cookie" /> to be found.</param>
      <returns>A <see cref="T:System.Net.Cookie" /> with a specific index from a <see cref="T:System.Net.CookieCollection" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0 or <paramref name="index" /> is greater than or equal to <see cref="P:System.Net.CookieCollection.Count" />.</exception>
    </member>
    <member name="P:System.Net.CookieCollection.Item(System.String)">
      <summary>Gets the <see cref="T:System.Net.Cookie" /> with a specific name from a <see cref="T:System.Net.CookieCollection" />.</summary>
      <param name="name">The name of the <see cref="T:System.Net.Cookie" /> to be found.</param>
      <returns>The <see cref="T:System.Net.Cookie" /> with a specific name from a <see cref="T:System.Net.CookieCollection" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.CookieCollection.Remove(System.Net.Cookie)">
      <summary>Removes the specified cookie from the <see cref="T:System.Net.CookieCollection" />.</summary>
      <param name="cookie">The cookie to remove from the <see cref="T:System.Net.CookieCollection" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="cookie" /> was successfully removed from the <see cref="T:System.Net.CookieCollection" />; otherwise, <see langword="false" />. This method also returns <see langword="false" /> if item is not found in the original collection.</returns>
    </member>
    <member name="P:System.Net.CookieCollection.SyncRoot">
      <summary>Gets an object to synchronize access to the <see cref="T:System.Net.CookieCollection" />.</summary>
      <returns>An object to synchronize access to the <see cref="T:System.Net.CookieCollection" />.</returns>
    </member>
    <member name="M:System.Net.CookieCollection.System#Collections#Generic#IEnumerable{System#Net#Cookie}#GetEnumerator">
      <summary>Gets an enumerator that can iterate through the <see cref="T:System.Net.CookieCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> for this collection.</returns>
    </member>
    <member name="T:System.Net.CookieContainer">
      <summary>Provides a container for a collection of <see cref="T:System.Net.CookieCollection" /> objects.</summary>
    </member>
    <member name="M:System.Net.CookieContainer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.CookieContainer" /> class.</summary>
    </member>
    <member name="M:System.Net.CookieContainer.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.CookieContainer" /> class with a specified value for the number of <see cref="T:System.Net.Cookie" /> instances that the container can hold.</summary>
      <param name="capacity">The number of <see cref="T:System.Net.Cookie" /> instances that the <see cref="T:System.Net.CookieContainer" /> can hold.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="capacity" /> is less than or equal to zero.</exception>
    </member>
    <member name="M:System.Net.CookieContainer.#ctor(System.Int32,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.CookieContainer" /> class with specific properties.</summary>
      <param name="capacity">The number of <see cref="T:System.Net.Cookie" /> instances that the <see cref="T:System.Net.CookieContainer" /> can hold.</param>
      <param name="perDomainCapacity">The number of <see cref="T:System.Net.Cookie" /> instances per domain.</param>
      <param name="maxCookieSize">The maximum size in bytes for any single <see cref="T:System.Net.Cookie" /> in a <see cref="T:System.Net.CookieContainer" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="perDomainCapacity" /> is not equal to <see cref="F:System.Int32.MaxValue" />.
and
<paramref name="(perDomainCapacity" /> is less than or equal to zero or <paramref name="perDomainCapacity" /> is greater than <paramref name="capacity)" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="maxCookieSize" /> is less than or equal to zero.</exception>
    </member>
    <member name="M:System.Net.CookieContainer.Add(System.Net.Cookie)">
      <summary>Adds a <see cref="T:System.Net.Cookie" /> to a <see cref="T:System.Net.CookieContainer" />. This method uses the domain from the <see cref="T:System.Net.Cookie" /> to determine which domain collection to associate the <see cref="T:System.Net.Cookie" /> with.</summary>
      <param name="cookie">The <see cref="T:System.Net.Cookie" /> to be added to the <see cref="T:System.Net.CookieContainer" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="cookie" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The domain for <paramref name="cookie" /> is <see langword="null" /> or the empty string ("").</exception>
      <exception cref="T:System.Net.CookieException">
        <paramref name="cookie" /> is larger than <paramref name="maxCookieSize" />.
-or-
the domain for <paramref name="cookie" /> is not a valid URI.</exception>
    </member>
    <member name="M:System.Net.CookieContainer.Add(System.Net.CookieCollection)">
      <summary>Adds the contents of a <see cref="T:System.Net.CookieCollection" /> to the <see cref="T:System.Net.CookieContainer" />.</summary>
      <param name="cookies">The <see cref="T:System.Net.CookieCollection" /> to be added to the <see cref="T:System.Net.CookieContainer" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="cookies" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.CookieContainer.Add(System.Uri,System.Net.Cookie)">
      <summary>Adds a <see cref="T:System.Net.Cookie" /> to the <see cref="T:System.Net.CookieContainer" /> for a particular URI.</summary>
      <param name="uri">The URI of the <see cref="T:System.Net.Cookie" /> to be added to the <see cref="T:System.Net.CookieContainer" />.</param>
      <param name="cookie">The <see cref="T:System.Net.Cookie" /> to be added to the <see cref="T:System.Net.CookieContainer" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> is <see langword="null" /> or <paramref name="cookie" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.CookieException">
        <paramref name="cookie" /> is larger than <paramref name="maxCookieSize" />.
-or-
The domain for <paramref name="cookie" /> is not a valid URI.</exception>
    </member>
    <member name="M:System.Net.CookieContainer.Add(System.Uri,System.Net.CookieCollection)">
      <summary>Adds the contents of a <see cref="T:System.Net.CookieCollection" /> to the <see cref="T:System.Net.CookieContainer" /> for a particular URI.</summary>
      <param name="uri">The URI of the <see cref="T:System.Net.CookieCollection" /> to be added to the <see cref="T:System.Net.CookieContainer" />.</param>
      <param name="cookies">The <see cref="T:System.Net.CookieCollection" /> to be added to the <see cref="T:System.Net.CookieContainer" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="cookies" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The domain for one of the cookies in <paramref name="cookies" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.CookieException">One of the cookies in <paramref name="cookies" /> contains an invalid domain.</exception>
    </member>
    <member name="P:System.Net.CookieContainer.Capacity">
      <summary>Gets or sets the number of <see cref="T:System.Net.Cookie" /> instances that a <see cref="T:System.Net.CookieContainer" /> can hold.</summary>
      <returns>The number of <see cref="T:System.Net.Cookie" /> instances that a <see cref="T:System.Net.CookieContainer" /> can hold. This is a hard limit and cannot be exceeded by adding a <see cref="T:System.Net.Cookie" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="Capacity" /> is less than or equal to zero or (value is less than <see cref="P:System.Net.CookieContainer.PerDomainCapacity" /> and <see cref="P:System.Net.CookieContainer.PerDomainCapacity" /> is not equal to <see cref="F:System.Int32.MaxValue" />).</exception>
    </member>
    <member name="P:System.Net.CookieContainer.Count">
      <summary>Gets the number of <see cref="T:System.Net.Cookie" /> instances that a <see cref="T:System.Net.CookieContainer" /> currently holds.</summary>
      <returns>The number of <see cref="T:System.Net.Cookie" /> instances that a <see cref="T:System.Net.CookieContainer" /> currently holds. This is the total of <see cref="T:System.Net.Cookie" /> instances in all domains.</returns>
    </member>
    <member name="F:System.Net.CookieContainer.DefaultCookieLengthLimit">
      <summary>Represents the default maximum size, in bytes, of the <see cref="T:System.Net.Cookie" /> instances that the <see cref="T:System.Net.CookieContainer" /> can hold. This field is constant.</summary>
    </member>
    <member name="F:System.Net.CookieContainer.DefaultCookieLimit">
      <summary>Represents the default maximum number of <see cref="T:System.Net.Cookie" /> instances that the <see cref="T:System.Net.CookieContainer" /> can hold. This field is constant.</summary>
    </member>
    <member name="F:System.Net.CookieContainer.DefaultPerDomainCookieLimit">
      <summary>Represents the default maximum number of <see cref="T:System.Net.Cookie" /> instances that the <see cref="T:System.Net.CookieContainer" /> can reference per domain. This field is constant.</summary>
    </member>
    <member name="M:System.Net.CookieContainer.GetCookieHeader(System.Uri)">
      <summary>Gets the HTTP cookie header that contains the HTTP cookies that represent the <see cref="T:System.Net.Cookie" /> instances that are associated with a specific URI.</summary>
      <param name="uri">The URI of the <see cref="T:System.Net.Cookie" /> instances desired.</param>
      <returns>An HTTP cookie header, with strings representing <see cref="T:System.Net.Cookie" /> instances delimited by semicolons.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.CookieContainer.GetCookies(System.Uri)">
      <summary>Gets a <see cref="T:System.Net.CookieCollection" /> that contains the <see cref="T:System.Net.Cookie" /> instances that are associated with a specific URI.</summary>
      <param name="uri">The URI of the <see cref="T:System.Net.Cookie" /> instances desired.</param>
      <returns>A <see cref="T:System.Net.CookieCollection" /> that contains the <see cref="T:System.Net.Cookie" /> instances that are associated with a specific URI.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Net.CookieContainer.MaxCookieSize">
      <summary>Represents the maximum allowed length of a <see cref="T:System.Net.Cookie" />.</summary>
      <returns>The maximum allowed length, in bytes, of a <see cref="T:System.Net.Cookie" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="MaxCookieSize" /> is less than or equal to zero.</exception>
    </member>
    <member name="P:System.Net.CookieContainer.PerDomainCapacity">
      <summary>Gets or sets the number of <see cref="T:System.Net.Cookie" /> instances that a <see cref="T:System.Net.CookieContainer" /> can hold per domain.</summary>
      <returns>The number of <see cref="T:System.Net.Cookie" /> instances that are allowed per domain.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="PerDomainCapacity" /> is less than or equal to zero.
-or-
<paramref name="(PerDomainCapacity" /> is greater than the maximum allowable number of cookies instances, 300, and is not equal to <see cref="F:System.Int32.MaxValue" />).</exception>
    </member>
    <member name="M:System.Net.CookieContainer.SetCookies(System.Uri,System.String)">
      <summary>Adds <see cref="T:System.Net.Cookie" /> instances for one or more cookies from an HTTP cookie header to the <see cref="T:System.Net.CookieContainer" /> for a specific URI.</summary>
      <param name="uri">The URI of the <see cref="T:System.Net.CookieCollection" />.</param>
      <param name="cookieHeader">The contents of an HTTP set-cookie header as returned by a HTTP server, with <see cref="T:System.Net.Cookie" /> instances delimited by commas.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> or <paramref name="cookieHeader" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Net.CookieException">One of the cookies is invalid.
-or-
An error occurred while adding one of the cookies to the container.</exception>
    </member>
    <member name="T:System.Net.CookieException">
      <summary>The exception that is thrown when an error is made adding a <see cref="T:System.Net.Cookie" /> to a <see cref="T:System.Net.CookieContainer" />.</summary>
    </member>
    <member name="M:System.Net.CookieException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.CookieException" /> class.</summary>
    </member>
    <member name="M:System.Net.CookieException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.CookieException" /> class with specific values of <paramref name="serializationInfo" /> and <paramref name="streamingContext" />.</summary>
      <param name="serializationInfo">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to be used.</param>
      <param name="streamingContext">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> to be used.</param>
    </member>
    <member name="M:System.Net.CookieException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> instance with the data needed to serialize the <see cref="T:System.Net.CookieException" />.</summary>
      <param name="serializationInfo">The object that holds the serialized object data. The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="streamingContext">The contextual information about the source or destination. A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that specifies the destination for this serialization.</param>
    </member>
    <member name="M:System.Net.CookieException.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> instance with the data needed to serialize the <see cref="T:System.Net.CookieException" />.</summary>
      <param name="serializationInfo">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to be used.</param>
      <param name="streamingContext">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> to be used.</param>
    </member>
    <member name="T:System.Net.CredentialCache">
      <summary>Provides storage for multiple credentials.</summary>
    </member>
    <member name="M:System.Net.CredentialCache.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Net.CredentialCache" /> class.</summary>
    </member>
    <member name="M:System.Net.CredentialCache.Add(System.String,System.Int32,System.String,System.Net.NetworkCredential)">
      <summary>Adds a <see cref="T:System.Net.NetworkCredential" /> instance for use with SMTP to the credential cache and associates it with a host computer, port, and authentication protocol. Credentials added using this method are valid for SMTP only. This method does not work for HTTP or FTP requests.</summary>
      <param name="host">A <see cref="T:System.String" /> that identifies the host computer.</param>
      <param name="port">A <see cref="T:System.Int32" /> that specifies the port to connect to on <paramref name="host" />.</param>
      <param name="authenticationType">A <see cref="T:System.String" /> that identifies the authentication scheme used when connecting to <paramref name="host" /> using <paramref name="cred" />.</param>
      <param name="credential">The <see cref="T:System.Net.NetworkCredential" /> to add to the credential cache.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="host" /> is <see langword="null" />.
-or-
<paramref name="authType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="authType" /> not an accepted value.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> is less than zero.</exception>
    </member>
    <member name="M:System.Net.CredentialCache.Add(System.Uri,System.String,System.Net.NetworkCredential)">
      <summary>Adds a <see cref="T:System.Net.NetworkCredential" /> instance to the credential cache for use with protocols other than SMTP and associates it with a Uniform Resource Identifier (URI) prefix and authentication protocol.</summary>
      <param name="uriPrefix">A <see cref="T:System.Uri" /> that specifies the URI prefix of the resources that the credential grants access to.</param>
      <param name="authType">The authentication scheme used by the resource named in <paramref name="uriPrefix" />.</param>
      <param name="cred">The <see cref="T:System.Net.NetworkCredential" /> to add to the credential cache.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uriPrefix" /> is <see langword="null" />.
-or-
<paramref name="authType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The same credentials are added more than once.</exception>
    </member>
    <member name="P:System.Net.CredentialCache.DefaultCredentials">
      <summary>Gets the system credentials of the application.</summary>
      <returns>An <see cref="T:System.Net.ICredentials" /> that represents the system credentials of the application.</returns>
    </member>
    <member name="P:System.Net.CredentialCache.DefaultNetworkCredentials">
      <summary>Gets the network credentials of the current security context.</summary>
      <returns>An <see cref="T:System.Net.NetworkCredential" /> that represents the network credentials of the current user or application.</returns>
    </member>
    <member name="M:System.Net.CredentialCache.GetCredential(System.String,System.Int32,System.String)">
      <summary>Returns the <see cref="T:System.Net.NetworkCredential" /> instance associated with the specified host, port, and authentication protocol.</summary>
      <param name="host">A <see cref="T:System.String" /> that identifies the host computer.</param>
      <param name="port">A <see cref="T:System.Int32" /> that specifies the port to connect to on <paramref name="host" />.</param>
      <param name="authenticationType">A <see cref="T:System.String" /> that identifies the authentication scheme used when connecting to <paramref name="host" />.</param>
      <returns>A <see cref="T:System.Net.NetworkCredential" /> or, if there is no matching credential in the cache, <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="host" /> is <see langword="null" />.
-or-
<paramref name="authType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="authType" /> not an accepted value.
-or-
<paramref name="host" /> is equal to the empty string ("").</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> is less than zero.</exception>
    </member>
    <member name="M:System.Net.CredentialCache.GetCredential(System.Uri,System.String)">
      <summary>Returns the <see cref="T:System.Net.NetworkCredential" /> instance associated with the specified Uniform Resource Identifier (URI) and authentication type.</summary>
      <param name="uriPrefix">A <see cref="T:System.Uri" /> that specifies the URI prefix of the resources that the credential grants access to.</param>
      <param name="authType">The authentication scheme used by the resource named in <paramref name="uriPrefix" />.</param>
      <returns>A <see cref="T:System.Net.NetworkCredential" /> or, if there is no matching credential in the cache, <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uriPrefix" /> or <paramref name="authType" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Net.CredentialCache.GetEnumerator">
      <summary>Returns an enumerator that can iterate through the <see cref="T:System.Net.CredentialCache" /> instance.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Net.CredentialCache" />.</returns>
    </member>
    <member name="M:System.Net.CredentialCache.Remove(System.String,System.Int32,System.String)">
      <summary>Deletes a <see cref="T:System.Net.NetworkCredential" /> instance from the cache if it is associated with the specified host, port, and authentication protocol.</summary>
      <param name="host">A <see cref="T:System.String" /> that identifies the host computer.</param>
      <param name="port">A <see cref="T:System.Int32" /> that specifies the port to connect to on <paramref name="host" />.</param>
      <param name="authenticationType">A <see cref="T:System.String" /> that identifies the authentication scheme used when connecting to <paramref name="host" />.</param>
    </member>
    <member name="M:System.Net.CredentialCache.Remove(System.Uri,System.String)">
      <summary>Deletes a <see cref="T:System.Net.NetworkCredential" /> instance from the cache if it is associated with the specified Uniform Resource Identifier (URI) prefix and authentication protocol.</summary>
      <param name="uriPrefix">A <see cref="T:System.Uri" /> that specifies the URI prefix of the resources that the credential is used for.</param>
      <param name="authType">The authentication scheme used by the host named in <paramref name="uriPrefix" />.</param>
    </member>
    <member name="T:System.Net.DecompressionMethods">
      <summary>Represents the file compression and decompression encoding format to be used to compress the data received in response to an <see cref="T:System.Net.HttpWebRequest" />.</summary>
    </member>
    <member name="F:System.Net.DecompressionMethods.All">
      <summary>Use all compression-decompression algorithms.</summary>
    </member>
    <member name="F:System.Net.DecompressionMethods.Brotli">
      <summary>Use the Brotli compression-decompression algorithm.</summary>
    </member>
    <member name="F:System.Net.DecompressionMethods.Deflate">
      <summary>Use the deflate compression-decompression algorithm.</summary>
    </member>
    <member name="F:System.Net.DecompressionMethods.GZip">
      <summary>Use the gZip compression-decompression algorithm.</summary>
    </member>
    <member name="F:System.Net.DecompressionMethods.None">
      <summary>Do not use compression.</summary>
    </member>
    <member name="T:System.Net.DnsEndPoint">
      <summary>Represents a network endpoint as a host name or a string representation of an IP address and a port number.</summary>
    </member>
    <member name="M:System.Net.DnsEndPoint.#ctor(System.String,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.DnsEndPoint" /> class with the host name or string representation of an IP address and a port number.</summary>
      <param name="host">The host name or a string representation of the IP address.</param>
      <param name="port">The port number associated with the address, or 0 to specify any available port. <paramref name="port" /> is in host order.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="host" /> parameter contains an empty string.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="host" /> parameter is a <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> is less than <see cref="F:System.Net.IPEndPoint.MinPort" />.
-or-
<paramref name="port" /> is greater than <see cref="F:System.Net.IPEndPoint.MaxPort" />.</exception>
    </member>
    <member name="M:System.Net.DnsEndPoint.#ctor(System.String,System.Int32,System.Net.Sockets.AddressFamily)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.DnsEndPoint" /> class with the host name or string representation of an IP address, a port number, and an address family.</summary>
      <param name="host">The host name or a string representation of the IP address.</param>
      <param name="port">The port number associated with the address, or 0 to specify any available port. <paramref name="port" /> is in host order.</param>
      <param name="addressFamily">One of the <see cref="T:System.Net.Sockets.AddressFamily" /> values.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="host" /> parameter contains an empty string.
-or-
<paramref name="addressFamily" /> is <see cref="F:System.Net.Sockets.AddressFamily.Unknown" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="host" /> parameter is a <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> is less than <see cref="F:System.Net.IPEndPoint.MinPort" />.
-or-
<paramref name="port" /> is greater than <see cref="F:System.Net.IPEndPoint.MaxPort" />.</exception>
    </member>
    <member name="P:System.Net.DnsEndPoint.AddressFamily">
      <summary>Gets the Internet Protocol (IP) address family.</summary>
      <returns>One of the <see cref="T:System.Net.Sockets.AddressFamily" /> values.</returns>
    </member>
    <member name="M:System.Net.DnsEndPoint.Equals(System.Object)">
      <summary>Compares two <see cref="T:System.Net.DnsEndPoint" /> objects.</summary>
      <param name="comparand">A <see cref="T:System.Net.DnsEndPoint" /> instance to compare to the current instance.</param>
      <returns>
        <see langword="true" /> if the two <see cref="T:System.Net.DnsEndPoint" /> instances are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.DnsEndPoint.GetHashCode">
      <summary>Returns a hash value for a <see cref="T:System.Net.DnsEndPoint" />.</summary>
      <returns>An integer hash value for the <see cref="T:System.Net.DnsEndPoint" />.</returns>
    </member>
    <member name="P:System.Net.DnsEndPoint.Host">
      <summary>Gets the host name or string representation of the Internet Protocol (IP) address of the host.</summary>
      <returns>A host name or string representation of an IP address.</returns>
    </member>
    <member name="P:System.Net.DnsEndPoint.Port">
      <summary>Gets the port number of the <see cref="T:System.Net.DnsEndPoint" />.</summary>
      <returns>An integer value in the range 0 to 0xffff indicating the port number of the <see cref="T:System.Net.DnsEndPoint" />.</returns>
    </member>
    <member name="M:System.Net.DnsEndPoint.ToString">
      <summary>Returns the host name or string representation of the IP address and port number of the <see cref="T:System.Net.DnsEndPoint" />.</summary>
      <returns>A string containing the address family, host name or IP address string, and the port number of the specified <see cref="T:System.Net.DnsEndPoint" />.</returns>
    </member>
    <member name="T:System.Net.EndPoint">
      <summary>Identifies a network address. This is an <see langword="abstract" /> class.</summary>
    </member>
    <member name="M:System.Net.EndPoint.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.EndPoint" /> class.</summary>
    </member>
    <member name="P:System.Net.EndPoint.AddressFamily">
      <summary>Gets the address family to which the endpoint belongs.</summary>
      <returns>One of the <see cref="T:System.Net.Sockets.AddressFamily" /> values.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property when the property is not overridden in a descendant class.</exception>
    </member>
    <member name="M:System.Net.EndPoint.Create(System.Net.SocketAddress)">
      <summary>Creates an <see cref="T:System.Net.EndPoint" /> instance from a <see cref="T:System.Net.SocketAddress" /> instance.</summary>
      <param name="socketAddress">The socket address that serves as the endpoint for a connection.</param>
      <returns>A new <see cref="T:System.Net.EndPoint" /> instance that is initialized from the specified <see cref="T:System.Net.SocketAddress" /> instance.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method when the method is not overridden in a descendant class.</exception>
    </member>
    <member name="M:System.Net.EndPoint.Serialize">
      <summary>Serializes endpoint information into a <see cref="T:System.Net.SocketAddress" /> instance.</summary>
      <returns>A <see cref="T:System.Net.SocketAddress" /> instance that contains the endpoint information.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method when the method is not overridden in a descendant class.</exception>
    </member>
    <member name="T:System.Net.HttpStatusCode">
      <summary>Contains the values of status codes defined for HTTP.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Accepted">
      <summary>Equivalent to HTTP status 202. <see cref="F:System.Net.HttpStatusCode.Accepted" /> indicates that the request has been accepted for further processing.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.AlreadyReported">
      <summary>Equivalent to HTTP status 208. <see cref="F:System.Net.HttpStatusCode.AlreadyReported" /> indicates that the members of a WebDAV binding have already been enumerated in a preceding part of the multistatus response, and are not being included again.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Ambiguous">
      <summary>Equivalent to HTTP status 300. <see cref="F:System.Net.HttpStatusCode.Ambiguous" /> indicates that the requested information has multiple representations. The default action is to treat this status as a redirect and follow the contents of the Location header associated with this response. <c>Ambiguous</c> is a synonym for <c>MultipleChoices</c>.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.BadGateway">
      <summary>Equivalent to HTTP status 502. <see cref="F:System.Net.HttpStatusCode.BadGateway" /> indicates that an intermediate proxy server received a bad response from another proxy or the origin server.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.BadRequest">
      <summary>Equivalent to HTTP status 400. <see cref="F:System.Net.HttpStatusCode.BadRequest" /> indicates that the request could not be understood by the server. <see cref="F:System.Net.HttpStatusCode.BadRequest" /> is sent when no other error is applicable, or if the exact error is unknown or does not have its own error code.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Conflict">
      <summary>Equivalent to HTTP status 409. <see cref="F:System.Net.HttpStatusCode.Conflict" /> indicates that the request could not be carried out because of a conflict on the server.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Continue">
      <summary>Equivalent to HTTP status 100. <see cref="F:System.Net.HttpStatusCode.Continue" /> indicates that the client can continue with its request.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Created">
      <summary>Equivalent to HTTP status 201. <see cref="F:System.Net.HttpStatusCode.Created" /> indicates that the request resulted in a new resource created before the response was sent.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.EarlyHints">
      <summary>Equivalent to HTTP status 103. <see cref="F:System.Net.HttpStatusCode.EarlyHints" /> indicates to the client that the server is likely to send a final response with the header fields included in the informational response.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.ExpectationFailed">
      <summary>Equivalent to HTTP status 417. <see cref="F:System.Net.HttpStatusCode.ExpectationFailed" /> indicates that an expectation given in an Expect header could not be met by the server.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.FailedDependency">
      <summary>Equivalent to HTTP status 424. <see cref="F:System.Net.HttpStatusCode.FailedDependency" /> indicates that the method couldn't be performed on the resource because the requested action depended on another action and that action failed.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Forbidden">
      <summary>Equivalent to HTTP status 403. <see cref="F:System.Net.HttpStatusCode.Forbidden" /> indicates that the server refuses to fulfill the request.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Found">
      <summary>Equivalent to HTTP status 302. <see cref="F:System.Net.HttpStatusCode.Found" /> indicates that the requested information is located at the URI specified in the Location header. The default action when this status is received is to follow the Location header associated with the response. When the original request method was POST, the redirected request will use the GET method. <c>Found</c> is a synonym for <c>Redirect</c>.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.GatewayTimeout">
      <summary>Equivalent to HTTP status 504. <see cref="F:System.Net.HttpStatusCode.GatewayTimeout" /> indicates that an intermediate proxy server timed out while waiting for a response from another proxy or the origin server.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Gone">
      <summary>Equivalent to HTTP status 410. <see cref="F:System.Net.HttpStatusCode.Gone" /> indicates that the requested resource is no longer available.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.HttpVersionNotSupported">
      <summary>Equivalent to HTTP status 505. <see cref="F:System.Net.HttpStatusCode.HttpVersionNotSupported" /> indicates that the requested HTTP version is not supported by the server.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.IMUsed">
      <summary>Equivalent to HTTP status 226. <see cref="F:System.Net.HttpStatusCode.IMUsed" /> indicates that the server has fulfilled a request for the resource, and the response is a representation of the result of one or more instance-manipulations applied to the current instance.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.InsufficientStorage">
      <summary>Equivalent to HTTP status 507. <see cref="F:System.Net.HttpStatusCode.InsufficientStorage" /> indicates that the server is unable to store the representation needed to complete the request.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.InternalServerError">
      <summary>Equivalent to HTTP status 500. <see cref="F:System.Net.HttpStatusCode.InternalServerError" /> indicates that a generic error has occurred on the server.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.LengthRequired">
      <summary>Equivalent to HTTP status 411. <see cref="F:System.Net.HttpStatusCode.LengthRequired" /> indicates that the required Content-length header is missing.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Locked">
      <summary>Equivalent to HTTP status 423. <see cref="F:System.Net.HttpStatusCode.Locked" /> indicates that the source or destination resource is locked.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.LoopDetected">
      <summary>Equivalent to HTTP status 508. <see cref="F:System.Net.HttpStatusCode.LoopDetected" /> indicates that the server terminated an operation because it encountered an infinite loop while processing a WebDAV request with "Depth: infinity". This status code is meant for backward compatibility with clients not aware of the 208 status code <see cref="F:System.Net.HttpStatusCode.AlreadyReported" /> appearing in multistatus response bodies.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.MethodNotAllowed">
      <summary>Equivalent to HTTP status 405. <see cref="F:System.Net.HttpStatusCode.MethodNotAllowed" /> indicates that the request method (POST or GET) is not allowed on the requested resource.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.MisdirectedRequest">
      <summary>Equivalent to HTTP status 421. <see cref="F:System.Net.HttpStatusCode.MisdirectedRequest" /> indicates that the request was directed at a server that is not able to produce a response.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Moved">
      <summary>Equivalent to HTTP status 301. <see cref="F:System.Net.HttpStatusCode.Moved" /> indicates that the requested information has been moved to the URI specified in the Location header. The default action when this status is received is to follow the Location header associated with the response. When the original request method was POST, the redirected request will use the GET method. <c>Moved</c> is a synonym for <c>MovedPermanently</c>.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.MovedPermanently">
      <summary>Equivalent to HTTP status 301. <see cref="F:System.Net.HttpStatusCode.MovedPermanently" /> indicates that the requested information has been moved to the URI specified in the Location header. The default action when this status is received is to follow the Location header associated with the response. <c>MovedPermanently</c> is a synonym for <c>Moved</c>.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.MultipleChoices">
      <summary>Equivalent to HTTP status 300. <see cref="F:System.Net.HttpStatusCode.MultipleChoices" /> indicates that the requested information has multiple representations. The default action is to treat this status as a redirect and follow the contents of the Location header associated with this response. <c>MultipleChoices</c> is a synonym for <c>Ambiguous</c>.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.MultiStatus">
      <summary>Equivalent to HTTP status 207. <see cref="F:System.Net.HttpStatusCode.MultiStatus" /> indicates multiple status codes for a single response during a Web Distributed Authoring and Versioning (WebDAV) operation. The response body contains XML that describes the status codes.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.NetworkAuthenticationRequired">
      <summary>Equivalent to HTTP status 511. <see cref="F:System.Net.HttpStatusCode.NetworkAuthenticationRequired" /> indicates that the client needs to authenticate to gain network access; it's intended for use by intercepting proxies used to control access to the network.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.NoContent">
      <summary>Equivalent to HTTP status 204. <see cref="F:System.Net.HttpStatusCode.NoContent" /> indicates that the request has been successfully processed and that the response is intentionally blank.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.NonAuthoritativeInformation">
      <summary>Equivalent to HTTP status 203. <see cref="F:System.Net.HttpStatusCode.NonAuthoritativeInformation" /> indicates that the returned metainformation is from a cached copy instead of the origin server and therefore may be incorrect.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.NotAcceptable">
      <summary>Equivalent to HTTP status 406. <see cref="F:System.Net.HttpStatusCode.NotAcceptable" /> indicates that the client has indicated with Accept headers that it will not accept any of the available representations of the resource.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.NotExtended">
      <summary>Equivalent to HTTP status 510. <see cref="F:System.Net.HttpStatusCode.NotExtended" /> indicates that further extensions to the request are required for the server to fulfill it.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.NotFound">
      <summary>Equivalent to HTTP status 404. <see cref="F:System.Net.HttpStatusCode.NotFound" /> indicates that the requested resource does not exist on the server.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.NotImplemented">
      <summary>Equivalent to HTTP status 501. <see cref="F:System.Net.HttpStatusCode.NotImplemented" /> indicates that the server does not support the requested function.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.NotModified">
      <summary>Equivalent to HTTP status 304. <see cref="F:System.Net.HttpStatusCode.NotModified" /> indicates that the client's cached copy is up to date. The contents of the resource are not transferred.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.OK">
      <summary>Equivalent to HTTP status 200. <see cref="F:System.Net.HttpStatusCode.OK" /> indicates that the request succeeded and that the requested information is in the response. This is the most common status code to receive.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.PartialContent">
      <summary>Equivalent to HTTP status 206. <see cref="F:System.Net.HttpStatusCode.PartialContent" /> indicates that the response is a partial response as requested by a GET request that includes a byte range.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.PaymentRequired">
      <summary>Equivalent to HTTP status 402. <see cref="F:System.Net.HttpStatusCode.PaymentRequired" /> is reserved for future use.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.PermanentRedirect">
      <summary>Equivalent to HTTP status 308. <see cref="F:System.Net.HttpStatusCode.PermanentRedirect" /> indicates that the request information is located at the URI specified in the Location header. The default action when this status is received is to follow the Location header associated with the response. When the original request method was POST, the redirected request will also use the POST method.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.PreconditionFailed">
      <summary>Equivalent to HTTP status 412. <see cref="F:System.Net.HttpStatusCode.PreconditionFailed" /> indicates that a condition set for this request failed, and the request cannot be carried out. Conditions are set with conditional request headers like If-Match, If-None-Match, or If-Unmodified-Since.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.PreconditionRequired">
      <summary>Equivalent to HTTP status 428. <see cref="F:System.Net.HttpStatusCode.PreconditionRequired" /> indicates that the server requires the request to be conditional.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Processing">
      <summary>Equivalent to HTTP status 102. <see cref="F:System.Net.HttpStatusCode.Processing" /> indicates that the server has accepted the complete request but hasn't completed it yet.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.ProxyAuthenticationRequired">
      <summary>Equivalent to HTTP status 407. <see cref="F:System.Net.HttpStatusCode.ProxyAuthenticationRequired" /> indicates that the requested proxy requires authentication. The Proxy-authenticate header contains the details of how to perform the authentication.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Redirect">
      <summary>Equivalent to HTTP status 302. <see cref="F:System.Net.HttpStatusCode.Redirect" /> indicates that the requested information is located at the URI specified in the Location header. The default action when this status is received is to follow the Location header associated with the response. When the original request method was POST, the redirected request will use the GET method. <c>Redirect</c> is a synonym for <c>Found</c>.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.RedirectKeepVerb">
      <summary>Equivalent to HTTP status 307. <see cref="F:System.Net.HttpStatusCode.RedirectKeepVerb" /> indicates that the request information is located at the URI specified in the Location header. The default action when this status is received is to follow the Location header associated with the response. When the original request method was POST, the redirected request will also use the POST method. <c>RedirectKeepVerb</c> is a synonym for <c>TemporaryRedirect</c>.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.RedirectMethod">
      <summary>Equivalent to HTTP status 303. <see cref="F:System.Net.HttpStatusCode.RedirectMethod" /> automatically redirects the client to the URI specified in the Location header as the result of a POST. The request to the resource specified by the Location header will be made with a GET. <c>RedirectMethod</c> is a synonym for <c>SeeOther</c>.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.RequestedRangeNotSatisfiable">
      <summary>Equivalent to HTTP status 416. <see cref="F:System.Net.HttpStatusCode.RequestedRangeNotSatisfiable" /> indicates that the range of data requested from the resource cannot be returned, either because the beginning of the range is before the beginning of the resource, or the end of the range is after the end of the resource.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.RequestEntityTooLarge">
      <summary>Equivalent to HTTP status 413. <see cref="F:System.Net.HttpStatusCode.RequestEntityTooLarge" /> indicates that the request is too large for the server to process.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.RequestHeaderFieldsTooLarge">
      <summary>Equivalent to HTTP status 431. <see cref="F:System.Net.HttpStatusCode.RequestHeaderFieldsTooLarge" /> indicates that the server is unwilling to process the request because its header fields (either an individual header field or all the header fields collectively) are too large.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.RequestTimeout">
      <summary>Equivalent to HTTP status 408. <see cref="F:System.Net.HttpStatusCode.RequestTimeout" /> indicates that the client did not send a request within the time the server was expecting the request.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.RequestUriTooLong">
      <summary>Equivalent to HTTP status 414. <see cref="F:System.Net.HttpStatusCode.RequestUriTooLong" /> indicates that the URI is too long.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.ResetContent">
      <summary>Equivalent to HTTP status 205. <see cref="F:System.Net.HttpStatusCode.ResetContent" /> indicates that the client should reset (not reload) the current resource.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.SeeOther">
      <summary>Equivalent to HTTP status 303. <see cref="F:System.Net.HttpStatusCode.SeeOther" /> automatically redirects the client to the URI specified in the Location header as the result of a POST. The request to the resource specified by the Location header will be made with a GET. <c>SeeOther</c> is a synonym for <c>RedirectMethod</c></summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.ServiceUnavailable">
      <summary>Equivalent to HTTP status 503. <see cref="F:System.Net.HttpStatusCode.ServiceUnavailable" /> indicates that the server is temporarily unavailable, usually due to high load or maintenance.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.SwitchingProtocols">
      <summary>Equivalent to HTTP status 101. <see cref="F:System.Net.HttpStatusCode.SwitchingProtocols" /> indicates that the protocol version or protocol is being changed.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.TemporaryRedirect">
      <summary>Equivalent to HTTP status 307. <see cref="F:System.Net.HttpStatusCode.TemporaryRedirect" /> indicates that the request information is located at the URI specified in the Location header. The default action when this status is received is to follow the Location header associated with the response. When the original request method was POST, the redirected request will also use the POST method. <c>TemporaryRedirect</c> is a synonym for <c>RedirectKeepVerb</c>.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.TooManyRequests">
      <summary>Equivalent to HTTP status 429. <see cref="F:System.Net.HttpStatusCode.TooManyRequests" /> indicates that the user has sent too many requests in a given amount of time.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Unauthorized">
      <summary>Equivalent to HTTP status 401. <see cref="F:System.Net.HttpStatusCode.Unauthorized" /> indicates that the requested resource requires authentication. The WWW-Authenticate header contains the details of how to perform the authentication.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.UnavailableForLegalReasons">
      <summary>Equivalent to HTTP status 451. <see cref="F:System.Net.HttpStatusCode.UnavailableForLegalReasons" /> indicates that the server is denying access to the resource as a consequence of a legal demand.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.UnprocessableEntity">
      <summary>Equivalent to HTTP status 422. <see cref="F:System.Net.HttpStatusCode.UnprocessableEntity" /> indicates that the request was well-formed but was unable to be followed due to semantic errors.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.UnsupportedMediaType">
      <summary>Equivalent to HTTP status 415. <see cref="F:System.Net.HttpStatusCode.UnsupportedMediaType" /> indicates that the request is an unsupported type.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.Unused">
      <summary>Equivalent to HTTP status 306. <see cref="F:System.Net.HttpStatusCode.Unused" /> is a proposed extension to the HTTP/1.1 specification that is not fully specified.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.UpgradeRequired">
      <summary>Equivalent to HTTP status 426. <see cref="F:System.Net.HttpStatusCode.UpgradeRequired" /> indicates that the client should switch to a different protocol such as TLS/1.0.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.UseProxy">
      <summary>Equivalent to HTTP status 305. <see cref="F:System.Net.HttpStatusCode.UseProxy" /> indicates that the request should use the proxy server at the URI specified in the Location header.</summary>
    </member>
    <member name="F:System.Net.HttpStatusCode.VariantAlsoNegotiates">
      <summary>Equivalent to HTTP status 506. <see cref="F:System.Net.HttpStatusCode.VariantAlsoNegotiates" /> indicates that the chosen variant resource is configured to engage in transparent content negotiation itself and, therefore, isn't a proper endpoint in the negotiation process.</summary>
    </member>
    <member name="T:System.Net.HttpVersion">
      <summary>Defines the HTTP version numbers that are supported by the <see cref="T:System.Net.HttpWebRequest" /> and <see cref="T:System.Net.HttpWebResponse" /> classes.</summary>
    </member>
    <member name="F:System.Net.HttpVersion.Unknown" />
    <member name="F:System.Net.HttpVersion.Version10">
      <summary>Defines a <see cref="T:System.Version" /> instance for HTTP 1.0.</summary>
    </member>
    <member name="F:System.Net.HttpVersion.Version11">
      <summary>Defines a <see cref="T:System.Version" /> instance for HTTP 1.1.</summary>
    </member>
    <member name="F:System.Net.HttpVersion.Version20" />
    <member name="T:System.Net.ICredentials">
      <summary>Provides the base authentication interface for retrieving credentials for Web client authentication.</summary>
    </member>
    <member name="M:System.Net.ICredentials.GetCredential(System.Uri,System.String)">
      <summary>Returns a <see cref="T:System.Net.NetworkCredential" /> object that is associated with the specified URI, and authentication type.</summary>
      <param name="uri">The <see cref="T:System.Uri" /> that the client is providing authentication for.</param>
      <param name="authType">The type of authentication, as defined in the <see cref="P:System.Net.IAuthenticationModule.AuthenticationType" /> property.</param>
      <returns>The <see cref="T:System.Net.NetworkCredential" /> that is associated with the specified URI and authentication type, or, if no credentials are available, <see langword="null" />.</returns>
    </member>
    <member name="T:System.Net.ICredentialsByHost">
      <summary>Provides the interface for retrieving credentials for a host, port, and authentication type.</summary>
    </member>
    <member name="M:System.Net.ICredentialsByHost.GetCredential(System.String,System.Int32,System.String)">
      <summary>Returns the credential for the specified host, port, and authentication protocol.</summary>
      <param name="host">The host computer that is authenticating the client.</param>
      <param name="port">The port on <paramref name="host" /> that the client will communicate with.</param>
      <param name="authenticationType">The authentication protocol.</param>
      <returns>A <see cref="T:System.Net.NetworkCredential" /> for the specified host, port, and authentication protocol, or <see langword="null" /> if there are no credentials available for the specified host, port, and authentication protocol.</returns>
    </member>
    <member name="T:System.Net.IPAddress">
      <summary>Provides an Internet Protocol (IP) address.</summary>
    </member>
    <member name="M:System.Net.IPAddress.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Net.IPAddress" /> class with the address specified as a <see cref="T:System.Byte" /> array.</summary>
      <param name="address">The byte array value of the IP address.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="address" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="address" /> contains a bad IP address.</exception>
    </member>
    <member name="M:System.Net.IPAddress.#ctor(System.Byte[],System.Int64)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.IPAddress" /> class with the address specified as a <see cref="T:System.Byte" /> array and the specified scope identifier.</summary>
      <param name="address">The byte array value of the IP address.</param>
      <param name="scopeid">The long value of the scope identifier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="address" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="address" /> contains a bad IP address.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="scopeid" /> &lt; 0 or
<paramref name="scopeid" /> &gt; 0x00000000FFFFFFFF</exception>
    </member>
    <member name="M:System.Net.IPAddress.#ctor(System.Int64)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.IPAddress" /> class with the address specified as an <see cref="T:System.Int64" />.</summary>
      <param name="newAddress">The long value of the IP address. For example, the value 0x2414188f in big-endian format would be the IP address "************".</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="newAddress" /> &lt; 0 or
<paramref name="newAddress" /> &gt; 0x00000000FFFFFFFF</exception>
    </member>
    <member name="M:System.Net.IPAddress.#ctor(System.ReadOnlySpan{System.Byte})">
      <param name="address" />
    </member>
    <member name="M:System.Net.IPAddress.#ctor(System.ReadOnlySpan{System.Byte},System.Int64)">
      <param name="address" />
      <param name="scopeid" />
    </member>
    <member name="P:System.Net.IPAddress.Address">
      <summary>An Internet Protocol (IP) address.</summary>
      <returns>The long value of the IP address.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">The address family is <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" />.</exception>
    </member>
    <member name="P:System.Net.IPAddress.AddressFamily">
      <summary>Gets the address family of the IP address.</summary>
      <returns>Returns <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> for IPv4 or <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" /> for IPv6.</returns>
    </member>
    <member name="F:System.Net.IPAddress.Any">
      <summary>Provides an IP address that indicates that the server must listen for client activity on all network interfaces. This field is read-only.</summary>
    </member>
    <member name="F:System.Net.IPAddress.Broadcast">
      <summary>Provides the IP broadcast address. This field is read-only.</summary>
    </member>
    <member name="M:System.Net.IPAddress.Equals(System.Object)">
      <summary>Compares two IP addresses.</summary>
      <param name="comparand">An <see cref="T:System.Net.IPAddress" /> instance to compare to the current instance.</param>
      <returns>
        <see langword="true" /> if the two addresses are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.IPAddress.GetAddressBytes">
      <summary>Provides a copy of the <see cref="T:System.Net.IPAddress" /> as an array of bytes.</summary>
      <returns>A <see cref="T:System.Byte" /> array.</returns>
    </member>
    <member name="M:System.Net.IPAddress.GetHashCode">
      <summary>Returns a hash value for an IP address.</summary>
      <returns>An integer hash value.</returns>
    </member>
    <member name="M:System.Net.IPAddress.HostToNetworkOrder(System.Int16)">
      <summary>Converts a short value from host byte order to network byte order.</summary>
      <param name="host">The number to convert, expressed in host byte order.</param>
      <returns>A short value, expressed in network byte order.</returns>
    </member>
    <member name="M:System.Net.IPAddress.HostToNetworkOrder(System.Int32)">
      <summary>Converts an integer value from host byte order to network byte order.</summary>
      <param name="host">The number to convert, expressed in host byte order.</param>
      <returns>An integer value, expressed in network byte order.</returns>
    </member>
    <member name="M:System.Net.IPAddress.HostToNetworkOrder(System.Int64)">
      <summary>Converts a long value from host byte order to network byte order.</summary>
      <param name="host">The number to convert, expressed in host byte order.</param>
      <returns>A long value, expressed in network byte order.</returns>
    </member>
    <member name="F:System.Net.IPAddress.IPv6Any">
      <summary>The <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)" /> method uses the <see cref="F:System.Net.IPAddress.IPv6Any" /> field to indicate that a <see cref="T:System.Net.Sockets.Socket" /> must listen for client activity on all network interfaces.</summary>
    </member>
    <member name="F:System.Net.IPAddress.IPv6Loopback">
      <summary>Provides the IP loopback address. This property is read-only.</summary>
    </member>
    <member name="F:System.Net.IPAddress.IPv6None">
      <summary>Provides an IP address that indicates that no network interface should be used. This property is read-only.</summary>
    </member>
    <member name="P:System.Net.IPAddress.IsIPv4MappedToIPv6">
      <summary>Gets whether the IP address is an IPv4-mapped IPv6 address.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.
<see langword="true" /> if the IP address is an IPv4-mapped IPv6 address; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.IPAddress.IsIPv6LinkLocal">
      <summary>Gets whether the address is an IPv6 link local address.</summary>
      <returns>
        <see langword="true" /> if the IP address is an IPv6 link local address; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.IPAddress.IsIPv6Multicast">
      <summary>Gets whether the address is an IPv6 multicast global address.</summary>
      <returns>
        <see langword="true" /> if the IP address is an IPv6 multicast global address; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.IPAddress.IsIPv6SiteLocal">
      <summary>Gets whether the address is an IPv6 site local address.</summary>
      <returns>
        <see langword="true" /> if the IP address is an IPv6 site local address; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.IPAddress.IsIPv6Teredo">
      <summary>Gets whether the address is an IPv6 Teredo address.</summary>
      <returns>
        <see langword="true" /> if the IP address is an IPv6 Teredo address; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.IPAddress.IsLoopback(System.Net.IPAddress)">
      <summary>Indicates whether the specified IP address is the loopback address.</summary>
      <param name="address">An IP address.</param>
      <returns>
        <see langword="true" /> if <paramref name="address" /> is the loopback address; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="F:System.Net.IPAddress.Loopback">
      <summary>Provides the IP loopback address. This field is read-only.</summary>
    </member>
    <member name="M:System.Net.IPAddress.MapToIPv4">
      <summary>Maps the <see cref="T:System.Net.IPAddress" /> object to an IPv4 address.</summary>
      <returns>Returns <see cref="T:System.Net.IPAddress" />.
An IPv4 address.</returns>
    </member>
    <member name="M:System.Net.IPAddress.MapToIPv6">
      <summary>Maps the <see cref="T:System.Net.IPAddress" /> object to an IPv6 address.</summary>
      <returns>Returns <see cref="T:System.Net.IPAddress" />.
An IPv6 address.</returns>
    </member>
    <member name="M:System.Net.IPAddress.NetworkToHostOrder(System.Int16)">
      <summary>Converts a short value from network byte order to host byte order.</summary>
      <param name="network">The number to convert, expressed in network byte order.</param>
      <returns>A short value, expressed in host byte order.</returns>
    </member>
    <member name="M:System.Net.IPAddress.NetworkToHostOrder(System.Int32)">
      <summary>Converts an integer value from network byte order to host byte order.</summary>
      <param name="network">The number to convert, expressed in network byte order.</param>
      <returns>An integer value, expressed in host byte order.</returns>
    </member>
    <member name="M:System.Net.IPAddress.NetworkToHostOrder(System.Int64)">
      <summary>Converts a long value from network byte order to host byte order.</summary>
      <param name="network">The number to convert, expressed in network byte order.</param>
      <returns>A long value, expressed in host byte order.</returns>
    </member>
    <member name="F:System.Net.IPAddress.None">
      <summary>Provides an IP address that indicates that no network interface should be used. This field is read-only.</summary>
    </member>
    <member name="M:System.Net.IPAddress.Parse(System.ReadOnlySpan{System.Char})">
      <param name="ipString" />
    </member>
    <member name="M:System.Net.IPAddress.Parse(System.String)">
      <summary>Converts an IP address string to an <see cref="T:System.Net.IPAddress" /> instance.</summary>
      <param name="ipString">A string that contains an IP address in dotted-quad notation for IPv4 and in colon-hexadecimal notation for IPv6.</param>
      <returns>An <see cref="T:System.Net.IPAddress" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ipString" /> is <see langword="null" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="ipString" /> is not a valid IP address.</exception>
    </member>
    <member name="P:System.Net.IPAddress.ScopeId">
      <summary>Gets or sets the IPv6 address scope identifier.</summary>
      <returns>A long integer that specifies the scope of the address.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">
        <see langword="AddressFamily" /> = <see langword="InterNetwork" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="scopeId" /> &lt; 0
-or-

<paramref name="scopeId" /> &gt; 0x00000000FFFFFFFF</exception>
    </member>
    <member name="M:System.Net.IPAddress.ToString">
      <summary>Converts an Internet address to its standard notation.</summary>
      <returns>A string that contains the IP address in either IPv4 dotted-quad or in IPv6 colon-hexadecimal notation.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">The address family is <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" /> and the address is bad.</exception>
    </member>
    <member name="M:System.Net.IPAddress.TryFormat(System.Span{System.Char},System.Int32@)">
      <param name="destination" />
      <param name="charsWritten" />
    </member>
    <member name="M:System.Net.IPAddress.TryParse(System.ReadOnlySpan{System.Char},System.Net.IPAddress@)">
      <param name="ipString" />
      <param name="address" />
    </member>
    <member name="M:System.Net.IPAddress.TryParse(System.String,System.Net.IPAddress@)">
      <summary>Determines whether a string is a valid IP address.</summary>
      <param name="ipString">The string to validate.</param>
      <param name="address">The <see cref="T:System.Net.IPAddress" /> version of the string.</param>
      <returns>
        <see langword="true" /> if <paramref name="ipString" /> was able to be parsed as an IP address; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ipString" /> is null.</exception>
    </member>
    <member name="M:System.Net.IPAddress.TryWriteBytes(System.Span{System.Byte},System.Int32@)">
      <param name="destination" />
      <param name="bytesWritten" />
    </member>
    <member name="T:System.Net.IPEndPoint">
      <summary>Represents a network endpoint as an IP address and a port number.</summary>
    </member>
    <member name="M:System.Net.IPEndPoint.#ctor(System.Int64,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.IPEndPoint" /> class with the specified address and port number.</summary>
      <param name="address">The IP address of the Internet host.</param>
      <param name="port">The port number associated with the <paramref name="address" />, or 0 to specify any available port. <paramref name="port" /> is in host order.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> is less than <see cref="F:System.Net.IPEndPoint.MinPort" />.
-or-
<paramref name="port" /> is greater than <see cref="F:System.Net.IPEndPoint.MaxPort" />.
-or-
<paramref name="address" /> is less than 0 or greater than 0x00000000FFFFFFFF.</exception>
    </member>
    <member name="M:System.Net.IPEndPoint.#ctor(System.Net.IPAddress,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.IPEndPoint" /> class with the specified address and port number.</summary>
      <param name="address">An <see cref="T:System.Net.IPAddress" />.</param>
      <param name="port">The port number associated with the <paramref name="address" />, or 0 to specify any available port. <paramref name="port" /> is in host order.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="address" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="port" /> is less than <see cref="F:System.Net.IPEndPoint.MinPort" />.
-or-
<paramref name="port" /> is greater than <see cref="F:System.Net.IPEndPoint.MaxPort" />.
-or-
<paramref name="address" /> is less than 0 or greater than 0x00000000FFFFFFFF.</exception>
    </member>
    <member name="P:System.Net.IPEndPoint.Address">
      <summary>Gets or sets the IP address of the endpoint.</summary>
      <returns>An <see cref="T:System.Net.IPAddress" /> instance containing the IP address of the endpoint.</returns>
    </member>
    <member name="P:System.Net.IPEndPoint.AddressFamily">
      <summary>Gets the Internet Protocol (IP) address family.</summary>
      <returns>Returns <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" />.</returns>
    </member>
    <member name="M:System.Net.IPEndPoint.Create(System.Net.SocketAddress)">
      <summary>Creates an endpoint from a socket address.</summary>
      <param name="socketAddress">The <see cref="T:System.Net.SocketAddress" /> to use for the endpoint.</param>
      <returns>An <see cref="T:System.Net.EndPoint" /> instance using the specified socket address.</returns>
      <exception cref="T:System.ArgumentException">The AddressFamily of <paramref name="socketAddress" /> is not equal to the AddressFamily of the current instance.
-or-
<paramref name="socketAddress" />.Size &lt; 8.</exception>
    </member>
    <member name="M:System.Net.IPEndPoint.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Object" />.</summary>
      <param name="comparand">The <see cref="T:System.Object" /> to compare with the current <see cref="T:System.Object" />.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Object" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.IPEndPoint.GetHashCode">
      <summary>Returns a hash value for a <see cref="T:System.Net.IPEndPoint" /> instance.</summary>
      <returns>An integer hash value.</returns>
    </member>
    <member name="F:System.Net.IPEndPoint.MaxPort">
      <summary>Specifies the maximum value that can be assigned to the <see cref="P:System.Net.IPEndPoint.Port" /> property. The MaxPort value is set to 0x0000FFFF. This field is read-only.</summary>
    </member>
    <member name="F:System.Net.IPEndPoint.MinPort">
      <summary>Specifies the minimum value that can be assigned to the <see cref="P:System.Net.IPEndPoint.Port" /> property. This field is read-only.</summary>
    </member>
    <member name="M:System.Net.IPEndPoint.Parse(System.ReadOnlySpan{System.Char})">
      <summary>Converts an IP network endpoint (address and port) represented as a read-only span to an <see cref="T:System.Net.IPEndPoint" /> instance.</summary>
      <param name="s">A read-only span that contains an IP endpoint in dotted-quad notation or network byte order for IPv4 and in colon-hexadecimal notation for IPv6.</param>
      <returns>The object representation of an IP network endpoint.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is <see langword="null" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not a valid IP endpoint.</exception>
    </member>
    <member name="M:System.Net.IPEndPoint.Parse(System.String)">
      <summary>Converts an IP network endpoint (address and port) represented as a string to an <see cref="T:System.Net.IPEndPoint" /> instance.</summary>
      <param name="s">A string that contains an IP endpoint in dotted-quad notation or network byte order for IPv4 and in colon-hexadecimal notation for IPv6.</param>
      <returns>The object representation of an IP network endpoint.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is <see langword="null" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not a valid IP endpoint.</exception>
    </member>
    <member name="P:System.Net.IPEndPoint.Port">
      <summary>Gets or sets the port number of the endpoint.</summary>
      <returns>An integer value in the range <see cref="F:System.Net.IPEndPoint.MinPort" /> to <see cref="F:System.Net.IPEndPoint.MaxPort" /> indicating the port number of the endpoint.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value that was specified for a set operation is less than <see cref="F:System.Net.IPEndPoint.MinPort" /> or greater than <see cref="F:System.Net.IPEndPoint.MaxPort" />.</exception>
    </member>
    <member name="M:System.Net.IPEndPoint.Serialize">
      <summary>Serializes endpoint information into a <see cref="T:System.Net.SocketAddress" /> instance.</summary>
      <returns>A <see cref="T:System.Net.SocketAddress" /> instance containing the socket address for the endpoint.</returns>
    </member>
    <member name="M:System.Net.IPEndPoint.ToString">
      <summary>Returns the IP address and port number of the specified endpoint.</summary>
      <returns>A string containing the IP address and the port number of the specified endpoint (for example, ***********:80).</returns>
    </member>
    <member name="M:System.Net.IPEndPoint.TryParse(System.ReadOnlySpan{System.Char},System.Net.IPEndPoint@)">
      <summary>Tries to convert an IP network endpoint (address and port) represented as a read-only span to its <see cref="T:System.Net.IPEndPoint" /> equivalent, and returns a value that indicates whether the conversion succeeded.</summary>
      <param name="s">The IP endpoint to validate.</param>
      <param name="result">When this method returns, the <see cref="T:System.Net.IPEndPoint" /> version of <paramref name="s" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="s" /> can be parsed as an IP endpoint; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.IPEndPoint.TryParse(System.String,System.Net.IPEndPoint@)">
      <summary>Tries to convert an IP network endpoint (address and port) represented as a string to its <see cref="T:System.Net.IPEndPoint" /> equivalent, and returns a value that indicates whether the conversion succeeded.</summary>
      <param name="s">The IP endpoint to validate.</param>
      <param name="result">When this method returns, the <see cref="T:System.Net.IPEndPoint" /> version of <paramref name="s" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="s" /> can be parsed as an IP endpoint; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.IWebProxy">
      <summary>Provides the base interface for implementation of proxy access for the <see cref="T:System.Net.WebRequest" /> class.</summary>
    </member>
    <member name="P:System.Net.IWebProxy.Credentials">
      <summary>The credentials to submit to the proxy server for authentication.</summary>
      <returns>An <see cref="T:System.Net.ICredentials" /> instance that contains the credentials that are needed to authenticate a request to the proxy server.</returns>
    </member>
    <member name="M:System.Net.IWebProxy.GetProxy(System.Uri)">
      <summary>Returns the URI of a proxy.</summary>
      <param name="destination">A <see cref="T:System.Uri" /> that specifies the requested Internet resource.</param>
      <returns>A <see cref="T:System.Uri" /> instance that contains the URI of the proxy used to contact <paramref name="destination" />.</returns>
    </member>
    <member name="M:System.Net.IWebProxy.IsBypassed(System.Uri)">
      <summary>Indicates that the proxy should not be used for the specified host.</summary>
      <param name="host">The <see cref="T:System.Uri" /> of the host to check for proxy use.</param>
      <returns>
        <see langword="true" /> if the proxy server should not be used for <paramref name="host" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Net.NetworkCredential">
      <summary>Provides credentials for password-based authentication schemes such as basic, digest, NTLM, and Kerberos authentication.</summary>
    </member>
    <member name="M:System.Net.NetworkCredential.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.NetworkCredential" /> class.</summary>
    </member>
    <member name="M:System.Net.NetworkCredential.#ctor(System.String,System.Security.SecureString)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.NetworkCredential" /> class with the specified user name and password.</summary>
      <param name="userName">The user name associated with the credentials.</param>
      <param name="password">The password for the user name associated with the credentials.</param>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Security.SecureString" /> class is not supported on this platform.</exception>
    </member>
    <member name="M:System.Net.NetworkCredential.#ctor(System.String,System.Security.SecureString,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.NetworkCredential" /> class with the specified user name, password, and domain.</summary>
      <param name="userName">The user name associated with the credentials.</param>
      <param name="password">The password for the user name associated with the credentials.</param>
      <param name="domain">The domain associated with these credentials.</param>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Security.SecureString" /> class is not supported on this platform.</exception>
    </member>
    <member name="M:System.Net.NetworkCredential.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.NetworkCredential" /> class with the specified user name and password.</summary>
      <param name="userName">The user name associated with the credentials.</param>
      <param name="password">The password for the user name associated with the credentials.</param>
    </member>
    <member name="M:System.Net.NetworkCredential.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.NetworkCredential" /> class with the specified user name, password, and domain.</summary>
      <param name="userName">The user name associated with the credentials.</param>
      <param name="password">The password for the user name associated with the credentials.</param>
      <param name="domain">The domain associated with these credentials.</param>
    </member>
    <member name="P:System.Net.NetworkCredential.Domain">
      <summary>Gets or sets the domain or computer name that verifies the credentials.</summary>
      <returns>The name of the domain associated with the credentials.</returns>
    </member>
    <member name="M:System.Net.NetworkCredential.GetCredential(System.String,System.Int32,System.String)">
      <summary>Returns an instance of the <see cref="T:System.Net.NetworkCredential" /> class for the specified host, port, and authentication type.</summary>
      <param name="host">The host computer that authenticates the client.</param>
      <param name="port">The port on the <paramref name="host" /> that the client communicates with.</param>
      <param name="authenticationType">The type of authentication requested, as defined in the <see cref="P:System.Net.IAuthenticationModule.AuthenticationType" /> property.</param>
      <returns>A <see cref="T:System.Net.NetworkCredential" /> for the specified host, port, and authentication protocol, or <see langword="null" /> if there are no credentials available for the specified host, port, and authentication protocol.</returns>
    </member>
    <member name="M:System.Net.NetworkCredential.GetCredential(System.Uri,System.String)">
      <summary>Returns an instance of the <see cref="T:System.Net.NetworkCredential" /> class for the specified Uniform Resource Identifier (URI) and authentication type.</summary>
      <param name="uri">The URI that the client provides authentication for.</param>
      <param name="authType">The type of authentication requested, as defined in the <see cref="P:System.Net.IAuthenticationModule.AuthenticationType" /> property.</param>
      <returns>A <see cref="T:System.Net.NetworkCredential" /> object.</returns>
    </member>
    <member name="P:System.Net.NetworkCredential.Password">
      <summary>Gets or sets the password for the user name associated with the credentials.</summary>
      <returns>The password associated with the credentials. If this <see cref="T:System.Net.NetworkCredential" /> instance was initialized with the <paramref name="password" /> parameter set to <see langword="null" />, then the <see cref="P:System.Net.NetworkCredential.Password" /> property will return an empty string.</returns>
    </member>
    <member name="P:System.Net.NetworkCredential.SecurePassword">
      <summary>Gets or sets the password as a <see cref="T:System.Security.SecureString" /> instance.</summary>
      <returns>The password for the user name associated with the credentials.</returns>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Security.SecureString" /> class is not supported on this platform.</exception>
    </member>
    <member name="P:System.Net.NetworkCredential.UserName">
      <summary>Gets or sets the user name associated with the credentials.</summary>
      <returns>The user name associated with the credentials.</returns>
    </member>
    <member name="T:System.Net.NetworkInformation.IPAddressCollection">
      <summary>Stores a set of <see cref="T:System.Net.IPAddress" /> types.</summary>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.NetworkInformation.IPAddressCollection" /> class.</summary>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.Add(System.Net.IPAddress)">
      <summary>Throws a <see cref="T:System.NotSupportedException" /> because this operation is not supported for this collection.</summary>
      <param name="address">The object to be added to the collection.</param>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.Clear">
      <summary>Throws a <see cref="T:System.NotSupportedException" /> because this operation is not supported for this collection.</summary>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.Contains(System.Net.IPAddress)">
      <summary>Checks whether the collection contains the specified <see cref="T:System.Net.IPAddress" /> object.</summary>
      <param name="address">The <see cref="T:System.Net.IPAddress" /> object to be searched in the collection.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.IPAddress" /> object exists in the collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.CopyTo(System.Net.IPAddress[],System.Int32)">
      <summary>Copies the elements in this collection to a one-dimensional array of type <see cref="T:System.Net.IPAddress" />.</summary>
      <param name="array">A one-dimensional array that receives a copy of the collection.</param>
      <param name="offset">The zero-based index in <paramref name="array" /> at which the copy begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.

-or-
The number of elements in this <see cref="T:System.Net.NetworkInformation.IPAddressCollection" /> is greater than the available space from <paramref name="offset" /> to the end of the destination <paramref name="array" />.</exception>
      <exception cref="T:System.InvalidCastException">The elements in this <see cref="T:System.Net.NetworkInformation.IPAddressCollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Net.NetworkInformation.IPAddressCollection.Count">
      <summary>Gets the number of <see cref="T:System.Net.IPAddress" /> types in this collection.</summary>
      <returns>An <see cref="T:System.Int32" /> value that contains the number of <see cref="T:System.Net.IPAddress" /> types in this collection.</returns>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.GetEnumerator">
      <summary>Returns an object that can be used to iterate through this collection.</summary>
      <returns>An object that implements the <see cref="T:System.Collections.IEnumerator" /> interface and provides access to the <see cref="T:System.Net.NetworkInformation.IPAddressCollection" /> types in this collection.</returns>
    </member>
    <member name="P:System.Net.NetworkInformation.IPAddressCollection.IsReadOnly">
      <summary>Gets a value that indicates whether access to this collection is read-only.</summary>
      <returns>
        <see langword="true" /> in all cases.</returns>
    </member>
    <member name="P:System.Net.NetworkInformation.IPAddressCollection.Item(System.Int32)">
      <summary>Gets the <see cref="T:System.Net.IPAddress" /> at the specific index of the collection.</summary>
      <param name="index">The index of interest.</param>
      <returns>The <see cref="T:System.Net.IPAddress" /> at the specific index in the collection.</returns>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.Remove(System.Net.IPAddress)">
      <summary>Throws a <see cref="T:System.NotSupportedException" /> because this operation is not supported for this collection.</summary>
      <param name="address">The object to be removed.</param>
      <returns>Always throws a <see cref="T:System.NotSupportedException" />.</returns>
    </member>
    <member name="M:System.Net.NetworkInformation.IPAddressCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an object that can be used to iterate through this collection.</summary>
      <returns>An object that implements the <see cref="T:System.Collections.IEnumerator" /> interface and provides access to the <see cref="T:System.Net.NetworkInformation.IPAddressCollection" /> types in this collection.</returns>
    </member>
    <member name="T:System.Net.Security.AuthenticationLevel">
      <summary>Specifies client requirements for authentication and impersonation when using the <see cref="T:System.Net.WebRequest" /> class and derived classes to request a resource.</summary>
    </member>
    <member name="F:System.Net.Security.AuthenticationLevel.MutualAuthRequested">
      <summary>The client and server should be authenticated. The request does not fail if the server is not authenticated. To determine whether mutual authentication occurred, check the value of the <see cref="P:System.Net.WebResponse.IsMutuallyAuthenticated" /> property.</summary>
    </member>
    <member name="F:System.Net.Security.AuthenticationLevel.MutualAuthRequired">
      <summary>The client and server should be authenticated. If the server is not authenticated, your application will receive an <see cref="T:System.IO.IOException" /> with a <see cref="T:System.Net.ProtocolViolationException" /> inner exception that indicates that mutual authentication failed.</summary>
    </member>
    <member name="F:System.Net.Security.AuthenticationLevel.None">
      <summary>No authentication is required for the client and server.</summary>
    </member>
    <member name="T:System.Net.Security.SslPolicyErrors">
      <summary>Enumerates Secure Socket Layer (SSL) policy errors.</summary>
    </member>
    <member name="F:System.Net.Security.SslPolicyErrors.None">
      <summary>No SSL policy errors.</summary>
    </member>
    <member name="F:System.Net.Security.SslPolicyErrors.RemoteCertificateChainErrors">
      <summary>
        <see cref="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus" /> has returned a non empty array.</summary>
    </member>
    <member name="F:System.Net.Security.SslPolicyErrors.RemoteCertificateNameMismatch">
      <summary>Certificate name mismatch.</summary>
    </member>
    <member name="F:System.Net.Security.SslPolicyErrors.RemoteCertificateNotAvailable">
      <summary>Certificate not available.</summary>
    </member>
    <member name="T:System.Net.SocketAddress">
      <summary>Stores serialized information from <see cref="T:System.Net.EndPoint" /> derived classes.</summary>
    </member>
    <member name="M:System.Net.SocketAddress.#ctor(System.Net.Sockets.AddressFamily)">
      <summary>Creates a new instance of the <see cref="T:System.Net.SocketAddress" /> class for the given address family.</summary>
      <param name="family">An <see cref="T:System.Net.Sockets.AddressFamily" /> enumerated value.</param>
    </member>
    <member name="M:System.Net.SocketAddress.#ctor(System.Net.Sockets.AddressFamily,System.Int32)">
      <summary>Creates a new instance of the <see cref="T:System.Net.SocketAddress" /> class using the specified address family and buffer size.</summary>
      <param name="family">An <see cref="T:System.Net.Sockets.AddressFamily" /> enumerated value.</param>
      <param name="size">The number of bytes to allocate for the underlying buffer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="size" /> is less than 2. These 2 bytes are needed to store <paramref name="family" />.</exception>
    </member>
    <member name="M:System.Net.SocketAddress.Equals(System.Object)">
      <summary>Determines whether the specified <see langword="Object" /> is equal to the current <see langword="Object" />.</summary>
      <param name="comparand">The <see cref="T:System.Object" /> to compare with the current <see langword="Object" />.</param>
      <returns>
        <see langword="true" /> if the specified <see langword="Object" /> is equal to the current <see langword="Object" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.SocketAddress.Family">
      <summary>Gets the <see cref="T:System.Net.Sockets.AddressFamily" /> enumerated value of the current <see cref="T:System.Net.SocketAddress" />.</summary>
      <returns>One of the <see cref="T:System.Net.Sockets.AddressFamily" /> enumerated values.</returns>
    </member>
    <member name="M:System.Net.SocketAddress.GetHashCode">
      <summary>Serves as a hash function for a particular type, suitable for use in hashing algorithms and data structures like a hash table.</summary>
      <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
    </member>
    <member name="P:System.Net.SocketAddress.Item(System.Int32)">
      <summary>Gets or sets the specified index element in the underlying buffer.</summary>
      <param name="offset">The array index element of the desired information.</param>
      <returns>The value of the specified index element in the underlying buffer.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The specified index does not exist in the buffer.</exception>
    </member>
    <member name="P:System.Net.SocketAddress.Size">
      <summary>Gets the underlying buffer size of the <see cref="T:System.Net.SocketAddress" />.</summary>
      <returns>The underlying buffer size of the <see cref="T:System.Net.SocketAddress" />.</returns>
    </member>
    <member name="M:System.Net.SocketAddress.ToString">
      <summary>Returns information about the socket address.</summary>
      <returns>A string that contains information about the <see cref="T:System.Net.SocketAddress" />.</returns>
    </member>
    <member name="T:System.Net.Sockets.AddressFamily">
      <summary>Specifies the addressing scheme that an instance of the <see cref="T:System.Net.Sockets.Socket" /> class can use.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.AppleTalk">
      <summary>AppleTalk address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Atm">
      <summary>Native ATM services address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Banyan">
      <summary>Banyan address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Ccitt">
      <summary>Addresses for CCITT protocols, such as X.25.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Chaos">
      <summary>Address for MIT CHAOS protocols.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Cluster">
      <summary>Address for Microsoft cluster products.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.ControllerAreaNetwork">
      <summary>Controller Area Network address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.DataKit">
      <summary>Address for Datakit protocols.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.DataLink">
      <summary>Direct data-link interface address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.DecNet">
      <summary>DECnet address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Ecma">
      <summary>European Computer Manufacturers Association (ECMA) address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.FireFox">
      <summary>FireFox address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.HyperChannel">
      <summary>NSC Hyperchannel address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Ieee12844">
      <summary>IEEE 1284.4 workgroup address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.ImpLink">
      <summary>ARPANET IMP address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.InterNetwork">
      <summary>Address for IP version 4.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.InterNetworkV6">
      <summary>Address for IP version 6.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Ipx">
      <summary>IPX or SPX address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Irda">
      <summary>IrDA address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Iso">
      <summary>Address for ISO protocols.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Lat">
      <summary>LAT address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Max">
      <summary>MAX address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.NetBios">
      <summary>NetBios address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.NetworkDesigners">
      <summary>Address for Network Designers OSI gateway-enabled protocols.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.NS">
      <summary>Address for Xerox NS protocols.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Osi">
      <summary>Address for OSI protocols.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Packet">
      <summary>Low-level Packet address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Pup">
      <summary>Address for PUP protocols.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Sna">
      <summary>IBM SNA address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Unix">
      <summary>Unix local to host address.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Unknown">
      <summary>Unknown address family.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.Unspecified">
      <summary>Unspecified address family.</summary>
    </member>
    <member name="F:System.Net.Sockets.AddressFamily.VoiceView">
      <summary>VoiceView address.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketError">
      <summary>Defines error codes for the <see cref="T:System.Net.Sockets.Socket" /> class.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.AccessDenied">
      <summary>An attempt was made to access a <see cref="T:System.Net.Sockets.Socket" /> in a way that is forbidden by its access permissions.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.AddressAlreadyInUse">
      <summary>Only one use of an address is normally permitted.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.AddressFamilyNotSupported">
      <summary>The address family specified is not supported. This error is returned if the IPv6 address family was specified and the IPv6 stack is not installed on the local machine. This error is returned if the IPv4 address family was specified and the IPv4 stack is not installed on the local machine.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.AddressNotAvailable">
      <summary>The selected IP address is not valid in this context.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.AlreadyInProgress">
      <summary>The nonblocking <see cref="T:System.Net.Sockets.Socket" /> already has an operation in progress.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ConnectionAborted">
      <summary>The connection was aborted by the .NET Framework or the underlying socket provider.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ConnectionRefused">
      <summary>The remote host is actively refusing a connection.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ConnectionReset">
      <summary>The connection was reset by the remote peer.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.DestinationAddressRequired">
      <summary>A required address was omitted from an operation on a <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.Disconnecting">
      <summary>A graceful shutdown is in progress.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.Fault">
      <summary>An invalid pointer address was detected by the underlying socket provider.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.HostDown">
      <summary>The operation failed because the remote host is down.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.HostNotFound">
      <summary>No such host is known. The name is not an official host name or alias.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.HostUnreachable">
      <summary>There is no network route to the specified host.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.InProgress">
      <summary>A blocking operation is in progress.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.Interrupted">
      <summary>A blocking <see cref="T:System.Net.Sockets.Socket" /> call was canceled.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.InvalidArgument">
      <summary>An invalid argument was supplied to a <see cref="T:System.Net.Sockets.Socket" /> member.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.IOPending">
      <summary>The application has initiated an overlapped operation that cannot be completed immediately.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.IsConnected">
      <summary>The <see cref="T:System.Net.Sockets.Socket" /> is already connected.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.MessageSize">
      <summary>The datagram is too long.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NetworkDown">
      <summary>The network is not available.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NetworkReset">
      <summary>The application tried to set <see cref="F:System.Net.Sockets.SocketOptionName.KeepAlive" /> on a connection that has already timed out.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NetworkUnreachable">
      <summary>No route to the remote host exists.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NoBufferSpaceAvailable">
      <summary>No free buffer space is available for a <see cref="T:System.Net.Sockets.Socket" /> operation.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NoData">
      <summary>The requested name or IP address was not found on the name server.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NoRecovery">
      <summary>The error is unrecoverable or the requested database cannot be located.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NotConnected">
      <summary>The application tried to send or receive data, and the <see cref="T:System.Net.Sockets.Socket" /> is not connected.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NotInitialized">
      <summary>The underlying socket provider has not been initialized.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.NotSocket">
      <summary>A <see cref="T:System.Net.Sockets.Socket" /> operation was attempted on a non-socket.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.OperationAborted">
      <summary>The overlapped operation was aborted due to the closure of the <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.OperationNotSupported">
      <summary>The address family is not supported by the protocol family.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ProcessLimit">
      <summary>Too many processes are using the underlying socket provider.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ProtocolFamilyNotSupported">
      <summary>The protocol family is not implemented or has not been configured.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ProtocolNotSupported">
      <summary>The protocol is not implemented or has not been configured.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ProtocolOption">
      <summary>An unknown, invalid, or unsupported option or level was used with a <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.ProtocolType">
      <summary>The protocol type is incorrect for this <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.Shutdown">
      <summary>A request to send or receive data was disallowed because the <see cref="T:System.Net.Sockets.Socket" /> has already been closed.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.SocketError">
      <summary>An unspecified <see cref="T:System.Net.Sockets.Socket" /> error has occurred.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.SocketNotSupported">
      <summary>The support for the specified socket type does not exist in this address family.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.Success">
      <summary>The <see cref="T:System.Net.Sockets.Socket" /> operation succeeded.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.SystemNotReady">
      <summary>The network subsystem is unavailable.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.TimedOut">
      <summary>The connection attempt timed out, or the connected host has failed to respond.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.TooManyOpenSockets">
      <summary>There are too many open sockets in the underlying socket provider.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.TryAgain">
      <summary>The name of the host could not be resolved. Try again later.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.TypeNotFound">
      <summary>The specified class was not found.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.VersionNotSupported">
      <summary>The version of the underlying socket provider is out of range.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketError.WouldBlock">
      <summary>An operation on a nonblocking socket cannot be completed immediately.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketException">
      <summary>The exception that is thrown when a socket error occurs.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SocketException" /> class with the last operating system error code.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketException.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SocketException" /> class with the specified error code.</summary>
      <param name="errorCode">The error code that indicates the error that occurred.</param>
    </member>
    <member name="M:System.Net.Sockets.SocketException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.Sockets.SocketException" /> class from the specified instances of the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" /> classes.</summary>
      <param name="serializationInfo">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> instance that contains the information that is required to serialize the new <see cref="T:System.Net.Sockets.SocketException" /> instance.</param>
      <param name="streamingContext">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains the source of the serialized stream that is associated with the new <see cref="T:System.Net.Sockets.SocketException" /> instance.</param>
    </member>
    <member name="P:System.Net.Sockets.SocketException.ErrorCode">
      <summary>Gets the error code that is associated with this exception.</summary>
      <returns>An integer error code that is associated with this exception.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketException.Message">
      <summary>Gets the error message that is associated with this exception.</summary>
      <returns>A string that contains the error message.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketException.SocketErrorCode">
      <summary>Gets the error code that is associated with this exception.</summary>
      <returns>An integer error code that is associated with this exception.</returns>
    </member>
    <member name="T:System.Net.TransportContext">
      <summary>The <see cref="T:System.Net.TransportContext" /> class provides additional context about the underlying transport layer.</summary>
    </member>
    <member name="M:System.Net.TransportContext.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Net.TransportContext" /> class.</summary>
    </member>
    <member name="M:System.Net.TransportContext.GetChannelBinding(System.Security.Authentication.ExtendedProtection.ChannelBindingKind)">
      <summary>Retrieves the requested channel binding.</summary>
      <param name="kind">The type of channel binding to retrieve.</param>
      <returns>The requested <see cref="T:System.Security.Authentication.ExtendedProtection.ChannelBinding" />, or <see langword="null" /> if the channel binding is not supported by the current transport or by the operating system.</returns>
      <exception cref="T:System.NotSupportedException">
        <paramref name="kind" /> is must be <see cref="F:System.Security.Authentication.ExtendedProtection.ChannelBindingKind.Endpoint" /> for use with the <see cref="T:System.Net.TransportContext" /> retrieved from the <see cref="P:System.Net.HttpListenerRequest.TransportContext" /> property.</exception>
    </member>
    <member name="T:System.Security.Authentication.CipherAlgorithmType">
      <summary>Defines the possible cipher algorithms for the <see cref="T:System.Net.Security.SslStream" /> class.</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Aes">
      <summary>The Advanced Encryption Standard (AES) algorithm.</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Aes128">
      <summary>The Advanced Encryption Standard (AES) algorithm with a 128 bit key.</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Aes192">
      <summary>The Advanced Encryption Standard (AES) algorithm with a 192 bit key.</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Aes256">
      <summary>The Advanced Encryption Standard (AES) algorithm with a 256 bit key.</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Des">
      <summary>The Data Encryption Standard (DES) algorithm.</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.None">
      <summary>No encryption algorithm is used.</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Null">
      <summary>No encryption is used with a Null cipher algorithm.</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Rc2">
      <summary>Rivest's Code 2 (RC2) algorithm.</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.Rc4">
      <summary>Rivest's Code 4 (RC4) algorithm.</summary>
    </member>
    <member name="F:System.Security.Authentication.CipherAlgorithmType.TripleDes">
      <summary>The Triple Data Encryption Standard (3DES) algorithm.</summary>
    </member>
    <member name="T:System.Security.Authentication.ExchangeAlgorithmType">
      <summary>Specifies the algorithm used to create keys shared by the client and server.</summary>
    </member>
    <member name="F:System.Security.Authentication.ExchangeAlgorithmType.DiffieHellman">
      <summary>The Diffie Hellman ephemeral key exchange algorithm.</summary>
    </member>
    <member name="F:System.Security.Authentication.ExchangeAlgorithmType.None">
      <summary>No key exchange algorithm is used.</summary>
    </member>
    <member name="F:System.Security.Authentication.ExchangeAlgorithmType.RsaKeyX">
      <summary>The RSA public-key exchange algorithm.</summary>
    </member>
    <member name="F:System.Security.Authentication.ExchangeAlgorithmType.RsaSign">
      <summary>The RSA public-key signature algorithm.</summary>
    </member>
    <member name="T:System.Security.Authentication.ExtendedProtection.ChannelBinding">
      <summary>The <see cref="T:System.Security.Authentication.ExtendedProtection.ChannelBinding" /> class encapsulates a pointer to the opaque data used to bind an authenticated transaction to a secure channel.</summary>
    </member>
    <member name="M:System.Security.Authentication.ExtendedProtection.ChannelBinding.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Authentication.ExtendedProtection.ChannelBinding" /> class.</summary>
    </member>
    <member name="M:System.Security.Authentication.ExtendedProtection.ChannelBinding.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Authentication.ExtendedProtection.ChannelBinding" /> class.</summary>
      <param name="ownsHandle">A Boolean value that indicates if the application owns the safe handle to a native memory region containing the byte data that would be passed to native calls that provide extended protection for integrated windows authentication.</param>
    </member>
    <member name="P:System.Security.Authentication.ExtendedProtection.ChannelBinding.Size">
      <summary>The <see cref="P:System.Security.Authentication.ExtendedProtection.ChannelBinding.Size" /> property gets the size, in bytes, of the channel binding token associated with the <see cref="T:System.Security.Authentication.ExtendedProtection.ChannelBinding" /> instance.</summary>
      <returns>The size, in bytes, of the channel binding token in the <see cref="T:System.Security.Authentication.ExtendedProtection.ChannelBinding" /> instance.</returns>
    </member>
    <member name="T:System.Security.Authentication.ExtendedProtection.ChannelBindingKind">
      <summary>The <see cref="T:System.Security.Authentication.ExtendedProtection.ChannelBindingKind" /> enumeration represents the kinds of channel bindings that can be queried from secure channels.</summary>
    </member>
    <member name="F:System.Security.Authentication.ExtendedProtection.ChannelBindingKind.Endpoint">
      <summary>A channel binding unique to a given endpoint (a TLS server certificate, for example).</summary>
    </member>
    <member name="F:System.Security.Authentication.ExtendedProtection.ChannelBindingKind.Unique">
      <summary>A channel binding completely unique to a given channel (a TLS session key, for example).</summary>
    </member>
    <member name="F:System.Security.Authentication.ExtendedProtection.ChannelBindingKind.Unknown">
      <summary>An unknown channel binding type.</summary>
    </member>
    <member name="T:System.Security.Authentication.HashAlgorithmType">
      <summary>Specifies the algorithm used for generating message authentication codes (MACs).</summary>
    </member>
    <member name="F:System.Security.Authentication.HashAlgorithmType.Md5">
      <summary>The Message Digest 5 (MD5) hashing algorithm.</summary>
    </member>
    <member name="F:System.Security.Authentication.HashAlgorithmType.None">
      <summary>No hashing algorithm is used.</summary>
    </member>
    <member name="F:System.Security.Authentication.HashAlgorithmType.Sha1">
      <summary>The Secure Hashing Algorithm (SHA1).</summary>
    </member>
    <member name="F:System.Security.Authentication.HashAlgorithmType.Sha256">
      <summary>The Secure Hashing Algorithm 2 (SHA-2), using a 256-bit digest.</summary>
    </member>
    <member name="F:System.Security.Authentication.HashAlgorithmType.Sha384">
      <summary>The Secure Hashing Algorithm 2 (SHA-2), using a 384-bit digest.</summary>
    </member>
    <member name="F:System.Security.Authentication.HashAlgorithmType.Sha512">
      <summary>The Secure Hashing Algorithm 2 (SHA-2), using a 512-bit digest.</summary>
    </member>
    <member name="T:System.Security.Authentication.SslProtocols">
      <summary>Defines the possible versions of <see cref="T:System.Security.Authentication.SslProtocols" />.</summary>
    </member>
    <member name="F:System.Security.Authentication.SslProtocols.Default">
      <summary>Use None instead of Default. Default permits only the Secure Sockets Layer (SSL) 3.0 or Transport Layer Security (TLS) 1.0 protocols to be negotiated, and those options are now considered obsolete. Consequently, Default is not allowed in many organizations. Despite the name of this field, <see cref="T:System.Net.Security.SslStream" /> does not use it as a default except under special circumstances.</summary>
    </member>
    <member name="F:System.Security.Authentication.SslProtocols.None">
      <summary>Allows the operating system to choose the best protocol to use, and to block protocols that are not secure. Unless your app has a specific reason not to, you should use this field.</summary>
    </member>
    <member name="F:System.Security.Authentication.SslProtocols.Ssl2">
      <summary>Specifies the SSL 2.0 protocol. SSL 2.0 has been superseded by the TLS protocol and is provided for backward compatibility only.</summary>
    </member>
    <member name="F:System.Security.Authentication.SslProtocols.Ssl3">
      <summary>Specifies the SSL 3.0 protocol. SSL 3.0 has been superseded by the TLS protocol and is provided for backward compatibility only.</summary>
    </member>
    <member name="F:System.Security.Authentication.SslProtocols.Tls">
      <summary>Specifies the TLS 1.0 security protocol. The TLS protocol is defined in IETF RFC 2246.</summary>
    </member>
    <member name="F:System.Security.Authentication.SslProtocols.Tls11">
      <summary>Specifies the TLS 1.1 security protocol. The TLS protocol is defined in IETF RFC 4346.</summary>
    </member>
    <member name="F:System.Security.Authentication.SslProtocols.Tls12">
      <summary>Specifies the TLS 1.2 security protocol. The TLS protocol is defined in IETF RFC 5246.</summary>
    </member>
    <member name="F:System.Security.Authentication.SslProtocols.Tls13">
      <summary>Specifies the TLS 1.3 security protocol. The TLS protocol is defined in IETF RFC 8446.</summary>
    </member>
  </members>
</doc>