﻿using Coldairarrow.Business.HR_DataDictionaryManage;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.HR_EmployeeInfoManage
{
    [Route("/HR_EmployeeInfoManage/[controller]/[action]")]
    public class HR_TransferInfoController : BaseApiController
    {
        #region DI

        public HR_TransferInfoController(IHR_TransferInfoBusiness hR_TransferInfoBus, IHR_DataDictionaryDetailsBusiness hR_DataDictionaryDetailsBus, IConfiguration configuration, IHR_FormalEmployeesBusiness hR_FormalEmployeesBus)
        {
            _hR_TransferInfoBus = hR_TransferInfoBus;
            _configuration = configuration;
            _hR_DataDictionaryDetailsBus = hR_DataDictionaryDetailsBus;
            _hR_FormalEmployeesBus = hR_FormalEmployeesBus;
        }

        IHR_TransferInfoBusiness _hR_TransferInfoBus { get; }
        readonly IConfiguration _configuration;
        IHR_DataDictionaryDetailsBusiness _hR_DataDictionaryDetailsBus { get; }
        IHR_FormalEmployeesBusiness _hR_FormalEmployeesBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public AjaxResult GetInit()
        {
            var changesOperatingList = _hR_DataDictionaryDetailsBus.GetDetailList("调动变动操作", "");
            var changesTypeList = _hR_DataDictionaryDetailsBus.GetDetailList("调动变动类型", "");
            var mobilizeRankList = _hR_DataDictionaryDetailsBus.GetDetailList("职级", "");
            var data = new
            {
                wfStates = EnumHelper.ToOptionList(typeof(WFStates)),
                changesOperatingList,
                changesTypeList,
                mobilizeRankList
            };
            return Success(data);
        }

        [HttpPost]
        public async Task<PageResult<HR_TransferInfoDTO>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_TransferInfoBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_TransferInfo> GetTheData(IdInputDTO input)
        {
            return await _hR_TransferInfoBus.GetTheDataAsync(input.id);
        }

        /// <summary>
        /// 根据ID获取调动信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public HR_TransferInfoDTO GetTransferInfo(IdInputDTO input)
        {
            return _hR_TransferInfoBus.GetTransferInfo(input.id);
        }

        #endregion

        #region 提交

        /// <summary>
        /// 流程回调
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult FlowCallBack(FlowInputDTO input)
        {
            _hR_TransferInfoBus.FlowCallBack(input);

            return Success();
        }

        /// <summary>
        /// 保存并创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult SaveAndCreateFlow(HR_TransferInfoDTO data)
        {
            var ret = _hR_TransferInfoBus.CreateFlow(data, _configuration["OAUrl"] + "rest/archives/", GetOperator());
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("创建流程失败");
            }
        }

        [HttpPost]
        public async Task SaveData(HR_TransferInfo data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);
                data.F_WFState = (int)WFStates.草稿;

                await _hR_TransferInfoBus.AddDataAsync(data);
            }
            else
            {
                await _hR_TransferInfoBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_TransferInfoBus.DeleteDataAsync(ids);
        }

        /// <summary>
        /// 保存并创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ActWorkflow(HR_TransferInfoDTO data)
        {
            var ret = _hR_TransferInfoBus.ActWorkflow(data, _configuration["OAUrl"] + "rest/archives/", GetOperator());
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("提交流程失败");
            }
        }
        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ArchiveWorkflow(HR_TransferInfoDTO data)
        {
            var ret = _hR_TransferInfoBus.ArchiveWorkflow(data, _configuration["OAUrl"] + "rest/archives/");
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("强制归档失败");
            }
        }
        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<AskLeaveConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_TransferInfoBus.GetExcelListAsync(input);
                if (exportTable != null && exportTable.Rows.Count > 0)
                {
                    var ChangesOperatingList = _hR_DataDictionaryDetailsBus.GetDetailList("调动变动操作", "");
                    var ChangesTypeList = _hR_DataDictionaryDetailsBus.GetDetailList("调动变动类型", "");
                    var rankList = _hR_DataDictionaryDetailsBus.GetDetailList("职级", "");
                    exportTable.Columns.Add("changesoperating");
                    exportTable.Columns.Add("changestype");
                    exportTable.Columns.Add("mobilizerank");
                    exportTable.Columns.Add("wfstate");
                    //exportTable.Columns.Add("starttime");
                    for (int i = 0; i < exportTable.Rows.Count; i++)
                    {
                        var mobilizerank = exportTable.Rows[i]["F_MobilizeRank"];
                        if (mobilizerank != null && !mobilizerank.ToString().IsNullOrEmpty())
                        {
                            var mobilizerankStr = rankList.FirstOrDefault(x => x.F_ItemValue == mobilizerank.ToString())?.F_ItemName;
                            exportTable.Rows[i]["mobilizerank"] = mobilizerankStr;
                        }
                        var changesoperating = exportTable.Rows[i]["F_ChangesOperating"];
                        if (changesoperating != null && !changesoperating.ToString().IsNullOrEmpty())
                        {
                            var changesoperatingStr = ChangesOperatingList.FirstOrDefault(x => x.F_ItemValue == changesoperating.ToString())?.F_ItemName;
                            exportTable.Rows[i]["changesoperating"] = changesoperatingStr;
                        }
                        var changestype = exportTable.Rows[i]["F_ChangesType"];
                        if (changestype != null && !changestype.ToString().IsNullOrEmpty())
                        {
                            var askTypeStr = ChangesTypeList.FirstOrDefault(x => x.F_ItemValue == changestype.ToString())?.F_ItemName;
                            exportTable.Rows[i]["changestype"] = askTypeStr;
                        }
                        var wfstate = exportTable.Rows[i]["F_WFState"];
                        if (wfstate != null && !wfstate.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["wfstate"] = Enum.Parse(typeof(WFStates), wfstate.ToString()).ToString();
                        }
                    }
                }
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "调动";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                int sort = 0;
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "empcode", ExcelColumn = "员工编码", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "empname", ExcelColumn = "姓名", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_department", ExcelColumn = "调动前部门", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_currentposition", ExcelColumn = "调动前职位", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_currentrank", ExcelColumn = "调动前职级", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_mobilizedep", ExcelColumn = "调动后部门", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_mobilizeposition", ExcelColumn = "调动后职位", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "mobilizerank", ExcelColumn = "调动后职级", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_changesdate", ExcelColumn = "变动生效时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "changesoperating", ExcelColumn = "变动操作", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "changestype", ExcelColumn = "变动类型", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "wfstate", ExcelColumn = "流程状态", Alignment = "left", Sort = sort++ });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

    }
}