﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using Coldairarrow.Entity;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HR_EmployeeInfoManage
{
    public class HR_DisciplinaryRecordsBusiness : BaseBusiness<HR_DisciplinaryRecords>, IHR_DisciplinaryRecordsBusiness, ITransientDependency
    {
        readonly IMapper _mapper;
        public HR_DisciplinaryRecordsBusiness(IDbAccessor db, IMapper mapper)
            : base(db)
        {
            _mapper = mapper;
        }

        #region 外部接口

        public async Task<PageResult<HR_DisciplinaryRecordsDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_DisciplinaryRecords, HR_FormalEmployees, HR_DisciplinaryRecordsDTO>> select = (e, d) => new HR_DisciplinaryRecordsDTO
            {
                UserName = d.NameUser,
                EmployeesCode = d.EmployeesCode,
                UserId = e.UserId,
                F_Id = e.F_Id,
                DisciplinaryType = e.DisciplinaryType,
                DisciplinaryContent = e.DisciplinaryContent,
                Remark = e.Remark
            };
            select = select.BuildExtendSelectExpre();
            var q = from e in this.Db.GetIQueryable<HR_DisciplinaryRecords>().AsExpandable()
                    join f in this.Db.GetIQueryable<HR_FormalEmployees>() on e.UserId equals f.F_Id into dep
                    from depa in dep.DefaultIfEmpty()
                    select @select.Invoke(e, depa);
            var d = q.ToList();
            var where = LinqHelper.True<HR_DisciplinaryRecordsDTO>();
            var search = input.Search;
            //筛选
            if (!search.F_Id.IsNullOrEmpty())
            {
                where = where.And(i => i.UserId.Contains(search.F_Id));
            }
            //按年份筛选
            if (search.IsProbation.HasValue)
            {
                var starttime = new DateTime(search.IsProbation.Value, 1, 1);
                var endtime = new DateTime(search.IsProbation.Value, 12, 31);
                //q = q.Where(i => i.DisciplinaryStartTime.Value >= starttime && i.DisciplinaryStartTime.Value <= endtime);
                where = where.And(i => i.DisciplinaryStartTime>=starttime&&i.DisciplinaryStartTime<=endtime);
            }
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_DisciplinaryRecordsDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            return await q.Where(i => i.UserId != null && i.UserName != null).Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_DisciplinaryRecords> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        public async Task<HR_DisciplinaryRecordsDTO> GetFormDataAsync(string id)
        {
            var hR_DisciplinaryRecords = await GetEntityAsync(id);
            var disciplinaryRecordsDTO = MapHelper.Mapping<HR_DisciplinaryRecordsDTO, HR_DisciplinaryRecords>(hR_DisciplinaryRecords);
            if (disciplinaryRecordsDTO != null && disciplinaryRecordsDTO.UserId != null)
            {
                var formalEmployees = this.Db.GetIQueryable<HR_FormalEmployees>().Where(i => i.F_Id == disciplinaryRecordsDTO.UserId).FirstOrDefault();
                disciplinaryRecordsDTO.UserName = formalEmployees?.NameUser;
            }
            return disciplinaryRecordsDTO;
        }
        public async Task AddDataAsync(HR_DisciplinaryRecords data)
        {
            try
            {
                await InsertAsync(data);
            }
            catch (Exception ex)
            {
                throw new System.Exception(ex.ToString());
            }

        }
        public async Task AddDataListAsync(List<HR_DisciplinaryRecords> data)
        {
            try
            {
                await InsertAsync(data);
            }
            catch (Exception ex)
            {
                throw new System.Exception(ex.ToString());
            }
        }
        public async Task UpdateDataAsync(HR_DisciplinaryRecords data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_DisciplinaryRecords>();
            var search = input.Search;
            //筛选
            if (!search.F_Id.IsNullOrEmpty())
            {
                where = where.And(i => i.UserId.Contains(search.F_Id));
            }
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_DisciplinaryRecords, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            return q.Where(where).ProjectTo<HR_DisciplinaryRecordsDTO>(_mapper.ConfigurationProvider).ToDataTable();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}