﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using System.IO;
using DocumentFormat.OpenXml;
namespace Coldairarrow.Api.Word
{
    public class WordBase
    {
        /// <summary>生成DOC</summary>
        /// <param name="DocPath">绝对地址E:\\TenderingWeb\\</param>
        public static string WordHandle(string DocPath, Dictionary<string, string> Labels, Dictionary<string, string> imagePaths)
        {

            using (WordprocessingDocument doc = WordprocessingDocument.Open(DocPath, true))
            {
                var body = doc.MainDocumentPart.Document.Body;

                //Paragraph newPara = new Paragraph(new Run(new Break() { Type = BreakValues.Page }, new Text("text on the new page")));

                //body.Append(newPara);//向新的页面添加内容

                //AddHeaderOrFooter(doc, null, null);
                if (Labels != null && Labels.Count>0)
                {
                    WordBookmark.QueryDocMarkbook(doc, Labels);
                }
                if (imagePaths != null && imagePaths.Count > 0)
                {
                    foreach (var str in imagePaths)
                    {
                        if (str.Value != null && !string.IsNullOrEmpty(str.Value))
                        {
                            WordImages.AddPictureIntoWord(doc, str.Value);
                        }

                    }
                }
                doc.MainDocumentPart.Document.Save();
                doc.Close();
            }

            //using (WordprocessingDocument objWordDocument = WordprocessingDocument.Create(DocPath, WordprocessingDocumentType.Document))
            //{
            //    MainDocumentPart objMainDocumentPart = objWordDocument.AddMainDocumentPart();
            //    objMainDocumentPart.Document = new Document(new Body());
            //    Body objBody = objMainDocumentPart.Document.Body;
            //    //创建一些需要用到的样式,如标题3,标题4,在OpenXml里面,这些样式都要自己来创建的 
            //    //ReportExport.CreateParagraphStyle(objWordDocument);
            //    SectionProperties sectionProperties = new SectionProperties();
            //    PageSize pageSize = new PageSize();
            //    PageMargin pageMargin = new PageMargin();
            //    Columns columns = new Columns() { Space = "220" };//720
            //    DocGrid docGrid = new DocGrid() { LinePitch = 100 };//360
            //    //创建页面的大小,页距,页面方向一些基本的设置,如A4,B4,Letter, 
            //    GetPageSetting(ref pageSize, ref pageMargin);

            //    //在这里填充各个Paragraph,与Table,页面上第一级元素就是段落,表格.
            //    objBody.Append(new Paragraph());
            //    objBody.Append(new Table());
            //    objBody.Append(new Paragraph());

            //    //我会告诉你这里的顺序很重要吗?下面才是把上面那些设置放到Word里去.(大家可以试试把这下面的代码放上面,会不会出现打开openxml文件有误,因为内容有误)
            //    sectionProperties.Append(pageSize, pageMargin, columns, docGrid);
            //    objBody.Append(sectionProperties);

            //    //如果有页眉,在这里添加页眉.
            //    //if (IsAddHead)
            //    //{
            //        //添加页面,如果有图片,这个图片和上面添加在objBody方式有点不一样,这里搞了好久.
            //        //ReportExport.AddHeader(objMainDocumentPart, image);
            //    //}
            //    objMainDocumentPart.Document.Save();
            //}
            return DocPath;
        }

        /// <summary>生成DOC</summary>
        /// <param name="DocPath">绝对地址E:\\TenderingWeb\\</param>
        public static string WordHandleNew(string DocPath, Dictionary<string, string> Labels, Dictionary<string, byte[]> images)
        {
            using (WordprocessingDocument doc = WordprocessingDocument.Open(DocPath, true))
            {
                var body = doc.MainDocumentPart.Document.Body;

                if (Labels != null && Labels.Count > 0)
                {
                    WordBookmark.QueryDocMarkbookNew(doc, Labels);
                }

                if (images != null && images.Count > 0)
                {
                    foreach (var item in images)
                    {
                        if (item.Value != null)
                        {
                            WordImages.AddPictureIntoWord(doc, item.Value, item.Key);
                        }
                    }
                }

                doc.MainDocumentPart.Document.Save();
                doc.Close();
            }

            return DocPath;
        }

        public static void GetPageSetting(ref PageSize pageSize, ref PageMargin pageMargin)
        {

            bool val = true; //IsPaperOrientation;//是否横置页面

            string str_paperSize = "Letter";//A4，B4
            UInt32Value width = 15840U;
            UInt32Value height = 12240U;
            int top = 1440;
            UInt32Value left = 1440U;
            switch (str_paperSize)
            {
                case "A4":
                    width = 16840U;
                    height = 11905U;
                    break;
                case "B4":
                    width = 20636U;
                    height = 14570U;
                    break;
            }

            if (!val)
            {
                UInt32Value sweep = width;
                width = height;
                height = sweep;

                int top_sweep = top;
                top = (int)left.Value;
                left = (uint)top_sweep;
            }

            pageSize.Width = width;
            pageSize.Height = height;
            pageSize.Orient = new EnumValue<PageOrientationValues>(val ? PageOrientationValues.Landscape : PageOrientationValues.Portrait);

            pageMargin.Top = top;
            pageMargin.Bottom = top;
            pageMargin.Left = left;
            pageMargin.Right = left;
            pageMargin.Header = (UInt32Value)720U;
            pageMargin.Footer = (UInt32Value)720U;
            pageMargin.Gutter = (UInt32Value)0U;

        }

        /// <summary>添加并替换页眉页脚</summary>
        /// <param name="doc"></param>
        /// <param name="HeaderText">声明的页眉文本字符串</param>
        /// <param name="FooterText">声明的页脚文本字符串</param>
        public static void AddHeaderOrFooter(WordprocessingDocument doc, string HeaderText = null, string FooterText = null)
        {
            //得到主文件的一部分。
            MainDocumentPart mainDocPart = doc.MainDocumentPart;
            if (HeaderText != null)
            {
                //删除现有的报头部分。
                mainDocPart.DeleteParts(mainDocPart.HeaderParts);

                //创建一个新的标题部分和得到的关系ID。
                HeaderPart newHeaderPart = mainDocPart.AddNewPart<HeaderPart>();
                string rId = mainDocPart.GetIdOfPart(newHeaderPart);

                // Call the GeneratePageHeaderPart helper method, passing in
                // the header text, to create the header markup and then save
                // that markup to the header part.
                GeneratePageHeaderPart(HeaderText).Save(newHeaderPart);

                // Loop through all section properties in the document
                // which is where header references are defined.
                //遍历所有文档属性查找已经被定义的头部引用
                foreach (SectionProperties sectProperties in
                  mainDocPart.Document.Descendants<SectionProperties>())
                {
                    //  Delete any existing references to headers.
                    foreach (HeaderReference headerReference in sectProperties.Descendants<HeaderReference>())
                    {
                        sectProperties.RemoveChild(headerReference);
                    }

                    //  Create a new header reference that points to the new
                    // header part and add it to the section properties.
                    HeaderReference newHeaderReference =
                      new HeaderReference() { Id = rId, Type = HeaderFooterValues.Default };
                    sectProperties.Append(newHeaderReference);
                }
            }
            if (FooterText != null)
            {
                //删除现有的页脚部分。
                mainDocPart.DeleteParts(mainDocPart.FooterParts);

                //创建一个新的页脚部分得到其关系ID。
                FooterPart newFooterPart = mainDocPart.AddNewPart<FooterPart>();
                string rId = mainDocPart.GetIdOfPart(newFooterPart);

                // Call the GeneratePageFooterPart helper method, passing in
                // the footer text, to create the footer markup and then save
                // that markup to the footer part.
                //调用generatepagefooterpart辅助方法，通过在页脚文本，创建页脚标记后保存该标记的页脚部分。
                GeneratePageFooterPart(FooterText).Save(newFooterPart);

                // Loop through all section properties in the document which is where footer references are defined.
                //遍历所有截面属性文件中引用的定义就是页脚。
                foreach (SectionProperties sectProperties in mainDocPart.Document.Descendants<SectionProperties>())
                {
                    //删除任何现有文献页脚。
                    foreach (FooterReference footerReference in sectProperties.Descendants<FooterReference>())
                    {
                        sectProperties.RemoveChild(footerReference);
                    }

                    //  Create a new footer reference that points to the new 创建一个新的脚注引用指向新的
                    // footer part and add it to the section properties. 页脚部分并将其添加到截面特性。
                    FooterReference newFooterReference = new FooterReference() { Id = rId, Type = HeaderFooterValues.Default };
                    sectProperties.Append(newFooterReference);
                }
            }
            //更改保存到主文档的一部分。
            mainDocPart.Document.Save();

        }

        /// <summary>创建一个实例并添加页头 </summary>
        /// <param name="HeaderText"></param>
        /// <returns></returns>
        public static Header GeneratePageHeaderPart(string HeaderText)
        {
            // set the position to be the center 设置位置为中心
            PositionalTab pTab = new PositionalTab()
            {
                Alignment = AbsolutePositionTabAlignmentValues.Center,
                RelativeTo = AbsolutePositionTabPositioningBaseValues.Margin,
                Leader = AbsolutePositionTabLeaderCharValues.None
            };

            var element = new Header(new Paragraph(new ParagraphProperties(new ParagraphStyleId() { Val = "Header" }), new Run(pTab, new Text(HeaderText))));

            return element;
        }


        /// <summary>创建一个实例并添加页脚</summary>
        /// <param name="FooterText"></param>
        /// <returns></returns>
        public static Footer GeneratePageFooterPart(string FooterText)
        {
            PositionalTab pTab = new PositionalTab()
            {
                Alignment = AbsolutePositionTabAlignmentValues.Center,
                RelativeTo = AbsolutePositionTabPositioningBaseValues.Margin,
                Leader = AbsolutePositionTabLeaderCharValues.None
            };

            var elment = new Footer(new Paragraph(new ParagraphProperties(new ParagraphStyleId() { Val = "Footer" }), new Run(pTab, new Text(FooterText))));
            return elment;
        }


    }
}
