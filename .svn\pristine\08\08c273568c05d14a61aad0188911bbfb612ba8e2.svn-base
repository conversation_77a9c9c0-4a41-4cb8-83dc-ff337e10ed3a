﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.HR_Manage
{
    public interface IHR_ContentOperationLogBusiness
    {
        Task<PageResult<HR_ContentOperationLog>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<HR_ContentOperationLog> GetTheDataAsync(string id);
        Task SaveDataAsync(List<HR_ContentOperationLog> data);
        void SaveData(List<HR_ContentOperationLog> data);
        Task AddDataAsync(HR_ContentOperationLog data);
        Task UpdateDataAsync(HR_ContentOperationLog data);
        Task DeleteDataAsync(List<string> ids);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
    }
}