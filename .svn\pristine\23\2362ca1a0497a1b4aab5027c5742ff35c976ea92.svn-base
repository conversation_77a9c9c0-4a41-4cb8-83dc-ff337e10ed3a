<template>
  <div style="padding: 30px;">
    <a-card>
      <div>

        <a-button @click="refresh">刷新</a-button>
        &nbsp;
        <a-button @click="back">返回</a-button>
        &nbsp;
        <a-button type="primary" @click="questionUpdate">保存</a-button>

      </div>
      <a-divider>基本信息</a-divider>
      <a-form-model :model="baseInfo" :rules="rules" ref="ruleForm">
        <a-row :gutter="20">
          <a-col :span="12">
            <a-form-model-item labelAlign="left" label="问题名称" prop="name">
              <a-input style="width: 90%;" v-model="baseInfo.name" placeholder="请填写问题名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item labelAlign="left" label="问题类型" prop="type">
              <a-select style="width: 90%;" v-model="baseInfo.typeName" @change="handleTypeChange">
                <a-select-option v-for="(item,index) in types" :key="item.name">{{item.desc}}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="20">
          <a-col :span="24">
            <a-form-model-item label="解析" prop="description">
              <a-input v-model="baseInfo.description" style="width: 95%;" placeholder="请填写问题解析" :rows="4" type="textarea" />
            </a-form-model-item>
          </a-col>
        </a-row>

      </a-form-model>
      <a-divider>问题明细</a-divider>
      <!-- <div style="margin-bottom: 15px;">
    
     <a-button type="primary" @click="handleAdd" plain>添加答案</a-button>
      &nbsp;
     <a-button type="primary" @click="handleReset">全部清除</a-button>
      
                 
        </div> -->
      <a-table :columns="columns" :data-source="detailInfo" :pagination="false" :rowKey="record=>record.questionOptionId">
        <template slot="name" slot-scope="text,record,index">
          <a-input v-if="editIndex===index" v-model="option.name"></a-input>
          <span v-else>{{text}}</span>
        </template>
        <template slot="isAnswer" slot-scope="text,record,index">
          <a-select v-model="option.isAnswer" v-if="editIndex===index" @change="handleIsAnswerChange">

            <a-select-option key="1">是</a-select-option>
            <a-select-option key="0">否</a-select-option>
          </a-select>
          <span v-else>{{text==1?'是':'否'}}</span>

        </template>

        <template slot="action" slot-scope="text,record,index">
          <span v-if="editIndex===index">
            <a @click="handleSave(record,index)">保存</a>
            <a-divider type="vertical"></a-divider>
            <a @click="handleCancel">取消</a>
          </span>

          <span v-else>
            <a @click="handleEdit(record,index)">编辑</a>
            <a-divider type="vertical"></a-divider>
            <a @click="handleDelete(index)">删除</a>
          </span>

        </template>
      </a-table>

    </a-card>

  </div>

</template>

<script> 
import '@/assets/school/css/bootstrap.min.css'
import '@/assets/school/css/base.css'
import '@/assets/school/css/high-quality-courses.css'
import { questionUpdate, examStatusChange, createExam, getQuestionType, getQuestionDetail } from '../../../../api/exam'


const columns = [
  {
    title: '序号',
    dataIndex: 'serial',
    align: 'center',
    key: 'serial',
    ellipsis: true,
    customRender: (text, record, index) => `${index + 1}`,
    width: '5%'
  },
  {
    title: '答案内容',
    dataIndex: 'questionOptionContent',
    align: 'center',
    ellipsis: true,
    key: 'questionOptionContent',
    scopedSlots: { customRender: 'name' },
    width: '50%'
  },

  {
    title: '是否为正确答案',
    dataIndex: 'isAnswer',
    align: 'center',
    ellipsis: true,
    key: 'isAnswer',
    scopedSlots: { customRender: 'isAnswer' },
    width: '15%'
  },

  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    key: 'action',
    scopedSlots: { customRender: 'action' },
    width: '30%'
  }

]

export default {
  name: "ExamCreate",
  data() {
    return {
      columns,
      value: "",
      baseInfo: {
        id: "",
        name: "",
        description: "",
        type: "",
        typeName: "",
        options: []
      },
      types: [],
      detailInfo: [],
      question: {
        name: "",
        id: "",
        type: ""
      },
      userData: {
        userId: "",
        userName: ""
      },
      option: {
        name: "",
        isAnswer: ""
      },
      editIndex: "",
      rules: {
        name: [{ required: true, message: '问题名称不得为空', trigger: 'change' }],
        description: [{ required: true, message: '答案解析不得为空,若无请填写无', trigger: 'change' }],
      }
    }
  },
  methods: {
    refresh() {
      this.$router.go(0);
    },
    back() {
      const routeUrl = this.$router.resolve({
        path: `/School/Exam/QuestionList`
      })
      window.open(routeUrl.href, '_self')
    },
    handleAdd() {
      this.detailInfo.push(this.question);
    },
    handleEdit(record, index) {
      this.editIndex = index;
      this.option.name = record.questionOptionContent;
      this.option.isAnswer = record.isAnswer == 1 ? '是' : '否';

    },
    handleDelete(index) {
      this.detailInfo.splice(index, 1)
    },
    handleSave(record, index) {
      this.editIndex = "";
      this.detailInfo[index].questionOptionContent = this.option.name;
      if (this.option.isAnswer === "是") {
        this.detailInfo[index].isAnswer = 1;
      }
      if (this.option.isAnswer === "否") {
        this.detailInfo[index].isAnswer = 0;
      }
      this.option.isAnswer = "";
      this.option.name = "";
    },
    handleReset() {
      this.detailInfo.splice(0);
    },
    handleChange(e) {
      const value = e.target.value;
      this.value = value;
    },
    handleTypeChange(value, option) {

      this.baseInfo.type = value;

    },
    handleIsAnswerChange(value) {
      this.isAnswer = value;
    },
    handleCancel() {
      this.option.name = "";
      this.option.isAnswer = "";
      this.editIndex = "";
    },
    questionUpdate() {
      if (this.editIndex !== "") {
        this.$message.info("请先保存第" + (this.editIndex + 1) + "行的问题选项");
        return false;
      } else {
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            this.baseInfo.options = this.detailInfo;
            questionUpdate(this.baseInfo).then(res => {
              if (res.success) {
                let _this = this;
                this.$message.success("修改成功！", 2, function () {
                  _this.$router.go(0);
                });
              } else {
                this.$message.error(res.msg);
              }
            })
          } else {
            return false;
          }
        })
      }

    },
    getQuestionType() {
      getQuestionType().then(res => {
        if (res.success) {
          this.types = res.data;
        }
      })
    },
    getQuestionDetail() {
      getQuestionDetail(this.$route.query.qid).then(res => {
        if (res.success) {
          this.baseInfo.id = res.data.id;
          this.baseInfo.name = res.data.name;
          this.baseInfo.description = res.data.description;
          this.baseInfo.type = res.data.type;
          this.baseInfo.typeName = res.data.typeName;
          this.detailInfo = res.data.options;
        } else {
          this.$message.error("获取问题明细失败！");
        }
      })
    }

  },
  mounted() {
    this.$http.post('/Base_Manage/Home/GetHomeInfo', {}).then((resJson) => {
      this.userData.userId = resJson.Data.UserId;
      this.userData.userName = resJson.Data.Name;
      this.baseInfo.userName = resJson.Data.Name;
    });

    this.getQuestionType();
    this.getQuestionDetail();

  }
}

</script>

<style>
</style>
