﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Win32.Primitives</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.Win32Exception">
      <summary>Throws an exception for a Win32 error code.</summary>
    </member>
    <member name="M:System.ComponentModel.Win32Exception.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Win32Exception" /> class with the last Win32 error that occurred.</summary>
    </member>
    <member name="M:System.ComponentModel.Win32Exception.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Win32Exception" /> class with the specified error.</summary>
      <param name="error">The Win32 error code associated with this exception.</param>
    </member>
    <member name="M:System.ComponentModel.Win32Exception.#ctor(System.Int32,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Win32Exception" /> class with the specified error and the specified detailed description.</summary>
      <param name="error">The Win32 error code associated with this exception.</param>
      <param name="message">A detailed description of the error.</param>
    </member>
    <member name="M:System.ComponentModel.Win32Exception.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Win32Exception" /> class with the specified context and the serialization information.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> associated with this exception.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that represents the context of this exception.</param>
    </member>
    <member name="M:System.ComponentModel.Win32Exception.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Win32Exception" /> class with the specified detailed description.</summary>
      <param name="message">A detailed description of the error.</param>
    </member>
    <member name="M:System.ComponentModel.Win32Exception.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Win32Exception" /> class with the specified detailed description and the specified exception.</summary>
      <param name="message">A detailed description of the error.</param>
      <param name="innerException">A reference to the inner exception that is the cause of this exception.</param>
    </member>
    <member name="M:System.ComponentModel.Win32Exception.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with the file name and line number at which this <see cref="T:System.ComponentModel.Win32Exception" /> occurred.</summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" />.</param>
      <param name="context">The contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.ComponentModel.Win32Exception.NativeErrorCode">
      <summary>Gets the Win32 error code associated with this exception.</summary>
      <returns>The Win32 error code associated with this exception.</returns>
    </member>
    <member name="M:System.ComponentModel.Win32Exception.ToString">
      <summary>Returns a string that contains the <see cref="P:System.ComponentModel.Win32Exception.NativeErrorCode" />, or <see cref="P:System.Exception.HResult" />, or both.</summary>
      <returns>A string that represents the <see cref="P:System.ComponentModel.Win32Exception.NativeErrorCode" />, or <see cref="P:System.Exception.HResult" />, or both.</returns>
    </member>
  </members>
</doc>