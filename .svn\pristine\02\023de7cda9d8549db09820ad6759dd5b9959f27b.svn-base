﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.0</TargetFramework>

    <IsPackable>false</IsPackable>

    <ApplicationIcon />

    <OutputType>Library</OutputType>

    <StartupObject />

    <RootNamespace>Coldairarrow.UnitTests</RootNamespace>

    <AssemblyName>Coldairarrow.UnitTests</AssemblyName>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE;RELEASE;NETCOREAPP2_0</DefineConstants>
    <Optimize>false</Optimize>
    <CodeAnalysisRuleSet />
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <CodeAnalysisRuleSet />
    <NoWarn>1701;1702;CS1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="appsettings.Development.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="16.7.1" />
    <PackageReference Include="MSTest.TestAdapter" Version="2.1.2" />
    <PackageReference Include="MSTest.TestFramework" Version="2.1.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Coldairarrow.Business\04.Coldairarrow.Business.csproj" />
    <ProjectReference Include="..\Coldairarrow.Entity\02.Coldairarrow.Entity.csproj" />
    <ProjectReference Include="..\Coldairarrow.Util\01.Coldairarrow.Util.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
