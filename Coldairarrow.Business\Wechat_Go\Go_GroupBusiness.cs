﻿using Coldairarrow.Entity.Wechat_Go;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Util.DataAccess;
using System;

namespace Coldairarrow.Business.Wechat_Go
{
    public class Go_GroupBusiness : BaseBusiness<Go_Group>, IGo_GroupBusiness, ITransientDependency
    {
        public Go_GroupBusiness(IGoDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<Go_Group>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Go_Group>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Go_Group, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Go_Group> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(Go_Group data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Go_Group data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Go_Group>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Go_Group, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<Go_Group, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        public List<Go_Group> GetMyTeam(string openId)
        {
            var list = (from a in Db.GetIQueryable<Go_GroupUser>()
                        where a.F_OpenId == openId
                        join b in Db.GetIQueryable<Go_Group>()
                        on a.F_TeamId equals b.Id
                        select b).ToList();
            return list;
        }

        public List<Go_MiniUserDTO> GetTeamUser(string teamId)
        {
            var list = (from a in Db.GetIQueryable<Go_GroupUser>()
                        where a.F_TeamId == teamId
                        join b in Db.GetIQueryable<Go_MiniUser>()
                        on a.F_OpenId equals b.F_Id
                        select new Go_MiniUserDTO
                        {
                            U_RealName = b.U_RealName,
                            post = a.F_UserType,
                            U_HeadIcon = b.U_HeadIcon,
                            F_Id = b.F_Id,
                            U_NickName = b.U_NickName,
                            U_Gender = b.U_Gender
                        }).OrderByDescending(x => x.post).ToList();
            return list;
        }

        public List<Go_GroupDTO> GetTeamByOpenId(string openId)
        {
            var team = (from a in Db.GetIQueryable<Go_GroupUser>()
                        where a.F_OpenId == openId && a.F_IsAble == 1
                        select a).FirstOrDefault();
            if (!team.IsNullOrEmpty())
            {
                var teamId = team.F_TeamId;
                var teamList = from p in Db.GetIQueryable<Go_GroupUser>()
                               where p.F_TeamId == teamId
                               group p by p.F_TeamId into g
                               select new
                               {
                                   teamId = g.Key,
                                   count = g.Count()
                               };
                var list = (from a in Db.GetIQueryable<Go_Group>()
                            where a.Id == teamId && a.F_IsAble == 1
                            join b in teamList on a.Id equals b.teamId
                            select new Go_GroupDTO
                            {
                                Count = b.count,
                                Id = a.Id,
                                F_Name = a.F_Name,
                                F_Describe = a.F_Describe,
                                F_Cover = a.F_Cover,
                                F_Icon = a.F_Icon,
                                CreateTime = a.CreateTime
                            }).ToList();
                return list;
            }
            else
            {
                var list = new List<Go_GroupDTO>();
                return list;
            }

        }

        public List<Go_MiniUserDTO> getUserRank(string teamId, int type)
        {
            var queryBeginDate = DateTime.Now;
            var queryEndDate = DateTime.Now;
            var now = DateTime.Now;//当前日期 
            var nowDayOfWeek = Convert.ToInt32(now.DayOfWeek); //今天本周的第几天 
            var nowDay = now.Day; //当前日 
            var nowMonth = now.Month; //当前月 
            var nowYear = now.Year; //当前年 
            DateTime.Today.AddDays(-Convert.ToInt32(DateTime.Now.Date.DayOfYear));
            switch (type)
            {
                case 1://本周
                    //queryBeginDate = (new DateTime(nowYear, nowMonth, nowDay - nowDayOfWeek + 1));
                    //queryEndDate = (new DateTime(nowYear, nowMonth, nowDay - nowDayOfWeek + 8));
                    queryBeginDate = (new DateTime(nowYear, nowMonth, nowDay).AddDays(1-nowDayOfWeek));
                    queryEndDate = (new DateTime(nowYear, nowMonth, nowDay).AddDays(8 - nowDayOfWeek));
                    break;
                case 2://上周
                       //queryBeginDate = (new DateTime(nowYear, nowMonth, nowDay - nowDayOfWeek - 6));
                       //queryEndDate = (new DateTime(nowYear, nowMonth, nowDay - nowDayOfWeek + 1));
                    queryBeginDate = (new DateTime(nowYear, nowMonth, nowDay).AddDays(-6 - nowDayOfWeek));
                    queryEndDate = (new DateTime(nowYear, nowMonth, nowDay).AddDays(1 - nowDayOfWeek));
                    break;
                case 3://本月
                    queryBeginDate = (new DateTime(nowYear, nowMonth, 1));
                    queryEndDate = (new DateTime(nowYear, nowMonth+1, 1));
                    break;
                default://上月
                    queryBeginDate = (new DateTime(nowYear, nowMonth-1, 1));
                    queryEndDate = (new DateTime(nowYear, nowMonth , 1));
                    break;
            }
            var userList = from a in Db.GetIQueryable<Go_GroupUser>()
                           where a.F_TeamId == teamId
                           join b in Db.GetIQueryable<Go_Record>()
                           on a.F_OpenId equals b.F_OpenId
                           where b.F_Date >= queryBeginDate && b.F_Date < queryEndDate
                           && b.F_Isable == 1 &&(!string.IsNullOrEmpty(b.F_PhotoUrl))&& b.F_Score ==1
                           group new { a = a, b = b } by a.F_OpenId into g
                           select new
                           {
                               openId = g.Key,
                               score = g.Sum(x => x.b.F_Score)
                           };
            var list = (from c in Db.GetIQueryable<Go_GroupUser>()
                        where c.F_TeamId == teamId
                        join a in Db.GetIQueryable<Go_MiniUser>()
                        on c.F_OpenId equals a.F_Id
                        join b in userList on a.F_Id equals b.openId
                        into r from re in r.DefaultIfEmpty()
                        select new Go_MiniUserDTO
                        {
                            F_Id = a.F_Id,
                            Count = (re.score == null) ? 0 : re.score,
                            U_RealName = a.U_RealName,
                            U_HeadIcon = a.U_HeadIcon,
                            U_NickName = a.U_NickName

                        }).OrderByDescending(x => x.Count).ToList();
            return list;
        }
        //同时要筛选已缴纳和未缴纳
        public List<Go_MiniUserDTO> getUserRankByDate(string teamId,DateTime queryBeginDate,DateTime queryEndDate)
        {
            var userList = from a in Db.GetIQueryable<Go_GroupUser>()
                           where a.F_TeamId == teamId
                           join b in Db.GetIQueryable<Go_Record>()
                           on a.F_OpenId equals b.F_OpenId
                           where b.F_Date >= queryBeginDate && b.F_Date < queryEndDate
                           && b.F_Isable == 1 && (!string.IsNullOrEmpty(b.F_PhotoUrl)) && b.F_Score == 1
                           group new { a = a, b = b } by a.F_OpenId into g
                           select new
                           {
                               openId = g.Key,
                               score = g.Sum(x => x.b.F_Score)
                           };
            var list = (from c in Db.GetIQueryable<Go_GroupUser>()
                        where c.F_TeamId == teamId
                        join a in Db.GetIQueryable<Go_MiniUser>()
                        on c.F_OpenId equals a.F_Id
                        join b in userList on a.F_Id equals b.openId
                        into r
                        from re in r.DefaultIfEmpty()
                        select new Go_MiniUserDTO
                        {
                            F_Id = a.F_Id,
                            Count = (re.score==null) ? 0: re.score,
                            U_RealName = a.U_RealName,
                            U_HeadIcon = a.U_HeadIcon,
                            U_NickName = a.U_NickName,
                            PayState = (re.score == null)? true:false
                        }).OrderByDescending(x => x.Count).ToList();
            return list;
        }

        public  string GetMoneyByDate(string openId, string teamId ,DateTime queryBeginDate, DateTime queryEndDate)
        {
            var data = (from a in Db.GetIQueryable<Go_Money>()
                        where a.F_OpenId == openId && a.F_Date >= queryBeginDate && a.F_Date < queryEndDate
                        && a.F_Isable == 1 && a.F_TeamId == teamId
                        select a.F_Id).FirstOrDefault();
            return data;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}