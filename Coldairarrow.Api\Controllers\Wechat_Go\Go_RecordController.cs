﻿using Coldairarrow.Business.Wechat_Go;
using Coldairarrow.Entity.Wechat_Go;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Hosting;
using Coldairarrow.Util.Helper;
using java.net;

namespace Coldairarrow.Api.Controllers.Wechat_Go
{
    [Route("/Wechat_Go/[controller]/[action]")]
    public class Go_RecordController : BaseApiController
    {
        #region DI

        public Go_RecordController(IGo_RecordBusiness go_RecordBus, IGo_PhotoBusiness go_PhotoBusiness,IGo_TeamBusiness go_TeamBusiness,IHostingEnvironment hostingEnvironment)
        {
            _go_RecordBus = go_RecordBus;
            _go_PhotoBusiness = go_PhotoBusiness;
            _go_TeamBusiness = go_TeamBusiness;
            _hostingEnvironment = hostingEnvironment;
        }

        IGo_RecordBusiness _go_RecordBus { get; }
        IGo_PhotoBusiness _go_PhotoBusiness { get; }
        IGo_TeamBusiness _go_TeamBusiness{ get; }
        private static IHostingEnvironment _hostingEnvironment;

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Go_Record>> GetDataList(PageInput<ConditionDTO> input)
        {
            
            return await _go_RecordBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Go_Record> GetTheData(IdInputDTO input)
        {
            return await _go_RecordBus.GetTheDataAsync(input.id);
        }
        /// <summary>
        /// 运动日历的获取
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetMyCalendar()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                var startTime = HttpContext.Request.Form["startTime"].ToString().ToDateTime();
                var endTime = HttpContext.Request.Form["endTime"].ToString().ToDateTime();
                
                var data = _go_RecordBus.GetCalendar(openId, startTime, endTime);

                return Success(data);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetListDate()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                var startTime = HttpContext.Request.Form["startTime"].ToString().ToDateTime();
                var endTime = HttpContext.Request.Form["endTime"].ToString().ToDateTime();
                var data = _go_RecordBus.GetListDate(openId, startTime, endTime);
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 获取本月的记录
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetMyMonthList()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                DateTime now = DateTime.Now;
                var date = new DateTime(now.Year, now.Month, 1);
                var date_ = date.AddMonths(1).AddSeconds(-1);
                var data = _go_RecordBus.GetListDate(openId, date, date);
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 根据状态查询步数
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetMyDaka()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                var type = int.Parse(HttpContext.Request.Form["type"].ToString());
                var data = new Go_MyTotal();
                switch (type) {
                    case 0: data = _go_RecordBus.GetMyTodayTotal(openId); break;
                    case 1: data = _go_RecordBus.GetMyWeekTotal(openId); break;
                   case 2: data = _go_RecordBus.GetMyMonthTotal(openId); break;
                    default: data = _go_RecordBus.GetMyYearTotal(openId); break;
                }
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 根据状态查询分数
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetMyScore()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                var data = _go_RecordBus.GetMyScore(openId);
                var team = _go_TeamBusiness.GetMyTeam(openId).FirstOrDefault();
                if (!team.IsNullOrEmpty())
                {
                    data.TeamName = team.F_Name;
                }
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 根据首页的值
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetIndexScore()
        {
            try
            {
                var data = _go_RecordBus.GetCompanyScore();
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 获取某人今日是否达标
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetTodayScore()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                var data = _go_RecordBus.GetDataToday(openId);
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 作废某次打卡
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult DeleteRecord()
        {
            try
            {
               
                string Id = HttpContext.Request.Form["Id"].ToString();
                var data = _go_RecordBus.GetTheDataAsync(Id).Result;
                if (!data.IsNullOrEmpty())
                {
                    if (data.F_Step < 10000||data.F_Step.IsNullOrEmpty())
                    {
                        data.F_Score = 0;
                    }
                    data.F_Reason = "请重新上传更表现运动的视频";
                    data.F_Descri = "";
                    data.F_PhotoUrl = "";
                    data.F_Url = "";
                    _go_RecordBus.UpdateDataAsync(data).Wait();
                    WechatGoMiniHelper.sendDeleteMessage(data.F_OpenId, data.F_Date.Value.ToLongDateString().ToString());
                    return Success("作废成功");
                }
                else
                {
                    return Error("未获取到数据");
                }
               
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 获取公司的打卡记录
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetCompanyRecord()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var page = HttpContext.Request.Form["page"].ToString().ToInt();
                var pagesize = HttpContext.Request.Form["pagesize"].ToString().ToInt();
                if (pagesize==0)
                {
                    //默认为10
                    pagesize = 10;
                }
                var input = new miniInput()
                {
                    page = page,
                    pagesize = pagesize
                };
                var list = new List<Go_RecordDTO>();
               
                    if (teamId == "0")
                    {
                        list = _go_RecordBus.GetCompanyRecord(input);
                    }
                    else
                    {
                    var result = _go_TeamBusiness.GetTheDataAsync(teamId).Result;
                    if (result.IsNullOrEmpty())
                    {
                        //为空则取查群组
                        list = _go_RecordBus.GetGroupRecord(teamId, input);
                    }
                    else
                    {
                        //不为空则正常查询
                        list = _go_RecordBus.GetTeamRecord(teamId, input);
                    }
                    }
           
                foreach (var i in list)
                {
                    i.zanList = _go_RecordBus.getZanByRecordId(i.F_Id);
                    i.contentList = _go_RecordBus.getDiscussByRecordId(i.F_Id);
                    var result = _go_RecordBus.getZanData(i.F_Id, openId);
                    if (result.IsNullOrEmpty())
                    {
                        i.isZan = 0;
                    }
                    else
                    {
                        i.isZan = 1;
                    }
                }
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Go_Record data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _go_RecordBus.AddDataAsync(data);
            }
            else
            {
                await _go_RecordBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _go_RecordBus.DeleteDataAsync(ids);
        }

        //微信步数的添加
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult addWenRun()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                var stepInfoString = HttpContext.Request.Form["stepInfoList"].ToString();
                var stepInfoList = JsonConvert.DeserializeObject<List<Go_StepInfo>>(stepInfoString).TakeLast(1);
                foreach (var i in stepInfoList)
                {
                    var date = ConvertStringToDateTime(i.timestamp);
                    var record = _go_RecordBus.GetDataByDate(date, openId);
                    if (record.IsNullOrEmpty())
                    {
                        //为空新建
                        var newRecord = new Go_Record();
                        newRecord.F_OpenId = openId;
                        newRecord.F_CreateTime = DateTime.Now;
                        newRecord.F_UpdateTime = DateTime.Now;
                        newRecord.F_Step = i.step;
                        newRecord.F_Id = Guid.NewGuid().ToString("N");
                        newRecord.F_Date = date;
                        newRecord.F_Isable = 1;
                        if (i.step > 10000)
                        {
                            newRecord.F_Score = 1;
                        }
                        else
                        {
                            newRecord.F_Score = 0;
                        }
                        _go_RecordBus.AddDataAsync(newRecord).Wait();
                    }
                    else
                    {
                        //不为空更新
                        record.F_Step = i.step;
                        record.F_UpdateTime = DateTime.Now;
                        //大于1万步更新
                        if (i.step > 10000)
                        {
                            record.F_Score = 1;
                            
                        }
                        _go_RecordBus.UpdateDataAsync(record).Wait();
                    }
                }
                return Success("微信步数打卡成功");

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        //上传照片的打卡
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult addDaka()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                var photoUrl = HttpContext.Request.Form["photoUrl"].ToString();
                var Url = HttpContext.Request.Form["Url"].ToString();
                var descri = HttpContext.Request.Form["descri"].ToString();
                //var descri =  URLDecoder.decode(descri_);
                var checkDescir = WechatGoMiniHelper.checkMsgSec(descri);
                if (!checkDescir)
                {
                    return Error("简述内容不合法");
                }
                var date = DateTime.Today;
                var record = _go_RecordBus.GetDataByDate(date, openId);
                    if (record.IsNullOrEmpty())
                    {
                        //为空新建
                        var newRecord = new Go_Record();
                        newRecord.F_OpenId = openId;
                        newRecord.F_CreateTime = DateTime.Now;
                        newRecord.F_UpdateTime = DateTime.Now;
                        newRecord.F_Descri = descri;
                        newRecord.F_Id = Guid.NewGuid().ToString("N");
                        newRecord.F_Date = date;
                        newRecord.F_PhotoUrl = photoUrl;
                        newRecord.F_Isable = 1;
                        newRecord.F_Score = 1;
                         newRecord.F_Url = Url;
                        _go_RecordBus.AddDataAsync(newRecord).Wait();
                         addTeamPhoto(openId,photoUrl);
                         return Success("上传图片打卡成功");
                }
                    else
                    {
                        //不为空更新
                        record.F_Descri = descri;
                        record.F_PhotoUrl = photoUrl;
                        record.F_Url = Url;
                        record.F_Score = 1;
                        record.F_UpdateTime = DateTime.Now;
                        _go_RecordBus.UpdateDataAsync(record).Wait();
                    return Success("更新图片打卡成功");
                }
          

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        /// <summary>
        /// 获取个人排行
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult GetUserRank()
        {
            try
            {
                string type = HttpContext.Request.Form["type"].ToString();
                var data = new List<Go_MiniUserDTO>();
                switch (type)
                {
                    case "today": data = _go_RecordBus.getDayUserRank(type); break;
                    case "day": data = _go_RecordBus.getDayUserRank(type); break;
                    case "week": data = _go_RecordBus.getWeekUserRank(type); break;
                    case "month": data = _go_RecordBus.getWeekUserRank(type); break;
                    case "lastweek": data = _go_RecordBus.getlastMonthUserRank(type); break;
                    case "lastmonth": data = _go_RecordBus.getlastMonthUserRank(type); break;
                    default: data = _go_RecordBus.getWeekUserRank(type); break;
                }

                return Success(data);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }



        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _go_RecordBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Go_Record>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _go_RecordBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion

        #region 二次开发
        /// <summary>       
        /// 10位时间戳转为C#格式时间       
        /// </summary>       
        /// <param name=”timeStamp”></param>       
        /// <returns></returns>       
        public static DateTime ConvertStringToDateTime(string timeStamp)
        {
            Int64 begtime = Convert.ToInt64(timeStamp) * 10000000;
            DateTime dt_1970 = new DateTime(1970, 1, 1, 8, 0, 0);
            long tricks_1970 = dt_1970.Ticks;//1970年1月1日刻度
            long time_tricks = tricks_1970 + begtime;//日志日期刻度
            DateTime dt = new DateTime(time_tricks);//转化为DateTime
            return dt;
        }

        public void addTeamPhoto(string openId,string photoUrl)
        {
            var teamId = _go_PhotoBusiness.GetTeamByOpenId(openId);
            if (!teamId.IsNullOrEmpty())
            {
                var data = new Go_Photo {
                    F_Id = Guid.NewGuid().ToString("N"),
                    F_CreateTime = DateTime.Now,
                    F_TeamId = teamId,
                    F_OpenId = openId,
                    F_PhotoUrl = photoUrl,
                    F_IsAble =1,
                    F_CreatorId =  openId
                };
                _go_PhotoBusiness.AddDataAsync(data).Wait();
            }
        }

        /// <summary>
        /// 微信小程序上传图片
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult SingleFileUpload()
        {
            var formFile = Request.Form.Files["file"];//获取请求发送过来的文件
            var currentDate = DateTime.Now;
            var webRootPath = _hostingEnvironment.WebRootPath;//>>>相当于HttpContext.Current.Server.MapPath("") 
            try
            {
                var filePath = $"/UploadFile/{currentDate:yyyyMMdd}/";
                //创建每日存储文件夹
                if (!Directory.Exists(webRootPath + filePath))
                {
                    Directory.CreateDirectory(webRootPath + filePath);
                }
                if (formFile != null)
                {
                    //文件后缀
                    var fileExtension = Path.GetExtension(formFile.FileName);//获取文件格式，拓展名
                    if (fileExtension != ".xls" && fileExtension != ".xlsx" && fileExtension != ".pdf" && fileExtension != ".jpg" && fileExtension != ".png" && fileExtension != ".doc" && fileExtension != ".docx")
                    {
                        return Error("只支持上传execl、pdf、word、图片文件");
                    }
                    //判断文件大小
                    var fileSize = formFile.Length;
                    if (fileSize > 1024 * 1024 * 10) //10M TODO:(1mb=1024X1024b)
                    {
                        //return new JsonResult(new { isSuccess = false, resultMsg = "上传的文件不能大于10M" });
                        return Error("上传的文件不能大于10M");
                    }
                    //保存的文件名称(以名称和保存时间命名)
                    var saveName = currentDate.ToString("HHmmss") + "_" + fileExtension;
                    //文件保存
                    using (var fs = System.IO.File.Create(webRootPath + filePath + saveName))
                    {
                        formFile.CopyTo(fs);
                        fs.Flush();
                    }
                    //完整的文件路径
                    var completeFilePath = Path.Combine(filePath, saveName);
                    //return new JsonResult(new { isSuccess = true, returnMsg = "上传成功", completeFilePath = completeFilePath });
                    return Success(completeFilePath);
                }
                else
                {
                    //return new JsonResult(new { isSuccess = false, resultMsg = "上传失败，未检测上传的文件信息~" });
                    return Error("上传失败，未检测上传的文件信息~");
                }

            }
            catch (Exception ex)
            {
                //return new JsonResult(new { isSuccess = false, resultMsg = "文件保存失败，异常信息为：" + ex.Message });
                return Error("文件保存失败，异常信息为：" + ex.Message);
            }
        }
        
        #endregion
    }
}