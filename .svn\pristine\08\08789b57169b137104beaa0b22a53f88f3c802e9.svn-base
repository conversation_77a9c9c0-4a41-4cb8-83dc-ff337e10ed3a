﻿using Coldairarrow.Entity.Wechat_CostDept;
using Coldairarrow.Util;
using Coldairarrow.Util.DataAccess;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

namespace Coldairarrow.Business.Wechat_CostDept
{
    public class Wechat_ManagerBusiness : BaseBusiness<Wechat_Manager>, IWechat_ManagerBusiness, ITransientDependency
    {
        public Wechat_ManagerBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<Wechat_Manager>> GetDataListAsync(PageInput<ManagerInputDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Wechat_Manager>();
            var search = input.Search;

            //筛选
            //if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            //{
            //    var newWhere = DynamicExpressionParser.ParseLambda<Wechat_Manager, bool>(
            //        ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
            //    where = where.And(newWhere);
            //}
            if (!search.W_Name.IsNullOrEmpty())
                where = where.And(x => x.W_Name.Contains(search.W_Name));
            if (!search.W_Place.IsNullOrEmpty())
                where = where.And(x => x.W_Place == search.W_Place);
            if (!search.W_Type.IsNullOrEmpty())
                where = where.And(x => x.W_Type == search.W_Type);
            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Wechat_Manager> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(Wechat_Manager data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Wechat_Manager data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public List<Wechat_Manager> GetListByType(int type)
        {
            var q = GetIQueryable();
            var user = q.Where(o => o.W_Place == type).OrderBy(o =>o.Sort).ToList();
            return user;
        }

        public List<Wechat_Manager> GetListByTest(int type)
        {
            var q = GetIQueryable();
            var user = q.Where(o => o.W_Place == type).OrderBy(o => o.Sort).ToList();
            return user;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}