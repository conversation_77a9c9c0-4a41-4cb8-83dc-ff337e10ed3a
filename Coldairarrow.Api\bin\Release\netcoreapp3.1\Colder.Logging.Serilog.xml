<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Colder.Logging.Serilog</name>
    </assembly>
    <members>
        <member name="T:Colder.Logging.Serilog.SerilogDIExtentions">
            <summary>
            注入拓展
            </summary>
        </member>
        <member name="M:Colder.Logging.Serilog.SerilogDIExtentions.ConfigureLoggingDefaults(Microsoft.Extensions.Hosting.IHostBuilder)">
            <summary>
            配置日志
            </summary>
            <param name="hostBuilder">建造者</param>
            <returns></returns>
        </member>
    </members>
</doc>
