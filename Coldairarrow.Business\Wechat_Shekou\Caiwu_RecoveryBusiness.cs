﻿using Coldairarrow.Entity.Wechat_Shekou;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Util.DataAccess;
using System;

namespace Coldairarrow.Business.Wechat_Shekou
{
    public class Caiwu_RecoveryBusiness : BaseBusiness<Caiwu_Recovery>, ICaiwu_RecoveryBusiness, ITransientDependency
    {
        public Caiwu_RecoveryBusiness(IShekouDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<Caiwu_Recovery>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Caiwu_Recovery>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Caiwu_Recovery, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Caiwu_Recovery> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(Caiwu_Recovery data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Caiwu_Recovery data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Caiwu_Recovery>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Caiwu_Recovery, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<Caiwu_Recovery, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        public List<Caiwu_Recovery> GetCaiwu_Recovery()
        {
            var month = DateTime.Now.Month;
            var year = DateTime.Now.Year;
            var q = GetIQueryable().Where(x => x.Month == month && x.Year == year).OrderBy(x => x.F_Id).ToList();
            return q;

        }
        #endregion

        #region 私有成员

        #endregion
    }
}