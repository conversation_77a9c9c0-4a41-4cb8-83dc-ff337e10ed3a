﻿using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System;
using AutoMapper;
using AutoMapper.QueryableExtensions;

namespace Coldairarrow.Business.HR_EmployeeInfoManage
{
    public class HR_SocialWorkExpBusiness : BaseBusiness<HR_SocialWorkExp>, IHR_SocialWorkExpBusiness, ITransientDependency
    {
        readonly IMapper _mapper;
        public HR_SocialWorkExpBusiness(IDbAccessor db, IMapper mapper)
            : base(db)
        {
            _mapper = mapper;
        }

        #region 外部接口

        public async Task<PageResult<HR_SocialWorkExp>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            try
            {
                var q = GetIQueryable();
                var where = LinqHelper.True<HR_SocialWorkExp>();
                var search = input.Search;

                //筛选
                if (!search.F_Id.IsNullOrEmpty())
                {
                    q = q.Where(i => i.F_UserId.Contains(search.F_Id));
                }
                if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
                {
                    var newWhere = DynamicExpressionParser.ParseLambda<HR_SocialWorkExp, bool>(
                        ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                    where = where.And(newWhere);
                }

                return await q.Where(where).GetPageResultAsync(input);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
        }
        public async Task<List<HR_SocialWorkExp>> GetDataListByUserIdAsync(string userId)
        {
            return await GetIQueryable().Where(x => userId == x.F_UserId).ToListAsync();
        }
        public async Task<HR_SocialWorkExp> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_SocialWorkExp data)
        {
            try
            {
                await InsertAsync(data);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
        }

        public async Task UpdateDataAsync(HR_SocialWorkExp data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_SocialWorkExp>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_SocialWorkExp, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_SocialWorkExp, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        public int AddData(HR_SocialWorkExp data)
        {
            return Insert(data);
        }

        public int UpdateData(HR_SocialWorkExp data)
        {
            return Update(data);
        }

        public int UpdateListData(List<HR_SocialWorkExp> data)
        {
            return Update(data);
        }

        public int DeleteData(List<string> ids)
        {
            return DeleteData(ids);
        }
        #endregion

        #region 私有成员

        #endregion
    }
}