﻿using Coldairarrow.Business.WorkWx_Robot;
using Coldairarrow.Entity.WorkWx_Robot;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Coldairarrow.Business.HR_DataDictionaryManage;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.Xml;

namespace Coldairarrow.Api.Controllers.WorkWx_Robot
{
    [Route("/WorkWx_Robot/[controller]/[action]")]
    public class WorkWx_LogsController : BaseApiController
    {
        #region DI

        public WorkWx_LogsController(IWorkWx_LogsBusiness workWx_LogsBus, IHR_DataDictionaryDetailsBusiness iHR_DataDictionaryDetailsBusiness)
        {
            _workWx_LogsBus = workWx_LogsBus;
            _iHR_DataDictionaryDetailsBusiness = iHR_DataDictionaryDetailsBusiness;
        }

        IWorkWx_LogsBusiness _workWx_LogsBus { get; }
        IHR_DataDictionaryDetailsBusiness _iHR_DataDictionaryDetailsBusiness { get; }
        //招置地址
        string _sToken = "EQ69tWOfAKywyAT6kK2";
        string _corpId = "wwe9e87379e762b7fa";
        string _sEncodingAESKey = "bJP2m7YLLKurUbUQJ3Msr62iLK4gJWvpYLGC6aO121x";
        //置地测试的地址
        //string _sToken = "MaNiRojx";
        //string _corpId = "ww768a758c0fddf27b";
        //string _sEncodingAESKey = ":QO2DH4cHvgk7PyrTTcG1HP13TIVlm0lPuilJOVyaLgS";

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<WorkWx_Logs>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _workWx_LogsBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<WorkWx_Logs> GetTheData(IdInputDTO input)
        {
            return await _workWx_LogsBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(WorkWx_Logs data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);
                var url = _iHR_DataDictionaryDetailsBusiness.GetDataByValue(data.F_Robot, "839f6af9ec484c878d78736aa820e7d3").Remark;
                var result = _workWx_LogsBus.sendRobotTextMsg(url, data.F_Content,"");
                data.F_JsonStr = result;
                var resultObj = JsonConvert.DeserializeObject<WorkWx_RobotResult> (result);       
                if (resultObj.errcode == 0)
                {
                   data.F_Result = 1;
                }
                else
                {
                    data.F_Result = 0;
                }
                await _workWx_LogsBus.AddDataAsync(data);
            }
            else
            {
                await _workWx_LogsBus.UpdateDataAsync(data);
            }
        }


        [HttpPost]
        [NoCheckJWT]
        public async Task SendTextMessage(string robot,string content)
        {
              
               
                var url = _iHR_DataDictionaryDetailsBusiness.GetDataByValue(robot, "839f6af9ec484c878d78736aa820e7d3").Remark;
                var result = _workWx_LogsBus.sendRobotTextMsg(url, content, "");
                var data = new WorkWx_Logs() {
                    F_JsonStr = result,
                    F_Content = content,
                    F_Robot = robot,
                    F_Id = Guid.NewGuid().ToString("N"),
                    F_CreateDate  = DateTime.Now,
                    F_CreateUserId = "IP",
                    F_CreateUserName = "匿名"
                };
                var resultObj = JsonConvert.DeserializeObject<WorkWx_RobotResult>(result);
                if (resultObj.errcode == 0)
                {
                    data.F_Result = 1;
                }
                else
                {
                    data.F_Result = 0;
                }
                await _workWx_LogsBus.AddDataAsync(data);
           
        }



        [HttpPost]
        [NoCheckJWT]
        public async Task SendImageData(string content)
        {
            var url = _iHR_DataDictionaryDetailsBusiness.GetDataByValue("小黑", "839f6af9ec484c878d78736aa820e7d3").Remark;
            var result = _workWx_LogsBus.sendRobotImageMsg(url, content);
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _workWx_LogsBus.DeleteDataAsync(ids);
        }

        [HttpGet, HttpPost]
        [NoCheckJWT]
        public async Task<ActionResult> VerifyURL(string msg_signature, string timestamp, string nonce, string echostr)
        {
            Tencent.WXBizMsgCrypt wxcpt = new Tencent.WXBizMsgCrypt(_sToken, _sEncodingAESKey, _corpId);
            string sEchoStr = "";
            int ret = 0;
            ret = wxcpt.VerifyURL(msg_signature, timestamp, nonce, echostr, ref sEchoStr);
            if (ret != 0)
            {
                LogHelper.WriteLog_LocalTxt(sEchoStr);
                return Content(sEchoStr, "text/xml");
            }
            LogHelper.WriteLog_LocalTxt(sEchoStr);
            return Content(sEchoStr, "text/xml");
        }

        [HttpGet, HttpPost]
        [NoCheckJWT]
        public async Task<ActionResult> VerifyURLNew(string msg_signature,string timestamp,string nonce,string echostr)
        {
            try
            {
                Tencent.WXBizMsgCrypt wxcpt = new Tencent.WXBizMsgCrypt(_sToken, _sEncodingAESKey, _corpId);
                string model = "";
                int ret = 0;
                var sReqData = "";
                if (HttpContext.Request.Method == System.Net.Http.HttpMethod.Get.Method)
                {
                    //处理Get
                    ret = wxcpt.VerifyURL(msg_signature, timestamp, nonce, echostr, ref model);
                    if (ret != 0)
                    {
                        LogHelper.WriteLog_WechatTxt(model);
                        return Content(model, "text/xml");
                    }
                    LogHelper.WriteLog_WechatTxt(model);
                    return Content(model, "text/xml");
                }
                else if (HttpContext.Request.Method == System.Net.Http.HttpMethod.Post.Method)
                {
                    //处理POST
                    var stream = Request.Body;
                    using (var reader = new StreamReader(stream))
                    {
                        sReqData = reader.ReadToEndAsync().Result;
                    }
                    //回调数据
                    // 解析之后的明文
                    ret = wxcpt.DecryptMsg(msg_signature, timestamp, nonce, sReqData, ref model);
                    if (ret != 0)
                    {
                        throw new Exception($"ERR: Decrypt Fail, ret: {ret}");
                    }
                    //处理model
                    var jsonRes = ReceiveXml(model);
                    LogHelper.WriteLog_WechatTxt(jsonRes.FromUserName+ "|"+jsonRes.Content);
                    return Content(model, "text/xml");
                }
                LogHelper.WriteLog_WechatTxt(model);
                return Content("ok", "text/xml");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLog_WechatTxt(ex.Message);
                return Content(ex.Message, "text/xml");
            }

        }

        private WxXmlModel ReceiveXml(string str)
        {
           XmlDocument requestDocXml = new XmlDocument();
           requestDocXml.LoadXml(str);
           XmlElement rootElement = requestDocXml.DocumentElement;
           WxXmlModel WxXmlModel = new WxXmlModel();
           WxXmlModel.ToUserName = rootElement.SelectSingleNode("ToUserName").InnerText;
           WxXmlModel.FromUserName = rootElement.SelectSingleNode("FromUserName").InnerText;
           WxXmlModel.CreateTime = rootElement.SelectSingleNode("CreateTime").InnerText;
           WxXmlModel.MsgType = rootElement.SelectSingleNode("MsgType").InnerText;
           switch (WxXmlModel.MsgType) {
           case "text":WxXmlModel.Content = rootElement.SelectSingleNode("Content").InnerText;break;
           default:break;
           }
            return WxXmlModel;
        }
        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _workWx_LogsBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<WorkWx_Logs>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _workWx_LogsBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}