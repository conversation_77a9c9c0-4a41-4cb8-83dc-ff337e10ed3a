﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_RecruitWorkExpeBusiness : BaseBusiness<HR_RecruitWorkExpe>, IHR_RecruitWorkExpeBusiness, ITransientDependency
    {
        public HR_RecruitWorkExpeBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_RecruitWorkExpe>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RecruitWorkExpe>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RecruitWorkExpe, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_RecruitWorkExpe> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_RecruitWorkExpe data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_RecruitWorkExpe data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RecruitWorkExpe>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RecruitWorkExpe, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_RecruitWorkExpe, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        public int AddData(HR_RecruitWorkExpe data)
        {
            return Insert(data);
        }

        public int UpdateData(HR_RecruitWorkExpe data)
        {
            return Update(data);
        }

        public int DeleteData(HR_RecruitWorkExpe data)
        {
            return Delete(data);
        }
        public int UpdateListData(List<HR_RecruitWorkExpe> data)
        {
            return Update(data);
        }

        public void AddListData(List<HR_RecruitWorkExpe> data)
        {
            BulkInsert(data);
        }
        #endregion

        #region 私有成员

        #endregion
    }
}