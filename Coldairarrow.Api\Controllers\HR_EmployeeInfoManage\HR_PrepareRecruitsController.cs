﻿using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Coldairarrow.Util.Excel;
using Coldairarrow.Util.Excel.Model;
using Microsoft.Extensions.Configuration;
using Coldairarrow.IBusiness;
using Coldairarrow.Entity;
using Coldairarrow.Entity.HR_RegistrManage;

namespace Coldairarrow.Api.Controllers.HR_EmployeeInfoManage
{
    [Route("/HR_EmployeeInfoManage/[controller]/[action]")]
    public class HR_PrepareRecruitsController : BaseApiController
    {
        #region DI

        public HR_PrepareRecruitsController(IHR_PrepareRecruitsBusiness hR_PrepareRecruitsBus, IConfiguration configuration)
        {
            _hR_PrepareRecruitsBus = hR_PrepareRecruitsBus;
            _configuration = configuration;
        }
        readonly IConfiguration _configuration;
        IHR_PrepareRecruitsBusiness _hR_PrepareRecruitsBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_PrepareRecruitsDto>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_PrepareRecruitsBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_PrepareRecruits> GetTheData(IdInputDTO input)
        {
            return await _hR_PrepareRecruitsBus.GetTheDataAsync(input.id);
        }

        [HttpPost]
        public HR_PrepareRecruits LoadData(DataInputDTO input)
        {
            return _hR_PrepareRecruitsBus.LoadData(input);
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_PrepareRecruits data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_PrepareRecruitsBus.AddDataAsync(data);
            }
            else
            {
                await _hR_PrepareRecruitsBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_PrepareRecruitsBus.DeleteDataAsync(ids);
        }

        #endregion

        #region 自动提起新员工入职的流程
        /// <summary>
        /// 自动发起新员工入职，
        /// </summary>
        /// <param name="hR_RegistrEntry">员工登记表</param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult SaveInitiatedautoFlow(HR_PrepareRecruits data)
        {
            _hR_PrepareRecruitsBus.SaveInitiFlow(data);
            return Success("发送流程成功！");
        }
        #endregion


        #region 流程
        /// <summary>
        /// 保存并创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult SaveAndCreateFlow(DataInputDTO inputDTO)
        {
            try
            {
                //List<HR_PrepareRecruits> hR_PrepareRecruits=JsonContent.
                var data = inputDTO.Data;
                var ret = _hR_PrepareRecruitsBus.CreateFlow(data, _configuration["OAUrl"] + "rest/archives/");

                if (ret)
                {
                    return Success();
                }
                else
                {
                    return Error("创建流程失败");
                }

            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult CreateFlow(string data)
        {
            var ret = _hR_PrepareRecruitsBus.CreateFlow(data, _configuration["OAUrl"] + "rest/archives/");
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("创建流程失败");
            }
        }

        /// <summary>
        /// 流程回调方法
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult FlowCallBack(List<PreReInputDTO> input)
        {
            var d = input;
            _hR_PrepareRecruitsBus.FlowCallBack(input);

            return Success();
        }

        /// <summary>
        ///提交退回流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ActWorkflow(string data)
        {
            try
            {
                var ret = _hR_PrepareRecruitsBus.ActWorkflow(data, _configuration["OAUrl"] + "rest/archives/");

                if (ret)
                {
                    return Success();
                }
                else
                {
                    return Error("创建流程失败");
                }

            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ArchiveWorkflow(string data)
        {
            var ret = _hR_PrepareRecruitsBus.ArchiveWorkflow(data, _configuration["OAUrl"] + "rest/archives/");
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("强制归档失败");
            }
        }
        #endregion

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_PrepareRecruitsBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据
        /// <summary>
        /// 模板下载
        /// </summary>
        [HttpPost]
        public FileContentResult DownloadTemplate()
        {
            string filePath = Directory.GetCurrentDirectory() + "/BusinessTemplate/入职准备清单模板.xls";
            if (FileHelper.Exists(filePath))
            {
                var bys = System.IO.File.ReadAllBytes(filePath);//excel表转换成流
                return File(bys, "application/vnd.ms-excel");
            }
            else
            {
                throw new System.Exception("找不到模板");
            }

        }

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        //[Transactional]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }
            //List<DataRowListModel> headLists = new List<DataRowListModel>();
            //for (int i = 0; i < 1; i++)
            //{
            //    DataRowListModel headList = new DataRowListModel();
            //    switch (i)
            //    {
            //        case 0:
            //            headList.Name = "部门";
            //            headList.DataRow = 2;
            //            break;
            //        case 1:
            //            headList.Name = "汇报上级";
            //            headList.DataRow = 2;
            //            headList.HeadData = 6;
            //            break;
            //        case 2:
            //            headList.Name = "表五";
            //            headList.DataRow = 3;
            //            headList.HeadColumn = 8;
            //            headList.FirstColumn = 1;
            //            break;
            //        case 3:
            //            headList.Name = "表六";
            //            headList.DataRow = 4;
            //            break;
            //        case 4:
            //            headList.Name = "表七";
            //            headList.DataRow = 3;
            //            headList.HeadData = 5;
            //            break;
            //        default:
            //            break;
            //    }
            //    headLists.Add(headList);
            //}
            //var list = new ExcelImportHelper().QuestionsImport(physicPath, headLists);
            Hashtable ht = new Hashtable();
            ht["F_Department"] = "部门";
            ht["F_Jobs"] = "岗位名称";
            ht["F_Project"] = "项目";
            ht["F_Name"] = "姓名";
            ht["F_Sex"] = "性别";
            ht["F_Rank"] = "职级";
            ht["F_ReportSuperior"] = "汇报上级";
            ht["F_WorkTime"] = "到岗时间";
            ht["F_JD"] = "JD";
            ht["F_ProbationPeriod"] = "试用期任务设定";
            ht["F_JobTitle"] = "标准职位名称";
            ht["F_IdNumber"] = "身份证号";
            ht["F_Contact"] = "联系方式";
            ht["F_Seat"] = "办公座位";
            ht["F_Supplies"] = "办公用品";
            ht["F_ComputerPurch"] = "电脑采购";
            ht["F_Account"] = "账号";
            ht["F_Remark"] = "备注";
            var list = new ExcelHelper<HR_PrepareRecruits>().ExcelImport(ht, physicPath);
            var IsSuccess = 0;
            var Message = "文件未包含入职数据！";
            var data = new List<HR_PrepareRecruits>();
            //如果出错则返回错误页面
            if (list != null)
            {
                var PerList = new List<HR_PrepareRecruits>();
                list.ForEach(i =>
                {
                    if (!i.F_Name.IsNullOrEmpty() && !i.F_Department.IsNullOrEmpty())
                    {
                        PerList.Add(i);
                    }
                });
                if (PerList.Count > 0)
                {
                    PerList.ForEach(i =>
                    {
                        InitEntity(i);
                        i.F_BusState = (int)ASKBusState.正常;
                        i.F_WFState = (int)WFStates.草稿;
                        transitionType(i);
                        i.F_Seat = i.F_Seat == null ? 0 : i.F_Seat;
                        i.F_Supplies = i.F_Supplies == null ? 0 : i.F_Supplies;
                        i.F_ComputerPurch = i.F_ComputerPurch == null ? 0 : i.F_ComputerPurch;
                    }
                    );
                    //PerList.ForEach(i =>

                    //);
                    //PerList.ForEach(i =>

                    //);
                    _hR_PrepareRecruitsBus.AddDataListAsync(PerList);
                    data = PerList;
                    Message = "导入成功！";
                    IsSuccess = 1;
                }
                else
                {
                    Message = "导入内容未包含相关数据！";
                    IsSuccess = 0;
                }

            };

            //string url = $"{_configuration["WebRootUrl"]}{path}";


            var res = new
            {
                IsSuccess = IsSuccess,
                Message = Message,
                data = data
            };

            return JsonContent(res.ToJson());
        }
        /// <summary>
        /// 项目字段转换
        /// </summary>
        /// <param name="hR_PrepareRecruits"></param>
        public HR_PrepareRecruits transitionType(HR_PrepareRecruits hR_PrepareRecruits)
        {
            if (!hR_PrepareRecruits.F_Project.IsNullOrEmpty())
            {
                switch (hR_PrepareRecruits.F_Project)
                {

                    case "嘉景湾":
                        hR_PrepareRecruits.F_Project = "礼嘉";
                        break;
                    case "公园大道":
                        hR_PrepareRecruits.F_Project = "中央公园";
                        break;
                    default:
                        break;
                }

            }
            return hR_PrepareRecruits;
        }
        #endregion
    }
}