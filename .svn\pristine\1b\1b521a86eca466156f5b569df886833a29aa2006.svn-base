﻿using Coldairarrow.Entity.HR_RegistrManage;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.HR_RegistrManage
{
    public interface IHR_RegistrFamilyRelatBusiness
    {
        Task<PageResult<HR_RegistrFamilyRelat>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<HR_RegistrFamilyRelat> GetTheDataAsync(string id);
        Task AddDataAsync(HR_RegistrFamilyRelat data);
        Task UpdateDataAsync(HR_RegistrFamilyRelat data);
        Task AddDataListAsync(List<HR_RegistrFamilyRelat> data);
        Task UpdateDataListAsync(List<HR_RegistrFamilyRelat> data);
        Task DeleteDataAsync(List<string> ids);
        /// <summary>
        /// 通过员工信息删除相关数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task DeleteDataAsync(string id);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
        int AddData(HR_RegistrFamilyRelat data);
        void AddListData(List<HR_RegistrFamilyRelat> data);
        int UpdatListeData(List<HR_RegistrFamilyRelat> data);
        int UpdateData(HR_RegistrFamilyRelat data);
        int DeleteData(HR_RegistrFamilyRelat data);
        int DeleteDataListeData(List<HR_RegistrFamilyRelat> data);
    }
}