﻿using Coldairarrow.Business.Wechat_CostDept;
using Coldairarrow.Entity.Wechat_CostDept;
using Coldairarrow.Util;
using Coldairarrow.Util.DTO;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Wechat_CostDept
{
    [Route("/Wechat_CostDept/[controller]/[action]")]
    public class Wechat_CommitController : BaseApiController
    {
        #region DI

        public Wechat_CommitController(IWechat_CommitBusiness wechat_CommitBus)
        {
            _wechat_CommitBus = wechat_CommitBus;
        }

        IWechat_CommitBusiness _wechat_CommitBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Wechat_Commit>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _wechat_CommitBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Wechat_Commit> GetTheData(IdInputDTO input)
        {
            return await _wechat_CommitBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Wechat_Commit data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _wechat_CommitBus.AddDataAsync(data);
            }
            else
            {
                await _wechat_CommitBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _wechat_CommitBus.DeleteDataAsync(ids);
        }

        #endregion

        #region 二次开发
        /// <summary>
        /// 对接ERP退款接口
        /// </summary>
        /// <param name="openId"></param>
        /// <param name="guideId"></param>
        /// <param name="Project"></param>
        /// <param name="AreaName"></param>
        /// <param name="ProjectType"></param>
        /// <param name="POMCustomName"></param>
        /// <param name="POMMoneyName"></param>
        /// <param name="POMMoney"></param>
        /// <param name="POMCkr"></param>
        /// <param name="POMSkBankOpenInfo"></param>
        /// <param name="POMBankCardNO"></param>
        /// <param name="Pombz"></param>
        /// <param name="province"></param>
        /// <param name="city"></param>
        /// <param name="fileId"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult entryRefundInfo(string openId,string guideId,string Project, string AreaName, string ProjectType, string POMCustomName, string POMMoneyName, string POMMoney, string POMCkr, string POMSkBankOpenInfo, string POMBankCardNO, string Pombz, string province, string city,string fileId)
        {
            try
            {
                var index = Project + "," + AreaName + "," + ProjectType + "," + POMCustomName + "," + POMMoneyName + "," + POMMoney + "," + POMCkr + "," + POMSkBankOpenInfo + "," + POMBankCardNO + "," + Pombz + "," + province + "," + city;
                RefundInfoDTO content = new RefundInfoDTO 
                {
                    Project = Project,
                    AreaName = AreaName,
                    ProjectType = ProjectType,
                    POMCustomName = POMCustomName,
                    POMMoneyName = POMMoneyName,
                    POMMoney = POMMoney,
                    POMCkr = POMCkr,
                    POMSkBankOpenInfo = POMSkBankOpenInfo,
                    POMBankCardNO = POMBankCardNO,
                    Pombz = Pombz,
                    province = province,
                    city = city
                };
                var res = _wechat_CommitBus.EntryRefundInfo(index);
                JObject jo1 = (JObject)JsonConvert.DeserializeObject(res);//转换为json对象
                var retData = jo1["RetData"].ToString();
                if (retData.IsNullOrEmpty())
                {
                    return Error("服务器网络错误");
                }else if (retData.Contains("fail"))
                {
                    return Error(retData);
                }
                else
                {
                    var repId = retData.Replace(",","");
                    Wechat_Commit commit = new Wechat_Commit
                    {
                        W_Content = content.ToJson(),
                        W_GuideId = guideId,
                        W_OpenId = openId,
                        Id = IdHelper.GetId(),
                        CreateTime = DateTime.Now,
                        W_File = fileId,
                        W_Name = "投标保证金",
                        W_ERPID = repId,
                        W_Type = 1
                    };
                    _wechat_CommitBus.AddDataAsync(commit);
                    return Success(commit);
                }
               
            }
            catch (Exception ex)
            {
                return Error("失败"+ex.Message);
            }
        }
       
        /// <summary>
        /// 对接ERP发票接口
        /// </summary>
        /// <param name="openId"></param>
        /// <param name="guideId"></param>
        /// <param name="applycode"></param>
        /// <param name="fpType"></param>
        /// <param name="fpCode"></param>
        /// <param name="fpNumber"></param>
        /// <param name="fpUnit"></param>
        /// <param name="fpKpDate"></param>
        /// <param name="fpbhsMoeny"></param>
        /// <param name="fpzhsMoney"></param>
        /// <param name="fileId"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult entryPayInvoice(string openId, string guideId, string applycode, string fpType, string fpCode, string fpNumber, string fpUnit, string fpKpDate, string fpbhsMoeny, string fpzhsMoney,string fileId)
        {
            try
            {
                var index = applycode + "," + fpType + "," + fpCode + "," + fpNumber + "," + fpUnit + "," + fpKpDate + "," + fpbhsMoeny + "," + fpzhsMoney;
                PayInvoiceDTO content = new PayInvoiceDTO
                {
                    applycode = applycode,
                    fpType = fpType,
                    fpCode = fpCode,
                    fpNumber = fpNumber,
                    fpUnit = fpUnit,
                    fpKpDate = fpKpDate,
                    fpbhsMoeny = fpbhsMoeny,
                    fpzhsMoney = fpzhsMoney
                };
                var res = _wechat_CommitBus.EntryPayInvoice(index);
                JObject jo1 = (JObject)JsonConvert.DeserializeObject(res);//转换为json对象
                var retData = jo1["RetData"].ToString();
                if (retData.IsNullOrEmpty())
                {
                    return Error("服务器网络错误");
                }
                else if (retData.Contains("fail"))
                {
                    return Error(retData);
                }
                else
                {
                    var repId = retData.Replace(",", "");
                    Wechat_Commit commit = new Wechat_Commit
                    {
                        W_Content = content.ToJson(),
                        W_GuideId = guideId,
                        W_OpenId = openId,
                        Id = IdHelper.GetId(),
                        CreateTime = DateTime.Now,
                        W_File = fileId,
                        W_Name = "发票信息录入",
                        W_ERPID = applycode,
                        W_Type = 2
                    };
                    _wechat_CommitBus.AddDataAsync(commit);
                    return Success(commit);
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        /// <summary>
        /// 对接ERP发票接口-新post请求
        /// </summary>
        /// <param name="openId"></param>
        /// <param name="guideId"></param>
        /// <param name="applycode"></param>
        /// <param name="fileId"></param>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult entryInvoice(  )
        {
            try
            {
                //获取参数 
                string openId = HttpContext.Request.Form["openId"].ToString();
                string guideId=HttpContext.Request.Form["guideId"].ToString();
                string applycode = HttpContext.Request.Form["applycode"].ToString();
                string fileId = HttpContext.Request.Form["fileId"].ToString();
                string fileName = HttpContext.Request.Form["fileName"].ToString();
                var invoices_str = HttpContext.Request.Form["invoices"].ToString();
                var resString = "";
                var invoices = invoices_str.ToObject<List<PayInvoiceDTO>>();
                var resResult = true;
                if (fileName.IsNullOrEmpty())
                {
                    fileName = "发票录入";
                }
                foreach (var i in invoices)
                {
                    if (i.fpType.IsNullOrEmpty())
                    {
                        resString = i.id+"的fpType参数为空";
                        resResult = false;
                        break;
                    }else if (applycode.IsNullOrEmpty())
                    {
                        resString = i.id + "的applycode参数为空";
                        resResult = false;
                        break;
                    }
                    else if (i.fpCode.IsNullOrEmpty())
                    {
                        resString = i.id + "的fpCode参数为空";
                        resResult = false;
                        break;
                    }
                    else if (i.fpNumber.IsNullOrEmpty())
                    {
                        resString = i.id + "的fpNumber参数为空";
                        resResult = false;
                        break;
                    }
                    else if (i.fpUnit.IsNullOrEmpty())
                    {
                        resString = i.id + "的fpUnit参数为空";
                        resResult = false;
                        break;
                    }
                    else if (i.fpKpDate.IsNullOrEmpty())
                    {
                        resString = i.id + "的fpKpDate参数为空";
                        resResult = false;
                        break;
                    }
                    else if (i.fpbhsMoeny.IsNullOrEmpty())
                    {
                        resString = i.id + "的fpbhsMoeny参数为空";
                        resResult = false;
                        break;
                    }


                    var index = applycode + "," + i.fpType + "," + i.fpCode + "," + i.fpNumber + "," + i.fpUnit + "," + i.fpKpDate + "," + i.fpbhsMoeny + "," + i.fpzhsMoney;
                    var res = _wechat_CommitBus.EntryPayInvoice(index);
                    JObject jo1 = (JObject)JsonConvert.DeserializeObject(res);//转换为json对象
                    var retData = jo1["RetData"].ToString();
                    if (retData.IsNullOrEmpty())
                    {
                        resString = "服务器网络错误";
                        resResult = false;
                        break;
                        //return Error("服务器网络错误");
                    }
                    else if (retData.Contains("fail"))
                    {
                        resString = retData;
                        resResult = false;
                        break;
                        //return Error(retData);
                    }
                }
                if (resResult)
                {
                    Wechat_Commit commit = new Wechat_Commit
                    {
                        W_Content = invoices.ToJson(),
                        W_GuideId = guideId,
                        W_OpenId = openId,
                        Id = IdHelper.GetId(),
                        CreateTime = DateTime.Now,
                        W_File = fileId,
                        W_Name = fileName,
                        W_ERPID = applycode,
                        W_Type = 2
                    };
                    _wechat_CommitBus.AddDataAsync(commit);
                    return Success(commit);
                }
                else
                {
                    return Error(resString);
                }


            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        [NoCheckJWT]
        [HttpGet]
        //根据openId查询列表 时间倒序
        public AjaxResult GetListByOpenId(string index)
        {
            try
            {
                var list = _wechat_CommitBus.GetListByOpenId(index);
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 查询合同号
        /// </summary>
        /// <param name="contractCode"></param>
        /// <param name="place"></param>
        /// <returns></returns>

        [NoCheckJWT]
        [HttpGet]
        //根据openId查询列表 时间倒序
        public AjaxResult GetApplySubject(string contractCode)
        {
            try
            {
                //var projname = "";
                //switch (place) {
                //    case 0: projname= "长嘉汇项目"; break;
                //    case 1: projname = "上东汇项目"; break;
                //    case 2: projname = "礼嘉项目"; break;
                //};
                var res = _wechat_CommitBus.GetApplySubject(contractCode);
                JObject jo1 = (JObject)JsonConvert.DeserializeObject(res);//转换为json对象
                var retData = jo1["RetData"].ToString();
                var retMsg = jo1["RetMsg"].ToString();
                if (retMsg== "success")
                {
                    return Success(retData);
                }
                else
                {
                    return Error("服务器网络错误");
                }
              
            }
            catch (Exception ex)
            {
                return Error("失败"+ex.Message);
            }
        }

        [NoCheckJWT]
        [HttpGet]
        //根据id查询详情 
        public AjaxResult GetDataById(string index)
        {
            try
            {
                var data = _wechat_CommitBus.GetDataById(index);
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        [NoCheckJWT]
        [HttpGet]
        //根据id查询详情 
        public AjaxResult GetprojectInfo()
        {
            try
            {
                var res = _wechat_CommitBus.GetprojectInfo();
                JObject jo1 = (JObject)JsonConvert.DeserializeObject(res);//转换为json对象
                var retData = jo1["RetData"].ToString();
                if (retData.IsNullOrEmpty())
                {
                    return Error("服务器网络错误");
                }
                else if (retData.Contains("fail"))
                {
                    return Error(retData);
                }
                else
                {
                    return Success(retData);
                }
              
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        #endregion
    }
}