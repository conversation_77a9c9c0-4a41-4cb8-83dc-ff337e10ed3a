﻿<template>
  <a-card :bordered="false" :hoverable="true">
    <div class="table-operator">
      <a-button type="primary" :disabled="isAdd" icon="plus" @click="hanldleAdd()">新建</a-button>
      <a-button type="primary" icon="arrow-down" @click="exportExcel()">导出Excel</a-button>
      <a-button type="primary" icon="redo" @click="getDataList()">刷新</a-button>
    </div>

    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="10">
          <a-col :md="4" :sm="24">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="queryParam.condition">
                <a-select-option key="EmpName">姓名</a-select-option>
                <a-select-option key="EmpCode">员工编码</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item>
              <a-input v-model="queryParam.keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item label="流程状态">
              <a-select allowClear v-model="queryParam.wfState">
                <a-select-option v-for="(item, i) in flowStatus" :key="i" :value="item.value">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-button type="primary" icon="search" @click="
                () => {
                  this.pagination.current = 1
                  this.getDataList()
                }
              ">查询</a-button>
            <a-button style="margin-left: 8px" icon="sync" @click="() => (queryParam = {})">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table" :columns="columns" :rowKey="row => row.F_Id" :dataSource="data" :pagination="false"
      :loading="loading" :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true"
      :scroll="{ x: 1100, y: 450 }">
      <span slot="Sex" slot-scope="text">
        <template>
          <span>{{ text | getSex }}</span>
        </template>
      </span>
      <span slot="F_ChangesOperating" slot-scope="text">
        <template>
          <span>{{ text | getDic(changesOperatingList) }}</span>
        </template>
      </span>
      <span slot="F_ChangesType" slot-scope="text">
        <template>
          <span>{{ text | getDic(changesTypeList) }}</span>
        </template>
      </span>
      <span slot="F_DepartureDate" slot-scope="text">
        <template>
          <span>{{ text | dayjs('YYYY-MM-DD') }}</span>
        </template>
      </span>
      <ul slot="F_WFState" slot-scope="text, record" class="WFState">
        <template>
          <li @click="commonApi.flowOpen(record.F_WFId)" :class="commonApi.colorFun(text)">
            {{ text | getFlowStatus(flowStatus) }}
          </li>
        </template>
      </ul>
      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record.F_Id, true)">详情</a>
          <a-divider v-if="record.F_WFState == 0" type="vertical" />
          <a v-if="record.F_WFState == 0" @click="handleEdit(record.F_Id, false)">编辑</a>
          <a-divider v-if="record.F_WFState == 0" type="vertical" />
          <a v-if="record.F_WFState == 0" @click="handleDelete([record.F_Id])">删除</a>
        </template>
      </span>
    </a-table>
    <a-pagination show-size-changer :default-current="pagination.current" :defaultPageSize="pagination.pageSize"
      :showTotal="pagination.showTotal" :total="pagination.total" @showSizeChange="onShowSizeChange"
      @change="onChangeCurrent" style="margin: 5px 0;text-align: center;" />

    <edit-form ref="editForm" :parentObj="this"></edit-form>
  </a-card>
</template>

<script>
import { downLoadFile } from '@/utils/plugin/axios-plugin.js'
import { operateFile } from '@/utils/tools.js'
import EditForm from './EditForm'

const columns = [
  { title: '员工编码', dataIndex: 'EmpCode', width: 100, ellipsis: true },
  { title: '姓名', dataIndex: 'EmpName', width: 100 },
  // { title: '所属部门', dataIndex: 'DepartmentName', width: '100' },
  // { title: '任职职位', dataIndex: 'PostName', width: '100' },
  { title: '性别', dataIndex: 'Sex', width: 80, scopedSlots: { customRender: 'Sex' } },
  {
    title: '实际离职生效时间',
    dataIndex: 'F_TrueDepartureDate',
    width: 150,
    scopedSlots: { customRender: 'F_DepartureDate' }
  },
  { title: '身份证号码', dataIndex: 'IdCardNumber', width: 200, ellipsis: true },
  { title: '原用工状态', dataIndex: 'F_OriginalEmplStatus', width: 150 },
  { title: '目标用工状态', dataIndex: 'F_MobilzOriEmplStatus', width: 150 },
  {
    title: '变动操作',
    dataIndex: 'F_ChangesOperating',
    width: 150,
    scopedSlots: { customRender: 'F_ChangesOperating' }
  },
  { title: '变动类型', dataIndex: 'F_ChangesType', width: 150, scopedSlots: { customRender: 'F_ChangesType' } },
  { title: '流程状态', dataIndex: 'F_WFState', width: 150, scopedSlots: { customRender: 'F_WFState' }, fixed: 'right' },
  {
    title: '操作',
    fixed: 'right',
    width: 160,
    align: 'center',
    scopedSlots: { customRender: 'action' },
    fixed: 'right'
  }
]

export default {
  components: {
    EditForm
  },
  mounted () {
    //this.userId = this.$route.query.id;
    this.init()
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 15,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      filters: {},
      sorter: { field: 'F_TrueDepartureDate', order: 'desc' },
      loading: false,
      columns,
      queryParam: { condition: 'EmpName' },
      userId: 0,
      isAdd: true,
      flowStatus: [], //流程状态
      changesOperatingList: [], //离职变动操作
      changesTypeList: [], //离职变动类型
      selectedRowKeys: [],
      currUser: {} //当前登录人员
    }
  },
  methods: {
    onChangeCurrent (current, pageSize) {
      this.pagination.current = current
      this.getDataList()
    },
    onShowSizeChange (current, pageSize) {
      this.pagination.current = current
      this.pagination.pageSize = pageSize
      this.getDataList()
    },
    validAdd () {
      var empId = ''
      if (this.$op.EmployeesInfo) {
        empId = this.$op.EmployeesInfo.F_Id
      }
      this.$http.post('/HR_EmployeeInfoManage/HR_Departure/ValidateIsDeparture', { id: empId }).then(resJson => {
        if (resJson.Success) {
          this.isAdd = resJson.Data.isDep
        }
      })
    },
    init () {
      this.$http.get('/HR_EmployeeInfoManage/HR_Departure/GetInit').then(resJson => {
        this.flowStatus = resJson.Data.wfStates
        this.changesOperatingList = resJson.Data.changesOperatingList
        this.changesTypeList = resJson.Data.changesTypeList
        this.currUser = resJson.Data.user
        this.isAdd = resJson.Data.isDep
        console.log(this.currUser)
        this.getDataList()
      })
      this.validAdd()
    },
    handleTableChange (pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.filters = { ...filters }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    getDataList () {
      this.validAdd()
      if (this.currUser) {
        this.queryParam.UserId = this.currUser.UserId
      }
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/HR_EmployeeInfoManage/HR_Departure/GetMyDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: '',
          SortType: this.sorter.order,
          Search: this.queryParam
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange (selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    hasSelected () {
      return this.selectedRowKeys.length > 0
    },
    hanldleAdd () {
      this.$refs.editForm.openForm(0, '离职申请')
    },
    handleEdit (id, isedit) {
      let title = '修改申请'
      if (isedit) {
        title = '详情'
      }
      this.$refs.editForm.openForm(id, title, isedit)
    },
    handleDelete (ids) {
      var thisObj = this
      this.$confirm({
        title: '确认删除吗?',
        onOk () {
          return new Promise((resolve, reject) => {
            thisObj.$http.post('/HR_EmployeeInfoManage/HR_Departure/DeleteData', ids).then(resJson => {
              resolve()

              if (resJson.Success) {
                thisObj.$message.success('操作成功!')

                thisObj.getDataList()
              } else {
                thisObj.$message.error(resJson.Msg)
              }
            })
          })
        }
      })
    },
    exportExcel () {
      this.queryParam.selectIds = this.selectedRowKeys
      if (this.currUser) {
        this.queryParam.UserId = this.currUser.UserId
      }
      const data = {
        PageIndex: this.pagination.current,
        PageRows: this.pagination.pageSize,
        SortField: this.sorter.field || 'F_Id',
        SortType: this.sorter.order,
        Search: this.queryParam,
        ...this.filters
      }
      const url = '/HR_EmployeeInfoManage/HR_Departure/ExcelDownload'
      downLoadFile(
        url,
        data,
        function (res) {
          console.log(res)
          if (res) {
            operateFile(res, '离职')
          } else {
            console.log('失败')
          }
        },
        function (err) {
          console.log(err)
        }
      )
    }
  }
}
</script>
