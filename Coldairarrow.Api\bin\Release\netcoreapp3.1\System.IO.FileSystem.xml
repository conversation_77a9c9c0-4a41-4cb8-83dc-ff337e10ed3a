﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.FileSystem</name>
  </assembly>
  <members>
    <member name="T:System.IO.Directory">
      <summary>Exposes static methods for creating, moving, and enumerating through directories and subdirectories. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.IO.Directory.CreateDirectory(System.String)">
      <summary>Creates all directories and subdirectories in the specified path unless they already exist.</summary>
      <param name="path">The directory to create.</param>
      <returns>An object that represents the directory at the specified path. This object is returned regardless of whether a directory at the specified path already exists.</returns>
      <exception cref="T:System.IO.IOException">The directory specified by <paramref name="path" /> is a file.
-or-
The network name is not known.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.
-or-
<paramref name="path" /> is prefixed with, or contains, only a colon character (:).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> contains a colon character (:) that is not part of a drive label ("C:\").</exception>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String)">
      <summary>Deletes an empty directory from a specified path.</summary>
      <param name="path">The name of the empty directory to remove. This directory must be writable and empty.</param>
      <exception cref="T:System.IO.IOException">A file with the same name and location specified by <paramref name="path" /> exists.
-or-
The directory is the application's current working directory.
-or-
The directory specified by <paramref name="path" /> is not empty.
-or-
The directory is read-only or contains a read-only file.
-or-
The directory is being used by another process.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> does not exist or could not be found.
-or-
The specified path is invalid (for example, it is on an unmapped drive).</exception>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String,System.Boolean)">
      <summary>Deletes the specified directory and, if indicated, any subdirectories and files in the directory.</summary>
      <param name="path">The name of the directory to remove.</param>
      <param name="recursive">
        <see langword="true" /> to remove directories, subdirectories, and files in <paramref name="path" />; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.IO.IOException">A file with the same name and location specified by <paramref name="path" /> exists.
-or-
The directory specified by <paramref name="path" /> is read-only, or <paramref name="recursive" /> is <see langword="false" /> and <paramref name="path" /> is not an empty directory.
-or-
The directory is the application's current working directory.
-or-
The directory contains a read-only file.
-or-
The directory is being used by another process.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> does not exist or could not be found.
-or-
The specified path is invalid (for example, it is on an unmapped drive).</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String)">
      <summary>Returns an enumerable collection of directory names in a specified path.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <returns>An enumerable collection of the full names (including paths) for the directories in the directory specified by <paramref name="path" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String)">
      <summary>Returns an enumerable collection of directory names that match a search pattern in a specified path.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of directories in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <returns>An enumerable collection of the full names (including paths) for the directories in the directory specified by <paramref name="path" /> and that match the specified search pattern.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.
-or-
<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.
-or-
<paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String,System.IO.EnumerationOptions)">
      <param name="path" />
      <param name="searchPattern" />
      <param name="enumerationOptions" />
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>Returns an enumerable collection of directory names that match a search pattern in a specified path, and optionally searches subdirectories.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of directories in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or should include all subdirectories.
The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <returns>An enumerable collection of the full names (including paths) for the directories in the directory specified by <paramref name="path" /> and that match the specified search pattern and option.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.
-or-
<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.
-or-
<paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String)">
      <summary>Returns an enumerable collection of file names in a specified path.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <returns>An enumerable collection of the full names (including paths) for the files in the directory specified by <paramref name="path" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String)">
      <summary>Returns an enumerable collection of file names that match a search pattern in a specified path.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of files in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <returns>An enumerable collection of the full names (including paths) for the files in the directory specified by <paramref name="path" /> and that match the specified search pattern.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.
-or-
<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.
-or-
<paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String,System.IO.EnumerationOptions)">
      <param name="path" />
      <param name="searchPattern" />
      <param name="enumerationOptions" />
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>Returns an enumerable collection of file names that match a search pattern in a specified path, and optionally searches subdirectories.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of files in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or should include all subdirectories.
The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <returns>An enumerable collection of the full names (including paths) for the files in the directory specified by <paramref name="path" /> and that match the specified search pattern and option.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.
-or-
<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.
-or-
<paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String)">
      <summary>Returns an enumerable collection of file names and directory names in a specified path.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <returns>An enumerable collection of file-system entries in the directory specified by <paramref name="path" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String)">
      <summary>Returns an enumerable collection of file names and directory names that  match a search pattern in a specified path.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of file-system entries in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <returns>An enumerable collection of file-system entries in the directory specified by <paramref name="path" /> and that match the specified search pattern.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.
-or-
<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.
-or-
<paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String,System.IO.EnumerationOptions)">
      <param name="path" />
      <param name="searchPattern" />
      <param name="enumerationOptions" />
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>Returns an enumerable collection of file names and directory names that match a search pattern in a specified path, and optionally searches subdirectories.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against file-system entries in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values  that specifies whether the search operation should include only the current directory or should include all subdirectories.
The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <returns>An enumerable collection of file-system entries in the directory specified by <paramref name="path" /> and that match the specified search pattern and option.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.
-or-
<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.
-or-
<paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.Exists(System.String)">
      <summary>Determines whether the given path refers to an existing directory on disk.</summary>
      <param name="path">The path to test.</param>
      <returns>
        <see langword="true" /> if <paramref name="path" /> refers to an existing directory; <see langword="false" /> if the directory does not exist or an error occurs when trying to determine if the specified directory exists.</returns>
    </member>
    <member name="M:System.IO.Directory.GetCreationTime(System.String)">
      <summary>Gets the creation date and time of a directory.</summary>
      <param name="path">The path of the directory.</param>
      <returns>A structure that is set to the creation date and time for the specified directory. This value is expressed in local time.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
    </member>
    <member name="M:System.IO.Directory.GetCreationTimeUtc(System.String)">
      <summary>Gets the creation date and time, in Coordinated Universal Time (UTC) format, of a directory.</summary>
      <param name="path">The path of the directory.</param>
      <returns>A structure that is set to the creation date and time for the specified directory. This value is expressed in UTC time.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
    </member>
    <member name="M:System.IO.Directory.GetCurrentDirectory">
      <summary>Gets the current working directory of the application.</summary>
      <returns>A string that contains the absolute path of the current working directory, and does not end with a backslash (\).</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">The operating system is Windows CE, which does not have current directory functionality.
This method is available in the .NET Compact Framework, but is not currently supported.</exception>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String)">
      <summary>Returns the names of subdirectories (including their paths) in the specified directory.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <returns>An array of the full names (including paths) of subdirectories in the specified path, or an empty array if no directories are found.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String)">
      <summary>Returns the names of subdirectories (including their paths) that match the specified search pattern in the specified directory.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of subdirectories in <paramref name="path" />. This parameter can contain a combination of valid literal and wildcard characters, but it doesn't support regular expressions.</param>
      <returns>An array of the full names (including paths) of the subdirectories that match the search pattern in the specified directory, or an empty array if no directories are found.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using <see cref="M:System.IO.Path.GetInvalidPathChars" />.
-or-
<paramref name="searchPattern" /> doesn't contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String,System.IO.EnumerationOptions)">
      <param name="path" />
      <param name="searchPattern" />
      <param name="enumerationOptions" />
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>Returns the names of the subdirectories (including their paths) that match the specified search pattern in the specified directory, and optionally searches subdirectories.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of subdirectories in <paramref name="path" />. This parameter can contain a combination of valid literal and wildcard characters, but it doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include all subdirectories or only the current directory.</param>
      <returns>An array of the full names (including paths) of the subdirectories that match the specified criteria, or an empty array if no directories are found.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.
-or-
<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
    </member>
    <member name="M:System.IO.Directory.GetDirectoryRoot(System.String)">
      <summary>Returns the volume information, root information, or both for the specified path.</summary>
      <param name="path">The path of a file or directory.</param>
      <returns>A string that contains the volume information, root information, or both for the specified path.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String)">
      <summary>Returns the names of files (including their paths) in the specified directory.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <returns>An array of the full names (including paths) for the files in the specified directory, or an empty array if no files are found.</returns>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.
-or-
A network error has occurred.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is not found or is invalid (for example, it is on an unmapped drive).</exception>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String)">
      <summary>Returns the names of files (including their paths) that match the specified search pattern in the specified directory.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of files in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <returns>An array of the full names (including paths) for the files in the specified directory that match the specified search pattern, or an empty array if no files are found.</returns>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.
-or-
A network error has occurred.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using <see cref="M:System.IO.Path.GetInvalidPathChars" />.
-or-
<paramref name="searchPattern" /> doesn't contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is not found or is invalid (for example, it is on an unmapped drive).</exception>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String,System.IO.EnumerationOptions)">
      <param name="path" />
      <param name="searchPattern" />
      <param name="enumerationOptions" />
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>Returns the names of files (including their paths) that match the specified search pattern in the specified directory, using a value to determine whether to search subdirectories.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of files in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include all subdirectories or only the current directory.</param>
      <returns>An array of the full names (including paths) for the files in the specified directory that match the specified search pattern and option, or an empty array if no files are found.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.
-or-
<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="searchpattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is not found or is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.
-or-
A network error has occurred.</exception>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String)">
      <summary>Returns the names of all files and subdirectories in a specified path.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <returns>An array of the names of files and subdirectories in the specified directory, or an empty array if no files or subdirectories are found.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String)">
      <summary>Returns an array of file names and directory names that match a search pattern in a specified path.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of file and directories in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <returns>An array of file names and directory names that match the specified search criteria, or an empty array if no files or directories are found.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.
-or-
<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String,System.IO.EnumerationOptions)">
      <param name="path" />
      <param name="searchPattern" />
      <param name="enumerationOptions" />
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>Returns an array of all the file names and directory names that match a search pattern in a specified path, and optionally searches subdirectories.</summary>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of files and directories in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or should include all subdirectories.
The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <returns>An array of file the file names and directory names that match the specified search criteria, or an empty array if no files or directories are found.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.
-or-
<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.
-or-
<paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTime(System.String)">
      <summary>Returns the date and time the specified file or directory was last accessed.</summary>
      <param name="path">The file or directory for which to obtain access date and time information.</param>
      <returns>A structure that is set to the date and time the specified file or directory was last accessed. This value is expressed in local time.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">The <paramref name="path" /> parameter is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTimeUtc(System.String)">
      <summary>Returns the date and time, in Coordinated Universal Time (UTC) format, that the specified file or directory was last accessed.</summary>
      <param name="path">The file or directory for which to obtain access date and time information.</param>
      <returns>A structure that is set to the date and time the specified file or directory was last accessed. This value is expressed in UTC time.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">The <paramref name="path" /> parameter is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTime(System.String)">
      <summary>Returns the date and time the specified file or directory was last written to.</summary>
      <param name="path">The file or directory for which to obtain modification date and time information.</param>
      <returns>A structure that is set to the date and time the specified file or directory was last written to. This value is expressed in local time.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTimeUtc(System.String)">
      <summary>Returns the date and time, in Coordinated Universal Time (UTC) format, that the specified file or directory was last written to.</summary>
      <param name="path">The file or directory for which to obtain modification date and time information.</param>
      <returns>A structure that is set to the date and time the specified file or directory was last written to. This value is expressed in UTC time.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
    </member>
    <member name="M:System.IO.Directory.GetLogicalDrives">
      <summary>Retrieves the names of the logical drives on this computer in the form "&lt;drive letter&gt;:\".</summary>
      <returns>The logical drives on this computer.</returns>
      <exception cref="T:System.IO.IOException">An I/O error occurred (for example, a disk error).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.GetParent(System.String)">
      <summary>Retrieves the parent directory of the specified path, including both absolute and relative paths.</summary>
      <param name="path">The path for which to retrieve the parent directory.</param>
      <returns>The parent directory, or <see langword="null" /> if <paramref name="path" /> is the root directory, including the root of a UNC server or share name.</returns>
      <exception cref="T:System.IO.IOException">The directory specified by <paramref name="path" /> is read-only.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For more information, see the <see cref="T:System.IO.PathTooLongException" /> topic.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path was not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">.NET Framework only: The caller does not have the required permissions.</exception>
    </member>
    <member name="M:System.IO.Directory.Move(System.String,System.String)">
      <summary>Moves a file or a directory and its contents to a new location.</summary>
      <param name="sourceDirName">The path of the file or directory to move.</param>
      <param name="destDirName">The path to the new location for <paramref name="sourceDirName" />. If <paramref name="sourceDirName" /> is a file, then <paramref name="destDirName" /> must also be a file name.</param>
      <exception cref="T:System.IO.IOException">An attempt was made to move a directory to a different volume.
-or-
<paramref name="destDirName" /> already exists. See the Note in the Remarks section.
-or-
The <paramref name="sourceDirName" /> and <paramref name="destDirName" /> parameters refer to the same file or directory.
-or-
The directory or a file within it is being used by another process.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirName" /> or <paramref name="destDirName" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirName" /> or <paramref name="destDirName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path specified by <paramref name="sourceDirName" /> is invalid (for example, it is on an unmapped drive).</exception>
    </member>
    <member name="M:System.IO.Directory.SetCreationTime(System.String,System.DateTime)">
      <summary>Sets the creation date and time for the specified file or directory.</summary>
      <param name="path">The file or directory for which to set the creation date and time information.</param>
      <param name="creationTime">The date and time the file or directory was last written to. This value is expressed in local time.</param>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
    </member>
    <member name="M:System.IO.Directory.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>Sets the creation date and time, in Coordinated Universal Time (UTC) format, for the specified file or directory.</summary>
      <param name="path">The file or directory for which to set the creation date and time information.</param>
      <param name="creationTimeUtc">The date and time the directory or file was created. This value is expressed in local time.</param>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
    </member>
    <member name="M:System.IO.Directory.SetCurrentDirectory(System.String)">
      <summary>Sets the application's current working directory to the specified directory.</summary>
      <param name="path">The path to which the current working directory is set.</param>
      <exception cref="T:System.IO.IOException">An I/O error occurred.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access unmanaged code.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified directory was not found.</exception>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTime(System.String,System.DateTime)">
      <summary>Sets the date and time the specified file or directory was last accessed.</summary>
      <param name="path">The file or directory for which to set the access date and time information.</param>
      <param name="lastAccessTime">An object that contains the value to set for the access date and time of <paramref name="path" />. This value is expressed in local time.</param>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>Sets the date and time, in Coordinated Universal Time (UTC) format, that the specified file or directory was last accessed.</summary>
      <param name="path">The file or directory for which to set the access date and time information.</param>
      <param name="lastAccessTimeUtc">An object that  contains the value to set for the access date and time of <paramref name="path" />. This value is expressed in UTC time.</param>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTime(System.String,System.DateTime)">
      <summary>Sets the date and time a directory was last written to.</summary>
      <param name="path">The path of the directory.</param>
      <param name="lastWriteTime">The date and time the directory was last written to. This value is expressed in local time.</param>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> was not found (for example, the directory doesn't exist or it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> was not found (for example, the directory doesn't exist or it is on an unmapped drive).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>Sets the date and time, in Coordinated Universal Time (UTC) format, that a directory was last written to.</summary>
      <param name="path">The path of the directory.</param>
      <param name="lastWriteTimeUtc">The date and time the directory was last written to. This value is expressed in UTC time.</param>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> was not found (for example, the directory doesn't exist or it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> was not found (for example, the directory doesn't exist or it is on an unmapped drive).</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
    </member>
    <member name="T:System.IO.DirectoryInfo">
      <summary>Exposes instance methods for creating, moving, and enumerating through directories and subdirectories. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.IO.DirectoryInfo.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.DirectoryInfo" /> class on the specified path.</summary>
      <param name="path">A string specifying the path on which to create the <see langword="DirectoryInfo" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contains invalid characters such as ", &lt;, &gt;, or |.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.Create">
      <summary>Creates a directory.</summary>
      <exception cref="T:System.IO.IOException">The directory cannot be created.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.CreateSubdirectory(System.String)">
      <summary>Creates a subdirectory or subdirectories on the specified path. The specified path can be relative to this instance of the <see cref="T:System.IO.DirectoryInfo" /> class.</summary>
      <param name="path">The specified path. This cannot be a different disk volume or Universal Naming Convention (UNC) name.</param>
      <returns>The last directory specified in <paramref name="path" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> does not specify a valid file path or contains invalid <see langword="DirectoryInfo" /> characters.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">The subdirectory cannot be created.
-or-
A file or directory already has the name specified by <paramref name="path" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have code access permission to create the directory.
-or-
The caller does not have code access permission to read the directory described by the returned <see cref="T:System.IO.DirectoryInfo" /> object.  This can occur when the <paramref name="path" /> parameter describes an existing directory.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> contains a colon character (:) that is not part of a drive label ("C:\").</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete">
      <summary>Deletes this <see cref="T:System.IO.DirectoryInfo" /> if it is empty.</summary>
      <exception cref="T:System.UnauthorizedAccessException">The directory contains a read-only file.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory described by this <see cref="T:System.IO.DirectoryInfo" /> object does not exist or could not be found.</exception>
      <exception cref="T:System.IO.IOException">The directory is not empty.
-or-
The directory is the application's current working directory.
-or-
There is an open handle on the directory, and the operating system is Windows XP or earlier. This open handle can result from enumerating directories. For more information, see How to: Enumerate Directories and Files.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete(System.Boolean)">
      <summary>Deletes this instance of a <see cref="T:System.IO.DirectoryInfo" />, specifying whether to delete subdirectories and files.</summary>
      <param name="recursive">
        <see langword="true" /> to delete this directory, its subdirectories, and all files; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.UnauthorizedAccessException">The directory contains a read-only file.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory described by this <see cref="T:System.IO.DirectoryInfo" /> object does not exist or could not be found.</exception>
      <exception cref="T:System.IO.IOException">The directory is read-only.
-or-
The directory contains one or more files or subdirectories and <paramref name="recursive" /> is <see langword="false" />.
-or-
The directory is the application's current working directory.
-or-
There is an open handle on the directory or on one of its files, and the operating system is Windows XP or earlier. This open handle can result from enumerating directories and files. For more information, see How to: Enumerate Directories and Files.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories">
      <summary>Returns an enumerable collection of directory information in the current directory.</summary>
      <returns>An enumerable collection of directories in the current directory.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String)">
      <summary>Returns an enumerable collection of directory information that matches a specified search pattern.</summary>
      <param name="searchPattern">The search string to match against the names of directories.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <returns>An enumerable collection of directories that matches <paramref name="searchPattern" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String,System.IO.EnumerationOptions)">
      <param name="searchPattern" />
      <param name="enumerationOptions" />
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String,System.IO.SearchOption)">
      <summary>Returns an enumerable collection of directory information that matches a specified search pattern and search subdirectory option.</summary>
      <param name="searchPattern">The search string to match against the names of directories.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or all subdirectories. The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <returns>An enumerable collection of directories that matches <paramref name="searchPattern" /> and <paramref name="searchOption" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles">
      <summary>Returns an enumerable collection of file information in the current directory.</summary>
      <returns>An enumerable collection of the files in the current directory.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String)">
      <summary>Returns an enumerable collection of file information that matches a search pattern.</summary>
      <param name="searchPattern">The search string to match against the names of files.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <returns>An enumerable collection of files that matches <paramref name="searchPattern" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid, (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String,System.IO.EnumerationOptions)">
      <param name="searchPattern" />
      <param name="enumerationOptions" />
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String,System.IO.SearchOption)">
      <summary>Returns an enumerable collection of file information that matches a specified search pattern and search subdirectory option.</summary>
      <param name="searchPattern">The search string to match against the names of files.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or all subdirectories. The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <returns>An enumerable collection of files that matches <paramref name="searchPattern" /> and <paramref name="searchOption" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos">
      <summary>Returns an enumerable collection of file system information in the current directory.</summary>
      <returns>An enumerable collection of file system information in the current directory.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String)">
      <summary>Returns an enumerable collection of file system information that matches a specified search pattern.</summary>
      <param name="searchPattern">The search string to match against the names of directories.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <returns>An enumerable collection of file system information objects that matches <paramref name="searchPattern" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String,System.IO.EnumerationOptions)">
      <param name="searchPattern" />
      <param name="enumerationOptions" />
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>Returns an enumerable collection of file system information that matches a specified search pattern and search subdirectory option.</summary>
      <param name="searchPattern">The search string to match against the names of directories.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or all subdirectories. The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <returns>An enumerable collection of file system information objects that matches <paramref name="searchPattern" /> and <paramref name="searchOption" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="P:System.IO.DirectoryInfo.Exists">
      <summary>Gets a value indicating whether the directory exists.</summary>
      <returns>
        <see langword="true" /> if the directory exists; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories">
      <summary>Returns the subdirectories of the current directory.</summary>
      <returns>An array of <see cref="T:System.IO.DirectoryInfo" /> objects.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String)">
      <summary>Returns an array of directories in the current <see cref="T:System.IO.DirectoryInfo" /> matching the given search criteria.</summary>
      <param name="searchPattern">The search string to match against the names of directories.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <returns>An array of type <see langword="DirectoryInfo" /> matching <paramref name="searchPattern" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" /> contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see langword="DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String,System.IO.EnumerationOptions)">
      <param name="searchPattern" />
      <param name="enumerationOptions" />
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String,System.IO.SearchOption)">
      <summary>Returns an array of directories in the current <see cref="T:System.IO.DirectoryInfo" /> matching the given search criteria and using a value to determine whether to search subdirectories.</summary>
      <param name="searchPattern">The search string to match against the names of directories.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or all subdirectories.</param>
      <returns>An array of type <see langword="DirectoryInfo" /> matching <paramref name="searchPattern" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" /> contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see langword="DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles">
      <summary>Returns a file list from the current directory.</summary>
      <returns>An array of type <see cref="T:System.IO.FileInfo" />.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is invalid, such as being on an unmapped drive.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String)">
      <summary>Returns a file list from the current directory matching the given search pattern.</summary>
      <param name="searchPattern">The search string to match against the names of files.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <returns>An array of type <see cref="T:System.IO.FileInfo" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" /> contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String,System.IO.EnumerationOptions)">
      <param name="searchPattern" />
      <param name="enumerationOptions" />
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String,System.IO.SearchOption)">
      <summary>Returns a file list from the current directory matching the given search pattern and using a value to determine whether to search subdirectories.</summary>
      <param name="searchPattern">The search string to match against the names of files.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or all subdirectories.</param>
      <returns>An array of type <see cref="T:System.IO.FileInfo" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" /> contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos">
      <summary>Returns an array of strongly typed <see cref="T:System.IO.FileSystemInfo" /> entries representing all the files and subdirectories in a directory.</summary>
      <returns>An array of strongly typed <see cref="T:System.IO.FileSystemInfo" /> entries.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is invalid (for example, it is on an unmapped drive).</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String)">
      <summary>Retrieves an array of strongly typed <see cref="T:System.IO.FileSystemInfo" /> objects representing the files and subdirectories that match the specified search criteria.</summary>
      <param name="searchPattern">The search string to match against the names of directories and files.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <returns>An array of strongly typed <see langword="FileSystemInfo" /> objects matching the search criteria.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" /> contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String,System.IO.EnumerationOptions)">
      <param name="searchPattern" />
      <param name="enumerationOptions" />
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>Retrieves an array of <see cref="T:System.IO.FileSystemInfo" /> objects that represent the files and subdirectories matching the specified search criteria.</summary>
      <param name="searchPattern">The search string to match against the names of directories and files.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters, but it doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or all subdirectories. The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <returns>An array of file system entries that match the search criteria.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" /> contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.MoveTo(System.String)">
      <summary>Moves a <see cref="T:System.IO.DirectoryInfo" /> instance and its contents to a new path.</summary>
      <param name="destDirName">The name and path to which to move this directory. The destination cannot be another disk volume or a directory with the identical name. It can be an existing directory to which you want to add this directory as a subdirectory.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destDirName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destDirName" /> is an empty string (''").</exception>
      <exception cref="T:System.IO.IOException">An attempt was made to move a directory to a different volume.
-or-
<paramref name="destDirName" /> already exists.
-or-
You are not authorized to access this path.
-or-
The directory being moved and the destination directory have the same name.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The destination directory cannot be found.</exception>
    </member>
    <member name="P:System.IO.DirectoryInfo.Name">
      <summary>Gets the name of this <see cref="T:System.IO.DirectoryInfo" /> instance.</summary>
      <returns>The directory name.</returns>
    </member>
    <member name="P:System.IO.DirectoryInfo.Parent">
      <summary>Gets the parent directory of a specified subdirectory.</summary>
      <returns>The parent directory, or <see langword="null" /> if the path is null or if the file path denotes a root (such as \, C:\, or \\server\share).</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="P:System.IO.DirectoryInfo.Root">
      <summary>Gets the root portion of the directory.</summary>
      <returns>An object that represents the root of the directory.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.ToString">
      <summary>Returns the original path that was passed to the <see cref="T:System.IO.DirectoryInfo" /> constructor. Use the <see cref="P:System.IO.DirectoryInfo.FullName" /> or <see cref="P:System.IO.DirectoryInfo.Name" /> properties for the full path or file/directory name instead of this method.</summary>
      <returns>The original path that was passed by the user.</returns>
    </member>
    <member name="T:System.IO.Enumeration.FileSystemEntry">
      <summary>Provides a lower level view of <see cref="T:System.IO.FileSystemInfo" /> to help process and filter find results.</summary>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEntry.Attributes">
      <summary>Gets the attributes for this entry.</summary>
      <returns>The attributes for this entry.</returns>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEntry.CreationTimeUtc">
      <summary>Gets the creation time for the entry or the oldest available time stamp if the operating system does not support creation time stamps.</summary>
      <returns>The creation time for the entry.</returns>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEntry.Directory">
      <summary>Gets the full path of the directory this entry resides in.</summary>
      <returns>The full path of this entry's directory.</returns>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEntry.FileName">
      <summary>Gets the file name for this entry.</summary>
      <returns>This entry's file name.</returns>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEntry.IsDirectory">
      <summary>Gets a value that indicates whether this entry is a directory.</summary>
      <returns>
        <see langword="true" /> if the entry is a directory; otherwise, <see langword="true" />.</returns>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEntry.IsHidden">
      <summary>Gets a value that indicates whether the file has the hidden attribute.</summary>
      <returns>
        <see langword="true" /> if the file has the hidden attribute; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEntry.LastAccessTimeUtc">
      <summary>Gets a datetime offset that represents the last access time in UTC.</summary>
      <returns>The last access time in UTC.</returns>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEntry.LastWriteTimeUtc">
      <summary>Gets a datetime offset that represents the last write time in UTC.</summary>
      <returns>The last write time in UTC.</returns>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEntry.Length">
      <summary>Gets the length of the file, in bytes.</summary>
      <returns>The file length in bytes.</returns>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEntry.OriginalRootDirectory">
      <summary>Gets the root directory for the enumeration as specified in the constructor.</summary>
      <returns>The original root directory.</returns>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEntry.RootDirectory">
      <summary>Gets the full path of the root directory used for the enumeration.</summary>
      <returns>The root directory.</returns>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEntry.ToFileSystemInfo">
      <summary>Converts the value of this instance to a <see cref="T:System.IO.FileSystemInfo" />.</summary>
      <returns>The value of this instance as a <see cref="T:System.IO.FileSystemInfo" />.</returns>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEntry.ToFullPath">
      <summary>Returns the full path of the find result.</summary>
      <returns>A string representing the full path.</returns>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEntry.ToSpecifiedFullPath">
      <summary>Returns the full path for the find results, based on the initially provided path.</summary>
      <returns>A string representing the full path.</returns>
    </member>
    <member name="T:System.IO.Enumeration.FileSystemEnumerable`1">
      <summary>Allows utilizing custom filter predicates and transform delegates for enumeration purposes.</summary>
      <typeparam name="TResult">The type that this enumerable encapsulates.</typeparam>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEnumerable`1.#ctor(System.String,System.IO.Enumeration.FileSystemEnumerable{`0}.FindTransform,System.IO.EnumerationOptions)">
      <param name="directory" />
      <param name="transform" />
      <param name="options" />
    </member>
    <member name="T:System.IO.Enumeration.FileSystemEnumerable`1.FindPredicate">
      <summary>Encapsulates a method for filtering out find results.</summary>
      <param name="entry">A reference to the file system entry that will be evaluated with the predicate condition.</param>
      <typeparam name="TResult" />
      <returns>
        <see langword="true" /> if the predicate condition is met; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.IO.Enumeration.FileSystemEnumerable`1.FindTransform">
      <summary>Encapsulates a method for transforming raw find data into a result.</summary>
      <param name="entry">A reference to the file system entry that will be evaluated with the predicate condition.</param>
      <typeparam name="TResult" />
      <returns>An instance of the type that this delegate encapsulates.</returns>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEnumerable`1.GetEnumerator">
      <summary>Retrieves the enumerator for this type of result.</summary>
      <returns>An enumerator.</returns>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEnumerable`1.ShouldIncludePredicate">
      <summary>Gets or sets the predicate that can be used to verify if the TResults should be included.</summary>
      <returns>The include predicate.</returns>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEnumerable`1.ShouldRecursePredicate">
      <summary>Gets or sets the predicate that can be used to verify if the TResults should be recursed.</summary>
      <returns>The recurse predicate.</returns>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEnumerable`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gets an enumerator that can be used to iterate.</summary>
      <returns>An enumerator instance.</returns>
    </member>
    <member name="T:System.IO.Enumeration.FileSystemEnumerator`1">
      <typeparam name="TResult" />
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEnumerator`1.#ctor(System.String,System.IO.EnumerationOptions)">
      <summary>Encapsulates a find operation.</summary>
      <param name="directory">The directory to search in.</param>
      <param name="options">Enumeration options to use.</param>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEnumerator`1.ContinueOnError(System.Int32)">
      <summary>When overriden in a derived class, returns a value that indicates whether to continue execution or throw the default exception.</summary>
      <param name="error">The native error code.</param>
      <returns>
        <see langword="true" /> to continue; <see langword="false" /> to throw the default exception for the given error.</returns>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEnumerator`1.Current">
      <summary>Gets the currently visited element.</summary>
      <returns>The currently visited element.</returns>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEnumerator`1.Dispose">
      <summary>Releases the resources used by the current instance of the <see cref="T:System.IO.Enumeration.FileSystemEnumerator`1" /> class.</summary>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEnumerator`1.Dispose(System.Boolean)">
      <summary>When overridden in a derived class, releases the unmanaged resources used by the <see cref="T:System.IO.Enumeration.FileSystemEnumerator`1" /> class and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEnumerator`1.MoveNext">
      <summary>Advances the enumerator to the next item of the <see cref="T:System.IO.Enumeration.FileSystemEnumerator`1" />.</summary>
      <returns>
        <see langword="true" /> if the enumerator successfully advanced to the next item; <see langword="false" /> if the end of the enumerator has been passed.</returns>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEnumerator`1.OnDirectoryFinished(System.ReadOnlySpan{System.Char})">
      <summary>When overriden in a derived class, this method is called whenever the end of a directory is reached.</summary>
      <param name="directory">The directory path as a read-only span.</param>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEnumerator`1.Reset">
      <summary>Always throws <see cref="T:System.NotSupportedException" />.</summary>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEnumerator`1.ShouldIncludeEntry(System.IO.Enumeration.FileSystemEntry@)">
      <summary>When overridden in a derived class, determines whether the specified file system entry should be included in the results.</summary>
      <param name="entry">A file system entry reference.</param>
      <returns>
        <see langword="true" /> if the specified file system entry should be included in the results; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEnumerator`1.ShouldRecurseIntoEntry(System.IO.Enumeration.FileSystemEntry@)">
      <summary>When overridden in a derived class, determines whether the specified file system entry should be recursed.</summary>
      <param name="entry">A file system entry reference.</param>
      <returns>
        <see langword="true" /> if the specified directory entry should be recursed into; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.IO.Enumeration.FileSystemEnumerator`1.System#Collections#IEnumerator#Current">
      <summary>Gets the currently visited object.</summary>
      <returns>The currently visited object.</returns>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemEnumerator`1.TransformEntry(System.IO.Enumeration.FileSystemEntry@)">
      <summary>When overridden in a derived class, generates the result type from the current entry.</summary>
      <param name="entry" />
      <returns>The result type from the current entry.</returns>
    </member>
    <member name="T:System.IO.Enumeration.FileSystemName">
      <summary>Provides methods for matching file system names.</summary>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemName.MatchesSimpleExpression(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.Boolean)">
      <summary>Verifies if the given expression matches the given name. Supports the following wildcards: '*' and '?'. The backslash character '' escapes.</summary>
      <param name="expression">The expression to match with.</param>
      <param name="name">The name to check against the expression.</param>
      <param name="ignoreCase">
        <see langword="true" /> to ignore case (default); <see langword="false" /> if the match should be case-sensitive.</param>
      <returns>
        <see langword="true" /> if the given expression matches the given name; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemName.MatchesWin32Expression(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.Boolean)">
      <summary>Verifies if the given Win32 expression matches the given name. Supports the following wildcards: '*', '?', '&lt;', '&gt;', '"'. The backslash character '' escapes.</summary>
      <param name="expression">The expression to match with, such as "*.foo".</param>
      <param name="name">The name to check against the expression.</param>
      <param name="ignoreCase">
        <see langword="true" /> to ignore case (default), <see langword="false" /> if the match should be case-sensitive.</param>
      <returns>
        <see langword="true" /> if the given expression matches the given name; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.IO.Enumeration.FileSystemName.TranslateWin32Expression(System.String)">
      <summary>Translates the given Win32 expression. Change '*' and '?' to '&lt;', '&gt;' and '"' to match Win32 behavior.</summary>
      <param name="expression">The expression to translate.</param>
      <returns>A string with the translated Win32 expression.</returns>
    </member>
    <member name="T:System.IO.EnumerationOptions" />
    <member name="M:System.IO.EnumerationOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.EnumerationOptions" /> class with the recommended default options.</summary>
    </member>
    <member name="P:System.IO.EnumerationOptions.AttributesToSkip">
      <summary>Gets or sets the attributes to skip. The default is <c>FileAttributes.Hidden | FileAttributes.System</c>.</summary>
      <returns>The attributes to skip.</returns>
    </member>
    <member name="P:System.IO.EnumerationOptions.BufferSize">
      <summary>Gets or sets the suggested buffer size, in bytes. The default is 0 (no suggestion).</summary>
      <returns>The buffer size.</returns>
    </member>
    <member name="P:System.IO.EnumerationOptions.IgnoreInaccessible">
      <summary>Gets or sets a value that indicates whether to skip files or directories when access is denied (for example, <see cref="T:System.UnauthorizedAccessException" /> or <see cref="T:System.Security.SecurityException" />). The default is <see langword="true" />.</summary>
      <returns>
        <see langword="true" /> to skip innacessible files or directories; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.IO.EnumerationOptions.MatchCasing">
      <summary>Gets or sets the case matching behavior.</summary>
      <returns>One of the enumeration values that indicates the case matching behavior.</returns>
    </member>
    <member name="P:System.IO.EnumerationOptions.MatchType">
      <summary>Gets or sets the match type.</summary>
      <returns>One of the enumeration values that indicates the match type.</returns>
    </member>
    <member name="P:System.IO.EnumerationOptions.RecurseSubdirectories">
      <summary>Gets or sets a value that indicates whether to recurse into subdirectories while enumerating. The default is <see langword="false" />.</summary>
      <returns>
        <see langword="true" /> to recurse into subdirectories; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.IO.EnumerationOptions.ReturnSpecialDirectories">
      <summary>Gets or sets a value that indicates whether to return the special directory entries "." and "..".</summary>
      <returns>
        <see langword="true" /> to return the special directory entries "." and ".."; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.IO.File">
      <summary>Provides static methods for the creation, copying, deletion, moving, and opening of a single file, and aids in the creation of <see cref="T:System.IO.FileStream" /> objects.</summary>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Appends lines to a file, and then closes the file. If the specified file does not exist, this method creates a file, writes the specified lines to the file, and then closes the file.</summary>
      <param name="path">The file to append the lines to. The file is created if it doesn't already exist.</param>
      <param name="contents">The lines to append to the file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">Either <paramref name="path" /> or <paramref name="contents" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid (for example, the directory doesn't exist or it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to write to the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specifies a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> is a directory.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>Appends lines to a file by using a specified encoding, and then closes the file. If the specified file does not exist, this method creates a file, writes the specified lines to the file, and then closes the file.</summary>
      <param name="path">The file to append the lines to. The file is created if it doesn't already exist.</param>
      <param name="contents">The lines to append to the file.</param>
      <param name="encoding">The character encoding to use.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">Either <paramref name="path" />, <paramref name="contents" />, or <paramref name="encoding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid (for example, the directory doesn't exist or it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specifies a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> is a directory.
-or-
The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllLinesAsync(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding,System.Threading.CancellationToken)">
      <summary>Asynchronously appends lines to a file by using a specified encoding, and then closes the file. If the specified file does not exist, this method creates a file, writes the specified lines to the file, and then closes the file.</summary>
      <param name="path">The file to append the lines to. The file is created if it doesn't already exist.</param>
      <param name="contents">The lines to append to the file.</param>
      <param name="encoding">The character encoding to use.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous append operation.</returns>
    </member>
    <member name="M:System.IO.File.AppendAllLinesAsync(System.String,System.Collections.Generic.IEnumerable{System.String},System.Threading.CancellationToken)">
      <summary>Asynchronously appends lines to a file, and then closes the file. If the specified file does not exist, this method creates a file, writes the specified lines to the file, and then closes the file.</summary>
      <param name="path">The file to append the lines to. The file is created if it doesn't already exist.</param>
      <param name="contents">The lines to append to the file.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous append operation.</returns>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String)">
      <summary>Opens a file, appends the specified string to the file, and then closes the file. If the file does not exist, this method creates a file, writes the specified string to the file, then closes the file.</summary>
      <param name="path">The file to append the specified string to.</param>
      <param name="contents">The string to append to the file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, the directory doesn't exist or it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String,System.Text.Encoding)">
      <summary>Appends the specified string to the file using the specified encoding, creating the file if it does not already exist.</summary>
      <param name="path">The file to append the specified string to.</param>
      <param name="contents">The string to append to the file.</param>
      <param name="encoding">The character encoding to use.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, the directory doesn't exist or it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllTextAsync(System.String,System.String,System.Text.Encoding,System.Threading.CancellationToken)">
      <summary>Asynchronously opens a file or creates the file if it does not already exist, appends the specified string to the file using the specified encoding, and then closes the file.</summary>
      <param name="path">The file to append the specified string to.</param>
      <param name="contents">The string to append to the file.</param>
      <param name="encoding">The character encoding to use.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous append operation.</returns>
    </member>
    <member name="M:System.IO.File.AppendAllTextAsync(System.String,System.String,System.Threading.CancellationToken)">
      <summary>Asynchronously opens a file or creates a file if it does not already exist, appends the specified string to the file, and then closes the file.</summary>
      <param name="path">The file to append the specified string to.</param>
      <param name="contents">The string to append to the file.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous append operation.</returns>
    </member>
    <member name="M:System.IO.File.AppendText(System.String)">
      <summary>Creates a <see cref="T:System.IO.StreamWriter" /> that appends UTF-8 encoded text to an existing file, or to a new file if the specified file does not exist.</summary>
      <param name="path">The path to the file to append to.</param>
      <returns>A stream writer that appends UTF-8 encoded text to the specified file or to a new file.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, the directory doesn't exist or it is on an unmapped drive).</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String)">
      <summary>Copies an existing file to a new file. Overwriting a file of the same name is not allowed.</summary>
      <param name="sourceFileName">The file to copy.</param>
      <param name="destFileName">The name of the destination file. This cannot be a directory or an existing file.</param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.
-or-
<paramref name="sourceFileName" /> or <paramref name="destFileName" /> specifies a directory.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path specified in <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" /> was not found.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> exists.
-or-
An I/O error has occurred.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String,System.Boolean)">
      <summary>Copies an existing file to a new file. Overwriting a file of the same name is allowed.</summary>
      <param name="sourceFileName">The file to copy.</param>
      <param name="destFileName">The name of the destination file. This cannot be a directory.</param>
      <param name="overwrite">
        <see langword="true" /> if the destination file can be overwritten; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.
-or-
<paramref name="destFileName" /> is read-only.
-or-
<paramref name="overwrite" /> is <see langword="true" />, <paramref name="destFileName" /> exists and is hidden, but <paramref name="sourceFileName" /> is not hidden.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.
-or-
<paramref name="sourceFileName" /> or <paramref name="destFileName" /> specifies a directory.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path specified in <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" /> was not found.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> exists and <paramref name="overwrite" /> is <see langword="false" />.
-or-
An I/O error has occurred.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.Create(System.String)">
      <summary>Creates or overwrites a file in the specified path.</summary>
      <param name="path">The path and name of the file to create.</param>
      <returns>A <see cref="T:System.IO.FileStream" /> that provides read/write access to the file specified in <paramref name="path" />.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.
-or-
<paramref name="path" /> specified a file that is read-only.
-or-
<paramref name="path" /> specified a file that is hidden.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while creating the file.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32)">
      <summary>Creates or overwrites a file in the specified path, specifying a buffer size.</summary>
      <param name="path">The path and name of the file to create.</param>
      <param name="bufferSize">The number of bytes buffered for reads and writes to the file.</param>
      <returns>A <see cref="T:System.IO.FileStream" /> with the specified buffer size that provides read/write access to the file specified in <paramref name="path" />.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.
-or-
<paramref name="path" /> specified a file that is read-only.
-or-
<paramref name="path" /> specified a file that is hidden.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while creating the file.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32,System.IO.FileOptions)">
      <summary>Creates or overwrites a file in the specified path, specifying a buffer size and options that describe how to create or overwrite the file.</summary>
      <param name="path">The path and name of the file to create.</param>
      <param name="bufferSize">The number of bytes buffered for reads and writes to the file.</param>
      <param name="options">One of the <see cref="T:System.IO.FileOptions" /> values that describes how to create or overwrite the file.</param>
      <returns>A new file with the specified buffer size.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.
-or-
<paramref name="path" /> specified a file that is read-only.
-or-
<paramref name="path" /> specified a file that is hidden.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while creating the file.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.CreateText(System.String)">
      <summary>Creates or opens a file for writing UTF-8 encoded text. If the file already exists, its contents are overwritten.</summary>
      <param name="path">The file to be opened for writing.</param>
      <returns>A <see cref="T:System.IO.StreamWriter" /> that writes to the specified file using UTF-8 encoding.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.
-or-
<paramref name="path" /> specified a file that is read-only.
-or-
<paramref name="path" /> specified a file that is hidden.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.Decrypt(System.String)">
      <summary>Decrypts a file that was encrypted by the current account using the <see cref="M:System.IO.File.Encrypt(System.String)" /> method.</summary>
      <param name="path">A path that describes a file to decrypt.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> parameter is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="path" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DriveNotFoundException">An invalid drive was specified.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file described by the <paramref name="path" /> parameter could not be found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. For example, the encrypted file is already open.
-or-
This operation is not supported on the current platform.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.NotSupportedException">The file system is not NTFS.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <paramref name="path" /> parameter specified a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
The <paramref name="path" /> parameter specified a directory.
-or-
The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.Delete(System.String)">
      <summary>Deletes the specified file.</summary>
      <param name="path">The name of the file to be deleted. Wildcard characters are not supported.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">The specified file is in use.
-or-
There is an open handle on the file, and the operating system is Windows XP or earlier. This open handle can result from enumerating directories and files. For more information, see How to: Enumerate Directories and Files.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.
-or-
The file is an executable file that is in use.
-or-
<paramref name="path" /> is a directory.
-or-
<paramref name="path" /> specified a read-only file.</exception>
    </member>
    <member name="M:System.IO.File.Encrypt(System.String)">
      <summary>Encrypts a file so that only the account used to encrypt the file can decrypt it.</summary>
      <param name="path">A path that describes a file to encrypt.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="path" /> parameter is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="path" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DriveNotFoundException">An invalid drive was specified.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file described by the <paramref name="path" /> parameter could not be found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.
-or-
This operation is not supported on the current platform.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.NotSupportedException">The file system is not NTFS.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <paramref name="path" /> parameter specified a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
The <paramref name="path" /> parameter specified a directory.
-or-
The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.Exists(System.String)">
      <summary>Determines whether the specified file exists.</summary>
      <param name="path">The file to check.</param>
      <returns>
        <see langword="true" /> if the caller has the required permissions and <paramref name="path" /> contains the name of an existing file; otherwise, <see langword="false" />. This method also returns <see langword="false" /> if <paramref name="path" /> is <see langword="null" />, an invalid path, or a zero-length string. If the caller does not have sufficient permissions to read the specified file, no exception is thrown and the method returns <see langword="false" /> regardless of the existence of <paramref name="path" />.</returns>
    </member>
    <member name="M:System.IO.File.GetAttributes(System.String)">
      <summary>Gets the <see cref="T:System.IO.FileAttributes" /> of the file on the path.</summary>
      <param name="path">The path to the file.</param>
      <returns>The <see cref="T:System.IO.FileAttributes" /> of the file on the path.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> represents a file and is invalid, such as being on an unmapped drive, or the file cannot be found.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> represents a directory and is invalid, such as being on an unmapped drive, or the directory cannot be found.</exception>
      <exception cref="T:System.IO.IOException">This file is being used by another process.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.GetCreationTime(System.String)">
      <summary>Returns the creation date and time of the specified file or directory.</summary>
      <param name="path">The file or directory for which to obtain creation date and time information.</param>
      <returns>A <see cref="T:System.DateTime" /> structure set to the creation date and time for the specified file or directory. This value is expressed in local time.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.GetCreationTimeUtc(System.String)">
      <summary>Returns the creation date and time, in coordinated universal time (UTC), of the specified file or directory.</summary>
      <param name="path">The file or directory for which to obtain creation date and time information.</param>
      <returns>A <see cref="T:System.DateTime" /> structure set to the creation date and time for the specified file or directory. This value is expressed in UTC time.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.GetLastAccessTime(System.String)">
      <summary>Returns the date and time the specified file or directory was last accessed.</summary>
      <param name="path">The file or directory for which to obtain access date and time information.</param>
      <returns>A <see cref="T:System.DateTime" /> structure set to the date and time that the specified file or directory was last accessed. This value is expressed in local time.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.GetLastAccessTimeUtc(System.String)">
      <summary>Returns the date and time, in coordinated universal time (UTC), that the specified file or directory was last accessed.</summary>
      <param name="path">The file or directory for which to obtain access date and time information.</param>
      <returns>A <see cref="T:System.DateTime" /> structure set to the date and time that the specified file or directory was last accessed. This value is expressed in UTC time.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.GetLastWriteTime(System.String)">
      <summary>Returns the date and time the specified file or directory was last written to.</summary>
      <param name="path">The file or directory for which to obtain write date and time information.</param>
      <returns>A <see cref="T:System.DateTime" /> structure set to the date and time that the specified file or directory was last written to. This value is expressed in local time.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.GetLastWriteTimeUtc(System.String)">
      <summary>Returns the date and time, in coordinated universal time (UTC), that the specified file or directory was last written to.</summary>
      <param name="path">The file or directory for which to obtain write date and time information.</param>
      <returns>A <see cref="T:System.DateTime" /> structure set to the date and time that the specified file or directory was last written to. This value is expressed in UTC time.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.Move(System.String,System.String)">
      <summary>Moves a specified file to a new location, providing the option to specify a new file name.</summary>
      <param name="sourceFileName">The name of the file to move. Can include a relative or absolute path.</param>
      <param name="destFileName">The new path and name for the file.</param>
      <exception cref="T:System.IO.IOException">The destination file already exists.
-or-
<paramref name="sourceFileName" /> was not found.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is a zero-length string, contains only white space, or contains invalid characters as defined in <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path specified in <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is invalid, (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.Move(System.String,System.String,System.Boolean)">
      <summary>Moves a specified file to a new location, providing the options to specify a new file name and to overwrite the destination file if it already exists.</summary>
      <param name="sourceFileName">The name of the file to move. Can include a relative or absolute path.</param>
      <param name="destFileName">The new path and name for the file.</param>
      <param name="overwrite">
        <see langword="true" /> to overwrite the destination file if it already exists; <see langword="false" /> otherwise.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="sourceFileName" /> was not found.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is a zero-length string, contains only white space, or contains invalid characters as defined in <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path specified in <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is invalid, (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode)">
      <summary>Opens a <see cref="T:System.IO.FileStream" /> on the specified path with read/write access with no sharing.</summary>
      <param name="path">The file to open.</param>
      <param name="mode">A <see cref="T:System.IO.FileMode" /> value that specifies whether a file is created if one does not exist, and determines whether the contents of existing files are retained or overwritten.</param>
      <returns>A <see cref="T:System.IO.FileStream" /> opened in the specified mode and path, with read/write access and not shared.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.
-or-
<paramref name="mode" /> is <see cref="F:System.IO.FileMode.Create" /> and the specified file is a hidden file.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> specified an invalid value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Opens a <see cref="T:System.IO.FileStream" /> on the specified path, with the specified mode and access with no sharing.</summary>
      <param name="path">The file to open.</param>
      <param name="mode">A <see cref="T:System.IO.FileMode" /> value that specifies whether a file is created if one does not exist, and determines whether the contents of existing files are retained or overwritten.</param>
      <param name="access">A <see cref="T:System.IO.FileAccess" /> value that specifies the operations that can be performed on the file.</param>
      <returns>An unshared <see cref="T:System.IO.FileStream" /> that provides access to the specified file, with the specified mode and access.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.
-or-
<paramref name="access" /> specified <see langword="Read" /> and <paramref name="mode" /> specified <see langword="Create" />, <see langword="CreateNew" />, <see langword="Truncate" />, or <see langword="Append" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only and <paramref name="access" /> is not <see langword="Read" />.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.
-or-
<paramref name="mode" /> is <see cref="F:System.IO.FileMode.Create" /> and the specified file is a hidden file.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> or <paramref name="access" /> specified an invalid value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Opens a <see cref="T:System.IO.FileStream" /> on the specified path, having the specified mode with read, write, or read/write access and the specified sharing option.</summary>
      <param name="path">The file to open.</param>
      <param name="mode">A <see cref="T:System.IO.FileMode" /> value that specifies whether a file is created if one does not exist, and determines whether the contents of existing files are retained or overwritten.</param>
      <param name="access">A <see cref="T:System.IO.FileAccess" /> value that specifies the operations that can be performed on the file.</param>
      <param name="share">A <see cref="T:System.IO.FileShare" /> value specifying the type of access other threads have to the file.</param>
      <returns>A <see cref="T:System.IO.FileStream" /> on the specified path, having the specified mode with read, write, or read/write access and the specified sharing option.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.
-or-
<paramref name="access" /> specified <see langword="Read" /> and <paramref name="mode" /> specified <see langword="Create" />, <see langword="CreateNew" />, <see langword="Truncate" />, or <see langword="Append" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only and <paramref name="access" /> is not <see langword="Read" />.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.
-or-
<paramref name="mode" /> is <see cref="F:System.IO.FileMode.Create" /> and the specified file is a hidden file.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" />, <paramref name="access" />, or <paramref name="share" /> specified an invalid value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.OpenRead(System.String)">
      <summary>Opens an existing file for reading.</summary>
      <param name="path">The file to be opened for reading.</param>
      <returns>A read-only <see cref="T:System.IO.FileStream" /> on the specified path.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
    </member>
    <member name="M:System.IO.File.OpenText(System.String)">
      <summary>Opens an existing UTF-8 encoded text file for reading.</summary>
      <param name="path">The file to be opened for reading.</param>
      <returns>A <see cref="T:System.IO.StreamReader" /> on the specified path.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.OpenWrite(System.String)">
      <summary>Opens an existing file or creates a new file for writing.</summary>
      <param name="path">The file to be opened for writing.</param>
      <returns>An unshared <see cref="T:System.IO.FileStream" /> object on the specified path with <see cref="F:System.IO.FileAccess.Write" /> access.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.
-or-
<paramref name="path" /> specified a read-only file or directory.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.ReadAllBytes(System.String)">
      <summary>Opens a binary file, reads the contents of the file into a byte array, and then closes the file.</summary>
      <param name="path">The file to open for reading.</param>
      <returns>A byte array containing the contents of the file.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">This operation is not supported on the current platform.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.ReadAllBytesAsync(System.String,System.Threading.CancellationToken)">
      <summary>Asynchronously opens a binary file, reads the contents of the file into a byte array, and then closes the file.</summary>
      <param name="path">The file to open for reading.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous read operation, which wraps the byte array containing the contents of the file.</returns>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String)">
      <summary>Opens a text file, reads all lines of the file, and then closes the file.</summary>
      <param name="path">The file to open for reading.</param>
      <returns>A string array containing all lines of the file.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String,System.Text.Encoding)">
      <summary>Opens a file, reads all lines of the file with the specified encoding, and then closes the file.</summary>
      <param name="path">The file to open for reading.</param>
      <param name="encoding">The encoding applied to the contents of the file.</param>
      <returns>A string array containing all lines of the file.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.ReadAllLinesAsync(System.String,System.Text.Encoding,System.Threading.CancellationToken)">
      <summary>Asynchronously opens a text file, reads all lines of the file with the specified encoding, and then closes the file.</summary>
      <param name="path">The file to open for reading.</param>
      <param name="encoding">The encoding applied to the contents of the file.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous read operation, which wraps the string array containing all lines of the file.</returns>
    </member>
    <member name="M:System.IO.File.ReadAllLinesAsync(System.String,System.Threading.CancellationToken)">
      <summary>Asynchronously opens a text file, reads all lines of the file, and then closes the file.</summary>
      <param name="path">The file to open for reading.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous read operation, which wraps the string array containing all lines of the file.</returns>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String)">
      <summary>Opens a text file, reads all the text in the file, and then closes the file.</summary>
      <param name="path">The file to open for reading.</param>
      <returns>A string containing all the text in the file.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String,System.Text.Encoding)">
      <summary>Opens a file, reads all text in the file with the specified encoding, and then closes the file.</summary>
      <param name="path">The file to open for reading.</param>
      <param name="encoding">The encoding applied to the contents of the file.</param>
      <returns>A string containing all text in the file.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.ReadAllTextAsync(System.String,System.Text.Encoding,System.Threading.CancellationToken)">
      <summary>Asynchronously opens a text file, reads all text in the file with the specified encoding, and then closes the file.</summary>
      <param name="path">The file to open for reading.</param>
      <param name="encoding">The encoding applied to the contents of the file.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous read operation, which wraps the string containing all text in the file.</returns>
    </member>
    <member name="M:System.IO.File.ReadAllTextAsync(System.String,System.Threading.CancellationToken)">
      <summary>Asynchronously opens a text file, reads all the text in the file, and then closes the file.</summary>
      <param name="path">The file to open for reading.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous read operation, which wraps the string containing all text in the file.</returns>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String)">
      <summary>Reads the lines of a file.</summary>
      <param name="path">The file to read.</param>
      <returns>All the lines of the file, or the lines that are the result of a query.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specifies a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> is a directory.
-or-
The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String,System.Text.Encoding)">
      <summary>Read the lines of a file that has a specified encoding.</summary>
      <param name="path">The file to read.</param>
      <param name="encoding">The encoding that is applied to the contents of the file.</param>
      <returns>All the lines of the file, or the lines that are the result of a query.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specifies a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> is a directory.
-or-
The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.Replace(System.String,System.String,System.String)">
      <summary>Replaces the contents of a specified file with the contents of another file, deleting the original file, and creating a backup of the replaced file.</summary>
      <param name="sourceFileName">The name of a file that replaces the file specified by <paramref name="destinationFileName" />.</param>
      <param name="destinationFileName">The name of the file being replaced.</param>
      <param name="destinationBackupFileName">The name of the backup file.</param>
      <exception cref="T:System.ArgumentException">The path described by the <paramref name="destinationFileName" /> parameter was not of a legal form.
-or-
The path described by the <paramref name="destinationBackupFileName" /> parameter was not of a legal form.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="destinationFileName" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DriveNotFoundException">An invalid drive was specified.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file described by the current <see cref="T:System.IO.FileInfo" /> object could not be found.
-or-
The file described by the <paramref name="destinationBackupFileName" /> parameter could not be found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.
-or-
The <paramref name="sourceFileName" /> and <paramref name="destinationFileName" /> parameters specify the same file.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The operating system is Windows 98 Second Edition or earlier and the files system is not NTFS.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <paramref name="sourceFileName" /> or <paramref name="destinationFileName" /> parameter specifies a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
Source or destination parameters specify a directory instead of a file.
-or-
The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.Replace(System.String,System.String,System.String,System.Boolean)">
      <summary>Replaces the contents of a specified file with the contents of another file, deleting the original file, and creating a backup of the replaced file and optionally ignores merge errors.</summary>
      <param name="sourceFileName">The name of a file that replaces the file specified by <paramref name="destinationFileName" />.</param>
      <param name="destinationFileName">The name of the file being replaced.</param>
      <param name="destinationBackupFileName">The name of the backup file.</param>
      <param name="ignoreMetadataErrors">
        <see langword="true" /> to ignore merge errors (such as attributes and access control lists (ACLs)) from the replaced file to the replacement file; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentException">The path described by the <paramref name="destinationFileName" /> parameter was not of a legal form.
-or-
The path described by the <paramref name="destinationBackupFileName" /> parameter was not of a legal form.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="destinationFileName" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DriveNotFoundException">An invalid drive was specified.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file described by the current <see cref="T:System.IO.FileInfo" /> object could not be found.
-or-
The file described by the <paramref name="destinationBackupFileName" /> parameter could not be found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.
-or-
The <paramref name="sourceFileName" /> and <paramref name="destinationFileName" /> parameters specify the same file.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The operating system is Windows 98 Second Edition or earlier and the files system is not NTFS.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <paramref name="sourceFileName" /> or <paramref name="destinationFileName" /> parameter specifies a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
Source or destination parameters specify a directory instead of a file.
-or-
The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.SetAttributes(System.String,System.IO.FileAttributes)">
      <summary>Sets the specified <see cref="T:System.IO.FileAttributes" /> of the file on the specified path.</summary>
      <param name="path">The path to the file.</param>
      <param name="fileAttributes">A bitwise combination of the enumeration values.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is empty, contains only white spaces, contains invalid characters, or the file attribute is invalid.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.SetCreationTime(System.String,System.DateTime)">
      <summary>Sets the date and time the file was created.</summary>
      <param name="path">The file for which to set the creation date and time information.</param>
      <param name="creationTime">A <see cref="T:System.DateTime" /> containing the value to set for the creation date and time of <paramref name="path" />. This value is expressed in local time.</param>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while performing the operation.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> specifies a value outside the range of dates, times, or both permitted for this operation.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>Sets the date and time, in coordinated universal time (UTC), that the file was created.</summary>
      <param name="path">The file for which to set the creation date and time information.</param>
      <param name="creationTimeUtc">A <see cref="T:System.DateTime" /> containing the value to set for the creation date and time of <paramref name="path" />. This value is expressed in UTC time.</param>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while performing the operation.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> specifies a value outside the range of dates, times, or both permitted for this operation.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
    </member>
    <member name="M:System.IO.File.SetLastAccessTime(System.String,System.DateTime)">
      <summary>Sets the date and time the specified file was last accessed.</summary>
      <param name="path">The file for which to set the access date and time information.</param>
      <param name="lastAccessTime">A <see cref="T:System.DateTime" /> containing the value to set for the last access date and time of <paramref name="path" />. This value is expressed in local time.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
    </member>
    <member name="M:System.IO.File.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>Sets the date and time, in coordinated universal time (UTC), that the specified file was last accessed.</summary>
      <param name="path">The file for which to set the access date and time information.</param>
      <param name="lastAccessTimeUtc">A <see cref="T:System.DateTime" /> containing the value to set for the last access date and time of <paramref name="path" />. This value is expressed in UTC time.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
    </member>
    <member name="M:System.IO.File.SetLastWriteTime(System.String,System.DateTime)">
      <summary>Sets the date and time that the specified file was last written to.</summary>
      <param name="path">The file for which to set the date and time information.</param>
      <param name="lastWriteTime">A <see cref="T:System.DateTime" /> containing the value to set for the last write date and time of <paramref name="path" />. This value is expressed in local time.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
    </member>
    <member name="M:System.IO.File.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>Sets the date and time, in coordinated universal time (UTC), that the specified file was last written to.</summary>
      <param name="path">The file for which to set the date and time information.</param>
      <param name="lastWriteTimeUtc">A <see cref="T:System.DateTime" /> containing the value to set for the last write date and time of <paramref name="path" />. This value is expressed in UTC time.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllBytes(System.String,System.Byte[])">
      <summary>Creates a new file, writes the specified byte array to the file, and then closes the file. If the target file already exists, it is overwritten.</summary>
      <param name="path">The file to write to.</param>
      <param name="bytes">The bytes to write to the file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" /> or the byte array is empty.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
<paramref name="path" /> specified a file that is hidden.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllBytesAsync(System.String,System.Byte[],System.Threading.CancellationToken)">
      <summary>Asynchronously creates a new file, writes the specified byte array to the file, and then closes the file. If the target file already exists, it is overwritten.</summary>
      <param name="path">The file to write to.</param>
      <param name="bytes">The bytes to write to the file.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Creates a new file, writes a collection of strings to the file, and then closes the file.</summary>
      <param name="path">The file to write to.</param>
      <param name="contents">The lines to write to the file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">Either <paramref name="path" /> or <paramref name="contents" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
<paramref name="path" /> specified a file that is hidden.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> is a directory.
-or-
The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>Creates a new file by using the specified encoding, writes a collection of strings to the file, and then closes the file.</summary>
      <param name="path">The file to write to.</param>
      <param name="contents">The lines to write to the file.</param>
      <param name="encoding">The character encoding to use.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">Either <paramref name="path" />, <paramref name="contents" />, or <paramref name="encoding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
<paramref name="path" /> specified a file that is hidden.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> is a directory.
-or-
The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.String[])">
      <summary>Creates a new file, write the specified string array to the file, and then closes the file.</summary>
      <param name="path">The file to write to.</param>
      <param name="contents">The string array to write to the file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">Either <paramref name="path" /> or <paramref name="contents" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
<paramref name="path" /> specified a file that is hidden.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.String[],System.Text.Encoding)">
      <summary>Creates a new file, writes the specified string array to the file by using the specified encoding, and then closes the file.</summary>
      <param name="path">The file to write to.</param>
      <param name="contents">The string array to write to the file.</param>
      <param name="encoding">An <see cref="T:System.Text.Encoding" /> object that represents the character encoding applied to the string array.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">Either <paramref name="path" /> or <paramref name="contents" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
<paramref name="path" /> specified a file that is hidden.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllLinesAsync(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding,System.Threading.CancellationToken)">
      <summary>Asynchronously creates a new file, write the specified lines to the file by using the specified encoding, and then closes the file.</summary>
      <param name="path">The file to write to.</param>
      <param name="contents">The lines to write to the file.</param>
      <param name="encoding">The character encoding to use.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="M:System.IO.File.WriteAllLinesAsync(System.String,System.Collections.Generic.IEnumerable{System.String},System.Threading.CancellationToken)">
      <summary>Asynchronously creates a new file, writes the specified lines to the file, and then closes the file.</summary>
      <param name="path">The file to write to.</param>
      <param name="contents">The lines to write to the file.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String)">
      <summary>Creates a new file, writes the specified string to the file, and then closes the file. If the target file already exists, it is overwritten.</summary>
      <param name="path">The file to write to.</param>
      <param name="contents">The string to write to the file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
<paramref name="path" /> specified a file that is hidden.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String,System.Text.Encoding)">
      <summary>Creates a new file, writes the specified string to the file using the specified encoding, and then closes the file. If the target file already exists, it is overwritten.</summary>
      <param name="path">The file to write to.</param>
      <param name="contents">The string to write to the file.</param>
      <param name="encoding">The encoding to apply to the string.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.
-or-
<paramref name="path" /> specified a file that is hidden.
-or-
This operation is not supported on the current platform.
-or-
<paramref name="path" /> specified a directory.
-or-
The caller does not have the required permission.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllTextAsync(System.String,System.String,System.Text.Encoding,System.Threading.CancellationToken)">
      <summary>Asynchronously creates a new file, writes the specified string to the file using the specified encoding, and then closes the file. If the target file already exists, it is overwritten.</summary>
      <param name="path">The file to write to.</param>
      <param name="contents">The string to write to the file.</param>
      <param name="encoding">The encoding to apply to the string.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="M:System.IO.File.WriteAllTextAsync(System.String,System.String,System.Threading.CancellationToken)">
      <summary>Asynchronously creates a new file, writes the specified string to the file, and then closes the file. If the target file already exists, it is overwritten.</summary>
      <param name="path">The file to write to.</param>
      <param name="contents">The string to write to the file.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <returns>A task that represents the asynchronous write operation.</returns>
    </member>
    <member name="T:System.IO.FileInfo">
      <summary>Provides properties and instance methods for the creation, copying, deletion, moving, and opening of files, and aids in the creation of <see cref="T:System.IO.FileStream" /> objects. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.IO.FileInfo.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.FileInfo" /> class, which acts as a wrapper for a file path.</summary>
      <param name="fileName">The fully qualified name of the new file, or the relative file name. Do not end the path with the directory separator character.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">The file name is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to <paramref name="fileName" /> is denied.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="fileName" /> contains a colon (:) in the middle of the string.</exception>
    </member>
    <member name="M:System.IO.FileInfo.AppendText">
      <summary>Creates a <see cref="T:System.IO.StreamWriter" /> that appends text to the file represented by this instance of the <see cref="T:System.IO.FileInfo" />.</summary>
      <returns>A new <see langword="StreamWriter" />.</returns>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String)">
      <summary>Copies an existing file to a new file, disallowing the overwriting of an existing file.</summary>
      <param name="destFileName">The name of the new file to copy to.</param>
      <returns>A new file with a fully qualified path.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.IO.IOException">An error occurs, or the destination file already exists.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">A directory path is passed in, or the file is being moved to a different drive.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory specified in <paramref name="destFileName" /> does not exist.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contains a colon (:) within the string but does not specify the volume.</exception>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String,System.Boolean)">
      <summary>Copies an existing file to a new file, allowing the overwriting of an existing file.</summary>
      <param name="destFileName">The name of the new file to copy to.</param>
      <param name="overwrite">
        <see langword="true" /> to allow an existing file to be overwritten; otherwise, <see langword="false" />.</param>
      <returns>A new file, or an overwrite of an existing file if <paramref name="overwrite" /> is <see langword="true" />. If the file exists and <paramref name="overwrite" /> is <see langword="false" />, an <see cref="T:System.IO.IOException" /> is thrown.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.IO.IOException">An error occurs, or the destination file already exists and <paramref name="overwrite" /> is <see langword="false" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory specified in <paramref name="destFileName" /> does not exist.</exception>
      <exception cref="T:System.UnauthorizedAccessException">A directory path is passed in, or the file is being moved to a different drive.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contains a colon (:) in the middle of the string.</exception>
    </member>
    <member name="M:System.IO.FileInfo.Create">
      <summary>Creates a file.</summary>
      <returns>A new file.</returns>
    </member>
    <member name="M:System.IO.FileInfo.CreateText">
      <summary>Creates a <see cref="T:System.IO.StreamWriter" /> that writes a new text file.</summary>
      <returns>A new <see langword="StreamWriter" />.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The file name is a directory.</exception>
      <exception cref="T:System.IO.IOException">The disk is read-only.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.FileInfo.Decrypt">
      <summary>Decrypts a file that was encrypted by the current account using the <see cref="M:System.IO.FileInfo.Encrypt" /> method.</summary>
      <exception cref="T:System.IO.DriveNotFoundException">An invalid drive was specified.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file described by the current <see cref="T:System.IO.FileInfo" /> object could not be found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.NotSupportedException">The file system is not NTFS.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Microsoft Windows NT or later.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The file described by the current <see cref="T:System.IO.FileInfo" /> object is read-only.
-or-
This operation is not supported on the current platform.
-or-
The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.FileInfo.Delete">
      <summary>Permanently deletes a file.</summary>
      <exception cref="T:System.IO.IOException">The target file is open or memory-mapped on a computer running Microsoft Windows NT.
-or-
There is an open handle on the file, and the operating system is Windows XP or earlier. This open handle can result from enumerating directories and files. For more information, see How to: Enumerate Directories and Files.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The path is a directory.</exception>
    </member>
    <member name="P:System.IO.FileInfo.Directory">
      <summary>Gets an instance of the parent directory.</summary>
      <returns>A <see cref="T:System.IO.DirectoryInfo" /> object representing the parent directory of this file.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="P:System.IO.FileInfo.DirectoryName">
      <summary>Gets a string representing the directory's full path.</summary>
      <returns>A string representing the directory's full path.</returns>
      <exception cref="T:System.ArgumentNullException">
        <see langword="null" /> was passed in for the directory name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The fully qualified path name exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.FileInfo.Encrypt">
      <summary>Encrypts a file so that only the account used to encrypt the file can decrypt it.</summary>
      <exception cref="T:System.IO.DriveNotFoundException">An invalid drive was specified.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file described by the current <see cref="T:System.IO.FileInfo" /> object could not be found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.NotSupportedException">The file system is not NTFS.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Microsoft Windows NT or later.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The file described by the current <see cref="T:System.IO.FileInfo" /> object is read-only.
-or-
This operation is not supported on the current platform.
-or-
The caller does not have the required permission.</exception>
    </member>
    <member name="P:System.IO.FileInfo.Exists">
      <summary>Gets a value indicating whether a file exists.</summary>
      <returns>
        <see langword="true" /> if the file exists; <see langword="false" /> if the file does not exist or if the file is a directory.</returns>
    </member>
    <member name="P:System.IO.FileInfo.IsReadOnly">
      <summary>Gets or sets a value that determines if the current file is read only.</summary>
      <returns>
        <see langword="true" /> if the current file is read only; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.IO.FileNotFoundException">The file described by the current <see cref="T:System.IO.FileInfo" /> object could not be found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">This operation is not supported on the current platform.
-or-
The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">The user does not have write permission, but attempted to set this property to <see langword="false" />.</exception>
    </member>
    <member name="P:System.IO.FileInfo.Length">
      <summary>Gets the size, in bytes, of the current file.</summary>
      <returns>The size of the current file in bytes.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot update the state of the file or directory.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.
-or-
The <see langword="Length" /> property is called for a directory.</exception>
    </member>
    <member name="M:System.IO.FileInfo.MoveTo(System.String)">
      <summary>Moves a specified file to a new location, providing the option to specify a new file name.</summary>
      <param name="destFileName">The path to move the file to, which can specify a different file name.</param>
      <exception cref="T:System.IO.IOException">An I/O error occurs, such as the destination file already exists or the destination device is not ready.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destFileName" /> is read-only or is a directory.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file is not found.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contains a colon (:) in the middle of the string.</exception>
    </member>
    <member name="M:System.IO.FileInfo.MoveTo(System.String,System.Boolean)">
      <summary>Moves a specified file to a new location, providing the options to specify a new file name and to overwrite the destination file if it already exists.</summary>
      <param name="destFileName">The path to move the file to, which can specify a different file name.</param>
      <param name="overwrite">
        <see langword="true" /> to overwrite the destination file if it already exists; <see langword="false" /> otherwise.</param>
      <exception cref="T:System.IO.IOException">An I/O error occurred, such as the destination device is not ready.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> is empty, contains only white spaces, or contains invalid characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destFileName" /> is read-only or is a directory.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file is not found.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contains a colon (:) in the middle of the string.</exception>
    </member>
    <member name="P:System.IO.FileInfo.Name">
      <summary>Gets the name of the file.</summary>
      <returns>The name of the file.</returns>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode)">
      <summary>Opens a file in the specified mode.</summary>
      <param name="mode">A <see cref="T:System.IO.FileMode" /> constant specifying the mode (for example, <see langword="Open" /> or <see langword="Append" />) in which to open the file.</param>
      <returns>A file opened in the specified mode, with read/write access and unshared.</returns>
      <exception cref="T:System.IO.FileNotFoundException">The file is not found.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The file is read-only or is a directory.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">The file is already open.</exception>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess)">
      <summary>Opens a file in the specified mode with read, write, or read/write access.</summary>
      <param name="mode">A <see cref="T:System.IO.FileMode" /> constant specifying the mode (for example, <see langword="Open" /> or <see langword="Append" />) in which to open the file.</param>
      <param name="access">A <see cref="T:System.IO.FileAccess" /> constant specifying whether to open the file with <see langword="Read" />, <see langword="Write" />, or <see langword="ReadWrite" /> file access.</param>
      <returns>A <see cref="T:System.IO.FileStream" /> object opened in the specified mode and access, and unshared.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file is not found.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> is read-only or is a directory.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">The file is already open.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is empty or contains only white spaces.</exception>
      <exception cref="T:System.ArgumentNullException">One or more arguments is null.</exception>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Opens a file in the specified mode with read, write, or read/write access and the specified sharing option.</summary>
      <param name="mode">A <see cref="T:System.IO.FileMode" /> constant specifying the mode (for example, <see langword="Open" /> or <see langword="Append" />) in which to open the file.</param>
      <param name="access">A <see cref="T:System.IO.FileAccess" /> constant specifying whether to open the file with <see langword="Read" />, <see langword="Write" />, or <see langword="ReadWrite" /> file access.</param>
      <param name="share">A <see cref="T:System.IO.FileShare" /> constant specifying the type of access other <see langword="FileStream" /> objects have to this file.</param>
      <returns>A <see cref="T:System.IO.FileStream" /> object opened with the specified mode, access, and sharing options.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file is not found.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> is read-only or is a directory.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">The file is already open.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is empty or contains only white spaces.</exception>
      <exception cref="T:System.ArgumentNullException">One or more arguments is null.</exception>
    </member>
    <member name="M:System.IO.FileInfo.OpenRead">
      <summary>Creates a read-only <see cref="T:System.IO.FileStream" />.</summary>
      <returns>A new read-only <see cref="T:System.IO.FileStream" /> object.</returns>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> is read-only or is a directory.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">The file is already open.</exception>
    </member>
    <member name="M:System.IO.FileInfo.OpenText">
      <summary>Creates a <see cref="T:System.IO.StreamReader" /> with UTF8 encoding that reads from an existing text file.</summary>
      <returns>A new <see langword="StreamReader" /> with UTF8 encoding.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file is not found.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> is read-only or is a directory.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive.</exception>
    </member>
    <member name="M:System.IO.FileInfo.OpenWrite">
      <summary>Creates a write-only <see cref="T:System.IO.FileStream" />.</summary>
      <returns>A write-only unshared <see cref="T:System.IO.FileStream" /> object for a new or existing file.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The path specified when creating an instance of the <see cref="T:System.IO.FileInfo" /> object is read-only or is a directory.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path specified when creating an instance of the <see cref="T:System.IO.FileInfo" /> object is invalid, such as being on an unmapped drive.</exception>
    </member>
    <member name="M:System.IO.FileInfo.Replace(System.String,System.String)">
      <summary>Replaces the contents of a specified file with the file described by the current <see cref="T:System.IO.FileInfo" /> object, deleting the original file, and creating a backup of the replaced file.</summary>
      <param name="destinationFileName">The name of a file to replace with the current file.</param>
      <param name="destinationBackupFileName">The name of a file with which to create a backup of the file described by the <paramref name="destFileName" /> parameter.</param>
      <returns>A <see cref="T:System.IO.FileInfo" /> object that encapsulates information about the file described by the <paramref name="destFileName" /> parameter.</returns>
      <exception cref="T:System.ArgumentException">The path described by the <paramref name="destFileName" /> parameter was not of a legal form.
-or-
The path described by the <paramref name="destBackupFileName" /> parameter was not of a legal form.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="destFileName" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file described by the current <see cref="T:System.IO.FileInfo" /> object could not be found.
-or-
The file described by the <paramref name="destinationFileName" /> parameter could not be found.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Microsoft Windows NT or later.</exception>
    </member>
    <member name="M:System.IO.FileInfo.Replace(System.String,System.String,System.Boolean)">
      <summary>Replaces the contents of a specified file with the file described by the current <see cref="T:System.IO.FileInfo" /> object, deleting the original file, and creating a backup of the replaced file.  Also specifies whether to ignore merge errors.</summary>
      <param name="destinationFileName">The name of a file to replace with the current file.</param>
      <param name="destinationBackupFileName">The name of a file with which to create a backup of the file described by the <paramref name="destFileName" /> parameter.</param>
      <param name="ignoreMetadataErrors">
        <see langword="true" /> to ignore merge errors (such as attributes and ACLs) from the replaced file to the replacement file; otherwise <see langword="false" />.</param>
      <returns>A <see cref="T:System.IO.FileInfo" /> object that encapsulates information about the file described by the <paramref name="destFileName" /> parameter.</returns>
      <exception cref="T:System.ArgumentException">The path described by the <paramref name="destFileName" /> parameter was not of a legal form.
-or-
The path described by the <paramref name="destBackupFileName" /> parameter was not of a legal form.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="destFileName" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file described by the current <see cref="T:System.IO.FileInfo" /> object could not be found.
-or-
The file described by the <paramref name="destinationFileName" /> parameter could not be found.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Microsoft Windows NT or later.</exception>
    </member>
    <member name="M:System.IO.FileInfo.ToString">
      <summary>Returns the path as a string. Use the <see cref="P:System.IO.FileInfo.Name" /> property for the full path.</summary>
      <returns>A string representing the path.</returns>
    </member>
    <member name="T:System.IO.FileSystemInfo">
      <summary>Provides the base class for both <see cref="T:System.IO.FileInfo" /> and <see cref="T:System.IO.DirectoryInfo" /> objects.</summary>
    </member>
    <member name="M:System.IO.FileSystemInfo.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.FileSystemInfo" /> class.</summary>
    </member>
    <member name="M:System.IO.FileSystemInfo.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.FileSystemInfo" /> class with serialized data.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">The specified <see cref="T:System.Runtime.Serialization.SerializationInfo" /> is null.</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.Attributes">
      <summary>Gets or sets the attributes for the current file or directory.</summary>
      <returns>
        <see cref="T:System.IO.FileAttributes" /> of the current <see cref="T:System.IO.FileSystemInfo" />.</returns>
      <exception cref="T:System.IO.FileNotFoundException">The specified file doesn't exist. Only thrown when setting the property value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid. For example, it's on an unmapped drive. Only thrown when setting the property value.</exception>
      <exception cref="T:System.Security.SecurityException">The caller doesn't have the required permission.</exception>
      <exception cref="T:System.ArgumentException">The caller attempts to set an invalid file attribute.
-or-
The user attempts to set an attribute value but doesn't have write permission.</exception>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot initialize the data.</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTime">
      <summary>Gets or sets the creation time of the current file or directory.</summary>
      <returns>The creation date and time of the current <see cref="T:System.IO.FileSystemInfo" /> object.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot initialize the data.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid; for example, it is on an unmapped drive.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The caller attempts to set an invalid creation time.</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTimeUtc">
      <summary>Gets or sets the creation time, in coordinated universal time (UTC), of the current file or directory.</summary>
      <returns>The creation date and time in UTC format of the current <see cref="T:System.IO.FileSystemInfo" /> object.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot initialize the data.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid; for example, it is on an unmapped drive.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The caller attempts to set an invalid access time.</exception>
    </member>
    <member name="M:System.IO.FileSystemInfo.Delete">
      <summary>Deletes a file or directory.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid; for example, it is on an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">There is an open handle on the file or directory, and the operating system is Windows XP or earlier. This open handle can result from enumerating directories and files. For more information, see How to: Enumerate Directories and Files.</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.Exists">
      <summary>Gets a value indicating whether the file or directory exists.</summary>
      <returns>
        <see langword="true" /> if the file or directory exists; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.IO.FileSystemInfo.Extension">
      <summary>Gets the string representing the extension part of the file.</summary>
      <returns>A string containing the <see cref="T:System.IO.FileSystemInfo" /> extension.</returns>
    </member>
    <member name="P:System.IO.FileSystemInfo.FullName">
      <summary>Gets the full path of the directory or file.</summary>
      <returns>A string containing the full path.</returns>
      <exception cref="T:System.IO.PathTooLongException">The fully qualified path and file name exceed the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="F:System.IO.FileSystemInfo.FullPath">
      <summary>Represents the fully qualified path of the directory or file.</summary>
      <exception cref="T:System.IO.PathTooLongException">The fully qualified path exceeds the system-defined maximum length.</exception>
    </member>
    <member name="M:System.IO.FileSystemInfo.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with the file name and additional exception information.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTime">
      <summary>Gets or sets the time the current file or directory was last accessed.</summary>
      <returns>The time that the current file or directory was last accessed.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot initialize the data.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The caller attempts to set an invalid access time</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTimeUtc">
      <summary>Gets or sets the time, in coordinated universal time (UTC), that the current file or directory was last accessed.</summary>
      <returns>The UTC time that the current file or directory was last accessed.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot initialize the data.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The caller attempts to set an invalid access time.</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTime">
      <summary>Gets or sets the time when the current file or directory was last written to.</summary>
      <returns>The time the current file was last written.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot initialize the data.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The caller attempts to set an invalid write time.</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTimeUtc">
      <summary>Gets or sets the time, in coordinated universal time (UTC), when the current file or directory was last written to.</summary>
      <returns>The UTC time when the current file was last written to.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot initialize the data.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The caller attempts to set an invalid write time.</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.Name">
      <summary>For files, gets the name of the file. For directories, gets the name of the last directory in the hierarchy if a hierarchy exists. Otherwise, the <see langword="Name" /> property gets the name of the directory.</summary>
      <returns>A string that is the name of the parent directory, the name of the last directory in the hierarchy, or the name of a file, including the file name extension.</returns>
    </member>
    <member name="F:System.IO.FileSystemInfo.OriginalPath">
      <summary>The path originally specified by the user, whether relative or absolute.</summary>
    </member>
    <member name="M:System.IO.FileSystemInfo.Refresh">
      <summary>Refreshes the state of the object.</summary>
      <exception cref="T:System.IO.IOException">A device such as a disk drive is not ready.</exception>
    </member>
    <member name="M:System.IO.FileSystemInfo.ToString">
      <summary>Returns the original path. Use the <see cref="P:System.IO.FileSystemInfo.FullName" /> or <see cref="P:System.IO.FileSystemInfo.Name" /> properties for the full path or file/directory name.</summary>
      <returns>A string with the original path.</returns>
    </member>
    <member name="T:System.IO.MatchCasing" />
    <member name="F:System.IO.MatchCasing.CaseInsensitive">
      <summary>Matches ignoring character casing.</summary>
    </member>
    <member name="F:System.IO.MatchCasing.CaseSensitive">
      <summary>Matches respecting character casing.</summary>
    </member>
    <member name="F:System.IO.MatchCasing.PlatformDefault">
      <summary>Matches using the default casing for the given platform.</summary>
    </member>
    <member name="T:System.IO.MatchType" />
    <member name="F:System.IO.MatchType.Simple">
      <summary>Matches using '*' and '?' wildcards.</summary>
    </member>
    <member name="F:System.IO.MatchType.Win32">
      <summary>Match using Win32 DOS style matching semantics. '*', '?', '&lt;', '&gt;', and '"' are all considered wildcards.</summary>
    </member>
    <member name="T:System.IO.SearchOption">
      <summary>Specifies whether to search the current directory, or the current directory and all subdirectories.</summary>
    </member>
    <member name="F:System.IO.SearchOption.AllDirectories">
      <summary>Includes the current directory and all its subdirectories in a search operation. This option includes reparse points such as mounted drives and symbolic links in the search.</summary>
    </member>
    <member name="F:System.IO.SearchOption.TopDirectoryOnly">
      <summary>Includes only the current directory in a search operation.</summary>
    </member>
  </members>
</doc>