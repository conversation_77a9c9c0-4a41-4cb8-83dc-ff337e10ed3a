﻿using Coldairarrow.Entity.PunchCard_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System.Linq.Expressions;
using System;
using Coldairarrow.Entity.HR_EmployeeInfoManage;

namespace Coldairarrow.Business.PunchCard_Manage
{
    public class PunchCard_Flow_InfoBusiness : BaseBusiness<PunchCard_Flow_Info>, IPunchCard_Flow_InfoBusiness, ITransientDependency
    {
        public PunchCard_Flow_InfoBusiness(IDbAccessor db, IPunchCard_Flow_ListBusiness punchCard_Flow_ListBus, IPunchCard_InfoBusiness punchCard_InfoBus)
            : base(db)
        {
            _punchCard_Flow_ListBus = punchCard_Flow_ListBus;
            _punchCard_InfoBus = punchCard_InfoBus;
        }
        IPunchCard_Flow_ListBusiness _punchCard_Flow_ListBus { get; }
        IPunchCard_InfoBusiness _punchCard_InfoBus { get; }
        #region 外部接口

        public async Task<PageResult<PunchCard_Flow_InfoDTO>> GetDataListAsync(PageInput<PunchCardModel> input)
        {
            //Expression<Func<PunchCard_Flow_Info, HR_FormalEmployees, HR_FormalEmployees, PunchCard_Flow_InfoDTO>> select = (a, e, f) => new PunchCard_Flow_InfoDTO
            //{
            //    UserName = e.NameUser,
            //    flowapproverUserName = e.NameUser
            //};
            //select = select.BuildExtendSelectExpre();
            //var q = from a in GetIQueryable().AsExpandable()
            //        join e in this.Db.GetIQueryable<HR_FormalEmployees>() on a.F_initiator equals e.F_Id
            //        join f in this.Db.GetIQueryable<HR_FormalEmployees>() on a.F_FlowApprover equals f.F_Id
            //        //where e.F_Id == userId
            //        select @select.Invoke(a, e, f);
            var search = input.Search;
            var sqlStr = @" SELECT P.*,F.NameUser AS UserName,F2.NameUser AS approverUserName,P2.F_Desc userDesc,F3.NameUser as flowapproverUserName  FROM [dbo].[PunchCard_Flow_Info] P
LEFT JOIN[dbo].[HR_FormalEmployees] F ON P.F_initiator = F.F_Id
LEFT JOIN[dbo].[HR_FormalEmployees] F2 ON P.F_approver = F2.F_Id 
LEFT JOIN[dbo].[HR_FormalEmployees] F3 ON P.F_FlowApprover = F3.F_Id 
LEFT JOIN[dbo].[PunchCard_Info] P2 ON P.F_WFId = P2.F_WFId 
where 1=1 ";
            if (!string.IsNullOrEmpty(search.userName))
            {
                sqlStr += " and F.NameUser like '%" + search.userName + "%' ";
            }
            var pList = await this.Db.GetListBySqlAsync<PunchCard_Flow_InfoDTO>(sqlStr);

            var where = LinqHelper.True<PunchCard_Flow_InfoDTO>();

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<PunchCard_Flow_InfoDTO, bool>(
                   ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            if (search.F_WFState.HasValue)
            {
                //不等于抄送
                if (search.F_WFState == 1)
                {
                    var newWhere = DynamicExpressionParser.ParseLambda<PunchCard_Flow_InfoDTO, bool>(
                  ParsingConfig.Default, false, $@"F_WFState = @0 and F_FlowType<>2", 1);
                    where = where.And(newWhere);
                }
                else if (search.F_WFState == 2)
                {
                    var newWhere = DynamicExpressionParser.ParseLambda<PunchCard_Flow_InfoDTO, bool>(
                ParsingConfig.Default, false, $@"F_WFState <> @0 and F_FlowType<>2", 1);
                    where = where.And(newWhere);
                }
                else
                {
                    var newWhere = DynamicExpressionParser.ParseLambda<PunchCard_Flow_InfoDTO, bool>(
                 ParsingConfig.Default, false, $@"F_FlowType = @0", 2);
                    where = where.And(newWhere);
                }
            }
            if (!string.IsNullOrEmpty(search.F_approver))
            {
                var newWhere = DynamicExpressionParser.ParseLambda<PunchCard_Flow_InfoDTO, bool>(
           ParsingConfig.Default, false, $@"F_approver = @0", search.F_approver);
                where = where.And(newWhere);
            }
            if (!string.IsNullOrEmpty(search.F_initiator))
            {
                var newWhere = DynamicExpressionParser.ParseLambda<PunchCard_Flow_InfoDTO, bool>(
           ParsingConfig.Default, false, $@"F_initiator = @0", search.F_initiator);
                where = where.And(newWhere);
            }
            //where = where.AndIf(search.wfState.HasValue, x => x.F_WFState == search.wfState);

            return pList.Where(where.Compile()).GetPageResult(input);
            //var q = GetIQueryable();
            //var where = LinqHelper.True<PunchCard_Flow_Info>();
            //var search = input.Search;

            ////筛选
            //if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            //{
            //    var newWhere = DynamicExpressionParser.ParseLambda<PunchCard_Flow_Info, bool>(
            //        ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
            //    where = where.And(newWhere);
            //}

            //return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<PunchCard_Flow_Info> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        public async Task<PunchCard_Flow_InfoDTO> GetFlowTheDataAsync(PunchCardModel input)
        {
            PunchCard_Flow_InfoDTO flow_Info = new PunchCard_Flow_InfoDTO();
            var sqlStr = @" SELECT P.*,F.NameUser AS UserName,F2.NameUser AS approverUserName,P2.F_Desc userDesc FROM [dbo].[PunchCard_Flow_Info] P
LEFT JOIN[dbo].[HR_FormalEmployees] F ON P.F_initiator = F.F_Id
LEFT JOIN[dbo].[HR_FormalEmployees] F2 ON P.F_approver = F2.F_Id 
LEFT JOIN[dbo].[PunchCard_Info] P2 ON P.F_WFId = P2.F_WFId 
where 1=1  ";
            if (!input.id.IsNullOrEmpty())
            {
                sqlStr += "  and P.F_Id='" + input.id + "' ";
            }
            if (!input.F_WFId.IsNullOrEmpty())
            {
                sqlStr += "  and P.F_WFId = '" + input.F_WFId + "' ";
            }
            if (!input.F_approver.IsNullOrEmpty())
            {
                sqlStr += "  and P.F_approver = '" + input.F_approver + "' ";
            }
            var pList = await this.Db.GetListBySqlAsync<PunchCard_Flow_InfoDTO>(sqlStr);
            if (pList.Count > 0)
            {
                var subFlow = pList.FirstOrDefault(x => x.F_WFState.Value != WfType.审核中);
                if (subFlow != null)
                {
                    flow_Info = subFlow;
                }
                else
                {
                    flow_Info = pList.FirstOrDefault();
                }
            }
            return flow_Info;
        }
        public async Task<List<PunchCard_Flow_InfoDTO>> GetFlowTheDataByWfIdAsync(PunchCardModel input)
        {
            PunchCard_Flow_Info flow_Info = new PunchCard_Flow_Info();
            var sqlStr = @" SELECT P.*,F.NameUser AS UserName,F2.NameUser AS approverUserName,P2.F_Desc userDesc 
FROM [dbo].[PunchCard_Flow_Info] P
LEFT JOIN[dbo].[HR_FormalEmployees] F ON P.F_initiator = F.F_Id
LEFT JOIN[dbo].[HR_FormalEmployees] F2 ON P.F_approver = F2.F_Id 
LEFT JOIN[dbo].[PunchCard_Info] P2 ON P.F_WFId = P2.F_WFId 
where 1=1 ";
            if (!input.F_WFId.IsNullOrEmpty())
            {
                sqlStr += "  and P.F_WFId = '" + input.F_WFId + "' ";
            }
            if (!input.F_approver.IsNullOrEmpty())
            {
                sqlStr += "  and P.F_approver = '" + input.F_approver + "' ";
            }
            return await this.Db.GetListBySqlAsync<PunchCard_Flow_InfoDTO>(sqlStr);
        }
        /// <summary>
        /// 流程审核
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [Transactional]
        public async Task SaveFlowData(PunchCard_Flow_Info data)
        {
            //查询所有相关流程
            var flow_Infos = await GetIQueryable().Where(x => x.F_WFId == data.F_WFId && x.F_FlowType == FlowType.审核).ToListAsync();
            flow_Infos.ForEach(item =>
            {
                item.F_WFState = data.F_WFState;
                item.F_FlowApprover = data.F_approver;
                item.F_EndTime = DateTime.Now;
            });
            var flow_List = await _punchCard_Flow_ListBus.GetTheDataAsync(data.F_WFId);
            if (flow_List != null)
            {
                flow_List.F_WFState = data.F_WFState;
                flow_List.F_FlowApprover = data.F_approver;
                flow_List.F_EndTime = DateTime.Now;
            }
            var punchCard_Info = await _punchCard_InfoBus.GetTheDataByWfIdAsync(data.F_WFId);
            if (punchCard_Info != null)
            {
                punchCard_Info.F_WFState = data.F_WFState;
            }
            await UpdateAsync(flow_Infos);
            await _punchCard_Flow_ListBus.UpdateDataAsync(flow_List);
            await _punchCard_InfoBus.UpdateDataAsync(punchCard_Info);
        }
        public async Task AddDataAsync(PunchCard_Flow_Info data)
        {
            await InsertAsync(data);
        }
        public async Task AddDataAsync(List<PunchCard_Flow_Info> list)
        {
            await InsertAsync(list);
        }
        public async Task UpdateDataAsync(PunchCard_Flow_Info data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<PunchCard_Flow_Info>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<PunchCard_Flow_Info, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<PunchCard_Flow_Info, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}