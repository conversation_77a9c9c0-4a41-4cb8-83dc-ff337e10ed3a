﻿using Coldairarrow.Entity.HR_DataDictionaryManage;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using Coldairarrow.Util.UEditor;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HR_DataDictionaryManage
{
    public class HR_DataDictionaryDetailsBusiness : BaseBusiness<HR_DataDictionaryDetails>, IHR_DataDictionaryDetailsBusiness, ITransientDependency
    {
        public HR_DataDictionaryDetailsBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口
        /// <summary>
        /// 获取数据字典明显
        /// </summary>
        /// <param name="itemCode">分类编码</param>
        /// <returns></returns>
        public List<HR_DataDictionaryDetails> GetDetailList(string itemCode)
        {
            //缓存查询
            //List<DataItemDetailEntity> list = cache.Read<List<DataItemDetailEntity>>(cacheKeyDetail + itemCode, CacheId.dataItem);
            //if (list?.Count == 0 || list == null)
            //{
            var list = GetIQueryable().Where(x => x.F_ItemCode == itemCode).ToList();
            //    cache.Write<List<DataItemDetailEntity>>(cacheKeyDetail + itemCode, list, CacheId.dataItem);
            //}
            return list;
        }

        /// <summary>
        /// 获取数据字典明显
        /// </summary>
        /// <param name="itemCode">分类编码</param>
        /// <returns></returns>
        public List<HR_DataDictionaryDetails> PostDetailList(string itemCode)
        {
            //缓存查询
            //List<DataItemDetailEntity> list = cache.Read<List<DataItemDetailEntity>>(cacheKeyDetail + itemCode, CacheId.dataItem);
            //if (list?.Count == 0 || list == null)
            //{
            var list = GetIQueryable().Where(x => x.F_ItemCode == itemCode && x.F_EnabledMark == 1).OrderBy(i => i.F_SortCode).ToList();
            //    cache.Write<List<DataItemDetailEntity>>(cacheKeyDetail + itemCode, list, CacheId.dataItem);
            //}
            return list;
        }

        /// <summary>
        /// 获取数据字典明显
        /// </summary>
        /// <param name="itemCode">分类编码</param>
        /// <param name="keyword">关键词（名称/值）</param>
        /// <returns></returns>
        public List<HR_DataDictionaryDetails> GetDetailList(string itemCode, string keyword)
        {
            List<HR_DataDictionaryDetails> list = GetDetailList(itemCode).OrderBy(i => i.F_SortCode).ToList();
            if (!string.IsNullOrEmpty(keyword))
            {
                list = list.FindAll(t => t.F_ItemName.Contains(keyword) || t.F_ItemValue.Contains(keyword));
            }
            return list;
        }
        /// <summary>
        /// 项目值不能重复
        /// </summary>
        /// <param name="keyValue">主键</param>
        /// <param name="itemValue">项目值</param>
        /// <param name="itemCode">分类编码</param>
        /// <returns></returns>
        public bool ExistDetailItemValue(string keyValue, string itemValue, string itemCode)
        {
            bool res = false;
            List<HR_DataDictionaryDetails> list = GetDetailList(itemCode);

            if (string.IsNullOrEmpty(keyValue))
            {
                res = list.FindAll(t => t.F_ItemValue.Equals(itemValue)).Count <= 0;
            }
            else
            {
                res = list.FindAll(t => !!t.F_ItemValue.IsNullOrWhiteSpace()&& t.F_ItemValue.Equals(itemValue) && !t.F_Id.Equals(keyValue))?.Count <= 0;
            }
            return res;

        }
        /// <summary>
        /// 项目名不能重复
        /// </summary>
        /// <param name="keyValue">主键</param>
        /// <param name="itemName">项目名</param>
        /// <param name="itemCode">分类编码</param>
        /// <returns></returns>
        public bool ExistDetailItemName(string keyValue, string itemName, string itemCode)
        {
            bool res = false;
            List<HR_DataDictionaryDetails> list = GetDetailList(itemCode);

            if (string.IsNullOrEmpty(keyValue))
            {
                res = list.FindAll(t => t.F_ItemName.Equals(itemName)).Count <= 0;
            }
            else
            {
                res = list.FindAll(t => t.F_ItemName.Equals(itemName) && !t.F_Id.Equals(keyValue)).Count <= 0;
            }
            return res;

        }
        /// <summary>
        /// 根据字典ID查询明细
        /// </summary>
        /// <param name="dicId">字典ID</param>
        /// <returns>明细列表</returns>
        public List<HR_DataDictionaryDetails> GetListByDicId(string dicId)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_DataDictionaryDetails>().And(x => x.F_ParentId == dicId);


            return q.Where(where).ToList();
        }
        /// <summary>
        /// 根据字典值查询明细
        /// </summary>
        /// <param name="dicId">字典ID</param>
        /// <returns>明细列表</returns>
        public HR_DataDictionaryDetails GetDataByValue(string value,string classId)
        {
            var q = GetIQueryable();
            var data = q.Where(x => x.F_ItemId == classId && x.F_ItemValue == value && x.F_EnabledMark == 1).FirstOrDefault();
            return data;
        }
        public async Task<PageResult<HR_DataDictionaryDetails>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_DataDictionaryDetails>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_DataDictionaryDetails, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_DataDictionaryDetails> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_DataDictionaryDetails data)
        {
            try
            {
                await InsertAsync(data);
            }
            catch (Exception ex)
            {
                throw new System.Exception(ex.ToString());
            }
        }

        public async Task UpdateDataAsync(HR_DataDictionaryDetails data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<DataDictionaryDetailsOut>> GetDataClassListAsync()
        {
            var classList = await Db.GetIQueryable<HR_DataDictionaryClass>().ToListAsync();
            var detailsList = await GetIQueryable().ToListAsync();
            List<DataDictionaryDetailsOut> list = new List<DataDictionaryDetailsOut>();
            foreach (var item in detailsList)
            {
                var classModel = classList.FirstOrDefault(i => i.F_Id == item.F_ItemId);
                DataDictionaryDetailsOut detailsOut = new DataDictionaryDetailsOut()
                {
                    F_Id = item.F_Id,
                    F_ItemId = item.F_ItemId,
                    F_ItemCode = item.F_ItemCode,
                    F_ItemName = item.F_ItemName,
                    F_ItemValue = item.F_ItemValue,
                    F_SortCode = item.F_SortCode,
                    F_SimpleSpelling = item.F_SimpleSpelling,
                    F_QuickQuery = item.F_QuickQuery,
                    ClassCode = classModel.F_ItemCode,
                    ClassName = classModel.F_ItemName,
                };
                list.Add(detailsOut);
            }
            return list;
        }
        public List<OAData> GetOANDData(string str)
        {
            List<OAData> oADatas = new List<OAData>();
            string data = HttpHelper.PostData(str, null, null, ContentType.Json);
            var retObj = JsonConvert.DeserializeObject<httpdata>(data);
            if (retObj != null)
            {
                if (retObj.errCode == "0000")
                {
                    if (retObj.data.Count > 0)
                    {
                        foreach (var item in retObj.data)
                        {
                            OAData oAData = new OAData();
                            oAData.project_id = item.project_id;
                            oAData.project_name = item.project_name;
                            oADatas.Add(oAData);
                        }
                    }
                }
            }
            return oADatas;
        }

        #endregion

        #region 私有成员
        public class httpdata
        {
            public string errCode { get; set; }
            public string errDesc { get; set; }
            public List<lbgsData> data { get; set; }
        }
        public class lbgsData
        {
            public string project_id { get; set; }
            public string project_name { get; set; }
            public string project_company { get; set; }
        }
        #endregion
    }
}