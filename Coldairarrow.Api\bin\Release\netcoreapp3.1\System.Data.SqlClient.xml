﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Data.SqlClient</name>
  </assembly>
  <members>
    <member name="T:Microsoft.SqlServer.Server.DataAccessKind">
      <summary>Describes the type of access to user data for a user-defined method or function.</summary>
    </member>
    <member name="F:Microsoft.SqlServer.Server.DataAccessKind.None">
      <summary>The method or function does not access user data.</summary>
    </member>
    <member name="F:Microsoft.SqlServer.Server.DataAccessKind.Read">
      <summary>The method or function reads user data.</summary>
    </member>
    <member name="T:Microsoft.SqlServer.Server.Format">
      <summary>Used by <see cref="T:Microsoft.SqlServer.Server.SqlUserDefinedTypeAttribute" /> and <see cref="T:Microsoft.SqlServer.Server.SqlUserDefinedAggregateAttribute" /> to indicate the serialization format of a user-defined type (UDT) or aggregate.</summary>
    </member>
    <member name="F:Microsoft.SqlServer.Server.Format.Native">
      <summary>This serialization format uses a very simple algorithm that enables SQL Server to store an efficient representation of the UDT on disk. Types marked for <see langword="Native" /> serialization can only have value types (structs in Microsoft Visual C# and structures in Microsoft Visual Basic .NET) as members. Members of reference types (such as classes in Visual C# and Visual Basic), either user-defined or those existing in .NET class libraries (such as <see cref="T:System.String" />), are not supported.</summary>
    </member>
    <member name="F:Microsoft.SqlServer.Server.Format.Unknown">
      <summary>The serialization format is unknown.</summary>
    </member>
    <member name="F:Microsoft.SqlServer.Server.Format.UserDefined">
      <summary>This serialization format gives the developer full control over the binary format through the <see cref="M:Microsoft.SqlServer.Server.IBinarySerialize.Write(System.IO.BinaryWriter)" /> and <see cref="M:Microsoft.SqlServer.Server.IBinarySerialize.Read(System.IO.BinaryReader)" /> methods.</summary>
    </member>
    <member name="T:Microsoft.SqlServer.Server.IBinarySerialize">
      <summary>Provides custom implementation for user-defined type (UDT) and user-defined aggregate serialization and deserialization.</summary>
    </member>
    <member name="M:Microsoft.SqlServer.Server.IBinarySerialize.Read(System.IO.BinaryReader)">
      <summary>Generates a user-defined type (UDT) or user-defined aggregate from its binary form.</summary>
      <param name="r">The <see cref="T:System.IO.BinaryReader" /> stream from which the object is deserialized.</param>
    </member>
    <member name="M:Microsoft.SqlServer.Server.IBinarySerialize.Write(System.IO.BinaryWriter)">
      <summary>Converts a user-defined type (UDT) or user-defined aggregate into its binary format so that it may be persisted.</summary>
      <param name="w">The <see cref="T:System.IO.BinaryWriter" /> stream to which the UDT or user-defined aggregate is serialized.</param>
    </member>
    <member name="T:Microsoft.SqlServer.Server.InvalidUdtException">
      <summary>Thrown when SQL Server or the ADO.NET <see cref="N:System.Data.SqlClient" /> provider detects an invalid user-defined type (UDT).</summary>
    </member>
    <member name="T:Microsoft.SqlServer.Server.SqlDataRecord">
      <summary>Represents a single row of data and its metadata. This class cannot be inherited.</summary>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.#ctor(Microsoft.SqlServer.Server.SqlMetaData[])">
      <summary>Inititializes a new <see cref="T:Microsoft.SqlServer.Server.SqlDataRecord" /> instance with the schema based on the array of <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> objects passed as an argument.</summary>
      <param name="metaData">An array of <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> objects that describe each column in the <see cref="T:Microsoft.SqlServer.Server.SqlDataRecord" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="metaData" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount">
      <summary>Gets the number of columns in the data row. This property is read-only.</summary>
      <returns>The number of columns in the data row as an integer.</returns>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetBoolean(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Boolean" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Boolean" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The column specified by <paramref name="ordinal" /> is null.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetByte(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Byte" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Byte" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The column specified by <paramref name="ordinal" /> is null.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as an array of <see cref="T:System.Byte" /> objects.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="fieldOffset">The offset into the field value to start retrieving bytes.</param>
      <param name="buffer">The target buffer to which to copy bytes.</param>
      <param name="bufferOffset">The offset into the buffer to which to start copying bytes.</param>
      <param name="length">The number of bytes to copy to the buffer.</param>
      <returns>The number of bytes copied.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The column specified by <paramref name="ordinal" /> is null.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetChar(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Char" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Char" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The column specified by <paramref name="ordinal" /> is null.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as an array of <see cref="T:System.Char" /> objects.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="fieldOffset">The offset into the field value to start retrieving characters.</param>
      <param name="buffer">The target buffer to copy chars to.</param>
      <param name="bufferOffset">The offset into the buffer to start copying chars to.</param>
      <param name="length">The number of chars to copy to the buffer.</param>
      <returns>The number of characters copied.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The column specified by <paramref name="ordinal" /> is null.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetDataTypeName(System.Int32)">
      <summary>Returns the name of the data type for the column specified by the ordinal argument.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>A <see cref="T:System.String" /> that contains the data type of the column.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetDateTime(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.DateTime" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.DateTime" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The column specified by <paramref name="ordinal" /> is null.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetDateTimeOffset(System.Int32)">
      <summary>Returns the specified column's data as a <see cref="T:System.DateTimeOffset" />.</summary>
      <param name="ordinal">The zero-based column ordinal.</param>
      <returns>The value of the specified column as a <see cref="T:System.DateTimeOffset" />.</returns>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetDecimal(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Decimal" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Decimal" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The column specified by <paramref name="ordinal" /> is null.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetDouble(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Double" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Double" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The column specified by <paramref name="ordinal" /> is null.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetFieldType(System.Int32)">
      <summary>Returns a <see cref="T:System.Type" /> object representing the common language runtime (CLR) type that maps to the SQL Server type of the column specified by the <paramref name="ordinal" /> argument.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column type as a <see cref="T:System.Type" /> object.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.TypeLoadException">The column is of a user-defined type that is not available to the calling application domain.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The column is of a user-defined type that is not available to the calling application domain.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetFloat(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see langword="float" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see langword="float" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The column specified by <paramref name="ordinal" /> is null.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetGuid(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Guid" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Guid" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The column specified by <paramref name="ordinal" /> is null.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetInt16(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Int16" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Int16" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The column specified by <paramref name="ordinal" /> is null.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetInt32(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Int32" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Int32" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The column specified by <paramref name="ordinal" /> is null.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetInt64(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Int64" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Int64" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The column specified by <paramref name="ordinal" /> is null.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetName(System.Int32)">
      <summary>Returns the name of the column specified by the ordinal argument.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>A <see cref="T:System.String" /> containing the column name.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetOrdinal(System.String)">
      <summary>Returns the column ordinal specified by the column name.</summary>
      <param name="name">The name of the column to look up.</param>
      <returns>The zero-based ordinal of the column as an integer.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IndexOutOfRangeException">The column name could not be found.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlBinary(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlBinary" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlBinary" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlBoolean(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlBoolean" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlBoolean" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlByte(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlByte" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlByte" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlBytes(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlBytes" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlBytes" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlChars(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlChars" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlChars" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlDateTime(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlDateTime" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlDateTime" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlDecimal(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlDecimal" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlDecimal" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlDouble(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlDouble" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlDouble" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlFieldType(System.Int32)">
      <summary>Returns a <see cref="T:System.Type" /> object that represents the type (as a SQL Server type, defined in <see cref="N:System.Data.SqlTypes" />) that maps to the SQL Server type of the column.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column type as a <see cref="T:System.Type" /> object.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.TypeLoadException">The column is of a user-defined type that is not available to the calling application domain.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The column is of a user-defined type that is not available to the calling application domain.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlGuid(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlGuid" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlGuid" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlInt16(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlInt16" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlInt16" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlInt32(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlInt32" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlInt32" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlInt64(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlInt64" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlInt64" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlMetaData(System.Int32)">
      <summary>Returns a <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> object, describing the metadata of the column specified by the column ordinal.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column metadata as a <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> object.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlMoney(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlMoney" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlMoney" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlSingle(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlSingle" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlSingle" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlString(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlString" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlString" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlValue(System.Int32)">
      <summary>Returns the data value stored in the column, expressed as a SQL Server type, specified by the column ordinal.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The value of the column, expressed as a SQL Server type, as a <see cref="T:System.Object" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlValues(System.Object[])">
      <summary>Returns the values for all the columns in the record, expressed as SQL Server types, in an array.</summary>
      <param name="values">The array into which to copy the values column values.</param>
      <returns>An <see cref="T:System.Int32" /> that indicates the number of columns copied.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetSqlXml(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.Data.SqlTypes.SqlXml" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.Data.SqlTypes.SqlXml" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetString(System.Int32)">
      <summary>Gets the value for the column specified by the ordinal as a <see cref="T:System.String" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The column value as a <see cref="T:System.String" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetTimeSpan(System.Int32)">
      <summary>Returns the specified column's data as a <see cref="T:System.TimeSpan" />.</summary>
      <param name="ordinal">The zero-based column ordinal.</param>
      <returns>The value of the specified column as a <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetValue(System.Int32)">
      <summary>Returns the common language runtime (CLR) type value for the column specified by the ordinal argument.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The CLR type value of the column specified by the ordinal.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.GetValues(System.Object[])">
      <summary>Returns the values for all the columns in the record, expressed as common language runtime (CLR) types, in an array.</summary>
      <param name="values">The array into which to copy the values column values.</param>
      <returns>An <see cref="T:System.Int32" /> that indicates the number of columns copied.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidCastException">There is a type mismatch.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.IsDBNull(System.Int32)">
      <summary>Returns true if the column specified by the column ordinal parameter is null.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>
        <see langword="true" /> if the column is null; <see langword="false" /> otherwise.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlDataRecord.Item(System.Int32)">
      <summary>Gets the common language runtime (CLR) type value for the column specified by the column <paramref name="ordinal" /> argument.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>The CLR type value of the column specified by the <paramref name="ordinal" />.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlDataRecord.Item(System.String)">
      <summary>Gets the common language runtime (CLR) type value for the column specified by the column <paramref name="name" /> argument.</summary>
      <param name="name">The name of the column.</param>
      <returns>The CLR type value of the column specified by the <paramref name="name" />.</returns>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetBoolean(System.Int32,System.Boolean)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Boolean" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetByte(System.Int32,System.Byte)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Byte" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
      <summary>Sets the data stored in the column to the specified array of <see cref="T:System.Byte" /> values.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="fieldOffset">The offset into the field value to start copying bytes.</param>
      <param name="buffer">The target buffer from which to copy bytes.</param>
      <param name="bufferOffset">The offset into the buffer from which to start copying bytes.</param>
      <param name="length">The number of bytes to copy from the buffer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetChar(System.Int32,System.Char)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Char" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
      <summary>Sets the data stored in the column to the specified array of <see cref="T:System.Char" /> values.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="fieldOffset">The offset into the field value to start copying characters.</param>
      <param name="buffer">The target buffer from which to copy chars.</param>
      <param name="bufferOffset">The offset into the buffer from which to start copying chars.</param>
      <param name="length">The number of chars to copy from the buffer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetDateTime(System.Int32,System.DateTime)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.DateTime" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetDateTimeOffset(System.Int32,System.DateTimeOffset)">
      <summary>Sets the value of the column specified to the <see cref="T:System.DateTimeOffset" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetDBNull(System.Int32)">
      <summary>Sets the value in the specified column to <see cref="T:System.DBNull" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetDecimal(System.Int32,System.Decimal)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Decimal" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetDouble(System.Int32,System.Double)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Double" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetFloat(System.Int32,System.Single)">
      <summary>Sets the data stored in the column to the specified <see langword="float" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetGuid(System.Int32,System.Guid)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Guid" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetInt16(System.Int32,System.Int16)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Int16" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetInt32(System.Int32,System.Int32)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Int32" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetInt64(System.Int32,System.Int64)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Int64" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlBinary(System.Int32,System.Data.SqlTypes.SqlBinary)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlBinary" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlBoolean(System.Int32,System.Data.SqlTypes.SqlBoolean)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlBoolean" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlByte(System.Int32,System.Data.SqlTypes.SqlByte)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlByte" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlBytes(System.Int32,System.Data.SqlTypes.SqlBytes)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlBytes" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlChars(System.Int32,System.Data.SqlTypes.SqlChars)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlChars" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlDateTime(System.Int32,System.Data.SqlTypes.SqlDateTime)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlDateTime" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlDecimal(System.Int32,System.Data.SqlTypes.SqlDecimal)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlDecimal" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlDouble(System.Int32,System.Data.SqlTypes.SqlDouble)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlDouble" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlGuid(System.Int32,System.Data.SqlTypes.SqlGuid)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlGuid" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlInt16(System.Int32,System.Data.SqlTypes.SqlInt16)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlInt16" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlInt32(System.Int32,System.Data.SqlTypes.SqlInt32)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlInt32" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlInt64(System.Int32,System.Data.SqlTypes.SqlInt64)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlInt64" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlMoney(System.Int32,System.Data.SqlTypes.SqlMoney)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlMoney" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlSingle(System.Int32,System.Data.SqlTypes.SqlSingle)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlSingle" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlString(System.Int32,System.Data.SqlTypes.SqlString)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlString" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetSqlXml(System.Int32,System.Data.SqlTypes.SqlXml)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.Data.SqlTypes.SqlXml" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetString(System.Int32,System.String)">
      <summary>Sets the data stored in the column to the specified <see cref="T:System.String" /> value.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetTimeSpan(System.Int32,System.TimeSpan)">
      <summary>Sets the value of the column specified to the <see cref="T:System.TimeSpan" />.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value of the column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> passed in is a negative number.</exception>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.TimeSpan" /> value passed in is greater than 24 hours in length.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetValue(System.Int32,System.Object)">
      <summary>Sets a new value, expressed as a common language runtime (CLR) type, for the column specified by the column ordinal.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <param name="value">The new value for the specified column.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.SetValues(System.Object[])">
      <summary>Sets new values for all of the columns in the <see cref="T:Microsoft.SqlServer.Server.SqlDataRecord" />. These values are expressed as common language runtime (CLR) types.</summary>
      <param name="values">The array of new values, expressed as CLR types boxed as <see cref="T:System.Object" /> references, for the <see cref="T:Microsoft.SqlServer.Server.SqlDataRecord" /> instance.</param>
      <returns>The number of column values set as an integer.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The size of values does not match the number of columns in the <see cref="T:Microsoft.SqlServer.Server.SqlDataRecord" /> instance.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlDataRecord.System#Data#IDataRecord#GetData(System.Int32)">
      <summary>Not supported in this release.</summary>
      <param name="ordinal">The zero-based ordinal of the column.</param>
      <returns>
        <see cref="T:System.Data.IDataReader" />
Always throws an exception.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="ordinal" /> is less than 0 or greater than the number of columns (that is, <see cref="P:Microsoft.SqlServer.Server.SqlDataRecord.FieldCount" />).</exception>
    </member>
    <member name="T:Microsoft.SqlServer.Server.SqlFacetAttribute">
      <summary>Annotates the returned result of a user-defined type (UDT) with additional information that can be used in Transact-SQL.</summary>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlFacetAttribute.#ctor">
      <summary>An optional attribute on a user-defined type (UDT) return type, used to annotate the returned result with additional information that can be used in Transact-SQL.</summary>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlFacetAttribute.IsFixedLength">
      <summary>Indicates whether the return type of the user-defined type is of a fixed length.</summary>
      <returns>
        <see langword="true" /> if the return type is of a fixed length; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlFacetAttribute.IsNullable">
      <summary>Indicates whether the return type of the user-defined type can be <see langword="null" />.</summary>
      <returns>
        <see langword="true" /> if the return type of the user-defined type can be <see langword="null" />; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlFacetAttribute.MaxSize">
      <summary>The maximum size, in logical units, of the underlying field type of the user-defined type.</summary>
      <returns>An <see cref="T:System.Int32" /> representing the maximum size, in logical units, of the underlying field type.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlFacetAttribute.Precision">
      <summary>The precision of the return type of the user-defined type.</summary>
      <returns>An <see cref="T:System.Int32" /> representing the precision of the return type.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlFacetAttribute.Scale">
      <summary>The scale of the return type of the user-defined type.</summary>
      <returns>An <see cref="T:System.Int32" /> representing the scale of the return type.</returns>
    </member>
    <member name="T:Microsoft.SqlServer.Server.SqlFunctionAttribute">
      <summary>Used to mark a method definition of a user-defined aggregate as a function in SQL Server. The properties on the attribute reflect the physical characteristics used when the type is registered with SQL Server.</summary>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlFunctionAttribute.#ctor">
      <summary>An optional attribute on a user-defined aggregate, used to indicate that the method should be registered in SQL Server as a function. Also used to set the <see cref="P:Microsoft.SqlServer.Server.SqlFunctionAttribute.DataAccess" />, <see cref="P:Microsoft.SqlServer.Server.SqlFunctionAttribute.FillRowMethodName" />, <see cref="P:Microsoft.SqlServer.Server.SqlFunctionAttribute.IsDeterministic" />, <see cref="P:Microsoft.SqlServer.Server.SqlFunctionAttribute.IsPrecise" />, <see cref="P:Microsoft.SqlServer.Server.SqlFunctionAttribute.Name" />, <see cref="P:Microsoft.SqlServer.Server.SqlFunctionAttribute.SystemDataAccess" />, and <see cref="P:Microsoft.SqlServer.Server.SqlFunctionAttribute.TableDefinition" /> properties of the function attribute.</summary>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlFunctionAttribute.DataAccess">
      <summary>Indicates whether the function involves access to user data stored in the local instance of SQL Server.</summary>
      <returns>
        <see cref="T:Microsoft.SqlServer.Server.DataAccessKind" />.<see langword="None" />: Does not access data. <see cref="T:Microsoft.SqlServer.Server.DataAccessKind" />.<see langword="Read" />: Only reads user data.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlFunctionAttribute.FillRowMethodName">
      <summary>The name of a method in the same class which is used to fill a row of data in the table returned by the table-valued function.</summary>
      <returns>A <see cref="T:System.String" /> value representing the name of a method in the same class which is used to fill a row of data in the table returned by the table-valued function.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlFunctionAttribute.IsDeterministic">
      <summary>Indicates whether the user-defined function is deterministic.</summary>
      <returns>
        <see langword="true" /> if the function is deterministic; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlFunctionAttribute.IsPrecise">
      <summary>Indicates whether the function involves imprecise computations, such as floating point operations.</summary>
      <returns>
        <see langword="true" /> if the function involves precise computations; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlFunctionAttribute.Name">
      <summary>The name under which the function should be registered in SQL Server.</summary>
      <returns>A <see cref="T:System.String" /> value representing the name under which the function should be registered.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlFunctionAttribute.SystemDataAccess">
      <summary>Indicates whether the function requires access to data stored in the system catalogs or virtual system tables of SQL Server.</summary>
      <returns>
        <see cref="T:Microsoft.SqlServer.Server.DataAccessKind" />.<see langword="None" />: Does not access system data. <see cref="T:Microsoft.SqlServer.Server.DataAccessKind" />.<see langword="Read" />: Only reads system data.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlFunctionAttribute.TableDefinition">
      <summary>A string that represents the table definition of the results, if the method is used as a table-valued function (TVF).</summary>
      <returns>A <see cref="T:System.String" /> value representing the table definition of the results.</returns>
    </member>
    <member name="T:Microsoft.SqlServer.Server.SqlMetaData">
      <summary>Specifies and retrieves metadata information from parameters and columns of <see cref="T:Microsoft.SqlServer.Server.SqlDataRecord" /> objects. This class cannot be inherited.</summary>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name and type.</summary>
      <param name="name">The name of the column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="Name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">A <see langword="SqlDbType" /> that is not allowed was passed to the constructor as <paramref name="dbType" />.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType,System.Boolean,System.Boolean,System.Data.SqlClient.SortOrder,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name, and default server. This form of the constructor supports table-valued parameters by allowing you to specify if the column is unique in the table-valued parameter, the sort order for the column, and the ordinal of the sort column.</summary>
      <param name="name">The name of the column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <param name="useServerDefault">Specifies whether this column should use the default server value.</param>
      <param name="isUniqueKey">Specifies if the column in the table-valued parameter is unique.</param>
      <param name="columnSortOrder">Specifies the sort order for a column.</param>
      <param name="sortOrdinal">Specifies the ordinal of the sort column.</param>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType,System.Byte,System.Byte)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name, type, precision, and scale.</summary>
      <param name="name">The name of the parameter or column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <param name="precision">The precision of the parameter or column.</param>
      <param name="scale">The scale of the parameter or column.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="Name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">A <see langword="SqlDbType" /> that is not allowed was passed to the constructor as <paramref name="dbType" />, or <paramref name="scale" /> was greater than <paramref name="precision" />.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType,System.Byte,System.Byte,System.Boolean,System.Boolean,System.Data.SqlClient.SortOrder,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name, type, precision, scale, and server default. This form of the constructor supports table-valued parameters by allowing you to specify if the column is unique in the table-valued parameter, the sort order for the column, and the ordinal of the sort column.</summary>
      <param name="name">The name of the column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <param name="precision">The precision of the parameter or column.</param>
      <param name="scale">The scale of the parameter or column.</param>
      <param name="useServerDefault">Specifies whether this column should use the default server value.</param>
      <param name="isUniqueKey">Specifies if the column in the table-valued parameter is unique.</param>
      <param name="columnSortOrder">Specifies the sort order for a column.</param>
      <param name="sortOrdinal">Specifies the ordinal of the sort column.</param>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType,System.Int64)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name, type, and maximum length.</summary>
      <param name="name">The name of the column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <param name="maxLength">The maximum length of the specified type.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="Name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">A SqlDbType that is not allowed was passed to the constructor as <paramref name="dbType" />.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType,System.Int64,System.Boolean,System.Boolean,System.Data.SqlClient.SortOrder,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name, type, maximum length, and server default. This form of the constructor supports table-valued parameters by allowing you to specify if the column is unique in the table-valued parameter, the sort order for the column, and the ordinal of the sort column.</summary>
      <param name="name">The name of the column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <param name="maxLength">The maximum length of the specified type.</param>
      <param name="useServerDefault">Specifies whether this column should use the default server value.</param>
      <param name="isUniqueKey">Specifies if the column in the table-valued parameter is unique.</param>
      <param name="columnSortOrder">Specifies the sort order for a column.</param>
      <param name="sortOrdinal">Specifies the ordinal of the sort column.</param>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType,System.Int64,System.Byte,System.Byte,System.Int64,System.Data.SqlTypes.SqlCompareOptions,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name, type, maximum length, precision, scale, locale ID, compare options, and user-defined type (UDT).</summary>
      <param name="name">The name of the column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <param name="maxLength">The maximum length of the specified type.</param>
      <param name="precision">The precision of the parameter or column.</param>
      <param name="scale">The scale of the parameter or column.</param>
      <param name="locale">The locale ID of the parameter or column.</param>
      <param name="compareOptions">The comparison rules of the parameter or column.</param>
      <param name="userDefinedType">A <see cref="T:System.Type" /> instance that points to the UDT.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="Name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">A <see langword="SqlDbType" /> that is not allowed was passed to the constructor as <paramref name="dbType" />, or <paramref name="userDefinedType" /> points to a type that does not have <see cref="T:Microsoft.SqlServer.Server.SqlUserDefinedTypeAttribute" /> declared.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType,System.Int64,System.Byte,System.Byte,System.Int64,System.Data.SqlTypes.SqlCompareOptions,System.Type,System.Boolean,System.Boolean,System.Data.SqlClient.SortOrder,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name, type, maximum length, precision, scale, locale ID, compare options, and user-defined type (UDT). This form of the constructor supports table-valued parameters by allowing you to specify if the column is unique in the table-valued parameter, the sort order for the column, and the ordinal of the sort column.</summary>
      <param name="name">The name of the column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <param name="maxLength">The maximum length of the specified type.</param>
      <param name="precision">The precision of the parameter or column.</param>
      <param name="scale">The scale of the parameter or column.</param>
      <param name="localeId">The locale ID of the parameter or column.</param>
      <param name="compareOptions">The comparison rules of the parameter or column.</param>
      <param name="userDefinedType">A <see cref="T:System.Type" /> instance that points to the UDT.</param>
      <param name="useServerDefault">Specifies whether this column should use the default server value.</param>
      <param name="isUniqueKey">Specifies if the column in the table-valued parameter is unique.</param>
      <param name="columnSortOrder">Specifies the sort order for a column.</param>
      <param name="sortOrdinal">Specifies the ordinal of the sort column.</param>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType,System.Int64,System.Int64,System.Data.SqlTypes.SqlCompareOptions)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name, type, maximum length, locale, and compare options.</summary>
      <param name="name">The name of the parameter or column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <param name="maxLength">The maximum length of the specified type.</param>
      <param name="locale">The locale ID of the parameter or column.</param>
      <param name="compareOptions">The comparison rules of the parameter or column.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="Name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">A SqlDbType that is not allowed was passed to the constructor as <paramref name="dbType" />.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType,System.Int64,System.Int64,System.Data.SqlTypes.SqlCompareOptions,System.Boolean,System.Boolean,System.Data.SqlClient.SortOrder,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name, type, maximum length, locale, compare options, and server default. This form of the constructor supports table-valued parameters by allowing you to specify if the column is unique in the table-valued parameter, the sort order for the column, and the ordinal of the sort column.</summary>
      <param name="name">The name of the column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <param name="maxLength">The maximum length of the specified type.</param>
      <param name="locale">The locale ID of the parameter or column.</param>
      <param name="compareOptions">The comparison rules of the parameter or column.</param>
      <param name="useServerDefault">Specifies whether this column should use the default server value.</param>
      <param name="isUniqueKey">Specifies if the column in the table-valued parameter is unique.</param>
      <param name="columnSortOrder">Specifies the sort order for a column.</param>
      <param name="sortOrdinal">Specifies the ordinal of the sort column.</param>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name, type, database name, owning schema, and object name.</summary>
      <param name="name">The name of the column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <param name="database">The database name of the XML schema collection of a typed XML instance.</param>
      <param name="owningSchema">The relational schema name of the XML schema collection of a typed XML instance.</param>
      <param name="objectName">The name of the XML schema collection of a typed XML instance.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="Name" /> is <see langword="null" />, or <paramref name="objectName" /> is <see langword="null" /> when <paramref name="database" /> and <paramref name="owningSchema" /> are non-<see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">A SqlDbType that is not allowed was passed to the constructor as <paramref name="dbType" />.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType,System.String,System.String,System.String,System.Boolean,System.Boolean,System.Data.SqlClient.SortOrder,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name, database name, owning schema, object name, and default server. This form of the constructor supports table-valued parameters by allowing you to specify if the column is unique in the table-valued parameter, the sort order for the column, and the ordinal of the sort column.</summary>
      <param name="name">The name of the column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <param name="database">The database name of the XML schema collection of a typed XML instance.</param>
      <param name="owningSchema">The relational schema name of the XML schema collection of a typed XML instance.</param>
      <param name="objectName">The name of the XML schema collection of a typed XML instance.</param>
      <param name="useServerDefault">Specifies whether this column should use the default server value.</param>
      <param name="isUniqueKey">Specifies if the column in the table-valued parameter is unique.</param>
      <param name="columnSortOrder">Specifies the sort order for a column.</param>
      <param name="sortOrdinal">Specifies the ordinal of the sort column.</param>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name, type, and user-defined type (UDT).</summary>
      <param name="name">The name of the column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <param name="userDefinedType">A <see cref="T:System.Type" /> instance that points to the UDT.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="Name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">A SqlDbType that is not allowed was passed to the constructor as <paramref name="dbType" />, or <paramref name="userDefinedType" /> points to a type that does not have <see cref="T:Microsoft.SqlServer.Server.SqlUserDefinedTypeAttribute" /> declared.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType,System.Type,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name, user-defined type (UDT), and SQLServer type.</summary>
      <param name="name">The name of the column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <param name="userDefinedType">A <see cref="T:System.Type" /> instance that points to the UDT.</param>
      <param name="serverTypeName">The SQL Server type name for <paramref name="userDefinedType" />.</param>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.#ctor(System.String,System.Data.SqlDbType,System.Type,System.String,System.Boolean,System.Boolean,System.Data.SqlClient.SortOrder,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> class with the specified column name, type, user-defined type, SQL Server type, and server default. This form of the constructor supports table-valued parameters by allowing you to specify if the column is unique in the table-valued parameter, the sort order for the column, and the ordinal of the sort column.</summary>
      <param name="name">The name of the column.</param>
      <param name="dbType">The SQL Server type of the parameter or column.</param>
      <param name="userDefinedType">A <see cref="T:System.Type" /> instance that points to the UDT.</param>
      <param name="serverTypeName">The SQL Server type name for <paramref name="userDefinedType" />.</param>
      <param name="useServerDefault">Specifies whether this column should use the default server value.</param>
      <param name="isUniqueKey">Specifies if the column in the table-valued parameter is unique.</param>
      <param name="columnSortOrder">Specifies the sort order for a column.</param>
      <param name="sortOrdinal">Specifies the ordinal of the sort column.</param>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Boolean)">
      <summary>Validates the specified <see cref="T:System.Boolean" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Boolean" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Byte)">
      <summary>Validates the specified <see cref="T:System.Byte" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Byte" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Byte[])">
      <summary>Validates the specified array of <see cref="T:System.Byte" /> values against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as an array of <see cref="T:System.Byte" /> values.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Char)">
      <summary>Validates the specified <see cref="T:System.Char" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Char" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Char[])">
      <summary>Validates the specified array of <see cref="T:System.Char" /> values against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as an array <see cref="T:System.Char" /> values.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlBinary)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlBinary" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlBinary" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlBoolean)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlBoolean" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlBoolean" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlByte)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlByte" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlByte" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlBytes)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlBytes" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlBytes" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlChars)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlChars" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlChars" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlDateTime)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlDateTime" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlDateTime" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlDecimal)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlDecimal" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlDecimal" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlDouble)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlDouble" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlDouble" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlGuid)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlGuid" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlGuid" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlInt16)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlInt16" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlInt16" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlInt32)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlInt32" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlInt32" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlInt64)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlInt64" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlInt64" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlMoney)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlMoney" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlMoney" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlSingle)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlSingle" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlSingle" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlString)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlString" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlString" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Data.SqlTypes.SqlXml)">
      <summary>Validates the specified <see cref="T:System.Data.SqlTypes.SqlXml" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Data.SqlTypes.SqlXml" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.DateTime)">
      <summary>Validates the specified <see cref="T:System.DateTime" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.DateTime" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.DateTimeOffset)">
      <summary>Validates the specified <see cref="T:System.DateTimeOffset" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as an array of <see cref="T:System.DateTimeOffset" /> values.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Decimal)">
      <summary>Validates the specified <see cref="T:System.Decimal" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Decimal" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Double)">
      <summary>Validates the specified <see cref="T:System.Double" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Double" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Guid)">
      <summary>Validates the specified <see cref="T:System.Guid" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Guid" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Int16)">
      <summary>Validates the specified <see cref="T:System.Int16" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Int16" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Int32)">
      <summary>Validates the specified <see cref="T:System.Int32" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Int32" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Int64)">
      <summary>Validates the specified <see cref="T:System.Int64" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Int64" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Object)">
      <summary>Validates the specified <see cref="T:System.Object" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Object" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.Single)">
      <summary>Validates the specified <see cref="T:System.Single" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.Single" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.String)">
      <summary>Validates the specified <see cref="T:System.String" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as a <see cref="T:System.String" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.Adjust(System.TimeSpan)">
      <summary>Validates the specified <see cref="T:System.TimeSpan" /> value against the metadata, and adjusts the value if necessary.</summary>
      <param name="value">The value to validate against the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The adjusted value as an array of <see cref="T:System.TimeSpan" /> values.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="Value" /> does not match the <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> type, or <paramref name="value" /> could not be adjusted.</exception>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.CompareOptions">
      <summary>Gets the comparison rules used for the column or parameter.</summary>
      <returns>The comparison rules used for the column or parameter as a <see cref="T:System.Data.SqlTypes.SqlCompareOptions" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.DbType">
      <summary>Gets the data type of the column or parameter.</summary>
      <returns>The data type of the column or parameter as a <see cref="T:System.Data.DbType" />.</returns>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMetaData.InferFromValue(System.Object,System.String)">
      <summary>Infers the metadata from the specified object and returns it as a <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</summary>
      <param name="value">The object used from which the metadata is inferred.</param>
      <param name="name">The name assigned to the returned <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</param>
      <returns>The inferred metadata as a <see cref="T:Microsoft.SqlServer.Server.SqlMetaData" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.IsUniqueKey">
      <summary>Indicates if the column in the table-valued parameter is unique.</summary>
      <returns>A <see langword="Boolean" /> value.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.LocaleId">
      <summary>Gets the locale ID of the column or parameter.</summary>
      <returns>The locale ID of the column or parameter as a <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.Max">
      <summary>Gets the length of <see langword="text" />, <see langword="ntext" />, and <see langword="image" /> data types.</summary>
      <returns>The length of <see langword="text" />, <see langword="ntext" />, and <see langword="image" /> data types.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.MaxLength">
      <summary>Gets the maximum length of the column or parameter.</summary>
      <returns>The maximum length of the column or parameter as a <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.Name">
      <summary>Gets the name of the column or parameter.</summary>
      <returns>The name of the column or parameter as a <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <paramref name="Name" /> specified in the constructor is longer than 128 characters.</exception>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.Precision">
      <summary>Gets the precision of the column or parameter.</summary>
      <returns>The precision of the column or parameter as a <see cref="T:System.Byte" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.Scale">
      <summary>Gets the scale of the column or parameter.</summary>
      <returns>The scale of the column or parameter.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.SortOrder">
      <summary>Returns the sort order for a column.</summary>
      <returns>A <see cref="T:System.Data.SqlClient.SortOrder" /> object.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.SortOrdinal">
      <summary>Returns the ordinal of the sort column.</summary>
      <returns>The ordinal of the sort column.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.SqlDbType">
      <summary>Gets the data type of the column or parameter.</summary>
      <returns>The data type of the column or parameter as a <see cref="T:System.Data.DbType" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.Type">
      <summary>Gets the common language runtime (CLR) type of a user-defined type (UDT).</summary>
      <returns>The CLR type name of a user-defined type as a <see cref="T:System.Type" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.TypeName">
      <summary>Gets the three-part name of the user-defined type (UDT) or the SQL Server type represented by the instance.</summary>
      <returns>The name of the UDT or SQL Server type as a <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.UseServerDefault">
      <summary>Reports whether this column should use the default server value.</summary>
      <returns>A <see langword="Boolean" /> value.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.XmlSchemaCollectionDatabase">
      <summary>Gets the name of the database where the schema collection for this XML instance is located.</summary>
      <returns>The name of the database where the schema collection for this XML instance is located as a <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.XmlSchemaCollectionName">
      <summary>Gets the name of the schema collection for this XML instance.</summary>
      <returns>The name of the schema collection for this XML instance as a <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMetaData.XmlSchemaCollectionOwningSchema">
      <summary>Gets the owning relational schema where the schema collection for this XML instance is located.</summary>
      <returns>The owning relational schema where the schema collection for this XML instance is located as a <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.SqlServer.Server.SqlMethodAttribute">
      <summary>Indicates the determinism and data access properties of a method or property on a user-defined type (UDT). The properties on the attribute reflect the physical characteristics that are used when the type is registered with SQL Server.</summary>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlMethodAttribute.#ctor">
      <summary>An attribute on a user-defined type (UDT), used to indicate the determinism and data access properties of a method or a property on a UDT.</summary>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMethodAttribute.InvokeIfReceiverIsNull">
      <summary>Indicates whether SQL Server should invoke the method on null instances.</summary>
      <returns>
        <see langword="true" /> if SQL Server should invoke the method on null instances; otherwise, <see langword="false" />. If the method cannot be invoked (because of an attribute on the method), the SQL Server <see langword="DbNull" /> is returned.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMethodAttribute.IsMutator">
      <summary>Indicates whether a method on a user-defined type (UDT) is a mutator.</summary>
      <returns>
        <see langword="true" /> if the method is a mutator; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlMethodAttribute.OnNullCall">
      <summary>Indicates whether the method on a user-defined type (UDT) is called when <see langword="null" /> input arguments are specified in the method invocation.</summary>
      <returns>
        <see langword="true" /> if the method is called when <see langword="null" /> input arguments are specified in the method invocation; <see langword="false" /> if the method returns a <see langword="null" /> value when any of its input parameters are <see langword="null" />. If the method cannot be invoked (because of an attribute on the method), the SQL Server <see langword="DbNull" /> is returned.</returns>
    </member>
    <member name="T:Microsoft.SqlServer.Server.SqlUserDefinedAggregateAttribute">
      <summary>Indicates that the type should be registered as a user-defined aggregate. The properties on the attribute reflect the physical attributes used when the type is registered with SQL Server. This class cannot be inherited.</summary>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlUserDefinedAggregateAttribute.#ctor(Microsoft.SqlServer.Server.Format)">
      <summary>A required attribute on a user-defined aggregate, used to indicate that the given type is a user-defined aggregate and the storage format of the user-defined aggregate.</summary>
      <param name="format">One of the <see cref="T:Microsoft.SqlServer.Server.Format" /> values representing the serialization format of the aggregate.</param>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlUserDefinedAggregateAttribute.Format">
      <summary>The serialization format as a <see cref="T:Microsoft.SqlServer.Server.Format" />.</summary>
      <returns>A <see cref="T:Microsoft.SqlServer.Server.Format" /> representing the serialization format.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlUserDefinedAggregateAttribute.IsInvariantToDuplicates">
      <summary>Indicates whether the aggregate is invariant to duplicates.</summary>
      <returns>
        <see langword="true" /> if the aggregate is invariant to duplicates; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlUserDefinedAggregateAttribute.IsInvariantToNulls">
      <summary>Indicates whether the aggregate is invariant to nulls.</summary>
      <returns>
        <see langword="true" /> if the aggregate is invariant to nulls; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlUserDefinedAggregateAttribute.IsInvariantToOrder">
      <summary>Indicates whether the aggregate is invariant to order.</summary>
      <returns>
        <see langword="true" /> if the aggregate is invariant to order; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlUserDefinedAggregateAttribute.IsNullIfEmpty">
      <summary>Indicates whether the aggregate returns <see langword="null" /> if no values have been accumulated.</summary>
      <returns>
        <see langword="true" /> if the aggregate returns <see langword="null" /> if no values have been accumulated; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlUserDefinedAggregateAttribute.MaxByteSize">
      <summary>The maximum size, in bytes, of the aggregate instance.</summary>
      <returns>An <see cref="T:System.Int32" /> value representing the maximum size of the aggregate instance.</returns>
    </member>
    <member name="F:Microsoft.SqlServer.Server.SqlUserDefinedAggregateAttribute.MaxByteSizeValue">
      <summary>The maximum size, in bytes, required to store the state of this aggregate instance during computation.</summary>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlUserDefinedAggregateAttribute.Name">
      <summary>The name of the aggregate.</summary>
      <returns>A <see cref="T:System.String" /> value representing the name of the aggregate.</returns>
    </member>
    <member name="T:Microsoft.SqlServer.Server.SqlUserDefinedTypeAttribute">
      <summary>Used to mark a type definition in an assembly as a user-defined type (UDT) in SQL Server. The properties on the attribute reflect the physical characteristics used when the type is registered with SQL Server. This class cannot be inherited.</summary>
    </member>
    <member name="M:Microsoft.SqlServer.Server.SqlUserDefinedTypeAttribute.#ctor(Microsoft.SqlServer.Server.Format)">
      <summary>A required attribute on a user-defined type (UDT), used to confirm that the given type is a UDT and to indicate the storage format of the UDT.</summary>
      <param name="format">One of the <see cref="T:Microsoft.SqlServer.Server.Format" /> values representing the serialization format of the type.</param>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlUserDefinedTypeAttribute.Format">
      <summary>The serialization format as a <see cref="T:Microsoft.SqlServer.Server.Format" />.</summary>
      <returns>A <see cref="T:Microsoft.SqlServer.Server.Format" /> value representing the serialization format.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlUserDefinedTypeAttribute.IsByteOrdered">
      <summary>Indicates whether the user-defined type is byte ordered.</summary>
      <returns>
        <see langword="true" /> if the user-defined type is byte ordered; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlUserDefinedTypeAttribute.IsFixedLength">
      <summary>Indicates whether all instances of this user-defined type are the same length.</summary>
      <returns>
        <see langword="true" /> if all instances of this type are the same length; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlUserDefinedTypeAttribute.MaxByteSize">
      <summary>The maximum size of the instance, in bytes.</summary>
      <returns>An <see cref="T:System.Int32" /> value representing the maximum size of the instance.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlUserDefinedTypeAttribute.Name">
      <summary>The SQL Server name of the user-defined type.</summary>
      <returns>A <see cref="T:System.String" /> value representing the SQL Server name of the user-defined type.</returns>
    </member>
    <member name="P:Microsoft.SqlServer.Server.SqlUserDefinedTypeAttribute.ValidationMethodName">
      <summary>The name of the method used to validate instances of the user-defined type.</summary>
      <returns>A <see cref="T:System.String" /> representing the name of the method used to validate instances of the user-defined type.</returns>
    </member>
    <member name="T:Microsoft.SqlServer.Server.SystemDataAccessKind">
      <summary>Describes the type of access to system data for a user-defined method or function.</summary>
    </member>
    <member name="F:Microsoft.SqlServer.Server.SystemDataAccessKind.None">
      <summary>The method or function does not access system data.</summary>
    </member>
    <member name="F:Microsoft.SqlServer.Server.SystemDataAccessKind.Read">
      <summary>The method or function reads system data.</summary>
    </member>
    <member name="T:System.Data.OperationAbortedException">
      <summary>This exception is thrown when an ongoing operation is aborted by the user.</summary>
    </member>
    <member name="T:System.Data.Sql.SqlNotificationRequest">
      <summary>Represents a request for notification for a given command.</summary>
    </member>
    <member name="M:System.Data.Sql.SqlNotificationRequest.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Data.Sql.SqlNotificationRequest" /> class with default values.</summary>
    </member>
    <member name="M:System.Data.Sql.SqlNotificationRequest.#ctor(System.String,System.String,System.Int32)">
      <summary>Creates a new instance of the <see cref="T:System.Data.Sql.SqlNotificationRequest" /> class with a user-defined string that identifies a particular notification request, the name of a predefined SQL Server 2005 Service Broker service name, and the time-out period, measured in seconds.</summary>
      <param name="userData">A string that contains an application-specific identifier for this notification. It is not used by the notifications infrastructure, but it allows you to associate notifications with the application state. The value indicated in this parameter is included in the Service Broker queue message.</param>
      <param name="options">A string that contains the Service Broker service name where notification messages are posted, and it must include a database name or a Service Broker instance GUID that restricts the scope of the service name lookup to a particular database.
For more information about the format of the <paramref name="options" /> parameter, see <see cref="P:System.Data.Sql.SqlNotificationRequest.Options" />.</param>
      <param name="timeout">The time, in seconds, to wait for a notification message.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="options" /> parameter is NULL.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="options" /> or <paramref name="userData" /> parameter is longer than <see langword="uint16.MaxValue" /> or the value in the <paramref name="timeout" /> parameter is less than zero.</exception>
    </member>
    <member name="P:System.Data.Sql.SqlNotificationRequest.Options">
      <summary>Gets or sets the SQL Server Service Broker service name where notification messages are posted.</summary>
      <returns>
        <see langword="string" /> that contains the SQL Server 2005 Service Broker service name where notification messages are posted and the database or service broker instance GUID to scope the server name lookup.</returns>
      <exception cref="T:System.ArgumentNullException">The value is NULL.</exception>
      <exception cref="T:System.ArgumentException">The value is longer than <see langword="uint16.MaxValue" />.</exception>
    </member>
    <member name="P:System.Data.Sql.SqlNotificationRequest.Timeout">
      <summary>Gets or sets a value that specifies how long SQL Server waits for a change to occur before the operation times out.</summary>
      <returns>A signed integer value that specifies, in seconds, how long SQL Server waits for a change to occur before the operation times out.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value is less than zero.</exception>
    </member>
    <member name="P:System.Data.Sql.SqlNotificationRequest.UserData">
      <summary>Gets or sets an application-specific identifier for this notification.</summary>
      <returns>A <see langword="string" /> value of the application-specific identifier for this notification.</returns>
      <exception cref="T:System.ArgumentException">The value is longer than <see langword="uint16.MaxValue" />.</exception>
    </member>
    <member name="T:System.Data.SqlClient.ApplicationIntent">
      <summary>Specifies a value for <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.ApplicationIntent" />. Possible values are <see langword="ReadWrite" /> and <see langword="ReadOnly" />.</summary>
    </member>
    <member name="F:System.Data.SqlClient.ApplicationIntent.ReadOnly">
      <summary>The application workload type when connecting to a server is read only.</summary>
    </member>
    <member name="F:System.Data.SqlClient.ApplicationIntent.ReadWrite">
      <summary>The application workload type when connecting to a server is read write.</summary>
    </member>
    <member name="T:System.Data.SqlClient.OnChangeEventHandler">
      <summary>Handles the <see cref="E:System.Data.SqlClient.SqlDependency.OnChange" /> event that is fired when a notification is received for any of the commands associated with a <see cref="T:System.Data.SqlClient.SqlDependency" /> object.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:System.Data.SqlClient.SqlNotificationEventArgs" /> object that contains the event data.</param>
    </member>
    <member name="T:System.Data.SqlClient.PoolBlockingPeriod">
      <summary>Specifies a value for the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.PoolBlockingPeriod" /> property.</summary>
    </member>
    <member name="F:System.Data.SqlClient.PoolBlockingPeriod.AlwaysBlock">
      <summary>Blocking period ON for all SQL servers including Azure SQL servers.</summary>
    </member>
    <member name="F:System.Data.SqlClient.PoolBlockingPeriod.Auto">
      <summary>Blocking period OFF for Azure SQL servers, but ON for all other SQL servers.</summary>
    </member>
    <member name="F:System.Data.SqlClient.PoolBlockingPeriod.NeverBlock">
      <summary>Blocking period OFF for all SQL servers including Azure SQL servers.</summary>
    </member>
    <member name="T:System.Data.SqlClient.SortOrder">
      <summary>Specifies how rows of data are sorted.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SortOrder.Ascending">
      <summary>Rows are sorted in ascending order.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SortOrder.Descending">
      <summary>Rows are sorted in descending order.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SortOrder.Unspecified">
      <summary>The default. No sort order is specified.</summary>
    </member>
    <member name="T:System.Data.SqlClient.SqlBulkCopy">
      <summary>Lets you efficiently bulk load a SQL Server table with data from another source.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.#ctor(System.Data.SqlClient.SqlConnection)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> class using the specified open instance of <see cref="T:System.Data.SqlClient.SqlConnection" />.</summary>
      <param name="connection">The already open <see cref="T:System.Data.SqlClient.SqlConnection" /> instance that will be used to perform the bulk copy operation. If your connection string does not use <see langword="Integrated Security = true" />, you can use <see cref="T:System.Data.SqlClient.SqlCredential" /> to pass the user ID and password more securely than by specifying the user ID and password as text in the connection string.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.#ctor(System.Data.SqlClient.SqlConnection,System.Data.SqlClient.SqlBulkCopyOptions,System.Data.SqlClient.SqlTransaction)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> class using the supplied existing open instance of <see cref="T:System.Data.SqlClient.SqlConnection" />. The <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> instance behaves according to options supplied in the <paramref name="copyOptions" /> parameter. If a non-null <see cref="T:System.Data.SqlClient.SqlTransaction" /> is supplied, the copy operations will be performed within that transaction.</summary>
      <param name="connection">The already open <see cref="T:System.Data.SqlClient.SqlConnection" /> instance that will be used to perform the bulk copy. If your connection string does not use <see langword="Integrated Security = true" />, you can use <see cref="T:System.Data.SqlClient.SqlCredential" /> to pass the user ID and password more securely than by specifying the user ID and password as text in the connection string.</param>
      <param name="copyOptions">A combination of values from the <see cref="T:System.Data.SqlClient.SqlBulkCopyOptions" /> enumeration that determines which data source rows are copied to the destination table.</param>
      <param name="externalTransaction">An existing <see cref="T:System.Data.SqlClient.SqlTransaction" /> instance under which the bulk copy will occur.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.#ctor(System.String)">
      <summary>Initializes and opens a new instance of <see cref="T:System.Data.SqlClient.SqlConnection" /> based on the supplied <paramref name="connectionString" />. The constructor uses the <see cref="T:System.Data.SqlClient.SqlConnection" /> to initialize a new instance of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> class.</summary>
      <param name="connectionString">The string defining the connection that will be opened for use by the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> instance. If your connection string does not use <see langword="Integrated Security = true" />, you can use <see cref="M:System.Data.SqlClient.SqlBulkCopy.#ctor(System.Data.SqlClient.SqlConnection)" /> or <see cref="M:System.Data.SqlClient.SqlBulkCopy.#ctor(System.Data.SqlClient.SqlConnection,System.Data.SqlClient.SqlBulkCopyOptions,System.Data.SqlClient.SqlTransaction)" /> and <see cref="T:System.Data.SqlClient.SqlCredential" /> to pass the user ID and password more securely than by specifying the user ID and password as text in the connection string.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.#ctor(System.String,System.Data.SqlClient.SqlBulkCopyOptions)">
      <summary>Initializes and opens a new instance of <see cref="T:System.Data.SqlClient.SqlConnection" /> based on the supplied <paramref name="connectionString" />. The constructor uses that <see cref="T:System.Data.SqlClient.SqlConnection" /> to initialize a new instance of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> class. The <see cref="T:System.Data.SqlClient.SqlConnection" /> instance behaves according to options supplied in the <paramref name="copyOptions" /> parameter.</summary>
      <param name="connectionString">The string defining the connection that will be opened for use by the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> instance. If your connection string does not use <see langword="Integrated Security = true" />, you can use <see cref="M:System.Data.SqlClient.SqlBulkCopy.#ctor(System.Data.SqlClient.SqlConnection)" /> or <see cref="M:System.Data.SqlClient.SqlBulkCopy.#ctor(System.Data.SqlClient.SqlConnection,System.Data.SqlClient.SqlBulkCopyOptions,System.Data.SqlClient.SqlTransaction)" /> and <see cref="T:System.Data.SqlClient.SqlCredential" /> to pass the user ID and password more securely than by specifying the user ID and password as text in the connection string.</param>
      <param name="copyOptions">A combination of values from the <see cref="T:System.Data.SqlClient.SqlBulkCopyOptions" /> enumeration that determines which data source rows are copied to the destination table.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlBulkCopy.BatchSize">
      <summary>Number of rows in each batch. At the end of each batch, the rows in the batch are sent to the server.</summary>
      <returns>The integer value of the <see cref="P:System.Data.SqlClient.SqlBulkCopy.BatchSize" /> property, or zero if no value has been set.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlBulkCopy.BulkCopyTimeout">
      <summary>Number of seconds for the operation to complete before it times out.</summary>
      <returns>The integer value of the <see cref="P:System.Data.SqlClient.SqlBulkCopy.BulkCopyTimeout" /> property. The default is 30 seconds. A value of 0 indicates no limit; the bulk copy will wait indefinitely.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.Close">
      <summary>Closes the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> instance.</summary>
    </member>
    <member name="P:System.Data.SqlClient.SqlBulkCopy.ColumnMappings">
      <summary>Returns a collection of <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> items. Column mappings define the relationships between columns in the data source and columns in the destination.</summary>
      <returns>A collection of column mappings. By default, it is an empty collection.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName">
      <summary>Name of the destination table on the server.</summary>
      <returns>The string value of the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property, or null if none as been supplied.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlBulkCopy.EnableStreaming">
      <summary>Enables or disables a <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object to stream data from an <see cref="T:System.Data.IDataReader" /> object.</summary>
      <returns>
        <see langword="true" /> if a <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object can stream data from an <see cref="T:System.Data.IDataReader" /> object; otherwise, false. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlBulkCopy.NotifyAfter">
      <summary>Defines the number of rows to be processed before generating a notification event.</summary>
      <returns>The integer value of the <see cref="P:System.Data.SqlClient.SqlBulkCopy.NotifyAfter" /> property, or zero if the property has not been set.</returns>
    </member>
    <member name="E:System.Data.SqlClient.SqlBulkCopy.SqlRowsCopied">
      <summary>Occurs every time that the number of rows specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.NotifyAfter" /> property have been processed.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.System#IDisposable#Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> class.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.Common.DbDataReader)">
      <summary>Copies all rows from the supplied <see cref="T:System.Data.Common.DbDataReader" /> array to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.</summary>
      <param name="reader">A <see cref="T:System.Data.Common.DbDataReader" /> whose rows will be copied to the destination table.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataRow[])">
      <summary>Copies all rows from the supplied <see cref="T:System.Data.DataRow" /> array to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.</summary>
      <param name="rows">An array of <see cref="T:System.Data.DataRow" /> objects that will be copied to the destination table.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataTable)">
      <summary>Copies all rows in the supplied <see cref="T:System.Data.DataTable" /> to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.</summary>
      <param name="table">A <see cref="T:System.Data.DataTable" /> whose rows will be copied to the destination table.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataTable,System.Data.DataRowState)">
      <summary>Copies only rows that match the supplied row state in the supplied <see cref="T:System.Data.DataTable" /> to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.</summary>
      <param name="table">A <see cref="T:System.Data.DataTable" /> whose rows will be copied to the destination table.</param>
      <param name="rowState">A value from the <see cref="T:System.Data.DataRowState" /> enumeration. Only rows matching the row state are copied to the destination.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.IDataReader)">
      <summary>Copies all rows in the supplied <see cref="T:System.Data.IDataReader" /> to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.</summary>
      <param name="reader">A <see cref="T:System.Data.IDataReader" /> whose rows will be copied to the destination table.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.Common.DbDataReader)">
      <summary>The asynchronous version of <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.Common.DbDataReader)" />, which copies all rows from the supplied <see cref="T:System.Data.Common.DbDataReader" /> array to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.</summary>
      <param name="reader">A <see cref="T:System.Data.Common.DbDataReader" /> whose rows will be copied to the destination table.</param>
      <returns>A task representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.Common.DbDataReader,System.Threading.CancellationToken)">
      <summary>The asynchronous version of <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.Common.DbDataReader)" />, which copies all rows from the supplied <see cref="T:System.Data.Common.DbDataReader" /> array to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.</summary>
      <param name="reader">A <see cref="T:System.Data.Common.DbDataReader" /> whose rows will be copied to the destination table.</param>
      <param name="cancellationToken">The cancellation instruction. A <see cref="P:System.Threading.CancellationToken.None" /> value in this parameter makes this method equivalent to <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.Common.DbDataReader)" />.</param>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataRow[])">
      <summary>The asynchronous version of <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataRow[])" />, which copies all rows from the supplied <see cref="T:System.Data.DataRow" /> array to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.</summary>
      <param name="rows">An array of <see cref="T:System.Data.DataRow" /> objects that will be copied to the destination table.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataRow[])" /> multiple times for the same instance before task completion.
Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataRow[])" /> and <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataRow[])" /> for the same instance before task completion.
The connection drops or is closed during <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataRow[])" /> execution.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object was closed during the method execution.
Returned in the task object, there was a connection pool timeout.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlConnection" /> object is closed before method execution.
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Returned in the task object, any error returned by SQL Server that occurred while opening the connection.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataRow[],System.Threading.CancellationToken)">
      <summary>The asynchronous version of <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataRow[])" />, which copies all rows from the supplied <see cref="T:System.Data.DataRow" /> array to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.
The cancellation token can be used to request that the operation be abandoned before the command timeout elapses.  Exceptions will be reported via the returned Task object.</summary>
      <param name="rows">An array of <see cref="T:System.Data.DataRow" /> objects that will be copied to the destination table.</param>
      <param name="cancellationToken">The cancellation instruction. A <see cref="P:System.Threading.CancellationToken.None" /> value in this parameter makes this method equivalent to <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable)" />.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataRow[])" /> multiple times for the same instance before task completion.
Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataRow[])" /> and <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataRow[])" /> for the same instance before task completion.
The connection drops or is closed during <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataRow[])" /> execution.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object was closed during the method execution.
Returned in the task object, there was a connection pool timeout.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlConnection" /> object is closed before method execution.
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Returned in the task object, any error returned by SQL Server that occurred while opening the connection.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable)">
      <summary>The asynchronous version of <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataTable)" />, which copies all rows in the supplied <see cref="T:System.Data.DataTable" /> to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.</summary>
      <param name="table">A <see cref="T:System.Data.DataTable" /> whose rows will be copied to the destination table.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable)" /> multiple times for the same instance before task completion.
Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable)" /> and <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataTable)" /> for the same instance before task completion.
The connection drops or is closed during <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable)" /> execution.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object was closed during the method execution.
Returned in the task object, there was a connection pool timeout.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlConnection" /> object is closed before method execution.
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Returned in the task object, any error returned by SQL Server that occurred while opening the connection.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable,System.Data.DataRowState)">
      <summary>The asynchronous version of <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataTable,System.Data.DataRowState)" />, which copies only rows that match the supplied row state in the supplied <see cref="T:System.Data.DataTable" /> to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.</summary>
      <param name="table">A <see cref="T:System.Data.DataTable" /> whose rows will be copied to the destination table.</param>
      <param name="rowState">A value from the <see cref="T:System.Data.DataRowState" /> enumeration. Only rows matching the row state are copied to the destination.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable,System.Data.DataRowState)" /> multiple times for the same instance before task completion.
Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable,System.Data.DataRowState)" /> and <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataTable,System.Data.DataRowState)" /> for the same instance before task completion.
The connection drops or is closed during <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable,System.Data.DataRowState)" /> execution.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object was closed during the method execution.
Returned in the task object, there was a connection pool timeout.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlConnection" /> object is closed before method execution.
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Returned in the task object, any error returned by SQL Server that occurred while opening the connection.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable,System.Data.DataRowState,System.Threading.CancellationToken)">
      <summary>The asynchronous version of <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataTable,System.Data.DataRowState)" />, which copies only rows that match the supplied row state in the supplied <see cref="T:System.Data.DataTable" /> to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.
The cancellation token can be used to request that the operation be abandoned before the command timeout elapses.  Exceptions will be reported via the returned Task object.</summary>
      <param name="table">A <see cref="T:System.Data.DataTable" /> whose rows will be copied to the destination table.</param>
      <param name="rowState">A value from the <see cref="T:System.Data.DataRowState" /> enumeration. Only rows matching the row state are copied to the destination.</param>
      <param name="cancellationToken">The cancellation instruction. A <see cref="P:System.Threading.CancellationToken.None" /> value in this parameter makes this method equivalent to <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable)" />.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable,System.Data.DataRowState)" /> multiple times for the same instance before task completion.
Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable,System.Data.DataRowState)" /> and <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataTable,System.Data.DataRowState)" /> for the same instance before task completion.
The connection drops or is closed during <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable,System.Data.DataRowState)" /> execution.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object was closed during the method execution.
Returned in the task object, there was a connection pool timeout.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlConnection" /> object is closed before method execution.
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Returned in the task object, any error returned by SQL Server that occurred while opening the connection.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable,System.Threading.CancellationToken)">
      <summary>The asynchronous version of <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataTable)" />, which copies all rows in the supplied <see cref="T:System.Data.DataTable" /> to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.
The cancellation token can be used to request that the operation be abandoned before the command timeout elapses.  Exceptions will be reported via the returned Task object.</summary>
      <param name="table">A <see cref="T:System.Data.DataTable" /> whose rows will be copied to the destination table.</param>
      <param name="cancellationToken">The cancellation instruction. A <see cref="P:System.Threading.CancellationToken.None" /> value in this parameter makes this method equivalent to <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable)" />.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable)" /> multiple times for the same instance before task completion.
Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable)" /> and <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.DataTable)" /> for the same instance before task completion.
The connection drops or is closed during <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable)" /> execution.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object was closed during the method execution.
Returned in the task object, there was a connection pool timeout.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlConnection" /> object is closed before method execution.
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Returned in the task object, any error returned by SQL Server that occurred while opening the connection.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.IDataReader)">
      <summary>The asynchronous version of <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.IDataReader)" />, which copies all rows in the supplied <see cref="T:System.Data.IDataReader" /> to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.</summary>
      <param name="reader">A <see cref="T:System.Data.IDataReader" /> whose rows will be copied to the destination table.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.IDataReader)" /> multiple times for the same instance before task completion.
Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.IDataReader)" /> and <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.IDataReader)" /> for the same instance before task completion.
The connection drops or is closed during <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.IDataReader)" /> execution.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object was closed during the method execution.
Returned in the task object, there was a connection pool timeout.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlConnection" /> object is closed before method execution.
The <see cref="T:System.Data.IDataReader" /> was closed before the completed <see cref="T:System.Threading.Tasks.Task" /> returned.
The <see cref="T:System.Data.IDataReader" />'s associated connection was closed before the completed <see cref="T:System.Threading.Tasks.Task" /> returned.
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Returned in the task object, any error returned by SQL Server that occurred while opening the connection.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.IDataReader,System.Threading.CancellationToken)">
      <summary>The asynchronous version of <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.IDataReader)" />, which copies all rows in the supplied <see cref="T:System.Data.IDataReader" /> to a destination table specified by the <see cref="P:System.Data.SqlClient.SqlBulkCopy.DestinationTableName" /> property of the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object.
The cancellation token can be used to request that the operation be abandoned before the command timeout elapses.  Exceptions will be reported via the returned Task object.</summary>
      <param name="reader">A <see cref="T:System.Data.IDataReader" /> whose rows will be copied to the destination table.</param>
      <param name="cancellationToken">The cancellation instruction. A <see cref="P:System.Threading.CancellationToken.None" /> value in this parameter makes this method equivalent to <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.DataTable)" />.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.IDataReader)" /> multiple times for the same instance before task completion.
Calling <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.IDataReader)" /> and <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServer(System.Data.IDataReader)" /> for the same instance before task completion.
The connection drops or is closed during <see cref="M:System.Data.SqlClient.SqlBulkCopy.WriteToServerAsync(System.Data.IDataReader)" /> execution.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> object was closed during the method execution.
Returned in the task object, there was a connection pool timeout.
Returned in the task object, the <see cref="T:System.Data.SqlClient.SqlConnection" /> object is closed before method execution.
The <see cref="T:System.Data.IDataReader" /> was closed before the completed <see cref="T:System.Threading.Tasks.Task" /> returned.
The <see cref="T:System.Data.IDataReader" />'s associated connection was closed before the completed <see cref="T:System.Threading.Tasks.Task" /> returned.
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Returned in the task object, any error returned by SQL Server that occurred while opening the connection.</exception>
    </member>
    <member name="T:System.Data.SqlClient.SqlBulkCopyColumnMapping">
      <summary>Defines the mapping between a column in a <see cref="T:System.Data.SqlClient.SqlBulkCopy" /> instance's data source and a column in the instance's destination table.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMapping.#ctor">
      <summary>Parameterless constructor that initializes a new <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> object.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMapping.#ctor(System.Int32,System.Int32)">
      <summary>Creates a new column mapping, using column ordinals to refer to source and destination columns.</summary>
      <param name="sourceColumnOrdinal">The ordinal position of the source column within the data source.</param>
      <param name="destinationOrdinal">The ordinal position of the destination column within the destination table.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMapping.#ctor(System.Int32,System.String)">
      <summary>Creates a new column mapping, using a column ordinal to refer to the source column and a column name for the target column.</summary>
      <param name="sourceColumnOrdinal">The ordinal position of the source column within the data source.</param>
      <param name="destinationColumn">The name of the destination column within the destination table.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMapping.#ctor(System.String,System.Int32)">
      <summary>Creates a new column mapping, using a column name to refer to the source column and a column ordinal for the target column.</summary>
      <param name="sourceColumn">The name of the source column within the data source.</param>
      <param name="destinationOrdinal">The ordinal position of the destination column within the destination table.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMapping.#ctor(System.String,System.String)">
      <summary>Creates a new column mapping, using column names to refer to source and destination columns.</summary>
      <param name="sourceColumn">The name of the source column within the data source.</param>
      <param name="destinationColumn">The name of the destination column within the destination table.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlBulkCopyColumnMapping.DestinationColumn">
      <summary>Name of the column being mapped in the destination database table.</summary>
      <returns>The string value of the <see cref="P:System.Data.SqlClient.SqlBulkCopyColumnMapping.DestinationColumn" /> property.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlBulkCopyColumnMapping.DestinationOrdinal">
      <summary>Ordinal value of the destination column within the destination table.</summary>
      <returns>The integer value of the <see cref="P:System.Data.SqlClient.SqlBulkCopyColumnMapping.DestinationOrdinal" /> property, or -1 if the property has not been set.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlBulkCopyColumnMapping.SourceColumn">
      <summary>Name of the column being mapped in the data source.</summary>
      <returns>The string value of the <see cref="P:System.Data.SqlClient.SqlBulkCopyColumnMapping.SourceColumn" /> property.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlBulkCopyColumnMapping.SourceOrdinal">
      <summary>The ordinal position of the source column within the data source.</summary>
      <returns>The integer value of the <see cref="P:System.Data.SqlClient.SqlBulkCopyColumnMapping.SourceOrdinal" /> property.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection">
      <summary>Collection of <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> objects that inherits from <see cref="T:System.Collections.CollectionBase" />.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection.Add(System.Data.SqlClient.SqlBulkCopyColumnMapping)">
      <summary>Adds the specified mapping to the <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection" />.</summary>
      <param name="bulkCopyColumnMapping">The <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> object that describes the mapping to be added to the collection.</param>
      <returns>A <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> object.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection.Add(System.Int32,System.Int32)">
      <summary>Creates a new <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> and adds it to the collection, using ordinals to specify both source and destination columns.</summary>
      <param name="sourceColumnIndex">The ordinal position of the source column within the data source.</param>
      <param name="destinationColumnIndex">The ordinal position of the destination column within the destination table.</param>
      <returns>A column mapping.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection.Add(System.Int32,System.String)">
      <summary>Creates a new <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> and adds it to the collection, using an ordinal for the source column and a string for the destination column.</summary>
      <param name="sourceColumnIndex">The ordinal position of the source column within the data source.</param>
      <param name="destinationColumn">The name of the destination column within the destination table.</param>
      <returns>A column mapping.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection.Add(System.String,System.Int32)">
      <summary>Creates a new <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> and adds it to the collection, using a column name to describe the source column and an ordinal to specify the destination column.</summary>
      <param name="sourceColumn">The name of the source column within the data source.</param>
      <param name="destinationColumnIndex">The ordinal position of the destination column within the destination table.</param>
      <returns>A column mapping.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection.Add(System.String,System.String)">
      <summary>Creates a new <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> and adds it to the collection, using column names to specify both source and destination columns.</summary>
      <param name="sourceColumn">The name of the source column within the data source.</param>
      <param name="destinationColumn">The name of the destination column within the destination table.</param>
      <returns>A column mapping.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection.Clear">
      <summary>Clears the contents of the collection.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection.Contains(System.Data.SqlClient.SqlBulkCopyColumnMapping)">
      <summary>Gets a value indicating whether a specified <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> object exists in the collection.</summary>
      <param name="value">A valid <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> object.</param>
      <returns>
        <see langword="true" /> if the specified mapping exists in the collection; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection.CopyTo(System.Data.SqlClient.SqlBulkCopyColumnMapping[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection" /> to an array of <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> items, starting at a particular index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> array that is the destination of the elements copied from <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection" />. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection.IndexOf(System.Data.SqlClient.SqlBulkCopyColumnMapping)">
      <summary>Gets the index of the specified <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> object.</summary>
      <param name="value">The <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> object for which to search.</param>
      <returns>The zero-based index of the column mapping, or -1 if the column mapping is not found in the collection.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection.Insert(System.Int32,System.Data.SqlClient.SqlBulkCopyColumnMapping)">
      <summary>Insert a new <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> at the index specified.</summary>
      <param name="index">Integer value of the location within the <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection" /> at which to insert the new <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" />.</param>
      <param name="value">
        <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> object to be inserted in the collection.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection.Item(System.Int32)">
      <summary>Gets the <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> object at the specified index.</summary>
      <param name="index">The zero-based index of the <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> to find.</param>
      <returns>A <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> object.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection.Remove(System.Data.SqlClient.SqlBulkCopyColumnMapping)">
      <summary>Removes the specified <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> element from the <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection" />.</summary>
      <param name="value">
        <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> object to be removed from the collection.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlBulkCopyColumnMappingCollection.RemoveAt(System.Int32)">
      <summary>Removes the mapping at the specified index from the collection.</summary>
      <param name="index">The zero-based index of the <see cref="T:System.Data.SqlClient.SqlBulkCopyColumnMapping" /> object to be removed from the collection.</param>
    </member>
    <member name="T:System.Data.SqlClient.SqlBulkCopyOptions">
      <summary>Bitwise flag that specifies one or more options to use with an instance of <see cref="T:System.Data.SqlClient.SqlBulkCopy" />.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlBulkCopyOptions.CheckConstraints">
      <summary>Check constraints while data is being inserted. By default, constraints are not checked.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlBulkCopyOptions.Default">
      <summary>Use the default values for all options.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlBulkCopyOptions.FireTriggers">
      <summary>When specified, cause the server to fire the insert triggers for the rows being inserted into the database.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlBulkCopyOptions.KeepIdentity">
      <summary>Preserve source identity values. When not specified, identity values are assigned by the destination.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlBulkCopyOptions.KeepNulls">
      <summary>Preserve null values in the destination table regardless of the settings for default values. When not specified, null values are replaced by default values where applicable.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlBulkCopyOptions.TableLock">
      <summary>Obtain a bulk update lock for the duration of the bulk copy operation. When not specified, row locks are used.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlBulkCopyOptions.UseInternalTransaction">
      <summary>When specified, each batch of the bulk-copy operation will occur within a transaction. If you indicate this option and also provide a <see cref="T:System.Data.SqlClient.SqlTransaction" /> object to the constructor, an <see cref="T:System.ArgumentException" /> occurs.</summary>
    </member>
    <member name="T:System.Data.SqlClient.SqlClientFactory">
      <summary>Represents a set of methods for creating instances of the <see cref="N:System.Data.SqlClient" /> provider's implementation of the data source classes.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlClientFactory.CreateCommand">
      <summary>Returns a strongly typed <see cref="T:System.Data.Common.DbCommand" /> instance.</summary>
      <returns>A new strongly typed instance of <see cref="T:System.Data.Common.DbCommand" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlClientFactory.CreateCommandBuilder">
      <summary>Returns a strongly typed <see cref="T:System.Data.Common.DbCommandBuilder" /> instance.</summary>
      <returns>A new strongly typed instance of <see cref="T:System.Data.Common.DbCommandBuilder" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlClientFactory.CreateConnection">
      <summary>Returns a strongly typed <see cref="T:System.Data.Common.DbConnection" /> instance.</summary>
      <returns>A new strongly typed instance of <see cref="T:System.Data.Common.DbConnection" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlClientFactory.CreateConnectionStringBuilder">
      <summary>Returns a strongly typed <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> instance.</summary>
      <returns>A new strongly typed instance of <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlClientFactory.CreateDataAdapter">
      <summary>Returns a strongly typed <see cref="T:System.Data.Common.DbDataAdapter" /> instance.</summary>
      <returns>A new strongly typed instance of <see cref="T:System.Data.Common.DbDataAdapter" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlClientFactory.CreateParameter">
      <summary>Returns a strongly typed <see cref="T:System.Data.Common.DbParameter" /> instance.</summary>
      <returns>A new strongly typed instance of <see cref="T:System.Data.Common.DbParameter" />.</returns>
    </member>
    <member name="F:System.Data.SqlClient.SqlClientFactory.Instance">
      <summary>Gets an instance of the <see cref="T:System.Data.SqlClient.SqlClientFactory" />. This can be used to retrieve strongly typed data objects.</summary>
    </member>
    <member name="T:System.Data.SqlClient.SqlClientMetaDataCollectionNames">
      <summary>Provides a list of constants for use with the GetSchema method to retrieve metadata collections.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlClientMetaDataCollectionNames.Columns">
      <summary>A constant for use with the GetSchema method that represents the Columns collection.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlClientMetaDataCollectionNames.Databases">
      <summary>A constant for use with the GetSchema method that represents the Databases collection.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlClientMetaDataCollectionNames.ForeignKeys">
      <summary>A constant for use with the GetSchema method that represents the ForeignKeys collection.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlClientMetaDataCollectionNames.IndexColumns">
      <summary>A constant for use with the GetSchema method that represents the IndexColumns collection.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlClientMetaDataCollectionNames.Indexes">
      <summary>A constant for use with the GetSchema method that represents the Indexes collection.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlClientMetaDataCollectionNames.Parameters">
      <summary>A constant for use with the GetSchema method that represents the Parameters collection.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlClientMetaDataCollectionNames.ProcedureColumns">
      <summary>A constant for use with the GetSchema method that represents the ProcedureColumns collection.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlClientMetaDataCollectionNames.Procedures">
      <summary>A constant for use with the GetSchema method that represents the Procedures collection.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlClientMetaDataCollectionNames.Tables">
      <summary>A constant for use with the GetSchema method that represents the Tables collection.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlClientMetaDataCollectionNames.UserDefinedTypes">
      <summary>A constant for use with the GetSchema method that represents the UserDefinedTypes collection.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlClientMetaDataCollectionNames.Users">
      <summary>A constant for use with the GetSchema method that represents the Users collection.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlClientMetaDataCollectionNames.ViewColumns">
      <summary>A constant for use with the GetSchema method that represents the ViewColumns collection.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlClientMetaDataCollectionNames.Views">
      <summary>A constant for use with the GetSchema method that represents the Views collection.</summary>
    </member>
    <member name="T:System.Data.SqlClient.SqlCommand">
      <summary>Represents a Transact-SQL statement or stored procedure to execute against a SQL Server database. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlCommand" /> class.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlCommand" /> class with the text of the query.</summary>
      <param name="cmdText">The text of the query.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.#ctor(System.String,System.Data.SqlClient.SqlConnection)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlCommand" /> class with the text of the query and a <see cref="T:System.Data.SqlClient.SqlConnection" />.</summary>
      <param name="cmdText">The text of the query.</param>
      <param name="connection">A <see cref="T:System.Data.SqlClient.SqlConnection" /> that represents the connection to an instance of SQL Server.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.#ctor(System.String,System.Data.SqlClient.SqlConnection,System.Data.SqlClient.SqlTransaction)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlCommand" /> class with the text of the query, a <see cref="T:System.Data.SqlClient.SqlConnection" />, and the <see cref="T:System.Data.SqlClient.SqlTransaction" />.</summary>
      <param name="cmdText">The text of the query.</param>
      <param name="connection">A <see cref="T:System.Data.SqlClient.SqlConnection" /> that represents the connection to an instance of SQL Server.</param>
      <param name="transaction">The <see cref="T:System.Data.SqlClient.SqlTransaction" /> in which the <see cref="T:System.Data.SqlClient.SqlCommand" /> executes.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.BeginExecuteNonQuery">
      <summary>Initiates the asynchronous execution of the Transact-SQL statement or stored procedure that is described by this <see cref="T:System.Data.SqlClient.SqlCommand" />.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that can be used to poll or wait for results, or both; this value is also needed when invoking <see cref="M:System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(System.IAsyncResult)" />, which returns the number of affected rows.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Any error that occurred while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.InvalidOperationException">The name/value pair "Asynchronous Processing=true" was not included within the connection string defining the connection for this <see cref="T:System.Data.SqlClient.SqlCommand" />.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.BeginExecuteNonQuery(System.AsyncCallback,System.Object)">
      <summary>Initiates the asynchronous execution of the Transact-SQL statement or stored procedure that is described by this <see cref="T:System.Data.SqlClient.SqlCommand" />, given a callback procedure and state information.</summary>
      <param name="callback">An <see cref="T:System.AsyncCallback" /> delegate that is invoked when the command's execution has completed. Pass <see langword="null" /> (<see langword="Nothing" /> in Microsoft Visual Basic) to indicate that no callback is required.</param>
      <param name="stateObject">A user-defined state object that is passed to the callback procedure. Retrieve this object from within the callback procedure using the <see cref="P:System.IAsyncResult.AsyncState" /> property.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that can be used to poll or wait for results, or both; this value is also needed when invoking <see cref="M:System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(System.IAsyncResult)" />, which returns the number of affected rows.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Any error that occurred while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.InvalidOperationException">The name/value pair "Asynchronous Processing=true" was not included within the connection string defining the connection for this <see cref="T:System.Data.SqlClient.SqlCommand" />.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.BeginExecuteReader">
      <summary>Initiates the asynchronous execution of the Transact-SQL statement or stored procedure that is described by this <see cref="T:System.Data.SqlClient.SqlCommand" />, and retrieves one or more result sets from the server.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that can be used to poll or wait for results, or both; this value is also needed when invoking <see cref="M:System.Data.SqlClient.SqlCommand.EndExecuteReader(System.IAsyncResult)" />, which returns a <see cref="T:System.Data.SqlClient.SqlDataReader" /> instance that can be used to retrieve the returned rows.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Any error that occurred while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.InvalidOperationException">The name/value pair "Asynchronous Processing=true" was not included within the connection string defining the connection for this <see cref="T:System.Data.SqlClient.SqlCommand" />.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.BeginExecuteReader(System.AsyncCallback,System.Object)">
      <summary>Initiates the asynchronous execution of the Transact-SQL statement or stored procedure that is described by this <see cref="T:System.Data.SqlClient.SqlCommand" /> and retrieves one or more result sets from the server, given a callback procedure and state information.</summary>
      <param name="callback">An <see cref="T:System.AsyncCallback" /> delegate that is invoked when the command's execution has completed. Pass <see langword="null" /> (<see langword="Nothing" /> in Microsoft Visual Basic) to indicate that no callback is required.</param>
      <param name="stateObject">A user-defined state object that is passed to the callback procedure. Retrieve this object from within the callback procedure using the <see cref="P:System.IAsyncResult.AsyncState" /> property.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that can be used to poll, wait for results, or both; this value is also needed when invoking <see cref="M:System.Data.SqlClient.SqlCommand.EndExecuteReader(System.IAsyncResult)" />, which returns a <see cref="T:System.Data.SqlClient.SqlDataReader" /> instance which can be used to retrieve the returned rows.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Any error that occurred while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.InvalidOperationException">The name/value pair "Asynchronous Processing=true" was not included within the connection string defining the connection for this <see cref="T:System.Data.SqlClient.SqlCommand" />.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.BeginExecuteReader(System.AsyncCallback,System.Object,System.Data.CommandBehavior)">
      <summary>Initiates the asynchronous execution of the Transact-SQL statement or stored procedure that is described by this <see cref="T:System.Data.SqlClient.SqlCommand" />, using one of the <see langword="CommandBehavior" /> values, and retrieving one or more result sets from the server, given a callback procedure and state information.</summary>
      <param name="callback">An <see cref="T:System.AsyncCallback" /> delegate that is invoked when the command's execution has completed. Pass <see langword="null" /> (<see langword="Nothing" /> in Microsoft Visual Basic) to indicate that no callback is required.</param>
      <param name="stateObject">A user-defined state object that is passed to the callback procedure. Retrieve this object from within the callback procedure using the <see cref="P:System.IAsyncResult.AsyncState" /> property.</param>
      <param name="behavior">One of the <see cref="T:System.Data.CommandBehavior" /> values, indicating options for statement execution and data retrieval.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that can be used to poll or wait for results, or both; this value is also needed when invoking <see cref="M:System.Data.SqlClient.SqlCommand.EndExecuteReader(System.IAsyncResult)" />, which returns a <see cref="T:System.Data.SqlClient.SqlDataReader" /> instance which can be used to retrieve the returned rows.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Any error that occurred while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.InvalidOperationException">The name/value pair "Asynchronous Processing=true" was not included within the connection string defining the connection for this <see cref="T:System.Data.SqlClient.SqlCommand" />.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.BeginExecuteReader(System.Data.CommandBehavior)">
      <summary>Initiates the asynchronous execution of the Transact-SQL statement or stored procedure that is described by this <see cref="T:System.Data.SqlClient.SqlCommand" /> using one of the <see cref="T:System.Data.CommandBehavior" /> values.</summary>
      <param name="behavior">One of the <see cref="T:System.Data.CommandBehavior" /> values, indicating options for statement execution and data retrieval.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that can be used to poll, wait for results, or both; this value is also needed when invoking <see cref="M:System.Data.SqlClient.SqlCommand.EndExecuteReader(System.IAsyncResult)" />, which returns a <see cref="T:System.Data.SqlClient.SqlDataReader" /> instance that can be used to retrieve the returned rows.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Any error that occurred while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.InvalidOperationException">The name/value pair "Asynchronous Processing=true" was not included within the connection string defining the connection for this <see cref="T:System.Data.SqlClient.SqlCommand" />.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.BeginExecuteXmlReader">
      <summary>Initiates the asynchronous execution of the Transact-SQL statement or stored procedure that is described by this <see cref="T:System.Data.SqlClient.SqlCommand" /> and returns results as an <see cref="T:System.Xml.XmlReader" /> object.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that can be used to poll or wait for results, or both; this value is also needed when invoking <see langword="EndExecuteXmlReader" />, which returns a single XML value.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Any error that occurred while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.InvalidOperationException">The name/value pair "Asynchronous Processing=true" was not included within the connection string defining the connection for this <see cref="T:System.Data.SqlClient.SqlCommand" />.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.BeginExecuteXmlReader(System.AsyncCallback,System.Object)">
      <summary>Initiates the asynchronous execution of the Transact-SQL statement or stored procedure that is described by this <see cref="T:System.Data.SqlClient.SqlCommand" /> and returns results as an <see cref="T:System.Xml.XmlReader" /> object, using a callback procedure.</summary>
      <param name="callback">An <see cref="T:System.AsyncCallback" /> delegate that is invoked when the command's execution has completed. Pass <see langword="null" /> (<see langword="Nothing" /> in Microsoft Visual Basic) to indicate that no callback is required.</param>
      <param name="stateObject">A user-defined state object that is passed to the callback procedure. Retrieve this object from within the callback procedure using the <see cref="P:System.IAsyncResult.AsyncState" /> property.</param>
      <returns>An <see cref="T:System.IAsyncResult" /> that can be used to poll, wait for results, or both; this value is also needed when the <see cref="M:System.Data.SqlClient.SqlCommand.EndExecuteXmlReader(System.IAsyncResult)" /> is called, which returns the results of the command as XML.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Any error that occurred while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.InvalidOperationException">The name/value pair "Asynchronous Processing=true" was not included within the connection string defining the connection for this <see cref="T:System.Data.SqlClient.SqlCommand" />.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.Cancel">
      <summary>Tries to cancel the execution of a <see cref="T:System.Data.SqlClient.SqlCommand" />.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.Clone">
      <summary>Creates a new <see cref="T:System.Data.SqlClient.SqlCommand" /> object that is a copy of the current instance.</summary>
      <returns>A new <see cref="T:System.Data.SqlClient.SqlCommand" /> object that is a copy of this instance.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommand.CommandText">
      <summary>Gets or sets the Transact-SQL statement, table name or stored procedure to execute at the data source.</summary>
      <returns>The Transact-SQL statement or stored procedure to execute. The default is an empty string.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommand.CommandTimeout">
      <summary>Gets or sets the wait time before terminating the attempt to execute a command and generating an error.</summary>
      <returns>The time in seconds to wait for the command to execute. The default is 30 seconds.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommand.CommandType">
      <summary>Gets or sets a value indicating how the <see cref="P:System.Data.SqlClient.SqlCommand.CommandText" /> property is to be interpreted.</summary>
      <returns>One of the <see cref="T:System.Data.CommandType" /> values. The default is <see langword="Text" />.</returns>
      <exception cref="T:System.ArgumentException">The value was not a valid <see cref="T:System.Data.CommandType" />.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommand.Connection">
      <summary>Gets or sets the <see cref="T:System.Data.SqlClient.SqlConnection" /> used by this instance of the <see cref="T:System.Data.SqlClient.SqlCommand" />.</summary>
      <returns>The connection to a data source. The default value is <see langword="null" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Data.SqlClient.SqlCommand.Connection" /> property was changed while the command was enlisted in a transaction.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.CreateParameter">
      <summary>Creates a new instance of a <see cref="T:System.Data.SqlClient.SqlParameter" /> object.</summary>
      <returns>A <see cref="T:System.Data.SqlClient.SqlParameter" /> object.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommand.DesignTimeVisible">
      <summary>Gets or sets a value indicating whether the command object should be visible in a Windows Form Designer control.</summary>
      <returns>A value indicating whether the command object should be visible in a control. The default is <see langword="true" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(System.IAsyncResult)">
      <summary>Finishes asynchronous execution of a Transact-SQL statement.</summary>
      <param name="asyncResult">The <see cref="T:System.IAsyncResult" /> returned by the call to <see cref="M:System.Data.SqlClient.SqlCommand.BeginExecuteNonQuery" />.</param>
      <returns>The number of rows affected (the same behavior as <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteNonQuery" />).</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> parameter is null (<see langword="Nothing" /> in Microsoft Visual Basic)</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(System.IAsyncResult)" /> was called more than once for a single command execution, or the method was mismatched against its execution method (for example, the code called <see cref="M:System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(System.IAsyncResult)" /> to complete execution of a call to <see cref="M:System.Data.SqlClient.SqlCommand.BeginExecuteXmlReader" />.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">The amount of time specified in <see cref="P:System.Data.SqlClient.SqlCommand.CommandTimeout" /> elapsed and the asynchronous operation specified with <see cref="Overload:System.Data.SqlClient.SqlCommand.BeginExecuteNonQuery" /> is not complete.
-or-
In some situations, <see cref="T:System.IAsyncResult" /> can be set to <see langword="IsCompleted" /> incorrectly. If this occurs and <see cref="M:System.Data.SqlClient.SqlCommand.EndExecuteNonQuery(System.IAsyncResult)" /> is called, EndExecuteNonQuery could raise a SqlException error if the amount of time specified in <see cref="P:System.Data.SqlClient.SqlCommand.CommandTimeout" /> elapsed and the asynchronous operation specified with <see cref="Overload:System.Data.SqlClient.SqlCommand.BeginExecuteNonQuery" /> is not complete. To correct this situation, you should either increase the value of CommandTimeout or reduce the work being done by the asynchronous operation.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.EndExecuteReader(System.IAsyncResult)">
      <summary>Finishes asynchronous execution of a Transact-SQL statement, returning the requested <see cref="T:System.Data.SqlClient.SqlDataReader" />.</summary>
      <param name="asyncResult">The <see cref="T:System.IAsyncResult" /> returned by the call to <see cref="M:System.Data.SqlClient.SqlCommand.BeginExecuteReader" />.</param>
      <returns>A <see cref="T:System.Data.SqlClient.SqlDataReader" /> object that can be used to retrieve the requested rows.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> parameter is null (<see langword="Nothing" /> in Microsoft Visual Basic)</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Data.SqlClient.SqlCommand.EndExecuteReader(System.IAsyncResult)" /> was called more than once for a single command execution, or the method was mismatched against its execution method (for example, the code called <see cref="M:System.Data.SqlClient.SqlCommand.EndExecuteReader(System.IAsyncResult)" /> to complete execution of a call to <see cref="M:System.Data.SqlClient.SqlCommand.BeginExecuteXmlReader" />.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.EndExecuteXmlReader(System.IAsyncResult)">
      <summary>Finishes asynchronous execution of a Transact-SQL statement, returning the requested data as XML.</summary>
      <param name="asyncResult">The <see cref="T:System.IAsyncResult" /> returned by the call to <see cref="M:System.Data.SqlClient.SqlCommand.BeginExecuteXmlReader" />.</param>
      <returns>An <see cref="T:System.Xml.XmlReader" /> object that can be used to fetch the resulting XML data.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> parameter is null (<see langword="Nothing" /> in Microsoft Visual Basic)</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Data.SqlClient.SqlCommand.EndExecuteXmlReader(System.IAsyncResult)" /> was called more than once for a single command execution, or the method was mismatched against its execution method (for example, the code called <see cref="M:System.Data.SqlClient.SqlCommand.EndExecuteXmlReader(System.IAsyncResult)" /> to complete execution of a call to <see cref="M:System.Data.SqlClient.SqlCommand.BeginExecuteNonQuery" />.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.ExecuteNonQuery">
      <summary>Executes a Transact-SQL statement against the connection and returns the number of rows affected.</summary>
      <returns>The number of rows affected.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">An exception occurred while executing the command against a locked row. This exception is not generated when you are using Microsoft .NET Framework version 1.0.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.ExecuteNonQueryAsync(System.Threading.CancellationToken)">
      <summary>An asynchronous version of <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteNonQuery" />, which executes a Transact-SQL statement against the connection and returns the number of rows affected. The cancellation token can be used to request that the operation be abandoned before the command timeout elapses.  Exceptions will be reported via the returned Task object.</summary>
      <param name="cancellationToken">The cancellation instruction.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteNonQueryAsync(System.Threading.CancellationToken)" /> more than once for the same instance before task completion.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.
-or-
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">SQL Server returned an error while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.ExecuteReader">
      <summary>Sends the <see cref="P:System.Data.SqlClient.SqlCommand.CommandText" /> to the <see cref="P:System.Data.SqlClient.SqlCommand.Connection" /> and builds a <see cref="T:System.Data.SqlClient.SqlDataReader" />.</summary>
      <returns>A <see cref="T:System.Data.SqlClient.SqlDataReader" /> object.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">An exception occurred while executing the command against a locked row. This exception is not generated when you are using Microsoft .NET Framework version 1.0.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.InvalidOperationException">The current state of the connection is closed. <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteReader" /> requires an open <see cref="T:System.Data.SqlClient.SqlConnection" />.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.ExecuteReader(System.Data.CommandBehavior)">
      <summary>Sends the <see cref="P:System.Data.SqlClient.SqlCommand.CommandText" /> to the <see cref="P:System.Data.SqlClient.SqlCommand.Connection" />, and builds a <see cref="T:System.Data.SqlClient.SqlDataReader" /> using one of the <see cref="T:System.Data.CommandBehavior" /> values.</summary>
      <param name="behavior">One of the <see cref="T:System.Data.CommandBehavior" /> values.</param>
      <returns>A <see cref="T:System.Data.SqlClient.SqlDataReader" /> object.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.ExecuteReaderAsync">
      <summary>An asynchronous version of <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteReader" />, which sends the <see cref="P:System.Data.SqlClient.SqlCommand.CommandText" /> to the <see cref="P:System.Data.SqlClient.SqlCommand.Connection" /> and builds a <see cref="T:System.Data.SqlClient.SqlDataReader" />. Exceptions will be reported via the returned Task object.</summary>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.ArgumentException">An invalid <see cref="T:System.Data.CommandBehavior" /> value.</exception>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteReaderAsync" /> more than once for the same instance before task completion.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.
-or-
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">SQL Server returned an error while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.ExecuteReaderAsync(System.Data.CommandBehavior)">
      <summary>An asynchronous version of <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteReader(System.Data.CommandBehavior)" />, which sends the <see cref="P:System.Data.SqlClient.SqlCommand.CommandText" /> to the <see cref="P:System.Data.SqlClient.SqlCommand.Connection" />, and builds a <see cref="T:System.Data.SqlClient.SqlDataReader" />. Exceptions will be reported via the returned Task object.</summary>
      <param name="behavior">Options for statement execution and data retrieval.  When is set to <see langword="Default" />, <see cref="M:System.Data.SqlClient.SqlDataReader.ReadAsync(System.Threading.CancellationToken)" /> reads the entire row before returning a complete Task.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.ArgumentException">An invalid <see cref="T:System.Data.CommandBehavior" /> value.</exception>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteReaderAsync(System.Data.CommandBehavior)" /> more than once for the same instance before task completion.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.
-or-
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">SQL Server returned an error while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.ExecuteReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)">
      <summary>An asynchronous version of <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteReader(System.Data.CommandBehavior)" />, which sends the <see cref="P:System.Data.SqlClient.SqlCommand.CommandText" /> to the <see cref="P:System.Data.SqlClient.SqlCommand.Connection" />, and builds a <see cref="T:System.Data.SqlClient.SqlDataReader" />
The cancellation token can be used to request that the operation be abandoned before the command timeout elapses.  Exceptions will be reported via the returned Task object.</summary>
      <param name="behavior">Options for statement execution and data retrieval.  When is set to <see langword="Default" />, <see cref="M:System.Data.SqlClient.SqlDataReader.ReadAsync(System.Threading.CancellationToken)" /> reads the entire row before returning a complete Task.</param>
      <param name="cancellationToken">The cancellation instruction.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.ArgumentException">An invalid <see cref="T:System.Data.CommandBehavior" /> value.</exception>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" /> more than once for the same instance before task completion.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.
-or-
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">SQL Server returned an error while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.ExecuteReaderAsync(System.Threading.CancellationToken)">
      <summary>An asynchronous version of <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteReader" />, which sends the <see cref="P:System.Data.SqlClient.SqlCommand.CommandText" /> to the <see cref="P:System.Data.SqlClient.SqlCommand.Connection" /> and builds a <see cref="T:System.Data.SqlClient.SqlDataReader" />.
The cancellation token can be used to request that the operation be abandoned before the command timeout elapses.  Exceptions will be reported via the returned Task object.</summary>
      <param name="cancellationToken">The cancellation instruction.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.ArgumentException">An invalid <see cref="T:System.Data.CommandBehavior" /> value.</exception>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" /> more than once for the same instance before task completion.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.
-or-
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">SQL Server returned an error while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.ExecuteScalar">
      <summary>Executes the query, and returns the first column of the first row in the result set returned by the query. Additional columns or rows are ignored.</summary>
      <returns>The first column of the first row in the result set, or a null reference (<see langword="Nothing" /> in Visual Basic) if the result set is empty. Returns a maximum of 2033 characters.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">An exception occurred while executing the command against a locked row. This exception is not generated when you are using Microsoft .NET Framework version 1.0.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.ExecuteScalarAsync(System.Threading.CancellationToken)">
      <summary>An asynchronous version of <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteScalar" />, which executes the query asynchronously and returns the first column of the first row in the result set returned by the query. Additional columns or rows are ignored.
The cancellation token can be used to request that the operation be abandoned before the command timeout elapses. Exceptions will be reported via the returned Task object.</summary>
      <param name="cancellationToken">The cancellation instruction.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteScalarAsync(System.Threading.CancellationToken)" /> more than once for the same instance before task completion.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.
-or-
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">SQL Server returned an error while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.ExecuteXmlReader">
      <summary>Sends the <see cref="P:System.Data.SqlClient.SqlCommand.CommandText" /> to the <see cref="P:System.Data.SqlClient.SqlCommand.Connection" /> and builds an <see cref="T:System.Xml.XmlReader" /> object.</summary>
      <returns>An <see cref="T:System.Xml.XmlReader" /> object.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">An exception occurred while executing the command against a locked row. This exception is not generated when you are using Microsoft .NET Framework version 1.0.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.ExecuteXmlReaderAsync">
      <summary>An asynchronous version of <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteXmlReader" />, which sends the <see cref="P:System.Data.SqlClient.SqlCommand.CommandText" /> to the <see cref="P:System.Data.SqlClient.SqlCommand.Connection" /> and builds an <see cref="T:System.Xml.XmlReader" /> object.
Exceptions will be reported via the returned Task object.</summary>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteScalarAsync(System.Threading.CancellationToken)" /> more than once for the same instance before task completion.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.
-or-
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">SQL Server returned an error while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.ExecuteXmlReaderAsync(System.Threading.CancellationToken)">
      <summary>An asynchronous version of <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteXmlReader" />, which sends the <see cref="P:System.Data.SqlClient.SqlCommand.CommandText" /> to the <see cref="P:System.Data.SqlClient.SqlCommand.Connection" /> and builds an <see cref="T:System.Xml.XmlReader" /> object.
The cancellation token can be used to request that the operation be abandoned before the command timeout elapses.  Exceptions will be reported via the returned Task object.</summary>
      <param name="cancellationToken">The cancellation instruction.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidCastException">A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Binary or VarBinary was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.Stream" />. For more information about streaming, see SqlClient Streaming Support.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Char, NChar, NVarChar, VarChar, or  Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.IO.TextReader" />.
-or-
A <see cref="P:System.Data.SqlClient.SqlParameter.SqlDbType" /> other than Xml was used when <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> was set to <see cref="T:System.Xml.XmlReader" />.</exception>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlCommand.ExecuteScalarAsync(System.Threading.CancellationToken)" /> more than once for the same instance before task completion.
-or-
The <see cref="T:System.Data.SqlClient.SqlConnection" /> closed or dropped during a streaming operation. For more information about streaming, see SqlClient Streaming Support.
-or-
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">SQL Server returned an error while executing the command text.
-or-
A timeout occurred during a streaming operation. For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.IO.IOException">An error occurred in a <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.Stream" />, <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.IO.TextReader" /> object was closed during a streaming operation.  For more information about streaming, see SqlClient Streaming Support.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommand.Notification">
      <summary>Gets or sets a value that specifies the <see cref="T:System.Data.Sql.SqlNotificationRequest" /> object bound to this command.</summary>
      <returns>When set to null (default), no notification should be requested.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommand.Parameters">
      <summary>Gets the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</summary>
      <returns>The parameters of the Transact-SQL statement or stored procedure. The default is an empty collection.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.Prepare">
      <summary>Creates a prepared version of the command on an instance of SQL Server.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.ResetCommandTimeout">
      <summary>Resets the <see cref="P:System.Data.SqlClient.SqlCommand.CommandTimeout" /> property to its default value.</summary>
    </member>
    <member name="E:System.Data.SqlClient.SqlCommand.StatementCompleted">
      <summary>Occurs when the execution of a Transact-SQL statement completes.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommand.System#ICloneable#Clone">
      <summary>Creates a new <see cref="T:System.Data.SqlClient.SqlCommand" /> object that is a copy of the current instance.</summary>
      <returns>A new <see cref="T:System.Data.SqlClient.SqlCommand" /> object that is a copy of this instance.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommand.Transaction">
      <summary>Gets or sets the <see cref="T:System.Data.SqlClient.SqlTransaction" /> within which the <see cref="T:System.Data.SqlClient.SqlCommand" /> executes.</summary>
      <returns>The <see cref="T:System.Data.SqlClient.SqlTransaction" />. The default value is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommand.UpdatedRowSource">
      <summary>Gets or sets how command results are applied to the <see cref="T:System.Data.DataRow" /> when used by the Update method of the <see cref="T:System.Data.Common.DbDataAdapter" />.</summary>
      <returns>One of the <see cref="T:System.Data.UpdateRowSource" /> values.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlCommandBuilder">
      <summary>Automatically generates single-table commands that are used to reconcile changes made to a <see cref="T:System.Data.DataSet" /> with the associated SQL Server database. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommandBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlCommandBuilder" /> class.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommandBuilder.#ctor(System.Data.SqlClient.SqlDataAdapter)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlCommandBuilder" /> class with the associated <see cref="T:System.Data.SqlClient.SqlDataAdapter" /> object.</summary>
      <param name="adapter">The name of the <see cref="T:System.Data.SqlClient.SqlDataAdapter" />.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommandBuilder.CatalogLocation">
      <summary>Sets or gets the <see cref="T:System.Data.Common.CatalogLocation" /> for an instance of the <see cref="T:System.Data.SqlClient.SqlCommandBuilder" /> class.</summary>
      <returns>A <see cref="T:System.Data.Common.CatalogLocation" /> object.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommandBuilder.CatalogSeparator">
      <summary>Sets or gets a string used as the catalog separator for an instance of the <see cref="T:System.Data.SqlClient.SqlCommandBuilder" /> class.</summary>
      <returns>A string that indicates the catalog separator for use with an instance of the <see cref="T:System.Data.SqlClient.SqlCommandBuilder" /> class.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommandBuilder.DataAdapter">
      <summary>Gets or sets a <see cref="T:System.Data.SqlClient.SqlDataAdapter" /> object for which Transact-SQL statements are automatically generated.</summary>
      <returns>A <see cref="T:System.Data.SqlClient.SqlDataAdapter" /> object.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommandBuilder.DeriveParameters(System.Data.SqlClient.SqlCommand)">
      <summary>Retrieves parameter information from the stored procedure specified in the <see cref="T:System.Data.SqlClient.SqlCommand" /> and populates the <see cref="P:System.Data.SqlClient.SqlCommand.Parameters" /> collection of the specified <see cref="T:System.Data.SqlClient.SqlCommand" /> object.</summary>
      <param name="command">The <see cref="T:System.Data.SqlClient.SqlCommand" /> referencing the stored procedure from which the parameter information is to be derived. The derived parameters are added to the <see cref="P:System.Data.SqlClient.SqlCommand.Parameters" /> collection of the <see cref="T:System.Data.SqlClient.SqlCommand" />.</param>
      <exception cref="T:System.InvalidOperationException">The command text is not a valid stored procedure name.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommandBuilder.GetDeleteCommand">
      <summary>Gets the automatically generated <see cref="T:System.Data.SqlClient.SqlCommand" /> object required to perform deletions on the database.</summary>
      <returns>The automatically generated <see cref="T:System.Data.SqlClient.SqlCommand" /> object required to perform deletions.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommandBuilder.GetDeleteCommand(System.Boolean)">
      <summary>Gets the automatically generated <see cref="T:System.Data.SqlClient.SqlCommand" /> object that is required to perform deletions on the database.</summary>
      <param name="useColumnsForParameterNames">If <see langword="true" />, generate parameter names matching column names if possible. If <see langword="false" />, generate <c>@p1</c>, <c>@p2</c>, and so on.</param>
      <returns>The automatically generated <see cref="T:System.Data.SqlClient.SqlCommand" /> object that is required to perform deletions.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommandBuilder.GetInsertCommand">
      <summary>Gets the automatically generated <see cref="T:System.Data.SqlClient.SqlCommand" /> object required to perform insertions on the database.</summary>
      <returns>The automatically generated <see cref="T:System.Data.SqlClient.SqlCommand" /> object required to perform insertions.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommandBuilder.GetInsertCommand(System.Boolean)">
      <summary>Gets the automatically generated <see cref="T:System.Data.SqlClient.SqlCommand" /> object that is required to perform insertions on the database.</summary>
      <param name="useColumnsForParameterNames">If <see langword="true" />, generate parameter names matching column names if possible. If <see langword="false" />, generate <c>@p1</c>, <c>@p2</c>, and so on.</param>
      <returns>The automatically generated <see cref="T:System.Data.SqlClient.SqlCommand" /> object that is required to perform insertions.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommandBuilder.GetUpdateCommand">
      <summary>Gets the automatically generated <see cref="T:System.Data.SqlClient.SqlCommand" /> object required to perform updates on the database.</summary>
      <returns>The automatically generated <see cref="T:System.Data.SqlClient.SqlCommand" /> object that is required to perform updates.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommandBuilder.GetUpdateCommand(System.Boolean)">
      <summary>Gets the automatically generated <see cref="T:System.Data.SqlClient.SqlCommand" /> object required to perform updates on the database.</summary>
      <param name="useColumnsForParameterNames">If <see langword="true" />, generate parameter names matching column names if possible. If <see langword="false" />, generate <c>@p1</c>, <c>@p2</c>, and so on.</param>
      <returns>The automatically generated <see cref="T:System.Data.SqlClient.SqlCommand" /> object required to perform updates.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommandBuilder.QuoteIdentifier(System.String)">
      <summary>Given an unquoted identifier in the correct catalog case, returns the correct quoted form of that identifier. This includes correctly escaping any embedded quotes in the identifier.</summary>
      <param name="unquotedIdentifier">The original unquoted identifier.</param>
      <returns>The quoted version of the identifier. Embedded quotes within the identifier are correctly escaped.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommandBuilder.QuotePrefix">
      <summary>Gets or sets the starting character or characters to use when specifying SQL Server database objects, such as tables or columns, whose names contain characters such as spaces or reserved tokens.</summary>
      <returns>The starting character or characters to use. The default is an empty string.</returns>
      <exception cref="T:System.InvalidOperationException">This property cannot be changed after an INSERT, UPDATE, or DELETE command has been generated.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommandBuilder.QuoteSuffix">
      <summary>Gets or sets the ending character or characters to use when specifying SQL Server database objects, such as tables or columns, whose names contain characters such as spaces or reserved tokens.</summary>
      <returns>The ending character or characters to use. The default is an empty string.</returns>
      <exception cref="T:System.InvalidOperationException">This property cannot be changed after an insert, update, or delete command has been generated.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlCommandBuilder.SchemaSeparator">
      <summary>Gets or sets the character to be used for the separator between the schema identifier and any other identifiers.</summary>
      <returns>The character to be used as the schema separator.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlCommandBuilder.UnquoteIdentifier(System.String)">
      <summary>Given a quoted identifier, returns the correct unquoted form of that identifier. This includes correctly unescaping any embedded quotes in the identifier.</summary>
      <param name="quotedIdentifier">The identifier that will have its embedded quotes removed.</param>
      <returns>The unquoted identifier, with embedded quotes properly unescaped.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlConnection">
      <summary>Represents a connection to a SQL Server database. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlConnection" /> class.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlConnection" /> class when given a string that contains the connection string.</summary>
      <param name="connectionString">The connection used to open the SQL Server database.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.#ctor(System.String,System.Data.SqlClient.SqlCredential)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlConnection" /> class given a connection string, that does not use <see langword="Integrated Security = true" /> and a <see cref="T:System.Data.SqlClient.SqlCredential" /> object that contains the user ID and password.</summary>
      <param name="connectionString">A connection string that does not use any of the following connection string keywords: <see langword="Integrated Security = true" />, <see langword="UserId" />, or <see langword="Password" />; or that does not use <see langword="ContextConnection = true" />.</param>
      <param name="credential">A <see cref="T:System.Data.SqlClient.SqlCredential" /> object. If <paramref name="credential" /> is null, <see cref="M:System.Data.SqlClient.SqlConnection.#ctor(System.String,System.Data.SqlClient.SqlCredential)" /> is functionally equivalent to <see cref="M:System.Data.SqlClient.SqlConnection.#ctor(System.String)" />.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnection.AccessToken">
      <summary>Gets or sets the access token for the connection.</summary>
      <returns>The access token for the connection.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.BeginTransaction">
      <summary>Starts a database transaction.</summary>
      <returns>An object representing the new transaction.</returns>
      <exception cref="T:System.Data.SqlClient.SqlException">Parallel transactions are not allowed when using Multiple Active Result Sets (MARS).</exception>
      <exception cref="T:System.InvalidOperationException">Parallel transactions are not supported.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.BeginTransaction(System.Data.IsolationLevel)">
      <summary>Starts a database transaction with the specified isolation level.</summary>
      <param name="iso">The isolation level under which the transaction should run.</param>
      <returns>An object representing the new transaction.</returns>
      <exception cref="T:System.Data.SqlClient.SqlException">Parallel transactions are not allowed when using Multiple Active Result Sets (MARS).</exception>
      <exception cref="T:System.InvalidOperationException">Parallel transactions are not supported.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.BeginTransaction(System.Data.IsolationLevel,System.String)">
      <summary>Starts a database transaction with the specified isolation level and transaction name.</summary>
      <param name="iso">The isolation level under which the transaction should run.</param>
      <param name="transactionName">The name of the transaction.</param>
      <returns>An object representing the new transaction.</returns>
      <exception cref="T:System.Data.SqlClient.SqlException">Parallel transactions are not allowed when using Multiple Active Result Sets (MARS).</exception>
      <exception cref="T:System.InvalidOperationException">Parallel transactions are not supported.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.BeginTransaction(System.String)">
      <summary>Starts a database transaction with the specified transaction name.</summary>
      <param name="transactionName">The name of the transaction.</param>
      <returns>An object representing the new transaction.</returns>
      <exception cref="T:System.Data.SqlClient.SqlException">Parallel transactions are not allowed when using Multiple Active Result Sets (MARS).</exception>
      <exception cref="T:System.InvalidOperationException">Parallel transactions are not supported.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.ChangeDatabase(System.String)">
      <summary>Changes the current database for an open <see cref="T:System.Data.SqlClient.SqlConnection" />.</summary>
      <param name="database">The name of the database to use instead of the current database.</param>
      <exception cref="T:System.ArgumentException">The database name is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">The connection is not open.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Cannot change the database.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.ChangePassword(System.String,System.Data.SqlClient.SqlCredential,System.Security.SecureString)">
      <summary>Changes the SQL Server password for the user indicated in the <see cref="T:System.Data.SqlClient.SqlCredential" /> object.</summary>
      <param name="connectionString">The connection string that contains enough information to connect to a server. The connection string should not use any of the following connection string keywords: <see langword="Integrated Security = true" />, <see langword="UserId" />, or <see langword="Password" />; or <see langword="ContextConnection = true" />.</param>
      <param name="credential">A <see cref="T:System.Data.SqlClient.SqlCredential" /> object.</param>
      <param name="newPassword">The new password.<paramref name="newPassword" /> must be read only. The password must also comply with any password security policy set on the server (for example, minimum length and requirements for specific characters).</param>
      <exception cref="T:System.ArgumentException">The connection string contains any combination of <see langword="UserId" />, <see langword="Password" />, or <see langword="Integrated Security=true" />.
-or-
The connection string contains <see langword="Context Connection=true" />.
-or-
<paramref name="newSecurePassword" /> (or <paramref name="newPassword" />) is greater than 128 characters.
-or-
<paramref name="newSecurePassword" /> (or <paramref name="newPassword" />) is not read only.
-or-
<paramref name="newSecurePassword" /> (or <paramref name="newPassword" />) is an empty string.</exception>
      <exception cref="T:System.ArgumentNullException">One of the parameters (<paramref name="connectionString" />, <paramref name="credential" />, or <paramref name="newSecurePassword" />) is null.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.ChangePassword(System.String,System.String)">
      <summary>Changes the SQL Server password for the user indicated in the connection string to the supplied new password.</summary>
      <param name="connectionString">The connection string that contains enough information to connect to the server that you want. The connection string must contain the user ID and the current password.</param>
      <param name="newPassword">The new password to set. This password must comply with any password security policy set on the server, including minimum length, requirements for specific characters, and so on.</param>
      <exception cref="T:System.ArgumentException">The connection string includes the option to use integrated security.
Or
The <paramref name="newPassword" /> exceeds 128 characters.</exception>
      <exception cref="T:System.ArgumentNullException">Either the <paramref name="connectionString" /> or the <paramref name="newPassword" /> parameter is null.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.ClearAllPools">
      <summary>Empties the connection pool.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.ClearPool(System.Data.SqlClient.SqlConnection)">
      <summary>Empties the connection pool associated with the specified connection.</summary>
      <param name="connection">The <see cref="T:System.Data.SqlClient.SqlConnection" /> to be cleared from the pool.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnection.ClientConnectionId">
      <summary>The connection ID of the most recent connection attempt, regardless of whether the attempt succeeded or failed.</summary>
      <returns>The connection ID of the most recent connection attempt.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.Close">
      <summary>Closes the connection to the database. This is the preferred method of closing any open connection.</summary>
      <exception cref="T:System.Data.SqlClient.SqlException">The connection-level error that occurred while opening the connection.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnection.ConnectionString">
      <summary>Gets or sets the string used to open a SQL Server database.</summary>
      <returns>The connection string that includes the source database name, and other parameters needed to establish the initial connection. The default value is an empty string.</returns>
      <exception cref="T:System.ArgumentException">An invalid connection string argument has been supplied, or a required connection string argument has not been supplied.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnection.ConnectionTimeout">
      <summary>Gets the time to wait while trying to establish a connection before terminating the attempt and generating an error.</summary>
      <returns>The time (in seconds) to wait for a connection to open. The default value is 15 seconds.</returns>
      <exception cref="T:System.ArgumentException">The value set is less than 0.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.CreateCommand">
      <summary>Creates and returns a <see cref="T:System.Data.SqlClient.SqlCommand" /> object associated with the <see cref="T:System.Data.SqlClient.SqlConnection" />.</summary>
      <returns>A <see cref="T:System.Data.SqlClient.SqlCommand" /> object.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnection.Credential">
      <summary>Gets or sets the <see cref="T:System.Data.SqlClient.SqlCredential" /> object for this connection.</summary>
      <returns>The <see cref="T:System.Data.SqlClient.SqlCredential" /> object for this connection.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnection.Database">
      <summary>Gets the name of the current database or the database to be used after a connection is opened.</summary>
      <returns>The name of the current database or the name of the database to be used after a connection is opened. The default value is an empty string.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnection.DataSource">
      <summary>Gets the name of the instance of SQL Server to which to connect.</summary>
      <returns>The name of the instance of SQL Server to which to connect. The default value is an empty string.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnection.FireInfoMessageEventOnUserErrors">
      <summary>Gets or sets the <see cref="P:System.Data.SqlClient.SqlConnection.FireInfoMessageEventOnUserErrors" /> property.</summary>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Data.SqlClient.SqlConnection.FireInfoMessageEventOnUserErrors" /> property has been set; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.GetSchema">
      <summary>Returns schema information for the data source of this <see cref="T:System.Data.SqlClient.SqlConnection" />. For more information about scheme, see SQL Server Schema Collections.</summary>
      <returns>A <see cref="T:System.Data.DataTable" /> that contains schema information.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.GetSchema(System.String)">
      <summary>Returns schema information for the data source of this <see cref="T:System.Data.SqlClient.SqlConnection" /> using the specified string for the schema name.</summary>
      <param name="collectionName">Specifies the name of the schema to return.</param>
      <returns>A <see cref="T:System.Data.DataTable" /> that contains schema information.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="collectionName" /> is specified as null.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.GetSchema(System.String,System.String[])">
      <summary>Returns schema information for the data source of this <see cref="T:System.Data.SqlClient.SqlConnection" /> using the specified string for the schema name and the specified string array for the restriction values.</summary>
      <param name="collectionName">Specifies the name of the schema to return.</param>
      <param name="restrictionValues">A set of restriction values for the requested schema.</param>
      <returns>A <see cref="T:System.Data.DataTable" /> that contains schema information.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="collectionName" /> is specified as null.</exception>
    </member>
    <member name="E:System.Data.SqlClient.SqlConnection.InfoMessage">
      <summary>Occurs when SQL Server returns a warning or informational message.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.Open">
      <summary>Opens a database connection with the property settings specified by the <see cref="P:System.Data.SqlClient.SqlConnection.ConnectionString" />.</summary>
      <exception cref="T:System.InvalidOperationException">Cannot open a connection without specifying a data source or server.
or
The connection is already open.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">A connection-level error occurred while opening the connection. If the <see cref="P:System.Data.SqlClient.SqlException.Number" /> property contains the value 18487 or 18488, this indicates that the specified password has expired or must be reset. See the <see cref="M:System.Data.SqlClient.SqlConnection.ChangePassword(System.String,System.String)" /> method for more information.
The <see langword="&lt;system.data.localdb&gt;" /> tag in the app.config file has invalid or unknown elements.</exception>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">There are two entries with the same name in the <see langword="&lt;localdbinstances&gt;" /> section.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.OpenAsync(System.Threading.CancellationToken)">
      <summary>An asynchronous version of <see cref="M:System.Data.SqlClient.SqlConnection.Open" />, which opens a database connection with the property settings specified by the <see cref="P:System.Data.SqlClient.SqlConnection.ConnectionString" />. The cancellation token can be used to request that the operation be abandoned before the connection timeout elapses.  Exceptions will be propagated via the returned Task. If the connection timeout time elapses without successfully connecting, the returned Task will be marked as faulted with an Exception. The implementation returns a Task without blocking the calling thread for both pooled and non-pooled connections.</summary>
      <param name="cancellationToken">The cancellation instruction.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlConnection.OpenAsync(System.Threading.CancellationToken)" /> more than once for the same instance before task completion.
<see langword="Context Connection=true" /> is specified in the connection string.
A connection was not available from the connection pool before the connection time out elapsed.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">Any error returned by SQL Server that occurred while opening the connection.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnection.PacketSize">
      <summary>Gets the size (in bytes) of network packets used to communicate with an instance of SQL Server.</summary>
      <returns>The size (in bytes) of network packets. The default value is 8000.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.ResetStatistics">
      <summary>If statistics gathering is enabled, all values are reset to zero.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.RetrieveStatistics">
      <summary>Returns a name value pair collection of statistics at the point in time the method is called.</summary>
      <returns>Returns a reference of type <see cref="T:System.Collections.IDictionary" /> of <see cref="T:System.Collections.DictionaryEntry" /> items.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnection.ServerVersion">
      <summary>Gets a string that contains the version of the instance of SQL Server to which the client is connected.</summary>
      <returns>The version of the instance of SQL Server.</returns>
      <exception cref="T:System.InvalidOperationException">The connection is closed.
<see cref="P:System.Data.SqlClient.SqlConnection.ServerVersion" /> was called while the returned Task was not completed and the connection was not opened after a call to <see cref="M:System.Data.SqlClient.SqlConnection.OpenAsync(System.Threading.CancellationToken)" />.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnection.State">
      <summary>Indicates the state of the <see cref="T:System.Data.SqlClient.SqlConnection" /> during the most recent network operation performed on the connection.</summary>
      <returns>An <see cref="T:System.Data.ConnectionState" /> enumeration.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnection.StatisticsEnabled">
      <summary>When set to <see langword="true" />, enables statistics gathering for the current connection.</summary>
      <returns>Returns <see langword="true" /> if statistics gathering is enabled; otherwise <see langword="false" />. <see langword="false" /> is the default.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnection.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current instance.</summary>
      <returns>A new object that is a copy of this instance.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnection.WorkstationId">
      <summary>Gets a string that identifies the database client.</summary>
      <returns>A string that identifies the database client. If not specified, the name of the client computer. If neither is specified, the value is an empty string.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlConnectionStringBuilder">
      <summary>Provides a simple way to create and manage the contents of connection strings used by the <see cref="T:System.Data.SqlClient.SqlConnection" /> class.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnectionStringBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" /> class.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnectionStringBuilder.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" /> class. The provided connection string provides the data for the instance's internal connection information.</summary>
      <param name="connectionString">The basis for the object's internal connection information. Parsed into name/value pairs. Invalid key names raise <see cref="T:System.Collections.Generic.KeyNotFoundException" />.</param>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Invalid key name within the connection string.</exception>
      <exception cref="T:System.FormatException">Invalid value within the connection string (specifically, when a Boolean or numeric value was expected but not supplied).</exception>
      <exception cref="T:System.ArgumentException">The supplied <paramref name="connectionString" /> is not valid.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.ApplicationIntent">
      <summary>Declares the application workload type when connecting to a database in an SQL Server Availability Group. You can set the value of this property with <see cref="T:System.Data.SqlClient.ApplicationIntent" />. For more information about SqlClient support for Always On Availability Groups, see SqlClient Support for High Availability, Disaster Recovery.</summary>
      <returns>Returns the current value of the property (a value of type <see cref="T:System.Data.SqlClient.ApplicationIntent" />).</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.ApplicationName">
      <summary>Gets or sets the name of the application associated with the connection string.</summary>
      <returns>The name of the application, or ".NET SqlClient Data Provider" if no name has been supplied.</returns>
      <exception cref="T:System.ArgumentNullException">To set the value to null, use <see cref="F:System.DBNull.Value" />.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.AttachDBFilename">
      <summary>Gets or sets a string that contains the name of the primary data file. This includes the full path name of an attachable database.</summary>
      <returns>The value of the <see langword="AttachDBFilename" /> property, or <see langword="String.Empty" /> if no value has been supplied.</returns>
      <exception cref="T:System.ArgumentNullException">To set the value to null, use <see cref="F:System.DBNull.Value" />.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnectionStringBuilder.Clear">
      <summary>Clears the contents of the <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" /> instance.</summary>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.ConnectRetryCount">
      <summary>The number of reconnections attempted after identifying that there was an idle connection failure. This must be an integer between 0 and 255. Default is 1. Set to 0 to disable reconnecting on idle connection failures. An <see cref="T:System.ArgumentException" /> will be thrown if set to a value outside of the allowed range.</summary>
      <returns>The number of reconnections attempted after identifying that there was an idle connection failure.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.ConnectRetryInterval">
      <summary>Amount of time (in seconds) between each reconnection attempt after identifying that there was an idle connection failure. This must be an integer between 1 and 60. The default is 10 seconds. An <see cref="T:System.ArgumentException" /> will be thrown if set to a value outside of the allowed range.</summary>
      <returns>Amount of time (in seconds) between each reconnection attempt after identifying that there was an idle connection failure.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.ConnectTimeout">
      <summary>Gets or sets the length of time (in seconds) to wait for a connection to the server before terminating the attempt and generating an error.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.ConnectTimeout" /> property, or 15 seconds if no value has been supplied.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnectionStringBuilder.ContainsKey(System.String)">
      <summary>Determines whether the <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" /> contains a specific key.</summary>
      <param name="keyword">The key to locate in the <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" />.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" /> contains an element that has the specified key; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> is null (<see langword="Nothing" /> in Visual Basic)</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.CurrentLanguage">
      <summary>Gets or sets the SQL Server Language record name.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.CurrentLanguage" /> property, or <see langword="String.Empty" /> if no value has been supplied.</returns>
      <exception cref="T:System.ArgumentNullException">To set the value to null, use <see cref="F:System.DBNull.Value" />.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.DataSource">
      <summary>Gets or sets the name or network address of the instance of SQL Server to connect to.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.DataSource" /> property, or <see langword="String.Empty" /> if none has been supplied.</returns>
      <exception cref="T:System.ArgumentNullException">To set the value to null, use <see cref="F:System.DBNull.Value" />.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.Encrypt">
      <summary>Gets or sets a Boolean value that indicates whether SQL Server uses SSL encryption for all data sent between the client and server if the server has a certificate installed.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.Encrypt" /> property, or <see langword="false" /> if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.Enlist">
      <summary>Gets or sets a Boolean value that indicates whether the SQL Server connection pooler automatically enlists the connection in the creation thread's current transaction context.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.Enlist" /> property, or <see langword="true" /> if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.FailoverPartner">
      <summary>Gets or sets the name or address of the partner server to connect to if the primary server is down.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.FailoverPartner" /> property, or <see langword="String.Empty" /> if none has been supplied.</returns>
      <exception cref="T:System.ArgumentNullException">To set the value to null, use <see cref="F:System.DBNull.Value" />.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.InitialCatalog">
      <summary>Gets or sets the name of the database associated with the connection.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.InitialCatalog" /> property, or <see langword="String.Empty" /> if none has been supplied.</returns>
      <exception cref="T:System.ArgumentNullException">To set the value to null, use <see cref="F:System.DBNull.Value" />.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.IntegratedSecurity">
      <summary>Gets or sets a Boolean value that indicates whether User ID and Password are specified in the connection (when <see langword="false" />) or whether the current Windows account credentials are used for authentication (when <see langword="true" />).</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.IntegratedSecurity" /> property, or <see langword="false" /> if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.Item(System.String)">
      <summary>Gets or sets the value associated with the specified key. In C#, this property is the indexer.</summary>
      <param name="keyword">The key of the item to get or set.</param>
      <returns>The value associated with the specified key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> is a null reference (<see langword="Nothing" /> in Visual Basic).</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Tried to add a key that does not exist within the available keys.</exception>
      <exception cref="T:System.FormatException">Invalid value within the connection string (specifically, a Boolean or numeric value was expected but not supplied).</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.Keys">
      <summary>Gets an <see cref="T:System.Collections.ICollection" /> that contains the keys in the <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" />.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> that contains the keys in the <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.LoadBalanceTimeout">
      <summary>Gets or sets the minimum time, in seconds, for the connection to live in the connection pool before being destroyed.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.LoadBalanceTimeout" /> property, or 0 if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.MaxPoolSize">
      <summary>Gets or sets the maximum number of connections allowed in the connection pool for this specific connection string.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.MaxPoolSize" /> property, or 100 if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.MinPoolSize">
      <summary>Gets or sets the minimum number of connections allowed in the connection pool for this specific connection string.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.MinPoolSize" /> property, or 0 if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.MultipleActiveResultSets">
      <summary>When true, an application can maintain multiple active result sets (MARS). When false, an application must process or cancel all result sets from one batch before it can execute any other batch on that connection.
For more information, see Multiple Active Result Sets (MARS).</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.MultipleActiveResultSets" /> property, or <see langword="false" /> if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.MultiSubnetFailover">
      <summary>If your application is connecting to an AlwaysOn availability group (AG) on different subnets, setting MultiSubnetFailover=true provides faster detection of and connection to the (currently) active server. For more information about SqlClient support for Always On Availability Groups, see SqlClient Support for High Availability, Disaster Recovery.</summary>
      <returns>Returns <see cref="T:System.Boolean" /> indicating the current value of the property.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.PacketSize">
      <summary>Gets or sets the size in bytes of the network packets used to communicate with an instance of SQL Server.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.PacketSize" /> property, or 8000 if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.Password">
      <summary>Gets or sets the password for the SQL Server account.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.Password" /> property, or <see langword="String.Empty" /> if none has been supplied.</returns>
      <exception cref="T:System.ArgumentNullException">The password was incorrectly set to null.  See code sample below.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.PersistSecurityInfo">
      <summary>Gets or sets a Boolean value that indicates if security-sensitive information, such as the password, is not returned as part of the connection if the connection is open or has ever been in an open state.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.PersistSecurityInfo" /> property, or <see langword="false" /> if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.PoolBlockingPeriod">
      <summary>The blocking period behavior for a connection pool.</summary>
      <returns>The available blocking period settings.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.Pooling">
      <summary>Gets or sets a Boolean value that indicates whether the connection will be pooled or explicitly opened every time that the connection is requested.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.Pooling" /> property, or <see langword="true" /> if none has been supplied.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnectionStringBuilder.Remove(System.String)">
      <summary>Removes the entry with the specified key from the <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" /> instance.</summary>
      <param name="keyword">The key of the key/value pair to be removed from the connection string in this <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" />.</param>
      <returns>
        <see langword="true" /> if the key existed within the connection string and was removed; <see langword="false" /> if the key did not exist.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> is null (<see langword="Nothing" /> in Visual Basic)</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.Replication">
      <summary>Gets or sets a Boolean value that indicates whether replication is supported using the connection.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.Replication" /> property, or false if none has been supplied.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnectionStringBuilder.ShouldSerialize(System.String)">
      <summary>Indicates whether the specified key exists in this <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" /> instance.</summary>
      <param name="keyword">The key to locate in the <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" />.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" /> contains an entry with the specified key; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.TransactionBinding">
      <summary>Gets or sets a string value that indicates how the connection maintains its association with an enlisted <see langword="System.Transactions" /> transaction.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.TransactionBinding" /> property, or <see langword="String.Empty" /> if none has been supplied.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.TrustServerCertificate">
      <summary>Gets or sets a value that indicates whether the channel will be encrypted while bypassing walking the certificate chain to validate trust.</summary>
      <returns>A <see langword="Boolean" />. Recognized values are <see langword="true" />, <see langword="false" />, <see langword="yes" />, and <see langword="no" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlConnectionStringBuilder.TryGetValue(System.String,System.Object@)">
      <summary>Retrieves a value corresponding to the supplied key from this <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" />.</summary>
      <param name="keyword">The key of the item to retrieve.</param>
      <param name="value">The value corresponding to <paramref name="keyword" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="keyword" /> was found within the connection string; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> contains a null value (<see langword="Nothing" /> in Visual Basic).</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.TypeSystemVersion">
      <summary>Gets or sets a string value that indicates the type system the application expects.</summary>
      <returns>The following table shows the possible values for the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.TypeSystemVersion" /> property:
  Value  
  
  Description  
  
  SQL Server 2005  
  
  Uses the SQL Server 2005 type system. No conversions are made for the current version of ADO.NET.  
  
  SQL Server 2008  
  
  Uses the SQL Server 2008 type system.  
  
  Latest  
  
  Use the latest version than this client-server pair can handle. This will automatically move forward as the client and server components are upgraded.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.UserID">
      <summary>Gets or sets the user ID to be used when connecting to SQL Server.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.UserID" /> property, or <see langword="String.Empty" /> if none has been supplied.</returns>
      <exception cref="T:System.ArgumentNullException">To set the value to null, use <see cref="F:System.DBNull.Value" />.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.UserInstance">
      <summary>Gets or sets a value that indicates whether to redirect the connection from the default SQL Server Express instance to a runtime-initiated instance running under the account of the caller.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.UserInstance" /> property, or <see langword="False" /> if none has been supplied.</returns>
      <exception cref="T:System.ArgumentNullException">To set the value to null, use <see cref="F:System.DBNull.Value" />.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.Values">
      <summary>Gets an <see cref="T:System.Collections.ICollection" /> that contains the values in the <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" />.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> that contains the values in the <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlConnectionStringBuilder.WorkstationID">
      <summary>Gets or sets the name of the workstation connecting to SQL Server.</summary>
      <returns>The value of the <see cref="P:System.Data.SqlClient.SqlConnectionStringBuilder.WorkstationID" /> property, or <see langword="String.Empty" /> if none has been supplied.</returns>
      <exception cref="T:System.ArgumentNullException">To set the value to null, use <see cref="F:System.DBNull.Value" />.</exception>
    </member>
    <member name="T:System.Data.SqlClient.SqlCredential">
      <summary>
        <see cref="T:System.Data.SqlClient.SqlCredential" /> provides a more secure way to specify the password for a login attempt using SQL Server Authentication.
<see cref="T:System.Data.SqlClient.SqlCredential" /> is comprised of a user id and a password that will be used for SQL Server Authentication. The password in a <see cref="T:System.Data.SqlClient.SqlCredential" /> object is of type <see cref="T:System.Security.SecureString" />.
<see cref="T:System.Data.SqlClient.SqlCredential" /> cannot be inherited.
Windows Authentication (<see langword="Integrated Security = true" />) remains the most secure way to log in to a SQL Server database.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlCredential.#ctor(System.String,System.Security.SecureString)">
      <summary>Creates an object of type <see cref="T:System.Data.SqlClient.SqlCredential" />.</summary>
      <param name="userId">The user id.</param>
      <param name="password">The password; a <see cref="T:System.Security.SecureString" /> value marked as read-only.  Passing a read/write <see cref="T:System.Security.SecureString" /> parameter will raise an <see cref="T:System.ArgumentException" />.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlCredential.Password">
      <summary>Gets the password component of the <see cref="T:System.Data.SqlClient.SqlCredential" /> object.</summary>
      <returns>The password component of the <see cref="T:System.Data.SqlClient.SqlCredential" /> object.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlCredential.UserId">
      <summary>Gets the user ID component of the <see cref="T:System.Data.SqlClient.SqlCredential" /> object.</summary>
      <returns>The user ID component of the <see cref="T:System.Data.SqlClient.SqlCredential" /> object.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlDataAdapter">
      <summary>Represents a set of data commands and a database connection that are used to fill the <see cref="T:System.Data.DataSet" /> and update a SQL Server database. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataAdapter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlDataAdapter" /> class.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataAdapter.#ctor(System.Data.SqlClient.SqlCommand)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlDataAdapter" /> class with the specified <see cref="T:System.Data.SqlClient.SqlCommand" /> as the <see cref="P:System.Data.SqlClient.SqlDataAdapter.SelectCommand" /> property.</summary>
      <param name="selectCommand">A <see cref="T:System.Data.SqlClient.SqlCommand" /> that is a Transact-SQL SELECT statement or stored procedure and is set as the <see cref="P:System.Data.SqlClient.SqlDataAdapter.SelectCommand" /> property of the <see cref="T:System.Data.SqlClient.SqlDataAdapter" />.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataAdapter.#ctor(System.String,System.Data.SqlClient.SqlConnection)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlDataAdapter" /> class with a <see cref="P:System.Data.SqlClient.SqlDataAdapter.SelectCommand" /> and a <see cref="T:System.Data.SqlClient.SqlConnection" /> object.</summary>
      <param name="selectCommandText">A <see cref="T:System.String" /> that is a Transact-SQL SELECT statement or stored procedure to be used by the <see cref="P:System.Data.SqlClient.SqlDataAdapter.SelectCommand" /> property of the <see cref="T:System.Data.SqlClient.SqlDataAdapter" />.</param>
      <param name="selectConnection">A <see cref="T:System.Data.SqlClient.SqlConnection" /> that represents the connection. If your connection string does not use <see langword="Integrated Security = true" />, you can use <see cref="T:System.Data.SqlClient.SqlCredential" /> to pass the user ID and password more securely than by specifying the user ID and password as text in the connection string.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataAdapter.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlDataAdapter" /> class with a <see cref="P:System.Data.SqlClient.SqlDataAdapter.SelectCommand" /> and a connection string.</summary>
      <param name="selectCommandText">A <see cref="T:System.String" /> that is a Transact-SQL SELECT statement or stored procedure to be used by the <see cref="P:System.Data.SqlClient.SqlDataAdapter.SelectCommand" /> property of the <see cref="T:System.Data.SqlClient.SqlDataAdapter" />.</param>
      <param name="selectConnectionString">The connection string. If your connection string does not use <see langword="Integrated Security = true" />, you can use <see cref="M:System.Data.SqlClient.SqlDataAdapter.#ctor(System.String,System.Data.SqlClient.SqlConnection)" /> and <see cref="T:System.Data.SqlClient.SqlCredential" /> to pass the user ID and password more securely than by specifying the user ID and password as text in the connection string.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataAdapter.DeleteCommand">
      <summary>Gets or sets a Transact-SQL statement or stored procedure to delete records from the data set.</summary>
      <returns>A <see cref="T:System.Data.SqlClient.SqlCommand" /> used during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> to delete records in the database that correspond to deleted rows in the <see cref="T:System.Data.DataSet" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataAdapter.InsertCommand">
      <summary>Gets or sets a Transact-SQL statement or stored procedure to insert new records into the data source.</summary>
      <returns>A <see cref="T:System.Data.SqlClient.SqlCommand" /> used during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> to insert records into the database that correspond to new rows in the <see cref="T:System.Data.DataSet" />.</returns>
    </member>
    <member name="E:System.Data.SqlClient.SqlDataAdapter.RowUpdated">
      <summary>Occurs during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> after a command is executed against the data source. The attempt to update is made, so the event fires.</summary>
    </member>
    <member name="E:System.Data.SqlClient.SqlDataAdapter.RowUpdating">
      <summary>Occurs during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> before a command is executed against the data source. The attempt to update is made, so the event fires.</summary>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataAdapter.SelectCommand">
      <summary>Gets or sets a Transact-SQL statement or stored procedure used to select records in the data source.</summary>
      <returns>A <see cref="T:System.Data.SqlClient.SqlCommand" /> used during <see cref="M:System.Data.Common.DbDataAdapter.Fill(System.Data.DataSet)" /> to select records from the database for placement in the <see cref="T:System.Data.DataSet" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataAdapter.System#Data#IDbDataAdapter#DeleteCommand">
      <summary>For a description of this member, see <see cref="P:System.Data.IDbDataAdapter.DeleteCommand" />.</summary>
      <returns>An <see cref="T:System.Data.IDbCommand" /> that is used during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> to delete records in the data source for deleted rows in the data set.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataAdapter.System#Data#IDbDataAdapter#InsertCommand">
      <summary>For a description of this member, see <see cref="P:System.Data.IDbDataAdapter.InsertCommand" />.</summary>
      <returns>An <see cref="T:System.Data.IDbCommand" /> that is used during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> to insert records in the data source for new rows in the data set.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataAdapter.System#Data#IDbDataAdapter#SelectCommand">
      <summary>For a description of this member, see <see cref="P:System.Data.IDbDataAdapter.SelectCommand" />.</summary>
      <returns>An <see cref="T:System.Data.IDbCommand" /> that is used during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> to select records from data source for placement in the data set.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataAdapter.System#Data#IDbDataAdapter#UpdateCommand">
      <summary>For a description of this member, see <see cref="P:System.Data.IDbDataAdapter.UpdateCommand" />.</summary>
      <returns>An <see cref="T:System.Data.IDbCommand" /> that is used during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> to update records in the data source for modified rows in the data set.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataAdapter.System#ICloneable#Clone">
      <summary>For a description of this member, see <see cref="M:System.ICloneable.Clone" />.</summary>
      <returns>A new object that is a copy of the current instance.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataAdapter.UpdateBatchSize">
      <summary>Gets or sets the number of rows that are processed in each round-trip to the server.</summary>
      <returns>The number of rows to process per-batch.
  Value is  
  
  Effect  
  
  0  
  
  There is no limit on the batch size. 
  
  1  
  
  Disables batch updating.  
  
  &gt;1  
  
  Changes are sent using batches of <see cref="P:System.Data.SqlClient.SqlDataAdapter.UpdateBatchSize" /> operations at a time.  
  
   
When setting this to a value other than 1, all the commands associated with the <see cref="T:System.Data.SqlClient.SqlDataAdapter" /> have to have their UpdatedRowSource property set to <see langword="None" /> or <see langword="OutputParameters" />. An exception is thrown otherwise.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataAdapter.UpdateCommand">
      <summary>Gets or sets a Transact-SQL statement or stored procedure used to update records in the data source.</summary>
      <returns>A <see cref="T:System.Data.SqlClient.SqlCommand" /> used during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> to update records in the database that correspond to modified rows in the <see cref="T:System.Data.DataSet" />.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlDataReader">
      <summary>Provides a way of reading a forward-only stream of rows from a SQL Server database. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataReader.Connection">
      <summary>Gets the <see cref="T:System.Data.SqlClient.SqlConnection" /> associated with the <see cref="T:System.Data.SqlClient.SqlDataReader" />.</summary>
      <returns>The <see cref="T:System.Data.SqlClient.SqlConnection" /> associated with the <see cref="T:System.Data.SqlClient.SqlDataReader" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataReader.Depth">
      <summary>Gets a value that indicates the depth of nesting for the current row.</summary>
      <returns>The depth of nesting for the current row.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataReader.FieldCount">
      <summary>Gets the number of columns in the current row.</summary>
      <returns>When not positioned in a valid recordset, 0; otherwise the number of columns in the current row. The default is -1.</returns>
      <exception cref="T:System.NotSupportedException">There is no current connection to an instance of SQL Server.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetBoolean(System.Int32)">
      <summary>Gets the value of the specified column as a Boolean.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column.</returns>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetByte(System.Int32)">
      <summary>Gets the value of the specified column as a byte.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the specified column as a byte.</returns>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
      <summary>Reads a stream of bytes from the specified column offset into the buffer an array starting at the given buffer offset.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <param name="dataIndex">The index within the field from which to begin the read operation.</param>
      <param name="buffer">The buffer into which to read the stream of bytes.</param>
      <param name="bufferIndex">The index within the <paramref name="buffer" /> where the write operation is to start.</param>
      <param name="length">The maximum length to copy into the buffer.</param>
      <returns>The actual number of bytes read.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetChar(System.Int32)">
      <summary>Gets the value of the specified column as a single character.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the specified column.</returns>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
      <summary>Reads a stream of characters from the specified column offset into the buffer as an array starting at the given buffer offset.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <param name="dataIndex">The index within the field from which to begin the read operation.</param>
      <param name="buffer">The buffer into which to read the stream of bytes.</param>
      <param name="bufferIndex">The index within the <paramref name="buffer" /> where the write operation is to start.</param>
      <param name="length">The maximum length to copy into the buffer.</param>
      <returns>The actual number of characters read.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetColumnSchema" />
    <member name="M:System.Data.SqlClient.SqlDataReader.GetDataTypeName(System.Int32)">
      <summary>Gets a string representing the data type of the specified column.</summary>
      <param name="i">The zero-based ordinal position of the column to find.</param>
      <returns>The string representing the data type of the specified column.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetDateTime(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.DateTime" /> object.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the specified column.</returns>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetDateTimeOffset(System.Int32)">
      <summary>Retrieves the value of the specified column as a <see cref="T:System.DateTimeOffset" /> object.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the specified column.</returns>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetDecimal(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.Decimal" /> object.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the specified column.</returns>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetDouble(System.Int32)">
      <summary>Gets the value of the specified column as a double-precision floating point number.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the specified column.</returns>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IEnumerator" /> that iterates through the <see cref="T:System.Data.SqlClient.SqlDataReader" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Data.SqlClient.SqlDataReader" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetFieldType(System.Int32)">
      <summary>Gets the <see cref="T:System.Type" /> that is the data type of the object.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The <see cref="T:System.Type" /> that is the data type of the object. If the type does not exist on the client, in the case of a User-Defined Type (UDT) returned from the database, GetFieldType returns null.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetFieldValue``1(System.Int32)">
      <summary>Synchronously gets the value of the specified column as a type. <see cref="M:System.Data.SqlClient.SqlDataReader.GetFieldValueAsync``1(System.Int32,System.Threading.CancellationToken)" /> is the asynchronous version of this method.</summary>
      <param name="i">The column to be retrieved.</param>
      <typeparam name="T">The type of the value to be returned.</typeparam>
      <returns>The returned type object.</returns>
      <exception cref="T:System.InvalidOperationException">The connection drops or is closed during the data retrieval.
The <see cref="T:System.Data.SqlClient.SqlDataReader" /> is closed during the data retrieval.
There is no data ready to be read (for example, the first <see cref="M:System.Data.SqlClient.SqlDataReader.Read" /> hasn't been called, or returned false).
Tried to read a previously-read column in sequential mode.
There was an asynchronous operation in progress. This applies to all Get* methods when running in sequential mode, as they could be called while reading a stream.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Trying to read a column that does not exist.</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The value of the column was null (<see cref="M:System.Data.SqlClient.SqlDataReader.IsDBNull(System.Int32)" /> == <see langword="true" />), retrieving a non-SQL type.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> doesn't match the type returned by SQL Server or cannot be cast.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetFieldValueAsync``1(System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously gets the value of the specified column as a type. <see cref="M:System.Data.SqlClient.SqlDataReader.GetFieldValue``1(System.Int32)" /> is the synchronous version of this method.</summary>
      <param name="i">The column to be retrieved.</param>
      <param name="cancellationToken">The cancellation instruction, which propagates a notification that operations should be canceled. This does not guarantee the cancellation. A setting of <see langword="CancellationToken.None" /> makes this method equivalent to <see cref="M:System.Data.SqlClient.SqlDataReader.IsDBNull(System.Int32)" />. The returned task must be marked as cancelled.</param>
      <typeparam name="T">The type of the value to be returned.</typeparam>
      <returns>The returned type object.</returns>
      <exception cref="T:System.InvalidOperationException">The connection drops or is closed during the data retrieval.
The <see cref="T:System.Data.SqlClient.SqlDataReader" /> is closed during the data retrieval.
There is no data ready to be read (for example, the first <see cref="M:System.Data.SqlClient.SqlDataReader.Read" /> hasn't been called, or returned false).
Tried to read a previously-read column in sequential mode.
There was an asynchronous operation in progress. This applies to all Get* methods when running in sequential mode, as they could be called while reading a stream.
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Trying to read a column that does not exist.</exception>
      <exception cref="T:System.Data.SqlTypes.SqlNullValueException">The value of the column was null (<see cref="M:System.Data.SqlClient.SqlDataReader.IsDBNull(System.Int32)" /> == <see langword="true" />), retrieving a non-SQL type.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> doesn't match the type returned by SQL Server or cannot be cast.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetFloat(System.Int32)">
      <summary>Gets the value of the specified column as a single-precision floating point number.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the specified column.</returns>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetGuid(System.Int32)">
      <summary>Gets the value of the specified column as a globally unique identifier (GUID).</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the specified column.</returns>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetInt16(System.Int32)">
      <summary>Gets the value of the specified column as a 16-bit signed integer.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the specified column.</returns>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetInt32(System.Int32)">
      <summary>Gets the value of the specified column as a 32-bit signed integer.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the specified column.</returns>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetInt64(System.Int32)">
      <summary>Gets the value of the specified column as a 64-bit signed integer.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the specified column.</returns>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetName(System.Int32)">
      <summary>Gets the name of the specified column.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The name of the specified column.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetOrdinal(System.String)">
      <summary>Gets the column ordinal, given the name of the column.</summary>
      <param name="name">The name of the column.</param>
      <returns>The zero-based column ordinal.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The name specified is not a valid column name.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetProviderSpecificFieldType(System.Int32)">
      <summary>Gets an <see langword="Object" /> that is a representation of the underlying provider-specific field type.</summary>
      <param name="i">An <see cref="T:System.Int32" /> representing the column ordinal.</param>
      <returns>Gets an <see cref="T:System.Object" /> that is a representation of the underlying provider-specific field type.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetProviderSpecificValue(System.Int32)">
      <summary>Gets an <see langword="Object" /> that is a representation of the underlying provider specific value.</summary>
      <param name="i">An <see cref="T:System.Int32" /> representing the column ordinal.</param>
      <returns>An <see cref="T:System.Object" /> that is a representation of the underlying provider specific value.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetProviderSpecificValues(System.Object[])">
      <summary>Gets an array of objects that are a representation of the underlying provider specific values.</summary>
      <param name="values">An array of <see cref="T:System.Object" /> into which to copy the column values.</param>
      <returns>The array of objects that are a representation of the underlying provider specific values.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSchemaTable">
      <summary>Returns a <see cref="T:System.Data.DataTable" /> that describes the column metadata of the <see cref="T:System.Data.SqlClient.SqlDataReader" />.</summary>
      <returns>A <see cref="T:System.Data.DataTable" /> that describes the column metadata.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Data.SqlClient.SqlDataReader" /> is closed.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlBinary(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.Data.SqlTypes.SqlBinary" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a <see cref="T:System.Data.SqlTypes.SqlBinary" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlBoolean(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.Data.SqlTypes.SqlBoolean" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlByte(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.Data.SqlTypes.SqlByte" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a  <see cref="T:System.Data.SqlTypes.SqlByte" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlBytes(System.Int32)">
      <summary>Gets the value of the specified column as <see cref="T:System.Data.SqlTypes.SqlBytes" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a <see cref="T:System.Data.SqlTypes.SqlBytes" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlChars(System.Int32)">
      <summary>Gets the value of the specified column as <see cref="T:System.Data.SqlTypes.SqlChars" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a  <see cref="T:System.Data.SqlTypes.SqlChars" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlDateTime(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.Data.SqlTypes.SqlDateTime" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a  <see cref="T:System.Data.SqlTypes.SqlDateTime" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlDecimal(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.Data.SqlTypes.SqlDecimal" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a <see cref="T:System.Data.SqlTypes.SqlDecimal" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlDouble(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.Data.SqlTypes.SqlDouble" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a  <see cref="T:System.Data.SqlTypes.SqlDouble" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlGuid(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.Data.SqlTypes.SqlGuid" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a  <see cref="T:System.Data.SqlTypes.SqlGuid" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlInt16(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.Data.SqlTypes.SqlInt16" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a <see cref="T:System.Data.SqlTypes.SqlInt16" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlInt32(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.Data.SqlTypes.SqlInt32" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a <see cref="T:System.Data.SqlTypes.SqlInt32" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlInt64(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.Data.SqlTypes.SqlInt64" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a <see cref="T:System.Data.SqlTypes.SqlInt64" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlMoney(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.Data.SqlTypes.SqlMoney" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a <see cref="T:System.Data.SqlTypes.SqlMoney" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlSingle(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.Data.SqlTypes.SqlSingle" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a <see cref="T:System.Data.SqlTypes.SqlSingle" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlString(System.Int32)">
      <summary>Gets the value of the specified column as a <see cref="T:System.Data.SqlTypes.SqlString" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a <see cref="T:System.Data.SqlTypes.SqlString" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlValue(System.Int32)">
      <summary>Returns the data value in the specified column as a SQL Server type.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the column expressed as a <see cref="T:System.Data.SqlDbType" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlValues(System.Object[])">
      <summary>Fills an array of <see cref="T:System.Object" /> that contains the values for all the columns in the record, expressed as SQL Server types.</summary>
      <param name="values">An array of <see cref="T:System.Object" /> into which to copy the values. The column values are expressed as SQL Server types.</param>
      <returns>An integer indicating the number of columns copied.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetSqlXml(System.Int32)">
      <summary>Gets the value of the specified column as an XML value.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>A <see cref="T:System.Data.SqlTypes.SqlXml" /> value that contains the XML stored within the corresponding field.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The index passed was outside the range of 0 to <see cref="P:System.Data.DataTableReader.FieldCount" /> - 1</exception>
      <exception cref="T:System.InvalidOperationException">An attempt was made to read or access columns in a closed <see cref="T:System.Data.SqlClient.SqlDataReader" />.</exception>
      <exception cref="T:System.InvalidCastException">The retrieved data is not compatible with the <see cref="T:System.Data.SqlTypes.SqlXml" /> type.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetStream(System.Int32)">
      <summary>Retrieves binary, image, varbinary, UDT, and variant data types as a <see cref="T:System.IO.Stream" />.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>A stream object.</returns>
      <exception cref="T:System.InvalidOperationException">The connection drops or is closed during the data retrieval.
The <see cref="T:System.Data.SqlClient.SqlDataReader" /> is closed during the data retrieval.
There is no data ready to be read (for example, the first <see cref="M:System.Data.SqlClient.SqlDataReader.Read" /> hasn't been called, or returned false).
Tried to read a previously-read column in sequential mode.
There was an asynchronous operation in progress. This applies to all Get* methods when running in sequential mode, as they could be called while reading a stream.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Trying to read a column that does not exist.</exception>
      <exception cref="T:System.InvalidCastException">The returned type was not one of the types below:

binary

image

varbinary

udt</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetString(System.Int32)">
      <summary>Gets the value of the specified column as a string.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the specified column.</returns>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetTextReader(System.Int32)">
      <summary>Retrieves Char, NChar, NText, NVarChar, text, varChar, and Variant data types as a <see cref="T:System.IO.TextReader" />.</summary>
      <param name="i">The column to be retrieved.</param>
      <returns>The returned object.</returns>
      <exception cref="T:System.InvalidOperationException">The connection drops or is closed during the data retrieval.
The <see cref="T:System.Data.SqlClient.SqlDataReader" /> is closed during the data retrieval.
There is no data ready to be read (for example, the first <see cref="M:System.Data.SqlClient.SqlDataReader.Read" /> hasn't been called, or returned false).
Tried to read a previously-read column in sequential mode.
There was an asynchronous operation in progress. This applies to all Get* methods when running in sequential mode, as they could be called while reading a stream.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Trying to read a column that does not exist.</exception>
      <exception cref="T:System.InvalidCastException">The returned type was not one of the types below:

char

nchar

ntext

nvarchar

text

varchar</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetTimeSpan(System.Int32)">
      <summary>Retrieves the value of the specified column as a <see cref="T:System.TimeSpan" /> object.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the specified column.</returns>
      <exception cref="T:System.InvalidCastException">The specified cast is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetValue(System.Int32)">
      <summary>Gets the value of the specified column in its native format.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>This method returns <see cref="T:System.DBNull" /> for null database columns.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetValues(System.Object[])">
      <summary>Populates an array of objects with the column values of the current row.</summary>
      <param name="values">An array of <see cref="T:System.Object" /> into which to copy the attribute columns.</param>
      <returns>The number of instances of <see cref="T:System.Object" /> in the array.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.GetXmlReader(System.Int32)">
      <summary>Retrieves data of type XML as an <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="i">The value of the specified column.</param>
      <returns>The returned object.</returns>
      <exception cref="T:System.InvalidOperationException">The connection drops or is closed during the data retrieval.
The <see cref="T:System.Data.SqlClient.SqlDataReader" /> is closed during the data retrieval.
There is no data ready to be read (for example, the first <see cref="M:System.Data.SqlClient.SqlDataReader.Read" /> hasn't been called, or returned false).
Trying to read a previously read column in sequential mode.
There was an asynchronous operation in progress. This applies to all Get* methods when running in sequential mode, as they could be called while reading a stream.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Trying to read a column that does not exist.</exception>
      <exception cref="T:System.InvalidCastException">The returned type was not xml.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataReader.HasRows">
      <summary>Gets a value that indicates whether the <see cref="T:System.Data.SqlClient.SqlDataReader" /> contains one or more rows.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Data.SqlClient.SqlDataReader" /> contains one or more rows; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataReader.IsClosed">
      <summary>Retrieves a Boolean value that indicates whether the specified <see cref="T:System.Data.SqlClient.SqlDataReader" /> instance has been closed.</summary>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Data.SqlClient.SqlDataReader" /> instance is closed; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.IsCommandBehavior(System.Data.CommandBehavior)">
      <summary>Determines whether the specified <see cref="T:System.Data.CommandBehavior" /> matches that of the <see cref="T:System.Data.SqlClient.SqlDataReader" /> .</summary>
      <param name="condition">A <see cref="T:System.Data.CommandBehavior" /> enumeration.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Data.CommandBehavior" /> is true, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.IsDBNull(System.Int32)">
      <summary>Gets a value that indicates whether the column contains non-existent or missing values.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>
        <see langword="true" /> if the specified column value is equivalent to <see cref="T:System.DBNull" />; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.IsDBNullAsync(System.Int32,System.Threading.CancellationToken)">
      <summary>An asynchronous version of <see cref="M:System.Data.SqlClient.SqlDataReader.IsDBNull(System.Int32)" />, which gets a value that indicates whether the column contains non-existent or missing values.
The cancellation token can be used to request that the operation be abandoned before the command timeout elapses. Exceptions will be reported via the returned Task object.</summary>
      <param name="i">The zero-based column to be retrieved.</param>
      <param name="cancellationToken">The cancellation instruction, which propagates a notification that operations should be canceled. This does not guarantee the cancellation. A setting of <see langword="CancellationToken.None" /> makes this method equivalent to <see cref="M:System.Data.SqlClient.SqlDataReader.IsDBNull(System.Int32)" />. The returned task must be marked as cancelled.</param>
      <returns>
        <see langword="true" /> if the specified column value is equivalent to <see langword="DBNull" /> otherwise <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The connection drops or is closed during the data retrieval.
The <see cref="T:System.Data.SqlClient.SqlDataReader" /> is closed during the data retrieval.
There is no data ready to be read (for example, the first <see cref="M:System.Data.SqlClient.SqlDataReader.Read" /> hasn't been called, or returned false).
Trying to read a previously read column in sequential mode.
There was an asynchronous operation in progress. This applies to all Get* methods when running in sequential mode, as they could be called while reading a stream.
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Trying to read a column that does not exist.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataReader.Item(System.Int32)">
      <summary>Gets the value of the specified column in its native format given the column ordinal.</summary>
      <param name="i">The zero-based column ordinal.</param>
      <returns>The value of the specified column in its native format.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount" />.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataReader.Item(System.String)">
      <summary>Gets the value of the specified column in its native format given the column name.</summary>
      <param name="name">The column name.</param>
      <returns>The value of the specified column in its native format.</returns>
      <exception cref="T:System.IndexOutOfRangeException">No column with the specified name was found.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.NextResult">
      <summary>Advances the data reader to the next result, when reading the results of batch Transact-SQL statements.</summary>
      <returns>
        <see langword="true" /> if there are more result sets; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.NextResultAsync(System.Threading.CancellationToken)">
      <summary>An asynchronous version of <see cref="M:System.Data.SqlClient.SqlDataReader.NextResult" />, which advances the data reader to the next result, when reading the results of batch Transact-SQL statements.
The cancellation token can be used to request that the operation be abandoned before the command timeout elapses.  Exceptions will be reported via the returned Task object.</summary>
      <param name="cancellationToken">The cancellation instruction.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlDataReader.NextResultAsync(System.Threading.CancellationToken)" /> more than once for the same instance before task completion.
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">SQL Server returned an error while executing the command text.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.Read">
      <summary>Advances the <see cref="T:System.Data.SqlClient.SqlDataReader" /> to the next record.</summary>
      <returns>
        <see langword="true" /> if there are more rows; otherwise <see langword="false" />.</returns>
      <exception cref="T:System.Data.SqlClient.SqlException">SQL Server returned an error while executing the command text.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDataReader.ReadAsync(System.Threading.CancellationToken)">
      <summary>An asynchronous version of <see cref="M:System.Data.SqlClient.SqlDataReader.Read" />, which advances the <see cref="T:System.Data.SqlClient.SqlDataReader" /> to the next record.
The cancellation token can be used to request that the operation be abandoned before the command timeout elapses. Exceptions will be reported via the returned Task object.</summary>
      <param name="cancellationToken">The cancellation instruction.</param>
      <returns>A task representing the asynchronous operation.</returns>
      <exception cref="T:System.InvalidOperationException">Calling <see cref="M:System.Data.SqlClient.SqlDataReader.ReadAsync(System.Threading.CancellationToken)" /> more than once for the same instance before task completion.
<see langword="Context Connection=true" /> is specified in the connection string.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">SQL Server returned an error while executing the command text.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataReader.RecordsAffected">
      <summary>Gets the number of rows changed, inserted, or deleted by execution of the Transact-SQL statement.</summary>
      <returns>The number of rows changed, inserted, or deleted; 0 if no rows were affected or the statement failed; and -1 for SELECT statements.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlDataReader.VisibleFieldCount">
      <summary>Gets the number of fields in the <see cref="T:System.Data.SqlClient.SqlDataReader" /> that are not hidden.</summary>
      <returns>The number of fields that are not hidden.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlDependency">
      <summary>The <see cref="T:System.Data.SqlClient.SqlDependency" /> object represents a query notification dependency between an application and an instance of SQL Server. An application can create a <see cref="T:System.Data.SqlClient.SqlDependency" /> object and register to receive notifications via the <see cref="T:System.Data.SqlClient.OnChangeEventHandler" /> event handler.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlDependency.#ctor">
      <summary>Creates a new instance of the <see cref="T:System.Data.SqlClient.SqlDependency" /> class with the default settings.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlDependency.#ctor(System.Data.SqlClient.SqlCommand)">
      <summary>Creates a new instance of the <see cref="T:System.Data.SqlClient.SqlDependency" /> class and associates it with the <see cref="T:System.Data.SqlClient.SqlCommand" /> parameter.</summary>
      <param name="command">The <see cref="T:System.Data.SqlClient.SqlCommand" /> object to associate with this <see cref="T:System.Data.SqlClient.SqlDependency" /> object. The constructor will set up a <see cref="T:System.Data.Sql.SqlNotificationRequest" /> object and bind it to the command.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="command" /> parameter is NULL.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Data.SqlClient.SqlCommand" /> object already has a <see cref="T:System.Data.Sql.SqlNotificationRequest" /> object assigned to its <see cref="P:System.Data.SqlClient.SqlCommand.Notification" /> property, and that <see cref="T:System.Data.Sql.SqlNotificationRequest" /> is not associated with this dependency.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDependency.#ctor(System.Data.SqlClient.SqlCommand,System.String,System.Int32)">
      <summary>Creates a new instance of the <see cref="T:System.Data.SqlClient.SqlDependency" /> class, associates it with the <see cref="T:System.Data.SqlClient.SqlCommand" /> parameter, and specifies notification options and a time-out value.</summary>
      <param name="command">The <see cref="T:System.Data.SqlClient.SqlCommand" /> object to associate with this <see cref="T:System.Data.SqlClient.SqlDependency" /> object. The constructor sets up a <see cref="T:System.Data.Sql.SqlNotificationRequest" /> object and bind it to the command.</param>
      <param name="options">The notification request options to be used by this dependency. <see langword="null" /> to use the default service.</param>
      <param name="timeout">The time-out for this notification in seconds. The default is 0, indicating that the server's time-out should be used.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="command" /> parameter is NULL.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The time-out value is less than zero.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Data.SqlClient.SqlCommand" /> object already has a <see cref="T:System.Data.Sql.SqlNotificationRequest" /> object assigned to its <see cref="P:System.Data.SqlClient.SqlCommand.Notification" /> property and that <see cref="T:System.Data.Sql.SqlNotificationRequest" /> is not associated with this dependency.
An attempt was made to create a SqlDependency instance from within SQLCLR.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDependency.AddCommandDependency(System.Data.SqlClient.SqlCommand)">
      <summary>Associates a <see cref="T:System.Data.SqlClient.SqlCommand" /> object with this <see cref="T:System.Data.SqlClient.SqlDependency" /> instance.</summary>
      <param name="command">A <see cref="T:System.Data.SqlClient.SqlCommand" /> object containing a statement that is valid for notifications.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="command" /> parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Data.SqlClient.SqlCommand" /> object already has a <see cref="T:System.Data.Sql.SqlNotificationRequest" /> object assigned to its <see cref="P:System.Data.SqlClient.SqlCommand.Notification" /> property, and that <see cref="T:System.Data.Sql.SqlNotificationRequest" /> is not associated with this dependency.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlDependency.HasChanges">
      <summary>Gets a value that indicates whether one of the result sets associated with the dependency has changed.</summary>
      <returns>A Boolean value indicating whether one of the result sets has changed.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlDependency.Id">
      <summary>Gets a value that uniquely identifies this instance of the <see cref="T:System.Data.SqlClient.SqlDependency" /> class.</summary>
      <returns>A string representation of a GUID that is generated for each instance of the <see cref="T:System.Data.SqlClient.SqlDependency" /> class.</returns>
    </member>
    <member name="E:System.Data.SqlClient.SqlDependency.OnChange">
      <summary>Occurs when a notification is received for any of the commands associated with this <see cref="T:System.Data.SqlClient.SqlDependency" /> object.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlDependency.Start(System.String)">
      <summary>Starts the listener for receiving dependency change notifications from the instance of SQL Server specified by the connection string.</summary>
      <param name="connectionString">The connection string for the instance of SQL Server from which to obtain change notifications.</param>
      <returns>
        <see langword="true" /> if the listener initialized successfully; <see langword="false" /> if a compatible listener already exists.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="connectionString" /> parameter is NULL.</exception>
      <exception cref="T:System.InvalidOperationException">The <paramref name="connectionString" /> parameter is the same as a previous call to this method, but the parameters are different.
The method was called from within the CLR.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required <see cref="T:System.Data.SqlClient.SqlClientPermission" /> code access security (CAS) permission.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">A subsequent call to the method has been made with an equivalent <paramref name="connectionString" /> parameter with a different user, or a user that does not default to the same schema.
Also, any underlying SqlClient exceptions.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDependency.Start(System.String,System.String)">
      <summary>Starts the listener for receiving dependency change notifications from the instance of SQL Server specified by the connection string using the specified SQL Server Service Broker queue.</summary>
      <param name="connectionString">The connection string for the instance of SQL Server from which to obtain change notifications.</param>
      <param name="queue">An existing SQL Server Service Broker queue to be used. If <see langword="null" />, the default queue is used.</param>
      <returns>
        <see langword="true" /> if the listener initialized successfully; <see langword="false" /> if a compatible listener already exists.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="connectionString" /> parameter is NULL.</exception>
      <exception cref="T:System.InvalidOperationException">The <paramref name="connectionString" /> parameter is the same as a previous call to this method, but the parameters are different.
The method was called from within the CLR.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required <see cref="T:System.Data.SqlClient.SqlClientPermission" /> code access security (CAS) permission.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">A subsequent call to the method has been made with an equivalent <paramref name="connectionString" /> parameter but a different user, or a user that does not default to the same schema.
Also, any underlying SqlClient exceptions.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDependency.Stop(System.String)">
      <summary>Stops a listener for a connection specified in a previous <see cref="Overload:System.Data.SqlClient.SqlDependency.Start" /> call.</summary>
      <param name="connectionString">Connection string for the instance of SQL Server that was used in a previous <see cref="M:System.Data.SqlClient.SqlDependency.Start(System.String)" /> call.</param>
      <returns>
        <see langword="true" /> if the listener was completely stopped; <see langword="false" /> if the <see cref="T:System.AppDomain" /> was unbound from the listener, but there are is at least one other <see cref="T:System.AppDomain" /> using the same listener.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="connectionString" /> parameter is NULL.</exception>
      <exception cref="T:System.InvalidOperationException">The method was called from within SQLCLR.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required <see cref="T:System.Data.SqlClient.SqlClientPermission" /> code access security (CAS) permission.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">An underlying SqlClient exception occurred.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlDependency.Stop(System.String,System.String)">
      <summary>Stops a listener for a connection specified in a previous <see cref="Overload:System.Data.SqlClient.SqlDependency.Start" /> call.</summary>
      <param name="connectionString">Connection string for the instance of SQL Server that was used in a previous <see cref="M:System.Data.SqlClient.SqlDependency.Start(System.String,System.String)" /> call.</param>
      <param name="queue">The SQL Server Service Broker queue that was used in a previous <see cref="M:System.Data.SqlClient.SqlDependency.Start(System.String,System.String)" /> call.</param>
      <returns>
        <see langword="true" /> if the listener was completely stopped; <see langword="false" /> if the <see cref="T:System.AppDomain" /> was unbound from the listener, but there is at least one other <see cref="T:System.AppDomain" /> using the same listener.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="connectionString" /> parameter is NULL.</exception>
      <exception cref="T:System.InvalidOperationException">The method was called from within SQLCLR.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required <see cref="T:System.Data.SqlClient.SqlClientPermission" /> code access security (CAS) permission.</exception>
      <exception cref="T:System.Data.SqlClient.SqlException">And underlying SqlClient exception occurred.</exception>
    </member>
    <member name="T:System.Data.SqlClient.SqlError">
      <summary>Collects information relevant to a warning or error returned by SQL Server.</summary>
    </member>
    <member name="P:System.Data.SqlClient.SqlError.Class">
      <summary>Gets the severity level of the error returned from SQL Server.</summary>
      <returns>A value from 1 to 25 that indicates the severity level of the error. The default is 0.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlError.LineNumber">
      <summary>Gets the line number within the Transact-SQL command batch or stored procedure that contains the error.</summary>
      <returns>The line number within the Transact-SQL command batch or stored procedure that contains the error.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlError.Message">
      <summary>Gets the text describing the error.</summary>
      <returns>The text describing the error. For more information on errors generated by SQL Server, see Database Engine Events and Errors.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlError.Number">
      <summary>Gets a number that identifies the type of error.</summary>
      <returns>The number that identifies the type of error.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlError.Procedure">
      <summary>Gets the name of the stored procedure or remote procedure call (RPC) that generated the error.</summary>
      <returns>The name of the stored procedure or RPC. For more information on errors generated by SQL Server, see Database Engine Events and Errors.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlError.Server">
      <summary>Gets the name of the instance of SQL Server that generated the error.</summary>
      <returns>The name of the instance of SQL Server.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlError.Source">
      <summary>Gets the name of the provider that generated the error.</summary>
      <returns>The name of the provider that generated the error.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlError.State">
      <summary>Some error messages can be raised at multiple points in the code for the Database Engine. For example, an 1105 error can be raised for several different conditions. Each specific condition that raises an error assigns a unique state code.</summary>
      <returns>The state code.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlError.ToString">
      <summary>Gets the complete text of the error message.</summary>
      <returns>The complete text of the error.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlErrorCollection">
      <summary>Collects all errors generated by the .NET Framework Data Provider for SQL Server. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlErrorCollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Data.SqlClient.SqlErrorCollection" /> collection into an <see cref="T:System.Array" />, starting at the specified index.</summary>
      <param name="array">The <see cref="T:System.Array" /> to copy elements into.</param>
      <param name="index">The index from which to start copying into the <paramref name="array" /> parameter.</param>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="index" /> and the number of elements in the <see cref="T:System.Data.SqlClient.SqlErrorCollection" /> collection is greater than the <see cref="P:System.Array.Length" /> of the <see cref="T:System.Array" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> is not valid for <paramref name="array" />.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlErrorCollection.CopyTo(System.Data.SqlClient.SqlError[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Data.SqlClient.SqlErrorCollection" /> collection into a <see cref="T:System.Data.SqlClient.SqlErrorCollection" />, starting at the specified index.</summary>
      <param name="array">The <see cref="T:System.Data.SqlClient.SqlErrorCollection" /> to copy the elements into.</param>
      <param name="index">The index from which to start copying into the <paramref name="array" /> parameter.</param>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="index" /> and the number of elements in the <see cref="T:System.Data.SqlClient.SqlErrorCollection" /> collection is greater than the length of the <see cref="T:System.Data.SqlClient.SqlErrorCollection" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> is not valid for <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlErrorCollection.Count">
      <summary>Gets the number of errors in the collection.</summary>
      <returns>The total number of errors in the collection.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlErrorCollection.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Data.SqlClient.SqlErrorCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Data.SqlClient.SqlErrorCollection" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlErrorCollection.Item(System.Int32)">
      <summary>Gets the error at the specified index.</summary>
      <param name="index">The zero-based index of the error to retrieve.</param>
      <returns>A <see cref="T:System.Data.SqlClient.SqlError" /> that contains the error at the specified index.</returns>
      <exception cref="T:System.IndexOutOfRangeException">Index parameter is outside array bounds.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlErrorCollection.System#Collections#ICollection#IsSynchronized">
      <summary>For a description of this member, see <see cref="P:System.Collections.ICollection.IsSynchronized" />.</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlErrorCollection.System#Collections#ICollection#SyncRoot">
      <summary>For a description of this member, see <see cref="P:System.Collections.ICollection.SyncRoot" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlException">
      <summary>The exception that is thrown when SQL Server returns a warning or error. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Data.SqlClient.SqlException.Class">
      <summary>Gets the severity level of the error returned from the .NET Framework Data Provider for SQL Server.</summary>
      <returns>A value from 1 to 25 that indicates the severity level of the error.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlException.ClientConnectionId">
      <summary>Represents the client connection ID. For more information, see Data Tracing in ADO.NET.</summary>
      <returns>The client connection ID.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlException.Errors">
      <summary>Gets a collection of one or more <see cref="T:System.Data.SqlClient.SqlError" /> objects that give detailed information about exceptions generated by the .NET Framework Data Provider for SQL Server.</summary>
      <returns>The collected instances of the <see cref="T:System.Data.SqlClient.SqlError" /> class.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with information about the exception.</summary>
      <param name="si">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="si" /> parameter is a null reference (<see langword="Nothing" /> in Visual Basic).</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlException.LineNumber">
      <summary>Gets the line number within the Transact-SQL command batch or stored procedure that generated the error.</summary>
      <returns>The line number within the Transact-SQL command batch or stored procedure that generated the error.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlException.Number">
      <summary>Gets a number that identifies the type of error.</summary>
      <returns>The number that identifies the type of error.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlException.Procedure">
      <summary>Gets the name of the stored procedure or remote procedure call (RPC) that generated the error.</summary>
      <returns>The name of the stored procedure or RPC.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlException.Server">
      <summary>Gets the name of the computer that is running an instance of SQL Server that generated the error.</summary>
      <returns>The name of the computer running an instance of SQL Server.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlException.Source">
      <summary>Gets the name of the provider that generated the error.</summary>
      <returns>The name of the provider that generated the error.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlException.State">
      <summary>Gets a numeric error code from SQL Server that represents an error, warning or "no data found" message. For more information about how to decode these values, see Database Engine Events and Errors.</summary>
      <returns>The number representing the error code.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlException.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Data.SqlClient.SqlException" /> object, and includes the client connection ID (for more information, see <see cref="P:System.Data.SqlClient.SqlException.ClientConnectionId" />).</summary>
      <returns>A string that represents the current <see cref="T:System.Data.SqlClient.SqlException" /> object.<see cref="T:System.String" />.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlInfoMessageEventArgs">
      <summary>Provides data for the <see cref="E:System.Data.SqlClient.SqlConnection.InfoMessage" /> event.</summary>
    </member>
    <member name="P:System.Data.SqlClient.SqlInfoMessageEventArgs.Errors">
      <summary>Gets the collection of warnings sent from the server.</summary>
      <returns>The collection of warnings sent from the server.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlInfoMessageEventArgs.Message">
      <summary>Gets the full text of the error sent from the database.</summary>
      <returns>The full text of the error.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlInfoMessageEventArgs.Source">
      <summary>Gets the name of the object that generated the error.</summary>
      <returns>The name of the object that generated the error.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlInfoMessageEventArgs.ToString">
      <summary>Retrieves a string representation of the <see cref="E:System.Data.SqlClient.SqlConnection.InfoMessage" /> event.</summary>
      <returns>A string representing the <see cref="E:System.Data.SqlClient.SqlConnection.InfoMessage" /> event.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlInfoMessageEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.Data.SqlClient.SqlConnection.InfoMessage" /> event of a <see cref="T:System.Data.SqlClient.SqlConnection" />.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:System.Data.SqlClient.SqlInfoMessageEventArgs" /> object that contains the event data.</param>
    </member>
    <member name="T:System.Data.SqlClient.SqlNotificationEventArgs">
      <summary>Represents the set of arguments passed to the notification event handler.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlNotificationEventArgs.#ctor(System.Data.SqlClient.SqlNotificationType,System.Data.SqlClient.SqlNotificationInfo,System.Data.SqlClient.SqlNotificationSource)">
      <summary>Creates a new instance of the <see cref="T:System.Data.SqlClient.SqlNotificationEventArgs" /> object.</summary>
      <param name="type">
        <see cref="T:System.Data.SqlClient.SqlNotificationType" /> value that indicates whether this notification is generated because of an actual change, or by the subscription.</param>
      <param name="info">
        <see cref="T:System.Data.SqlClient.SqlNotificationInfo" /> value that indicates the reason for the notification event. This may occur because the data in the store actually changed, or the notification became invalid (for example, it timed out).</param>
      <param name="source">
        <see cref="T:System.Data.SqlClient.SqlNotificationSource" /> value that indicates the source that generated the notification.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlNotificationEventArgs.Info">
      <summary>Gets a value that indicates the reason for the notification event, such as a row in the database being modified or a table being truncated.</summary>
      <returns>The notification event reason.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlNotificationEventArgs.Source">
      <summary>Gets a value that indicates the source that generated the notification, such as a change to the query data or the database's state.</summary>
      <returns>The source of the notification.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlNotificationEventArgs.Type">
      <summary>Gets a value that indicates whether this notification is generated because of an actual change, or by the subscription.</summary>
      <returns>A value indicating whether the notification was generated by a change or a subscription.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlNotificationInfo">
      <summary>This enumeration provides additional information about the different notifications that can be received by the dependency event handler.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.AlreadyChanged">
      <summary>The <see langword="SqlDependency" /> object already fired, and new commands cannot be added to it.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Alter">
      <summary>An underlying server object related to the query was modified.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Delete">
      <summary>Data was changed by a DELETE statement.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Drop">
      <summary>An underlying object related to the query was dropped.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Error">
      <summary>An internal server error occurred.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Expired">
      <summary>The <see langword="SqlDependency" /> object has expired.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Insert">
      <summary>Data was changed by an INSERT statement.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Invalid">
      <summary>A statement was provided that cannot be notified (for example, an UPDATE statement).</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Isolation">
      <summary>The statement was executed under an isolation mode that was not valid (for example, Snapshot).</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Merge">
      <summary>Used to distinguish the server-side cause for a query notification firing.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Options">
      <summary>The SET options were not set appropriately at subscription time.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.PreviousFire">
      <summary>A previous statement has caused query notifications to fire under the current transaction.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Query">
      <summary>A SELECT statement that cannot be notified or was provided.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Resource">
      <summary>Fires as a result of server resource pressure.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Restart">
      <summary>The server was restarted (notifications are sent during restart.).</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.TemplateLimit">
      <summary>The subscribing query causes the number of templates on one of the target tables to exceed the maximum allowable limit.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Truncate">
      <summary>One or more tables were truncated.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Unknown">
      <summary>Used when the info option sent by the server was not recognized by the client.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationInfo.Update">
      <summary>Data was changed by an UPDATE statement.</summary>
    </member>
    <member name="T:System.Data.SqlClient.SqlNotificationSource">
      <summary>Indicates the source of the notification received by the dependency event handler.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationSource.Client">
      <summary>A client-initiated notification occurred, such as a client-side time-out or as a result of attempting to add a command to a dependency that has already fired.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationSource.Data">
      <summary>Data has changed; for example, an insert, update, delete, or truncate operation occurred.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationSource.Database">
      <summary>The database state changed; for example, the database related to the query was dropped or detached.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationSource.Environment">
      <summary>The run-time environment was not compatible with notifications; for example, the isolation level was set to snapshot, or one or more SET options are not compatible.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationSource.Execution">
      <summary>A run-time error occurred during execution.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationSource.Object">
      <summary>A database object changed; for example, an underlying object related to the query was dropped or modified.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationSource.Owner">
      <summary>Internal only; not intended to be used in your code.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationSource.Statement">
      <summary>The Transact-SQL statement is not valid for notifications; for example, a SELECT statement that could not be notified or a non-SELECT statement was executed.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationSource.System">
      <summary>A system-related event occurred. For example, there was an internal error, the server was restarted, or resource pressure caused the invalidation.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationSource.Timeout">
      <summary>The subscription time-out expired.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationSource.Unknown">
      <summary>Used when the source option sent by the server was not recognized by the client.</summary>
    </member>
    <member name="T:System.Data.SqlClient.SqlNotificationType">
      <summary>Describes the different notification types that can be received by an <see cref="T:System.Data.SqlClient.OnChangeEventHandler" /> event handler through the <see cref="T:System.Data.SqlClient.SqlNotificationEventArgs" /> parameter.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationType.Change">
      <summary>Data on the server being monitored changed. Use the <see cref="T:System.Data.SqlClient.SqlNotificationInfo" /> item to determine the details of the change.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationType.Subscribe">
      <summary>There was a failure to create a notification subscription. Use the <see cref="T:System.Data.SqlClient.SqlNotificationEventArgs" /> object's <see cref="T:System.Data.SqlClient.SqlNotificationInfo" /> item to determine the cause of the failure.</summary>
    </member>
    <member name="F:System.Data.SqlClient.SqlNotificationType.Unknown">
      <summary>Used when the type option sent by the server was not recognized by the client.</summary>
    </member>
    <member name="T:System.Data.SqlClient.SqlParameter">
      <summary>Represents a parameter to a <see cref="T:System.Data.SqlClient.SqlCommand" /> and optionally its mapping to <see cref="T:System.Data.DataSet" /> columns. This class cannot be inherited. For more information on parameters, see Configuring Parameters and Parameter Data Types.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlParameter" /> class.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameter.#ctor(System.String,System.Data.SqlDbType)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlParameter" /> class that uses the parameter name and the data type.</summary>
      <param name="parameterName">The name of the parameter to map.</param>
      <param name="dbType">One of the <see cref="T:System.Data.SqlDbType" /> values.</param>
      <exception cref="T:System.ArgumentException">The value supplied in the <paramref name="dbType" /> parameter is an invalid back-end data type.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameter.#ctor(System.String,System.Data.SqlDbType,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlParameter" /> class that uses the parameter name, the <see cref="T:System.Data.SqlDbType" />, and the size.</summary>
      <param name="parameterName">The name of the parameter to map.</param>
      <param name="dbType">One of the <see cref="T:System.Data.SqlDbType" /> values.</param>
      <param name="size">The length of the parameter.</param>
      <exception cref="T:System.ArgumentException">The value supplied in the <paramref name="dbType" /> parameter is an invalid back-end data type.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameter.#ctor(System.String,System.Data.SqlDbType,System.Int32,System.Data.ParameterDirection,System.Boolean,System.Byte,System.Byte,System.String,System.Data.DataRowVersion,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlParameter" /> class that uses the parameter name, the type of the parameter, the size of the parameter, a <see cref="T:System.Data.ParameterDirection" />, the precision of the parameter, the scale of the parameter, the source column, a <see cref="T:System.Data.DataRowVersion" /> to use, and the value of the parameter.</summary>
      <param name="parameterName">The name of the parameter to map.</param>
      <param name="dbType">One of the <see cref="T:System.Data.SqlDbType" /> values.</param>
      <param name="size">The length of the parameter.</param>
      <param name="direction">One of the <see cref="T:System.Data.ParameterDirection" /> values.</param>
      <param name="isNullable">
        <see langword="true" /> if the value of the field can be null; otherwise, <see langword="false" />.</param>
      <param name="precision">The total number of digits to the left and right of the decimal point to which <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> is resolved.</param>
      <param name="scale">The total number of decimal places to which <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> is resolved.</param>
      <param name="sourceColumn">The name of the source column (<see cref="P:System.Data.SqlClient.SqlParameter.SourceColumn" />) if this <see cref="T:System.Data.SqlClient.SqlParameter" /> is used in a call to <see cref="Overload:System.Data.Common.DbDataAdapter.Update" />.</param>
      <param name="sourceVersion">One of the <see cref="T:System.Data.DataRowVersion" /> values.</param>
      <param name="value">An <see cref="T:System.Object" /> that is the value of the <see cref="T:System.Data.SqlClient.SqlParameter" />.</param>
      <exception cref="T:System.ArgumentException">The value supplied in the <paramref name="dbType" /> parameter is an invalid back-end data type.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameter.#ctor(System.String,System.Data.SqlDbType,System.Int32,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlParameter" /> class that uses the parameter name, the <see cref="T:System.Data.SqlDbType" />, the size, and the source column name.</summary>
      <param name="parameterName">The name of the parameter to map.</param>
      <param name="dbType">One of the <see cref="T:System.Data.SqlDbType" /> values.</param>
      <param name="size">The length of the parameter.</param>
      <param name="sourceColumn">The name of the source column (<see cref="P:System.Data.SqlClient.SqlParameter.SourceColumn" />) if this <see cref="T:System.Data.SqlClient.SqlParameter" /> is used in a call to <see cref="Overload:System.Data.Common.DbDataAdapter.Update" />.</param>
      <exception cref="T:System.ArgumentException">The value supplied in the <paramref name="dbType" /> parameter is an invalid back-end data type.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameter.#ctor(System.String,System.Data.SqlDbType,System.String,System.String,System.String,System.Int32,System.Data.ParameterDirection,System.Byte,System.Byte,System.String,System.Data.DataRowVersion,System.Boolean,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlParameter" /> class that uses the parameter name, the type of the parameter, the length of the parameter the direction, the precision, the scale, the name of the source column, one of the <see cref="T:System.Data.DataRowVersion" /> values, a Boolean for source column mapping, the value of the <see langword="SqlParameter" />, the name of the database where the schema collection for this XML instance is located, the owning relational schema where the schema collection for this XML instance is located, and the name of the schema collection for this parameter.</summary>
      <param name="parameterName">The name of the parameter to map.</param>
      <param name="dbType">One of the <see cref="T:System.Data.SqlDbType" /> values.</param>
      <param name="xmlSchemaCollectionDatabase">The name of the database where the schema collection for this XML instance is located.</param>
      <param name="xmlSchemaCollectionOwningSchema">The owning relational schema where the schema collection for this XML instance is located.</param>
      <param name="xmlSchemaCollectionName">The name of the schema collection for this parameter.</param>
      <param name="size">The length of the parameter.</param>
      <param name="direction">One of the <see cref="T:System.Data.ParameterDirection" /> values.</param>
      <param name="precision">The total number of digits to the left and right of the decimal point to which <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> is resolved.</param>
      <param name="scale">The total number of decimal places to which <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> is resolved.</param>
      <param name="sourceColumn">The name of the source column (<see cref="P:System.Data.SqlClient.SqlParameter.SourceColumn" />) if this <see cref="T:System.Data.SqlClient.SqlParameter" /> is used in a call to <see cref="Overload:System.Data.Common.DbDataAdapter.Update" />.</param>
      <param name="sourceVersion">One of the <see cref="T:System.Data.DataRowVersion" /> values.</param>
      <param name="sourceColumnNullMapping">
        <see langword="true" /> if the source column is nullable; <see langword="false" /> if it is not.</param>
      <param name="value">An <see cref="T:System.Object" /> that is the value of the <see cref="T:System.Data.SqlClient.SqlParameter" />.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameter.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlParameter" /> class that uses the parameter name and a value of the new <see cref="T:System.Data.SqlClient.SqlParameter" />.</summary>
      <param name="parameterName">The name of the parameter to map.</param>
      <param name="value">An <see cref="T:System.Object" /> that is the value of the <see cref="T:System.Data.SqlClient.SqlParameter" />.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.CompareInfo">
      <summary>Gets or sets the <see cref="T:System.Globalization.CompareInfo" /> object that defines how string comparisons should be performed for this parameter.</summary>
      <returns>A <see cref="T:System.Globalization.CompareInfo" /> object that defines string comparison for this parameter.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.DbType">
      <summary>Gets or sets the <see cref="T:System.Data.SqlDbType" /> of the parameter.</summary>
      <returns>One of the <see cref="T:System.Data.SqlDbType" /> values. The default is <see langword="NVarChar" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.Direction">
      <summary>Gets or sets a value that indicates whether the parameter is input-only, output-only, bidirectional, or a stored procedure return value parameter.</summary>
      <returns>One of the <see cref="T:System.Data.ParameterDirection" /> values. The default is <see langword="Input" />.</returns>
      <exception cref="T:System.ArgumentException">The property was not set to one of the valid <see cref="T:System.Data.ParameterDirection" /> values.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.IsNullable">
      <summary>Gets or sets a value that indicates whether the parameter accepts null values. <see cref="P:System.Data.SqlClient.SqlParameter.IsNullable" /> is not used to validate the parameter's value and will not prevent sending or receiving a null value when executing a command.</summary>
      <returns>
        <see langword="true" /> if null values are accepted; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.LocaleId">
      <summary>Gets or sets the locale identifier that determines conventions and language for a particular region.</summary>
      <returns>The locale identifier associated with the parameter.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.Offset">
      <summary>Gets or sets the offset to the <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> property.</summary>
      <returns>The offset to the <see cref="P:System.Data.SqlClient.SqlParameter.Value" />. The default is 0.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.ParameterName">
      <summary>Gets or sets the name of the <see cref="T:System.Data.SqlClient.SqlParameter" />.</summary>
      <returns>The name of the <see cref="T:System.Data.SqlClient.SqlParameter" />. The default is an empty string.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.Precision">
      <summary>Gets or sets the maximum number of digits used to represent the <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> property.</summary>
      <returns>The maximum number of digits used to represent the <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> property. The default value is 0. This indicates that the data provider sets the precision for <see cref="P:System.Data.SqlClient.SqlParameter.Value" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameter.ResetDbType">
      <summary>Resets the type associated with this <see cref="T:System.Data.SqlClient.SqlParameter" />.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameter.ResetSqlDbType">
      <summary>Resets the type associated with this <see cref="T:System.Data.SqlClient.SqlParameter" />.</summary>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.Scale">
      <summary>Gets or sets the number of decimal places to which <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> is resolved.</summary>
      <returns>The number of decimal places to which <see cref="P:System.Data.SqlClient.SqlParameter.Value" /> is resolved. The default is 0.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.Size">
      <summary>Gets or sets the maximum size, in bytes, of the data within the column.</summary>
      <returns>The maximum size, in bytes, of the data within the column. The default value is inferred from the parameter value.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.SourceColumn">
      <summary>Gets or sets the name of the source column mapped to the <see cref="T:System.Data.DataSet" /> and used for loading or returning the <see cref="P:System.Data.SqlClient.SqlParameter.Value" /></summary>
      <returns>The name of the source column mapped to the <see cref="T:System.Data.DataSet" />. The default is an empty string.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.SourceColumnNullMapping">
      <summary>Sets or gets a value which indicates whether the source column is nullable. This allows <see cref="T:System.Data.SqlClient.SqlCommandBuilder" /> to correctly generate Update statements for nullable columns.</summary>
      <returns>
        <see langword="true" /> if the source column is nullable; <see langword="false" /> if it is not.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.SourceVersion">
      <summary>Gets or sets the <see cref="T:System.Data.DataRowVersion" /> to use when you load <see cref="P:System.Data.SqlClient.SqlParameter.Value" /></summary>
      <returns>One of the <see cref="T:System.Data.DataRowVersion" /> values. The default is <see langword="Current" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.SqlDbType">
      <summary>Gets or sets the <see cref="T:System.Data.SqlDbType" /> of the parameter.</summary>
      <returns>One of the <see cref="T:System.Data.SqlDbType" /> values. The default is <see langword="NVarChar" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.SqlValue">
      <summary>Gets or sets the value of the parameter as an SQL type.</summary>
      <returns>An <see cref="T:System.Object" /> that is the value of the parameter, using SQL types. The default value is null.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameter.System#ICloneable#Clone">
      <summary>For a description of this member, see <see cref="M:System.ICloneable.Clone" />.</summary>
      <returns>A new <see cref="T:System.Object" /> that is a copy of this instance.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameter.ToString">
      <summary>Gets a string that contains the <see cref="P:System.Data.SqlClient.SqlParameter.ParameterName" />.</summary>
      <returns>A string that contains the <see cref="P:System.Data.SqlClient.SqlParameter.ParameterName" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.TypeName">
      <summary>Gets or sets the type name for a table-valued parameter.</summary>
      <returns>The type name of the specified table-valued parameter.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.UdtTypeName">
      <summary>Gets or sets a <see langword="string" /> that represents a user-defined type as a parameter.</summary>
      <returns>A <see langword="string" /> that represents the fully qualified name of a user-defined type in the database.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.Value">
      <summary>Gets or sets the value of the parameter.</summary>
      <returns>An <see cref="T:System.Object" /> that is the value of the parameter. The default value is null.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.XmlSchemaCollectionDatabase">
      <summary>Gets the name of the database where the schema collection for this XML instance is located.</summary>
      <returns>The name of the database where the schema collection for this XML instance is located.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.XmlSchemaCollectionName">
      <summary>Gets the name of the schema collection for this XML instance.</summary>
      <returns>The name of the schema collection for this XML instance.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameter.XmlSchemaCollectionOwningSchema">
      <summary>The owning relational schema where the schema collection for this XML instance is located.</summary>
      <returns>The owning relational schema for this XML instance.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlParameterCollection">
      <summary>Represents a collection of parameters associated with a <see cref="T:System.Data.SqlClient.SqlCommand" /> and their respective mappings to columns in a <see cref="T:System.Data.DataSet" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.Add(System.Data.SqlClient.SqlParameter)">
      <summary>Adds the specified <see cref="T:System.Data.SqlClient.SqlParameter" /> object to the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</summary>
      <param name="value">The <see cref="T:System.Data.SqlClient.SqlParameter" /> to add to the collection.</param>
      <returns>A new <see cref="T:System.Data.SqlClient.SqlParameter" /> object.</returns>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Data.SqlClient.SqlParameter" /> specified in the <paramref name="value" /> parameter is already added to this or another <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</exception>
      <exception cref="T:System.InvalidCastException">The parameter passed was not a <see cref="T:System.Data.SqlClient.SqlParameter" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is null.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.Add(System.Object)">
      <summary>Adds the specified <see cref="T:System.Data.SqlClient.SqlParameter" /> object to the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</summary>
      <param name="value">An <see cref="T:System.Object" />.</param>
      <returns>The index of the new <see cref="T:System.Data.SqlClient.SqlParameter" /> object.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.Add(System.String,System.Data.SqlDbType)">
      <summary>Adds a <see cref="T:System.Data.SqlClient.SqlParameter" /> to the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> given the parameter name and the data type.</summary>
      <param name="parameterName">The name of the parameter.</param>
      <param name="sqlDbType">One of the <see cref="T:System.Data.SqlDbType" /> values.</param>
      <returns>A new <see cref="T:System.Data.SqlClient.SqlParameter" /> object.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.Add(System.String,System.Data.SqlDbType,System.Int32)">
      <summary>Adds a <see cref="T:System.Data.SqlClient.SqlParameter" /> to the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />, given the specified parameter name, <see cref="T:System.Data.SqlDbType" /> and size.</summary>
      <param name="parameterName">The name of the parameter.</param>
      <param name="sqlDbType">The <see cref="T:System.Data.SqlDbType" /> of the <see cref="T:System.Data.SqlClient.SqlParameter" /> to add to the collection.</param>
      <param name="size">The size as an <see cref="T:System.Int32" />.</param>
      <returns>A new <see cref="T:System.Data.SqlClient.SqlParameter" /> object.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.Add(System.String,System.Data.SqlDbType,System.Int32,System.String)">
      <summary>Adds a <see cref="T:System.Data.SqlClient.SqlParameter" /> to the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> with the parameter name, the data type, and the column length.</summary>
      <param name="parameterName">The name of the parameter.</param>
      <param name="sqlDbType">One of the <see cref="T:System.Data.SqlDbType" /> values.</param>
      <param name="size">The column length.</param>
      <param name="sourceColumn">The name of the source column (<see cref="P:System.Data.SqlClient.SqlParameter.SourceColumn" />) if this <see cref="T:System.Data.SqlClient.SqlParameter" /> is used in a call to <see cref="Overload:System.Data.Common.DbDataAdapter.Update" />.</param>
      <returns>A new <see cref="T:System.Data.SqlClient.SqlParameter" /> object.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.AddRange(System.Array)">
      <summary>Adds an array of values to the end of the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</summary>
      <param name="values">The <see cref="T:System.Array" /> values to add.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.AddRange(System.Data.SqlClient.SqlParameter[])">
      <summary>Adds an array of <see cref="T:System.Data.SqlClient.SqlParameter" /> values to the end of the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</summary>
      <param name="values">The <see cref="T:System.Data.SqlClient.SqlParameter" /> values to add.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.AddWithValue(System.String,System.Object)">
      <summary>Adds a value to the end of the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</summary>
      <param name="parameterName">The name of the parameter.</param>
      <param name="value">The value to be added. Use <see cref="F:System.DBNull.Value" /> instead of null, to indicate a null value.</param>
      <returns>A <see cref="T:System.Data.SqlClient.SqlParameter" /> object.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.Clear">
      <summary>Removes all the <see cref="T:System.Data.SqlClient.SqlParameter" /> objects from the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.Contains(System.Data.SqlClient.SqlParameter)">
      <summary>Determines whether the specified <see cref="T:System.Data.SqlClient.SqlParameter" /> is in this <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</summary>
      <param name="value">The <see cref="T:System.Data.SqlClient.SqlParameter" /> value.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> contains the value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.Contains(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Object" /> is in this <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</summary>
      <param name="value">The <see cref="T:System.Object" /> value.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> contains the value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.Contains(System.String)">
      <summary>Determines whether the specified parameter name is in this <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</summary>
      <param name="value">The <see cref="T:System.String" /> value.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> contains the value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies all the elements of the current <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> to the specified one-dimensional <see cref="T:System.Array" /> starting at the specified destination <see cref="T:System.Array" /> index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from the current <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</param>
      <param name="index">A 32-bit integer that represents the index in the <see cref="T:System.Array" /> at which copying starts.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.CopyTo(System.Data.SqlClient.SqlParameter[],System.Int32)">
      <summary>Copies all the elements of the current <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> to the specified <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> starting at the specified destination index.</summary>
      <param name="array">The <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> that is the destination of the elements copied from the current <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</param>
      <param name="index">A 32-bit integer that represents the index in the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> at which copying starts.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameterCollection.Count">
      <summary>Returns an Integer that contains the number of elements in the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />. Read-only.</summary>
      <returns>The number of elements in the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> as an Integer.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.IndexOf(System.Data.SqlClient.SqlParameter)">
      <summary>Gets the location of the specified <see cref="T:System.Data.SqlClient.SqlParameter" /> within the collection.</summary>
      <param name="value">The <see cref="T:System.Data.SqlClient.SqlParameter" /> to find.</param>
      <returns>The zero-based location of the specified <see cref="T:System.Data.SqlClient.SqlParameter" /> that is a <see cref="T:System.Data.SqlClient.SqlParameter" /> within the collection. Returns -1 when the object does not exist in the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.IndexOf(System.Object)">
      <summary>Gets the location of the specified <see cref="T:System.Object" /> within the collection.</summary>
      <param name="value">The <see cref="T:System.Object" /> to find.</param>
      <returns>The zero-based location of the specified <see cref="T:System.Object" /> that is a <see cref="T:System.Data.SqlClient.SqlParameter" /> within the collection. Returns -1 when the object does not exist in the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.IndexOf(System.String)">
      <summary>Gets the location of the specified <see cref="T:System.Data.SqlClient.SqlParameter" /> with the specified name.</summary>
      <param name="parameterName">The case-sensitive name of the <see cref="T:System.Data.SqlClient.SqlParameter" /> to find.</param>
      <returns>The zero-based location of the specified <see cref="T:System.Data.SqlClient.SqlParameter" /> with the specified case-sensitive name. Returns -1 when the object does not exist in the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.Insert(System.Int32,System.Data.SqlClient.SqlParameter)">
      <summary>Inserts a <see cref="T:System.Data.SqlClient.SqlParameter" /> object into the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> at the specified index.</summary>
      <param name="index">The zero-based index at which value should be inserted.</param>
      <param name="value">A <see cref="T:System.Data.SqlClient.SqlParameter" /> object to be inserted in the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.Insert(System.Int32,System.Object)">
      <summary>Inserts an <see cref="T:System.Object" /> into the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> at the specified index.</summary>
      <param name="index">The zero-based index at which value should be inserted.</param>
      <param name="value">An <see cref="T:System.Object" /> to be inserted in the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameterCollection.IsFixedSize">
      <summary>Gets a value that indicates whether the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> has a fixed size.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> has a fixed size; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameterCollection.IsReadOnly">
      <summary>Gets a value that indicates whether the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameterCollection.Item(System.Int32)">
      <summary>Gets the <see cref="T:System.Data.SqlClient.SqlParameter" /> at the specified index.</summary>
      <param name="index">The zero-based index of the parameter to retrieve.</param>
      <returns>The <see cref="T:System.Data.SqlClient.SqlParameter" /> at the specified index.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The specified index does not exist.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameterCollection.Item(System.String)">
      <summary>Gets the <see cref="T:System.Data.SqlClient.SqlParameter" /> with the specified name.</summary>
      <param name="parameterName">The name of the parameter to retrieve.</param>
      <returns>The <see cref="T:System.Data.SqlClient.SqlParameter" /> with the specified name.</returns>
      <exception cref="T:System.IndexOutOfRangeException">The specified <paramref name="parameterName" /> is not valid.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.Remove(System.Data.SqlClient.SqlParameter)">
      <summary>Removes the specified <see cref="T:System.Data.SqlClient.SqlParameter" /> from the collection.</summary>
      <param name="value">A <see cref="T:System.Data.SqlClient.SqlParameter" /> object to remove from the collection.</param>
      <exception cref="T:System.InvalidCastException">The parameter is not a <see cref="T:System.Data.SqlClient.SqlParameter" />.</exception>
      <exception cref="T:System.SystemException">The parameter does not exist in the collection.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.Remove(System.Object)">
      <summary>Removes the specified <see cref="T:System.Data.SqlClient.SqlParameter" /> from the collection.</summary>
      <param name="value">The object to remove from the collection.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.RemoveAt(System.Int32)">
      <summary>Removes the <see cref="T:System.Data.SqlClient.SqlParameter" /> from the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> at the specified index.</summary>
      <param name="index">The zero-based index of the <see cref="T:System.Data.SqlClient.SqlParameter" /> object to remove.</param>
    </member>
    <member name="M:System.Data.SqlClient.SqlParameterCollection.RemoveAt(System.String)">
      <summary>Removes the <see cref="T:System.Data.SqlClient.SqlParameter" /> from the <see cref="T:System.Data.SqlClient.SqlParameterCollection" /> at the specified parameter name.</summary>
      <param name="parameterName">The name of the <see cref="T:System.Data.SqlClient.SqlParameter" /> to remove.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlParameterCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Data.SqlClient.SqlParameterCollection" />.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlRowsCopiedEventArgs">
      <summary>Represents the set of arguments passed to the <see cref="T:System.Data.SqlClient.SqlRowsCopiedEventHandler" />.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlRowsCopiedEventArgs.#ctor(System.Int64)">
      <summary>Creates a new instance of the <see cref="T:System.Data.SqlClient.SqlRowsCopiedEventArgs" /> object.</summary>
      <param name="rowsCopied">An <see cref="T:System.Int64" /> that indicates the number of rows copied during the current bulk copy operation.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlRowsCopiedEventArgs.Abort">
      <summary>Gets or sets a value that indicates whether the bulk copy operation should be aborted.</summary>
      <returns>
        <see langword="true" /> if the bulk copy operation should be aborted; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlRowsCopiedEventArgs.RowsCopied">
      <summary>Gets a value that returns the number of rows copied during the current bulk copy operation.</summary>
      <returns>
        <see langword="int" /> that returns the number of rows copied.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlRowsCopiedEventHandler">
      <summary>Represents the method that handles the <see cref="E:System.Data.SqlClient.SqlBulkCopy.SqlRowsCopied" /> event of a <see cref="T:System.Data.SqlClient.SqlBulkCopy" />.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:System.Data.SqlClient.SqlRowsCopiedEventArgs" /> object that contains the event data.</param>
    </member>
    <member name="T:System.Data.SqlClient.SqlRowUpdatedEventArgs">
      <summary>Provides data for the <see cref="E:System.Data.SqlClient.SqlDataAdapter.RowUpdated" /> event.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlRowUpdatedEventArgs.#ctor(System.Data.DataRow,System.Data.IDbCommand,System.Data.StatementType,System.Data.Common.DataTableMapping)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlRowUpdatedEventArgs" /> class.</summary>
      <param name="row">The <see cref="T:System.Data.DataRow" /> sent through an <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" />.</param>
      <param name="command">The <see cref="T:System.Data.IDbCommand" /> executed when <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> is called.</param>
      <param name="statementType">One of the <see cref="T:System.Data.StatementType" /> values that specifies the type of query executed.</param>
      <param name="tableMapping">The <see cref="T:System.Data.Common.DataTableMapping" /> sent through an <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" />.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlRowUpdatedEventArgs.Command">
      <summary>Gets or sets the <see cref="T:System.Data.SqlClient.SqlCommand" /> executed when <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> is called.</summary>
      <returns>The <see cref="T:System.Data.SqlClient.SqlCommand" /> executed when <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" /> is called.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlRowUpdatedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.Data.SqlClient.SqlDataAdapter.RowUpdated" /> event of a <see cref="T:System.Data.SqlClient.SqlDataAdapter" />.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">The <see cref="T:System.Data.SqlClient.SqlRowUpdatedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:System.Data.SqlClient.SqlRowUpdatingEventArgs">
      <summary>Provides data for the <see cref="E:System.Data.SqlClient.SqlDataAdapter.RowUpdating" /> event.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlRowUpdatingEventArgs.#ctor(System.Data.DataRow,System.Data.IDbCommand,System.Data.StatementType,System.Data.Common.DataTableMapping)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlClient.SqlRowUpdatingEventArgs" /> class.</summary>
      <param name="row">The <see cref="T:System.Data.DataRow" /> to <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" />.</param>
      <param name="command">The <see cref="T:System.Data.IDbCommand" /> to execute during <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" />.</param>
      <param name="statementType">One of the <see cref="T:System.Data.StatementType" /> values that specifies the type of query executed.</param>
      <param name="tableMapping">The <see cref="T:System.Data.Common.DataTableMapping" /> sent through an <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" />.</param>
    </member>
    <member name="P:System.Data.SqlClient.SqlRowUpdatingEventArgs.Command">
      <summary>Gets or sets the <see cref="T:System.Data.SqlClient.SqlCommand" /> to execute when performing the <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" />.</summary>
      <returns>The <see cref="T:System.Data.SqlClient.SqlCommand" /> to execute when performing the <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)" />.</returns>
    </member>
    <member name="T:System.Data.SqlClient.SqlRowUpdatingEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.Data.SqlClient.SqlDataAdapter.RowUpdating" /> event of a <see cref="T:System.Data.SqlClient.SqlDataAdapter" />.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">The <see cref="T:System.Data.SqlClient.SqlRowUpdatingEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:System.Data.SqlClient.SqlTransaction">
      <summary>Represents a Transact-SQL transaction to be made in a SQL Server database. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Data.SqlClient.SqlTransaction.Commit">
      <summary>Commits the database transaction.</summary>
      <exception cref="T:System.Exception">An error occurred while trying to commit the transaction.</exception>
      <exception cref="T:System.InvalidOperationException">The transaction has already been committed or rolled back.
-or-
The connection is broken.</exception>
    </member>
    <member name="P:System.Data.SqlClient.SqlTransaction.Connection">
      <summary>Gets the <see cref="T:System.Data.SqlClient.SqlConnection" /> object associated with the transaction, or <see langword="null" /> if the transaction is no longer valid.</summary>
      <returns>The <see cref="T:System.Data.SqlClient.SqlConnection" /> object associated with the transaction.</returns>
    </member>
    <member name="P:System.Data.SqlClient.SqlTransaction.IsolationLevel">
      <summary>Specifies the <see cref="T:System.Data.IsolationLevel" /> for this transaction.</summary>
      <returns>The <see cref="T:System.Data.IsolationLevel" /> for this transaction. The default is <see langword="ReadCommitted" />.</returns>
    </member>
    <member name="M:System.Data.SqlClient.SqlTransaction.Rollback">
      <summary>Rolls back a transaction from a pending state.</summary>
      <exception cref="T:System.Exception">An error occurred while trying to commit the transaction.</exception>
      <exception cref="T:System.InvalidOperationException">The transaction has already been committed or rolled back.
-or-
The connection is broken.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlTransaction.Rollback(System.String)">
      <summary>Rolls back a transaction from a pending state, and specifies the transaction or savepoint name.</summary>
      <param name="transactionName">The name of the transaction to roll back, or the savepoint to which to roll back.</param>
      <exception cref="T:System.ArgumentException">No transaction name was specified.</exception>
      <exception cref="T:System.InvalidOperationException">The transaction has already been committed or rolled back.
-or-
The connection is broken.</exception>
    </member>
    <member name="M:System.Data.SqlClient.SqlTransaction.Save(System.String)">
      <summary>Creates a savepoint in the transaction that can be used to roll back a part of the transaction, and specifies the savepoint name.</summary>
      <param name="savePointName">The name of the savepoint.</param>
      <exception cref="T:System.Exception">An error occurred while trying to commit the transaction.</exception>
      <exception cref="T:System.InvalidOperationException">The transaction has already been committed or rolled back.
-or-
The connection is broken.</exception>
    </member>
    <member name="T:System.Data.SqlTypes.SqlFileStream">
      <summary>Exposes SQL Server data that is stored with the FILESTREAM column attribute as a sequence of bytes.</summary>
    </member>
    <member name="M:System.Data.SqlTypes.SqlFileStream.#ctor(System.String,System.Byte[],System.IO.FileAccess)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlTypes.SqlFileStream" /> class.</summary>
      <param name="path">The logical path to the file. The path can be retrieved by using the Transact-SQL Pathname function on the underlying FILESTREAM column in the table.</param>
      <param name="transactionContext">The transaction context for the <see langword="SqlFileStream" /> object. Applications should return the byte array returned by calling the GET_FILESTREAM_TRANSACTION_CONTEXT method.</param>
      <param name="access">The access mode to use when opening the file. Supported <see cref="T:System.IO.FileAccess" /> enumeration values are <see cref="F:System.IO.FileAccess.Read" />, <see cref="F:System.IO.FileAccess.Write" />, and <see cref="F:System.IO.FileAccess.ReadWrite" />.
When using <see langword="FileAccess.Read" />, the <see langword="SqlFileStream" /> object can be used to read all of the existing data.
When using <see langword="FileAccess.Write" />, <see langword="SqlFileStream" /> points to a zero byte file. Existing data will be overwritten when the object is closed and the transaction is committed.
When using <see langword="FileAccess.ReadWrite" />, the <see langword="SqlFileStream" /> points to a file which has all the existing data in it. The handle is positioned at the beginning of the file. You can use one of the <see langword="System.IO" /><see langword="Seek" /> methods to move the handle position within the file to write or append new data.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is a null reference, or <paramref name="transactionContext" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="path" /> is an empty string (""), contains only white space, or contains one or more invalid characters.
<paramref name="path" /> begins with "\\.\", for example "\\.\PHYSICALDRIVE0 ".
The handle returned by the call to NTCreateFile is not of type FILE_TYPE_DISK.
<paramref name="options" /> contains an unsupported value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified <paramref name="path" /> is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The access requested is not permitted by the operating system for the specified path. This occurs when Write or ReadWrite access is specified, and the file or directory is set for read-only access.</exception>
      <exception cref="T:System.InvalidOperationException">NtCreateFile fails with error code set to ERROR_SHARING_VIOLATION.</exception>
    </member>
    <member name="M:System.Data.SqlTypes.SqlFileStream.#ctor(System.String,System.Byte[],System.IO.FileAccess,System.IO.FileOptions,System.Int64)">
      <summary>Initializes a new instance of the <see cref="T:System.Data.SqlTypes.SqlFileStream" /> class.</summary>
      <param name="path">The logical path to the file. The path can be retrieved by using the Transact-SQL Pathname function on the underlying FILESTREAM column in the table.</param>
      <param name="transactionContext">The transaction context for the <see langword="SqlFileStream" /> object. When set to null, an implicit transaction will be used for the <see langword="SqlFileStream" /> object. Applications should return the byte array returned by calling the GET_FILESTREAM_TRANSACTION_CONTEXT method.</param>
      <param name="access">The access mode to use when opening the file. Supported <see cref="T:System.IO.FileAccess" /> enumeration values are <see cref="F:System.IO.FileAccess.Read" />, <see cref="F:System.IO.FileAccess.Write" />, and <see cref="F:System.IO.FileAccess.ReadWrite" />.
When using <see langword="FileAccess.Read" />, the <see langword="SqlFileStream" /> object can be used to read all of the existing data.
When using <see langword="FileAccess.Write" />, <see langword="SqlFileStream" /> points to a zero byte file. Existing data will be overwritten when the object is closed and the transaction is committed.
When using <see langword="FileAccess.ReadWrite" />, the <see langword="SqlFileStream" /> points to a file which has all the existing data in it. The handle is positioned at the beginning of the file. You can use one of the <see langword="System.IO" /><see langword="Seek" /> methods to move the handle position within the file to write or append new data.</param>
      <param name="options">Specifies the option to use while opening the file. Supported <see cref="T:System.IO.FileOptions" /> values are <see cref="F:System.IO.FileOptions.Asynchronous" />, <see cref="F:System.IO.FileOptions.WriteThrough" />, <see cref="F:System.IO.FileOptions.SequentialScan" />, and <see cref="F:System.IO.FileOptions.RandomAccess" />.</param>
      <param name="allocationSize">The allocation size to use while creating a file. If set to 0, the default value is used.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is a null reference, or <paramref name="transactionContext" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="path" /> is an empty string (""), contains only white space, or contains one or more invalid characters.
<paramref name="path" /> begins with "\\.\", for example "\\.\PHYSICALDRIVE0 ".
The handle returned by call to NTCreateFile is not of type FILE_TYPE_DISK.
<paramref name="options" /> contains an unsupported value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified <paramref name="path" /> is invalid, such as being on an unmapped drive.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The access requested is not permitted by the operating system for the specified path. This occurs when Write or ReadWrite access is specified, and the file or directory is set for read-only access.</exception>
      <exception cref="T:System.InvalidOperationException">NtCreateFile fails with error code set to ERROR_SHARING_VIOLATION.</exception>
    </member>
    <member name="P:System.Data.SqlTypes.SqlFileStream.CanRead">
      <summary>Gets a value indicating whether the current stream supports reading.</summary>
      <returns>
        <see langword="true" /> if the current stream supports reading; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Data.SqlTypes.SqlFileStream.CanSeek">
      <summary>Gets a value indicating whether the current stream supports seeking.</summary>
      <returns>
        <see langword="true" /> if the current stream supports seeking; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Data.SqlTypes.SqlFileStream.CanWrite">
      <summary>Gets a value indicating whether the current stream supports writing.</summary>
      <returns>
        <see langword="true" /> if the current stream supports writing; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Data.SqlTypes.SqlFileStream.Flush">
      <summary>clears all buffers for this stream and causes any buffered data to be written to the underlying device.</summary>
    </member>
    <member name="P:System.Data.SqlTypes.SqlFileStream.Length">
      <summary>Gets a value indicating the length of the current stream in bytes.</summary>
      <returns>An <see cref="T:System.Int64" /> indicating the length of the current stream in bytes.</returns>
    </member>
    <member name="P:System.Data.SqlTypes.SqlFileStream.Name">
      <summary>Gets the logical path of the <see cref="T:System.Data.SqlTypes.SqlFileStream" /> passed to the constructor.</summary>
      <returns>A string value indicating the name of the <see cref="T:System.Data.SqlTypes.SqlFileStream" />.</returns>
    </member>
    <member name="P:System.Data.SqlTypes.SqlFileStream.Position">
      <summary>Gets or sets the position within the current stream.</summary>
      <returns>The current position within the <see cref="T:System.Data.SqlTypes.SqlFileStream" />.</returns>
    </member>
    <member name="M:System.Data.SqlTypes.SqlFileStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Reads a sequence of bytes from the current stream and advances the position within the stream by the number of bytes read.</summary>
      <param name="buffer">An array of bytes. When this method returns, the buffer contains the specified byte array with the values between offset and (offset + count - 1) replaced by the bytes read from the current source.</param>
      <param name="offset">The zero-based byte offset in buffer at which to begin storing the data read from the current stream.</param>
      <param name="count">The maximum number of bytes to be read from the current stream.</param>
      <returns>The total number of bytes read into the buffer. This can be less than the number of bytes requested if that many bytes are not currently available, or zero (0) if the end of the stream has been reached.</returns>
      <exception cref="T:System.NotSupportedException">The object does not support reading of data.</exception>
    </member>
    <member name="M:System.Data.SqlTypes.SqlFileStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Sets the position within the current stream.</summary>
      <param name="offset">A byte offset relative to the <paramref name="origin" /> parameter</param>
      <param name="origin">A value of type <see cref="T:System.IO.SeekOrigin" /> indicating the reference point used to obtain the new position</param>
      <returns>The new position within the current stream.</returns>
    </member>
    <member name="M:System.Data.SqlTypes.SqlFileStream.SetLength(System.Int64)">
      <summary>Sets the length of the current stream.</summary>
      <param name="value">The desired length of the current stream in bytes.</param>
      <exception cref="T:System.NotSupportedException">The object does not support reading of data.</exception>
    </member>
    <member name="P:System.Data.SqlTypes.SqlFileStream.TransactionContext">
      <summary>Gets or sets the transaction context for this <see cref="T:System.Data.SqlTypes.SqlFileStream" /> object.</summary>
      <returns>The <paramref name="transactionContext" /> array that was passed to the constructor for this <see cref="T:System.Data.SqlTypes.SqlFileStream" /> object.</returns>
    </member>
    <member name="M:System.Data.SqlTypes.SqlFileStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Writes a sequence of bytes to the current stream and advances the current position within this stream by the number of bytes written.</summary>
      <param name="buffer">An array of bytes. This method copies <paramref name="count" /> bytes from <paramref name="buffer" /> to the current stream.</param>
      <param name="offset">The zero-based byte offset in <paramref name="buffer" /> at which to begin copying bytes to the current stream.</param>
      <param name="count">The number of bytes to be written to the current stream.</param>
      <exception cref="T:System.NotSupportedException">The object does not support writing of data.</exception>
    </member>
  </members>
</doc>