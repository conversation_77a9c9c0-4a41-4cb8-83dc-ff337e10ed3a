﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.IBusiness;

namespace Coldairarrow.Business.HR_Manage
{
    public interface IHR_FeeUserInfoBusiness
    {
        Task<PageResult<HR_FeeUserInfo>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<HR_FeeUserInfo> GetTheDataAsync(string id);
        Task<HR_FeeUserInfo> GetTheDataByUserAsync(string userId);
        Task AddDataAsync(HR_FeeUserInfo data);
        Task UpdateDataAsync(HR_FeeUserInfo data);
        Task DeleteDataAsync(List<string> ids);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
        /// <summary>
        /// 导入并保存数据
        /// </summary>
        /// <param name="physicPath">物理路径</param>
        /// <returns></returns>
        Task<AjaxResult<DataTable>> ImportSaveData(string physicPath, IOperator op);
    }
}