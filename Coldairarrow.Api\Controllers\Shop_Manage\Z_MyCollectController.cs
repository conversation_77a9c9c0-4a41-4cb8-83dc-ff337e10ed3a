﻿using Coldairarrow.Business.Shop_Manage;
using Coldairarrow.Entity.Shop_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;

namespace Coldairarrow.Api.Controllers.Shop_Manage
{
    [Route("/Shop_Manage/[controller]/[action]")]
    public class Z_MyCollectController : BaseApiController
    {
        #region DI

        public Z_MyCollectController(IZ_MyCollectBusiness z_MyCollectBus)
        {
            _z_MyCollectBus = z_MyCollectBus;
        }

        IZ_MyCollectBusiness _z_MyCollectBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Z_MyCollect>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _z_MyCollectBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Z_MyCollect> GetTheData(IdInputDTO input)
        {
            return await _z_MyCollectBus.GetTheDataAsync(input.id);
        }


        /// <summary>
        /// 查询是否关注
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// 
        [HttpPost]
        public async Task<Z_MyCollect> MyCollection(Z_MyCollect data)
        {
            if (!string.IsNullOrEmpty(data.F_UserCode) && !string.IsNullOrEmpty(data.F_ProductId))
            {
                return await _z_MyCollectBus.IsCollection(data);
            }
            else
            {
                return new Z_MyCollect();
            }

        }

        /// <summary>
        /// 查询是否关注
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// 
        [HttpPost]
        public async Task AddOrCancel(Z_MyCollect data)
        {
            if (data.IsAdd.HasValue && !string.IsNullOrWhiteSpace(data.F_UserCode) && !string.IsNullOrWhiteSpace(data.F_ProductId))
            {
                if (data.IsAdd.Value)
                {
                    await this.SaveData(data);
                }
                else
                {
                    await this.DeleteData(new List<string> { data.F_Id });
                }
            }
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Z_MyCollect data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                //InitEntity(data);
                data.F_CreateUserId = data.F_UserId;
                data.F_CreateUserName = data.F_UserName;
                data.F_CreateDate =DateTime.Now;
                data.F_Id = Guid.NewGuid().ToString("N");
                await _z_MyCollectBus.AddDataAsync(data);
            }
            else
            {
                await _z_MyCollectBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _z_MyCollectBus.DeleteDataAsync(ids);
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _z_MyCollectBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Z_MyCollect>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _z_MyCollectBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}