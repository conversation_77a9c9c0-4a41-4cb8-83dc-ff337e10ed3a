<template>
  <a-card :bordered="false" :hoverable="true">
    <div class="table-operator">
      <a-button type="primary" icon="arrow-down" @click="exportExcel()">导出Excel</a-button>
      <a-button type="primary" icon="redo" @click="getDataList()">刷新</a-button>
    </div>

    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="10">
          <a-col :md="4" :sm="24">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="queryParam.condition">
                <a-select-option key="NameUser">姓名</a-select-option>
                <a-select-option key="DepartmentName">部门</a-select-option>
                <a-select-option key="EmployRelStatus">员工状态</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item>
              <a-input v-model="queryParam.keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>
          <a-col :md="3" :sm="24">
            <a-form-item>
              <!-- <a-input v-model="queryParam.keyword" placeholder="关键字" /> -->
              <a-select mode="multiple" style="width: 150px;" placeholder="部门" @change="handleChangeDept">
                <a-select-option v-for="(temp,index) in department" :key="index" :value="temp.Value">
                  {{ (temp.PName==="" || temp.PName===null)?temp.Name:temp.Name+'('+temp.PName+')'}}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="3" :sm="24">
            <a-form-item>
              <!-- <a-input v-model="queryParam.keyword" placeholder="关键字" /> -->
              <a-select mode="multiple" style="width: 150px;" placeholder="在职状态" @change="handleChangeWork">
                <a-select-option v-for="(temp,index) in workingState" :key="index" :value="temp.Value">
                  {{ temp.Name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="3" :sm="24">
            <a-form-item>
              <!-- <a-input v-model="queryParam.keyword" placeholder="关键字" /> -->
              <a-select mode="multiple" style="width: 150px;" placeholder="所属项目" @change="handleChangeProj">
                <a-select-option v-for="(temp,index) in projectList" :key="index" :value="temp.Value">
                  {{ temp.Name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-button type="primary" icon="search" @click="
                () => {
                  this.pagination.current = 1
                  this.getDataList()
                }
              ">查询</a-button>
            <a-button style="margin-left: 8px" icon="sync" @click="() => (queryParam = {})">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table" :columns="columns" :rowKey="row => row.Id" :dataSource="data" :pagination="false"
      :loading="loading" :scroll="{ x: 1100, y: 450 }" :bordered="true" size="small">
      <template v-for="col in ['DirthDate', 'F_AttackTime', 'F_InductionDate',
      'ThirdRnSigningDate','SecondRnContractEndDate','SecondRnSigningDate',
'FristRnContractEndDate','FristRenewSigningDate','ContractEndDate','SigningDate',
'F_TrueDepartureDate','F_PositiveDate','SigningDate','ContractEndDate'
               ]" :slot="col" slot-scope="text">
        <div :key="col">
          <template>
            <span>{{ text | dayjs('YYYY-MM-DD')  }}</span>
          </template>
        </div>
      </template>
      <span slot="RWorkExperience" slot-scope="text">
        <template>
          <span :title="text">{{ commonApi.subStrFormat(text,10)}}</span>
        </template>
      </span>
      <span slot="PWorkExperience" slot-scope="text">
        <template>
          <span :title="text">{{ commonApi.subStrFormat(text,10)}}</span>
        </template>
      </span>
      <span slot="IdCardAddress" slot-scope="text">
        <template>
          <span :title="text">{{ commonApi.subStrFormat(text,10)}}</span>
        </template>
      </span>
      <span slot="RegisteredResidence" slot-scope="text">
        <template>
          <span :title="text">{{ commonApi.subStrFormat(text,10)}}</span>
        </template>
      </span>
      <span slot="HomeAddress" slot-scope="text">
        <template>
          <span :title="text">{{ commonApi.subStrFormat(text,10)}}</span>
        </template>
      </span>
      <span slot="PromotionRecord" slot-scope="text">
        <template>
          <span :title="text">{{ commonApi.subStrFormat(text,10)}}</span>
        </template>
      </span>
      <span slot="PostChangeRecord" slot-scope="text">
        <template>
          <span :title="text">{{ commonApi.subStrFormat(text,10)}}</span>
        </template>
      </span>
      <span slot="DisciplinaryRecord" slot-scope="text">
        <template>
          <span :title="text">{{ commonApi.subStrFormat(text,10)}}</span>
        </template>
      </span>
      <!-- <span slot="F_AttackTime" slot-scope="text, record">
        <template>
          <span>{{ text  | dayjs('YYYY-MM-DD')  }}</span>
        </template>
      </span>
      <span slot="F_InductionDate" slot-scope="text, record">
        <template>
          <span>{{ text  | dayjs('YYYY-MM-DD')  }}</span>
        </template>
      </span> -->
    </a-table>
    <a-pagination show-size-changer :default-current="pagination.current" :defaultPageSize="pagination.pageSize"
      :showTotal="pagination.showTotal" :total="pagination.total" @showSizeChange="onShowSizeChange"
      @change="onChangeCurrent" style="margin: 5px 0;text-align: center;" />
  </a-card>
</template>

<script>
import moment from 'moment'
import { downLoadFile } from '@/utils/plugin/axios-plugin.js'
import { parseTime } from '@/utils/util.js'
import { operateFile } from '@/utils/tools.js'
const columns = [
  {
    title: '员工编码',
    dataIndex: 'EmployeesCode',
    width: 100,
    fixed: 'left',
    align: 'center'
  },
  {
    title: '姓名',
    dataIndex: 'NameUser',
    width: 100,
    fixed: 'left',
    align: 'center'
  },
  {
    title: '合同劳动签订公司',
    dataIndex: 'ContractCompany',
    width: 240,
    align: 'center'
  },
  {
    title: '所属项目',
    dataIndex: 'ProjectName',
    width: 100,
    align: 'center'
  },
  {
    title: '服务项目',
    dataIndex: 'ND',
    width: 100,
    align: 'center'
  },
  {
    title: '职能归属',
    dataIndex: 'ND2',
    width: 100,
    align: 'center'
  },
  {
    title: '类别归属',
    dataIndex: 'ND3',
    width: 100,
    align: 'center'
  },
  {
    title: '部门',
    dataIndex: 'DepartmentName',
    width: 100,
    align: 'center'
  },
  {
    title: '岗位',
    dataIndex: 'PostName',
    width: 100,
    align: 'center'
  },
  {
    title: '职级',
    dataIndex: 'F_Rank',
    width: 100,
    align: 'center'
  },
  {
    title: '性别',
    dataIndex: 'SexText',
    width: 100,
    align: 'center'
  },
  {
    title: '身份证号码',
    dataIndex: 'IdCardNumber',
    width: 180,
    align: 'center'
  },
  {
    title: '出生年月',
    dataIndex: 'DirthDate',
    width: 100,
    scopedSlots: { customRender: 'DirthDate' },
    align: 'center'
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 100,
    align: 'center',
    align: 'center'
  },
  {
    title: '开始工作时间',
    dataIndex: 'F_AttackTime',
    width: 100,
    scopedSlots: { customRender: 'F_AttackTime' },
    align: 'center'
  },
  {
    title: '工作年限',
    dataIndex: 'F_YearSorking',
    width: 100,
    align: 'center'
  },
  {
    title: '员工状态',
    dataIndex: 'EmployRelStatus',
    width: 100,
    align: 'center'
  },
  {
    title: '入职日期',
    dataIndex: 'F_InductionDate',
    width: 100,
    scopedSlots: { customRender: 'F_InductionDate' },
    align: 'center'
  },
  // {
  //   title: '司龄',
  //   dataIndex: 'Commander',
  //   width: 130
  // },
  {
    title: '转正日期',
    dataIndex: 'F_PositiveDate',
    width: 100,
    scopedSlots: { customRender: 'F_PositiveDate' },
    align: 'center'
  },
  {
    title: '离职日期',
    dataIndex: 'F_TrueDepartureDate',
    width: 100,
    scopedSlots: { customRender: 'F_TrueDepartureDate' },
    align: 'center'
  },
  {
    title: '首次合同签订日期',
    dataIndex: 'SigningDate',
    width: 120,
    scopedSlots: { customRender: 'SigningDate' },
    align: 'center'
  },
  {
    title: '首次合同到期日期',
    dataIndex: 'ContractEndDate',
    width: 120,
    scopedSlots: { customRender: 'ContractEndDate' },
    align: 'center'
  },
  {
    title: '第一次续签合同日期',
    dataIndex: 'FristRenewSigningDate',
    width: 120,
    scopedSlots: { customRender: 'FristRenewSigningDate' },
    align: 'center'
  },
  {
    title: '第一次续签合同到期日期',
    dataIndex: 'FristRnContractEndDate',
    width: 120,
    scopedSlots: { customRender: 'FristRnContractEndDate' },
    align: 'center'
  },
  {
    title: '第二次续签合同日期',
    dataIndex: 'SecondRnSigningDate',
    width: 120,
    scopedSlots: { customRender: 'SecondRnSigningDate' },
    align: 'center'
  },
  {
    title: '第二次续签合同到期日期',
    dataIndex: 'SecondRnContractEndDate',
    width: 120,
    scopedSlots: { customRender: 'SecondRnContractEndDate' },
    align: 'center'
  },
  {
    title: '第三次续签',
    dataIndex: 'ThirdRnSigningDate',
    width: 100,
    scopedSlots: { customRender: 'ThirdRnSigningDate' },
    align: 'center'
  },
  {
    title: '学历',
    dataIndex: 'RecordSchooling',
    width: 100,
    align: 'center'
  },
  {
    title: '毕业院校',
    dataIndex: 'GraduatedSchool',
    width: 120,
    align: 'center'
  },
  {
    title: '专业',
    dataIndex: 'Professional',
    width: 110,
    align: 'center'
  },
  {
    title: '职称',
    dataIndex: 'F_Title',
    width: 100,
    align: 'center'
  },
  {
    title: '过往工作经验最近',
    dataIndex: 'RWorkExperience',
    width: 150,
    scopedSlots: { customRender: 'RWorkExperience' },
    align: 'center'
  },
  {
    title: '过往工作经验次近',
    dataIndex: 'PWorkExperience',
    width: 150,
    scopedSlots: { customRender: 'PWorkExperience' },
    align: 'center'
  },
  {
    title: '民族',
    dataIndex: 'NationalInfo',
    width: 100,
    align: 'center'
  },
  {
    title: '政治面貌',
    dataIndex: 'PoliticalLandscape',
    width: 100,
    align: 'center'
  },
  {
    title: '手机号码',
    dataIndex: 'MobilePhone',
    width: 110,
    align: 'center'
  },
  {
    title: '籍贯',
    dataIndex: 'NativePlace',
    width: 100,
    align: 'center'
  },
  {
    title: '户籍地址',
    dataIndex: 'IdCardAddress',
    width: 130,
    scopedSlots: { customRender: 'IdCardAddress' },
    align: 'center'
  },
  {
    title: '档案所在地',
    dataIndex: 'RegisteredResidence',
    width: 130,
    scopedSlots: { customRender: 'RegisteredResidence' },
    align: 'center'
  },
  {
    title: '家庭地址',
    dataIndex: 'HomeAddress',
    width: 130,
    scopedSlots: { customRender: 'HomeAddress' },
    align: 'center'
  },
  {
    title: '婚姻状况',
    dataIndex: 'MaritalStatusText',
    width: 100,
    align: 'center'
  },
  {
    title: '生育状况',
    dataIndex: 'FertilityStatus',
    width: 100,
    align: 'center'
  },
  {
    title: '户口类型',
    dataIndex: 'AccountType',
    width: 100,
    align: 'center'
  },
  {
    title: '紧急联系人',
    dataIndex: 'EmergencyContact',
    width: 100,
    align: 'center'
  },
  {
    title: '紧急联系人电话',
    dataIndex: 'EmergencyContactNumber',
    width: 110,
    align: 'center'
  },
  {
    title: '是否自驾车',
    dataIndex: 'IsWhetherCarText',
    width: 100,
    align: 'center'
  },
  {
    title: '过往三年绩效3',
    dataIndex: 'PastPerformance3',
    width: 100,
    align: 'center'
  },
  {
    title: '过往三年绩效2',
    dataIndex: 'PastPerformance2',
    width: 100,
    align: 'center'
  },
  {
    title: '过往三年绩效1',
    dataIndex: 'PastPerformance1',
    width: 100,
    align: 'center'
  },
  {
    title: '过往奖惩记录',
    dataIndex: 'DisciplinaryRecord',
    width: 150,
    scopedSlots: { customRender: 'DisciplinaryRecord' },
    align: 'center'
  },
  {
    title: '晋升记录',
    dataIndex: 'PromotionRecord',
    width: 150,
    scopedSlots: { customRender: 'PromotionRecord' },
    align: 'center'
  },
  {
    title: '岗位变动记录',
    dataIndex: 'PostChangeRecord',
    width: 150,
    scopedSlots: { customRender: 'PostChangeRecord' },
    align: 'center'
  },
  {
    title: '当期劳动合同期限',
    dataIndex: 'ContractPeriod',
    width: 100,
    align: 'center'
  },
  {
    title: '累计签定合同的次数',
    dataIndex: 'CumContractsNumber',
    width: 100,
    align: 'center'
  }
]

export default {
  components: {},
  filters: {
    // 过滤器
  },
  mounted () {
    this.dataInfo()
    this.getDataList()
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 15,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      filters: {},
      sorter: {
        field: 'F_Id',
        order: 'desc'
      },
      loading: false,
      columns,
      queryParam: { condition: "NameUser" },
      selectedRowKeys: [],
      projectList: [],
      workingState: [],
      department: [],
    }
  },
  methods: {
    handleChangeDept (value) {
      this.PostId = value
    },
    handleChangeProj (value) {
      this.IsInterview = value
    },
    handleChangeWork (value) {
      this.RoundInterview = value
    },
    dataInfo () {
      this.$http
        .post('/HR_EmployeeInfoManage/HR_FormalEmployees/GetScreeningInfo', {
          data: this.queryParam.EmployRelStatus,
        })
        .then(resJson => {
          this.projectList = resJson.Data.ProjectList
          this.workingState = resJson.Data.WorkingState
          this.department = resJson.Data.Department
          console.log(resJson);
        })

    },
    onChangeCurrent (current, pageSize) {
      this.pagination.current = current
      this.getDataList()
    },
    onShowSizeChange (current, pageSize) {
      this.pagination.current = current
      this.pagination.pageSize = pageSize
      this.getDataList()
    },
    handleTableChange (pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.filters = { ...filters }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    exportExcel () {
      const data = {
        PageIndex: this.pagination.current,
        PageRows: this.pagination.pageSize,
        SortField: this.sorter.field || 'F_Id',
        SortType: this.sorter.order,
        Search: this.queryParam,
        PostId: this.PostId,
        IsInterview: this.IsInterview,
        RoundInterview: this.RoundInterview,
        ...this.filters
      }
      const url = '/HR_ReportFormsManage/HR_EmployeesBasicInfo/ExcelDownload'
      downLoadFile(
        url,
        data,
        function (res) {
          console.log(res)
          if (res) {
            operateFile(res, '员工基础信息')
          } else {
            console.log('失败')
          }
        },
        function (err) {
          console.log(err)
        }
      )
    },
    getDataList () {
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/HR_ReportFormsManage/HR_EmployeesBasicInfo/GetDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'F_Id',
          SortType: this.sorter.order,
          Search: this.queryParam,
          PostId: this.PostId,
          IsInterview: this.IsInterview,
          RoundInterview: this.RoundInterview,
          ...this.filters
        })
        .then(resJson => {
          // debugger
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }

          this.pagination = pagination
          this.pagination.total = resJson.Total
        })
    },
    onSelectChange (selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    hasSelected () {
      return this.selectedRowKeys.length > 0
    }
  }
}
</script>
