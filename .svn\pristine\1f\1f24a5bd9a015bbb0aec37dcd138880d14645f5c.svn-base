﻿using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HR_EmployeeInfoManage
{
    public interface IHR_FormalEmployeesBusiness
    {
        Task<PageResult<HR_FormalEmployeesDTO>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<PageResult<HR_FormalEmployeesDTO>> GetMyTeamsList(PageInput<ConditionDTO> input, string userId);

        Task<HR_FormalEmployees> GetTheDataAsync(string id);
        HR_FormalEmployees GetTheData(string id);
        /// <summary>
        /// 解密数据
        /// </summary>
        /// <param name="hR_Formal"></param>
        /// <returns></returns>
        Task<HR_FormalEmployeesTheDTO> DecryptFormalByDto(HR_FormalEmployeesTheDTO hR_Formal);
        /// <summary>
        /// 加密数据
        /// </summary>
        /// <param name="hR_Formal"></param>
        /// <returns></returns>
        HR_FormalEmployeesTheDTO EncryptFormByDto(HR_FormalEmployeesTheDTO hR_Formal);
        /// <summary>
        /// 解密数据
        /// </summary>
        /// <param name="hR_Formal"></param>
        /// <returns></returns>
        Task<HR_FormalEmployees> DecryptFormalByDto(HR_FormalEmployees hR_Formal);
        /// <summary>
        /// 加密数据
        /// </summary>
        /// <param name="hR_Formal"></param>
        /// <returns></returns>
        HR_FormalEmployees EncryptFormByDto(HR_FormalEmployees hR_Formal);
        /// <summary>
        /// 通过用户id去获取用户信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        HR_FormalEmployees GetTheDataByUserId(string userId);
        Task GetUserPush();
        Task<HR_FormalEmployeesTheDTO> GetFormDataAsync(string id);
        Task<HR_FormalEmployeesTheDTO> GetInfoDataAsync(string id);

        ProjectDept GetProjectDept(string id);
        Task<PageResult<HR_FormalEmployeesDetailsDTO>> GetDetailsFormDataAsync(PageInput<ConditionDTO> input);

        /// <summary>
        /// 根据用户名称获取员工信息
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        HR_FormalEmployeesDTO GetByName(string name);
        /// <summary>
        /// 取最大的整数编码
        /// </summary>
        /// <returns></returns>
        string GetMaxCode();
        /// <summary>
        /// 根据用户ID查询员工信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        HR_FormalEmployeesDTO GetDataByUserId(string userId);
        HR_FormalEmployeesDTO GetDataByFId(string userId);
        HR_FormalEmployeesDTO GetDataByMobile(string mobile);
        /// <summary>
        /// 筛选信息
        /// </summary>
        /// <returns></returns> 
        ScreeningInfoModel GetScreeningInfo(DataInputDTO data);
        /// <summary>
        /// 数据保存
        /// </summary>
        /// <param name="data"></param>
        void SaveData(HR_FormalEmployeesTheDTO data);
        /// <summary>
        /// 根据员工Id获取的员工和部门职位的左连接
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<IEnumerable<HR_FormalEmployeesDTO>> GetConDataListAsync(string id);
        Task AddDataAsync(HR_FormalEmployees data);
        void AddData(HR_FormalEmployees data);
        Task UpdateDataAsync(HR_FormalEmployees data);
        void UpdateData(HR_FormalEmployees data);
        Task DeleteDataAsync(List<string> ids);
        /// <summary>
        /// 业务删除正式员工
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task BusDeleteDataAsync(List<string> ids);
        Task UnBindUsers(string id);

        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
        DataTable GetPositionExcelListAsync(PageInput<ConditionDTO> input);
        HomeInfo GetHomeInfo(string UserId);
        /// <summary>
        /// 上传用户信息导入
        /// </summary>
        /// <param name="list"></param>
        Task UploadFileByUserInfo(List<HR_FormalEmployeeImportDTO> list);
        #region 打卡小程序
        /// <summary>
        /// 获取打卡审批流程者
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<HR_FormalEmployees>> GetPunchCarFlowUsers();
        #endregion
    }
    public class ProjectDept
    {
        /// <summary>
        ///项目
        /// </summary>
        public string project { get; set; }
        /// <summary>
        ///部门
        /// </summary>
        public string dept { get; set; }

    }
    public class HR_FormalEmployeesDTO : HR_FormalEmployees
    {

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 职位名称
        /// </summary>
        public string PostName { get; set; }
        /// <summary>
        /// 所属组织
        /// </summary>
        public string OrgInfo { get; set; }
        /// <summary>
        /// 职位名称
        /// </summary>
        public string SexText { get => Sex == 0 ? "女" : "男"; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public string CreateDate { get => F_CreateDate != null ? F_CreateDate.ToString("yyyy-MM-dd") : ""; }

        /// <summary>
        /// 出生日期
        /// </summary>
        public string DirthDateText { get => DirthDate != null ? Convert.ToDateTime(DirthDate).ToString("yyyy-MM-dd") : ""; }
        /// <summary>
        /// 入职日期
        /// </summary>
        public DateTime? F_InductionDate { get; set; }
        /// <summary>
        /// 入职用工状态
        /// </summary>
        public String F_InductionEmployRelStatus { get; set; }
        /// <summary>
        /// 试用期（月）
        /// </summary>
        public Int32? F_ProbationPeriod { get; set; }
        /// <summary>
        /// 是否转正
        /// </summary>
        public bool IsPosive
        { get; set; }
        /// <summary>
        /// 转正ID
        /// </summary>
        public String positiveId { get; set; }
        /// <summary>
        /// 离职日期
        /// </summary>
        public DateTime? F_TrueDepartureDate { get; set; }
        /// <summary>
        /// 试用期（月）
        /// </summary>
        public DateTime? probationDate
        {
            get
            {
                if (F_InductionDate != null)
                {
                    if (F_ProbationPeriod != null)
                    {
                        return Convert.ToDateTime(F_InductionDate).AddMonths(Convert.ToInt32(F_ProbationPeriod));
                    }
                    else
                    {
                        return Convert.ToDateTime(F_InductionDate).AddMonths(3);
                    }
                }
                return new DateTime();
            }
        }
        /// <summary>
        /// 角色
        /// </summary>
        public String RoleStr { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>

    public class HR_FormalEmployeesTheDTO : HR_FormalEmployees
    {
        /// <summary>
        /// 年龄
        /// </summary>
        public int? age
        {
            get
            {
                var day = 0;
                if (!string.IsNullOrWhiteSpace(DirthDate.ToString()))
                {
                    //var NowDate = Convert.ToDateTime(DateTime.Now.ToShortDateString());
                    //TimeSpan sp = NowDate.Subtract(Convert.ToDateTime(Convert.ToDateTime(DirthDate).ToShortDateString()));
                    //day = sp.Days;
                    day = DateTime.Now.Year - DirthDate.Value.Year;
                }
                return day;
            }
        }
        /// <summary>
        /// 社会工作经历
        /// </summary>
        public List<HR_SocialWorkExp> hR_SocialWorkExps { get; set; }
        /// <summary>
        /// 出生日期
        /// </summary>
        public string DirthDateText { get => DirthDate?.ToString("yyyy-MM-dd"); }
        /// <summary>
        /// 头像ID
        /// </summary>
        public string HeardId { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public string SexText { get => Sex == 0 ? "女" : "男"; }
        /// <summary>
        /// 生效日期
        /// </summary>
        public string EffectiveDateText { get => F_CreateDate.ToString("yyyy-MM-dd"); }
        /// <summary>
        /// 是否是打卡人员
        /// </summary>
        public string IsPunchCardUserText { get => IsPunchCardUser.HasValue && IsPunchCardUser == YesOrNo.Yes ? "是" : "否"; }
        /// <summary>
        /// 是否购车
        /// </summary>
        public string IsWhetherCarText { get => IsWhetherCar != 1 ? "否" : "是"; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 职称
        /// </summary>
        public string PositionName { get; set; }
        /// <summary>
        /// 组织
        /// </summary>
        public string PositionOrg { get; set; }
        /// <summary>
        /// 入职日期
        /// </summary>
        public DateTime? F_InductionDate { get; set; }
        /// <summary>
        /// 加入公司日期
        /// </summary>
        public DateTime? F_WorkCompanyDate { get; set; }
        /// <summary>
        /// 公司服务年限(年)
        /// </summary>
        public int? ServiceYear
        {
            get
            {
                if (F_InductionDate != null)
                {
                    var NowYear = DateTime.Now.Year;
                    var InYear = Convert.ToDateTime(F_InductionDate).Year;
                    //如果有离职时间则离职时间减去入职的时间
                    if (F_TrueDepartureDate != null)
                    {
                        InYear = F_TrueDepartureDate.Value.Year;
                    }

                    return (NowYear - InYear);
                }
                return 1;
            }
        }
        /// <summary>
        /// 参加工作年限(年)
        /// </summary>
        public int? WorkYear
        {
            get
            {
                if (F_StartWorkTime != null)
                {
                    var NowYear = DateTime.Now.Year;
                    var InYear = Convert.ToDateTime(F_StartWorkTime).Year;
                    return (NowYear - InYear);
                }
                return 1;
            }
        }
        /// <summary>
        /// 试用期（月）
        /// </summary>
        public Int32? F_ProbationPeriod { get; set; }

        /// <summary>
        /// 入职来源
        /// </summary>
        public string F_InductionSource { get; set; }
        /// <summary>
        /// 转正日期
        /// </summary>
        public DateTime? F_PositiveDate { get; set; }
        /// <summary>
        /// 离职原因
        /// </summary>
        public String F_DepartureReason { get; set; }

        /// <summary>
        /// 实际离职生效日期
        /// </summary>
        public DateTime? F_TrueDepartureDate { get; set; }
        /// <summary>
        /// 职级
        /// </summary>
        //public string Rank { get; set; }
        /// <summary>
        /// 婚姻状况
        /// </summary>
        public string MaritalStatusText { get => MaritalStatus.HasValue ? MaritalStatus.Value == 1 ? "已婚" : MaritalStatus.Value == 2 ? "离异" : "未婚" : ""; }
        /// <summary>
        /// 头像
        /// </summary>
        public String HeadPortraitText { get; set; }

    }
    public class HR_FormalEmployeesDetailsDTO : HR_FormalEmployees
    {
        public int? PositiWFState { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 入职后职位
        /// </summary>
        public String F_InductionPosition { get; set; }

        /// <summary>
        /// 入职后职级
        /// </summary>
        public String F_InductionRank { get; set; }

        /// <summary>
        /// 职位名称
        /// </summary>
        public string PostName { get; set; }

        /// <summary>
        /// 所属组织
        /// </summary>
        public string F_InductionOrg { get; set; }

        /// <summary>
        /// 入职日期
        /// </summary>
        public DateTime? F_InductionDate { get; set; }
        /// <summary>
        /// 入职用工状态
        /// </summary>
        public String F_InductionEmployRelStatus { get; set; }
        /// <summary>
        /// 试用期（月）
        /// </summary>
        public Int32? F_ProbationPeriod { get; set; }
        /// <summary>
        /// 试用期（月）
        /// </summary>
        public DateTime? probationDate
        {
            get
            {
                if (F_InductionDate != null)
                {
                    if (F_ProbationPeriod != null)
                    {
                        return Convert.ToDateTime(F_InductionDate).AddMonths(Convert.ToInt32(F_ProbationPeriod));
                    }
                    else
                    {
                        return Convert.ToDateTime(F_InductionDate).AddMonths(3);
                    }
                }
                return new DateTime();
            }
        }
    }
    /// <summary>
    /// 员工信息导入实体
    /// </summary>
    public class HR_FormalEmployeeImportDTO
    {
        /// <summary>
        /// 服务公司
        /// </summary>
        public string companyName { get; set; }
        /// <summary>
        /// 合同签订公司
        /// </summary>
        public string laborCompanyName { get; set; }
        /// <summary>
        /// 一级架构
        /// </summary>
        public string firstDepName { get; set; }
        /// <summary>
        /// 二级架构
        /// </summary>
        public string seconDepName { get; set; }
        /// <summary>
        ///姓名
        /// </summary>
        public string userName { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public string sex { get; set; }
        /// <summary>
        /// 职位
        /// </summary>
        public string postName { get; set; }
        /// <summary>
        /// 职级
        /// </summary>
        public string rankName { get; set; }
        /// <summary>
        /// 入司时间
        /// </summary>
        public DateTime? inductionDate { get; set; }
        /// <summary>
        /// 身份证号
        /// </summary>
        public string idCard { get; set; }
    }

    public class HomeInfo
    {
        public DateTime? F_InTime { get; set; }
        public string F_Id { get; set; }
        public string ContractType { get; set; }
        public string F_PositionId { get; set; }
        public int? F_IsDirectReports { get; set; }

        public int ContractTypeNum { get; set; }
        public string Url { get; set; }

        public string Name { get; set; }

        public string HeadImage { get; set; }

        public string CompanyName { get; set; }

        public string DepName { get; set; }

        public string PostName { get; set; }
        public int PostNameNum { get; set; }
        public string Phone { get; set; }

        public string TepPhone { get; set; }

        public string EMail { get; set; }

        public int InductionYear { get; set; }

        public int InductionMonth { get; set; }

        public int InductionDay { get; set; }

        public decimal Holiday { get; set; }

        public string ExpirationTime { get; set; }

        public DateTime? InductionTime { get; set; }

        public string RankInfo { get; set; }
        public string UserId { get; set; }
        public int RankInfoNum { get; set; }
    }
    /// <summary>
    /// 历程
    /// </summary>
    public class CourseInfo
    {
        public int Type { get; set; }

        public string Name { get; set; }

        public int Day { get; set; }
    }
    /// <summary>
    /// 团队动态
    /// </summary>
    public class DepartmentDynamic
    {
        public string Url { get; set; }

        public string Name { get; set; }

        public int Day { get; set; }

        public string StartTime { get; set; }

        public string EndTime { get; set; }

    }
    /// <summary>
    /// 我得团队
    /// </summary>
    public class Department
    {
        public string Url { get; set; }

        public string Name { get; set; }
    }



    public class ScreeningInfoModel
    {
        /// <summary>
        /// 项目
        /// </summary>
        public List<ScreeningInfo> ProjectList { get; set; }
        /// <summary>
        /// 在职状态
        /// </summary>
        public List<ScreeningInfo> WorkingState { get; set; }
        /// <summary>
        /// 部门
        /// </summary>
        public List<ScreeningInfo> Department { get; set; }
    }

    public class ScreeningInfo
    {
        public string Value { get; set; }

        public string Name { get; set; }

        public string PName { get; set; }
    }

}