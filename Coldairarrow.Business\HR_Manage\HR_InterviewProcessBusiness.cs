﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.Base_Manage;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_InterviewProcessBusiness : BaseBusiness<HR_InterviewProcess>, IHR_InterviewProcessBusiness, ITransientDependency
    {
        public HR_InterviewProcessBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_InterviewProcess>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable().Where(i => input.Keyword == i.F_PostId);



            var where = LinqHelper.True<HR_InterviewProcess>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_InterviewProcess, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            var temp = await q.Where(where).GetPageResultAsync(input);
            var proIds = temp.Data.Select(i => i.F_Id).ToList();
            var entity = Db.GetIQueryable<HR_Interviewer>().Where(i => proIds.Contains(i.F_InterviewPId)).ToList();
            var userIds = entity.Select(i => i.F_UserId).ToList();

            var entityUser = Db.GetIQueryable<HR_FormalEmployees>().Where(i => userIds.Contains(i.F_Id)).ToList();
            foreach (var item in temp.Data)
            {
                if (!string.IsNullOrWhiteSpace(item.F_ParentId))
                {
                    item.F_ParentName = temp.Data.FirstOrDefault(i => i.F_Id == item.F_ParentId)?.F_InterviewName;
                }
                var smEntity = entity.Where(i => i.F_InterviewPId == item.F_Id).Select(i => i.F_UserId).ToList();
                item.Interviewer = string.Join(",", entityUser.Where(i => smEntity.Contains(i.F_Id)).Select(i => i.NameUser));
            }
            return temp;
        }

        public async Task<HR_InterviewProcess> GetTheDataAsync(string id)
        {
            var temp = await GetEntityAsync(id);
            var userIds = Db.GetIQueryable<HR_Interviewer>().Where(i => i.F_InterviewPId == temp.F_Id).Select(i => i.F_UserId).ToList();
            temp.Data = Db.GetIQueryable<HR_FormalEmployees>().Where(i => userIds.Contains(i.F_Id)).ToList();
            var depIds = temp.Data.Select(i => i.F_DepartmentId).ToList();
            var depEntity = Db.GetIQueryable<Base_Department>().Where(i => depIds.Contains(i.Id)).ToList();
            foreach (var item in temp.Data)
            {
                item.DepartmentName = depEntity.FirstOrDefault(i => i.Id == item.F_DepartmentId)?.Name;
            }
            return temp;
        }
        [DataAddJsonLog(UserLogType.招聘管理, "F_InterviewName", "面试流程")]
        public async Task AddDataAsync(HR_InterviewProcess data)
        {

            await InsertAsync(data);
            if (data.List.Count > 0)
            {
                var entity = Db.GetIQueryable<HR_Interviewer>().Where(i => i.F_InterviewPId == data.F_Id);
                if (entity.Count() > 0)
                {
                    Db.Delete(entity);
                }
                List<HR_Interviewer> interviewers = new List<HR_Interviewer>();
                foreach (var item in data.List)
                {
                    HR_Interviewer interviewer = new HR_Interviewer()
                    {
                        F_CreateDate = data.F_CreateDate,
                        F_CreateUserId = data.F_CreateUserId,
                        F_CreateUserName = data.F_CreateUserName,
                        F_UserId = item,
                        F_InterviewPId = data.F_Id,
                        F_Id = Guid.NewGuid().ToString("N"),
                    };
                    interviewers.Add(interviewer);
                }
                Db.Insert(interviewers);
            }
        }
        [DataEditJsonCLog(UserLogType.招聘管理, "F_InterviewName", "面试流程")]
        public async Task UpdateDataAsync(HR_InterviewProcess data)
        {
            await UpdateAsync(data);
            if (data.List.Count > 0)
            {
                var entity = Db.GetIQueryable<HR_Interviewer>().Where(i => i.F_InterviewPId == data.F_Id).ToList();
                if (entity.Count > 0)
                {
                    Db.Delete(entity);
                }
                List<HR_Interviewer> interviewers = new List<HR_Interviewer>();
                foreach (var item in data.List)
                {
                    HR_Interviewer interviewer = new HR_Interviewer()
                    {
                        F_CreateDate = data.F_ModifyDate.Value,
                        F_CreateUserId = data.F_ModifyUserId,
                        F_CreateUserName = data.F_ModifyUserName,
                        F_UserId = item,
                        F_InterviewPId = data.F_Id,
                        F_Id = Guid.NewGuid().ToString("N"),
                    };
                    interviewers.Add(interviewer);
                }
                Db.Insert(interviewers);
            }
        }
        [DataDeleteJsonLog(UserLogType.招聘管理, "F_InterviewName", "面试流程")]
        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_InterviewProcess>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_InterviewProcess, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_InterviewProcess, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        public List<HR_InterviewProcess> GetDataDepIntPr(string Id, string postId)
        {
            var temp = Db.GetIQueryable<HR_InterviewProcess>().Where(i => i.F_PostId == postId).ToList();
            if (!string.IsNullOrEmpty(Id))
            {
                temp = temp.Where(i => i.F_Id != Id).ToList();
            }
            return temp;

        }
        #endregion

        #region 私有成员

        #endregion
    }
}