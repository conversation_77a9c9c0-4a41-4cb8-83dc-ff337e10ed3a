	SELECT
	(select count(1) from  [HRSystem].[dbo].[HR_Entry]  where 1=1 and F_CreateDate > convert(char(4),DATEPART(yyyy, getdate()))+'-01-01' and F_CreateDate< convert(char(4),DATEPART(yyyy, getdate()))+'-12-31' AND F_FileId IS NOT NULL) AS ��ְ,
	(select count(1) from  HR_RecruitmentCandidates  where  F_IsInvitedInt=1 and F_IsDelete=0 and F_CreateDate > convert(char(4),DATEPART(yyyy, getdate()))+'-01-01' and F_CreateDate<convert(char(4),DATEPART(yyyy, getdate()))+'-12-31') AS ����,
	(select count(1) from  HR_RecruitmentCandidates  where  F_Through=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-01-01' and F_ThroughTime<convert(char(4),DATEPART(yyyy, getdate()))+'-12-31') AS ����ͨ��,
	(select count(1) from  HR_RecruitmentCandidates  where  F_Through=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-01-01' and F_ThroughTime<convert(char(4),DATEPART(yyyy, getdate()))+'-12-31')*1.0 / 
	(select count(1) from  HR_RecruitmentCandidates  where  F_IsInvitedInt=1 and F_IsDelete=0 and F_CreateDate > convert(char(4),DATEPART(yyyy, getdate()))+'-01-01' and F_CreateDate<convert(char(4),DATEPART(yyyy, getdate()))+'-12-31')*1.0 AS ����ͨ����


	--��ѯ�������
	select count(1) from [dbo].[HR_RecruitmentCandidates] re
	left join [dbo].[HR_Entry] en on re.F_UserId=en.F_Id 
	where 1=1 and re.F_IsDelete=0 and re.F_IsInvitedInt=1 
	and re.F_CreateDate > convert(char(4),DATEPART(yyyy, getdate()))+'-01-01' 
	and re.F_CreateDate< convert(char(4),DATEPART(yyyy, getdate()))+'-12-31';


select dt.����,dt.����,dt.ͨ��,dt.���� from (
SELECT top 10 c.F_Name ����,
		ISNULL(ForInt.Num,
		0) ����,
		ISNULL(ThroughInt.Num,
		0) ͨ��,
		ISNULL((ThroughInt.Num*1.0/ForInt.Num*1.0),
		0) AS ����
FROM 
    (SELECT F_Role ,
		COUNT(1)as Num
    FROM 
        (SELECT a.F_UserId,
		b.F_Role
        FROM HR_RecruitmentCandidates a
        LEFT JOIN [dbo].[HR_Recruit] b
        	ON a.F_RecruitId=b.F_Id
        WHERE a.F_IsInvitedInt=1
        		AND a.F_CreateDate >convert(char(4),DATEPART(yyyy, getdate()))+'-01-01'and a.F_CreateDate<convert(char(4),DATEPART(yyyy, getdate())+1)+'-01-01') AS Rec
        WHERE F_Role is NOT null
        GROUP BY  F_Role) AS ForInt
    LEFT JOIN 
    (SELECT F_Role ,
		COUNT(1) AS Num
    FROM 
        (SELECT a.F_UserId,
		b.F_Role
        FROM HR_RecruitmentCandidates a
        LEFT JOIN [dbo].[HR_Recruit] b
        	ON a.F_RecruitId=b.F_Id
        WHERE a.F_IsInvitation=1
        		AND a.F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-01-01'
        		AND a.F_ThroughTime<convert(char(4),DATEPART(yyyy, getdate())+1)+'-01-01') AS Rec
        WHERE F_Role is NOT null
        GROUP BY  F_Role) AS ThroughInt
    	ON ForInt.F_Role=ThroughInt.F_Role
LEFT JOIN Base_Post c
	ON ForInt.F_Role = c.F_Id
ORDER BY  ���� desc,ThroughInt.Num,ForInt.Num desc
) as dt;

-----------------------------------------------------
--��ְ��
SELECT '01' AS �·�	,(SELECT convert(decimal(38, 0),(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate()))+'-01-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate()))+'-02-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate()))+'-01-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate()))+'-02-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate()))+'-01-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate()))+'-01-01')) AS ����,(SELECT convert(decimal(38, 0),(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate())-1)+'-01-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-02-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate())-1)+'-01-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-02-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-01-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-01-01')) AS ȥ��  UNION ALL

SELECT '02' AS �·�	,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate()))+'-02-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate()))+'-03-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate()))+'-02-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate()))+'-03-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate()))+'-02-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate()))+'-02-01')) AS ����,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate())-1)+'-02-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-03-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate())-1)+'-02-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-03-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-02-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-02-01')) AS ȥ��  UNION ALL

SELECT '03' AS �·�	,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate()))+'-03-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate()))+'-04-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate()))+'-03-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate()))+'-04-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate()))+'-03-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate()))+'-03-01')) AS ����,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate())-1)+'-03-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-04-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate())-1)+'-03-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-04-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-03-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-03-01')) AS ȥ��  UNION ALL

SELECT '04' AS �·�	,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate()))+'-04-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate()))+'-05-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate()))+'-04-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate()))+'-05-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate()))+'-04-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate()))+'-04-01')) AS ����,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate())-1)+'-04-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-05-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate())-1)+'-04-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-05-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-04-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-04-01')) AS ȥ��  UNION ALL

SELECT '05' AS �·�	,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate()))+'-05-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate()))+'-06-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate()))+'-05-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate()))+'-06-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate()))+'-05-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate()))+'-05-01')) AS ����,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate())-1)+'-05-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-06-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate())-1)+'-05-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-06-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-05-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-05-01')) AS ȥ��  UNION ALL

SELECT '06' AS �·�	,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate()))+'-06-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate()))+'-07-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate()))+'-06-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate()))+'-07-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate()))+'-06-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate()))+'-06-01')) AS ����,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate())-1)+'-06-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-07-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate())-1)+'-06-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-07-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-06-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-06-01')) AS ȥ��  UNION ALL

SELECT '07' AS �·�	,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate()))+'-07-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate()))+'-08-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate()))+'-07-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate()))+'-08-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate()))+'-07-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate()))+'-07-01')) AS ����,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate())-1)+'-07-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-08-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate())-1)+'-07-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-08-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-07-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-07-01')) AS ȥ��  UNION ALL

SELECT '08' AS �·�	,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate()))+'-08-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate()))+'-09-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate()))+'-08-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate()))+'-09-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate()))+'-08-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate()))+'-08-01')) AS ����,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate())-1)+'-08-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-09-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate())-1)+'-08-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-09-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-08-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-08-01')) AS ȥ��  UNION ALL

SELECT '09' AS �·�	,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate()))+'-09-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate()))+'-10-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate()))+'-09-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate()))+'-10-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate()))+'-09-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate()))+'-09-01')) AS ����,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate())-1)+'-09-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-10-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate())-1)+'-09-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-10-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-09-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-09-01')) AS ȥ��  UNION ALL

SELECT '10' AS �·�	,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate()))+'-10-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate()))+'-11-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate()))+'-10-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate()))+'-11-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate()))+'-10-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate()))+'-10-01')) AS ����,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate())-1)+'-10-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-11-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate())-1)+'-10-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-11-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-10-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-10-01')) AS ȥ��  UNION ALL

SELECT '11' AS �·�	,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate()))+'-11-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate()))+'-12-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate()))+'-11-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate()))+'-12-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate()))+'-11-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate()))+'-11-01')) AS ����,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate())-1)+'-11-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-12-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate())-1)+'-11-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate())-1)+'-12-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-11-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-11-01')) AS ȥ��  UNION ALL

SELECT '12' AS �·�	,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate()))+'-12-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate())+1)+'-01-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate()))+'-12-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate())+1)+'-01-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate()))+'-12-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate()))+'-12-01')) AS ����,(SELECT convert(decimal,(select count(1)  from [dbo].[HR_Departure] where F_TrueDepartureDate> convert(char(4),DATEPART(yyyy, getdate())-1)+'-12-01' and F_TrueDepartureDate < convert(char(4),DATEPART(yyyy, getdate()))+'-01-01')) /convert(decimal,(count(1) +(select count(1)  from [dbo].[HR_Induction] where F_InductionDate>  convert(char(4),DATEPART(yyyy, getdate())-1)+'-12-01' and F_InductionDate < convert(char(4),DATEPART(yyyy, getdate()))+'-01-01')))  FROM [dbo].[HR_Induction] WHERE F_InductionDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-12-01' AND F_UserId NOT IN (SELECT F_UserId FROM [dbo].HR_Departure WHERE F_TrueDepartureDate< convert(char(4),DATEPART(yyyy, getdate())-1)+'-12-01')) AS ȥ��  
--------------------------------------------------------------------------------------
--ת����
select '1' as �·�,isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime >= convert(char(4),DATEPART(yyyy, getdate()))+'-01-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate()))+'-02-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as '����',
isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime >= convert(char(4),DATEPART(yyyy, getdate())-1)+'-01-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate())-1)+'-02-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as 'ȥ��'
UNION ALL
select '2' as �·�,isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-02-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate()))+'-03-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as '����',
isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate())-1)+'-02-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate())-1)+'-03-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as 'ȥ��'
UNION ALL
select '3' as �·�,isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-03-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate()))+'-04-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as '����',
isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate())-1)+'-03-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate())-1)+'-04-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as 'ȥ��'
UNION ALL
select '4' as �·�,isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-04-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate()))+'-05-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as '����',
isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate())-1)+'-04-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate())-1)+'-05-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as 'ȥ��'
UNION ALL
select '5' as �·�,isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-05-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate()))+'-06-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as '����',
isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate())-1)+'-05-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate())-1)+'-06-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as 'ȥ��'
UNION ALL
select '6' as �·�,isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-06-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate()))+'-07-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as '����',
isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate())-1)+'-06-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate())-1)+'-07-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as 'ȥ��'
UNION ALL
select '7' as �·�,isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-07-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate()))+'-08-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as '����',
isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate())-1)+'-07-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate())-1)+'-08-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as 'ȥ��'
UNION ALL
select '8' as �·�,isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-08-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate()))+'-09-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as '����',
isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate())-1)+'-08-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate())-1)+'-09-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as 'ȥ��'
UNION ALL
select '9' as �·�,isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-09-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate()))+'-10-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as '����',
isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate())-1)+'-09-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate())-1)+'-10-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as 'ȥ��'
UNION ALL
select '10' as �·�,isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-10-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate()))+'-11-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as '����',
isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate())-1)+'-10-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate())-1)+'-11-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as 'ȥ��'
UNION ALL
select '11' as �·�,isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-11-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate()))+'-12-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as '����',
isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate())-1)+'-11-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate())-1)+'-12-01' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as 'ȥ��'
UNION ALL
select '12' as �·�,isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate()))+'-12-01'
and  F_ThroughTime <= convert(char(4),DATEPART(yyyy, getdate()))+'-12-31' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as '����',
isnull((
(select count(1)*1.0 from 
(
select F_Id,F_UserId from HR_RecruitmentCandidates 
where 1=1 and F_IsInvitation=1 and F_ThroughTime > convert(char(4),DATEPART(yyyy, getdate())-1)+'-12-01'
and  F_ThroughTime < convert(char(4),DATEPART(yyyy, getdate())-1)+'-12-31' 
) as  redata
left join HR_PrepareRecruits pr on redata.F_UserId=pr.F_UserId
left join [dbo].[Base_User] us on pr.F_ADAccount=us.UserName
left join 
[dbo].[HR_FormalEmployees] f  on f.BaseUserId=us.id
and f.EmployRelStatus like '%��ʽԱ��%'
where 1=1 and f.F_Id is not null)
),0) as 'ȥ��'

-----------------------------------------------------------------
select (
select count(1)   from [dbo].[HR_FormalEmployees] where 1=1 and F_BusState=1 and EmployRelStatus in('����Ա��','��ʽԱ��','��פ','�������ù�','����Ա��������ת����','��ʽԱ����У�У�','��ʽԱ�������ۣ�','����')
) as ��ǰ������,
(select count(1) as ������ְ from [dbo].[HR_Induction] where convert(varchar(7),F_InductionDate,120)=convert(varchar(7),getdate(),120)) as ������ְ, 
(select count(1) from HR_Departure where convert(varchar(7),F_TrueDepartureDate,120)=convert(varchar(7),getdate(),120)) as ������ְ,
(select count(1) as ������ְ from [dbo].[HR_Induction] where convert(varchar(4),F_InductionDate,120)=convert(varchar(4),getdate(),120)) as ����ۼ���ְ, 
(select count(1) from HR_Departure where convert(varchar(4),F_TrueDepartureDate,120)=convert(varchar(4),getdate(),120)) as ����ۼ���ְ, 
(SELECT AVG( cast(datediff(yy,DirthDate,getdate()) as int))  from [dbo].[HR_FormalEmployees] where EmployRelStatus in('����Ա��','��ʽԱ��','��פ','�������ù�','����Ա��������ת����','��ʽԱ����У�У�','��ʽԱ�������ۣ�','����')) as ƽ������


	select pla.F_Level,(pla.��ʱ���+pla.�������) as �����,pla.δ���
	from (
  SELECT pro.F_Level,
				    (  SELECT 
               COUNT(1)
                  FROM Plan_PlanList t  
				  LEFT JOIN ProMainDataView t1 on t1.F_Id=t.F_ProjectMainId 
				  WHERE 1=1  
							 		${if(len(��ֹ��ʼʱ��)=0,''," and t.F_EndDate >= '"+��ֹ��ʼʱ��+"'")}
					${if(len(��ֹ����ʱ��)=0,''," and t.F_EndDate <= '"+��ֹ����ʱ��+"'")}
				  AND t.F_BusState!=-1  
				    ${if(len(��Ŀ)=0,''," and t1.F_FullId like '%"+��Ŀ+"%'")}
				  AND t.F_IsCheck = 1 AND (t.F_Level = ''+pro.F_Level+'' ) AND (t.F_BusState = 3 OR t.F_BusState = 4 OR t.F_BusState = 33)
				  ) as δ���,
			 (  SELECT 
               COUNT(1)
                  FROM Plan_PlanList t  
				  LEFT JOIN ProMainDataView t1 on t1.F_Id=t.F_ProjectMainId 
				  LEFT JOIN Plan_PlanDetail d on t.F_Id=d.F_PlanId
				  WHERE 1=1  
							 		${if(len(��ֹ��ʼʱ��)=0,''," and t.F_EndDate >= '"+��ֹ��ʼʱ��+"'")}
					${if(len(��ֹ����ʱ��)=0,''," and t.F_EndDate <= '"+��ֹ����ʱ��+"'")}
					  ${if(len(��Ŀ)=0,''," and t1.F_FullId like '%"+��Ŀ+"%'")}
				  AND t.F_BusState!=-1  
				  AND t.F_IsCheck = 1 AND (t.F_Level = ''+pro.F_Level+'' ) AND (t.F_BusState = 1 AND d.F_Delayed <= 0)
				  ) as ��ʱ���,
			(  SELECT 
               COUNT(1)
                  FROM Plan_PlanList t  
				  LEFT JOIN ProMainDataView t1 on t1.F_Id=t.F_ProjectMainId 
				  LEFT JOIN Plan_PlanDetail d on t.F_Id=d.F_PlanId
				  WHERE 1=1  
							 		${if(len(��ֹ��ʼʱ��)=0,''," and t.F_EndDate >= '"+��ֹ��ʼʱ��+"'")}
					${if(len(��ֹ����ʱ��)=0,''," and t.F_EndDate <= '"+��ֹ����ʱ��+"'")}
					  ${if(len(��Ŀ)=0,''," and t1.F_FullId like '%"+��Ŀ+"%'")}
				  AND t.F_BusState!=-1  
				  AND t.F_IsCheck = 1 AND (t.F_Level = ''+pro.F_Level+'' ) AND (t.F_BusState = 2 OR (t.F_BusState = 1 AND d.F_Delayed > 0))
				  ) as �������
  FROM Plan_PlanList  pro
  WHERE 1=1
  group by pro.F_Level
  ) pla

  SELECT * FROM [dbo].[�ѿ�δ��KPI�];
  delete from [dbo].[�ѿ�δ��KPI�];

  --
  select (
select count(1)   from [dbo].[HR_FormalEmployees] where EmployRelStatus in('����Ա��','��ʽԱ��','��פ','�������ù�','����Ա��������ת����','��ʽԱ����У�У�','��ʽԱ�������ۣ�','����')
) as ��ǰ������,
(select count(1) as ������ְ from [dbo].[HR_Induction] where convert(varchar(7),F_InductionDate,120)=convert(varchar(7),getdate(),120)) as ������ְ, 
(select count(1) from HR_Departure where convert(varchar(7),F_TrueDepartureDate,120)=convert(varchar(7),getdate(),120)) as ������ְ,
(select count(1) as ������ְ from [dbo].[HR_Induction] where convert(varchar(4),F_InductionDate,120)=convert(varchar(4),getdate(),120)) as ����ۼ���ְ, 
(select count(1) from HR_Departure where convert(varchar(4),F_TrueDepartureDate,120)=convert(varchar(4),getdate(),120)) as ����ۼ���ְ, 
(SELECT AVG( cast(datediff(yy,DirthDate,getdate()) as int))  from [dbo].[HR_FormalEmployees] where EmployRelStatus in('����Ա��','��ʽԱ��','��פ','�������ù�','����Ա��������ת����','��ʽԱ����У�У�','��ʽԱ�������ۣ�','����')) as ƽ������





SELECT kpi.*,signs.��ǩԼ*���KPI/���KPI���/10000 �������,
signs.��ǩԼ*���KPI/�¶�KPI���/10000 �������, 
signs.��ǩԼ*���KPI/�ܶ�KPI���/10000 �������   FROM (
 SELECT ��Ʒ,SUBSTRING(��Ŀ����,1,CHARINDEX('Ŀ',��Ŀ����,1))  ��Ŀ 
 , CASE WHEN ��Ʒ= '����' THEN SUM(��ǩԼ���� ) ELSE SUM(��ǩԼ��� ) END ��ǩԼ
 , CASE WHEN ��Ʒ= '����' THEN SUM(��ǩԼ���� ) ELSE SUM(��ǩԼ��� ) END ��ǩԼ
 , CASE WHEN ��Ʒ= '����' THEN SUM(��ǩԼ����) ELSE SUM(��ǩԼ��� ) END ��ǩԼ  
FROM(
SELECT    ��Ŀ����,  ҵ̬,  ¥��,  ��Ʒ,pb.BldFullName ʵ��¥�� , 
(SELECT ISNULL(SUM(sc.BldArea),0) ��ǩԼ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT ISNULL(SUM(sc.BldArea),0) ����ǩ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ���,
(SELECT ISNULL(SUM(sc.HtTotal),0) ��ǩԼ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT ISNULL(SUM(sc.HtTotal),0) ����ǩ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )
+ (SELECT ISNULL(SUM(sc.SjBcTotal),0) ���겹����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}'))  )
 - (SELECT ISNULL(SUM(sc.SjBcTotal),0) �����˲�����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) ��ǩԼ���,
(SELECT COUNT(1) ��ǩԼ����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT COUNT(1) ����ǩ����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ����,

(SELECT ISNULL(SUM(sc.BldArea),0) ��ǩԼ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT ISNULL(SUM(sc.BldArea),0) ����ǩ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ���,
(SELECT ISNULL(SUM(sc.HtTotal),0) ��ǩԼ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT ISNULL(SUM(sc.HtTotal),0) ����ǩ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )
+ (SELECT ISNULL(SUM(sc.SjBcTotal),0) ���²�����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}'))  )
 - (SELECT ISNULL(SUM(sc.SjBcTotal),0) �����˲�����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ���,
(SELECT COUNT(1) ��ǩԼ����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT COUNT(1) ����ǩ����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ����,

(SELECT ISNULL(SUM(sc.BldArea),0) ��ǩԼ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT ISNULL(SUM(sc.BldArea),0) ����ǩ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ���,
(SELECT ISNULL(SUM(sc.HtTotal),0) ��ǩԼ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT ISNULL(SUM(sc.HtTotal),0) ����ǩ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )
+ (SELECT ISNULL(SUM(sc.SjBcTotal),0) �ܲ�����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}'))  )
 - (SELECT ISNULL(SUM(sc.SjBcTotal),0) ���˲�����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ���,
(SELECT COUNT(1) ��ǩԼ����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT COUNT(1) ����ǩ����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ���� 
  FROM �ѿ�δ��KPI KPI left JOIN �ѿ�δ��¥�������� cfp ON cfp.KPIguid = KPI.ID left JOIN dbo.p_Building pb ON pb.BldGUID = cfp.bldguid WHERE CONVERT(date,f_createdate) = (SELECT * FROM  fn_LastWeekend('${dateEditor}'))
  ) counts GROUP BY ��Ʒ,SUBSTRING(��Ŀ����,1,CHARINDEX('Ŀ',��Ŀ����,1))  
    )signs INNER JOIN (  
SELECT ��Ʒ,SUBSTRING(��Ŀ����,1,CHARINDEX('Ŀ',��Ŀ����,1))  ��Ŀ,SUM(���KPI�����) ���KPI,SUM(�¶�KPI���) �¶�KPI,SUM(�ܶ�KPI���) �ܶ�KPI,SUM(���KPI���) ���KPI���,SUM(�¶�KPI���) �¶�KPI���,SUM(�ܶ�KPI���) �ܶ�KPI���    FROM  �ѿ�δ��KPI WHERE CONVERT(DATE,F_CREATEDATE) =  (SELECT * FROM  fn_LastWeekend('${dateEditor}'))  GROUP BY SUBSTRING(��Ŀ����,1,CHARINDEX('Ŀ',��Ŀ����,1)),��Ʒ
)kpi ON kpi.��Ŀ = signs.��Ŀ AND kpi.��Ʒ= signs.��Ʒ 


--������
 SELECT  CONVERT(VARCHAR(100),DATEADD(WEEK,DATEDIFF(WEEK,0,'${dateEditor}'),0),23) ��һ, 
 CONVERT(VARCHAR(100),DATEADD(DAY,6, DATEADD(WEEK,DATEDIFF(WEEK,0,'${dateEditor}'),0)),23) ��ĩ, 
 CONVERT(VARCHAR(100),DATEADD(DAY,-1,DATEADD(WEEK,DATEDIFF(WEEK,0,DATEADD(DAY,-1,'${dateEditor}')),0) ),23) ����ĩ

 	
SELECT * FROM dbo.�ѿ�δ��KPI�
where 1=1
   and   ����>=CONVERT(VARCHAR(100),DATEADD(WEEK,DATEDIFF(WEEK,0,'2021-07-30'),0),23) and ����<= CONVERT(VARCHAR(100),DATEADD(DAY,6, DATEADD(WEEK,DATEDIFF(WEEK,0,'2021-07-30'),0)),23)

 select * from [dbo].[�ѿ�δ��KPI�]
 where 1=1 and 
 ����>='2021-07-26' and ����<='2021-08-01'


 
SELECT kpi.*,signs.��ǩԼ*���KPI/���KPI���/10000 �������,
signs.��ǩԼ*���KPI/�¶�KPI���/10000 �������, 
signs.��ǩԼ*���KPI/�ܶ�KPI���/10000 �������   FROM (
 SELECT ��Ʒ,SUBSTRING(��Ŀ����,1,CHARINDEX('Ŀ',��Ŀ����,1))  ��Ŀ 
 , CASE WHEN ��Ʒ= '����' THEN SUM(��ǩԼ���� ) ELSE SUM(��ǩԼ��� ) END ��ǩԼ
 , CASE WHEN ��Ʒ= '����' THEN SUM(��ǩԼ���� ) ELSE SUM(��ǩԼ��� ) END ��ǩԼ
 , CASE WHEN ��Ʒ= '����' THEN SUM(��ǩԼ����) ELSE SUM(��ǩԼ��� ) END ��ǩԼ  
FROM(
SELECT    ��Ŀ����,  ҵ̬,  ¥��,  ��Ʒ,pb.BldFullName ʵ��¥�� , 
(SELECT ISNULL(SUM(sc.BldArea),0) ��ǩԼ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT ISNULL(SUM(sc.BldArea),0) ����ǩ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ���,
(SELECT ISNULL(SUM(sc.HtTotal),0) ��ǩԼ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT ISNULL(SUM(sc.HtTotal),0) ����ǩ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )
+ (SELECT ISNULL(SUM(sc.SjBcTotal),0) ���겹����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}'))  )
 - (SELECT ISNULL(SUM(sc.SjBcTotal),0) �����˲�����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) ��ǩԼ���,
(SELECT COUNT(1) ��ǩԼ����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT COUNT(1) ����ǩ����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(YEAR,DATEDIFF(YEAR,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ����,

(SELECT ISNULL(SUM(sc.BldArea),0) ��ǩԼ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT ISNULL(SUM(sc.BldArea),0) ����ǩ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ���,
(SELECT ISNULL(SUM(sc.HtTotal),0) ��ǩԼ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT ISNULL(SUM(sc.HtTotal),0) ����ǩ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )
+ (SELECT ISNULL(SUM(sc.SjBcTotal),0) ���²�����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}'))  )
 - (SELECT ISNULL(SUM(sc.SjBcTotal),0) �����˲�����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ���,
(SELECT COUNT(1) ��ǩԼ����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT COUNT(1) ����ǩ����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN DATEADD(month,DATEDIFF(month,0,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))),0) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ����,

(SELECT ISNULL(SUM(sc.BldArea),0) ��ǩԼ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT ISNULL(SUM(sc.BldArea),0) ����ǩ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ���,
(SELECT ISNULL(SUM(sc.HtTotal),0) ��ǩԼ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT ISNULL(SUM(sc.HtTotal),0) ����ǩ���  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )
+ (SELECT ISNULL(SUM(sc.SjBcTotal),0) �ܲ�����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}'))  )
 - (SELECT ISNULL(SUM(sc.SjBcTotal),0) ���˲�����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ���,
(SELECT COUNT(1) ��ǩԼ����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND qsdate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) ) - (SELECT COUNT(1) ����ǩ����  FROM dbo.s_Contract sc INNER JOIN p_room pr ON pr.RoomGUID = sc.RoomGUID WHERE pr.BldGUID = pb.bldguid AND closedate BETWEEN CONVERT(VARCHAR(100),DATEADD(DAY,-6,(SELECT * FROM  fn_LastWeekend('${dateEditor}'))  ),23) AND (SELECT * FROM  fn_LastWeekend('${dateEditor}')) )��ǩԼ���� 
  FROM �ѿ�δ��KPI KPI left JOIN �ѿ�δ��¥�������� cfp ON cfp.KPIguid = KPI.ID left JOIN dbo.p_Building pb ON pb.BldGUID = cfp.bldguid WHERE CONVERT(date,f_createdate) = (SELECT * FROM  fn_LastWeekend('${dateEditor}'))
  ) counts GROUP BY ��Ʒ,SUBSTRING(��Ŀ����,1,CHARINDEX('Ŀ',��Ŀ����,1))  
    )signs INNER JOIN (  
SELECT ��Ʒ,SUBSTRING(��Ŀ����,1,CHARINDEX('Ŀ',��Ŀ����,1))  ��Ŀ,SUM(���KPI�����) ���KPI,SUM(�¶�KPI���) �¶�KPI,SUM(�ܶ�KPI���) �ܶ�KPI,SUM(���KPI���) ���KPI���,SUM(�¶�KPI���) �¶�KPI���,SUM(�ܶ�KPI���) �ܶ�KPI���    FROM  �ѿ�δ��KPI WHERE CONVERT(DATE,F_CREATEDATE) =  (SELECT * FROM  fn_LastWeekend('${dateEditor}'))  GROUP BY SUBSTRING(��Ŀ����,1,CHARINDEX('Ŀ',��Ŀ����,1)),��Ʒ
)kpi ON kpi.��Ŀ = signs.��Ŀ AND kpi.��Ʒ= signs.��Ʒ 