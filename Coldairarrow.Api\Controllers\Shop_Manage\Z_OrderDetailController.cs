﻿using Coldairarrow.Business.Shop_Manage;
using Coldairarrow.Entity.Shop_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Newtonsoft.Json;
using Quartz.Util;
using sun.misc;

namespace Coldairarrow.Api.Controllers.Shop_Manage
{
    [Route("/Shop_Manage/[controller]/[action]")]
    public class Z_OrderDetailController : BaseApiController
    {
        #region DI

        public Z_OrderDetailController(IZ_OrderDetailBusiness z_OrderDetailBus, IZ_ProductsBusiness z_ProductsBus)
        {
            _z_OrderDetailBus = z_OrderDetailBus;
            _z_ProductsBus = z_ProductsBus;

        }
        IZ_ProductsBusiness _z_ProductsBus { get; }
        IZ_OrderDetailBusiness _z_OrderDetailBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Z_OrderDetail>> GetDataList(PageInput<ProductConditinDto> input)
        {
            return await _z_OrderDetailBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Z_OrderDetail> GetTheData(IdInputDTO input)
        {
            return await _z_OrderDetailBus.GetTheDataAsync(input.id);
        }
        /// <summary>
        /// 获取待收货数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<int> GetMyReceiveCount(IdInputDTO input)
        {
            return await _z_OrderDetailBus.GetMyReceiveCount(input.userId);
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Z_OrderDetail data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _z_OrderDetailBus.AddDataAsync(data);
            }
            else
            {
                await _z_OrderDetailBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _z_OrderDetailBus.DeleteDataAsync(ids);
        }

        [HttpPost]
        public async Task SubmitOrder(OtherInputDTO data)
        {
            if (!string.IsNullOrWhiteSpace(data.jsonStr))
            {
                var list = JsonConvert.DeserializeObject<List<Z_OrderDetail>>(data.jsonStr);
                await _z_OrderDetailBus.SubmitOrder(list);
            }
            else
            {
                throw new Exception("订单不能为空！");
            }
        }
        /// <summary>
        /// 批量修改状态
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task UpdateOrderState(RefundOrderDto data)
        {
            await _z_OrderDetailBus.UpdateOrderState(data);
        }
        /// <summary>
        /// 退款功能
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task RefundOrder(RefundOrderDto dto)
        {
            await _z_OrderDetailBus.RefundOrderAsync(dto);
        }
        /// <summary>
        /// 单价退款补差价功能
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task RefundOrderByPrice(RefundOrderDto dto)
        {
            await _z_OrderDetailBus.RefundOrderByPriceAsync(dto);
        }
        #endregion

        #region 购物车
        /// <summary>
        /// <summary>
        /// 获取购物车
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task<List<Z_OrderDetail>> GetShopCartList(Z_OrderDetail data)
        {
            List<Z_OrderDetail> orderDetails = new List<Z_OrderDetail>();
            //获取购物车
            if (data != null && !data.F_UserId.IsNullOrEmpty())
            {
                //查询
                var list = await RedisHelper.HGetAllAsync("SHOPCART:" + data.F_UserId);
                if (list != null && list.Count > 0)
                {
                    //查询所有的商品
                    var products = await _z_ProductsBus.GetDataByIdsAsync(list.ToList().Select(x => x.Key).ToList());
                    if (products.Count > 0)
                    {
                        products.ForEach(item =>
                        {
                            orderDetails.Add(new Z_OrderDetail()
                            {
                                F_ProductId = item.F_Id,
                                F_UserId = data.F_UserId,
                                ProductDetail = item,
                                F_ProdNum = list[item.F_Id].ToInt(),
                                F_Price = (decimal)list[item.F_Id].ToInt() * item.F_Price
                            });
                        });
                    }
                }
            }
            return orderDetails;
        }

        /// <summary>
        /// <summary>
        /// 获取购物车商品总数
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task<int> GetShopCartNum(Z_OrderDetail data)
        {
            var total = 0;
            //获取购物车
            if (data != null && !data.F_UserId.IsNullOrEmpty())
            {
                //查询
                var list = await RedisHelper.HGetAllAsync("SHOPCART:" + data.F_UserId);
                if (list != null && list.Count > 0)
                {
                    return list.ToList().Sum(x => x.Value.ToInt());
                }
            }
            return total;
        }

        /// <summary>
        /// <summary>
        /// 删除购物车
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task DeleteShopCart(OtherInputDTO other)
        {
            if (other.jsonStr != null)
            {
                var list = JsonConvert.DeserializeObject<List<Z_OrderDetail>>(other.jsonStr);
                //删除redis购物车
                await RedisHelper.HDelAsync("SHOPCART:" + list[0].F_UserId, list.Select(x => x.F_ProductId).ToArray());
            }
        }


        /// <summary>
        /// <summary>
        /// 更新购物车
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task UpdateShopCart(Z_OrderDetail data)
        {
            if (data != null && !data.F_UserId.IsNullOrEmpty() && !data.F_ProductId.IsNullOrWhiteSpace())
            {
                //删除redis购物车
                await RedisHelper.HSetAsync("SHOPCART:" + data.F_UserId, data.F_ProductId, data.F_ProdNum);
            }
        }

        /// <summary>
        /// 提交购物车
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task AddShopCart(Z_OrderDetail data)
        {
            if (data != null && !data.F_UserId.IsNullOrEmpty())
            {
                //查询是否有该商品
                var list = await RedisHelper.HGetAllAsync("SHOPCART:" + data.F_UserId);
                if (list == null || list.Count == 0)
                {
                    //添加redis购物车
                    //key  商品id   value  商品数量
                    await RedisHelper.HSetAsync("SHOPCART:" + data.F_UserId, data.F_ProductId, data.F_ProdNum);
                }
                else
                {
                    //在list中查询到该商品是否存在
                    if (list.Keys.Any(x => x == data.F_ProductId))
                    {
                        var F_ProdNum = list[data.F_ProductId].ToInt() + 1;
                        //数量加一
                        await RedisHelper.HSetAsync("SHOPCART:" + data.F_UserId, data.F_ProductId, F_ProdNum);
                    }
                    else
                    {
                        //key  商品id   value  商品数量
                        await RedisHelper.HSetAsync("SHOPCART:" + data.F_UserId, data.F_ProductId, data.F_ProdNum);
                    }
                }
            }
        }
        #endregion

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public async Task<FileContentResult> ExcelDownload(PageInput<ProductConditinDto> input)
        {
            try
            {
                input.PageRows = 10000;
                var pageResult = await _z_OrderDetailBus.GetDataListAsync(input);
                if (pageResult.Data.Count > 0)
                {
                    pageResult.Data.ForEach((item) =>
                    {
                        if (item.ProductDetail != null)
                        {
                            item.F_ProductName = item.ProductDetail.F_Name;
                            item.ProductDetail = null;
                        }
                    });

                }
                //取出数据源
                DataTable exportTable = pageResult.Data.ToDataTable();
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                //excelconfig.Title = "人员异动详情";
                //excelconfig.TitleFont = "微软雅黑";
                //excelconfig.TitlePoint = 20;
                excelconfig.FileName = "订单明细.xls";
                excelconfig.IsAllSizeColumn = true;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                var Sort = 1;
                excelconfig.ColumnEntity = new List<ColumnModel>
                {
                    new ColumnModel() { Column = "f_createdate", ExcelColumn = "创建时间", Sort = Sort++ },
                    new ColumnModel() { Column = "f_username", ExcelColumn = "员工名称", Sort = Sort++ },
                                new ColumnModel() { Column = "f_companyname", ExcelColumn = "所属公司", Sort = Sort++ },
                         new ColumnModel() { Column = "f_depname", ExcelColumn = "所属部门", Sort = Sort++ },
                    new ColumnModel() { Column = "f_productname", ExcelColumn = "商品名称",Sort = Sort++ },
                    new ColumnModel() { Column = "f_price", ExcelColumn = "商品单价", Sort = Sort++ },
                    new ColumnModel() { Column = "f_usenumber", ExcelColumn = "消费积分", Sort = Sort++ },
                    new ColumnModel() { Column = "f_prodnum", ExcelColumn = "商品数量", Sort = Sort++ },
                    new ColumnModel() { Column = "f_address", ExcelColumn = "派送地址", Sort = Sort++ },
                    new ColumnModel() { Column = "f_typename", ExcelColumn = "订单类型", Sort = Sort++ },
                    new ColumnModel() { Column = "f_statename", ExcelColumn = "订单状态", Sort = Sort++ },
                };
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public async Task<FileContentResult> ExcelDownloadByAdress(PageInput<ProductConditinDto> input)
        {
            try
            {
                input.PageRows = 10000;
                var pageResult = await _z_OrderDetailBus.GetDataListByAdressAsync(input);

                //取出数据源
                DataTable exportTable = pageResult.Data.ToDataTable();
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                //excelconfig.Title = "人员异动详情";
                //excelconfig.TitleFont = "微软雅黑";
                //excelconfig.TitlePoint = 20;
                excelconfig.FileName = "订单地址销售明细.xls";
                excelconfig.IsAllSizeColumn = true;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                var Sort = 1;
                excelconfig.ColumnEntity = new List<ColumnModel>
                {

                    new ColumnModel() { Column = "f_address", ExcelColumn = "派送地址", Sort = Sort++ },
                    new ColumnModel() { Column = "f_productname", ExcelColumn = "商品名称",Sort = Sort++ },
                    new ColumnModel() { Column = "f_price", ExcelColumn = "商品单价", Sort = Sort++ },
                    new ColumnModel() { Column = "f_usenumber", ExcelColumn = "消费积分", Sort = Sort++ },
                    new ColumnModel() { Column = "f_prodnum", ExcelColumn = "商品数量", Sort = Sort++ },
                };
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        /// <summary>
        /// 分部门Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public async Task<FileContentResult> ExcelDownloadByDep(PageInput<ProductConditinDto> input)
        {
            try
            {
                input.PageRows = 10000;
                var pageResult = await _z_OrderDetailBus.GetDataListByDepAsync(input);

                //取出数据源
                DataTable exportTable = pageResult.Data.ToDataTable();
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                //excelconfig.Title = "人员异动详情";
                //excelconfig.TitleFont = "微软雅黑";
                //excelconfig.TitlePoint = 20;
                excelconfig.FileName = "部门商品销售明细.xls";
                excelconfig.IsAllSizeColumn = true;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                var Sort = 1;
                excelconfig.ColumnEntity = new List<ColumnModel>
                {
                         new ColumnModel() { Column = "f_companyname", ExcelColumn = "所属公司", Sort = Sort++ },
                    new ColumnModel() { Column = "f_depname", ExcelColumn = "所属部门", Sort = Sort++ },
                    new ColumnModel() { Column = "f_productname", ExcelColumn = "商品名称",Sort = Sort++ },
                        new ColumnModel() { Column = "f_address", ExcelColumn = "派送地址", Sort = Sort++ },
                    new ColumnModel() { Column = "f_price", ExcelColumn = "商品单价", Sort = Sort++ },
                    new ColumnModel() { Column = "f_usenumber", ExcelColumn = "消费积分", Sort = Sort++ },
                    new ColumnModel() { Column = "f_prodnum", ExcelColumn = "商品数量", Sort = Sort++ },
                };
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        /// <summary>
        /// 分商品Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public async Task<FileContentResult> ExcelDownloadByGoods(PageInput<ProductConditinDto> input)
        {
            try
            {
                input.PageRows = 10000;
                var pageResult = await _z_OrderDetailBus.GetDataListByGoodsAsync(input);

                //取出数据源
                DataTable exportTable = pageResult.Data.ToDataTable();
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                //excelconfig.Title = "人员异动详情";
                //excelconfig.TitleFont = "微软雅黑";
                //excelconfig.TitlePoint = 20;
                excelconfig.FileName = "商品销售汇总明细.xls";
                excelconfig.IsAllSizeColumn = true;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                var Sort = 1;
                excelconfig.ColumnEntity = new List<ColumnModel>
                {
                   new ColumnModel() { Column = "f_productcode", ExcelColumn = "商品编码", Sort = Sort++ },
                    new ColumnModel() { Column = "f_productname", ExcelColumn = "商品名称", Sort = Sort++ },
                    new ColumnModel() { Column = "f_price", ExcelColumn = "单价",Sort = Sort++ },
                        new ColumnModel() { Column = "f_prodnum", ExcelColumn = "购买数量", Sort = Sort++ },
                    new ColumnModel() { Column = "f_usenumber", ExcelColumn = "消费积分", Sort = Sort++ },
                };
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Z_OrderDetail>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _z_OrderDetailBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}