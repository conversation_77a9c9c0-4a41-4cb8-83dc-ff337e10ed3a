﻿using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
namespace Coldairarrow.Api.Controllers.HR_Manage
{
    [Route("/HR_Manage/[controller]/[action]")]
    public class HR_FeeUserInfoController : BaseApiController
    {
        #region DI

        public HR_FeeUserInfoController(IHR_FeeUserInfoBusiness hR_FeeUserInfoBus)
        {
            _hR_FeeUserInfoBus = hR_FeeUserInfoBus;
        }

        IHR_FeeUserInfoBusiness _hR_FeeUserInfoBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_FeeUserInfo>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_FeeUserInfoBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_FeeUserInfo> GetTheData(IdInputDTO input)
        {
            return await _hR_FeeUserInfoBus.GetTheDataAsync(input.id);
        }
        [HttpPost]
        public async Task<HR_FeeUserInfo> GetTheDataByUser(IdInputDTO input)
        {
            return await _hR_FeeUserInfoBus.GetTheDataByUserAsync(input.userId);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_FeeUserInfo data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_FeeUserInfoBus.AddDataAsync(data);
            }
            else
            {
                await _hR_FeeUserInfoBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_FeeUserInfoBus.DeleteDataAsync(ids);
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public async Task<FileContentResult> ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            {
                input.PageRows = 10000;
                var pageResult = await _hR_FeeUserInfoBus.GetDataListAsync(input);
                //取出数据源
                DataTable exportTable = pageResult.Data.ToDataTable();
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                //excelconfig.Title = "人员异动详情";
                //excelconfig.TitleFont = "微软雅黑";
                //excelconfig.TitlePoint = 20;
                excelconfig.FileName = "员工工资卡信息明细.xls";
                excelconfig.IsAllSizeColumn = true;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                //              { title: '员工编号', dataIndex: 'F_UserCode', width: '10%' },
                //{ title: '人员名称', dataIndex: 'F_UserName', width: '10%' },
                //{ title: '所属公司', dataIndex: 'F_Company', width: '10%' },
                //{ title: '银行卡号', dataIndex: 'F_BankCard', width: '10%' },
                //{ title: '身份证', dataIndex: 'F_IdCard', width: '10%' },
                excelconfig.ColumnEntity = new List<ColumnModel>
                {
                    new ColumnModel() { Column = "f_usercode", ExcelColumn = "员工编号", Sort = 1 },
                    new ColumnModel() { Column = "f_username", ExcelColumn = "人员名称", Sort = 2 },
                    new ColumnModel() { Column = "f_company", ExcelColumn = "所属公司", Sort = 3 },
                    new ColumnModel() { Column = "f_bankcard", ExcelColumn = "银行卡号", Sort = 4 },
                            new ColumnModel() { Column = "phone", ExcelColumn = "手机号", Sort = 5 },
                    new ColumnModel() { Column = "f_idcard", ExcelColumn = "身份证", Sort = 5 }
                };

                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<AjaxResult> UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return Error("上传文件不能为空");

            if (Request.Form.Files.Count > 1)
                return Error("只能上传一个文件");

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            var res = await _hR_FeeUserInfoBus.ImportSaveData(physicPath, this.GetOperator());
            return res;
        }
        /// <summary>
        /// 模板下载
        /// </summary>
        [HttpPost]
        public FileContentResult DownloadTemplate()
        {
            string filePath = Directory.GetCurrentDirectory() + "/BusinessTemplate/员工工资卡号-通讯费报销使用.xls";
            if (FileHelper.Exists(filePath))
            {
                var bys = System.IO.File.ReadAllBytes(filePath);//excel表转换成流
                return File(bys, "application/vnd.ms-excel");
            }
            else
            {
                throw new BusException("找不到模板");
            }

        }
        #endregion
    }
}