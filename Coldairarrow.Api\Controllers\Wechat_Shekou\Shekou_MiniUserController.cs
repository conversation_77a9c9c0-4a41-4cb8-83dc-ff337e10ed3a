﻿using Coldairarrow.Business.Wechat_Shekou;
using Coldairarrow.Entity.Wechat_Shekou;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using System.Net.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Coldairarrow.Api.Controllers.Wechat_Shekou
{
    [Route("/Wechat_Shekou/[controller]/[action]")]
    public class Shekou_MiniUserController : BaseApiController
    {
        #region DI

        public Shekou_MiniUserController(IShekou_MiniUserBusiness shekou_MiniUserBus, IShekou_CompanyUserBusiness shekou_CompanyUserBus)
        {
            _shekou_MiniUserBus = shekou_MiniUserBus;
            _shekou_CompanyUserBus = shekou_CompanyUserBus;
        }

        IShekou_MiniUserBusiness _shekou_MiniUserBus { get; }
        IShekou_CompanyUserBusiness _shekou_CompanyUserBus{ get;}

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Shekou_MiniUser>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _shekou_MiniUserBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Shekou_MiniUser> GetTheData(IdInputDTO input)
        {
            return await _shekou_MiniUserBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Shekou_MiniUser data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _shekou_MiniUserBus.AddDataAsync(data);
            }
            else
            {
                await _shekou_MiniUserBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _shekou_MiniUserBus.DeleteDataAsync(ids);
        }

        #endregion
        

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _shekou_MiniUserBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Shekou_MiniUser>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _shekou_MiniUserBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion


        //获取openId
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetOpenid()
        {
            try
            {
                string code = HttpContext.Request.Form["code"].ToString();
                if (code.IsNullOrEmpty())
                {
                    return Error("微信服务器波动");
                }
                string appid = "wx42536bf0cb851082";
                string secret = "a0e3eb29b8e06d6de485d253fb1db198";
                string grant_type = "authorization_code";
                using (var httpClient = new HttpClient())
                {
                    //post
                    var url = new System.Uri("https://api.weixin.qq.com/sns/jscode2session");
                    var body = new FormUrlEncodedContent(new Dictionary<string, string>
                {
                    { "appid", appid},
                    { "secret", secret },
                    { "js_code", code},
                    { "grant_type",grant_type}

                });
                    // response
                    var response = httpClient.PostAsync(url, body).Result;
                    var data = response.Content.ReadAsStringAsync().Result;
                    JObject jo = (JObject)JsonConvert.DeserializeObject(data);
                    var pm = new
                    {
                        openid = jo["openid"].ToString(),
                        session_key = jo["session_key"].ToString()
                    };
                    return Success(pm);
                }
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }

        }
        //根据openId查询用户信息
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetUserInfoById()
        {
            try
            {
                string id = HttpContext.Request.Form["openId"].ToString();
                var user = _shekou_MiniUserBus.GetTheDataAsync(id).Result;
                if (user != null)
                {   
                    user.U_LastLoginTime = DateTime.Now;
                    _shekou_MiniUserBus.UpdateDataAsync(user).Wait();
                    return Success(user);
                }
                else
                {
                    var newUser = new Shekou_MiniUser();
                    newUser.F_Id = id;
                    newUser.U_IsAble = 1;
                    newUser.U_IsRegister = 0;
                    newUser.U_UserType = 0;
                    newUser.U_LastLoginTime = DateTime.Now;
                    newUser.CreateTime = DateTime.Now;
                    _shekou_MiniUserBus.AddDataAsync(newUser).Wait();
                    return Success(newUser);
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
        /// <summary>
        /// 手机号匹配验证登录
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetPhone()
        {
            try
            {
                string id = HttpContext.Request.Form["openId"].ToString();
                string mobile = HttpContext.Request.Form["mobile"].ToString();
                var usermobile = _shekou_CompanyUserBus.getUserByMobile(mobile);
                var user = _shekou_MiniUserBus.GetTheDataAsync(id).Result;
                if (user != null)
                {
                    if (usermobile != null)
                    {
                        user.U_Mobile = usermobile.Mobile;
                        user.U_RealName = usermobile.Username;
                        user.U_IsRegister = 1;
                        _shekou_MiniUserBus.UpdateDataAsync(user).Wait();
                    }
                    return Success(user.U_IsRegister);
                }
                else
                {
                    return Error("获取用户信息失败,请重试");
                }

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }
    }
}