﻿using Coldairarrow.Entity.HR_DataDictionaryManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HR_DataDictionaryManage
{
    public class HR_DataDictionaryClassBusiness : BaseBusiness<HR_DataDictionaryClass>, IHR_DataDictionaryClassBusiness, ITransientDependency
    {
        public HR_DataDictionaryClassBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口
        public async Task<PageResult<HR_DataDictionaryClass>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_DataDictionaryClass>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_DataDictionaryClass, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_DataDictionaryClass> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_DataDictionaryClass data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_DataDictionaryClass data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }
        /// <summary>
        /// 分类列表
        /// </summary>
        /// <returns></returns>
        public List<HR_DataDictionaryClass> GetClassifyList()
        {
            //缓存查询
            //List<DataItemEntity> list = cache.Read<List<DataItemEntity>>(cacheKeyClassify, CacheId.dataItem);
            //if (list == null || list.Count == 0)
            //{
            var list = GetIQueryable().ToList();
            //    cache.Write<List<DataItemEntity>>(cacheKeyClassify, list, CacheId.dataItem);
            //}

            return list;
        }
        /// <summary>
        /// 获取字典分类列表
        /// </summary>
        /// <param name="keyword">关键词（名称/编码）</param>
        /// <returns></returns>
        public List<HR_DataDictionaryClass> GetClassifyList(string keyword)
        {
            List<HR_DataDictionaryClass> list = GetClassifyList();

            if (!string.IsNullOrEmpty(keyword))
            {
                list = list.FindAll(t => t.F_ItemName.Contains(keyword) || t.F_ItemCode.Contains(keyword));
            }

            return list;
        }

        /// <summary>
        /// 获取分类树形数据
        /// </summary>
        /// <returns></returns>
        public List<DictionaryClassDTO> GetClassifyTree()
        {
            List<HR_DataDictionaryClass> classifyList = GetClassifyList().OrderBy(i=>i.F_SortCode).ToList();
            List<DictionaryClassDTO> treeList = new List<DictionaryClassDTO>();
            foreach (var item in classifyList)
            {
                DictionaryClassDTO node = new DictionaryClassDTO();
                node.Id = item.F_Id;
                node.Text = item.F_ItemName;
                node.Value = item.F_ItemCode;
                node.ParentId = item.F_ParentId;
                node.selectable = true;
                treeList.Add(node);
            }
            return TreeHelper.BuildTree(treeList);
        }
        /// <summary>
        /// 判断分类编号是否重复
        /// </summary>
        /// <param name="keyValue">主键</param>
        /// <param name="itemCode">编码</param>
        /// <returns></returns>
        public bool ExistItemCode(string keyValue, string itemCode)
        {
            bool res = false;
            List<HR_DataDictionaryClass> list = GetClassifyList();
            if (string.IsNullOrEmpty(keyValue))
            {
                res = list.FindAll(t => t.F_ItemCode.Equals(itemCode)).Count <= 0;
            }
            else
            {
                res = list.FindAll(t => t.F_ItemCode.Equals(itemCode) && !t.F_Id.Equals(keyValue)).Count <= 0;
            }

            return res;
        }

        /// <summary>
        /// 判断分类名称是否重复
        /// </summary>
        /// <param name="keyValue">主键</param>
        /// <param name="itemName">名称</param>
        /// <returns></returns>
        public bool ExistItemName(string keyValue, string itemName)
        {
            bool res = false;
            List<HR_DataDictionaryClass> list = GetClassifyList();
            if (string.IsNullOrEmpty(keyValue))
            {
                res = list.FindAll(t => t.F_ItemName.Equals(itemName)).Count <= 0;
            }
            else
            {
                res = list.FindAll(t => t.F_ItemName.Equals(itemName) && !t.F_Id.Equals(keyValue)).Count <= 0;
            }

            return res;
        }

        /// <summary>
        /// 通过编号获取字典分类实体
        /// </summary>
        /// <param name="itemCode">编码</param>
        /// <returns></returns>
        public HR_DataDictionaryClass GetClassifyEntityByCode(string itemCode)
        {
            List<HR_DataDictionaryClass> list = GetClassifyList();
            return list.Find(t => t.F_ItemCode.Equals(itemCode));
        }
        #endregion

        #region 私有成员

        #endregion
    }
}