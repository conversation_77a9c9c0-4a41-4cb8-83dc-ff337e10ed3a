﻿using Coldairarrow.Entity.HR_AttendanceManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using AutoMapper.QueryableExtensions;
using AutoMapper;
using System;
using System.Linq.Expressions;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Util.Helper;
using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.Entity;

namespace Coldairarrow.Business.HR_AttendanceManage
{
    public class HR_AttendanceBusiness : BaseBusiness<HR_Attendance>, IHR_AttendanceBusiness, ITransientDependency
    {
        readonly IMapper _mapper;
        public HR_AttendanceBusiness(IDbAccessor db, IMapper mapper)
            : base(db)
        {
            _mapper = mapper;
        }

        #region 外部接口

        public async Task<PageResult<HR_AttendanceDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_FormalEmployees, HR_Attendance, Base_Company, HR_AttendanceDTO>> select = (a1, d, c) => new HR_AttendanceDTO
            {
                F_Id = d.F_Id,
                CompanyName = c.F_FullName,
                NameUser = a1.NameUser,
                F_AttendanceType = d.F_AttendanceType,
                F_AttendanceDate = d.F_AttendanceDate,
                F_ShiftName = d.F_ShiftName,
                F_GoWorkDate = d.F_GoWorkDate,
                F_AfterWorkDate = d.F_AfterWorkDate,
                F_Code = d.F_Code,
                F_AttendanceSystem = d.F_AttendanceSystem,
            };
            var where = LinqHelper.True<HR_AttendanceDTO>();
            var search = input.Search;
            var q = from e in this.Db.GetIQueryable<HR_Attendance>().AsExpandable()
                    join a1 in this.Db.GetIQueryable<HR_FormalEmployees>() on e.F_UserId equals a1.F_Id into form
                    from a1 in form.DefaultIfEmpty()
                    join d in this.Db.GetIQueryable<Base_Company>() on a1.F_CompanyId equals d.F_Id into com
                    from d in com.DefaultIfEmpty()
                    select @select.Invoke(a1, e, d);
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_AttendanceDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_Attendance> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        public async Task<HR_AttendanceDTO> GetFormDataAsync(string id)
        {
            var attendance = await GetEntityAsync(id);
            var attendanceDTO = MapHelper.Mapping<HR_AttendanceDTO, HR_Attendance>(attendance);
            if (attendance != null && !string.IsNullOrWhiteSpace(attendance.F_UserId))
            {
                var formalEmployees = await this.Db.GetEntityAsync<HR_FormalEmployees>(attendance.F_UserId);
                attendanceDTO.NameUser = formalEmployees.NameUser ?? formalEmployees.NameUser;
                var company = await this.Db.GetEntityAsync<Base_Company>(formalEmployees.F_CompanyId);
                attendanceDTO.CompanyName = company.F_FullName ?? company.F_FullName;
            }
            return attendanceDTO;
        }

        public async Task AddDataAsync(HR_Attendance data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_Attendance data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_FormalEmployees, HR_Attendance, Base_Company, HR_AttendanceDTO>> select = (a1, d, c) => new HR_AttendanceDTO
            {
                F_Id = d.F_Id,
                CompanyName = c.F_FullName,
                NameUser = a1.NameUser,
                F_AttendanceType = d.F_AttendanceType,
                F_AttendanceDate = d.F_AttendanceDate,
                F_ShiftName = d.F_ShiftName,
                F_GoWorkDate = d.F_GoWorkDate,
                F_AfterWorkDate = d.F_AfterWorkDate,
                F_Code = d.F_Code,
                F_AttendanceSystem = d.F_AttendanceSystem,
            };
            var where = LinqHelper.True<HR_AttendanceDTO>();
            var search = input.Search;
            var q = from e in this.Db.GetIQueryable<HR_Attendance>().AsExpandable()
                    join a1 in this.Db.GetIQueryable<HR_FormalEmployees>() on e.F_UserId equals a1.F_Id into form
                    from a1 in form.DefaultIfEmpty()
                    join d in this.Db.GetIQueryable<Base_Company>() on a1.F_CompanyId equals d.F_Id into com
                    from d in com.DefaultIfEmpty()
                    select @select.Invoke(a1, e, d);
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_AttendanceDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }


            return q.Where(where).ToDataTable();
        }

        /// <summary>
        /// 获得考勤报表
        /// </summary>
        /// <param name="reportCondition"></param>
        /// <returns></returns>
        public PageResult<CheckWorkAttendanceDTO> GetAttendanceReport(PageInput<ReportConditionDTO> input)
        {
            if (input == null)
            {
                throw new BusException("参数错误");
            }
            if (!input.Search.year.HasValue)
            {
                throw new BusException("年份不能为空");
            }
            string[] empStates = { "被动离职", "离职", "派驻终止", "主动离职" };
            var search = input.Search;
            PageResult<CheckWorkAttendanceDTO> returnList = new PageResult<CheckWorkAttendanceDTO>();

            Expression<Func<HR_FormalEmployees, Base_Department, Base_Post, HR_FormalEmployeesDetailsDTO>> select = (e, d, p) => new HR_FormalEmployeesDetailsDTO
            {
                DepartmentName = d.Name,
                PostName = p.F_Name
            };
            select = select.BuildExtendSelectExpre();
            //获取假期额度员工ID
            var userIds = Db.GetIQueryable<HR_HolidayLine>().Where(x => x.F_EffectTime.Value.Year == search.year.Value.Year).Select(x => x.F_UserId).Distinct();

            var q = from e in Db.GetIQueryable<HR_FormalEmployees>().AsExpandable()
                    join de in Db.GetIQueryable<HR_Departure>() on e.F_Id equals de.F_UserId into depar
                    from de in depar.DefaultIfEmpty()
                    join d in Db.GetIQueryable<Base_Department>() on e.F_DepartmentId equals d.Id into dept
                    from d in dept.DefaultIfEmpty()
                    join p in Db.GetIQueryable<Base_Post>() on e.F_PositionId equals p.F_Id into post
                    from p in post.DefaultIfEmpty()
                    where userIds.Contains(e.F_Id) && (!empStates.Contains(e.EmployRelStatus) || (empStates.Contains(e.EmployRelStatus) && de.F_TrueDepartureDate.HasValue && de.F_TrueDepartureDate.Value.AddMonths(3) >= DateTime.Now))
                    select @select.Invoke(e, d, p);
            var where = LinqHelper.True<HR_FormalEmployeesDetailsDTO>();
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_FormalEmployeesDetailsDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            where = where.AndIf(!search.EmployRelStatus.IsNullOrEmpty(), x => x.EmployRelStatus.Contains(search.EmployRelStatus));

            var result = q.Where(where).GetPageResult(input);

            returnList.ErrorCode = result.ErrorCode;
            returnList.Msg = result.Msg;
            returnList.Success = result.Success;
            returnList.Total = result.Total;
            returnList.Data = new List<CheckWorkAttendanceDTO>();

            if (result.Data != null && result.Data.Count > 0)
            {
                var empIds = result.Data.Select(x => x.F_Id);
                //获取假期额度
                var jqList = Db.GetIQueryable<HR_HolidayLine>().Where(x => empIds.Contains(x.F_UserId) && x.F_EffectTime.Value.Year == search.year.Value.Year && x.F_WFState == (int)WFStates.完成流程).ToList();
                //获取请假数据
                var askList = Db.GetIQueryable<HR_AskLeave>().Where(x => empIds.Contains(x.F_UserId) && x.F_WFState == (int)WFStates.完成流程 && x.F_StartTime.Value.Year == search.year.Value.Year).ToList();
                var askIds = askList.Select(x => x.F_Id);
                //获取销假数据
                var terList = Db.GetIQueryable<HR_TermLeave>().Where(x => askIds.Contains(x.F_AskLeaveId) && x.F_WFState == (int)WFStates.完成流程).ToList();
                //获取排班上班数据
                var scheduList = Db.GetIQueryable<HR_Scheduling>().Where(x => empIds.Contains(x.F_UserId) && !x.F_WorkState.Contains("休") && !x.F_WorkState.Contains("假") && x.F_Year == search.year.Value.Year).ToList();
                //获取加班数据
                var workOvertimeList = Db.GetIQueryable<HR_WorkOvertime>().Where(x => empIds.Contains(x.F_UserId) && x.F_WFState == (int)WFStates.完成流程 && x.F_WorkODate.Value.Year == search.year.Value.Year).ToList();
                //获取日历
                var calendarList = Db.GetIQueryable<HR_Calendar>().Where(x => x.F_Year == search.year.Value.Year).ToList();
                //法定假天数
                int fdDay = calendarList.Where(x=> x.F_StartTime.HasValue && x.F_StartTime.Value.DayOfWeek != DayOfWeek.Sunday && x.F_StartTime.Value.DayOfWeek != DayOfWeek.Saturday && x.F_BusState == 1).Count() - calendarList.Where(x => x.F_StartTime.HasValue && (x.F_StartTime.Value.DayOfWeek == DayOfWeek.Sunday || x.F_StartTime.Value.DayOfWeek == DayOfWeek.Saturday) && x.F_BusState == 2).Count();
                //应出勤天数=年历天数－周六、日天数－法定假天数
                DateTime dec31 = new DateTime(search.year.Value.Year, 12, 31);
                //年历天数
                int daysInYear = dec31.DayOfYear;
                //周六、日天数
                int wmDay = GetWMDay(search.year.Value.Year);

                foreach (var item in result.Data)
                {
                    //应出勤天数
                    decimal DaysOnDuty = scheduList.Count();
                    if (DaysOnDuty == 0)
                    {
                        DaysOnDuty = daysInYear - wmDay- fdDay;
                    }
                    //加班天数
                    decimal workDay = Math.Round(workOvertimeList.Where(x => x.F_UserId == item.F_Id).Sum(x => x.F_ActualWorkOTime ?? 0) / 8m, 1);
                    //实际出勤天数 排班天数+加班天数-（请假天数-销假天数)
                    decimal ActualAttendanceDays = DaysOnDuty + workDay - (Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id).Sum(x => x.F_AskLeaveTime ?? 0)) - terList.Where(x => x.F_UserId == item.F_Id).Sum(x => x.F_RealAskLeaveTime ?? 0));
                    //工作日加班
                    decimal WorkingOvertime = Math.Round(workOvertimeList.Where(x => x.F_UserId == item.F_Id && x.F_WorkOType == "工作日加班").Sum(x => x.F_ActualWorkOTime ?? 0) / 8m, 1);
                    //休息日加班
                    decimal OvertimeOnRestDays = Math.Round(workOvertimeList.Where(x => x.F_UserId == item.F_Id && x.F_WorkOType == "休息日加班").Sum(x => x.F_ActualWorkOTime ?? 0) / 8m, 1);
                    //法定节假日加班
                    decimal HolidayOvertime = Math.Round(workOvertimeList.Where(x => x.F_UserId == item.F_Id && x.F_WorkOType == "法定节假日加班").Sum(x => x.F_ActualWorkOTime ?? 0) / 8m, 1);
                    //已休法定年假天数
                    decimal StateAnnualLeaveDayed = jqList.Where(x => x.F_HolidayTypes == "法定年假" && x.F_UserId == item.F_Id).Sum(x => x.F_UsedLine ?? 0);
                    //已休福利年假天数
                    decimal ComAnnualLeaveDayed = jqList.Where(x => x.F_HolidayTypes == "福利年假" && x.F_UserId == item.F_Id).Sum(x => x.F_UsedLine ?? 0);
                    //病假销假
                    var bjxjQuery = from t in terList
                                    join a in askList on t.F_AskLeaveId equals a.F_Id
                                    where t.F_UserId == item.F_Id && a.F_AskLeaveType == "病假" && a.F_IsTerminate == 1
                                    select t.F_RealAskLeaveTime;
                    //已休病假天数
                    decimal SickLeaveDayed = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "病假" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + bjxjQuery.Sum() ?? 0;
                    //产假销假
                    var cjxjQuery = from t in terList
                                    join a in askList on t.F_AskLeaveId equals a.F_Id
                                    where t.F_UserId == item.F_Id && a.F_AskLeaveType == "产假" && a.F_IsTerminate == 1
                                    select t.F_RealAskLeaveTime;
                    //已休产假天数
                    decimal MaternityLeaveDayed = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "病假" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + cjxjQuery.Sum() ?? 0;
                    //丧假销假
                    var sjxjQuery = from t in terList
                                    join a in askList on t.F_AskLeaveId equals a.F_Id
                                    where t.F_UserId == item.F_Id && a.F_AskLeaveType == "丧假" && a.F_IsTerminate == 1
                                    select t.F_RealAskLeaveTime;
                    //已休丧假天数
                    decimal FuneralLeaveDayed = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "丧假" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + sjxjQuery.Sum() ?? 0;
                    //婚假销假
                    var hjxjQuery = from t in terList
                                    join a in askList on t.F_AskLeaveId equals a.F_Id
                                    where t.F_UserId == item.F_Id && a.F_AskLeaveType == "婚假" && a.F_IsTerminate == 1
                                    select t.F_RealAskLeaveTime;
                    //已休婚假天数
                    decimal MarriageLeaveDayed = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "婚假" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + hjxjQuery.Sum() ?? 0;
                    //事假销假
                    var shijxjQuery = from t in terList
                                      join a in askList on t.F_AskLeaveId equals a.F_Id
                                      where t.F_UserId == item.F_Id && a.F_AskLeaveType == "事假" && a.F_IsTerminate == 1
                                      select t.F_RealAskLeaveTime;
                    //已休事假天数
                    decimal Leave = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "事假" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + shijxjQuery.Sum() ?? 0;
                    //陪产假销假
                    var pcjxjQuery = from t in terList
                                     join a in askList on t.F_AskLeaveId equals a.F_Id
                                     where t.F_UserId == item.F_Id && a.F_AskLeaveType == "陪产假" && a.F_IsTerminate == 1
                                     select t.F_RealAskLeaveTime;
                    //已休陪产假天数
                    decimal PaternityLeave = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "陪产假" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + pcjxjQuery.Sum() ?? 0;
                    //产检假销假
                    var cjjxjQuery = from t in terList
                                     join a in askList on t.F_AskLeaveId equals a.F_Id
                                     where t.F_UserId == item.F_Id && a.F_AskLeaveType == "产检假" && a.F_IsTerminate == 1
                                     select t.F_RealAskLeaveTime;
                    //已休产检假天数
                    decimal CJJDay = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "产检假" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + cjjxjQuery.Sum() ?? 0;
                    //会务培训其他假期销假
                    var ojxjQuery = from t in terList
                                    join a in askList on t.F_AskLeaveId equals a.F_Id
                                    where t.F_UserId == item.F_Id && a.F_AskLeaveType == "会务/培训/其他假期" && a.F_IsTerminate == 1
                                    select t.F_RealAskLeaveTime;
                    //会务培训其他假期
                    decimal OtherHolidays = Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id && x.F_AskLeaveType == "会务/培训/其他假期" && x.F_IsTerminate != 1).Sum(x => x.F_AskLeaveTime) ?? 0) + ojxjQuery.Sum() ?? 0;

                    CheckWorkAttendanceDTO model = new CheckWorkAttendanceDTO()
                    {
                        F_Id = item.F_Id,
                        Name = item.NameUser,
                        DepartmentName = item.DepartmentName,
                        PostName = item.PostName,
                        EmployeeStatus = item.EmployRelStatus,
                        DaysOnDuty = DaysOnDuty,
                        ActualAttendanceDays = ActualAttendanceDays,
                        StatutoryAnnualLeave = NumFilter(StateAnnualLeaveDayed),
                        WelfareAnnualLeave = NumFilter(ComAnnualLeaveDayed),
                        SickLeave = NumFilter(SickLeaveDayed),
                        MarriageLeave = NumFilter(MarriageLeaveDayed),
                        MaternityLeave = NumFilter(MaternityLeaveDayed),
                        EscortLeave = NumFilter(PaternityLeave),
                        Absenteeism = 0,
                        CompassionateLeave = NumFilter(Leave),
                        FuneralLeave = NumFilter(FuneralLeaveDayed),
                        ProductionInspectionFraud = NumFilter(CJJDay),
                        Other = NumFilter(OtherHolidays),
                        TotalOvertimeHours = workDay,
                        WorkingOvertime = WorkingOvertime,
                        OvertimeOnRestDays = OvertimeOnRestDays,
                        HolidayOvertime = HolidayOvertime,
                        Remarks = ""
                    };
                    returnList.Data.Add(model);
                }
            }

            return returnList;
        }
        #endregion

        #region 私有成员
        private int GetFDDay(List<HR_Calendar> calendarList)
        {
            int retNum = 0;

            if(calendarList != null)
            {
                foreach (var item in calendarList)
                {
                    if(item.F_StartTime.HasValue)
                    {
                        if(item.F_StartTime.Value.DayOfWeek != DayOfWeek.Sunday && item.F_StartTime.Value.DayOfWeek != DayOfWeek.Saturday)
                        {
                            if (item.F_BusState == 1)
                            {
                                retNum++;
                            }
                        }
                    }
                }
            }
            return retNum;
        }
        //获取某年的周六周日
        private int GetWMDay(int year)
        {
            int retNum = 0;
            DateTime counYear = Convert.ToDateTime(year + "-01-01");

            DateTime nestYear = counYear.AddYears(1);

            for (DateTime i = counYear; i < nestYear; i = i.AddDays(1))
            {
                if (i.DayOfWeek == DayOfWeek.Saturday || i.DayOfWeek == DayOfWeek.Sunday)
                {
                    retNum++;
                }
            }
            return retNum;
        }
        /// <summary>
        /// 如果小于0返回0
        /// </summary>
        /// <param name="num"></param>
        /// <returns></returns>
        private decimal NumFilter(decimal num)
        {
            decimal ret = 0;

            if (num > 0)
            {
                ret = num;
            }

            return ret;
        }

        #endregion
    }
}