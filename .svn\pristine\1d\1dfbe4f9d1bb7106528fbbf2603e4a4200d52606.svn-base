﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Aliyun.Base.xiaoxizn
{
    public class PersonModel
    {
        /// <summary>
        /// 人物画像预测请参照返回解析结果列表
        /// </summary>
        public Predicted_Result predicted_result { get; set; }
        /// <summary>
        /// 错误码请参照错误码汇总，解析成功则为 0
        /// </summary>
        public int errorcode { get; set; }
        public string src_id { get; set; }
        /// <summary>
        /// 简历语言zh, zh/en, en
        /// </summary>
        public string cv_language { get; set; }
        /// <summary>
        /// 简历来源zhilian, 51job
        /// </summary>
        public string src_site { get; set; }
        public string file_name { get; set; }
        /// <summary>
        /// 错误信息解析错误信息
        /// </summary>
        public string errormessage { get; set; }
        /// <summary>
        /// 简历ID系统缓存简历ID
        /// </summary>
        public string cv_id { get; set; }
        /// <summary>
        /// 英文解析结果parsing_result=1时返回，请参照返回解析结果列表，没有解析结果则为 {}
        /// </summary>
        public English_Parsing_Result english_parsing_result { get; set; }
        /// <summary>
        /// 简历头像图片所在URL如简历中头像图片为链接形式，保存头像所在链接URL，没有链接返回空字符串
        /// </summary>
        public string avatar_url { get; set; }
        public string updated_time { get; set; }
        /// <summary>
        /// 简历文件名系统缓存文件名
        /// </summary>
        public string cv_name { get; set; }
        /// <summary>
        /// 中文解析结果parsing_result=1时返回，请参照返回解析结果列表，没有解析结果则为 {}
        /// </summary>
        public Parsing_Result parsing_result { get; set; }
        /// <summary>
        /// 版本简历解析版本
        /// </summary>
        public string version { get; set; }
        public string client_id { get; set; }
        public string file_ext { get; set; }
        /// <summary>
        /// 简历头像图片BASE64格式的简历头像图片，没有头像返回空字符串
        /// </summary>
        public string avatar_data { get; set; }
        public string hash_id { get; set; }
    }

    public class Predicted_Result
    {
        /// <summary>
        /// desired_salary主观期望薪资一共九档 under_4000，4000_to_6000，6000_to_8000，8000_to_10000，10000_to_15000，15000_to_20000，20000_to_30000，30000_to_40000，40000+
        /// </summary>
        public object desired_salary { get; set; }
        public Tags tags { get; set; }
        public Predicted_Skills[] predicted_skills { get; set; }
        public Predicted_Industry predicted_industry { get; set; }
        public Predicted_Titles[] predicted_titles { get; set; }
        public Stability stability { get; set; }
        /// <summary>
        /// 总体能力值评价
        /// </summary>
        public Predicted_Capability predicted_capability { get; set; }
        public Highlights highlights { get; set; }
        /// <summary>
        /// 薪资预测一共九档 under_4000，4000_to_6000，6000_to_8000，8000_to_10000，10000_to_15000，15000_to_20000，20000_to_30000，30000_to_40000，40000+
        /// </summary>
        public string predicted_salary { get; set; }
        /// <summary>
        /// 跳槽率预测候选人现阶段跳槽概率，范围在0-1
        /// </summary>
        public float? predicted_turnover { get; set; }
        public Risks risks { get; set; }
    }

    public class Tags
    {
        public Skill[] skills { get; set; }
        public Professional[] professional { get; set; }
        public Education[] education { get; set; }
        public Basic[] basic { get; set; }
        public Others[] others { get; set; }
    }

    /// <summary>
    /// 技能标签
    /// </summary>
    public class Skill
    {
        /// <summary>
        /// 技能标签名称
        /// </summary>
        public string tag { get; set; }
        /// <summary>
        /// 标签类型
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 技能类型类别
        /// </summary>
        public string subclass { get; set; }
    }
    /// <summary>
    /// 职业标签
    /// </summary>
    public class Professional
    {
        public string tag { get; set; }
        public string type { get; set; }
    }
    /// <summary>
    /// 教育背景标签
    /// </summary>
    public class Education
    {
        public string tag { get; set; }
        public string type { get; set; }
    }
    /// <summary>
    /// 基本信息标签
    /// </summary>
    public class Basic
    {
        public string tag { get; set; }
        public string type { get; set; }
    }
    /// <summary>
    /// 其他信息标签
    /// </summary>
    public class Others
    {
        public string tag { get; set; }
        public string type { get; set; }
        /// <summary>
        /// 掌握程度，目前仅支持语言掌握程度
        /// </summary>
        public string level { get; set; }
    }
    /// <summary>
    /// 行业隶属度评价
    /// </summary>
    public class Predicted_Industry
    {
        /// <summary>互联网行业归属度0-10分数</summary>
        public float? 互联网 { get; set; }
        /// <summary>产品行业归属度0-10分数</summary>
        public float? 产品 { get; set; }
        /// <summary>人事/行政/高级管理行业归属度0-10分数</summary>
        public float? 人事_行政_高级管理 { get; set; }
        /// <summary>咨询/法律/公务员行业归属度0-10分数</summary>
        public float? 咨询_法律_公务员 { get; set; }
        /// <summary>工程师行业归属度0-10分数</summary>
        public float? 工程师 { get; set; }
        /// <summary>建筑/房地产行业归属度0-10分数</summary>
        public float? 建筑_房地产 { get; set; }
        /// <summary>教育/翻译/服务业行业归属度0-10分数</summary>
        public float? 教育_翻译_服务业 { get; set; }
        /// <summary>生产/采购/物流行业归属度0-10分数</summary>
        public float? 生产_采购_物流 { get; set; }
        /// <summary>生物/制药/医疗/护理行业归属度0-10分数</summary>
        public float? 生物_制药_医疗_护理 { get; set; }
        /// <summary>运营/客服/销售/市场行业归属度0-10分数</summary>
        public float? 运营_客服_销售_市场 { get; set; }
        /// <summary>金融行业归属度0-10分数</summary>
        public float? 金融 { get; set; }
        /// <summary>其他行业归属度0-10分数</summary>
        public float? 其他 { get; set; }

    }

    public class Stability
    {
        /// <summary>
        /// 每个职能的平均停留时间，没有换过职能则为Null
        /// </summary>
        public float? average_job_function_time { get; set; }
        /// <summary>
        /// 每个行业的平均停留时间，没有换过行业则为Null
        /// </summary>
        public float? average_industry_time { get; set; }
        /// <summary>
        /// 每段工作的平均停留时间，没有换过工作则为Null
        /// </summary>
        public float? average_work_time { get; set; }
        /// <summary>
        /// 综合判断该候选人的工作忠诚度
        /// </summary>
        public string work_stability { get; set; }
    }

    /// <summary>
    /// 总体能力值评价
    /// </summary>
    public class Predicted_Capability
    {
        /// <summary>
        /// 语言指数0-10分数
        /// </summary>
        public float? language { get; set; }
        /// <summary>
        /// 社会活动指数0-10分数
        /// </summary>
        public float? socialexp { get; set; }
        /// <summary>
        /// 领导力指数0-10分数
        /// </summary>
        public float? leadership { get; set; }
        /// <summary>
        /// 工作经历指数0-10分数
        /// </summary>
        public float? workexp { get; set; }
        /// <summary>
        /// 教育背景指数0-10分数
        /// </summary>
        public float? education { get; set; }
        /// <summary>
        /// 所获荣誉指数0-10分数
        /// </summary>
        public float? honor { get; set; }
    }
    /// <summary>
    /// 简历亮点分析
    /// </summary>
    public class Highlights
    {
        /// <summary>
        /// 项目经历亮点项目经历中发掘的亮点
        /// </summary>
        public object[] project { get; set; }
        /// <summary>
        /// 工作经历亮点工作经历中发掘的亮点
        /// </summary>
        public string[] occupation { get; set; }
        /// <summary>
        /// 学习经历亮点教育经历中发掘的亮点
        /// </summary>
        public string[] education { get; set; }
        /// <summary>
        /// 其他亮点
        /// </summary>
        public string[] others { get; set; }
        /// <summary>
        /// 亮点标签
        /// </summary>
        public string[] tags { get; set; }
    }

    public class Risks
    {
        /// <summary>
        /// 工作经历风险工作经历中存在的风险点
        /// </summary>
        public object[] occupation { get; set; }
        /// <summary>
        /// 学习经历风险点教育经历中存在的风险点
        /// </summary>
        public object[] education { get; set; }
        /// <summary>
        /// 风险点标签风险点标签
        /// </summary>
        public object[] tags { get; set; }
    }

    /// <summary>
    /// 候选人技能分析
    /// </summary>
    public class Predicted_Skills
    {
        /// <summary>
        /// 职业技能名称
        /// </summary>
        public string skill { get; set; }
        /// <summary>
        /// 职业技能分数对应职业技能分数式，范围在0-1
        /// </summary>
        public float score { get; set; }
    }
    /// <summary>
    /// 候选人职称分析
    /// </summary>
    public class Predicted_Titles
    {
        /// <summary>
        /// 职位分数职位名称对应分数式，范围在0-1
        /// </summary>
        public float score { get; set; }
        /// <summary>
        /// 多层次职位名称
        /// </summary>
        public Title title { get; set; }
    }

    public class Title
    {
        /// <summary>
        /// 第二级别职位名称
        /// </summary>
        public string l2 { get; set; }
        /// <summary>
        /// 第三级别职位名称
        /// </summary>
        public string l3 { get; set; }
        /// <summary>
        /// 第一级别职位名称
        /// </summary>
        public string l1 { get; set; }
    }

    public class English_Parsing_Result
    {
    }

    public class Parsing_Result
    {
        public Project_Experience[] project_experience { get; set; }
        public Contact_Info contact_info { get; set; }
        public Social_Experience[] social_experience { get; set; }
        public Education_Experience[] education_experience { get; set; }
        /// <summary>
        /// 基本信息
        /// </summary>
        public Basic_Info basic_info { get; set; }
        public Training_Experience[] training_experience { get; set; }
        public string resume_rawtext { get; set; }
        public Others1 others { get; set; }
        public Work_Experience[] work_experience { get; set; }
    }

    public class Contact_Info
    {
        /// <summary>手机国内11位手机号</summary>
        public string phone_number { get; set; }
        /// <summary>固定电话 *国内8位座机号</summary>
        public string home_phone_number { get; set; }
        /// <summary>邮箱合格邮箱地址</summary>
        public string email { get; set; }
        /// <summary>QQ号QQ号码</summary>
        public string QQ { get; set; }
        /// <summary>微信号 *微信号码</summary>
        public string wechat { get; set; }

    }

    /// <summary>
    /// 基本信息
    /// </summary>
    public class Basic_Info
    {
        /// <summary>姓名中文姓名</summary>
        public string name { get; set; }
        /// <summary>性别男/女</summary>
        public string gender { get; set; }
        /// <summary>开始工作年限四位数年份，如2013</summary>
        public string work_start_year { get; set; }
        /// <summary>身份证号中国居民身份证号码</summary>
        public string national_identity_number { get; set; }
        /// <summary>生日出生日期，如 1981-02-01，如只有年月，则为1981-02</summary>
        public string date_of_birth { get; set; }
        /// <summary>民族中国56个民族， 如 汉族，壮族</summary>
        public string ethnic { get; set; }
        /// <summary>所在地所在地城市或区域，如 北京西城，湖南长沙</summary>
        public string current_location { get; set; }
        /// <summary>详细地址具体家庭居住地，如 长沙市雨花区劳动西路1号</summary>
        public string detailed_location { get; set; }
        /// <summary>年龄当前年龄，整数</summary>
        public int? age { get; set; }
        /// <summary>工作经验当前工作年限，整数</summary>
        public int? num_work_experience { get; set; }
        /// <summary>当前公司</summary>
        public string current_company { get; set; }
        /// <summary>当前职位</summary>
        public string current_position { get; set; }
        /// <summary>毕业学校最高学历学校</summary>
        public string school_name { get; set; }
        /// <summary>毕业院校类别985 211/211/空值</summary>
        public string school_type { get; set; }
        /// <summary>学历最高学历 博士/MBA/EMBA/硕士/本科/大专/高中/中专/初中</summary>
        public string degree { get; set; }
        /// <summary>专业最高学历专业</summary>
        public string major { get; set; }
        /// <summary>期望职位</summary>
        public string desired_position { get; set; }
        /// <summary>当前薪水当前工资或当前工资范围，如 20000-30000元/月，15-30万人民币等，以简历为准</summary>
        public string current_salary { get; set; }
        /// <summary>期望薪水期望工资或期望工资范围，如 2001-2999元/月，10k-12k等，以简历为准</summary>
        public string desired_salary { get; set; }
        /// <summary>所属行业最新工作的所属行业</summary>
        public string industry { get; set; }
        /// <summary>期望行业候选人提及期望行业，本字段目前仅支持中文简历提取</summary>
        public string desired_industry { get; set; }
        /// <summary>求职状态在职，正在找工作/在职，考虑好的职业机会/在职，暂不考虑其他机会/已离职/应届生</summary>
        public string current_status { get; set; }
        /// <summary>政治面貌党员/团员/群众</summary>
        public string political_status { get; set; }
        /// <summary>婚姻状况未婚/已婚</summary>
        public string marital_status { get; set; }
        /// <summary>邮编 *6位数字邮编，如 510610</summary>
        public string zipcode { get; set; }
        /// <summary>籍贯籍贯地区，如 广东广州，洛阳，江苏。以简历描述为准</summary>
        public string birthplace { get; set; }
        /// <summary>期望工作地区期望工作城市或区域，多个地区则以英文逗号分隔，如 北京,上海</summary>
        public string expect_location { get; set; }
        /// <summary>简历解析完成时间简历解析请求完成的时间格式为 2020-10-09-06-23-15</summary>
        public string lastupdate_time { get; set; }
        /// <summary>最近毕业年份表示候选人最近一段学历毕业的年份，如2020</summary>
        public string recent_graduate_year { get; set; }
        /// <summary>候选人专业级别根据经历评估候选人所在级别，初级/中级/高级/资深，本字段目前仅支持中文简历提取</summary>
        public string professional_level { get; set; }

    }

    public class Others1
    {
        /// <summary>
        /// 
        /// </summary>
        public string[] language { get; set; }
        public string[] certificate { get; set; }
        public string[] skills { get; set; }
        public string self_evaluation { get; set; }
        public string[] awards { get; set; }
        public string[] it_skills { get; set; }
        public string[] business_skills { get; set; }
    }

    /// <summary>
    /// 项目经历
    /// </summary>
    public class Project_Experience
    {
        /// <summary>开始时间年份4位数年份，2014</summary>
        public string start_time_year { get; set; }
        /// <summary>开始时间月份2位数月份，01</summary>
        public string start_time_month { get; set; }
        /// <summary>结束时间年份4位数年份，2014</summary>
        public string end_time_year { get; set; }
        /// <summary>结束时间月份2位数月份，03</summary>
        public string end_time_month { get; set; }
        /// <summary>是否仍在1/0，1表示仍在继续该经历</summary>
        public int? still_active { get; set; }
        /// <summary>项目名称</summary>
        public string project_name { get; set; }
        /// <summary>项目所属公司本字段目前仅支持中文简历提取</summary>
        public string company_name { get; set; }
        /// <summary>地点项目地点城市或区域</summary>
        public string location { get; set; }
        /// <summary>职位名</summary>
        public string job_title { get; set; }
        /// <summary>职位所属标准职能如果使用了候选人画像，会根据画像模型自动推断项目标准职能，否则为空字符串</summary>
        public string job_function { get; set; }
        /// <summary>项目描述</summary>publicstringdescription{ get; set; }
        /// <summary>项目技能该段项目经历中用到技能，本字段目前仅支持中文简历提取</summary>publicsting[]skills{ get; set; }
        public string description { get; set; }

    }

    /// <summary>
    /// 培训经历
    /// </summary>
    public class Training_Experience
    {
        /// <summary>开始时间年份4位数年份，2014</summary>
        public string start_time_year { get; set; }
        /// <summary>开始时间月份2位数月份，01</summary>
        public string start_time_month { get; set; }
        /// <summary>结束时间年份4位数年份，2014</summary>
        public string end_time_year { get; set; }
        /// <summary>结束时间月份2位数月份，03</summary>
        public string end_time_month { get; set; }
        /// <summary>是否仍在1/0，1表示仍在继续该培训经历</summary>
        public int? still_active { get; set; }
        /// <summary>培训机构名</summary>
        public string organization_name { get; set; }
        /// <summary>地点</summary>
        public string location { get; set; }
        /// <summary>培训主题</summary>
        public string subject { get; set; }
        /// <summary>培训描述</summary>
        public string description { get; set; }

    }

    /// <summary>
    /// 社会经历
    /// </summary>
    public class Social_Experience
    {
        /// <summary>开始时间年份4位数年份，2013</summary>
        public string start_time_year { get; set; }
        /// <summary>开始时间月份2位数月份，03</summary>
        public string start_time_month { get; set; }
        /// <summary>结束时间年份4位数年份，2013</summary>
        public string end_time_year { get; set; }
        /// <summary>结束时间月份2位数月份，11</summary>
        public string end_time_month { get; set; }
        /// <summary>是否仍在1/0，1表示仍在其位</summary>
        public int? still_active { get; set; }
        /// <summary>公司/学校/社团名</summary>
        public string organization_name { get; set; }
        /// <summary>所属部门</summary>
        public string department { get; set; }
        /// <summary>地点</summary>
        public string location { get; set; }
        /// <summary>职位名</summary>
        public string job_title { get; set; }
        /// <summary>工作描述</summary>
        public string description { get; set; }

    }
    /// <summary>
    /// 教育经历
    /// </summary>
    public class Education_Experience
    {
        /// <summary>开始时间年份4位数年份，2006</summary>
        public string start_time_year { get; set; }
        /// <summary>开始时间月份2位数月份，06</summary>
        public string start_time_month { get; set; }
        /// <summary>结束时间年份4位数年份，2009</summary>
        public string end_time_year { get; set; }
        /// <summary>结束时间月份2位数月份，09</summary>
        public string end_time_month { get; set; }
        /// <summary>是否仍在1/0，1表示仍未毕业</summary>
        public int? still_active { get; set; }
        /// <summary>学校</summary>
        public string school_name { get; set; }
        /// <summary>学校级别985 211/211/空值</summary>
        public string school_level { get; set; }
        /// <summary>上课模式 *全职，兼职</summary>
        public string study_model { get; set; }
        /// <summary>地点城市名</summary>
        public string location { get; set; }
        /// <summary>学位博士/MBA/EMBA/硕士/本科/大专/高中/中专/初中</summary>
        public string degree { get; set; }
        /// <summary>专业</summary>
        public string major { get; set; }
        /// <summary>GPA</summary>
        public string GPA { get; set; }
        /// <summary>排名 *学生在年级排名</summary>
        public string ranking { get; set; }
        /// <summary>所学课程</summary>
        public string courses { get; set; }
        /// <summary>学院</summary>
        public string department { get; set; }
        /// <summary>学校排名中国大学最新排名</summary>
        public string school_rank { get; set; }
        /// <summary>是否海外院校1/0, 1表示海外院校与港澳台院校</summary>
        public int? abroad { get; set; }
        /// <summary>海外国家海外国家或地区名称，如中国大陆院校，则为空字符</summary>
        public string abroad_country { get; set; }

    }

    /// <summary>
    /// 工作经历
    /// </summary>
    public class Work_Experience
    {
        /// <summary>开始时间年份4位数年份，2016</summary>
        public string start_time_year { get; set; }
        /// <summary>开始时间月份2位数月份，01</summary>
        public string start_time_month { get; set; }
        /// <summary>结束时间年份4位数年份，2018</summary>
        public string end_time_year { get; set; }
        /// <summary>结束时间月份2位数月份，05</summary>
        public string end_time_month { get; set; }
        /// <summary>是否仍在1/0，1表示仍在其位</summary>
        public int? still_active { get; set; }
        /// <summary>公司名称</summary>
        public string company_name { get; set; }
        /// <summary>所属部门</summary>
        public string department { get; set; }
        /// <summary>地点</summary>
        public string location { get; set; }
        /// <summary>职位名</summary>
        public string job_title { get; set; }
        /// <summary>工作描述</summary>
        public string description { get; set; }
        /// <summary>公司行业公司所属行业，如果没有填写，且使用了候选人画像，则会根据画像模型自动推断所在公司行业</summary>
        public string industry { get; set; }
        /// <summary>职位职能职位所属职能</summary>
        public string job_function { get; set; }
        /// <summary>公司规模公司人数，100-499人，1000人以上等</summary>
        public string company_size { get; set; }
        /// <summary>公司类型公司类型，如民营，国家机关，个体等，具体以简历描述为准</summary>
        public string company_type { get; set; }
        /// <summary>工资水平该岗位工资水平，如，3000-5000元/月</summary>
        public string salary { get; set; }
        /// <summary>下属人数管理下属人数人数，10人</summary>
        public string underling_num { get; set; }
        /// <summary>汇报对象简历提及汇报对象，如总经理</summary>
        public string report_to { get; set; }
        /// <summary>工作技能该段工作经历中用到技能，本字段目前仅支持中文简历提取</summary>
        public string[] skills { get; set; }

    }

}
