
import { Axios } from './../../utils/plugin/axios-plugin'
import { message } from 'ant-design-vue/es'
import defaultSettings from '@/config/defaultSettings'
function dictTypeInfo (type) {
  return Axios.post('/HR_DataDictionaryManage/HR_DataDictionaryDetails/PostDetailList', { itemCode: type, keyword: '' }).then(resJson => {
    return Promise.resolve(resJson.Data)
  })
}
function fn1 () {
  console.log('全局方法一')
}
function fn2 () {
  console.log('全局方法二')
}
function fn3 () {
  console.log('全局方法三')
}
function fn4 () {
  console.log('全局方法四')
}
function colorFun (num) {
  switch (num) {
    //草稿
    case 0:
      return 'Draft'
    //审核中
    case 1:
      return 'Audit'
    //退回
    case 2:
      return 'Warning'
    //完成流程
    case 3:
      return 'Success'
    //结束流程
    case 4:
      return 'Over'
    //提交生效
    case 5:
      return 'Completion'
    default:
      return ''
  }
}

///时间加
function DateAdd (interval, number, date) {
  /* 
    *   功能:实现VBScript的DateAdd功能. 
    *   参数:interval,字符串表达式，表示要添加的时间间隔. 
    *   参数:number,数值表达式，表示要添加的时间间隔的个数. 
    *   参数:date,时间对象. 
    *   返回:新的时间对象. 
    *   var   now   =   new   Date(); 
    *   var   newDate   =   DateAdd( "d ",5,now); 
    *---------------   DateAdd(interval,number,date)   ----------------- 
    */
  switch (interval) {
    case "y ": {
      date.setFullYear(date.getFullYear() + number);
      return date;
      break;
    }
    case "q ": {
      date.setMonth(date.getMonth() + number * 3);
      return date;
      break;
    }
    case "m ": {
      date.setMonth(date.getMonth() + number);
      return date;
      break;
    }
    case "w ": {
      date.setDate(date.getDate() + number * 7);
      return date;
      break;
    }
    case "d ": {
      date.setDate(date.getDate() + number);
      return date;
      break;
    }
    case "h ": {
      date.setHours(date.getHours() + number);
      return date;
      break;
    }
    case "m ": {
      date.setMinutes(date.getMinutes() + number);
      return date;
      break;
    }
    case "s ": {
      date.setSeconds(date.getSeconds() + number);
      return date;
      break;
    }
    default: {
      date.setDate(d.getDate() + number);
      return date;
      break;
    }
  }
}
//流程跳转
function flowOpen (id, wfstate) {
  Axios.post('/HolidayManage/HR_AskLeave/GetOAToken', {}).then(resJson => {
    if (!!id) {
      if (resJson.Data) {
        console.log(process.env);
        var url = `${defaultSettings.OAUrl}workflow/request/ViewRequest.jsp?requestid=${id}&ssoToken=${resJson.Data}`
        console.log(url)
        window.open(url, '_blank')
      }
    }
    else {
      console.log(wfstate, wfstate === 5)
      if (wfstate === 5) {
        message.error('该流程为提交生效，不能查看流程详情!')
      }
      else {
        message.error('未发起流程!')
      }
    }
  })
}
function subStrFormat (data, max) {
  if (data != null && data != undefined && data.length > max) {
    return data.substr(0, max) + "...";
  } else {
    return data;
  }
}
// 将四个全局公共方法，组合成一个对象，并暴露出去
export default {
  dictTypeInfo,
  fn1,
  fn2,
  fn3,
  fn4,
  colorFun,
  flowOpen,
  subStrFormat
}
