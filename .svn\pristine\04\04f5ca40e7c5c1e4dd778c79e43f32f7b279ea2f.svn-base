﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Coldairarrow.Entity.Base_Manage
{
    /// <summary>
    /// Base_HWWhiteIp
    /// </summary>
    [Table("Base_HWWhiteIp")]
    public class Base_HWWhiteIp
    {

        /// <summary>
        /// F_Id
        /// </summary>
        [Key, Column(Order = 1)]
        public String F_Id { get; set; }

        /// <summary>
        /// F_CreateDate
        /// </summary>
        public DateTime? F_CreateDate { get; set; }

        /// <summary>
        /// ip
        /// </summary>
        public String F_Ip { get; set; }

        /// <summary>
        /// F_Remark
        /// </summary>
        public String F_Remark { get; set; }

    }
}