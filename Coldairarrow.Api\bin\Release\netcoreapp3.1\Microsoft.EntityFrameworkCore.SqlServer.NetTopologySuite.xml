<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite</name>
    </assembly>
    <members>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Design.Internal.SqlServerNetTopologySuiteDesignTimeServices">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Design.Internal.SqlServerNetTopologySuiteDesignTimeServices.ConfigureDesignTimeServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerNetTopologySuiteOptionsExtension">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerNetTopologySuiteOptionsExtension.ApplyServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerNetTopologySuiteOptionsExtension.Info">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerNetTopologySuiteOptionsExtension.Validate(Microsoft.EntityFrameworkCore.Infrastructure.IDbContextOptions)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerNTSStrings">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Internal.SqlServerNTSStrings.NTSServicesMissing">
            <summary>
                UseNetTopologySuite requires AddEntityFrameworkSqlServerNetTopologySuite to be called on the internal service provider used.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Query.Internal.SqlServerNetTopologySuiteMemberTranslatorPlugin">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" />. This means a single instance
                    is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" /> instances. The implementation must be thread-safe.
                    This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Query.Internal.SqlServerNetTopologySuiteMemberTranslatorPlugin.#ctor(Microsoft.EntityFrameworkCore.Storage.IRelationalTypeMappingSource,Microsoft.EntityFrameworkCore.Query.ISqlExpressionFactory)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Query.Internal.SqlServerNetTopologySuiteMemberTranslatorPlugin.Translators">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Query.Internal.SqlServerNetTopologySuiteMethodCallTranslatorPlugin">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> and multiple registrations
                    are allowed. This means a single instance of each service is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" />
                    instances. The implementation must be thread-safe.
                    This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Query.Internal.SqlServerNetTopologySuiteMethodCallTranslatorPlugin.#ctor(Microsoft.EntityFrameworkCore.Storage.IRelationalTypeMappingSource,Microsoft.EntityFrameworkCore.Query.ISqlExpressionFactory)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Query.Internal.SqlServerNetTopologySuiteMethodCallTranslatorPlugin.Translators">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Scaffolding.Internal.SqlServerNetTopologySuiteCodeGeneratorPlugin">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Scaffolding.Internal.SqlServerNetTopologySuiteCodeGeneratorPlugin.GenerateProviderOptions">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerGeometryTypeMapping`1">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerGeometryTypeMapping`1.#ctor(NetTopologySuite.NtsGeometryServices,System.String)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerGeometryTypeMapping`1.#ctor(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters,Microsoft.EntityFrameworkCore.Storage.ValueConversion.ValueConverter{`0,System.Data.SqlTypes.SqlBytes})">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerGeometryTypeMapping`1.Clone(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMapping.RelationalTypeMappingParameters)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerGeometryTypeMapping`1.GenerateNonNullSqlLiteral(System.Object)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerGeometryTypeMapping`1.GetDataReaderMethod">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerGeometryTypeMapping`1.AsText(System.Object)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerGeometryTypeMapping`1.GetSrid(System.Object)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="P:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerGeometryTypeMapping`1.WKTReaderType">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerGeometryTypeMapping`1.ConfigureParameter(System.Data.Common.DbParameter)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerNetTopologySuiteTypeMappingSourcePlugin">
            <summary>
                <para>
                    This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                    the same compatibility standards as public APIs. It may be changed or removed without notice in
                    any release. You should only use it directly in your code with extreme caution and knowing that
                    doing so can result in application failures when updating to a new Entity Framework Core release.
                </para>
                <para>
                    The service lifetime is <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> and multiple registrations
                    are allowed. This means a single instance of each service is used by many <see cref="T:Microsoft.EntityFrameworkCore.DbContext" />
                    instances. The implementation must be thread-safe.
                    This service cannot depend on services registered as <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" />.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerNetTopologySuiteTypeMappingSourcePlugin.#ctor(NetTopologySuite.NtsGeometryServices)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerNetTopologySuiteTypeMappingSourcePlugin.FindMapping(Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingInfo@)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServer.Storage.ValueConversion.Internal.GeometryValueConverter`1">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServer.Storage.ValueConversion.Internal.GeometryValueConverter`1.#ctor(NetTopologySuite.IO.SqlServerBytesReader,NetTopologySuite.IO.SqlServerBytesWriter)">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="T:Microsoft.EntityFrameworkCore.SqlServerNetTopologySuiteDbContextOptionsBuilderExtensions">
            <summary>
                NetTopologySuite specific extension methods for <see cref="T:Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder" />.
            </summary>
        </member>
        <member name="M:Microsoft.EntityFrameworkCore.SqlServerNetTopologySuiteDbContextOptionsBuilderExtensions.UseNetTopologySuite(Microsoft.EntityFrameworkCore.Infrastructure.SqlServerDbContextOptionsBuilder)">
            <summary>
                Use NetTopologySuite to access SQL Server spatial data.
            </summary>
            <param name="optionsBuilder"> The build being used to configure SQL Server. </param>
            <returns> The options builder so that further configuration can be chained. </returns>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.SqlServerNetTopologySuiteServiceCollectionExtensions">
            <summary>
                EntityFrameworkCore.SqlServer.NetTopologySuite extension methods for <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.SqlServerNetTopologySuiteServiceCollectionExtensions.AddEntityFrameworkSqlServerNetTopologySuite(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
                Adds the services required for NetTopologySuite support in the SQL Server provider for Entity Framework.
            </summary>
            <param name="serviceCollection"> The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to. </param>
            <returns> The same service collection so that multiple calls can be chained. </returns>
        </member>
    </members>
</doc>
