﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_PersonPhotoBusiness : BaseBusiness<HR_PersonPhoto>, IHR_PersonPhotoBusiness, ITransientDependency
    {
        public HR_PersonPhotoBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_PersonPhoto>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_PersonPhoto>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_PersonPhoto, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_PersonPhoto> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task<HR_PersonPhoto> GetTheDataByPersonIdAsync(string userId)
        {
            var q = GetIQueryable();
            return await q.Where(o => o.F_UserId == userId).FirstOrDefaultAsync();
        }

        public async Task AddDataAsync(HR_PersonPhoto data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_PersonPhoto data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_PersonPhoto>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_PersonPhoto, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_PersonPhoto, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }



        public string GetPersonPhoto(string UserId)
        {
            var q = GetIQueryable();
            var pe = q.Where(o => o.FPersonID == UserId).FirstOrDefault();
            if(pe==null)
            {
                return null;
            }
            else
            {
                return (Convert.ToBase64String(pe.FImageDataSource)); 
            }

        }
        #endregion

        #region 私有成员

        #endregion
    }
}