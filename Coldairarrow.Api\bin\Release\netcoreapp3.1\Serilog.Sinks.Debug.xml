<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Serilog.Sinks.Debug</name>
    </assembly>
    <members>
        <member name="T:Serilog.ConsoleLoggerConfigurationExtensions">
            <summary>
            Adds the WriteTo.Debug() extension method to <see cref="T:Serilog.LoggerConfiguration"/>.
            </summary>
        </member>
        <member name="M:Serilog.ConsoleLoggerConfigurationExtensions.Debug(Serilog.Configuration.LoggerSinkConfiguration,Serilog.Events.LogEventLevel,System.String,System.IFormatProvider,Serilog.Core.LoggingLevelSwitch)">
            <summary>
            Writes log events to <see cref="T:System.Diagnostics.Debug"/>.
            </summary>
            <param name="sinkConfiguration">Logger sink configuration.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <param name="outputTemplate">A message template describing the format used to write to the sink.
            the default is <code>"[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"</code>.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Serilog.ConsoleLoggerConfigurationExtensions.Debug(Serilog.Configuration.LoggerSinkConfiguration,Serilog.Formatting.ITextFormatter,Serilog.Events.LogEventLevel,Serilog.Core.LoggingLevelSwitch)">
            <summary>
            Writes log events to <see cref="T:System.Diagnostics.Debug"/>.
            </summary>
            <param name="sinkConfiguration">Logger sink configuration.</param>
            <param name="formatter">Controls the rendering of log events into text, for example to log JSON. To
            control plain text formatting, use the overload that accepts an output template.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
    </members>
</doc>
