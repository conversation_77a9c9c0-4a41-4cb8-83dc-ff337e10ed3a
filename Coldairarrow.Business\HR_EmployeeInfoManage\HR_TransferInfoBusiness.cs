﻿using AutoMapper;
using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HR_DataDictionaryManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HR_EmployeeInfoManage
{
    public class HR_TransferInfoBusiness : BaseBusiness<HR_TransferInfo>, IHR_TransferInfoBusiness, ITransientDependency
    {
        readonly IMapper _mapper;
        IConfiguration _configuration;
        IBase_DepartmentBusiness _departmentBusiness;
        public HR_TransferInfoBusiness(IDbAccessor db, IBase_DepartmentBusiness departmentBusiness, IConfiguration configuration, IMapper mapper)
            : base(db)
        {
            _configuration = configuration;
            _mapper = mapper;
            _departmentBusiness = departmentBusiness;
        }

        #region 外部接口

        /// <summary>
        /// 导出Excel
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public DataTable GetExcelListAsync(PageInput<AskLeaveConditionDTO> input)
        {
            Expression<Func<HR_TransferInfo, HR_FormalEmployees, HR_TransferInfoDTO>> select = (d, e) => new HR_TransferInfoDTO
            {
                EmpCode = e.EmployeesCode,
                EmpName = e.NameUser,
            };
            select = select.BuildExtendSelectExpre();
            var q = from d in this.Db.GetIQueryable<HR_TransferInfo>().AsExpandable()
                    join e in this.Db.GetIQueryable<HR_FormalEmployees>() on d.F_UserId equals e.F_Id into emp
                    from e in emp.DefaultIfEmpty()
                    select @select.Invoke(d, e);
            var where = LinqHelper.True<HR_TransferInfoDTO>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_TransferInfoDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            if (search.selectIds != null && search.selectIds.Count > 0)
            {
                where = where.And(x => search.selectIds.Contains(x.F_Id));
            }
            where = where.AndIf(search.wfState.HasValue, x => x.F_WFState == search.wfState);

            return q.Where(where).OrderBy(input.SortField, input.SortType).ToDataTable();
        }
        public async Task<PageResult<HR_TransferInfoDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_TransferInfo, HR_FormalEmployees, HR_TransferInfoDTO>> select = (d, e) => new HR_TransferInfoDTO
            {
                EmpCode = e.EmployeesCode,
                EmpName = e.NameUser,
            };
            select = select.BuildExtendSelectExpre();
            var q = from d in this.Db.GetIQueryable<HR_TransferInfo>().AsExpandable()
                    join e in this.Db.GetIQueryable<HR_FormalEmployees>() on d.F_UserId equals e.F_Id into emp
                    from e in emp.DefaultIfEmpty()
                    select @select.Invoke(d, e);
            var where = LinqHelper.True<HR_TransferInfoDTO>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_TransferInfoDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            where = where.AndIf(search.wfState.HasValue, x => x.F_WFState == search.wfState);

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_TransferInfo> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_TransferInfo data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_TransferInfo data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        /// <summary>
        /// 调动流程回调方法
        /// </summary>
        /// <param name="input"></param>
        //[Transactional]
        public void FlowCallBack(FlowInputDTO input)
        {
            if (input == null)
            {
                throw new BusException("参数错误");
            }
            var model = this.GetEntity(input.id);
            if (model == null)
            {
                throw new BusException("未找到对象");
            }
            if (model.F_WFState == (int)WFStates.取消流程)
            {
                throw new BusException("该流程已取消，不能修改状态");
            }
            if (model.F_WFState == (int)WFStates.完成流程)
            {
                throw new BusException("该流程已完成，不能修改状态");
            }
            if (model.F_WFState == (int)WFStates.提交生效)
            {
                throw new BusException("该流程已提交生效，不能修改状态");
            }
            model.F_WFState = input.status;
            this.Update(model);
            if (input.status == (int)WFStates.完成流程)
            {
                var emp = Db.GetEntity<HR_FormalEmployees>(model.F_UserId);
                if (emp != null)
                {
                    emp.F_PositionId = model.F_MobilizePositionId;
                    emp.F_DepartmentId = model.F_MobilizeDepId;
                    emp.F_Rank = model.F_MobilizeRank;
                    emp.ND = model.F_MobilizeProject;

                    Db.Update(emp);
                }

                //找到上一条任职经历，并修改他的结束时间
                var editEmploy = Db.GetIQueryable<HR_CompanyEmploy>().Where(x => x.F_UserId == model.F_UserId && x.F_WorkExpInOrOut == 0).OrderByDescending(x => x.F_StartTime.Value).FirstOrDefault();

                if (editEmploy != null)
                {
                    editEmploy.F_EndTime = model.F_ChangesDate;

                    Db.Update(editEmploy);
                }

                //生成企业任职经历
                HR_CompanyEmploy entity = new HR_CompanyEmploy();
                entity.F_IsSystemGen = 1;
                entity.F_StartTime = model.F_ChangesDate;
                entity.F_UserId = model.F_UserId;
                entity.F_Rank = model.F_MobilizeRank;
                entity.F_PositionId = model.F_MobilizePositionId;
                entity.F_PositionInfo = model.F_MobilizePosition;
                entity.F_ChangesOperating = model.F_ChangesOperating;
                entity.F_ChangesReason = model.F_ChangesReason;
                entity.F_ChangesType = model.F_ChangesType;
                entity.F_CompanyId = entity.F_CompanyId;
                entity.F_EmployRelStatus = model.F_MobilzOriEmplStatus;
                entity.F_OrganizeInfo = model.F_MobilizeDep;
                entity.F_OriginalEmplStatus = model.F_OriginalEmplStatus;
                entity.F_Id = Guid.NewGuid().ToString();
                entity.F_CreateDate = DateTime.Now;
                entity.F_CreateUserId = "System";
                entity.F_CreateUserName = "System";
                entity.F_WorkExpInOrOut = 0;
                entity.F_ForType = model.F_ForType;
                Db.Insert(entity);

            }
        }

        private bool ValidateDeptTransfer(HR_TransferInfoDTO data)
        {
            if (data.F_ChangesReason == "晋升" || data.F_ChangesReason == "降职" || data.F_ChangesReason == "其他")
            {
                return true;
            }

            var curRootDept = _departmentBusiness.GetRootDept(data.F_DepartmentId);
            var moveRootDept = _departmentBusiness.GetRootDept(data.F_MobilizeDepId);
            if (data.F_ChangesReason == "跨部门调动")
            {
                return curRootDept.Name != moveRootDept.Name;
            }
            else if (data.F_ChangesReason == "部门内调动")
            {
                return curRootDept.Name == moveRootDept.Name;
            }

            return true;
        }

        //[Transactional]
        public bool CreateFlow(HR_TransferInfoDTO data, string url, IOperator op)
        {
            if (ValidateFlowing(data.F_UserId))
            {
                throw new BusException($"该员工已存在调任在途流程，不能重复发起");
            }
            if (!ValidateDeptTransfer(data))
            {
                throw new BusException("变动原因选择错误");
            }
            bool ret = false;
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data, op);
                //晋升或降职直接提交生效
                if (data.F_ChangesReason == "晋升" || data.F_ChangesReason == "降职" || data.F_ChangesReason == "其他" || data.F_ChangesReason == "部门内调动")
                {
                    data.F_WFState = (int)WFStates.提交生效;
                }
                else
                {
                    data.F_WFState = (int)WFStates.草稿;
                }

                Add(data);
            }
            else
            {
                UpdateEntity(data, op);
                Edit(data);
            }
            if (data.F_ChangesReason == "晋升" || data.F_ChangesReason == "降职" || data.F_ChangesReason == "其他" || data.F_ChangesReason == "部门内调动")
            {
                var emp = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
                if (emp != null)
                {
                    emp.F_Rank = data.F_MobilizeRank;
                    emp.F_PositionId = data.F_MobilizePositionId;
                    emp.F_DepartmentId = data.F_MobilizeDepId;
                    emp.ND = data.F_MobilizeProject;
                    Db.Update(emp);
                }

                //找到上一条任职经历，并修改他的结束时间
                var editEmploy = Db.GetIQueryable<HR_CompanyEmploy>().Where(x => x.F_UserId == data.F_UserId && x.F_WorkExpInOrOut == 0).OrderByDescending(x => x.F_StartTime.Value).FirstOrDefault();

                if (editEmploy != null)
                {
                    editEmploy.F_EndTime = data.F_ChangesDate;

                    Db.Update(editEmploy);
                }
                //生成企业任职经历
                HR_CompanyEmploy entity = new HR_CompanyEmploy();
                entity.F_IsSystemGen = 2;
                entity.F_StartTime = data.F_ChangesDate;
                entity.F_UserId = data.F_UserId;
                entity.F_Rank = data.F_MobilizeRank;
                entity.F_PositionId = data.F_MobilizePositionId;
                entity.F_PositionInfo = data.F_MobilizePosition;
                entity.F_ChangesOperating = data.F_ChangesOperating;
                entity.F_ChangesReason = data.F_ChangesReason;
                entity.F_ChangesType = data.F_ChangesType;
                entity.F_CompanyId = entity.F_CompanyId;
                entity.F_EmployRelStatus = data.F_MobilzOriEmplStatus;
                entity.F_OrganizeInfo = data.F_MobilizeDep;
                entity.F_OriginalEmplStatus = data.F_OriginalEmplStatus;
                entity.F_Id = Guid.NewGuid().ToString();
                entity.F_CreateDate = DateTime.Now;
                entity.F_CreateUserId = "System";
                entity.F_CreateUserName = "System";
                entity.F_WorkExpInOrOut = 0;
                entity.F_ForType = data.F_ForType;
                Db.Insert(entity);

                return true;
            }
            else
            {
                //string token = string.Empty;
                int? IsDirectReports = null;
                Base_User user = null;
                var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
                if (formalEmployees != null)
                {
                    user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == formalEmployees.BaseUserId);
                    //token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
                    var post = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == formalEmployees.F_PositionId);
                    IsDirectReports = post?.F_IsDirectReports??0;
                }
                var entity = Db.GetIQueryable<Base_FileInfo>().Where(i => i.F_FileFolderId == data.F_FileId).Select(x => new FileInfoDTO() { Id = x.F_Id, FileName = x.F_FileName, FilePath = x.F_FilePath }).ToList();
                //var user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == data.F_CreateUserId);
                string token = JWTHelper.GetBusinessToken(data.F_Id, op.ADName);
                // var currentDep = "";
                var MobilizeDep = "";
                var depList = Db.GetIQueryable<Base_Department>().ToList();
                //var depmodel = depList.FirstOrDefault(i => i.Id == data.F_DepartmentId);
                //if (!string.IsNullOrWhiteSpace(depmodel.ParentId))
                //{
                //    depmodel = depList.FirstOrDefault(i => i.Id == depmodel.ParentId);
                //}
                //currentDep = depmodel.Name;
                var depmodel = depList.FirstOrDefault(i => i.Id == data.F_MobilizeDepId);
                if (!string.IsNullOrWhiteSpace(depmodel.ParentId))
                {
                    depmodel = depList.FirstOrDefault(i => i.Id == depmodel.ParentId);
                }
                var olddepmodel = depList.FirstOrDefault(i => i.Id == data.F_DepartmentId);
                if (!string.IsNullOrWhiteSpace(olddepmodel.ParentId))
                {
                    olddepmodel = depList.FirstOrDefault(i => i.Id == olddepmodel.ParentId);
                }
                //获取之前的一级项目
                var oldproject = "";
                if (!formalEmployees.ND.IsNullOrEmpty())
                {
                    oldproject = this.Db.GetIQueryable<HR_DataDictionaryDetails>()
                              .FirstOrDefault(i => i.F_ItemValue == formalEmployees.ND).F_OAValue;
                }
                MobilizeDep = depmodel.Name;
                var projectName = this.Db.GetIQueryable<HR_DataDictionaryDetails>().FirstOrDefault(i => i.F_ItemValue == data.F_MobilizeProject)?.F_OAValue;
                Dictionary<string, object> paramters = new Dictionary<string, object>();
                paramters.Add("reqCode", token);
                paramters.Add("reqNo", data.F_Id);
                paramters.Add("applyPerson", op.ADName); //申请人，比如说纪殿凯发的流程，申请人就传jidk
                paramters.Add("formId", _configuration["OAFormId:TransferInfoFlow"]);
                Dictionary<string, object> hrData = new Dictionary<string, object>();
                hrData.Add("change_person", data.EmpName);   //调动员工
                hrData.Add("remove_employees", data.EmpCode);   //调动员工编码
                hrData.Add("phone", data.MobilePhone); //联系电话
                hrData.Add("current_job", data.F_CurrentPosition); //现任职位
                hrData.Add("remove_job", data.F_MobilizePosition);   //调动后职位
                hrData.Add("current_dept", data.F_Department); //现任部门
                hrData.Add("remove_dept", data.F_MobilizeDep);   //调动后部门

                hrData.Add("old_dept", olddepmodel.Name); //调动前一级部门 
                hrData.Add("new_dept", MobilizeDep);   //调动后一级部门
                hrData.Add("change_person_ad", user?.UserName);   //调动员工AD账号

                #region 调到后部门新拼接
                //当一级部门是工程部，且岗位名称包含精装两字，部门传递为精装工程组
                if (!string.IsNullOrWhiteSpace(MobilizeDep) && MobilizeDep.Equals("工程部") && data.F_MobilizePosition.Contains("精装"))
                {
                    hrData.Add("new_dept_name", "精装工程组");//调动后新部门拼接
                }
                else
                {
                    hrData.Add("new_dept_name", "");//调动后新部门拼接
                }
                #endregion
                hrData.Add("current_job_level", data.F_CurrentRank); //现任职级
                hrData.Add("remove_job_level", data.F_MobilizeRank);   //调动后职级
                hrData.Add("effective_date", data.F_ChangesDate.Value.ToString("yyyy-MM-dd")); //生效日期
                hrData.Add("note", data.F_Remark);  //备注
                hrData.Add("old_employ_status", data.F_OriginalEmplStatus); //原用工状态
                hrData.Add("target_employ_status", data.F_MobilzOriEmplStatus);  //目标用工状态
                hrData.Add("change_operating", data.F_ChangesOperating);  //变动操作
                hrData.Add("change_type", data.F_ChangesType);   //变动类型
                hrData.Add("change_reason", data.F_ChangesReason); //变动原因
                hrData.Add("apply_date", data.F_CreateDate.ToString("yyyy-MM-dd"));     //申请日期:格式yyyy-MM-dd
                hrData.Add("files", entity);
                hrData.Add("old_project", oldproject);  //调动前所属项目
                hrData.Add("project", projectName);  //调入项目
                hrData.Add("is_under_general_manager", IsDirectReports);//是否是总经理及直系下属

                var afterpost = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == data.F_MobilizePositionId);
                hrData.Add("is_under_general_manager_after", afterpost?.F_IsDirectReports??0);//调动后是否总经理直接下属

                var curRootDept = _departmentBusiness.GetRootDept(data.F_DepartmentId);
                var moveRootDept = _departmentBusiness.GetRootDept(data.F_MobilizeDepId);
                int transfer_type = 0;
                if (curRootDept.Name != moveRootDept.Name)
                {
                    transfer_type = 1;
                }
                hrData.Add("transfer_type", transfer_type);//调动类型：transfer_type  0：部门内调动，1：部门间调动
                paramters.Add("hrData", hrData);

                string action = "create";
                string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);

                var retObj = str.ToObject<Dictionary<string, string>>();
                if (retObj != null)
                {
                    if (retObj["errCode"] == "0000")
                    {
                        data.F_WFId = retObj["oaReqId"];
                        data.F_WFState = (int)WFStates.审核中;
                        Update(data);
                        ret = true;
                    }
                    else
                    {
                        throw new BusException(retObj["errDesc"]);
                    }
                }
                else
                {
                    throw new BusException("创建流程失败");
                }
            }


            return ret;
        }

        /// <summary>
        /// 提交、退回流程
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <param name="act">submit 提交  subnoback 提交不需回复   subback 提交需要回复  reject退回</param>
        /// <returns>是否成功</returns>
        //[Transactional]
        public bool ActWorkflow(HR_TransferInfoDTO data, string url, IOperator op, string act = "submit")
        {
            if (!data.F_Id.IsNullOrEmpty())
            {
                UpdateEntity(data, op);
                Edit(data);
            }
            bool ret = false;

            //string token = string.Empty;
            //Base_User user = null;
            //var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
            //if (formalEmployees != null)
            //{
            //    user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == formalEmployees.BaseUserId);
            //    token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
            //}
            var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
            Base_User user = null;
            int? IsDirectReports = null;
            if (formalEmployees != null)
            {
                user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == formalEmployees.BaseUserId);
                //token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
                var post = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == formalEmployees.F_PositionId);
                IsDirectReports = post?.F_IsDirectReports ?? 0;
            }
            var entity = Db.GetIQueryable<Base_FileInfo>().Where(i => i.F_FileFolderId == data.F_FileId).Select(x => new FileInfoDTO() { Id = x.F_Id, FileName = x.F_FileName, FilePath = x.F_FilePath }).ToList();
            //var user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == data.F_CreateUserId);
            string token = JWTHelper.GetBusinessToken(data.F_Id, op.ADName);
            // var currentDep = "";
            //var MobilizeDep = "";
            var depList = Db.GetIQueryable<Base_Department>().ToList();
            //var depmodel = depList.FirstOrDefault(i => i.Id == data.F_DepartmentId);
            //if (!string.IsNullOrWhiteSpace(depmodel.ParentId))
            //{
            //    depmodel = depList.FirstOrDefault(i => i.Id == depmodel.ParentId);
            //}
            //currentDep = depmodel.Name;
            var depmodel = depList.FirstOrDefault(i => i.Id == data.F_MobilizeDepId);
            if (!string.IsNullOrWhiteSpace(depmodel.ParentId))
            {
                depmodel = depList.FirstOrDefault(i => i.Id == depmodel.ParentId);
            }
            var olddepmodel = depList.FirstOrDefault(i => i.Id == data.F_DepartmentId);
            if (!string.IsNullOrWhiteSpace(olddepmodel.ParentId))
            {
                olddepmodel = depList.FirstOrDefault(i => i.Id == olddepmodel.ParentId);
            }
            //获取之前的一级项目
            var oldproject = "";
            if (!formalEmployees.ND.IsNullOrEmpty())
            {
                oldproject = this.Db.GetIQueryable<HR_DataDictionaryDetails>()
                          .FirstOrDefault(i => i.F_ItemValue == formalEmployees.ND).F_OAValue;
            }
            var MobilizeDep = depmodel.Name;
            var projectName = this.Db.GetIQueryable<HR_DataDictionaryDetails>().FirstOrDefault(i => i.F_ItemValue == data.F_MobilizeProject)?.F_OAValue;
            var model = this.GetEntity(data.F_Id);
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            paramters.Add("oaId", model.F_WFId);
            paramters.Add("reqCode", token);
            paramters.Add("reqNo", data.F_Id);
            paramters.Add("applyPerson", op.ADName); //申请人，比如说纪殿凯发的流程，申请人就传jidk
            paramters.Add("formId", _configuration["OAFormId:TransferInfoFlow"]);
            //paramters.Add("sign", "");
            paramters.Add("act", act);
            Dictionary<string, object> hrData = new Dictionary<string, object>();
            hrData.Add("change_person", data.EmpName);   //调动员工
            hrData.Add("remove_employees", data.EmpCode);   //调动员工编码
            hrData.Add("phone", data.MobilePhone); //联系电话
            hrData.Add("current_job", data.F_CurrentPosition); //现任职位
            hrData.Add("remove_job", data.F_MobilizePosition);   //调动后职位
            hrData.Add("current_dept", data.F_Department); //现任部门
            hrData.Add("remove_dept", data.F_MobilizeDep);   //调动后部门

            hrData.Add("old_dept", olddepmodel.Name); //调动前一级部门 
            hrData.Add("new_dept", MobilizeDep);   //调动后一级部门
            hrData.Add("change_person_ad", user?.UserName);   //调动员工AD账号

            #region 调到后部门新拼接
            //当一级部门是工程部，且岗位名称包含精装两字，部门传递为精装工程组
            if (!string.IsNullOrWhiteSpace(MobilizeDep) && MobilizeDep.Equals("工程部") && data.F_MobilizePosition.Contains("精装"))
            {
                hrData.Add("new_dept_name", "精装工程组");//调动后新部门拼接
            }
            else
            {
                hrData.Add("new_dept_name", "");//调动后新部门拼接
            }
            #endregion

            hrData.Add("current_job_level", data.F_CurrentRank); //现任职级
            hrData.Add("remove_job_level", data.F_MobilizeRank);   //调动后职级
            hrData.Add("effective_date", data.F_ChangesDate.Value.ToString("yyyy-MM-dd")); //生效日期
            hrData.Add("note", data.F_Remark);  //备注
            hrData.Add("old_employ_status", data.F_OriginalEmplStatus); //原用工状态
            hrData.Add("target_employ_status", data.F_MobilzOriEmplStatus);  //目标用工状态
            hrData.Add("change_operating", data.F_ChangesOperating);  //变动操作
            hrData.Add("change_type", data.F_ChangesType);   //变动类型
            hrData.Add("change_reason", data.F_ChangesReason); //变动原因
            hrData.Add("apply_date", data.F_CreateDate.ToString("yyyy-MM-dd"));     //申请日期:格式yyyy-MM-dd
            hrData.Add("files", entity);
            hrData.Add("old_project", oldproject);  //调动前所属项目
            hrData.Add("project", projectName);  //调入项目
            hrData.Add("is_under_general_manager", IsDirectReports);//是否是总经理及直系下属

            var afterpost = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == data.F_MobilizePositionId);
            hrData.Add("is_under_general_manager_after", afterpost?.F_IsDirectReports??0);//调动后是否总经理直接下属

            var curRootDept = _departmentBusiness.GetRootDept(data.F_DepartmentId);
            var moveRootDept = _departmentBusiness.GetRootDept(data.F_MobilizeDepId);
            int transfer_type = 0;
            if (curRootDept.Name != moveRootDept.Name)
            {
                transfer_type = 1;
            }
            hrData.Add("transfer_type", transfer_type);//调动类型：transfer_type  0：部门内调动，1：部门间调动
            paramters.Add("hrData", hrData);

            string action = "actWorkflow";
            string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);

            var retObj = str.ToObject<Dictionary<string, string>>();
            if (retObj != null)
            {
                if (retObj["errCode"] == "0000")
                {
                    if (act == "submit")
                    {
                        data.F_WFId = retObj["oaReqId"];
                        data.F_WFState = (int)WFStates.审核中;
                        Update(data);
                    }
                    ret = true;
                }
                else
                {
                    throw new BusException(retObj["errDesc"]);
                }
            }
            else
            {
                throw new BusException("创建流程失败");
            }

            return ret;
        }
        public void Add(HR_TransferInfo data)
        {
            Insert(data);
        }

        public void Edit(HR_TransferInfo data)
        {
            Update(data);
        }
        /// <summary>
        /// 根据ID获取调动信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public HR_TransferInfoDTO GetTransferInfo(string id)
        {
            HR_TransferInfo model = Db.GetIQueryable<HR_TransferInfo>().FirstOrDefault(x => x.F_Id == id);
            HR_TransferInfoDTO entity = null;
            if (model != null)
            {
                entity = _mapper.Map<HR_TransferInfoDTO>(model);
                var emp = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == entity.F_UserId);
                if (emp != null)
                {
                    entity.F_UserId = emp.F_Id;
                    entity.EmpCode = emp.EmployeesCode;
                    entity.EmpName = emp.NameUser;
                    entity.MobilePhone = emp.MobilePhoneStr;
                }
            }

            return entity;
        }

        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <returns>是否成功</returns>
        public bool ArchiveWorkflow(HR_TransferInfoDTO data, string url)
        {
            bool ret = false;

            //string token = string.Empty;
            //Base_User user = null;
            //var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
            //if (formalEmployees != null)
            //{
            //    user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == formalEmployees.BaseUserId);
            //    token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
            //}
            var user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == data.F_CreateUserId);
            string token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
            var model = this.GetEntity(data.F_Id);
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            paramters.Add("reqCode", token);
            paramters.Add("reqNo", data.F_Id);
            //paramters.Add("applyPerson", user?.UserName);
            paramters.Add("applyPerson", user?.UserName); //申请人，比如说纪殿凯发的流程，申请人就传jidk
            paramters.Add("oaId", model.F_WFId);
            string action = "archiveWorkflow";
            string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);

            var retObj = str.ToObject<Dictionary<string, string>>();
            if (retObj != null)
            {
                if (retObj["errCode"] == "0000")
                {
                    data.F_WFId = retObj["oaReqId"];
                    data.F_WFState = (int)WFStates.取消流程;
                    Update(data);
                    ret = true;
                }
                else
                {
                    throw new BusException(retObj["errDesc"]);
                }
            }
            else
            {
                throw new BusException("创建流程失败");
            }

            return ret;
        }

        #endregion

        #region 私有成员
        /// <summary>
        /// 验证是否有在途流程
        /// </summary>
        /// <param name="userId">员工ID</param>
        /// <returns></returns>
        private bool ValidateFlowing(string userId)
        {
            int[] states = { 1, 2 };
            var q = GetIQueryable().Where(x => x.F_UserId == userId && x.F_WFState.HasValue && states.Contains(x.F_WFState.Value));
            return q.Count() > 0;
        }
        #endregion
    }
}