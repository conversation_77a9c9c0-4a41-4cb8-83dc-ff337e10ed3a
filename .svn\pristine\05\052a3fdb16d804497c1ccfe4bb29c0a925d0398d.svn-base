<template>
  <a-card :bordered="false" :hoverable="true">
    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="hanldleAdd()">新建</a-button>
      <!--      <a-button
        type="primary"
        icon="minus"
        @click="handleDelete(selectedRowKeys)"
        :disabled="!hasSelected()"
        :loading="loading"
      >删除</a-button> -->
      <!--      <a-button type="primary" icon="arrow-down" @click="exportExcel()">导出Excel</a-button> -->
      <a-button type="primary" icon="redo" @click="getDataList()">刷新</a-button>
    </div>

    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="10">
          <a-col :md="4" :sm="24">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="queryParam.condition">
                <a-select-option key="EmployeesCode">员工编码</a-select-option>
                <a-select-option key="NameUser">员工姓名</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item>
              <a-input v-model="queryParam.keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-button type="primary" icon="search" @click="
                () => {
                  this.pagination.current = 1
                  this.getDataList()
                }
              ">查询</a-button>
            <a-button style="margin-left: 8px" icon="sync" @click="() => (queryParam = {})">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table" :columns="columns" :rowKey="row => row.Id" :dataSource="data" :pagination="false"
      :loading="loading" :bordered="true">
      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleInfo(record.F_Id)">详情</a>
          <a-divider v-if="!record.F_WFState || record.F_WFState == 0|| record.F_WFState == 4" type="vertical" />
          <a v-if="!record.F_WFState || record.F_WFState == 0|| record.F_WFState == 4"
            @click="handleEdit(record.F_Id, false)">编辑</a>
          <a-divider v-if="!record.F_WFState || record.F_WFState == 0" type="vertical" />
          <a v-if="!record.F_WFState || record.F_WFState == 0" @click="handleDelete([record.F_Id])">删除</a>
        </template>
      </span>
      <span slot="F_ModifyDate" slot-scope="text">
        <template>
          <span>{{ text | dayjs('YYYY-MM-DD') }}</span>
        </template>
      </span>
      <span slot="F_InductionDate" slot-scope="text">
        <template>
          <span>{{ text | dayjs('YYYY-MM-DD') }}</span>
        </template>
      </span>
      <ul slot="F_WFState" slot-scope="text, record" class="WFState">
        <template>
          <li @click="commonApi.flowOpen(record.F_WFId)" :class=commonApi.colorFun(record.F_WFState)>{{
          text
          }}</li>
        </template>
      </ul>
    </a-table>
    <a-pagination show-size-changer :default-current="pagination.current" :defaultPageSize="pagination.pageSize"
      :showTotal="pagination.showTotal" :total="pagination.total" @showSizeChange="onShowSizeChange"
      @change="onChangeCurrent" style="margin: 5px 0;text-align: center;" />

    <MyEditForm ref="myeditForm" :parentObj="this"></MyEditForm>

  </a-card>
</template>

<script>
import MyEditForm from './MyEditForm'
import { downLoadFile } from '@/utils/plugin/axios-plugin.js'
import { parseTime } from '@/utils/util.js'
import { operateFile } from '@/utils/tools.js'
const columns = [
  {
    title: '员工编码',
    dataIndex: 'EmployeesCode',
    width: 100,
    fixed: 'left'
  },
  {
    title: '员工姓名',
    dataIndex: 'NameUser',
    width: 100,
    fixed: 'left'
  },
  // {
  //   title: '所属部门',
  //   dataIndex: 'F_DepartmentName',
  //   width: 150
  // },
  {
    title: '所属组织',
    dataIndex: 'F_PositiveOrg',
    width: 300
  },
  // {
  //   title: '任职职位',
  //   dataIndex: 'F_PositionName',
  //   width: 100
  // },
  {
    title: '用工关系状态',
    dataIndex: 'EmployRelStatus',
    width: 150
  },
  {
    title: '身份证号码',
    dataIndex: 'IdCardNumber',
    width: 200
  },
  {
    title: '性别',
    dataIndex: 'SexName',
    width: 70
  },
  {
    title: '入职日期',
    dataIndex: 'F_InductionDate',
    width: 150,
    scopedSlots: { customRender: 'F_InductionDate' }
  },
  {
    title: '手机号码',
    dataIndex: 'MobilePhone',
    width: 150
  },
  {
    title: '流程状态',
    dataIndex: 'WFStateText',
    width: 120,
    fixed: 'right',
    scopedSlots: { customRender: 'F_WFState' }
  },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: {
      customRender: 'action'
    },
    width: 150,
    fixed: 'right'
  }
]

export default {
  components: {
    MyEditForm
  },
  mounted () {
    this.getDataList()
  },
  filters: {
    // 过滤器
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 15,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      filters: {},
      sorter: {
        field: 'F_CreateDate',
        order: 'asc'
      },
      loading: false,
      columns,
      queryParam: { condition: "NameUser" },
      selectedRowKeys: [],
      User: {}
    }
  },
  created () {
    //获取登录用户的信息
    this.$http.post('/Base_Manage/Base_User/GetLoginUser').then(resJson => {
      this.loading = false
      console.log(resJson.Data.Property)
      this.User = resJson.Data.Property
    })
  },
  methods: {
    onChangeCurrent (current, pageSize) {
      this.pagination.current = current
      this.getDataList()
    },
    onShowSizeChange (current, pageSize) {
      this.pagination.current = current
      this.pagination.pageSize = pageSize
      this.getDataList()
    },
    handleTableChange (pagination, filters, sorter) {
      this.pagination = {
        ...pagination
      }
      this.filters = {
        ...filters
      }
      this.sorter = {
        ...sorter
      }
      this.getDataList()
    },
    getDataList () {
      this.selectedRowKeys = []
      this.loading = true
      this.$http
        .post('/HR_EmployeeInfoManage/HR_Positive/GetMyDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'F_CreateDate',
          SortType: this.sorter.order,
          Search: this.queryParam,
          ...this.filters
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }

          this.pagination = pagination
          this.pagination.total = resJson.Total
        })
    },
    exportExcel () {
      const data = {
        PageIndex: this.pagination.current,
        PageRows: this.pagination.pageSize,
        SortField: this.sorter.field || 'F_CreateDate',
        SortType: this.sorter.order,
        Search: this.queryParam,
        ...this.filters
      }
      const url = '/HR_EmployeeInfoManage/HR_Positive/ExcelDownload'
      downLoadFile(
        url,
        data,
        function (res) {
          console.log(res)
          if (res) {
            operateFile(res, '转正单')
          } else {
            console.log('失败')
          }
        },
        function (err) {
          console.log(err)
        }
      )
    },
    onSelectChange (selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    hasSelected () {
      return this.selectedRowKeys.length > 0
    },
    //新建
    hanldleAdd () {
      this.$refs.myeditForm.openForm(0, '我要转正')
    },
    //编辑
    handleEdit (id) {
      this.$refs.myeditForm.openForm(id, '我要转正编辑', false)
    },
    //详情
    handleInfo (id) {
      this.$refs.myeditForm.openForm(id, '我要转正详情', true)
    },
    //流程编辑页面
    handleflowEdit (id) {
      this.$router.push({
        path: '../HR_Positive/AuditForm',
        query: {
          id: id
        }
      })
    },
    handleDelete (ids) {
      var thisObj = this
      this.$confirm({
        title: '确认删除吗?',
        onOk () {
          return new Promise((resolve, reject) => {
            thisObj.$http.post('/HR_EmployeeInfoManage/HR_Positive/DeleteData', ids).then(resJson => {
              resolve()

              if (resJson.Success) {
                thisObj.$message.success('操作成功!')

                thisObj.getDataList()
              } else {
                thisObj.$message.error(resJson.Msg)
              }
            })
          })
        }
      })
    }
  }
}
</script>
