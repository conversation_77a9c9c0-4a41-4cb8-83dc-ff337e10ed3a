﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Coldairarrow.Entity.S_School
{
    /// <summary>
    /// 系列文章关联表
    /// </summary>
    [Table("S_ClassifyContent")]
    public class S_ClassifyContent
    {

        /// <summary>
        /// F_Id
        /// </summary>
        [Key, Column(Order = 1)]
        public String F_Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime F_CreateDate { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public String F_CreateUserId { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public String F_CreateUserName { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        public DateTime? F_ModifyDate { get; set; }

        /// <summary>
        /// 修改人Id
        /// </summary>
        public String F_ModifyUserId { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public String F_ModifyUserName { get; set; }

        /// <summary>
        /// F_ClassifyId
        /// </summary>
        public String F_ClassifyId { get; set; }

        /// <summary>
        /// F_ContentId
        /// </summary>
        public String F_ContentId { get; set; }

    }
}