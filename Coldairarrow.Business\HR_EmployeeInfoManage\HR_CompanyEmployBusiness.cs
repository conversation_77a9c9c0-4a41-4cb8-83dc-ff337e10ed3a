﻿using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Entity;
using System;
using Coldairarrow.IBusiness;
using Coldairarrow.Entity.Base_Manage;
using System.IO;
using Microsoft.Extensions.Caching.Distributed;
using Coldairarrow.Entity.HR_DataDictionaryManage;
using Coldairarrow.Entity.DTO;
using Coldairarrow.Business.Base_Manage;

namespace Coldairarrow.Business.HR_EmployeeInfoManage
{
    public class HR_CompanyEmployBusiness : BaseBusiness<HR_CompanyEmploy>, IHR_CompanyEmployBusiness, ITransientDependency
    {
        public HR_CompanyEmployBusiness(IDbAccessor db, IOperator op, IBase_PostBusiness base_PostBusiness)
            : base(db)
        {
            this._op = op;
            _base_PostBusiness = base_PostBusiness;
        }
        private IBase_PostBusiness _base_PostBusiness;
        IOperator _op;
        #region 外部接口

        public async Task<PageResult<HR_CompanyEmploy>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable().Where(x => x.F_WorkExpInOrOut == 0);
            var where = LinqHelper.True<HR_CompanyEmploy>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_CompanyEmploy, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            where = where.AndIf(!search.F_Id.IsNullOrEmpty(), x => x.F_UserId == search.F_Id);

            return await q.Where(where).GetPageResultAsync(input);
        }
        public async Task<List<HR_CompanyEmploy>> GetDataListByUserIdAsync(string userId)
        {
            return await GetIQueryable().Where(x => x.F_WorkExpInOrOut == 0 && x.F_UserId == userId).ToListAsync();
        }
        public async Task<HR_CompanyEmploy> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_CompanyEmploy data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_CompanyEmploy data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable().Where(x => x.F_WorkExpInOrOut == 0);
            var where = LinqHelper.True<HR_CompanyEmploy>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_CompanyEmploy, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_CompanyEmploy, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        /// <summary>
        /// 员工变动
        /// </summary>
        /// <param name="data"></param>
        public void EmployeeChange(HR_CompanyEmploy data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                this.InitEntity(data, _op);
                //添加任职信息
                this.Insert(data);
            }
            else
            {
                this.UpdateEntity(data, _op);
                //修改员工职位信息
                this.Update(data);
            }
            //修改正式员工的职位
        }

        /// <summary>
        /// 企业任职经历新增
        /// </summary>
        /// <param name="emp">员工</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="op"></param>
        public void CreateComEmp(HR_FormalEmployees emp, DateTime startTime, IOperator op)
        {
            if (emp == null)
            {
                throw new BusException("员工不存在");
            }
            var pos = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == emp.F_PositionId);
            HR_CompanyEmploy model = new HR_CompanyEmploy()
            {
                F_ChangesOperating = "员工初始化",
                F_ChangesReason = "",
                F_ChangesType = "员工初始化",
                F_EmployRelStatus = emp?.EmployRelStatus,
                F_IsSystemGen = 1,
                F_UserId = emp.F_Id,
                F_CompanyId = emp?.F_CompanyId,
                F_PositionId = emp?.F_PositionId,
                F_PositionInfo = pos?.F_Name,
                F_Rank = emp?.F_Rank,
                F_WorkExpInOrOut = 0,
                F_ForType = "主要任职",
                F_BusState = 1,
                F_StartTime = startTime,
                F_OrganizeInfo = _base_PostBusiness.GetOrgName(emp.F_PositionId)
            };
            InitEntity(model, op);
            this.Insert(model);
        }
        /// <summary>
        /// 企业任职经历撤销
        /// </summary>
        /// <param name="id">员工id</param>
        public void CheXiao(string id, IOperator op)
        {
            var emp = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == id);
            if (emp != null)
            {
                HR_CompanyEmploy model = new HR_CompanyEmploy()
                {
                    F_ChangesDate = DateTime.Now,
                    F_ChangesOperating = "撤销任职经历",
                    F_ChangesReason = "",
                    F_ChangesType = "撤销",
                    F_EmployRelStatus = emp.EmployRelStatus,
                    F_IsSystemGen = 0,
                    F_MobilzOriEmplStatus = emp.EmployRelStatus,
                    F_UserId = id,
                    F_CreateDate = DateTime.Now,
                    F_CreateUserId = op?.UserId,
                    F_CreateUserName = op?.Property?.RealName,
                    F_CompanyId = emp.F_CompanyId,
                    F_PositionId = emp.F_PositionId,
                    F_Id = IdHelper.GetId()
                };
                this.Insert(model);

                emp.F_PositionId = null;
                emp.F_DepartmentId = null;
                Db.Update(emp);
            }
        }
        public async Task AddDataListAsync(List<HR_CompanyEmploy> data)
        {
            await InsertAsync(data);
        }

        /// <summary>
        /// 导入并保存数据
        /// </summary>
        /// <param name="physicPath">物理路径</param>
        /// <returns></returns>
        public AjaxResult<DataTable> ImportSaveData(string physicPath, IOperator op)
        {
            AjaxResult<DataTable> ajaxResult = new AjaxResult<DataTable>();

            DataTable dt = ExcelHelper.ExcelImport(physicPath);
            if (dt == null || dt.Rows.Count == 0)
            {
                ajaxResult.Success = false;
                ajaxResult.Msg = "上传数据错误或不能为空";
            }
            else
            {
                dt.Columns.Add("导入错误", typeof(string));
                bool isError = false;
                List<HR_CompanyEmploy> records = new List<HR_CompanyEmploy>();
                string[] itemCodes = { "用工关系状态", "任职变动操作", "任职变动类型", "任职变动原因" };
                //获取数字字典
                var dicDetails = Db.GetIQueryable<HR_DataDictionaryDetails>().Where(x => itemCodes.Contains(x.F_ItemCode)).ToList();

                foreach (DataRow dr in dt.Rows)
                {
                    if (dr["员工编码"] != null && dr["姓名"] != null && !dr["员工编码"].ToString().IsNullOrEmpty() && !dr["姓名"].ToString().IsNullOrEmpty())
                    {
                        HR_CompanyEmploy model = new HR_CompanyEmploy();

                        var emp = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.NameUser == dr["姓名"].ToString() && x.EmployeesCode == dr["员工编码"].ToString());
                        if (emp == null)
                        {
                            dr["导入错误"] += "员工不存在;";
                            isError = true;
                        }
                        else
                        {
                            model.F_UserId = emp.F_Id;
                            model.F_IsSystemGen = 0;
                            model.F_CompanyId = emp.F_CompanyId;
                            model.F_OriginalEmplStatus = emp.EmployRelStatus;
                            model.F_EmployRelStatus = emp.EmployRelStatus;
                        }
                        var post = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Name == dr["目标职位"].ToString());
                        if (post == null)
                        {
                            dr["导入错误"] += "目标职位不存在;";
                            isError = true;
                        }
                        else
                        {
                            model.F_PositionId = post.F_Id;
                            model.F_PositionInfo = post.F_Name;
                        }
                        if (dr["变动操作"] != null)
                        {
                            if (dr["变动操作"].ToString().Length < 51)
                            {
                                var count = dicDetails.Count(x => x.F_ItemCode == "任职变动操作" && x.F_ItemValue == dr["变动操作"].ToString());
                                if (count > 0)
                                {
                                    model.F_ChangesOperating = dr["变动操作"]?.ToString();
                                }
                                else
                                {
                                    dr["导入错误"] += "变动操作不在数据字典范围内;";
                                    isError = true;
                                }
                            }
                            else
                            {
                                dr["导入错误"] += "变动操作长度不能大于50个字符;";
                                isError = true;
                            }
                        }
                        if (dr["目标用工状态"] != null)
                        {
                            if (dr["目标用工状态"].ToString().Length < 51)
                            {
                                var count = dicDetails.Count(x => x.F_ItemCode == "用工关系状态" && x.F_ItemValue == dr["目标用工状态"].ToString());
                                if (count > 0)
                                {
                                    model.F_MobilzOriEmplStatus = dr["目标用工状态"]?.ToString();
                                }
                                else
                                {
                                    dr["导入错误"] += "目标用工状态不在数据字典范围内;";
                                    isError = true;
                                }
                            }
                            else
                            {
                                dr["导入错误"] += "目标用工状态长度不能大于50个字符;";
                                isError = true;
                            }
                        }
                        if (dr["变动类型"] != null)
                        {
                            if (dr["变动类型"].ToString().Length < 51)
                            {
                                var count = dicDetails.Count(x => x.F_ItemCode == "任职变动类型" && x.F_ItemValue == dr["变动类型"].ToString());
                                if (count > 0)
                                {
                                    model.F_ChangesType = dr["变动类型"]?.ToString();
                                }
                                else
                                {
                                    dr["导入错误"] += "变动类型不在数据字典范围内;";
                                    isError = true;
                                }
                            }
                            else
                            {
                                dr["导入错误"] += "变动类型长度不能大于50个字符;";
                                isError = true;
                            }
                        }
                        if (dr["变动原因"] != null)
                        {
                            if (dr["变动原因"].ToString().Length < 51)
                            {
                                var count = dicDetails.Count(x => x.F_ItemCode == "任职变动原因" && x.F_ItemValue == dr["变动原因"].ToString());
                                if (count > 0)
                                {
                                    model.F_ChangesReason = dr["变动原因"]?.ToString();
                                }
                                else
                                {
                                    dr["导入错误"] += "变动原因不在数据字典范围内;";
                                    isError = true;
                                }
                            }
                            else
                            {
                                dr["导入错误"] += "变动原因长度不能大于50个字符;";
                                isError = true;
                            }
                        }
                        if (dr["生效时间"] != null)
                        {
                            DateTime sxDate; //生效时间
                            if (DateTime.TryParse(dr["生效时间"].ToString(), out sxDate))
                            {
                                model.F_ChangesDate = sxDate;
                            }
                            else
                            {
                                dr["导入错误"] += "生效时间参数不对;";
                                isError = true;
                            }
                        }
                        else
                        {
                            dr["导入错误"] += "生效时间不能为空;";
                            isError = true;
                        }
                        if (dr["备注"] != null)
                        {
                            if (dr["备注"].ToString().Length < 1001)
                            {
                                model.F_Remark = dr["备注"]?.ToString();
                            }
                            else
                            {
                                dr["导入错误"] += "备注长度不能大于1000个字符;";
                                isError = true;
                            }
                        }
                        if (!isError)
                        {
                            model.F_Id = GuidHelper.GenerateKey();
                            model.F_CreateDate = DateTime.Now;
                            model.F_CreateUserId = op.UserId;
                            model.F_CreateUserName = op.RealName;
                            model.F_StartTime = model.F_ChangesDate;
                            model.F_WorkExpInOrOut = 0;
                            model.F_BusState = 2;
                            records.Add(model);
                        }
                    }
                    else
                    {
                        dr["导入错误"] += "员工编码和姓名不能为空;";
                        isError = true;
                    }
                }
                if (isError)
                {
                    ajaxResult.Data = dt;
                    ajaxResult.Success = false;
                    ajaxResult.Msg = "导入失败";

                    return ajaxResult;
                }
                else
                {
                    Insert(records);
                    ajaxResult.Success = true;
                    ajaxResult.Msg = "导入成功";
                    ajaxResult.ErrorCode = 1;
                }
            }
            return ajaxResult;
        }

        #endregion

        #region 私有成员

        #endregion
    }
}