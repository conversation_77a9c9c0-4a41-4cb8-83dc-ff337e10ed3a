﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Coldairarrow.Entity.HR_AttendanceManage
{
    /// <summary>
    /// 员工加班
    /// </summary>
    [Table("HR_WorkOvertime")]
    public class HR_WorkOvertime
    {

        /// <summary>
        /// F_Id
        /// </summary>
        [Key, Column(Order = 1)]
        public String F_Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime F_CreateDate { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public String F_CreateUserId { get; set; }

        /// <summary>
        /// 创建人名
        /// </summary>
        public String F_CreateUserName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? F_ModifyDate { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public String F_ModifyUserId { get; set; }

        /// <summary>
        /// 修改人名
        /// </summary>
        public String F_ModifyUserName { get; set; }

        /// <summary>
        /// 流程Guid
        /// </summary>
        public String F_WFId { get; set; }

        /// <summary>
        /// 业务状态
        /// </summary>
        public Int32? F_BusState { get; set; }

        /// <summary>
        /// 流程状态
        /// </summary>
        public Int32? F_WFState { get; set; }

        /// <summary>
        /// 加班编号
        /// </summary>
        public String F_WorkONo { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public String F_Remark { get; set; }

        /// <summary>
        /// 员工ID
        /// </summary>
        public String F_UserId { get; set; }

        /// <summary>
        /// 员工姓名
        /// </summary>
        public String F_UserName { get; set; }

        /// <summary>
        /// 加班日期
        /// </summary>
        public DateTime? F_WorkODate { get; set; }

        /// <summary>
        /// 加班类型
        /// </summary>
        public String F_WorkOType { get; set; }

        /// <summary>
        /// 加班开始时间
        /// </summary>
        public DateTime? F_WorkOStartTime { get; set; }

        /// <summary>
        /// 加班结束时间
        /// </summary>
        public DateTime? F_WorkOEndTime { get; set; }

        /// <summary>
        /// 休息时长（分钟）
        /// </summary>
        public Int32? F_RestTime { get; set; }

        /// <summary>
        /// 加班小时
        /// </summary>
        public Int32? F_WorkOTime { get; set; }

        /// <summary>
        /// 补偿方式
        /// </summary>
        public String F_CompensationMode { get; set; }

        /// <summary>
        /// 加班原因
        /// </summary>
        public String F_WorkOReason { get; set; }

        /// <summary>
        /// 实际加班小时数
        /// </summary>
        public Int32? F_ActualWorkOTime { get; set; }

        /// <summary>
        /// 实际加班开始时间
        /// </summary>
        public DateTime? F_ActualWorkOStart { get; set; }

        /// <summary>
        /// 实际加班结束实际
        /// </summary>
        public DateTime? F_ActualWorkOEnd { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public String F_CompanyId { get; set; }

        /// <summary>
        /// F_GroupId
        /// </summary>
        public String F_GroupId { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public String F_ProjectName { get; set; }
        [NotMapped]

        /// <summary>
        /// 说明
        /// </summary>
        public String F_GroupRemark { get; set; }
    }
}