﻿using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Entity.DTO;
using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Quartz.Util;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Base_Manage
{
    /// <summary>
    /// 首页控制器
    /// </summary>
    [Route("/Base_Manage/[controller]/[action]")]
    public class HomeController : BaseApiController
    {
        readonly IHomeBusiness _homeBus;
        readonly IPermissionBusiness _permissionBus;
        readonly IBase_UserBusiness _userBus;
        readonly IOperator _operator;
        readonly IHR_FormalEmployeesBusiness _hR_FormalEmployeesBus;
        readonly IHR_PersonPhotoBusiness _hR_PersonPhotoBus;
        readonly IConfiguration _configuration;
        public HomeController(
            IHomeBusiness homeBus,
            IPermissionBusiness permissionBus,
            IBase_UserBusiness userBus,
            IOperator @operator,
            IConfiguration configuration,
            IHR_FormalEmployeesBusiness hR_FormalEmployeesBus,
            IHR_PersonPhotoBusiness hR_PersonPhotoBus
            )
        {
            _configuration = configuration;
            _homeBus = homeBus;
            _permissionBus = permissionBus;
            _userBus = userBus;
            _operator = @operator;
            _hR_FormalEmployeesBus = hR_FormalEmployeesBus;
            _hR_PersonPhotoBus = hR_PersonPhotoBus;
        }

        /// <summary>
        /// 用户登录(获取token)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task<string> SubmitLogin(LoginInputDTO input)
        {
            SynchronousData();
            var isAD = Convert.ToBoolean(_configuration["IsEnableAD"]);
            if (isAD)
            {
                if (input.userName.ToLower() == "admin")
                {
                    return await _homeBus.SubmitLoginAsync(input);
                }
                else
                {
                    throw new BusException("登录失败，请按照指定方式进入！");
                    //return _userBus.LoginList(input.userName, input.password);
                }
            }
            else
            {
                return await _homeBus.SubmitLoginAsync(input);
            }
        }

        /// <summary>
        /// 考试用户登录(获取token)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task<string> ExamSubmitLogin(LoginInputDTO input)
        {
            var isAD = Convert.ToBoolean(_configuration["IsEnableAD"]);
            //Regex reg = new Regex(@"^(13[0-9]|14[5|7]|15[0|1|2|3|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$");
            //验证是否是手机号
            if (RegexHelper.IsPhoneNumber(input.userName))
            {
                return await _homeBus.SubmitPhoneLoginAsync(input);
            }

            if (isAD)
            {
                if (input.userName.ToLower() == "admin")
                {
                    return await _homeBus.SubmitLoginAsync(input);
                }
                else
                {
                    throw new BusException("禁止登录！");
                    //return _userBus.LoginList(input.userName, input.password);
                }
            }
            else
            {
                return await _homeBus.SubmitLoginAsync(input);
            }
        }

        /// <summary>
        /// 考试注册用户
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task registerUser(registerInputDTO registerInput)
        {
            var base_User = _userBus.GetTheDataByPhone(registerInput.PhoneNumbers);
            if (base_User != null)
                throw new BusException("该手机号已注册!");
            await _homeBus.registerUser(registerInput);
        }
        /// <summary>
        /// 考试注册小程序
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task registerMiniUser()
        {
            string register = HttpContext.Request.Form["register"].ToString();
            var registerInput = JsonConvert.DeserializeObject<registerInputDTO>(register);
            var base_User = _userBus.GetTheDataByPhone(registerInput.PhoneNumbers);
            if (base_User != null)
                throw new BusException("该手机号已注册!");
            await _homeBus.registerUser(registerInput);
        }

        [HttpPost]
        [NoCheckJWT]
        public async Task<AliMsgOutput> SendAliMsg(SendMsgDto data)
        {
            if (string.IsNullOrEmpty(data.PhoneNumbers))
                throw new BusException("手机号不能为空!");
            data.url = _configuration["SMSUrls:Url"];
            data.SMSAPIUrl = _configuration["SMSUrls:aliSendMsg"];
            return await _homeBus.SendAliMsg(data);
        }
        /// <summary>
        /// 单点登录验证
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task<string> VerifyValidity(VerifyValidityDTO input)
        {
            return await _homeBus.VerifyValidityAsync(input);
        }

        [HttpPost]
        public async Task ChangePwd(ChangePwdInputDTO input)
        {
            await _homeBus.ChangePwdAsync(input);
        }


        /// <summary>
        /// 清除缓存，系统中的缓存key值在此方法中清除
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public bool clearCache()
        {
            RedisHelper.Del("posttree");
            return true;
        }

        [HttpPost]
        public async Task<object> GetOperatorInfo()
        {
            var theInfo = await _userBus.GetTheDataAsync(_operator.UserId);
            var permissions = await _permissionBus.GetUserPermissionValuesAsync(_operator.UserId);
            var Employees = _hR_FormalEmployeesBus.GetDataByUserId(_operator.UserId);
            var resObj = new
            {
                UserInfo = theInfo,
                Permissions = permissions,
                EmployeesInfo = Employees
            };

            return resObj;
        }

        [HttpPost]
        public AjaxResult GetMyTeam()
        {
            var usermodel = GetOperator();
            var temp = _homeBus.GetMyTeam(usermodel.UserId);
            return Success(temp);
            //return _homeBus.GetMyTeam(usermodel.UserId);
        }
        [HttpPost]
        public AjaxResult GetTeamDynamicsInfo(PageInput<RolesInputDTO> input)
        {
            var usermodel = GetOperator();
            var temp = _homeBus.GetEmpTeamInfo(input, usermodel.UserId);
            return Success(temp);
            // return _homeBus.GetEmpTeamInfo(usermodel.UserId);List<TeamDynamicsDTO>
        }
        public HomeInfo GetHomeInfo()
        {
            var usermodel = GetOperator();
            var obj = _hR_FormalEmployeesBus.GetHomeInfo(usermodel.UserId);
            var user = this.GetOperator();
            obj.UserId = user.UserId;
            if (string.IsNullOrEmpty(obj.Name))
            {
                //获取use表的名称
                obj.Name = _userBus.GetTheData(user.UserId)?.RealName;
            }
            //obj.HeadImage = _hR_PersonPhotoBus.GetPersonPhoto(obj.F_Id);
            return obj;
        }

        public AjaxResult GetHomeData()
        {
            var usermodel = GetOperator();
            var url = _configuration["OAUrl"] + _configuration["OAUrls:BacklogProcess"];
            var url2 = _configuration["OAUrl"] + _configuration["OAUrls:HasProcess"];
            var url3 = _configuration["OAUrl"] + _configuration["OAUrls:GetToken"];
            OAInputDTO oAInput = new OAInputDTO()
            {
                ADName = usermodel.ADName,
                UserId = usermodel.UserId,
                RealName = usermodel.RealName,
                Url = url,
                Url2 = url2,
                Url3 = url3,
            };
            return Success(_homeBus.GetHomeInfo(oAInput));
        }

        [HttpPost]
        public async Task<List<Base_ActionDTO>> GetOperatorMenuList()
        {
            return await _permissionBus.GetUserMenuListAsync(_operator.UserId);
        }
        /// <summary>
        ///用户岗位、部门、公司同步
        /// </summary>
        public void GetComPostData()
        {
            _homeBus.GetUser();
            //入职转正离职同步
            //_homeBus.GetUserIPosition();
        }
        /// <summary>
        ///入职转正离职同步
        /// </summary>
        public void GetUserIPositionData()
        {

            _homeBus.GetUserIPosition();
        }
        public void SynchronousData()
        {
            //岗位信息
            //  _homeBus.GetPost();
            //用户岗位、部门、公司同步
            //_homeBus.GetUser();
            //入职转正离职同步
            //_homeBus.GetUserIPosition();




        }

        #region 对接AI接口

        [HttpPost]
        [NoCheckJWT]
        public async Task<string> GetKBMSAI(KdAiInput kdAi)
        {
            if (!kdAi.currentMsg.IsNullOrWhiteSpace())
            {
                //对接AI问答
                string str = HttpHelper.PostData("https://aiapi.cqlandmark.com/KBS_Admin/KBS_AIQuestion/GetKdAiTasker", JsonConvert.DeserializeObject<Dictionary<string, object>>(JsonConvert.SerializeObject(kdAi)), null, ContentType.Json);
                var retObj = str.ToObject<Dictionary<string, object>>();
                if (retObj != null && (bool)retObj["Success"])
                {
                    return retObj["Data"].ToString();
                }
                else
                {
                    throw new Exception("查询错误，请联系管理员！");
                }
            }
            else
            {
                throw new Exception("当前对话内容不能为空！");
            }
        }
        #endregion
        [HttpPost]
        [NoCheckJWT]
        public  string EncryptString(ConditionDTO dto)
        {
            return AESHelper.EncryptString(dto.Keyword, AESHelper.AesKey);
        }
        [HttpPost]
        [NoCheckJWT]
        public  string DecryptString(ConditionDTO dto)
        {
            return AESHelper.DecryptString(dto.Keyword, AESHelper.AesKey);
        }
        [HttpPost]
        [NoCheckJWT]
        public string CESHI(ConditionDTO dto)
        {
            return AESHelper.DecryptString(dto.Keyword, AESHelper.AesKey);
        }
    }

    #region AI参数
    /// <summary>
    /// 输入实体
    /// </summary>
    public class KdAiInput
    {
        /// <summary>
        /// 本次问答
        /// </summary>
        public string currentMsg { get; set; }
        /// <summary>
        /// 对话数据
        /// </summary>
        public List<Content> msgDatas { get; set; }
        /// <summary>
        /// 是否连续对话
        /// </summary>
        public YesOrNo isContinuous { get; set; }
        /// <summary>
        /// 请求对话的用户id
        /// </summary>
        public string chatUserId { get; set; }
        /// <summary>
        /// 文章Id
        /// </summary>
        public string contentId { get; set; }
        /// <summary>
        /// 提示类型枚举
        /// </summary>
        public promptType msgType { get; set; } = promptType.普通提问;
    }

    /// <summary>
    /// 提示类型枚举
    /// </summary>
    public enum promptType
    {
        普通提问,
        关键词,
        翻译文章,
        文章摘要提取
    }
    public class Content
    {
        public string role { get; set; }
        public string content { get; set; }
    }
    #endregion
}