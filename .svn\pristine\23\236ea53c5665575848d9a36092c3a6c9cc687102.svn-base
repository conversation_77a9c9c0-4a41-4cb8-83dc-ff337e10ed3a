﻿using Coldairarrow.Entity.Base_Business;
using Coldairarrow.Util;
using Coldairarrow.Util.DataAccess;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

namespace Coldairarrow.Business.Base_Business
{
    public class Tencent_DailyRecordBusiness : BaseBusiness<Tencent_DailyRecord>, ITencent_DailyRecordBusiness, ITransientDependency
    {
        public Tencent_DailyRecordBusiness(IBusinessDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<Tencent_DailyRecord>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Tencent_DailyRecord>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Tencent_DailyRecord, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Tencent_DailyRecord> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(Tencent_DailyRecord data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Tencent_DailyRecord data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }
        public Tencent_DailyRecord GetDataByDate(DateTime day)
        {
            var q = GetIQueryable().Where(x => x.Date == day).FirstOrDefault();
            return q;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}