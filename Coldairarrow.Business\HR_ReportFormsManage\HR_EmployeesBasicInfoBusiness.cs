﻿using AutoMapper;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HR_ReportFormsManage;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Coldairarrow.Entity.HR_EmployeeInfoManage.Extensions;
namespace Coldairarrow.Business.HR_ReportFormsManage
{
    public class HR_EmployeesBasicInfoBusiness : BaseBusiness<HR_EmployeesBasicInfoDTO>, IHR_EmployeesBasicInfoBusiness, ITransientDependency
    {
        public HR_EmployeesBasicInfoBusiness(IDbAccessor db)
            : base(db)
        {
        }
        public IQueryable<HR_EmployeesBasicInfoDTO> GetHR_PersonnelChangeDetails(PageInput<ConditionDTO> input)
        {
            try
            {
                //Expression<Func<HR_FormalEmployees, HR_Induction, Base_Department, Base_Company, Base_Post, HR_LaborContractInfo, HR_Positive, HR_Departure, HR_EmploEducationExp, HR_EmployeesBasicInfoDTO>> select = (a, b, c, d, e, h, g, m, emp) => new HR_EmployeesBasicInfoDTO
                //{
                //    F_Id = a.F_Id,
                //    EmployeesCode = a.EmployeesCode,
                //    ContractCompany = d.F_FullName,
                //    ND3 = a.ND3,
                //    ND2 = a.ND2,
                //    ND = a.ND,
                //    DepartmentName = c.Name,
                //    NameUser = a.NameUser,
                //    PostName = g.F_PositivePosition,
                //    F_Rank = a.F_Rank,
                //    Sex = a.Sex,
                //    IdCardNumber = a.IdCardNumber,
                //    DirthDate = a.DirthDate,
                //    F_AttackTime = "",
                //    F_YearSorking = "",
                //    EmployRelStatus = a.EmployRelStatus,
                //    F_InductionDate = b.F_InductionDate,
                //    Commander = 0,
                //    F_PositiveDate = g.F_PositiveDate,
                //    F_DepartureDate = m.F_DepartureDate,
                //    SigningDate = h.SigningDate,
                //    ContractEndDate = h.SigningDate,
                //    FristRenewSigningDate = "",
                //    FristRnContractEndDate = "",
                //    SecondRnSigningDate = "",
                //    SecondRnContractEndDate = "",
                //    ThirdRnSigningDate = "",
                //    RecordSchooling = emp.RecordSchooling,
                //    GraduatedSchool = emp.GraduatedSchool,
                //    Professional = emp.Professional,
                //    F_Title = e.F_Name,
                //    过往工作经验最近 = "",
                //    过往工作经验次近 = "",
                //    NationalInfo = a.NationalInfo,
                //    PoliticalLandscape = a.PoliticalLandscape,
                //    MobilePhone = a.MobilePhone,
                //    NativePlace = a.NativePlace,
                //    IdCardAddress = a.IdCardAddress,
                //    RegisteredResidence = a.RegisteredResidence,
                //    HomeAddress = a.HomeAddress,
                //    MaritalStatus = a.MaritalStatus,
                //    FertilityStatus = a.FertilityStatus,
                //    AccountType = a.AccountType,
                //    EmergencyContact = a.EmergencyContact,
                //    EmergencyContactNumber = a.EmergencyContactNumber,
                //    IsWhetherCar = a.IsWhetherCar,
                //    过往三年绩效3 = "",
                //    过往三年绩效2 = "",
                //    过往三年绩效1 = "",
                //    过往奖惩记录 = "",
                //    晋升记录 = "",
                //    岗位变动记录 = "",
                //    ContractPeriod = 0,
                //    CumContractsNumber = 0,
                //};
                //select = select.BuildExtendSelectExpre();
                //var q = from a in this.Db.GetIQueryable<HR_FormalEmployees>().AsExpandable()
                //        join b in this.Db.GetIQueryable<HR_Induction>() on a.F_Id equals b.F_UserId into indu
                //        from indud in indu.DefaultIfEmpty()
                //        join c in this.Db.GetIQueryable<Base_Department>() on a.F_DepartmentId equals c.Id into Dep
                //        from Depart in Dep.DefaultIfEmpty()
                //        join d in this.Db.GetIQueryable<Base_Company>() on a.F_CompanyId equals d.F_Id into Com
                //        from Company in Com.DefaultIfEmpty()
                //        join e in this.Db.GetIQueryable<Base_Post>() on a.F_PositionId equals e.F_Id into Post
                //        from PostEntity in Post.DefaultIfEmpty()
                //        join h in this.Db.GetIQueryable<HR_LaborContractInfo>() on a.F_Id equals h.UserId into labor
                //        from labore in labor.DefaultIfEmpty()
                //        join g in this.Db.GetIQueryable<HR_Positive>() on a.F_Id equals g.F_UserId into posi
                //        from positive in posi.DefaultIfEmpty()
                //        join m in this.Db.GetIQueryable<HR_Departure>() on a.F_Id equals m.F_UserId into Depae
                //        from depaet in Depae.DefaultIfEmpty()
                //        join y in this.Db.GetIQueryable<HR_EmploEducationExp>() on a.F_Id equals y.UserId into emplo
                //        from emploedu in emplo.DefaultIfEmpty()
                //        select @select.Invoke(a, indud, Depart, Company, PostEntity, labore, positive, depaet, emploedu);
                var strSql = new StringBuilder();
                strSql.Append(@" SELECT  
                    * from view_EmployeesBasicInfo");
                var search = input.Search;
                var q = new List<HR_EmployeesBasicInfoDTO>();
                //筛选
                if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
                {
                    strSql.Append($" Where {search.Condition} like  '%{search.Keyword}%' ");
                    q = this.Db.GetListBySql<HR_EmployeesBasicInfoDTO>(strSql.ToString());
                }
                else
                {
                    q = this.Db.GetListBySql<HR_EmployeesBasicInfoDTO>(strSql.ToString());
                }
                if (input.PostId?.Count > 0)//
                {
                    q = q.Where(i => input.PostId.Contains(i.DepartmentId)).ToList();
                }
                if (input.IsInterview?.Count > 0)//
                {
                    q = q.Where(i => input.IsInterview.Contains(i.CompanyId)).ToList();
                }
                if (input.RoundInterview?.Count > 0)//
                {
                    q = q.Where(i => input.RoundInterview.Contains(i.EmployRelStatus)).ToList();
                }
                var where = LinqHelper.True<HR_EmployeesBasicInfoDTO>();

                return q.AsQueryable().Where(where);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
        }
        public List<HR_EmployeesBasicInfoDTO> GetList(List<HR_EmployeesBasicInfoDTO> list)
        {
            if (list.Count() > 0)
            {
                //查询用户的主键
                var F_Ids = list
                            .Where(i => !i.F_Id.IsNullOrEmpty())
                            .Select(i => i.F_Id).ToList();
                //查询社会工作经历
                var socialWorkExps = this.Db.GetIQueryable<HR_SocialWorkExp>()
                                    .Where(i => F_Ids.Contains(i.F_UserId))
                                    .ToList();
                //查询所有绩效
                var hR_PerformanceInfos = this.Db.GetIQueryable<HR_PerformanceInfo>()
                                        .Where(i => F_Ids.Contains(i.UserId))
                                        .ToList();
                //查询所有的公司任职经历
                var companyEmploys = this.Db.GetIQueryable<HR_CompanyEmploy>()
                                    .Where(i => F_Ids.Contains(i.F_UserId))
                                    .ToList();
                //查询所有奖惩
                var disciplinaryRecords = this.Db.GetIQueryable<HR_DisciplinaryRecords>()
                                        .Where(i => F_Ids.Contains(i.UserId))
                                        .ToList();
                list.ForEach(i =>
                {
                    i.DecryptFormal();
                    //    过往工作经验最近 = "",
                    //    过往工作经验次近 = "",
                    var socialWorkExps1 = socialWorkExps
                                           .Where(z => z.F_UserId == i.F_Id && z.F_StartTime.HasValue && z.F_EndTime.HasValue)
                                           .ToList();
                    var socialWorkExp = socialWorkExps1
                                        ?.OrderByDescending(z => z.F_StartTime)
                                        .FirstOrDefault();
                    i.RWorkExperience = socialWorkExp != null ? Convert.ToDateTime(socialWorkExp.F_StartTime).ToString("yyyy-MM-dd") +
                                    "至" + Convert.ToDateTime(socialWorkExp.F_EndTime).ToString("yyyy-MM-dd")
                                 + (!socialWorkExp.F_CompanyName.IsNullOrEmpty() ? socialWorkExp.F_CompanyName : "") +
                                 (!socialWorkExp.F_Position.IsNullOrEmpty() ? socialWorkExp.F_Position : "") : "";
                    var SecondsocialWorkExp = socialWorkExps1?.OrderByDescending(z => z.F_StartTime)?.Skip(1).Take(1).FirstOrDefault();
                    i.PWorkExperience = SecondsocialWorkExp != null ? Convert.ToDateTime(SecondsocialWorkExp.F_StartTime).ToString("yyyy-MM-dd")
                                    + "至" + Convert.ToDateTime(SecondsocialWorkExp.F_EndTime).ToString("yyyy-MM-dd")
                                 + (!SecondsocialWorkExp.F_CompanyName.IsNullOrEmpty() ? SecondsocialWorkExp.F_CompanyName : "") +
                                 (!SecondsocialWorkExp.F_Position.IsNullOrEmpty() ? SecondsocialWorkExp.F_Position : "") : "";
                    var per = hR_PerformanceInfos
                                            .Where(z => z.UserId == i.F_Id).ToList();
                    if (per.Count() > 0)
                    {
                        i.PastPerformance3 = per.FirstOrDefault(z => z.PerformanceAppYear == (DateTime.Now.Year - 3))?.
                                                PerformanceRating;
                        i.PastPerformance2 = per.FirstOrDefault(z => z.PerformanceAppYear == (DateTime.Now.Year - 2))?.
                                                PerformanceRating;
                        i.PastPerformance1 = per.FirstOrDefault(z => z.PerformanceAppYear == (DateTime.Now.Year - 1))?.
                                                PerformanceRating;
                    }
                    var hR_CompanyEmploys = companyEmploys
                                         .Where(z => z.F_UserId == i.F_Id)
                                         .OrderByDescending(z => z.F_StartTime)
                                         .ToList();
                    if (hR_CompanyEmploys.Count() > 0)
                    {
                        string htDetails = string.Join(",", hR_CompanyEmploys
                                .Select(t => t.F_StartTime + "至" + t.F_EndTime + t.F_ForType +
                                t.F_PositionInfo).Distinct().ToArray());
                        i.PromotionRecord = Convert.ToDateTime(hR_CompanyEmploys.FirstOrDefault().F_StartTime).ToString("yyyy-MM-dd") + "至" +
                                 Convert.ToDateTime(hR_CompanyEmploys.FirstOrDefault().F_EndTime).ToString("yyyy-MM-dd")
                                 + hR_CompanyEmploys.FirstOrDefault().F_ForType +
                                 hR_CompanyEmploys.FirstOrDefault().F_PositionInfo;
                        i.PostChangeRecord = htDetails;
                    }

                    var hR_Disciplinaries = disciplinaryRecords
                                            .Where(z => z.UserId == i.F_Id)
                                            .ToList();
                    if (hR_Disciplinaries.Count() > 0)
                    {
                        string htDetails = string.Join(",", hR_Disciplinaries
                            .Select(t => t.DisciplinaryStartTime + "至" + t.DisciplinaryEndTime + t.DisciplinaryContent)
                            .Distinct().ToArray());
                        i.DisciplinaryRecord = htDetails;
                    }
                });

            }
            return list;
        }
        public async Task<PageResult<HR_EmployeesBasicInfoDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            try
            {
                var hR_PersonnelChangeDetailsDTOs = GetHR_PersonnelChangeDetails(input);
                var count = hR_PersonnelChangeDetailsDTOs.Count();
                var list = hR_PersonnelChangeDetailsDTOs
                    .OrderBy($@"{input.SortField} {input.SortType}")
                    .Skip((input.PageIndex - 1) * input.PageRows)
                    .Take(input.PageRows)
                    .ToList();
                list = GetList(list);
                if (list.Count > 0)
                {
                    foreach (var item in list)
                    {
                        item.DecryptFormal();
                    }
                }
                return new PageResult<HR_EmployeesBasicInfoDTO> { Data = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
        }
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var list = GetHR_PersonnelChangeDetails(input).ToList();
            list = GetList(list);
            return list.ToDataTable();
        }
    }
}