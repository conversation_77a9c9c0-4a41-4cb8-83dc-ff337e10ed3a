﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System.Linq.Expressions;
using System;
using AutoMapper;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_RecruitPlanBusiness : BaseBusiness<HR_RecruitPlan>, IHR_RecruitPlanBusiness, ITransientDependency
    {
        readonly IMapper _mapper;
        public HR_RecruitPlanBusiness(IDbAccessor db, IMapper mapper)
            : base(db)
        {
            _mapper = mapper;
        }

        #region 外部接口

        public async Task<PageResult<HR_RecruitPlanDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_RecruitPlan, HR_Recruit, HR_RecruitPlanDTO>> select = (t, a) => new HR_RecruitPlanDTO
            {
                F_RecruitName = a.F_RecruitName,
                F_Role = a.F_Role,
                F_Salary = a.F_Salary,
                F_Type = a.F_Type,
                F_City = a.F_City,
                F_Address = a.F_Address,
                Recruit = a
            };
            select = select.BuildExtendSelectExpre();
            var q = from t in this.Db.GetIQueryable<HR_RecruitPlan>().AsExpandable()
                    join a in this.Db.GetIQueryable<HR_Recruit>() on t.F_RecruitId equals a.F_Id into rec
                    from a in rec.DefaultIfEmpty()
                    select @select.Invoke(t, a);
            var where = LinqHelper.True<HR_RecruitPlanDTO>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RecruitPlanDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }
        public HR_RecruitPlanDTO GetTheData(string id)
        {
            HR_RecruitPlanDTO retValue = null;

            HR_RecruitPlan entity = GetEntity(id);
            if(entity != null)
            {
                retValue = _mapper.Map<HR_RecruitPlanDTO>(entity);
                HR_Recruit hR_Recruit = Db.GetIQueryable<HR_Recruit>().FirstOrDefault(x => x.F_Id == entity.F_RecruitId);
                if(hR_Recruit != null)
                {
                    retValue.Recruit = hR_Recruit;
                }
            }

            return retValue;
        }
        public async Task<HR_RecruitPlan> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_RecruitPlan data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_RecruitPlan data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RecruitPlan>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RecruitPlan, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_RecruitPlan, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}