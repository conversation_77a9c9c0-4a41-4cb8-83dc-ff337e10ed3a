﻿using Coldairarrow.Api.Word;
using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HR_ReportFormsManage;
using Coldairarrow.Util;
using Coldairarrow.Util.Excel;
using Coldairarrow.Util.Helper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing.Template;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using sun.swing;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;

namespace Coldairarrow.Api.Controllers.HR_EmployeeInfoManage
{
    [Route("/HR_EmployeeInfoManage/[controller]/[action]")]
    public class HR_FormalEmployeesController : BaseApiController
    {
        #region DI

        public HR_FormalEmployeesController(IHR_FormalEmployeesBusiness hR_FormalEmployeesBus,
                                            IHR_PositiveBusiness hR_PositiveBus,
                                            IHR_InductionBusiness hR_InductionBus,
                                            IBase_UserBusiness base_UserBusiness,
                                            IConfiguration configuration,
                                           IHR_SocialWorkExpBusiness hR_SocialWorkExpBus,
                                         IHR_EmploEducationExpBusiness hR_EmploEducationExpBus,
                                         IHR_CompanyEmployBusiness hR_CompanyEmployBus,
                                         IHR_LaborContractInfoBusiness hR_LaborContractInfoBus,
                                         IHR_PerformanceInfoBusiness hR_PerformanceInfoBus,
                                         IHR_PersonPhotoBusiness hR_PersonPhotoBus,
                                         IBase_UserBusiness userBus)

        {
            _hR_FormalEmployeesBus = hR_FormalEmployeesBus;
            _hR_PositiveBus = hR_PositiveBus;
            _hR_InductionBus = hR_InductionBus;
            _base_UserBusiness = base_UserBusiness;
            _hR_SocialWorkExpBus = hR_SocialWorkExpBus;
            _hR_EmploEducationExpBus = hR_EmploEducationExpBus;
            _hR_CompanyEmployBus = hR_CompanyEmployBus;
            _hR_LaborContractInfoBus = hR_LaborContractInfoBus;
            _hR_PerformanceInfoBus = hR_PerformanceInfoBus;
            _hR_PersonPhotoBus = hR_PersonPhotoBus;
            _userBus = userBus;
        }
        IHR_PersonPhotoBusiness _hR_PersonPhotoBus { get; }
        IHR_FormalEmployeesBusiness _hR_FormalEmployeesBus { get; }
        IHR_PositiveBusiness _hR_PositiveBus { get; }
        IHR_SocialWorkExpBusiness _hR_SocialWorkExpBus { get; }
        IHR_InductionBusiness _hR_InductionBus { get; }
        IBase_UserBusiness _base_UserBusiness { get; }
        IConfiguration _configuration { get; }
        IHR_EmploEducationExpBusiness _hR_EmploEducationExpBus { get; }
        IHR_CompanyEmployBusiness _hR_CompanyEmployBus { get; }
        IHR_LaborContractInfoBusiness _hR_LaborContractInfoBus { get; }
        IHR_PerformanceInfoBusiness _hR_PerformanceInfoBus { get; }
        IBase_UserBusiness _userBus { get; }
        #endregion

        #region 获取
        [HttpPost]
        public async Task<PageResult<HR_FormalEmployeesDTO>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_FormalEmployeesBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<PageResult<HR_FormalEmployeesDTO>> GetMyTeamsList(PageInput<ConditionDTO> input)
        {
            var usermodel = GetOperator();
            return await _hR_FormalEmployeesBus.GetMyTeamsList(input, usermodel.UserId);
        }

        [HttpPost]
        public async Task<HR_FormalEmployeesTheDTO> GetTheData(IdInputDTO input)
        {
            return await _hR_FormalEmployeesBus.GetFormDataAsync(input.id);
        }
        //[HttpPost]
        //public async Task<HR_FormalEmployeesTheDTO> GetTheData(IdInputDTO input)
        //{
        //    return await _hR_FormalEmployeesBus.GetFormDataAsync(input.id);
        //}
        [HttpPost]
        public async Task<HR_FormalEmployeesTheDTO> GetInfoTheData(IdInputDTO input)
        {
            return await _hR_FormalEmployeesBus.GetFormDataAsync(input.id);
        }
        [HttpPost]
        public async Task<PageResult<HR_FormalEmployeesDetailsDTO>> GetDetailsTheData(PageInput<ConditionDTO> input)
        {
            return await _hR_FormalEmployeesBus.GetDetailsFormDataAsync(input);
        }
        /// <summary>
        /// 获得当前登录用户信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public HR_FormalEmployeesDTO GetLoginEmp()
        {
            var op = this.GetOperator();
            return _hR_FormalEmployeesBus.GetDataByUserId(op?.UserId);
        }
        [NoCheckJWT]
        [HttpPost]
        public HR_FormalEmployeesDTO GetLoginEmpMini()
        {
            var id = HttpContext.Request.Form["id"].ToString();
            return _hR_FormalEmployeesBus.GetDataByFId(id);
        }
        [HttpPost]
        public AjaxResult GetOAToken()
        {
            var op = this.GetOperator();
            var url = _configuration["OAUrl"] + _configuration["OAUrls:GetToken"];
            return Success(OAHelper.GetOAToken(url, op.ADName));
        }
        /// <summary>
        /// 取最大的整数编码
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult GetMaxCode()
        {
            return Success(_hR_FormalEmployeesBus.GetMaxCode());
        }
        /// <summary>
        /// 筛选信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult GetScreeningInfo(DataInputDTO data)
        {
            var temp = _hR_FormalEmployeesBus.GetScreeningInfo(data);
            return Success(temp);
        }

        [HttpPost]
        /// <summary>
        /// 员工关怀推送
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task GetUserPush()
        {
            await _hR_FormalEmployeesBus.GetUserPush();
        }
        #endregion

        #region 提交

        [HttpPost]
        public AjaxResult SaveData(HR_FormalEmployeesTheDTO data)
        {
            try
            {
                _hR_FormalEmployeesBus.SaveData(data);
                //using (TransactionScope scope = new TransactionScope())
                //{

                //    scope.Complete();
                //}
                return Success();
            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_FormalEmployeesBus.DeleteDataAsync(ids);
        }

        /// <summary>
        /// 业务删除正式员工
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task BusDeleteData(List<string> ids)
        {
            await _hR_FormalEmployeesBus.BusDeleteDataAsync(ids);
        }
        /// <summary>
        /// 员工解绑方法
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task UnBindUsers(IdInputDTO input)
        {
            await _hR_FormalEmployeesBus.UnBindUsers(input.id);
        }
        /// <summary>
        /// 模板下载(公共的)
        /// </summary>
        [HttpPost]
        public FileContentResult DownloadTemplate(IdInputDTO input)
        {
            string filePath = Directory.GetCurrentDirectory() + input.id;
            if (FileHelper.Exists(filePath))
            {
                var bys = System.IO.File.ReadAllBytes(filePath);//excel表转换成流
                return File(bys, "application/octet-stream");
            }
            else
            {
                throw new BusException("找不到模板");
            }
        }
        #endregion

        #region 导入Excel
        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task<IActionResult> UploadFileByUserInfo()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();

            ht["companyName"] = "服务公司";
            ht["laborCompanyName"] = "合同签订公司";
            ht["firstDepName"] = "一级架构";
            ht["seconDepName"] = "二级架构";
            ht["userName"] = "姓名";
            ht["sex"] = "性别";
            ht["postName"] = "职务";
            ht["rankName"] = "职级";
            ht["inductionDate"] = "入司时间";
            ht["idCard"] = "身份证号码";
            var list = new ExcelHelper<HR_FormalEmployeeImportDTO>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                await _hR_FormalEmployeesBus.UploadFileByUserInfo(list);
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }
        #endregion

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_FormalEmployeesBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "正式员工.xlsx";
                excelconfig.Title = "正式员工";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "employeescode", ExcelColumn = "员工编码", Alignment = "left", Sort = 1 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "nameuser", ExcelColumn = "姓名", Alignment = "left", Sort = 2 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "departmentname", ExcelColumn = "所属部门", Alignment = "left", Sort = 3 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "postname", ExcelColumn = "任职职位", Alignment = "left", Sort = 4 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "sextext", ExcelColumn = "性别", Alignment = "left", Sort = 5 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "idcardnumber", ExcelColumn = "身份证号码", Alignment = "left", Sort = 6 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "dirthdatetext", ExcelColumn = "出生日期", Alignment = "left", Sort = 7 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "employrelstatus", ExcelColumn = "用工关系状态", Alignment = "left", Sort = 8 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "createdate", ExcelColumn = "创建时间", Alignment = "left", Sort = 9 });
                //ExcelHelper.ExcelDownload(exportTable, excelconfig);
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        /// <summary>
        /// Excel导出下载(导出最新职级)
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelPositionDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_FormalEmployeesBus.GetPositionExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "员工最新职级单.xlsx";
                excelconfig.Title = "员工最新职级单";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "nameuser", ExcelColumn = "姓名", Alignment = "left", Sort = 1 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "nd", ExcelColumn = "任职开始时间", Alignment = "left", Sort = 2 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "nd2", ExcelColumn = "任职结束时间", Alignment = "left", Sort = 3 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "postname", ExcelColumn = "任职职位", Alignment = "left", Sort = 4 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "sextext", ExcelColumn = "组织信息", Alignment = "left", Sort = 5 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "employrelstatus", ExcelColumn = "用工关系", Alignment = "left", Sort = 6 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_rank", ExcelColumn = "职级", Alignment = "left", Sort = 7 });
                //ExcelHelper.ExcelDownload(exportTable, excelconfig);
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion
        #region 套打
        /// <summary>
        /// 个人简历套打
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        [HttpPost]
        public async Task<FileContentResult> CurriculumVitaeDownload(ConditionDTO input)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(input.Keyword))
                {
                    throw new Exception("请选择要套打的个人信息");
                }

                string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "BusinessTemplate", "个人履历套打模版.docx");
                string newpath = $"/JL/" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".docx";
                string newFilePath = GetAbsolutePath($"~{newpath}");
                Directory.CreateDirectory(Path.GetDirectoryName(newFilePath));
                System.IO.File.Copy(templatePath, newFilePath);

                var hR_Employees = JsonConvert.DeserializeObject<HR_EmployeesBasicInfoDTO>(input.Keyword);

                var formalEmployeesTheDTO = await _hR_FormalEmployeesBus.GetFormDataAsync(hR_Employees.F_Id);
                var hR_SocialWorkExps = await _hR_SocialWorkExpBus.GetDataListByUserIdAsync(hR_Employees.F_Id);
                var hR_EmploEducationExpDTOs = await _hR_EmploEducationExpBus.GetDataListByUserIdAsync(hR_Employees.F_Id);
                var hR_CompanyEmploys = await _hR_CompanyEmployBus.GetDataListByUserIdAsync(hR_Employees.F_Id);
                var hR_Performances = await _hR_PerformanceInfoBus.GetDataListByUserIdAsync(hR_Employees.F_Id);
                var user = new Base_User();
                var email = "";

                if (!string.IsNullOrWhiteSpace(formalEmployeesTheDTO.BaseUserId))
                {
                    user = _userBus.GetBase_User(formalEmployeesTheDTO.BaseUserId);
                    if (user != null)
                    {
                        email = user.UserName.Contains("cqlandmark.com") ? user.UserName : user.UserName + "@cqlandmark.com";
                    }
                }

                Dictionary<string, string> books = new Dictionary<string, string>
            {
                { "username", hR_Employees.NameUser },
                { "sex", hR_Employees.SexText },
                { "nation", formalEmployeesTheDTO.NationalInfo },
                { "nativeplace", formalEmployeesTheDTO.NativePlace },
                { "registeredresidence", formalEmployeesTheDTO.RegisteredResidence },
                { "dirthdate", formalEmployeesTheDTO.DirthDate?.ToString("yyyy.MM.dd") ?? "" },
                { "politicallandscape", formalEmployeesTheDTO.PoliticalLandscape },
                { "maritalstatus", formalEmployeesTheDTO.MaritalStatusText },
                { "mobilephone", formalEmployeesTheDTO.MobilePhone },
                { "recordformalschooling", hR_Employees.RecordSchooling },
                { "degrees", hR_Employees.RecordSchooling },
                { "email", email },
                { "departmentname", hR_Employees.ContractCompany + "-" + hR_Employees.DepartmentName },
                { "postname", hR_Employees.PostName },
                { "rank", hR_Employees.F_Rank },
                { "education", hR_EmploEducationExpDTOs.Any() ? string.Join("\r\n", hR_EmploEducationExpDTOs.OrderByDescending(x => x.F_StartDate).Select(x => x.GraduatedSchool + "," + x.Professional + "," + x.RecordSchooling)) : "" },
                { "work", hR_SocialWorkExps.Any() ? string.Join("\r\n", hR_SocialWorkExps.OrderByDescending(x => x.F_StartTime).Select(x => x.F_StartTime.Value.ToString("yyyy-MM-dd") + " 至 " + x.F_EndTime.Value.ToString("yyyy-MM-dd") + "," + x.F_CompanyName + " 任职 " + x.F_Position)) : "" },
                { "family", hR_CompanyEmploys.Any() ? string.Join("\r\n", hR_CompanyEmploys.OrderByDescending(x => x.F_StartTime).Select(x => x.F_StartTime.Value.ToString("yyyy/MM") + "-" + (x.F_EndTime.HasValue ? x.F_EndTime.Value.ToString("yyyy/MM") : "至今") + " , " + x.F_OrganizeInfo + "," + x.F_PositionInfo)) : "" },
                { "performanceinfo", hR_Performances.Any() ? string.Join("\r\n", hR_Performances.OrderByDescending(x => x.PerformanceAppYear).Select(x => x.PerformanceAppYear.Value + " 年绩效考核成绩," + x.PerformanceRating)) : "" }
            };

                var byteData = GetImageBytes(Path.Combine(Directory.GetCurrentDirectory(), "BusinessTemplate", "head.png"));
                var imgType = "png";
                Dictionary<string, byte[]> images = new Dictionary<string, byte[]>();

                if (!string.IsNullOrEmpty(hR_Employees.F_Id))
                {
                    var _PersonPhoto = await _hR_PersonPhotoBus.GetTheDataByPersonIdAsync(hR_Employees.F_Id);
                    if (_PersonPhoto != null)
                    {
                        byteData = _PersonPhoto.FImageData;
                        imgType = _PersonPhoto.FimageContentType;
                        images.Add("photo", byteData);
                    }
                }

                var str = WordBase.WordHandleNew(newFilePath, books, images);
                // 确保文件存在
                if (!System.IO.File.Exists(newFilePath))
                {
                    throw new FileNotFoundException($"文件未找到: {newFilePath}");
                }
                // 读取文件内容并转换为字节数组
                byte[] fileBytes = System.IO.File.ReadAllBytes(newFilePath);
                return File(fileBytes, "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "curriculum_vitae.docx");
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private static byte[] GetImageBytes(string imagePath)
        {
            using (FileStream fs = new FileStream(imagePath, FileMode.Open))
            {
                byte[] buffer = new byte[fs.Length];
                fs.Read(buffer, 0, buffer.Length);
                return buffer;
            }
        }
        #endregion
    }

}