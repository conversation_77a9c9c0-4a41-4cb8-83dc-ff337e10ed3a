﻿
using Coldairarrow.Business.HR_ReportFormsManage;
using Coldairarrow.Entity.HR_ReportFormsManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.HR_ReportFormsManage
{
    [Route("/HR_ReportFormsManage/[controller]/[action]")]
    public class HR_PersonnelChangeController : BaseApiController
    {
        #region DI

        public HR_PersonnelChangeController(IHR_PersonnelChangeBusiness hR_PersonnelChangeBusiness)
        {
            _hR_PersonnelChangeBusiness = hR_PersonnelChangeBusiness;
        }

        IHR_PersonnelChangeBusiness _hR_PersonnelChangeBusiness { get; }

        #endregion

        #region 获取

        [HttpPost]
        public PageResult<HR_PersonnelChangeDTO> GetDataList(PageInput<ConditionDTO> input)
        {
            return  _hR_PersonnelChangeBusiness.GetDataListAsync(input);
        }
        [HttpPost]
        public List<HR_PersonnelChangeDTO> GetTreeDataList(PageInput<ConditionDTO> input)
        {
            return _hR_PersonnelChangeBusiness.GetTreeDataList(input);
        }
        #endregion

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            {
                var t= _hR_PersonnelChangeBusiness.ExcelDownload(input);
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                return file;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
    }
}