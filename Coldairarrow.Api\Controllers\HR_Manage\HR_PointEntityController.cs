﻿using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using System.Text;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using Coldairarrow.Entity.Shop_Manage.Enum;


namespace Coldairarrow.Api.Controllers.HR_Manage
{
    [Route("/HR_Manage/[controller]/[action]")]
    public class HR_PointEntityController : BaseApiController
    {
        #region DI

        public HR_PointEntityController(IHR_PointEntityBusiness hR_PointEntityBus, IHR_PointUseDetailBusiness hR_PointUseDetailBus)
        {
            _hR_PointEntityBus = hR_PointEntityBus;
            _hR_PointUseDetailBus = hR_PointUseDetailBus;
        }

        IHR_PointEntityBusiness _hR_PointEntityBus { get; }

        IHR_PointUseDetailBusiness _hR_PointUseDetailBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_PointEntity>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_PointEntityBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_PointEntity> GetTheData(IdInputDTO input)
        {
            return await _hR_PointEntityBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_PointEntity data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_PointEntityBus.AddDataAsync(data);
            }
            else
            {
                await _hR_PointEntityBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_PointEntityBus.DeleteDataAsync(ids);
        }

        //根据hrId获取余额，小程序用
        [HttpPost]
        public AjaxResult GetPoint(IdInputDTO idInput)
        {
            try
            {
                var point = _hR_PointEntityBus.GetThePoint(idInput.userId);
                if (point != null)
                {
                    return Success(Math.Round(point.F_ResNumber.Value, 2));
                }
                else
                {
                    return Success(0);
                }
            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        //创建食堂消费订单
        [NoCheckJWT]
        [HttpPost]
        public async Task<AjaxResult> CreatDineOrder()
        {
            try
            {
                string hrId = HttpContext.Request.Form["hrId"].ToString();
                string hrCode = HttpContext.Request.Form["hrCode"].ToString();
                string UserName = HttpContext.Request.Form["realName"].ToString();
                string orderId = HttpContext.Request.Form["OrderId"].ToString();
                string price = HttpContext.Request.Form["price"].ToString();
                //创建消费记录
                var entity = _hR_PointEntityBus.GetThePoint(hrId);
                var backNumber = entity.F_ResNumber - Decimal.Parse(price);
                HR_PointUseDetail data = new HR_PointUseDetail()
                {
                    F_ResType = ShopEnum.PointBuyType.减少,
                    F_OrderType = ShopEnum.ShopType.堂食,
                    F_UseNumber = Decimal.Parse(price),
                    F_Id = Guid.NewGuid().ToString("N"),
                    F_CreateDate = DateTime.Now,
                    F_UserId = hrId,
                    F_CreateUserId = "Wechat",
                    F_CreateUserName = "小程序",
                    F_OrderId = orderId,
                    F_ResNumer = entity.F_ResNumber,
                    F_BackNumer = backNumber,
                    F_Describe = "堂食消费",
                    F_Month = DateTime.Now.ToString("yyyy-MM"),
                    F_UserCode = hrCode,
                    F_UserName = UserName
                };
                await _hR_PointUseDetailBus.AddDataAsync(data);
                //更新主表

                entity.F_ResNumber = backNumber;
                await _hR_PointEntityBus.UpdateDataAsync(entity);
                return Success(0);

            }
            catch (Exception ex)
            {
                return Error("失败" + ex.Message);
            }
        }

        public static bool CheckValidationResult(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
        {
            return true;
        }
        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_PointEntityBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                //excelconfig.HeadFont = "微软雅黑";
                //excelconfig.HeadHeight = 15;
                //excelconfig.HeadPoint = 11;
                excelconfig.FileName = "员工剩余福豆导出.xlsx";
                excelconfig.Title = "员工剩余福豆导出";
                excelconfig.IsAllSizeColumn = true;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_iyear", ExcelColumn = "年份", Sort = 1 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_username", ExcelColumn = "员工姓名", Sort = 2});
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_resnumber", ExcelColumn = "剩余福豆", Sort = 3});
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<HR_PointEntity>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _hR_PointEntityBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}