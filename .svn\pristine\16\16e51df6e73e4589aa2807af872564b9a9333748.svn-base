﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Coldairarrow.Entity.Wechat_Shekou
{
    /// <summary>
    /// Shekou_Contract
    /// </summary>
    [Table("Shekou_Contract")]
    public class Shekou_Contract
    {

        /// <summary>
        /// F_Id
        /// </summary>
       [Key, Column(Order = 1)]
        public String F_Id { get; set; }

        /// <summary>
        /// F_Name
        /// </summary>
        public String F_Name { get; set; }

        /// <summary>
        /// F_User
        /// </summary>
        public String F_User { get; set; }

        /// <summary>
        /// F_Mobile
        /// </summary>
        public String F_Mobile { get; set; }

        /// <summary>
        /// F_UpdateUser
        /// </summary>
        public String F_UpdateUser { get; set; }

        /// <summary>
        /// F_UpdateTime
        /// </summary>
        public DateTime? F_UpdateTime { get; set; }

        /// <summary>
        /// F_CreateTime
        /// </summary>
        public DateTime? F_CreateTime { get; set; }

        /// <summary>
        /// F_CreateUser
        /// </summary>
        public String F_CreateUser { get; set; }

    }
}