﻿<template>
  <a-modal :title="title" width="60%" :visible="visible" :confirmLoading="loading" @ok="handleSubmit" @cancel="
      () => {
        this.visible = false
      }
    ">
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="entity" :rules="rules">
        <a-row :gutter="24">
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="开始时间" prop="F_StartTime" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-date-picker v-model="entity.F_StartTime" show-time type="date" format="YYYY-MM-DD"
                  placeholder="请选择开始时间" style="width: 100%;" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="结束时间" prop="F_EndTime" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-date-picker v-model="entity.F_EndTime" show-time type="date" format="YYYY-MM-DD"
                  placeholder="请选择结束时间" style="width: 100%;" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="组织信息" prop="F_OrganizeInfo" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.F_OrganizeInfo" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="职位信息" prop="F_PositionInfo" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-input-search v-model="entity.F_PositionInfo" placeholder="" style="width: 100%"
                  @search="onPostSearch" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="用工关系状态" prop="F_EmployRelStatus" :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 14 }">
                <a-select v-model="entity.F_EmployRelStatus" style="width: 100%">
                  <a-select-option v-for="item in empRelStatusDic" :value="item.F_ItemValue" :key="item.F_ItemValue">
                    {{ item.F_ItemName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="职级" prop="F_Rank" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <SelectDiction ref="F_Rank" :Name="'职级'" @selectedvalue="selectRank" :Value="entity.F_Rank"
                  style="width: 100%;"></SelectDiction>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="任职类型" prop="F_ForType" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-select v-model="entity.F_ForType" style="width: 100%">
                  <a-select-option v-for="item in forTypeDic" :value="item.F_ItemValue" :key="item.F_ItemValue">
                    {{ item.F_ItemName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="变动操作" prop="F_ChangesOperating" :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 14 }">
                <a-select v-model="entity.F_ChangesOperating" style="width: 100%">
                  <a-select-option v-for="item in changesOperatingDic" :value="item.F_ItemValue"
                    :key="item.F_ItemValue">
                    {{ item.F_ItemName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="变动类型" prop="F_ChangesType" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-select v-model="entity.F_ChangesType" style="width: 100%">
                  <a-select-option v-for="item in changesTypeDic" :value="item.F_ItemValue" :key="item.F_ItemValue">
                    {{ item.F_ItemName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="变动原因" prop="F_ChangesReason" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-select v-model="entity.F_ChangesReason" style="width: 100%">
                  <a-select-option v-for="item in changesReasonDic" :value="item.F_ItemValue" :key="item.F_ItemValue">
                    {{ item.F_ItemName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="备注" :labelCol="{ span: 3 }" :wrapperCol="{ span: 19 }">
                <a-textarea v-model="entity.F_Remark" placeholder="请输入" :rows="4" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-row>
      </a-form-model>
    </a-spin>
    <select-post ref="selectPost" :callBack="SeletedPost"></select-post>
  </a-modal>
</template>

<script>
import moment from 'moment'
import SelectPost from '@/components/SelectPost/PostList'
import SelectDiction from '@/components/SelectDictionaries/DictionariesList'
export default {
  components: {
    SelectPost,
    SelectDiction
  },
  props: {
    parentObj: Object,
    title: {
      type: String,
      default: ''
    },
    userId: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      visible: false,
      loading: false,
      forTypeDic: [], //任职类型
      empRelStatusDic: [], //用工关系状态
      changesOperatingDic: [], //变动操作
      changesTypeDic: [], //任职变动类型
      changesReasonDic: [], //任职变动原因
      entity: {},
      rules: {}
    }
  },
  methods: {
    selectRank (value) {
      this.entity.F_Rank = value
    },
    onPostSearch () {
      this.$refs.selectPost.openForm('选择职位')
    },
    SeletedPost (post) {
      this.entity.F_PositionInfo = post[0].F_Name
      this.entity.F_PositionId = post[0].F_Id
      this.entity.F_OrganizeInfo = post[0].OrgName
    },
    init () {
      this.commonApi.dictTypeInfo('任职类型').then(res => {
        this.forTypeDic = res
      })
      this.commonApi.dictTypeInfo('用工关系状态').then(res => {
        this.empRelStatusDic = res
      })
      this.commonApi.dictTypeInfo('任职变动操作').then(res => {
        this.changesOperatingDic = res
      })
      this.commonApi.dictTypeInfo('任职变动类型').then(res => {
        this.changesTypeDic = res
      })
      this.commonApi.dictTypeInfo('任职变动原因').then(res => {
        this.changesReasonDic = res
      })
      this.visible = true
      this.entity = {
        F_StartTime: null,
        F_EndTime: null,
        F_OrganizeInfo: '',
        F_PositionInfo: '',
        F_EmployRelStatus: '',
        F_Rank: '',
        F_ForType: '',
        F_ChangesOperating: '',
        F_ChangesType: '',
        F_ChangesReason: '',
        F_Remark: ''
      }
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    openForm (id) {
      this.init()

      if (id) {
        this.loading = true
        this.$http.post('/HR_EmployeeInfoManage/HR_CompanyEmploy/GetTheData', { id: id }).then(resJson => {
          this.loading = false

          if (resJson.Data['F_StartTime']) {
            resJson.Data['F_StartTime'] = moment(resJson.Data['F_StartTime'])
          }
          if (resJson.Data['F_EndTime']) {
            resJson.Data['F_EndTime'] = moment(resJson.Data['F_EndTime'])
          }
          console.log(resJson.Data)

          this.entity = resJson.Data
        })
      }
    },
    handleSubmit () {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.loading = true
        //是否系统生成，1：是、0：否
        this.entity.F_IsSystemGen = 0
        this.entity.F_WorkExpInOrOut = 0
        this.entity.F_UserId = this.userId
        this.$http.post('/HR_EmployeeInfoManage/HR_CompanyEmploy/SaveData', this.entity).then(resJson => {
          this.loading = false

          if (resJson.Success) {
            this.$message.success('操作成功!')
            this.visible = false

            this.parentObj.getDataList()
          } else {
            this.$message.error(resJson.Msg)
          }
        })
      })
    }
  }
}
</script>
