﻿using Coldairarrow.Business.HR_AttendanceManage;
using Coldairarrow.Entity.HR_AttendanceManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using System.Transactions;
using Coldairarrow.Entity;
using Microsoft.Extensions.Configuration;

namespace Coldairarrow.Api.Controllers.HR_AttendanceManage
{
    [Route("/HR_Attendance/[controller]/[action]")]
    public class HR_WorkOvertimeController : BaseApiController
    {
        #region DI

        public HR_WorkOvertimeController(IHR_WorkOvertimeBusiness hR_WorkOvertimeBus, IHR_WorkOvertimeGroupBusiness hR_WorkOvertimeGroupBus, IConfiguration configuration)
        {
            _hR_WorkOvertimeBus = hR_WorkOvertimeBus;
            _hR_WorkOvertimeGroupBus = hR_WorkOvertimeGroupBus;
            _configuration = configuration;
        }

        IHR_WorkOvertimeBusiness _hR_WorkOvertimeBus { get; }
        IHR_WorkOvertimeGroupBusiness _hR_WorkOvertimeGroupBus { get; }
        readonly IConfiguration _configuration;
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_WorkOvertimeDTO>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_WorkOvertimeBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_WorkOvertimeDTO> GetTheData(IdInputDTO input)
        {
            return await _hR_WorkOvertimeBus.GetFormDataAsync(input.id);
        }
        /// <summary>
        /// 获取单人加班表单数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<HR_WorkOvertimeDTO> GetFormDataAsync(IdInputDTO input)
        {
            return await _hR_WorkOvertimeBus.GetFormDataAsync(input.id);
        }

        [HttpPost]
        /// <summary>
        /// 根据分组id获取加班所有数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<WorkOverTimeGroup> GetOverTimeGroup(IdInputDTO input)
        {
            return await _hR_WorkOvertimeBus.GetOverTimeGroup(input.id);
        }

        #endregion

        #region 提交
        /// <summary>
        /// 单人加班单
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult SaveData(HR_WorkOvertime data)
        {
            try
            {
                _hR_WorkOvertimeBus.SaveData(data);
                return Success();
            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }

        }
        /// <summary>
        /// 多人加班单保存
        /// </summary>
        /// <param name="data"></param>
        [HttpPost]
        public AjaxResult MultSaveData(List<HR_WorkOvertime> data)
        {
            try
            {
                _hR_WorkOvertimeBus.MultSaveData(data);
                return Success();
            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }
        }
        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_WorkOvertimeBus.DeleteDataAsync(ids);
        }

        #endregion

        #region 流程
        /// <summary>
        /// 保存并创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult SaveAndCreateFlow(CreateFlowInfo createFlowInfo)
        {
            try
            {
                var ret = _hR_WorkOvertimeBus.CreateFlow(createFlowInfo.data, _configuration["OAUrl"] + "rest/archives/", createFlowInfo.IsManyPeopleWork);

                if (ret)
                {
                    return Success();
                }
                else
                {
                    return Error("创建流程失败");
                }

            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 流程编辑
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult Edit(HR_WorkOvertime data)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(data.F_Id))
                {
                    using (TransactionScope scope = new TransactionScope())
                    {
                        _hR_WorkOvertimeBus.UpdateData(data);
                        scope.Complete();
                    }
                }
                return Success();
            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult CreateFlow(List<HR_WorkOvertime> data)
        {
            var ret = _hR_WorkOvertimeBus.CreateFlow(data, _configuration["OAUrl"] + "rest/archives/", 0);
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("创建流程失败");
            }
        }

        /// <summary>
        /// 流程回调方法
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult FlowCallBack(FlowInputDTO input)
        {
            _hR_WorkOvertimeBus.FlowCallBack(input);

            return Success();
        }

        /// <summary>
        ///提交退回流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ActWorkflow(CreateFlowInfo createFlowInfo)
        {
            try
            {
                var ret = _hR_WorkOvertimeBus.ActWorkflow(createFlowInfo.data, createFlowInfo.IsManyPeopleWork, _configuration["OAUrl"] + "rest/archives/", createFlowInfo.id);

                if (ret)
                {
                    return Success();
                }
                else
                {
                    return Error("创建流程失败");
                }

            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ArchiveWorkflow(CreateFlowInfo createFlowInfo)
        {
            var ret = _hR_WorkOvertimeBus.ArchiveWorkflow(createFlowInfo.data, _configuration["OAUrl"] + "rest/archives/", createFlowInfo.id);
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("强制归档失败");
            }
        }
        #endregion

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_WorkOvertimeBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "加班单.xlsx";
                excelconfig.Title = "加班单";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "employeescode", ExcelColumn = "员工编码", Alignment = "left", Sort = 1 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_username", ExcelColumn = "员工姓名", Alignment = "left", Sort = 2 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_positiveorg", ExcelColumn = "组织", Alignment = "left", Sort = 3 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_positiveposition", ExcelColumn = "职位", Alignment = "left", Sort = 4 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_workodate", ExcelColumn = "加班日期", Alignment = "left", Sort = 5 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_workostarttimetext", ExcelColumn = "加班开始时间", Alignment = "left", Sort = 6 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_workoendtimetext", ExcelColumn = "加班结束时间", Alignment = "left", Sort = 7 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_resttime", ExcelColumn = "休息时长（分钟）", Alignment = "left", Sort = 8 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_workotime", ExcelColumn = "加班小时", Alignment = "left", Sort = 9 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_workotype", ExcelColumn = "加班类型", Alignment = "left", Sort = 10 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_workoreason", ExcelColumn = "加班原因", Alignment = "left", Sort = 11 });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据
        /// <summary>
        /// 模板下载
        /// </summary>
        [HttpPost]
        public FileContentResult DownloadTemplate()
        {
            string filePath = Directory.GetCurrentDirectory() + "/BusinessTemplate/多人加班单模板.xls";
            if (FileHelper.Exists(filePath))
            {
                var bys = System.IO.File.ReadAllBytes(filePath);//excel表转换成流
                return File(bys, "application/vnd.ms-excel");
            }
            else
            {
                throw new System.Exception("找不到模板");
            }

        }
        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["F_UserName"] = "姓名";
            ht["F_WorkODate"] = "加班日期";
            ht["F_WorkOType"] = "加班类型";
            ht["F_WorkOStartTime"] = "加班开始时间";
            ht["F_WorkOEndTime"] = "加班结束时间";
            ht["F_RestTime"] = "休息时长(分钟)";
            ht["F_WorkOTime"] = "加班小时数";
            ht["F_CompensationMode"] = "补偿方式";
            ht["F_WorkOReason"] = "加班原因";

            var list = new ExcelHelper<HR_WorkOvertime>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                list = _hR_WorkOvertimeBus.UploadFileByForm(list);
            }
            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                data = list,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
        #region 类
        public class CreateFlowInfo
        {
            /// <summary>
            /// 加班人数据
            /// </summary>
            public List<HR_WorkOvertime> data { get; set; }
            /// <summary>
            /// 是否多人加班
            /// </summary>
            public int IsManyPeopleWork { get; set; }
            /// <summary>
            /// 项目名称
            /// </summary>
            public string ProjectName { get; set; }
            /// <summary>
            /// id
            /// </summary>
            public string id { get; set; }
        }

        #endregion
    }
}