{"Version": 1, "Hash": "SoeGbklYMlzqTaC9oS01QYM0rv5Oq3dFfC1Vhm3TBoo=", "Source": "Coldairarrow.Api", "BasePath": "_content/Coldairarrow.Api", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Coldairarrow.Api\\wwwroot", "Source": "Coldairarrow.Api", "ContentRoot": "F:\\HMSystem\\SHR\\Coldairarrow.Api\\wwwroot\\", "BasePath": "_content/Coldairarrow.Api", "Pattern": "**"}], "Assets": []}