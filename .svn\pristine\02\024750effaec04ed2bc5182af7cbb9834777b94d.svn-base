﻿<template>
  <a-card :bordered="false" :hoverable="true">
    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="hanldleAdd()">新建</a-button>
      <a-button type="primary" icon="minus" @click="handleDelete(selectedRowKeys)" :disabled="!hasSelected()"
        :loading="loading">删除</a-button>
      <a-button type="primary" icon="redo" @click="getDataList()">刷新</a-button>
    </div>

    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="10">
          <a-col :md="4" :sm="24">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="queryParam.condition">
                <a-select-option key="F_Name">岗位名称</a-select-option>
                <!-- <a-select-option key="F_EnCode">岗位编号</a-select-option>
                <a-select-option key="F_DepartmentName">部门名称</a-select-option> -->
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item>
              <a-input v-model="queryParam.keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-button type="primary" icon="search" @click="
                () => {
                  this.pagination.current = 1
                  this.getDataList()
                }
              ">查询
            </a-button>
            <a-button style="margin-left: 8px" icon="sync" @click="() => (queryParam = {})">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table" :columns="columns" :rowKey="row => row.Id" :dataSource="data" :pagination="false"
      :loading="loading" :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true"
      :defaultExpandAllRows="true" :scroll="{ x: 1100, y: 450 }">
      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record.Id)">编辑</a>
          <a-divider type="vertical" />
          <a @click="UserDetailsFun(record.Id)">设置查看应聘者详情</a>
          <a-divider type="vertical" />
          <a @click="spaceFun(record.Id)">岗位说明</a>
          <a-divider type="vertical" />
          <a @click="handleDelete([record.Id])">删除</a>
        </template>
      </span>

      <span slot="F_IsDirec" slot-scope="text, record">
        <template v-if="record.F_IsDirec == 1">
          是
        </template>
        <template v-else>
          否
        </template>
      </span>
    </a-table>
    <a-pagination show-size-changer :default-current="pagination.current" :defaultPageSize="pagination.pageSize"
      :showTotal="pagination.showTotal" :total="pagination.total" @showSizeChange="onShowSizeChange"
      @change="onChangeCurrent" style="margin: 5px 0;text-align: center;" />
    <SpaceForm ref="spaceForm" :parentObj="this"></SpaceForm>
    <edit-form ref="editForm" :parentObj="this"></edit-form>
    <UserDetailsForm ref="userDetailsForm" :parentObj="this"></UserDetailsForm>
  </a-card>
</template>

<script>
import SpaceForm from './SpaceForm'
import EditForm from './EditForm'
import UserDetailsForm from './UserDetailsForm'
const columns = [
  { title: '岗位名称', dataIndex: 'F_Name', width: '20%' },
  { title: '岗位编号', dataIndex: 'F_EnCode', width: '15%' },
  { title: '部门名称', dataIndex: 'F_DepartmentName', width: '25%' },
  { title: '是否总经理直接下属', dataIndex: 'F_IsDirec', width: '25%', scopedSlots: { customRender: 'F_IsDirec' } },
  // { title: '备注', dataIndex: 'F_Description', width: '30%' },
  { title: '操作', dataIndex: 'action', scopedSlots: { customRender: 'action' }, width: '15%' }
]

export default {
  components: {
    EditForm,
    SpaceForm,
    UserDetailsForm
  },
  mounted () {
    this.getDataList()
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 15,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      filters: {},
      sorter: { field: 'F_EnCode', order: 'asc' },
      loading: false,
      columns,
      queryParam: {},
      selectedRowKeys: []
    }
  },
  methods: {
    onChangeCurrent (current, pageSize) {
      this.pagination.current = current
      this.getDataList()
    },
    onShowSizeChange (current, pageSize) {
      this.pagination.current = current
      this.pagination.pageSize = pageSize
      this.getDataList()
    },
    handleTableChange (pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.filters = { ...filters }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    getDataList () {
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/Base_Manage/Base_Post/GetTreeDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'F_EnCode',
          SortType: this.sorter.order,
          Name: this.queryParam.keyword,
          ...this.filters
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange (selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    hasSelected () {
      return this.selectedRowKeys.length > 0
    },
    hanldleAdd () {
      this.$refs.editForm.openForm()
    },
    handleEdit (id) {
      this.$refs.editForm.openForm(id)
    },
    UserDetailsFun (id) {
      this.$refs.userDetailsForm.openForm(id)
    },
    spaceFun (id) {
      this.$refs.spaceForm.openForm(id)
    },
    handleDelete (ids) {
      var thisObj = this
      this.$confirm({
        title: '确认删除吗?',
        onOk () {
          return new Promise((resolve, reject) => {
            thisObj.$http.post('/Base_Manage/Base_Post/DeleteData', ids).then(resJson => {
              resolve()

              if (resJson.Success) {
                thisObj.$message.success('操作成功!')

                thisObj.getDataList()
              } else {
                thisObj.$message.error(resJson.Msg)
              }
            })
          })
        }
      })
    }
  }
}
</script>
