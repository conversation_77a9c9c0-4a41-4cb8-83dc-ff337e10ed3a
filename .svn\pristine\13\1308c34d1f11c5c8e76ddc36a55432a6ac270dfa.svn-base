﻿using Coldairarrow.Entity.HR_RegistrManage;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.HR_RegistrManage
{
    public interface IHR_RegistrWorkExpeBusiness
    {
        Task<PageResult<HR_RegistrWorkExpe>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<HR_RegistrWorkExpe> GetTheDataAsync(string id);
        Task AddDataAsync(HR_RegistrWorkExpe data);
        Task UpdateDataAsync(HR_RegistrWorkExpe data);
        Task AddDataListAsync(List<HR_RegistrWorkExpe> data);
        Task UpdateDataListAsync(List<HR_RegistrWorkExpe> data);
        Task DeleteDataAsync(List<string> ids);
        /// <summary>
        /// 通过员工信息删除相关数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task DeleteDataAsync(string id);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
        int AddData(HR_RegistrWorkExpe data);
        void AddListData(List<HR_RegistrWorkExpe> data);
        int UpdatListeData(List<HR_RegistrWorkExpe> data);
        int UpdateData(HR_RegistrWorkExpe data);
        int DeleteData(HR_RegistrWorkExpe data);
        int DeleteDataListeData(List<HR_RegistrWorkExpe> data);
    }
}