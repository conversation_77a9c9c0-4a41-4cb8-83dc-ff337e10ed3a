﻿using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using System.Transactions;
using Coldairarrow.Entity;
using Coldairarrow.Api.Controllers.HR_RegistrManage;

namespace Coldairarrow.Api.Controllers.HR_Manage
{
    [Route("/HR_Manage/[controller]/[action]")]
    public class HR_InterviewRecordController : BaseApiController
    {
        #region DI

        public HR_InterviewRecordController(IHR_InterviewRecordBusiness hR_InterviewRecordBus,
                                            IHR_CertificateInfoBusiness hR_CertificateInfoBus,
                                            IHR_EntryBusiness hR_EntryBus,
                                            IHR_RecruitEduBackBusiness hR_RecruitEduBackBus,
                                            IHR_RecruitWorkExpeBusiness hR_RecruitWorkExpeBus,
                                            IHR_RecruitCertificateBusiness hR_RecruitCertificateBus
                                           )
        {
            _hR_InterviewRecordBus = hR_InterviewRecordBus;
            _hR_EntryBus = hR_EntryBus;
            _hR_CertificateInfoBus = hR_CertificateInfoBus;
            _hR_RecruitEduBackBus = hR_RecruitEduBackBus;
            _hR_RecruitWorkExpeBus = hR_RecruitWorkExpeBus;
            _hR_RecruitCertificateBus = hR_RecruitCertificateBus;
        }
        IHR_CertificateInfoBusiness _hR_CertificateInfoBus { get; }
        IHR_InterviewRecordBusiness _hR_InterviewRecordBus { get; }
        IHR_EntryBusiness _hR_EntryBus { get; }
        IHR_RecruitEduBackBusiness _hR_RecruitEduBackBus { get; }
        IHR_RecruitWorkExpeBusiness _hR_RecruitWorkExpeBus { get; }
        IHR_RecruitCertificateBusiness _hR_RecruitCertificateBus { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_InterviewRecordDTO>> GetInterviewRecordList(PageInput<ConditionDTO> input)
        {
            return await _hR_InterviewRecordBus.GetInterviewRecordListAsync(input);
        }
        [HttpPost]
        public async Task<PageResult<HR_InterviewRecord>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_InterviewRecordBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_InterviewRecord> GetTheData(IdInputDTO input)
        {
            return await _hR_InterviewRecordBus.GetTheDataAsync(input.id);
        }
        [HttpPost]
        public async Task<HR_InterviewRecordFromDTO> GetFormTheData(IdInputDTO input)
        {
            return await _hR_InterviewRecordBus.GetFormTheDataAsnc(input.id);
        }
        [HttpPost]
        public async Task<HR_InterviewRecordFromDTO> GetKeyTheData(InterviewDTO input)
        {
            return await _hR_InterviewRecordBus.GetKeyTheDataAsync(input);
        }
        [HttpPost]
        public async Task<HR_InterviewRecordFromDTO> GetNameTheData(NameCard nameCard)
        {
            return await _hR_InterviewRecordBus.GetKeyTheDataAsync(nameCard.userName, nameCard.IdCard);
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_InterviewRecord data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_InterviewRecordBus.AddDataAsync(data);
            }
            else
            {
                await _hR_InterviewRecordBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_InterviewRecordBus.DeleteDataAsync(ids);
        }
        /// <summary>
        /// 在线招聘保存
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>

        [HttpPost]
        public AjaxResult SaveFormData(HR_InterviewRecordFromDTO data)
        {
            try
            {
                var op = GetOperator();
                _hR_InterviewRecordBus.SaveFormData(data, op);
                return Success();
            }
            catch (Exception ex)
            {
                return Error("保存失败");
                throw new Exception(ex.ToString());
            }
        }
        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_InterviewRecordBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<HR_InterviewRecord>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _hR_InterviewRecordBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}