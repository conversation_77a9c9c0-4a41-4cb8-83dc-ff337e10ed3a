﻿using Coldairarrow.Business.HolidayManage;
using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.IO;
using System.Collections;
using Coldairarrow.IBusiness;
using Microsoft.Extensions.Configuration;
using Coldairarrow.Entity;
using Coldairarrow.Business.HR_DataDictionaryManage;
using Coldairarrow.Business.HR_AttendanceManage;
using System.Text.RegularExpressions;
using Newtonsoft.Json;

namespace Coldairarrow.Api.Controllers.HolidayManage
{
    [Route("/HolidayManage/[controller]/[action]")]
    public class HR_AskLeaveController : BaseApiController
    {
        #region DI

        public HR_AskLeaveController(IHR_AskLeaveBusiness hR_AskLeaveBus,
            IConfiguration configuration,
            IHR_DataDictionaryDetailsBusiness hR_DataDictionaryDetailsBus,
            IHR_SchedulingBusiness hR_SchedulingBus)
        {
            _hR_SchedulingBus = hR_SchedulingBus;
            _configuration = configuration;
            _hR_AskLeaveBus = hR_AskLeaveBus;
            _hR_DataDictionaryDetailsBus = hR_DataDictionaryDetailsBus;
        }

        readonly IConfiguration _configuration;
        IHR_AskLeaveBusiness _hR_AskLeaveBus { get; }
        IHR_SchedulingBusiness _hR_SchedulingBus { get; }
        IHR_DataDictionaryDetailsBusiness _hR_DataDictionaryDetailsBus { get; }

        #endregion

        #region 获取

        /// <summary>
        /// 获取年假剩余信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult GetAnnualLeaveSYInfo(PreReInputDTO input)
        {
            var info = _hR_AskLeaveBus.GetAnnualLeaveSYInfo(input.id, input.time);
            return Success(info);
        }
        /// <summary>
        /// 获取编码
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult GetCode()
        {
            string code = DateTime.Now.ToString("yyyyMMddffff");
            var op = GetOperator();
            var remainingDays = _hR_AskLeaveBus.GetTimeAsync(op?.UserId);
            var data = new
            {
                code,
                remainingDays
            };
            return Success(data);
        }

        [HttpPost]
        public AjaxResult GetInit()
        {
            var askLeaveTypes = _hR_DataDictionaryDetailsBus.GetDetailList("请假类型", "");
            var data = new
            {
                wfStates = EnumHelper.ToOptionList(typeof(WFStates)),
                askLeaveTypes
            };
            return Success(data);
        }

        /// <summary>
        /// 获得假期台账报表数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public PageResult<HolidayLedgerDTO> GetHolidayLedger(PageInput<ReportConditionDTO> input)
        {
            //var op = GetOperator();
            return _hR_AskLeaveBus.GetHolidayLedger(input);
        }

        /// <summary>
        /// 面向员工查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PageResult<HR_AskLeaveDTO>> GetEmpDataList(PageInput<ConditionDTO> input)
        {
            var op = GetOperator();
            return await _hR_AskLeaveBus.GetEmpDataListAsync(input, op?.UserId);
        }
        [HttpPost]
        public async Task<PageResult<HR_AskLeaveDTO>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_AskLeaveBus.GetDataListAsync(input);
        }
        [HttpPost]
        public async Task<PageResult<HR_AskLeaveDTO>> GetDataListByUser(PageInput<ConditionDTO> input)
        {
            input.Search.UserId = GetOperator().UserId;
            return await _hR_AskLeaveBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_AskLeave> GetTheData(IdInputDTO input)
        {
            var op = GetOperator();
            var model = await _hR_AskLeaveBus.GetTheDataAsync(input.id, op?.UserId);

            return model;
        }

        [HttpPost]
        public AjaxResult GetOAToken()
        {
            var op = GetOperator();
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            paramters.Add("appid", "ssss");
            paramters.Add("loginid", op.ADName);
            string str = HttpHelper.PostData(_configuration["OAUrl"] + "ssologin/getToken", paramters, null, ContentType.Form);
            return Success(str);
        }
        /// <summary>
        /// 获取天数信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult GetDayInfo(PeriodTime periodTime)
        {
            decimal dayNum = 0;
            decimal dayNNum = 0;
            decimal dayANum = 0;
            if (!(periodTime.StartTime.HasValue && periodTime.EndTime.HasValue))
            {
                return Success("");
            }
            var queryYear=periodTime.StartTime.Value.Year;
            var toDayYear = DateTime.Now.Year;
            //periodTime.StartTime = queryYear < toDayYear ? Convert.ToDateTime(queryYear.ToString() + "-01-01") : periodTime.StartTime;
            //periodTime.EndTime = queryYear < toDayYear ? Convert.ToDateTime(queryYear.ToString() + "-12-31") : periodTime.EndTime;
            //判断是否自然天计算节假日
            string holidayStr = "婚假,陪产假,产假";
            var holidayList = holidayStr.Split(new Char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            //var hStartTime = queryYear < toDayYear ? Convert.ToDateTime(queryYear.ToString() + "-01-01") : periodTime.StartTime;
            //var hEndTime = queryYear < toDayYear ? Convert.ToDateTime(queryYear.ToString() + "-12-31") : periodTime.EndTime;
            var holList = _hR_AskLeaveBus.GetCalender(periodTime.StartTime.Value.Date, (periodTime.EndTime.Value.ToString("yyyy-MM-dd") + " 23:59").ToDateTime());
            bool isWork = false;
            bool isWorking = false;

            if (holidayList.Count(i => i == periodTime.HolidayType) > 0)
            {

                if (periodTime.Noon == 0)
                {
                    dayNNum = 0;
                }
                else
                {
                    dayNNum = Convert.ToDecimal(-0.5);
                }
                if (periodTime.Afternoon == 0)
                {
                    dayANum = Convert.ToDecimal(-0.5);
                }
                else
                {
                    dayANum = 0;
                }
            }
            else
            {
                if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date && i.F_BusState == 2) > 0)
                {
                    isWork = true;
                }
                if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date && i.F_BusState == 1) > 0)
                {
                    isWorking = true;
                }
                if (((periodTime.StartTime.Value.DayOfWeek == DayOfWeek.Saturday || periodTime.StartTime.Value.DayOfWeek == DayOfWeek.Sunday) && !isWork) || isWorking)
                {
                    periodTime.Noon = 0;
                }
                else
                {
                    if (periodTime.Noon == 0)
                    {
                        dayNNum = 0;
                    }
                    else
                    {
                        dayNNum = Convert.ToDecimal(-0.5);
                    }
                }
                isWork = false;
                if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.EndTime.Value.Date && i.F_BusState == 2) > 0)
                {
                    isWork = true;
                }
                isWorking = false;
                if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.EndTime.Value.Date && i.F_BusState == 1) > 0)
                {
                    isWorking = true;
                }
                if (((periodTime.EndTime.Value.DayOfWeek == DayOfWeek.Saturday || periodTime.EndTime.Value.DayOfWeek == DayOfWeek.Sunday) && !isWork) || isWorking)
                {
                    periodTime.Afternoon = 1;
                }
                else
                {
                    if (periodTime.Afternoon == 0)
                    {
                        dayANum = Convert.ToDecimal(-0.5);
                    }
                    else
                    {
                        dayANum = 0;
                    }
                }


            }


            string baseUserId = "";
            if (string.IsNullOrEmpty(periodTime.UserId))
            {
                var op = GetOperator();
                baseUserId = op.UserId;

            }
            var SchedulingInfoList = _hR_SchedulingBus.GetWorkInfo(periodTime.UserId, periodTime.StartTime.Value.Date, (periodTime.EndTime.Value.ToString("yyyy-MM-dd") + " 23:59").ToDateTime(), baseUserId);
            var workTimeMax = SchedulingInfoList.Max(s => s.F_SchedulingTime);


            if (SchedulingInfoList.Count > 0)
            {
                //判断是否按照周末休息日时间去判断
                if (holidayList.Count(i => i == periodTime.HolidayType) > 0)
                {
                    while (true)
                    {
                        if (SchedulingInfoList.Count(i => i.F_SchedulingTime.Value.Date == periodTime.StartTime.Value.Date) == 0)
                        {
                            //节假日判断
                            if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date) == 0)
                            {
                                dayNum++;
                            }
                        }
                        else
                        {
                            var model = SchedulingInfoList.Where(i => (i.F_WorkState.Contains("休") || i.F_WorkState.Contains("假")) && i.F_SchedulingTime.Value.Date == periodTime.StartTime.Value.Date);
                            if (model == null)
                            {
                                dayNum++;
                            }
                        }
                        if (periodTime.StartTime.Value.ToShortDateString() == periodTime.EndTime.Value.ToShortDateString())
                        {
                            break;
                        }
                        periodTime.StartTime = periodTime.StartTime.Value.AddDays(1);
                    }
                    dayNum = dayNum + dayANum + dayNNum;
                    return Success(dayNum);
                }

                while (true)
                {
                    if (SchedulingInfoList.Count(i => i.F_SchedulingTime.Value.ToString("yyyy-MM-dd") == periodTime.StartTime.Value.ToString("yyyy-MM-dd")) == 0)
                    {
                        var day = periodTime.StartTime.Value.DayOfWeek;
                        if (day != DayOfWeek.Sunday && day != DayOfWeek.Saturday)
                        {
                            //节假日判断
                            if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date && i.F_BusState != 2) == 0)
                            {
                                dayNum++;
                            }
                        }
                        else
                        {
                            if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date && i.F_BusState == 2) > 0)
                            {
                                dayNum++;
                            }
                        }
                    }
                    else
                    {
                        var model = SchedulingInfoList.Where(i => i.F_WorkState == "年假" && i.F_SchedulingTime.Value.ToShortDateString() == periodTime.StartTime.Value.Date.ToShortDateString());
                        if (model.Count() > 0)
                        {
                            dayNum++;
                        }
                        else
                        {
                            model = SchedulingInfoList.Where(i => !(i.F_WorkState.Contains("休") || i.F_WorkState.Contains("假")) && i.F_SchedulingTime.Value.ToShortDateString() == periodTime.StartTime.Value.Date.ToShortDateString());
                            if (model.Count() > 0)
                            {
                                dayNum++;
                            }
                        }

                    }
                    if (periodTime.StartTime.Value.ToShortDateString() == periodTime.EndTime.Value.ToShortDateString())
                    {
                        break;
                    }
                    periodTime.StartTime = periodTime.StartTime.Value.AddDays(1);
                }
                dayNum = dayNum + dayANum + dayNNum;
                return Success(dayNum);
            }
            else
            {
                if (holidayList.Count(i => i == periodTime.HolidayType) > 0)
                {
                    //while (true)
                    //{
                    //    //节假日判断
                    //    if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date) == 0)
                    //    {
                    //        dayNum++;
                    //    }
                    //    if (periodTime.StartTime.Value.ToShortDateString() == periodTime.EndTime.Value.ToShortDateString())
                    //    {
                    //        break;
                    //    }
                    //    periodTime.StartTime = periodTime.StartTime.Value.AddDays(1);
                    //}
                    //dayNum = dayNum + dayANum + dayNNum;
                    DateTime start = Convert.ToDateTime(periodTime.StartTime.Value.ToShortDateString());

                    DateTime end = Convert.ToDateTime(periodTime.EndTime.Value.ToShortDateString());

                    TimeSpan sp = end.Subtract(start);
                    return Success(sp.Days + 1);
                }

                while (true)
                {
                    var day = periodTime.StartTime.Value.DayOfWeek;
                    if (day != DayOfWeek.Sunday && day != DayOfWeek.Saturday)
                    {
                        //节假日判断
                        if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date && i.F_BusState != 2) == 0)
                        {
                            dayNum++;
                        }
                    }
                    else
                    {
                        if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date && i.F_BusState == 2) > 0)
                        {
                            dayNum++;
                        }
                    }
                    if (periodTime.StartTime.Value.ToShortDateString() == periodTime.EndTime.Value.ToShortDateString())
                    {
                        break;
                    }
                    periodTime.StartTime = periodTime.StartTime.Value.AddDays(1);
                }
                dayNum = dayNum + dayANum + dayNNum;
                return Success(dayNum);
            }

        }

        /// <summary>
        /// 获取天数信息小程序用
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult GetDayInfoMini()
        {
            PeriodTime periodTime = new PeriodTime() { 
               StartTime = HttpContext.Request.Form["StartTime"].ToString().ToDateTime(),
               EndTime = HttpContext.Request.Form["EndTime"].ToString().ToDateTime(),
               Noon = Convert.ToInt32(HttpContext.Request.Form["Noon"].ToString()),
               Afternoon = Convert.ToInt32(HttpContext.Request.Form["Afternoon"].ToString())
            };
            decimal dayNum = 0;
            decimal dayNNum = 0;
            decimal dayANum = 0;
            if (!(periodTime.StartTime.HasValue && periodTime.EndTime.HasValue))
            {
                return Success("");
            }
            //判断是否自然天计算节假日
            string holidayStr = "婚假,陪产假,产假";
            var holidayList = holidayStr.Split(new Char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            var holList = _hR_AskLeaveBus.GetCalender(periodTime.StartTime.Value.Date, (periodTime.EndTime.Value.ToString("yyyy-MM-dd") + " 23:59").ToDateTime());
            bool isWork = false;
            bool isWorking = false;

            if (holidayList.Count(i => i == periodTime.HolidayType) > 0)
            {

                if (periodTime.Noon == 0)
                {
                    dayNNum = 0;
                }
                else
                {
                    dayNNum = Convert.ToDecimal(-0.5);
                }
                if (periodTime.Afternoon == 0)
                {
                    dayANum = Convert.ToDecimal(-0.5);
                }
                else
                {
                    dayANum = 0;
                }
            }
            else
            {
                if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date && i.F_BusState == 2) > 0)
                {
                    isWork = true;
                }
                if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date && i.F_BusState == 1) > 0)
                {
                    isWorking = true;
                }
                if (((periodTime.StartTime.Value.DayOfWeek == DayOfWeek.Saturday || periodTime.StartTime.Value.DayOfWeek == DayOfWeek.Sunday) && !isWork) || isWorking)
                {
                    periodTime.Noon = 0;
                }
                else
                {
                    if (periodTime.Noon == 0)
                    {
                        dayNNum = 0;
                    }
                    else
                    {
                        dayNNum = Convert.ToDecimal(-0.5);
                    }
                }
                isWork = false;
                if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.EndTime.Value.Date && i.F_BusState == 2) > 0)
                {
                    isWork = true;
                }
                isWorking = false;
                if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.EndTime.Value.Date && i.F_BusState == 1) > 0)
                {
                    isWorking = true;
                }
                if (((periodTime.EndTime.Value.DayOfWeek == DayOfWeek.Saturday || periodTime.EndTime.Value.DayOfWeek == DayOfWeek.Sunday) && !isWork) || isWorking)
                {
                    periodTime.Afternoon = 1;
                }
                else
                {
                    if (periodTime.Afternoon == 0)
                    {
                        dayANum = Convert.ToDecimal(-0.5);
                    }
                    else
                    {
                        dayANum = 0;
                    }
                }


            }


            string baseUserId = "";
            if (string.IsNullOrEmpty(periodTime.UserId))
            {
                var op = GetOperator();
                baseUserId = op.UserId;

            }
            var SchedulingInfoList = _hR_SchedulingBus.GetWorkInfo(periodTime.UserId, periodTime.StartTime.Value.Date, (periodTime.EndTime.Value.ToString("yyyy-MM-dd") + " 23:59").ToDateTime(), baseUserId);
            var workTimeMax = SchedulingInfoList.Max(s => s.F_SchedulingTime);


            if (SchedulingInfoList.Count > 0)
            {
                //判断是否按照周末休息日时间去判断
                if (holidayList.Count(i => i == periodTime.HolidayType) > 0)
                {
                    //while (true)
                    //{
                    //    if (SchedulingInfoList.Count(i => i.F_SchedulingTime.Value.Date == periodTime.StartTime.Value.Date) == 0)
                    //    {
                    //        //节假日判断
                    //        if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date) == 0)
                    //        {
                    //            dayNum++;
                    //        }
                    //    }
                    //    else
                    //    {
                    //        var model = SchedulingInfoList.Where(i => (i.F_WorkState.Contains("休") || i.F_WorkState.Contains("假")) && i.F_SchedulingTime.Value.Date == periodTime.StartTime.Value.Date);
                    //        if (model == null)
                    //        {
                    //            dayNum++;
                    //        }
                    //    }
                    //    if (periodTime.StartTime.Value.ToShortDateString() == periodTime.EndTime.Value.ToShortDateString())
                    //    {
                    //        break;
                    //    }
                    //    periodTime.StartTime = periodTime.StartTime.Value.AddDays(1);
                    //}
                    //dayNum = dayNum + dayANum + dayNNum;
                    DateTime start = Convert.ToDateTime(periodTime.StartTime.Value.ToShortDateString());

                    DateTime end = Convert.ToDateTime(periodTime.EndTime.Value.ToShortDateString());

                    TimeSpan sp = end.Subtract(start);
                    return Success(sp.Days+1);
                    //return Success(dayNum);
                }

                while (true)
                {
                    if (SchedulingInfoList.Count(i => i.F_SchedulingTime.Value.ToString("yyyy-MM-dd") == periodTime.StartTime.Value.ToString("yyyy-MM-dd")) == 0)
                    {
                        var day = periodTime.StartTime.Value.DayOfWeek;
                        if (day != DayOfWeek.Sunday && day != DayOfWeek.Saturday)
                        {
                            //节假日判断
                            if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date && i.F_BusState != 2) == 0)
                            {
                                dayNum++;
                            }
                        }
                        else
                        {
                            if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date && i.F_BusState == 2) > 0)
                            {
                                dayNum++;
                            }
                        }
                    }
                    else
                    {
                        var model = SchedulingInfoList.Where(i => i.F_WorkState == "年假" && i.F_SchedulingTime.Value.ToShortDateString() == periodTime.StartTime.Value.Date.ToShortDateString());
                        if (model.Count() > 0)
                        {
                            dayNum++;
                        }
                        else
                        {
                            model = SchedulingInfoList.Where(i => !(i.F_WorkState.Contains("休") || i.F_WorkState.Contains("假")) && i.F_SchedulingTime.Value.ToShortDateString() == periodTime.StartTime.Value.Date.ToShortDateString());
                            if (model.Count() > 0)
                            {
                                dayNum++;
                            }
                        }

                    }
                    if (periodTime.StartTime.Value.ToShortDateString() == periodTime.EndTime.Value.ToShortDateString())
                    {
                        break;
                    }
                    periodTime.StartTime = periodTime.StartTime.Value.AddDays(1);
                }
                dayNum = dayNum + dayANum + dayNNum;
                return Success(dayNum);
            }
            else
            {
                if (holidayList.Count(i => i == periodTime.HolidayType) > 0)
                {
                    while (true)
                    {
                        //节假日判断
                        if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date) == 0)
                        {
                            dayNum++;
                        }
                        if (periodTime.StartTime.Value.ToShortDateString() == periodTime.EndTime.Value.ToShortDateString())
                        {
                            break;
                        }
                        periodTime.StartTime = periodTime.StartTime.Value.AddDays(1);
                    }
                    dayNum = dayNum + dayANum + dayNNum;
                    return Success(dayNum);
                }

                while (true)
                {
                    var day = periodTime.StartTime.Value.DayOfWeek;
                    if (day != DayOfWeek.Sunday && day != DayOfWeek.Saturday)
                    {
                        //节假日判断
                        if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date && i.F_BusState != 2) == 0)
                        {
                            dayNum++;
                        }
                    }
                    else
                    {
                        if (holList.Count(i => i.F_StartTime.Value.Date == periodTime.StartTime.Value.Date && i.F_BusState == 2) > 0)
                        {
                            dayNum++;
                        }
                    }
                    if (periodTime.StartTime.Value.ToShortDateString() == periodTime.EndTime.Value.ToShortDateString())
                    {
                        break;
                    }
                    periodTime.StartTime = periodTime.StartTime.Value.AddDays(1);
                }
                dayNum = dayNum + dayANum + dayNNum;
                return Success(dayNum);
            }

        }

        #endregion

        #region 提交

        /// <summary>
        /// 流程回调方法
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult FlowCallBack(FlowInputDTO input)
        {
            _hR_AskLeaveBus.FlowCallBack(input);

            return Success();
        }
        /// <summary>
        /// 保存并创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult SaveAndCreateFlow(HR_AskLeave data)
        {
            var ret = _hR_AskLeaveBus.CreateFlow(data, _configuration["OAUrl"] + "rest/archives/", GetOperator());
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("创建流程失败");
            }
        }
        /// <summary>
        /// 保存并创建流程小程序用
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult SaveAndCreateFlowMini()
        {
            var askleave =HttpContext.Request.Form["data"].ToString();
            var data = JsonConvert.DeserializeObject<HR_AskLeave>(askleave);
            var ret = _hR_AskLeaveBus.CreateFlow(data, _configuration["OAUrl"] + "rest/archives/", null);
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("创建流程失败");
            }
        }

        /// <summary>
        /// 提交，退回流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ActWorkflow(HR_AskLeave data)
        {
            var ret = _hR_AskLeaveBus.ActWorkflow(data, _configuration["OAUrl"] + "rest/archives/", GetOperator());
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("提交流程失败");
            }
        }
        /// <summary>
        /// 创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult CreateFlow(HR_AskLeave data)
        {
            var ret = _hR_AskLeaveBus.CreateFlow(data, _configuration["OAUrl"] + "rest/archives/", GetOperator());
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("创建流程失败");
            }
        }
        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ArchiveWorkflow(HR_AskLeave data)
        {
            var ret = _hR_AskLeaveBus.ArchiveWorkflow(data, _configuration["OAUrl"] + "rest/archives/");
            if (ret)
            {
                return Success();
            }
            else
            {
                return Error("强制归档失败");
            }
        }
        [HttpPost]
        public async Task SaveData(HR_AskLeave data)
        {
            if (data.F_PeriodTime == 0)
            {
                data.F_StartTime = (data.F_StartTime.Value.ToString("yyyy-MM-dd") + " 09:00").ToDateTime();
            }
            else
            {
                data.F_StartTime = (data.F_StartTime.Value.ToString("yyyy-MM-dd") + " 13:00").ToDateTime();
            }
            if (data.F_PeriodTime2 == 0)
            {
                data.F_EndTime = (data.F_EndTime.Value.ToString("yyyy-MM-dd") + " 12:30").ToDateTime();
            }
            else
            {
                data.F_EndTime = (data.F_EndTime.Value.ToString("yyyy-MM-dd") + " 18:00").ToDateTime();
            }
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);
                data.F_BusState = (int)ASKBusState.正常;

                await _hR_AskLeaveBus.AddDataAsync(data);
            }
            else
            {
                UpdateEntity(data);
                await _hR_AskLeaveBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        [NoCheckJWT]
        public AjaxResult Edit(HR_AskLeave data)
        {
            if (data.F_PeriodTime == 0)
            {
                data.F_StartTime = (data.F_StartTime.Value.ToString("yyyy-MM-dd") + " 09:00").ToDateTime();
            }
            else
            {
                data.F_StartTime = (data.F_StartTime.Value.ToString("yyyy-MM-dd") + " 13:00").ToDateTime();
            }
            if (data.F_PeriodTime2 == 0)
            {
                data.F_EndTime = (data.F_EndTime.Value.ToString("yyyy-MM-dd") + " 12:30").ToDateTime();
            }
            else
            {
                data.F_EndTime = (data.F_EndTime.Value.ToString("yyyy-MM-dd") + " 18:00").ToDateTime();
            }
            if (data.F_Id.IsNullOrEmpty())
            {
                return Error("Id不能为空");
            }
            UpdateEntity(data);
            if (_hR_AskLeaveBus.UpdateData(data))
            {
                return Success();
            }
            else
            {
                return Error("保存失败");
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_AskLeaveBus.DeleteDataAsync(ids);
        }

        #endregion

        #region 导出Excel

        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownloadEmpData(PageInput<ConditionDTO> input)
        {
            try
            {
                //取出数据源
                DataTable exportTable = _hR_AskLeaveBus.GetExcelEmpDataList(input, GetOperator().UserId);
                if (exportTable != null && exportTable.Rows.Count > 0)
                {
                    var askLeaveTypes = _hR_DataDictionaryDetailsBus.GetDetailList("请假类型", "");
                    exportTable.Columns.Add("wfstate");
                    exportTable.Columns.Add("askleavetype");
                    exportTable.Columns.Add("starttime");
                    exportTable.Columns.Add("endtime");
                    for (int i = 0; i < exportTable.Rows.Count; i++)
                    {
                        var askleavetype = exportTable.Rows[i]["F_AskLeaveType"];
                        if (askleavetype != null && !askleavetype.ToString().IsNullOrEmpty())
                        {
                            var askTypeStr = askLeaveTypes.FirstOrDefault(x => x.F_ItemValue == askleavetype.ToString())?.F_ItemName;
                            exportTable.Rows[i]["askleavetype"] = askTypeStr;
                        }
                        var wfstate = exportTable.Rows[i]["F_WFState"];
                        if (wfstate != null && !wfstate.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["wfstate"] = Enum.Parse(typeof(WFStates), wfstate.ToString()).ToString();
                        }
                        var starttime = exportTable.Rows[i]["F_StartTime"];
                        if (starttime != null && !starttime.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["starttime"] = Convert.ToDateTime(starttime).ToString("yyyy-MM-dd HH:mm");
                        }
                        var endtime = exportTable.Rows[i]["F_EndTime"];
                        if (endtime != null && !endtime.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["endtime"] = Convert.ToDateTime(endtime).ToString("yyyy-MM-dd HH:mm");
                        }
                    }
                }
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "请假单.xlsx";
                excelconfig.Title = "请假单";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                int sort = 0;
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_askleavecode", ExcelColumn = "请假编号", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_username", ExcelColumn = "员工名称", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createdate", ExcelColumn = "申请日期", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "askleavetype", ExcelColumn = "假期类型", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "starttime", ExcelColumn = "开始时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "endtime", ExcelColumn = "结束时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_askleavetime", ExcelColumn = "请假时长", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_unit", ExcelColumn = "单位", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "wfstate", ExcelColumn = "单据状态", Alignment = "left", Sort = sort++ });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");

                return file;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// Excel导出下载
        /// </summary>
        [HttpPost]
        public FileContentResult ManagerExcelDownload(ConditionDTO input)
        {
            try
            {
                //取出数据源
                DataTable exportTable = _hR_AskLeaveBus.GetExcelManagerList(input);
                if (exportTable != null && exportTable.Rows.Count > 0)
                {
                    var askLeaveTypes = _hR_DataDictionaryDetailsBus.GetDetailList("请假类型", "");
                    exportTable.Columns.Add("wfstate");
                    exportTable.Columns.Add("askleavetype");
                    exportTable.Columns.Add("starttime");
                    exportTable.Columns.Add("endtime");
                    for (int i = 0; i < exportTable.Rows.Count; i++)
                    {
                        var askleavetype = exportTable.Rows[i]["F_AskLeaveType"];
                        if (askleavetype != null && !askleavetype.ToString().IsNullOrEmpty())
                        {
                            var askTypeStr = askLeaveTypes.FirstOrDefault(x => x.F_ItemValue == askleavetype.ToString())?.F_ItemName;
                            exportTable.Rows[i]["askleavetype"] = askTypeStr;
                        }
                        var wfstate = exportTable.Rows[i]["F_WFState"];
                        if (wfstate != null && !wfstate.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["wfstate"] = Enum.Parse(typeof(WFStates), wfstate.ToString()).ToString();
                        }
                        var starttime = exportTable.Rows[i]["F_StartTime"];
                        if (starttime != null && !starttime.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["starttime"] = Convert.ToDateTime(starttime).ToString("yyyy-MM-dd HH:mm");
                        }
                        var endtime = exportTable.Rows[i]["F_EndTime"];
                        if (endtime != null && !endtime.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["endtime"] = Convert.ToDateTime(endtime).ToString("yyyy-MM-dd HH:mm");
                        }
                    }
                }
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "请假单.xlsx";
                excelconfig.Title = $"{input.beginDate.ToString("yyyy-MM-dd")}至{input.endDate.ToString("yyyy-MM-dd")}请假数据";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                int sort = 0;
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "name", ExcelColumn = "部门", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "nameuser", ExcelColumn = "姓名", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "askleavetype", ExcelColumn = "请假类型", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "starttime", ExcelColumn = "开始时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "endtime", ExcelColumn = "结束时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_askleavetime", ExcelColumn = "请假时长", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "wfstate", ExcelColumn = "流程状态", Alignment = "left", Sort = sort++ });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");

                return file;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            {
                //取出数据源
                DataTable exportTable = _hR_AskLeaveBus.GetExcelListAsync(input);
                if (exportTable != null && exportTable.Rows.Count > 0)
                {
                    var askLeaveTypes = _hR_DataDictionaryDetailsBus.GetDetailList("请假类型", "");
                    exportTable.Columns.Add("wfstate");
                    exportTable.Columns.Add("askleavetype");
                    exportTable.Columns.Add("starttime");
                    exportTable.Columns.Add("endtime");
                    for (int i = 0; i < exportTable.Rows.Count; i++)
                    {
                        var askleavetype = exportTable.Rows[i]["F_AskLeaveType"];
                        if (askleavetype != null && !askleavetype.ToString().IsNullOrEmpty())
                        {
                            var askTypeStr = askLeaveTypes.FirstOrDefault(x => x.F_ItemValue == askleavetype.ToString())?.F_ItemName;
                            exportTable.Rows[i]["askleavetype"] = askTypeStr;
                        }
                        var wfstate = exportTable.Rows[i]["F_WFState"];
                        if (wfstate != null && !wfstate.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["wfstate"] = Enum.Parse(typeof(WFStates), wfstate.ToString()).ToString();
                        }
                        var starttime = exportTable.Rows[i]["F_StartTime"];
                        if (starttime != null && !starttime.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["starttime"] = Convert.ToDateTime(starttime).ToString("yyyy-MM-dd HH:mm");
                        }
                        var endtime = exportTable.Rows[i]["F_EndTime"];
                        if (endtime != null && !endtime.ToString().IsNullOrEmpty())
                        {
                            exportTable.Rows[i]["endtime"] = Convert.ToDateTime(endtime).ToString("yyyy-MM-dd HH:mm");
                        }
                    }
                }
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "请假单.xlsx";
                excelconfig.Title = "请假单";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                int sort = 0;
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_askleavecode", ExcelColumn = "请假编号", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_username", ExcelColumn = "员工名称", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createdate", ExcelColumn = "申请日期", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "askleavetype", ExcelColumn = "假期类型", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "starttime", ExcelColumn = "开始时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "endtime", ExcelColumn = "结束时间", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_askleavetime", ExcelColumn = "请假时长", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_unit", ExcelColumn = "单位", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "wfstate", ExcelColumn = "单据状态", Alignment = "left", Sort = sort++ });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");

                return file;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 假期台账导出
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public FileContentResult HolidayLedgerExcelDownload(PageInput<ReportConditionDTO> input)
        {
            try
            {
                if (input == null)
                {
                    throw new BusException("参数错误");
                }
                input.PageRows = 10000;
                //取出数据源
                DataTable exportTable = _hR_AskLeaveBus.GetHolidayLedger(input).Data.ToDataTable();
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "假期台账.xls";
                excelconfig.Title = "假期台账";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                int sort = 0;
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "departmentname", ExcelColumn = "部门", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "name", ExcelColumn = "姓名", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "workyear", ExcelColumn = "工作年限", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "entrydate", ExcelColumn = "入职日期", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "stateannualleave", ExcelColumn = "法定年休假", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "stateannualleavedayed", ExcelColumn = "已休法定年假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "remstateannualleavedayed", ExcelColumn = "剩余法定年假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "comannualleave", ExcelColumn = "福利年休假", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "comannualleavedayed", ExcelColumn = "已休福利年假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "remcomannualleavedayed", ExcelColumn = "剩余福利年假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "remannualleaveday", ExcelColumn = "剩余总年假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "sickleaveday", ExcelColumn = "本年病假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "sickleavedayed", ExcelColumn = "已休病假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "remsickleaveday", ExcelColumn = "本年剩余病假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "maternityleaveday", ExcelColumn = "本年产假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "maternityleavedayed", ExcelColumn = "已休产假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "remmaternityleaveday", ExcelColumn = "本年剩余产假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "funeralleave", ExcelColumn = "本年丧假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "funeralleavedayed", ExcelColumn = "已休丧假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "remfuneralleaveday", ExcelColumn = "本年剩余丧假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "marriageleaveday", ExcelColumn = "本年婚假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "marriageleavedayed", ExcelColumn = "已休婚假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "remmarriageleaveday", ExcelColumn = "本年剩余婚假天数", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "leave", ExcelColumn = "事假", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "paternityleave", ExcelColumn = "陪产假", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "absenteeism", ExcelColumn = "旷工", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "otherholidays", ExcelColumn = "会务培训其他假期", Alignment = "left", Sort = sort++ });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");

                return file;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<HR_AskLeave>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _hR_AskLeaveBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}