﻿<template>
  <a-modal :title="title" width="60%" :visible="visible" :confirmLoading="loading" :maskClosable="false" @cancel="
      () => {
        this.visible = false
      }
    ">
    <template v-if="!isEdit" slot="footer">
      <a-button key="save" @click="save" icon="edit">
        保存草稿
      </a-button>
      <a-button key="createFlow" :loading="loading" @click="createFlow" icon="check-square">
        提交流程
      </a-button>
      <a-button key="cancel" :loading="loading" @click="
          () => {
            this.visible = false
          }
        " icon="close">
        取消
      </a-button>
    </template>
    <template v-else slot="footer">
      <a-button key="cancel" icon="close" :loading="loading" @click="
          () => {
            this.visible = false
          }
        ">
        关闭
      </a-button>
    </template>
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="entity" :rules="rules" v-bind="layout">
        <a-row :gutter="24">
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="源合同" prop="SourceContract" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.SourceContract" disabled placeholder="" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-divider orientation="left">续签合同信息</a-divider>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="续签合同编号" prop="ContractNumber" :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.ContractNumber" :disabled="isEdit" autocomplete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="签订日期" prop="SigningDate" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-date-picker v-model="entity.SigningDate" type="date" :disabled="isEdit" format="YYYY-MM-DD"
                  placeholder="请选择签订日期" style="width: 100%;" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="合同类别" prop="ContractType" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <SelectDiction ref="ContractType" :Name="'合同类别'" @selectedvalue="selectContractType" :disabled="isEdit"
                  :Value="entity.ContractType" style="width: 100%;"></SelectDiction>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="合同模板" prop="ContractModule" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <SelectDiction ref="ContractModule" :Name="'合同模板'" @selectedvalue="selectContractModule"
                  :disabled="isEdit" :Value="entity.ContractModule" style="width: 100%;"></SelectDiction>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="合同期限类型" prop="ContractDurationDate" :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 14 }">
                <SelectDiction ref="ContractDurationDate" :Name="'合同期限类型'" @selectedvalue="selectContractDurationDate"
                  :disabled="isEdit" :Value="entity.ContractDurationDate" style="width: 100%;"></SelectDiction>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="合同期限(年)" prop="ContractPeriod" :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 14 }">
                <a-input-number v-model="entity.ContractPeriod" :min="1" :max="100" autocomplete="off"
                  @change="goYaer" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <!-- <a-row>
            <a-col :span="12">
              <a-form-model-item label="试用期开始日期" prop="ProbationStartDate" :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 14 }">
                <a-date-picker v-model="entity.ProbationStartDate" type="date" :disabled="isEdit" format="YYYY-MM-DD"
                  placeholder="请选择试用期开始日期" style="width: 100%;" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="试用期结束日期" prop="ProbationEndDate" :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 14 }">
                <a-date-picker v-model="entity.ProbationEndDate" type="date" :disabled="isEdit" format="YYYY-MM-DD"
                  placeholder="请选择试用期结束日期" style="width: 100%;" />
              </a-form-model-item>
            </a-col>
          </a-row> -->
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="生效日期" prop="ContractEffectDate" :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 14 }">
                <a-date-picker v-model="entity.ContractEffectDate" :disabled-date="disabledStartDate" type="date"
                  :disabled="isEdit" format="YYYY-MM-DD" placeholder="请选择生效日期" style="width: 100%;" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="终止日期" prop="ContractEndDate" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-date-picker v-model="entity.ContractEndDate" :disabled-date="disabledEndDate" type="date"
                  :disabled="isWriteEndDate || isEdit" format="YYYY-MM-DD" placeholder="请选择终止日期" style="width: 100%;" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="连续签订次数" prop="Remark" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a @click="openHistoryHT">{{ entity.ContinuousSignedConut }}</a>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="续签原因" prop="Remark" :labelCol="{ span: 3 }" :wrapperCol="{ span: 19 }">
                <a-textarea v-model="entity.Remark" :disabled="isEdit" placeholder="请输入" :rows="4" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-divider orientation="left">甲方（单位）信息</a-divider>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="劳动合同主体" prop="SubjectLaborContract" :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 14 }">
                <a-input-search v-model="entity.SubjectLaborContract" :disabled="isEdit" placeholder=""
                  style="width: 100%" @search="onSearch" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="甲方单位代表人" prop="RepParty" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.RepParty" :disabled="isEdit" autocomplete="off" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="甲方地址" prop="address" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.AddressPartyA" :disabled="isEdit" autocomplete="off" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-divider orientation="left">乙方（员工）信息</a-divider>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="乙方姓名" prop="EmpName" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.EmpName" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="员工编码" prop="EmpCode" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.EmpCode" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="身份证号" prop="IdCardNumber" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.IdCardNumber" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="乙方地址" prop="AddressPartyB" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.AddressPartyB" :disabled="isEdit" autocomplete="off" />
              </a-form-model-item>
            </a-col>
            <!-- <a-col :span="12">
              <a-form-model-item label="护照号" prop="PassportNo" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.PassportNo" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col> -->
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="所属职位" prop="PostName" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.PostName" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="所属组织" prop="OrgInfo" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-input v-model="entity.OrgInfo" disabled autocomplete="off" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="用工状态" prop="EmployRelStatus" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <SelectDiction ref="emselectDiction" :Name="'用工关系状态'" :disabled="isEdit"
                  @selectedvalue="selectEmployRelStatus" :Value="entity.EmployRelStatus"></SelectDiction>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="附件" :labelCol="{ span: 3 }" :wrapperCol="{ span: 19 }">
                <uploadInfo ref="selectDiction" :fileEntity="fileEntity" :isNoUploadP="isEdit"
                  @handleUpload="handleUpload"></uploadInfo>
              </a-form-item>
            </a-col>
          </a-row>
          <a-divider orientation="left">绩效信息</a-divider>
          <a-row>
            <a-table ref="table" :columns="perColumns" :rowKey="row => row.F_Id" :dataSource="perData"
              :pagination="false" :loading="loading" :bordered="true">
            </a-table>
          </a-row>
          <a-divider orientation="left">其他信息</a-divider>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="备注" prop="Remark" :labelCol="{ span: 3 }" :wrapperCol="{ span: 19 }">
                <a-textarea v-model="entity.RemarkStr" :disabled="isEdit" placeholder="请输入" :rows="4" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-row>
      </a-form-model>
    </a-spin>
    <SelectCompany ref="selectCompany" :callBack="SeletedComp"></SelectCompany>
    <HistoryList ref="historyList"></HistoryList>
  </a-modal>
</template>

<script>
import SelectDiction from '@/components/SelectDictionaries/DictionariesList'
import SelectCompany from '@/components/SelectCompany/Index'
import HistoryList from './HistoryList'
import moment from 'moment'
import uploadInfo from '@/components/TemplateUpload/upload'

const perColumns = [
  { title: '员工编码', dataIndex: 'EmpCode', width: '15%' },
  { title: '姓名', dataIndex: 'EmpName', width: '10%' },
  { title: '绩效考核年份', dataIndex: 'PerformanceAppYear', width: '20%' },
  { title: '绩效等级', dataIndex: 'PerformanceRating', width: '10%' },
  { title: '备注', dataIndex: 'Remark', width: '45%' }
]
export default {
  components: {
    SelectDiction,
    SelectCompany,
    HistoryList,
    uploadInfo
  },
  props: {
    parentObj: Object
  },
  data () {
    return {
      layout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 }
      },
      visible: false,
      loading: false,
      perColumns, //绩效列定义
      perData: [],
      entity: {},
      rules: {},
      isEdit: false,
      title: '',
      isWriteEndDate: false,
      fileEntity: {}
    }
  },
  methods: {
    goYaer (year) {
      this.$http.post('/HR_EmployeeInfoManage/HR_PerformanceInfo/ChangYear', { Year: year, YearDate: this.entity.ContractEffectDate }).then((rep) => {
        if (rep.Success) {
          this.entity.ContractEndDate = moment(rep.Data)
        }
      })
    },
    //员工绩效
    getPerformanceInfoList (userId) {
      var queryParam = { EmpId: userId }

      this.loading = true

      this.$http
        .post('/HR_EmployeeInfoManage/HR_PerformanceInfo/GetDataList', {
          PageIndex: 1,
          PageRows: 3,
          SortField: 'PerformanceAppYear',
          SortType: 'desc',
          Search: { EmpId: userId }
        })
        .then(resJson => {
          this.loading = false
          this.perData = resJson.Data
        })
    },
    handleUpload (res) {
      if (this.fileEntity.Files === null) {
        this.fileEntity.Files = res.Files
      } else {
        res.Files.forEach(e => {
          this.fileEntity.Files.push(e)
        })
      }
    },
    //打开历史合同签订情况
    openHistoryHT () {
      this.$refs.historyList.openForm('历史合同', this.entity)
    },
    onSearch () {
      this.$refs.selectCompany.openForm('选择劳动合同主体')
    },
    SeletedComp (data) {
      console.log(data)
      this.entity.SubjectLaborContract = data[0].F_FullName
      this.entity.RepParty = data[0].F_Manager
      this.entity.AddressPartyA = data[0].F_Address
      this.entity.F_CompanyId = data[0].F_Id
    },
    //选择合同类别
    selectContractType (value) {
      this.entity.ContractType = value
    },
    selectContractDurationDate (value) {
      console.log(value)
      if (value === '2') {
        this.entity.ContractEndDate = null
        this.entity.ContractPeriod = null
        this.isWriteEndDate = true
      } else {
        this.isWriteEndDate = false
      }
      this.entity.ContractDurationDate = value
    },
    //用工点击
    selectEmployRelStatus (value) {
      this.entity.EmployRelStatus = value
    },
    selectContractModule (value) {
      this.entity.ContractModule = value
    },
    disabledStartDate (startValue) {
      const endValue = this.entity.ContractEndDate
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate (endValue) {
      const startValue = this.entity.ContractEffectDate
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    init () {
      this.visible = true
      this.entity = {
        SubjectLaborContract: '',
        RepParty: '',
        AddressPartyA: '',
        EmpName: '',
        EmpCode: '',
        IdCardNumber: '',
        PassportNo: '',
        PostName: '',
        OrgInfo: '',
        AddressPartyB: '',
        ContractNumber: '',
        SigningDate: null,
        ContractType: '',
        ContractModule: '',
        ContractDurationDate: '',
        ContractPeriod: '',
        ProbationStartDate: null,
        ProbationEndDate: null,
        ContractEffectDate: null,
        ContractEndDate: null,
        Remark: ''
      }
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    openForm (id, queryType, title, isEdit, isAdd) {
      this.title = title
      this.isEdit = isEdit
      this.init()

      if (id) {
        var url = '/HR_EmployeeInfoManage/HR_LaborContractInfo/GetXQLaborContractInfo'
        if (!isAdd) {
          url = '/HR_EmployeeInfoManage/HR_LaborContractInfo/GetXQLaborContractInfoById'
        }
        this.loading = true
        this.$http.post(url, { id: id }).then(resJson => {
          this.loading = false
          if (resJson.Data) {
            if (resJson.Data.SigningDate) {
              resJson.Data.SigningDate = moment(resJson.Data.SigningDate)
            }
            if (resJson.Data.ContractEffectDate) {
              resJson.Data.ContractEffectDate = moment(resJson.Data.ContractEffectDate)
            }
            if (resJson.Data.ContractEndDate) {
              resJson.Data.ContractEndDate = moment(resJson.Data.ContractEndDate)
            }
            if (resJson.Data.ProbationStartDate) {
              resJson.Data.ProbationStartDate = moment(resJson.Data.ProbationStartDate)
            }
            if (resJson.Data.ProbationEndDate) {
              resJson.Data.ProbationEndDate = moment(resJson.Data.ProbationEndDate)
            }
            resJson.Data.Remark = ''
            this.entity = resJson.Data
            if (isAdd) {
              this.entity.F_Id = ''
            }
            //同一个合同主体连续续签2次后就不固定期限
            if (this.entity.ContinuousSignedConut > 2) {
              this.entity.ContractDurationDate = '无固定期限'
              this.entity.ContractEndDate = null
              this.isWriteEndDate = true
            }
            this.getPerformanceInfoList(this.entity.UserId)
            var fileId = ''
            if (!isAdd) {
              fileId = this.entity.F_FileId
            }
            this.$http.post('/Base_Manage/Base_FileInfo/LoadFiles', { FileFolderId: fileId }).then(data => {
              this.fileEntity = data
            })
          }
        })
      }
    },
    //保存数据
    save () {
      this.$http
        .post('/Base_Manage/Base_FileInfo/UploadFilesSave', { FileInfo: JSON.stringify(this.fileEntity) })
        .then(data => {
          this.entity.F_FileId = data.F_Id
          this.$refs['form'].validate(valid => {
            if (!valid) {
              return
            }
            this.loading = true
            this.entity.F_BusState = 2 //续签
            this.entity.F_WFState = 0
            this.$http.post('/HR_EmployeeInfoManage/HR_LaborContractInfo/SaveData', this.entity).then(resJson => {
              this.loading = false

              if (resJson.Success) {
                this.$message.success('操作成功!')
                this.visible = false

                this.parentObj.getDataList()
              } else {
                this.$message.error(resJson.Msg)
              }
            })
          })
        })
    },
    //保存并创建流程
    createFlow () {
      this.$http
        .post('/Base_Manage/Base_FileInfo/UploadFilesSave', { FileInfo: JSON.stringify(this.fileEntity) })
        .then(data => {
          this.entity.F_FileId = data.F_Id
          this.$refs['form'].validate(valid => {
            if (!valid) {
              return
            }
            this.loading = true
            this.entity.F_BusState = 2 //续签
            this.entity.F_WFState = 1
            this.$http
              .post('/HR_EmployeeInfoManage/HR_LaborContractInfo/SaveAndCreateFlow', this.entity)
              .then(resJson => {
                this.loading = false

                if (resJson.Success) {
                  this.$message.success('创建流程成功!')
                  this.visible = false

                  this.parentObj.getDataList()
                } else {
                  this.$message.error(resJson.Msg)
                }
              })
          })
        })
    }
  }
}
</script>
