﻿using AutoMapper;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HR_ReportFormsManage;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HR_ReportFormsManage
{
    public class HR_PersonnelChangeDetailsBusiness : BaseBusiness<HR_PersonnelChangeDetailsDTO>, IHR_PersonnelChangeDetailsBusiness, ITransientDependency
    {
        public HR_PersonnelChangeDetailsBusiness(IDbAccessor db)
            : base(db)
        {
        }

        public async Task<PageResult<HR_PersonnelChangeDetailsDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            try
            {
                return await GetHR_PersonnelChangeDetails(input).GetPageResultAsync(input);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
        }
        public PageResult<HR_PersonnelChangeDetailsDTO> GetPerDataListAsync(PageInput<ConditionDTO> input)
        {
            try
            {
                //var q = this.Db.GetListBySql<HR_PersonnelChangeDetailsDTO>(@"SELECT  
                //    *  from view_PersonnelChangeDetails").AsQueryable();
                //var where = LinqHelper.True<HR_PersonnelChangeDetailsDTO>();
                //var search = input.Search;

                ////筛选
                //if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
                //{
                //    var newWhere = DynamicExpressionParser.ParseLambda<HR_PersonnelChangeDetailsDTO, bool>(
                //        ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                //    where = where.And(newWhere);
                //}

                var strSql = new StringBuilder();
                strSql.Append(@"SELECT  
                    *  from view_PersonnelChangeDetails");
                var search = input.Search;
                var q = new List<HR_PersonnelChangeDetailsDTO>();
                List<string> Data = new List<string> {
                "F_InductionDate","ContractEffectDate","ContractEndDate"
                };
                //筛选
                if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
                {
                    //时间筛选
                    if (Data.Contains(search.Condition))
                    {
                        var DateT = search.Keyword.Split(',');
                        strSql.Append($" Where {search.Condition} >='{DateT[0].ToDateTime()}' and {search.Condition} <='{DateT[1].ToDateTime()}' ");
                    }
                    else
                    {
                        strSql.Append($" Where {search.Condition} like  '%{search.Keyword}%' ");
                    }
                    q = this.Db.GetListBySql<HR_PersonnelChangeDetailsDTO>(strSql.ToString());
                }
                else
                {
                    q = this.Db.GetListBySql<HR_PersonnelChangeDetailsDTO>(strSql.ToString());
                }
                var where = LinqHelper.True<HR_PersonnelChangeDetailsDTO>();

                var hR_PersonnelChangeDetailsDTOs = q.AsQueryable().Where(where);
                var count = hR_PersonnelChangeDetailsDTOs.Count();
                var list = hR_PersonnelChangeDetailsDTOs
               .OrderBy($@"{input.SortField} {input.SortType}")
               .Skip((input.PageIndex - 1) * input.PageRows)
               .Take(input.PageRows)
               .ToList();
                return new PageResult<HR_PersonnelChangeDetailsDTO> { Data = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
        }
        public IQueryable<HR_PersonnelChangeDetailsDTO> GetHR_PersonnelChangeDetails(PageInput<ConditionDTO> input)
        {
            //Expression<Func<HR_FormalEmployees, HR_Induction, Base_Department, Base_Company, Base_Post, HR_LaborContractInfo, HR_PersonnelChangeDetailsDTO>> select = (a, b, c, d, e, h) => new HR_PersonnelChangeDetailsDTO
            //{
            //    F_Id = a.F_Id,
            //    CompanyName = d.F_FullName,
            //    DepartmentName = c.Name,
            //    UserName = a.NameUser,
            //    Position = e.F_Name,
            //    F_InductionDate = b.F_InductionDate,
            //    ContractEffectDate = h.ContractEffectDate,
            //    ContractEndDate = h.ContractEndDate,
            //    IdCardNumber = a.IdCardNumber,
            //    EmployRelStatus = a.EmployRelStatus
            //};
            //select = select.BuildExtendSelectExpre();
            //var q = from a in this.Db.GetIQueryable<HR_FormalEmployees>().AsExpandable()
            //        join b in this.Db.GetIQueryable<HR_Induction>() on a.F_Id equals b.F_UserId into indu
            //        from indud in indu
            //        join c in this.Db.GetIQueryable<Base_Department>() on a.F_DepartmentId equals c.Id into Dep
            //        from Depart in Dep.DefaultIfEmpty()
            //        join d in this.Db.GetIQueryable<Base_Company>() on a.F_CompanyId equals d.F_Id into Com
            //        from Company in Com.DefaultIfEmpty()
            //        join e in this.Db.GetIQueryable<Base_Post>() on a.F_PositionId equals e.F_Id into Post
            //        from PostEntity in Post.DefaultIfEmpty()
            //        join h in this.Db.GetIQueryable<HR_LaborContractInfo>() on a.F_Id equals h.UserId into labor
            //        from labore in labor.DefaultIfEmpty()
            //        select @select.Invoke(a, indud, Depart, Company, PostEntity, labore);
            var q = this.Db.GetListBySql<HR_PersonnelChangeDetailsDTO>(@"SELECT DISTINCT
                    (t.F_Id),
                    t.NameUser as UserName,
                    t1.F_InductionDate,
                    t2.Name AS DepartmentName,
                    t3.F_FullName as CompanyName,
                    t4.F_Name as Position,
                    t1.F_InductionDate,
                    t5.ContractEffectDate,
                    t5.ContractEndDate,
                    t.IdCardNumber,
                    t.EmployRelStatus
                    FROM
                    HR_FormalEmployees t
                    LEFT JOIN HR_Induction t1 ON t.F_Id = t1.F_UserId
                    LEFT JOIN Base_Department t2 ON t.F_DepartmentId = t2.Id
                    LEFT JOIN Base_Company t3 ON t.F_CompanyId = t3.F_Id
                    LEFT JOIN Base_Post t4 ON t.F_PositionId = t4.F_Id
                    LEFT JOIN HR_LaborContractInfo t5 on t.F_Id = t5.UserId").AsQueryable();
            var where = LinqHelper.True<HR_PersonnelChangeDetailsDTO>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_PersonnelChangeDetailsDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            return q.Where(where);
        }
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            return GetHR_PersonnelChangeDetails(input).ToDataTable();
        }
    }
}