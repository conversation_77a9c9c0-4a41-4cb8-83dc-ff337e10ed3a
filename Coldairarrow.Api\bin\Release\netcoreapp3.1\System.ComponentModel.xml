﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.CancelEventArgs">
      <summary>Provides data for a cancelable event.</summary>
    </member>
    <member name="M:System.ComponentModel.CancelEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.CancelEventArgs" /> class with the <see cref="P:System.ComponentModel.CancelEventArgs.Cancel" /> property set to <see langword="false" />.</summary>
    </member>
    <member name="M:System.ComponentModel.CancelEventArgs.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.CancelEventArgs" /> class with the <see cref="P:System.ComponentModel.CancelEventArgs.Cancel" /> property set to the given value.</summary>
      <param name="cancel">
        <see langword="true" /> to cancel the event; otherwise, <see langword="false" />.</param>
    </member>
    <member name="P:System.ComponentModel.CancelEventArgs.Cancel">
      <summary>Gets or sets a value indicating whether the event should be canceled.</summary>
      <returns>
        <see langword="true" /> if the event should be canceled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.ComponentModel.IChangeTracking">
      <summary>Defines the mechanism for querying the object for changes and resetting of the changed status.</summary>
    </member>
    <member name="M:System.ComponentModel.IChangeTracking.AcceptChanges">
      <summary>Resets the object's state to unchanged by accepting the modifications.</summary>
    </member>
    <member name="P:System.ComponentModel.IChangeTracking.IsChanged">
      <summary>Gets the object's changed status.</summary>
      <returns>
        <see langword="true" /> if the object's content has changed since the last call to <see cref="M:System.ComponentModel.IChangeTracking.AcceptChanges" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.ComponentModel.IEditableObject">
      <summary>Provides functionality to commit or rollback changes to an object that is used as a data source.</summary>
    </member>
    <member name="M:System.ComponentModel.IEditableObject.BeginEdit">
      <summary>Begins an edit on an object.</summary>
    </member>
    <member name="M:System.ComponentModel.IEditableObject.CancelEdit">
      <summary>Discards changes since the last <see cref="M:System.ComponentModel.IEditableObject.BeginEdit" /> call.</summary>
    </member>
    <member name="M:System.ComponentModel.IEditableObject.EndEdit">
      <summary>Pushes changes since the last <see cref="M:System.ComponentModel.IEditableObject.BeginEdit" /> or <see cref="M:System.ComponentModel.IBindingList.AddNew" /> call into the underlying object.</summary>
    </member>
    <member name="T:System.ComponentModel.IRevertibleChangeTracking">
      <summary>Provides support for rolling back the changes.</summary>
    </member>
    <member name="M:System.ComponentModel.IRevertibleChangeTracking.RejectChanges">
      <summary>Resets the object's state to unchanged by rejecting the modifications.</summary>
    </member>
    <member name="T:System.IServiceProvider">
      <summary>Defines a mechanism for retrieving a service object; that is, an object that provides custom support to other objects.</summary>
    </member>
    <member name="M:System.IServiceProvider.GetService(System.Type)">
      <summary>Gets the service object of the specified type.</summary>
      <param name="serviceType">An object that specifies the type of service object to get.</param>
      <returns>A service object of type <paramref name="serviceType" />.
-or-
<see langword="null" /> if there is no service object of type <paramref name="serviceType" />.</returns>
    </member>
  </members>
</doc>