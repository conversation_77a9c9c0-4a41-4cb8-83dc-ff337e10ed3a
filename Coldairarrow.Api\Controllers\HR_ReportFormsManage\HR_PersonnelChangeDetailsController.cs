﻿
using Coldairarrow.Business.HR_ReportFormsManage;
using Coldairarrow.Entity.HR_ReportFormsManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.HR_ReportFormsManage
{
    [Route("/HR_ReportFormsManage/[controller]/[action]")]
    public class HR_PersonnelChangeDetailsController : BaseApiController
    {
        #region DI

        public HR_PersonnelChangeDetailsController(IHR_PersonnelChangeDetailsBusiness hR_PersonnelChangeDetailsBusiness)
        {
            _hR_PersonnelChangeDetailsBusiness = hR_PersonnelChangeDetailsBusiness;
        }

        IHR_PersonnelChangeDetailsBusiness _hR_PersonnelChangeDetailsBusiness { get; }

        #endregion

        #region 获取

        [HttpPost]
        public PageResult<HR_PersonnelChangeDetailsDTO> GetDataList(PageInput<ConditionDTO> input)
        {
            return  _hR_PersonnelChangeDetailsBusiness.GetPerDataListAsync(input);
        }
        #endregion

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { 
                //取出数据源
                DataTable exportTable = _hR_PersonnelChangeDetailsBusiness.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "人员异动详情.xlsx";
                excelconfig.Title = "人员异动详情";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "departmentname", ExcelColumn = "部门", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "username", ExcelColumn = "名称", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "position", ExcelColumn = "职位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_inductiondate", ExcelColumn = "入职日期", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "contracteffectdate", ExcelColumn = "合同签订日期", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "contractenddate", ExcelColumn = "合同结束日期", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "idcardnumber", ExcelColumn = "身份证", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "employrelstatus", ExcelColumn = "状态", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion
    }
}