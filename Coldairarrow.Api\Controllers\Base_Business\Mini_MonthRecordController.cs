﻿using Coldairarrow.Business.Base_Business;
using Coldairarrow.Entity.Base_Business;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Base_Business
{
    [Route("/Base_Business/[controller]/[action]")]
    public class Mini_MonthRecordController : BaseApiController
    {
        #region DI

        public Mini_MonthRecordController(IMini_MonthRecordBusiness mini_MonthRecordBus)
        {
            _mini_MonthRecordBus = mini_MonthRecordBus;
        }

        IMini_MonthRecordBusiness _mini_MonthRecordBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Mini_MonthRecord>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _mini_MonthRecordBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Mini_MonthRecord> GetTheData(IdInputDTO input)
        {
            return await _mini_MonthRecordBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Mini_MonthRecord data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _mini_MonthRecordBus.AddDataAsync(data);
            }
            else
            {
                await _mini_MonthRecordBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _mini_MonthRecordBus.DeleteDataAsync(ids);
        }

        #endregion

        #region 二次开发
        [NoCheckJWT]
        [HttpGet]
        public void testDayData()
        {
            DateTime DateNow = DateTime.Now.AddMonths(-1);
            DateTime day = new DateTime(DateNow.Year, DateNow.Month, 1);
            for (var i = 0; i < 7; i++)
            {

                //getMonthlyVisitTrend(day);
                //getDailySummary(day);
                getMonthlyRetain(day);
                day = day.AddMonths(-1);
            }
        }
        [NoCheckJWT]
        [HttpGet]
        public void getMonthData()
        {
            DateTime DateNow = DateTime.Now.AddMonths(-2);
            DateTime day = new DateTime(DateNow.Year, DateNow.Month, 1);         
                getMonthlyVisitTrend(day);
                getDailySummary(day);
                getMonthlyRetain(day);
        }
        [NoCheckJWT]
        [HttpGet]
        public string getMonthlyVisitTrend(DateTime day)
        {

           
            var visitTrend = WechatMiniHelper.getMonthlyVisitTrend(day);
            if (!visitTrend.IsNullOrEmpty())
            {
                var data = _mini_MonthRecordBus.GetDataByDate(day);
                if (data.IsNullOrEmpty())
                {
                    data = new Mini_MonthRecord();
                    data.W_session_cnt = visitTrend.session_cnt;
                    data.W_visit_pv = visitTrend.visit_pv;
                    data.W_visit_uv = visitTrend.visit_uv;
                    data.W_stay_time_session = visitTrend.stay_time_session;
                    data.W_stay_time_uv = visitTrend.stay_time_uv;
                    data.W_visit_depth = visitTrend.visit_depth;
                    data.W_visit_uv_new = visitTrend.visit_uv_new;
                    data.Id = IdHelper.GetId();
                    data.Date = day;
                    _mini_MonthRecordBus.AddDataAsync(data);
                }
                else
                {
                    data.W_session_cnt = visitTrend.session_cnt;
                    data.W_visit_pv = visitTrend.visit_pv;
                    data.W_visit_uv = visitTrend.visit_uv;
                    data.W_stay_time_session = visitTrend.stay_time_session;
                    data.W_stay_time_uv = visitTrend.stay_time_uv;
                    data.W_visit_depth = visitTrend.visit_depth;
                    data.W_visit_uv_new = visitTrend.visit_uv_new;
                    _mini_MonthRecordBus.UpdateDataAsync(data);
                }
            }
            return "1";
        }
        [NoCheckJWT]
        [HttpGet]
        public string getDailySummary(DateTime day)
        {


            var result = WechatMiniHelper.getDailySummary(day);
            if (!result.IsNullOrEmpty())
            {
                var data = _mini_MonthRecordBus.GetDataByDate(day);
                if (data.IsNullOrEmpty())
                {
                    data = new Mini_MonthRecord();
                    data.W_visit_total = result.visit_total;
                    data.W_share_pv = result.share_pv;
                    data.W_share_uv = result.share_uv;
                    data.Id = IdHelper.GetId();
                    data.Date = day;
                    _mini_MonthRecordBus.AddDataAsync(data);
                }
                else
                {
                    data.W_visit_total = result.visit_total;
                    data.W_share_pv = result.share_pv;
                    data.W_share_uv = result.share_uv;
                    _mini_MonthRecordBus.UpdateDataAsync(data);
                }
            }
            return "1";
        }
        [NoCheckJWT]
        [HttpGet]
        public string getMonthlyRetain(DateTime day)
        {


            var result = WechatMiniHelper.getMonthlyRetain(day);
            if (!result.IsNullOrEmpty())
            {
                var data = _mini_MonthRecordBus.GetDataByDate(day);
                for (var i=0;i<result.visit_uv.Length;i++)
                {
                    if (result.visit_uv[i].key ==1)
                    {
                        if (data.IsNullOrEmpty())
                        {
                            data = new Mini_MonthRecord();
                            data.W_retain_uv_hot = result.visit_uv[i].value;
                            data.W_retain_uv_new = result.visit_uv_new[i].value;
                            data.Id = IdHelper.GetId();
                            data.Date = day;
                            _mini_MonthRecordBus.AddDataAsync(data);
                        }
                        else
                        {
                            data.W_retain_uv_hot = result.visit_uv[i].value;
                            data.W_retain_uv_new = result.visit_uv_new[i].value;
                            _mini_MonthRecordBus.UpdateDataAsync(data);
                        }
                    }
                }
                
            }
            return "1";
        }
        #endregion
    }
}