﻿using AutoMapper;
using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HR_ReportFormsManage;
using Coldairarrow.Util;
using Coldairarrow.Util.Excel.Model;
using Coldairarrow.Util.Helper;
using EFCore.Sharding;
using LinqKit;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Reflection;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HR_ReportFormsManage
{
    public class HR_PersonnelChangeBusiness : BaseBusiness<HR_PersonnelChangeDTO>, IHR_PersonnelChangeBusiness, ITransientDependency
    {
        IBase_DepartmentBusiness _base_DepartmentBusiness;
        public HR_PersonnelChangeBusiness(IDbAccessor db, IBase_DepartmentBusiness base_DepartmentBusiness)
            : base(db)
        {
            _base_DepartmentBusiness = base_DepartmentBusiness;
        }
        public List<HR_PersonnelChangeDTO> GetHR_PersonnelChangeDetails(PageInput<ConditionDTO> input, out int total)
        {
            var where = LinqHelper.True<Base_Department>();
            var search = input.Search;
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Base_Department, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            var departments = this.Db.GetIQueryable<Base_Department>().Where(where);

            total = departments.Count();
            var list = departments.OrderBy($@"{input.SortField} {input.SortType}")
             .Skip((input.PageIndex - 1) * input.PageRows)
             .Take(input.PageRows);
            //var person = departments.GroupBy(x => new { x.Name, x.Id })
            //            .Select(g => new HR_PersonnelChangeDTO
            //            {
            //                DepartmentId = g.Key.Id,
            //                DepartmentName = g.Key.Name
            //            }); 
            var personList = list.Select(s => new HR_PersonnelChangeDTO { DepartmentId = s.Id, DepartmentName = s.Name }).ToList();
            if (personList.Count() > 0)
            {
                var depId = personList.Select(s => s.DepartmentId).ToList();
                var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().Where(s => depId.Contains(s.F_DepartmentId));
                var userIds = formalEmployees.Select(s => s.F_Id).ToList();
                var Year = DateTime.Now.Year;
                //获取离职数据
                var depList = this.Db.GetIQueryable<HR_Departure>().Where(s => userIds.Contains(s.F_UserId)).ToList();
                //查询所有的员工
                //var formalEmployees = this.Db.GetIQueryable<HR_FormalEmployees>();
                var toDate = new DateTime(DateTime.Now.Year, 1, 1);
                var endDate = new DateTime(DateTime.Now.Year, 12, 31);
                //查询今年入职人
                var inductions = this.Db.GetIQueryable<HR_Induction>()
                                 .Where(i => i.F_InductionDate >= toDate && i.F_InductionDate <= endDate && userIds.Contains(i.F_UserId))
                                 .ToList();

                foreach (var item in personList)
                {
                    var DepList = _base_DepartmentBusiness.GetDepList(item.DepartmentId);
                    List<PersonnelChangeData> personnelChangeDatas = new List<PersonnelChangeData>();
                    //查询这部门的正式员工
                    var formIds = formalEmployees.Where(i => DepList.Contains(i.F_DepartmentId))
                                .Select(i => i.F_Id).ToList();
                    for (var index = 1; index <= 12; index++)
                    {
                        var dateTime = GetDate(index);
                        var firstDate = dateTime.First();
                        var SenDate = dateTime.Skip(1).Take(1).First();
                        //排除之前离职的人员
                        var UserIds = depList.Where(i => i.F_TrueDepartureDate < firstDate)
                                      .Select(i => i.F_UserId).ToList();
                        if (formIds.Count > 0)
                        {
                            personnelChangeDatas.Add(new PersonnelChangeData()
                            {
                                Incumbency = formalEmployees.Where(i => !UserIds.Contains(i.F_Id) &&
                                                                   DepList.Contains(i.F_DepartmentId)).Count(),
                                NewHires = inductions.Where(i => Convert.ToDateTime(i.F_InductionDate).Month == index &&
                                                            formIds.Contains(i.F_UserId)).Count(),
                                TurnoverNumber = depList.Where(i => i.F_TrueDepartureDate >= SenDate
                                                && i.F_TrueDepartureDate <= firstDate && formIds.Contains(i.F_UserId))
                                                .Count(),
                                Month = index
                            });
                        }
                        else
                        {
                            personnelChangeDatas.Add(new PersonnelChangeData()
                            {
                                Incumbency = 0,
                                NewHires = 0,
                                TurnoverNumber = 0,
                                Month = index
                            });
                        }
                    }
                    item.Personnels = personnelChangeDatas;
                }
            }
            return personList;
        }
        public List<HR_PersonnelChangeDTO> GetHR_PersonnelList(PageInput<ConditionDTO> input)
        {
            var where = LinqHelper.True<Base_Department>();
            var search = input.Search;
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Base_Department, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            var departments = this.Db.GetIQueryable<Base_Department>().Where(where);
            var personList = departments.Select(s => new HR_PersonnelChangeDTO { DepartmentId = s.Id, DepartmentName = s.Name }).ToList();
            if (personList.Count() > 0)
            {
                var depId = personList.Select(s => s.DepartmentId).ToList();
                var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().Where(s => depId.Contains(s.F_DepartmentId));
                var userIds = formalEmployees.Select(s => s.F_Id).ToList();
                var Year = DateTime.Now.Year;
                //获取离职数据
                var depList = this.Db.GetIQueryable<HR_Departure>().Where(s => userIds.Contains(s.F_UserId)).ToList();
                //查询所有的员工
                //var formalEmployees = this.Db.GetIQueryable<HR_FormalEmployees>();
                var toDate = new DateTime(DateTime.Now.Year, 1, 1);
                var endDate = new DateTime(DateTime.Now.Year, 12, 31);
                //查询今年入职人
                var inductions = this.Db.GetIQueryable<HR_Induction>()
                                 .Where(i => i.F_InductionDate >= toDate && i.F_InductionDate <= endDate && userIds.Contains(i.F_UserId))
                                 .ToList();

                foreach (var item in personList)
                {
                    var DepList = _base_DepartmentBusiness.GetDepList(item.DepartmentId);
                    List<PersonnelChangeData> personnelChangeDatas = new List<PersonnelChangeData>();
                    //查询这部门的正式员工
                    var formIds = formalEmployees.Where(i => DepList.Contains(i.F_DepartmentId))
                                .Select(i => i.F_Id).ToList();
                    for (var index = 1; index <= 12; index++)
                    {
                        var dateTime = GetDate(index);
                        var firstDate = dateTime.First();
                        var SenDate = dateTime.Skip(1).Take(1).First();
                        //排除之前离职的人员
                        var UserIds = depList.Where(i => i.F_TrueDepartureDate < firstDate)
                                      .Select(i => i.F_UserId).ToList();
                        if (formIds.Count > 0)
                        {
                            personnelChangeDatas.Add(new PersonnelChangeData()
                            {
                                Incumbency = formalEmployees.Where(i => !UserIds.Contains(i.F_Id) &&
                                                                   DepList.Contains(i.F_DepartmentId)).Count(),
                                NewHires = inductions.Where(i => Convert.ToDateTime(i.F_InductionDate).Month == index &&
                                                            formIds.Contains(i.F_UserId)).Count(),
                                TurnoverNumber = depList.Where(i => i.F_TrueDepartureDate >= SenDate
                                                && i.F_TrueDepartureDate <= firstDate && formIds.Contains(i.F_UserId))
                                                .Count(),
                                Month = index
                            });
                        }
                        else
                        {
                            personnelChangeDatas.Add(new PersonnelChangeData()
                            {
                                Incumbency = 0,
                                NewHires = 0,
                                TurnoverNumber = 0,
                                Month = index
                            });
                        }
                    }
                    item.Personnels = personnelChangeDatas;
                }
            }
            return personList;
        }
        /// <summary>
        /// 获取日期
        /// </summary>
        /// <param name="Year"></param>
        /// <returns></returns>
        public List<DateTime> GetDate(int month)
        {
            List<DateTime> dateTimes = new List<DateTime>() {
             new DateTime(DateTime.Now.Year, month,DateTime.DaysInMonth(DateTime.Now.Year, month)),
             new DateTime(DateTime.Now.Year, month,1)
            };
            return dateTimes;
        }
        public List<HR_PersonnelChangeDTO> GetTreeDataList(PageInput<ConditionDTO> input)
        {
            List<HR_PersonnelChangeDTO> hR_PersonnelChangeDTOs = new List<HR_PersonnelChangeDTO>();
            var departmentTreeDTOs = _base_DepartmentBusiness.GetTreeDataList(input);
            hR_PersonnelChangeDTOs= departmentTreeDTOs.Select(x => new HR_PersonnelChangeDTO
            {
                Id = x.Id,
                ParentId = x.ParentId,
                Text = x.Text,
                Value = x.Id,
                Children=x.Children,
                Level=x.Level,
                key=x.key,
                title=x.title,
                value=x.value
            }).ToList(); 
            if (hR_PersonnelChangeDTOs.Count() > 0)
            {
                var depId = hR_PersonnelChangeDTOs.Select(s => s.Id).ToList();
                var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().AsNoTracking().ToList();
                var userIds = formalEmployees.Select(s => s.F_Id).ToList();
                var Year = DateTime.Now.Year;
                //获取离职数据
                var depList = this.Db.GetIQueryable<HR_Departure>().Where(s => userIds.Contains(s.F_UserId)).ToList();
                //查询所有的员工
                //var formalEmployees = this.Db.GetIQueryable<HR_FormalEmployees>();
                var toDate = new DateTime(DateTime.Now.Year, 1, 1);
                var endDate = new DateTime(DateTime.Now.Year, 12, 31);
                //查询今年入职人
                var inductions = this.Db.GetIQueryable<HR_Induction>()
                                 .Where(i => i.F_InductionDate >= toDate && i.F_InductionDate <= endDate && userIds.Contains(i.F_UserId))
                                 .ToList();

                foreach (var item in hR_PersonnelChangeDTOs)
                {
                    var DepList = _base_DepartmentBusiness.GetDepList(item.Id);
                    List<PersonnelChangeData> personnelChangeDatas = new List<PersonnelChangeData>();

                    //查询这部门的正式员工
                    var formIds = formalEmployees.Where(i => DepList.Contains(i.F_DepartmentId))
                                .Select(i => i.F_Id).ToList();
                    for (var index = 1; index <= 12; index++)
                    {
                        var dateTime = GetDate(index);
                        var firstDate = dateTime.First();
                        var SenDate = dateTime.Skip(1).Take(1).First();
                        //排除之前离职的人员
                        var UserIds = depList.Where(i => i.F_TrueDepartureDate < firstDate)
                                      .Select(i => i.F_UserId).ToList();
                        if (formIds.Count > 0)
                        {
                            personnelChangeDatas.Add(new PersonnelChangeData()
                            {
                                Incumbency = formalEmployees.Where(i => !UserIds.Contains(i.F_Id) &&
                                                                   DepList.Contains(i.F_DepartmentId)).Count(),
                                NewHires = inductions.Where(i => Convert.ToDateTime(i.F_InductionDate).Month == index &&
                                                            formIds.Contains(i.F_UserId)).Count(),
                                TurnoverNumber = depList.Where(i => i.F_TrueDepartureDate >= SenDate
                                                && i.F_TrueDepartureDate <= firstDate && formIds.Contains(i.F_UserId))
                                                .Count(),
                                Month = index
                            });
                        }
                        else
                        {
                            personnelChangeDatas.Add(new PersonnelChangeData()
                            {
                                Incumbency = 0,
                                NewHires = 0,
                                TurnoverNumber = 0,
                                Month = index
                            });
                        }
                    }
                    item.Personnels = personnelChangeDatas;
                }
            }
            return hR_PersonnelChangeDTOs;
        }
        public PageResult<HR_PersonnelChangeDTO> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            try
            {
                var total = 0;
                var personnelChangeDTOs = GetHR_PersonnelChangeDetails(input, out total);
                // var count = personnelChangeDTOs.Count();
                // var list = personnelChangeDTOs
                //.OrderBy($@"{input.SortField} {input.SortType}")
                //.Skip((input.PageIndex - 1) * input.PageRows)
                //.Take(input.PageRows);

                //if (personnelChangeDTOs.Count() > 0)
                //{
                //    var depId = personnelChangeDTOs.Select(s => s.DepartmentId).ToList();
                //    var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().Where(s => depId.Contains(s.F_DepartmentId));
                //    var userIds = formalEmployees.Select(s => s.F_Id).ToList();
                //    var Year = DateTime.Now.Year;
                //    //获取离职数据
                //    var depList = this.Db.GetIQueryable<HR_Departure>().Where(s => userIds.Contains(s.F_UserId)).ToList();
                //    //查询所有的员工
                //    //var formalEmployees = this.Db.GetIQueryable<HR_FormalEmployees>();
                //    var toDate = new DateTime(DateTime.Now.Year, 1, 1);
                //    var endDate = new DateTime(DateTime.Now.Year, 12, 31);
                //    //查询今年入职人
                //    var inductions = this.Db.GetIQueryable<HR_Induction>()
                //                     .Where(i => i.F_InductionDate >= toDate && i.F_InductionDate <= endDate && userIds.Contains(i.F_UserId))
                //                     .ToList();
                //    foreach (var item in personnelChangeDTOs)
                //    {
                //        var DepList = _base_DepartmentBusiness.GetDepList(item.DepartmentId);
                //        List<PersonnelChangeData> personnelChangeDatas = new List<PersonnelChangeData>();
                //        //查询这部门的正式员工
                //        var formIds = formalEmployees.Where(i => DepList.Contains(i.F_DepartmentId))
                //                    .Select(i => i.F_Id).ToList();
                //        for (var index = 1; index <= 12; index++)
                //        {
                //            var dateTime = GetDate(index);
                //            var firstDate = dateTime.First();
                //            var SenDate = dateTime.Skip(1).Take(1).First();
                //            //排除之前离职的人员
                //            var UserIds = depList.Where(i => i.F_TrueDepartureDate < firstDate)
                //                          .Select(i => i.F_UserId).ToList();
                //            if (formIds.Count > 0)
                //            {
                //                personnelChangeDatas.Add(new PersonnelChangeData()
                //                {
                //                    Incumbency = formalEmployees.Where(i => !UserIds.Contains(i.F_Id) &&
                //                                                       DepList.Contains(i.F_DepartmentId)).Count(),
                //                    NewHires = inductions.Where(i => Convert.ToDateTime(i.F_InductionDate).Month == index &&
                //                                                formIds.Contains(i.F_UserId)).Count(),
                //                    TurnoverNumber = depList.Where(i => i.F_TrueDepartureDate >= SenDate
                //                                    && i.F_TrueDepartureDate <= firstDate && formIds.Contains(i.F_UserId))
                //                                    .Count(),
                //                    Month = index
                //                });
                //            }
                //            else
                //            {
                //                personnelChangeDatas.Add(new PersonnelChangeData()
                //                {
                //                    Incumbency = 0,
                //                    NewHires = 0,
                //                    TurnoverNumber = 0,
                //                    Month = index
                //                });
                //            }
                //        }
                //        item.Personnels = personnelChangeDatas;
                //    }
                //}
                return new PageResult<HR_PersonnelChangeDTO> { Data = personnelChangeDTOs.ToList(), Total = total };
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
        }
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        public byte[] ExcelDownload(PageInput<ConditionDTO> input)
        {
            var hR_PersonnelChangeDTOs = GetHR_PersonnelList(input);
            List<PersonnelChangeDetailed> changeDetaileds = new List<PersonnelChangeDetailed>();
            foreach (var item in hR_PersonnelChangeDTOs)
            {
                PersonnelChangeDetailed changeDetailed = new PersonnelChangeDetailed();
                changeDetailed.DepartmentName = item.DepartmentName;
                foreach (var items in item.Personnels)
                {
                    switch (items.Month)
                    {
                        case 1:
                            changeDetailed.Incumbency1 = items.Incumbency;
                            changeDetailed.NewHires1 = items.NewHires;
                            changeDetailed.TurnoverNumber1 = items.TurnoverNumber;
                            break;
                        case 2:
                            changeDetailed.Incumbency2 = items.Incumbency;
                            changeDetailed.NewHires2 = items.NewHires;
                            changeDetailed.TurnoverNumber2 = items.TurnoverNumber;
                            break;
                        case 3:
                            changeDetailed.Incumbency3 = items.Incumbency;
                            changeDetailed.NewHires3 = items.NewHires;
                            changeDetailed.TurnoverNumber3 = items.TurnoverNumber;
                            break;
                        case 4:
                            changeDetailed.Incumbency4 = items.Incumbency;
                            changeDetailed.NewHires4 = items.NewHires;
                            changeDetailed.TurnoverNumber4 = items.TurnoverNumber;
                            break;
                        case 5:
                            changeDetailed.Incumbency5 = items.Incumbency;
                            changeDetailed.NewHires5 = items.NewHires;
                            changeDetailed.TurnoverNumber5 = items.TurnoverNumber;
                            break;
                        case 6:
                            changeDetailed.Incumbency6 = items.Incumbency;
                            changeDetailed.NewHires6 = items.NewHires;
                            changeDetailed.TurnoverNumber6 = items.TurnoverNumber;
                            break;
                        case 7:
                            changeDetailed.Incumbency7 = items.Incumbency;
                            changeDetailed.NewHires7 = items.NewHires;
                            changeDetailed.TurnoverNumber7 = items.TurnoverNumber;
                            break;
                        case 8:
                            changeDetailed.Incumbency8 = items.Incumbency;
                            changeDetailed.NewHires8 = items.NewHires;
                            changeDetailed.TurnoverNumber8 = items.TurnoverNumber;
                            break;
                        case 9:
                            changeDetailed.Incumbency9 = items.Incumbency;
                            changeDetailed.NewHires9 = items.NewHires;
                            changeDetailed.TurnoverNumber9 = items.TurnoverNumber;
                            break;
                        case 10:
                            changeDetailed.Incumbency10 = items.Incumbency;
                            changeDetailed.NewHires10 = items.NewHires;
                            changeDetailed.TurnoverNumber10 = items.TurnoverNumber;
                            break;
                        case 11:
                            changeDetailed.Incumbency11 = items.Incumbency;
                            changeDetailed.NewHires11 = items.NewHires;
                            changeDetailed.TurnoverNumber11 = items.TurnoverNumber;
                            break;
                        case 12:
                            changeDetailed.Incumbency12 = items.Incumbency;
                            changeDetailed.NewHires12 = items.NewHires;
                            changeDetailed.TurnoverNumber12 = items.TurnoverNumber;
                            break;
                        default:
                            break;
                    }
                }
                changeDetaileds.Add(changeDetailed);
            }
            //设置导出格式
            ExcelConfig excelconfig = new ExcelConfig();
            excelconfig.FileName = "人员异动.xls";
            excelconfig.IsAllSizeColumn = true;
            //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
            excelconfig.ColumnEntity = new List<ColumnModel>
                {
                    //new ColumnModel() { Column = "index", ExcelColumn = "序号", Sort = 0 },
                    new ColumnModel() { Column = "departmentname", ExcelColumn = "部门名称", Sort = 1 },
                    new ColumnModel() { Column = "incumbency1", ExcelColumn = "在职人数", Sort = 2 },
                    new ColumnModel() { Column = "newhires1", ExcelColumn = "新入职人数", Sort = 3 },
                    new ColumnModel() { Column = "turnovernumber1", ExcelColumn = "离职人数", Sort = 4 },
                    new ColumnModel() { Column = "incumbency2", ExcelColumn = "在职人数", Sort = 5 },
                    new ColumnModel() { Column = "newhires2", ExcelColumn = "新入职人数", Sort = 6 },
                    new ColumnModel() { Column = "turnovernumber2", ExcelColumn = "离职人数", Sort = 7 },
                    new ColumnModel() { Column = "incumbency3", ExcelColumn = "在职人数", Sort = 8 },
                    new ColumnModel() { Column = "newhires3", ExcelColumn = "新入职人数", Sort = 9 },
                    new ColumnModel() { Column = "turnovernumber3", ExcelColumn = "离职人数", Sort = 10 },
                    new ColumnModel() { Column = "incumbency4", ExcelColumn = "在职人数", Sort = 11 },
                    new ColumnModel() { Column = "newhires4", ExcelColumn = "新入职人数", Sort = 12 },
                    new ColumnModel() { Column = "turnovernumber4", ExcelColumn = "离职人数", Sort = 13 },
                    new ColumnModel() { Column = "incumbency5", ExcelColumn = "在职人数", Sort = 14 },
                    new ColumnModel() { Column = "newhires5", ExcelColumn = "新入职人数", Sort = 15 },
                    new ColumnModel() { Column = "TurnoverNumber5".ToLower(), ExcelColumn = "离职人数", Sort = 16 },
                    new ColumnModel() { Column = "Incumbency6".ToLower(), ExcelColumn = "在职人数", Sort = 17 },
                    new ColumnModel() { Column = "NewHires6".ToLower(), ExcelColumn = "新入职人数", Sort = 18},
                    new ColumnModel() { Column = "TurnoverNumber6".ToLower(), ExcelColumn = "离职人数", Sort = 19 },
                    new ColumnModel() { Column = "Incumbency7".ToLower(), ExcelColumn = "在职人数", Sort = 20 },
                    new ColumnModel() { Column = "NewHires7".ToLower(), ExcelColumn = "新入职人数", Sort = 21 },
                    new ColumnModel() { Column = "TurnoverNumber7".ToLower(), ExcelColumn = "离职人数", Sort = 22 },
                    new ColumnModel() { Column = "Incumbency8".ToLower(), ExcelColumn = "在职人数", Sort =23 },
                    new ColumnModel() { Column = "NewHires8".ToLower(), ExcelColumn = "新入职人数", Sort = 24 },
                    new ColumnModel() { Column = "TurnoverNumber8".ToLower(), ExcelColumn = "离职人数", Sort = 25 },
                    new ColumnModel() { Column = "Incumbency9".ToLower(), ExcelColumn = "在职人数", Sort = 26 },
                    new ColumnModel() { Column = "NewHires9".ToLower(), ExcelColumn = "新入职人数", Sort = 27 },
                    new ColumnModel() { Column = "TurnoverNumber9".ToLower(), ExcelColumn = "离职人数", Sort = 28 },
                    new ColumnModel() { Column = "Incumbency10".ToLower(), ExcelColumn = "在职人数", Sort = 29 },
                    new ColumnModel() { Column = "NewHires10".ToLower(), ExcelColumn = "新入职人数", Sort = 30 },
                    new ColumnModel() { Column = "TurnoverNumber10".ToLower(), ExcelColumn = "离职人数", Sort = 31 },
                    new ColumnModel() { Column = "Incumbency11".ToLower(), ExcelColumn = "在职人数", Sort = 32 },
                    new ColumnModel() { Column = "NewHires11".ToLower(), ExcelColumn = "新入职人数", Sort = 33 },
                    new ColumnModel() { Column = "TurnoverNumber11".ToLower(), ExcelColumn = "离职人数", Sort = 34 },
                    new ColumnModel() { Column = "Incumbency12".ToLower(), ExcelColumn = "在职人数", Sort = 35 },
                    new ColumnModel() { Column = "NewHires12".ToLower(), ExcelColumn = "新入职人数", Sort = 36 },
                    new ColumnModel() { Column = "TurnoverNumber12".ToLower(), ExcelColumn = "离职人数", Sort = 37 },
                };
            int i = 0;
            foreach (var item in excelconfig.ColumnEntity)
            {
                item.Sort = i;
                i++;
            }
            List<HeaderMultiMergeModel> headerMultis = new List<HeaderMultiMergeModel>();
            HeaderMultiMergeModel headerMulti = new HeaderMultiMergeModel
            {
                Row = 0
            };
            List<HeaderMultiMergeDetails> mergeDetails = new List<HeaderMultiMergeDetails>();
            for (int a = 0; a < 13; a++)
            {
                switch (a)
                {
                    case 0:
                        mergeDetails.Add(AssignmentInfo("部门", 1, 2, 0, 0));
                        break;
                    case 1:
                        mergeDetails.Add(AssignmentInfo("1月", 1, 1, 1, 3));
                        break;
                    case 2:
                        mergeDetails.Add(AssignmentInfo("2月", 1, 1, 4, 6));
                        break;
                    case 3:
                        mergeDetails.Add(AssignmentInfo("3月", 1, 1, 7, 9));
                        break;
                    case 4:
                        mergeDetails.Add(AssignmentInfo("4月", 1, 1, 10, 12));
                        break;
                    case 5:
                        mergeDetails.Add(AssignmentInfo("5月", 1, 1, 13, 15));
                        break;
                    case 6:
                        mergeDetails.Add(AssignmentInfo("6月", 1, 1, 16, 18));
                        break;
                    case 7:
                        mergeDetails.Add(AssignmentInfo("7月", 1, 1, 19, 21));
                        break;
                    case 8:
                        mergeDetails.Add(AssignmentInfo("8月", 1, 1, 22, 24));
                        break;
                    case 9:
                        mergeDetails.Add(AssignmentInfo("9月", 1, 1, 25, 27));
                        break;
                    case 10:
                        mergeDetails.Add(AssignmentInfo("10月", 1, 1, 28, 30));
                        break;
                    case 11:
                        mergeDetails.Add(AssignmentInfo("11月", 1, 1, 31, 33));
                        break;
                    case 12:
                        mergeDetails.Add(AssignmentInfo("12月", 1, 1, 34, 36));
                        break;
                    default:
                        break;
                }
            }
            headerMulti.List = mergeDetails;
            headerMultis.Add(headerMulti);
            var dataTabel = DataTableHelper.FillDataTable(changeDetaileds);
            var t = ExcelHelper.ExportMemoryStream(dataTabel, excelconfig, false, headerMultis).GetBuffer();
            //var t = ExcelHelper.ExportMemoryStream(dataTabel, excelconfig, true, null).GetBuffer();
            return t;
        }
        /// <summary>
        /// 装载表头数据
        /// </summary>
        /// <param name="name">表头列名</param>
        /// <param name="fRow">开始行</param>
        /// <param name="lRow">最后行</param>
        /// <param name="fCol">开始列</param>
        /// <param name="lCol">最后列</param>
        /// <returns></returns>
        public HeaderMultiMergeDetails AssignmentInfo(string name, int fRow, int lRow, int fCol, int lCol)
        {
            return new HeaderMultiMergeDetails()
            {
                HeaderTitle = name,
                FirstCol = fCol,
                FirstRow = fRow,
                LastCol = lCol,
                LastRow = lRow,
            };
        }
    }
}