﻿using AutoMapper;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using Coldairarrow.Util.Helper;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Coldairarrow.Business.HolidayManage
{
    public class HR_TermLeaveBusiness : BaseBusiness<HR_TermLeave>, IHR_TermLeaveBusiness, ITransientDependency
    {
        readonly IMapper _mapper;
        IConfiguration _configuration;
        private IHR_FormalEmployeesBusiness _hR_FormalEmployeesBusiness;
        public HR_TermLeaveBusiness(IDbAccessor db, IConfiguration configuration, IMapper mapper, IHR_FormalEmployeesBusiness hR_FormalEmployeesBusiness)
            : base(db)
        {
            _configuration = configuration;
            _mapper = mapper;
            _hR_FormalEmployeesBusiness = hR_FormalEmployeesBusiness;
        }

        #region 外部接口


        /// <summary>
        /// 根据请假ID获得已完成的销假数据
        /// </summary>
        /// <param name="askId"></param>
        /// <returns></returns>
        public async Task<List<HR_TermLeaveDTO>> GetListByAskIdAsync(ConditionDTO input)
        {
            Expression<Func<HR_TermLeave, HR_AskLeave, HR_TermLeaveDTO>> select = (t, a) => new HR_TermLeaveDTO
            {
                F_AskLeaveCode = a.F_AskLeaveCode,
                UserName = a.F_UserName,
            };
            int[] states = { 0, 4 };
            select = select.BuildExtendSelectExpre();
            var q = from t in this.Db.GetIQueryable<HR_TermLeave>().AsExpandable()
                    join a in this.Db.GetIQueryable<HR_AskLeave>() on t.F_AskLeaveId equals a.F_Id into ask
                    from a in ask.DefaultIfEmpty()
                    where !states.Contains(t.F_WFState.Value) && t.F_AskLeaveId == input.F_Id
                    select @select.Invoke(t, a);

            return await q.WhereIf(!input.UserId.IsNullOrEmpty(), x => x.F_Id != input.UserId).ToListAsync();
        }
        public async Task<PageResult<HR_TermLeaveDTO>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            Expression<Func<HR_TermLeave, HR_AskLeave, HR_TermLeaveDTO>> select = (t, a) => new HR_TermLeaveDTO
            {
                F_AskLeaveCode = a.F_AskLeaveCode,
                UserName = a.F_UserName,
            };
            select = select.BuildExtendSelectExpre();
            var q = from t in this.Db.GetIQueryable<HR_TermLeave>().AsExpandable()
                    join a in this.Db.GetIQueryable<HR_AskLeave>() on t.F_AskLeaveId equals a.F_Id into ask
                    from a in ask.DefaultIfEmpty()
                    select @select.Invoke(t, a);
            var where = LinqHelper.True<HR_TermLeaveDTO>();
            var search = input.Search;
            if (search.EmpId.IsNullOrEmpty())
            {
                q = default;
            }
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_TermLeaveDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            where = where.AndIf(!search.EmpId.IsNullOrEmpty(), x => x.F_UserId == search.EmpId);
            where = where.AndIf(search.wfState.HasValue, x => x.F_WFState == search.wfState);

            return await q.Where(where).GetPageResultAsync(input);
        }

        public int GetCount(string id)
        {

            Expression<Func<HR_TermLeave, HR_AskLeave, HR_TermLeaveDTO>> select = (t, a) => new HR_TermLeaveDTO
            {
                F_AskLeaveCode = a.F_AskLeaveCode,
                UserName = a.F_UserName,
            };
            select = select.BuildExtendSelectExpre();
            var q = from t in this.Db.GetIQueryable<HR_TermLeave>().AsExpandable()
                    join a in this.Db.GetIQueryable<HR_AskLeave>() on t.F_AskLeaveId equals a.F_Id into ask
                    from a in ask.DefaultIfEmpty()
                   
                    select @select.Invoke(t, a);
            var number =  q.Where(x => x.F_UserId == id).Count();
            return number;
        }
        /// <summary>
        /// 获得销假信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public HR_TermLeaveDTO GetTermLeave(string id)
        {
            var entity = this.GetIQueryable().FirstOrDefault(x => x.F_Id == id);
            HR_TermLeaveDTO model = null;
            if (entity != null)
            {
                model = _mapper.Map<HR_TermLeaveDTO>(entity);
                //model = new HR_TermLeaveDTO();
                //model.F_Id = entity.F_Id;
                //model.F_AskLeaveId = entity.F_AskLeaveId;
                //model.F_BusState = entity.F_BusState;
                //model.F_Code = entity.F_Code;
                //model.F_CompanyId = entity.F_CompanyId;
                //model.F_CreateDate = entity.F_CreateDate;
                //model.F_CreateUserId = entity.F_CreateUserId;
                //model.F_CreateUserName = entity.F_CreateUserName;
                //model.F_ModifyDate = entity.F_ModifyDate;
                //model.F_ModifyUserId = entity.F_ModifyUserId;
                //model.F_ModifyUserName = entity.F_ModifyUserName;
                //model.F_PeriodTime = entity.F_PeriodTime;
                //model.F_PeriodTime2 = entity.F_PeriodTime2;
                //model.F_RealAskLeaveTime = entity.F_RealAskLeaveTime;
                //model.F_RealEndTime = entity.F_RealEndTime;
                //model.F_RealStartTime = entity.F_RealStartTime;
                //model.F_Remark = entity.F_Remark;
                //model.F_UserId = entity.F_UserId;
                //model.F_WFId = entity.F_WFId;
                //model.F_WFState = entity.F_WFState;

                if (!model.F_AskLeaveId.IsNullOrEmpty())
                {
                    model.AskLeave = Db.GetIQueryable<HR_AskLeave>().FirstOrDefault(x => x.F_Id == model.F_AskLeaveId);
                    model.F_AskLeaveCode = model.AskLeave?.F_AskLeaveCode;
                    var emp = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == model.F_UserId);
                    model.UserName = emp?.NameUser;
                    if (emp != null)
                    {
                        var postion = Db.GetIQueryable<Base_Post>().FirstOrDefault(x => x.F_Id == emp.F_PositionId);
                        var com = Db.GetIQueryable<Base_Company>().FirstOrDefault(x => x.F_Id == emp.F_CompanyId);
                        var dept = Db.GetIQueryable<Base_Department>().FirstOrDefault(x => x.Id == emp.F_DepartmentId);
                        model.F_OrganizeInfo = $"{com?.F_FullName}_{dept?.Name}_{postion?.F_Name}";
                    }
                }
            }
            return model;
        }

        public async Task<HR_TermLeave> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_TermLeave data)
        {
            data.F_WFState = (int)WFStates.草稿;
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_TermLeave data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<AskLeaveConditionDTO> input)
        {
            Expression<Func<HR_TermLeave, HR_AskLeave, HR_TermLeaveDTO>> select = (t, a) => new HR_TermLeaveDTO
            {
                F_AskLeaveCode = a.F_AskLeaveCode,
                UserName = a.F_UserName,
            };
            select = select.BuildExtendSelectExpre();
            var q = from t in this.Db.GetIQueryable<HR_TermLeave>().AsExpandable()
                    join a in this.Db.GetIQueryable<HR_AskLeave>() on t.F_AskLeaveId equals a.F_Id into ask
                    from a in ask.DefaultIfEmpty()
                    select @select.Invoke(t, a);
            var where = LinqHelper.True<HR_TermLeaveDTO>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_TermLeaveDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            if (search.selectIds != null && search.selectIds.Count > 0)
            {
                where = where.And(x => search.selectIds.Contains(x.F_Id));
            }
            where = where.AndIf(!search.EmpId.IsNullOrEmpty(), x => x.F_UserId == search.EmpId);

            return q.Where(where).OrderBy(input.SortField, input.SortType).ToDataTable();
        }

        /// <summary>
        /// 调动流程回调方法
        /// </summary>
        /// <param name="input"></param>
        //[Transactional]
        public void FlowCallBack(FlowInputDTO input)
        {
            if (input == null)
            {
                throw new BusException("参数错误");
            }
            var entity = GetIQueryable().FirstOrDefault(x => x.F_Id == input.id);
            if (entity != null)
            {
                if (entity.F_WFState == (int)WFStates.取消流程)
                {
                    throw new BusException("该流程已取消，不能修改状态");
                }
                if (entity.F_WFState == (int)WFStates.完成流程)
                {
                    throw new BusException("该流程已完成，不能修改状态");
                }
                if (entity.F_WFState == (int)WFStates.提交生效)
                {
                    throw new BusException("该流程已提交生效，不能修改状态");
                }
                entity.F_WFState = input.status;
                this.Update(entity);
                if (input.status == (int)WFStates.完成流程)
                {
                    var ask = Db.GetIQueryable<HR_AskLeave>().FirstOrDefault(x => x.F_Id == entity.F_AskLeaveId);
                    if (ask != null)
                    {
                        if (ask.F_TermLeaveTime.HasValue)
                        {
                            ask.F_TermLeaveTime -= Convert.ToDouble(entity.F_RealAskLeaveTime);
                            if (ask.F_TermLeaveTime == 0)
                            {
                                ask.F_IsTerminate = 1;
                            }
                        }

                        var dataNow = DateTime.Now.Date;
                        //如果是法定假期则,取消流程后要加回额度
                        if (ask.F_AskLeaveType == "年假")
                        {

                            //修改假期额度
                            var hlEntity = Db.GetIQueryable<HR_HolidayLine>().Where(x => x.F_UserId == ask.F_UserId && x.F_EffectTime <= dataNow && x.F_EndTime >= dataNow && x.F_HolidayTypes.Contains("年假")).ToList();
                            if (hlEntity.Count() > 0)
                            {
                                decimal leaveTime = entity.F_RealAskLeaveTime.Value;
                                var legalH = hlEntity.FirstOrDefault(i => i.F_HolidayTypes == "法定年假");
                                var welfare = hlEntity.FirstOrDefault(i => i.F_HolidayTypes == "福利年假");
                                if (welfare == null)
                                {
                                    ask.F_StatutoryLeave = ask.F_StatutoryLeave - leaveTime;
                                    legalH.F_UsedLine -= leaveTime;
                                }
                                else
                                {
                                    var leH = welfare.F_UsedLine - leaveTime;
                                    if (leH >= 0)
                                    {
                                        welfare.F_UsedLine = welfare.F_UsedLine - leaveTime;
                                        ask.F_WelfareLeave = ask.F_WelfareLeave - leaveTime;
                                    }
                                    else
                                    {
                                        leaveTime = leaveTime - welfare.F_UsedLine.Value;
                                        welfare.F_UsedLine = 0;
                                        legalH.F_UsedLine = legalH.F_UsedLine - leaveTime;
                                        ask.F_StatutoryLeave = ask.F_StatutoryLeave - leaveTime;
                                        ask.F_WelfareLeave = 0;
                                    }
                                }
                                Db.Update(hlEntity);
                            }
                        }
                        else if (ask.F_AskLeaveType == "病假")
                        {
                            var hlEntity = Db.GetIQueryable<HR_HolidayLine>().FirstOrDefault(x => x.F_UserId == ask.F_UserId && x.F_EffectTime <= dataNow && x.F_EndTime >= dataNow && x.F_HolidayTypes.Contains("病假"));
                            decimal leaveTime = entity.F_RealAskLeaveTime.Value;
                            if (hlEntity != null)
                            {
                                if (hlEntity.F_ActualAmountLast + leaveTime >= hlEntity.F_ActualAmount)
                                {
                                    hlEntity.F_UsedLine = hlEntity.F_ActualAmountLast;
                                }
                                else
                                {
                                    hlEntity.F_UsedLine = hlEntity.F_UsedLine - leaveTime;
                                }
                                Db.Update(hlEntity);
                            }
                        }
                        Db.Update(ask);
                    }
                }
            }
        }

        public void Add(HR_TermLeave data)
        {
            data.F_WFState = (int)WFStates.草稿;
            Insert(data);

        }

        public void Edit(HR_TermLeave data)
        {
            Update(data);
        }
        //验证销假时间是否已销假
        public void ValidateTermDate(HR_TermLeave data)
        {
            int[] states = { 0, 4 };
            int num = this.GetIQueryable().Where(x => x.F_AskLeaveId == data.F_AskLeaveId && !states.Contains(x.F_WFState.Value) && ((x.F_RealEndTime >= data.F_RealEndTime && x.F_RealStartTime <= data.F_RealEndTime) || (x.F_RealEndTime >= data.F_RealStartTime && x.F_RealStartTime <= data.F_RealStartTime))).WhereIf(!data.F_Id.IsNullOrEmpty(), x => x.F_Id != data.F_Id).Count();
            if (num > 0)
            {
                throw new BusException("当前时间段已有销假，不能再销假");
            }
        }

        /// <summary>
        /// 创建流程
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <returns>是否创建成功</returns>
         //[Transactional]
        public bool CreateFlow(HR_TermLeave data, string url, IOperator op)
        {
            ValidateTermDate(data);
            var askModel = Db.GetIQueryable<HR_AskLeave>().FirstOrDefault(x => x.F_Id == data.F_AskLeaveId);
            if (Convert.ToDecimal(askModel.F_TermLeaveTime.Value) < data.F_RealAskLeaveTime.Value)
            {
                throw new BusException("不能大于可销假时长");
            }

            if (data.F_PeriodTime == 0)
            {
                data.F_RealStartTime = (data.F_RealStartTime.Value.ToString("yyyy-MM-dd") + " 09:00").ToDateTime();
            }
            else
            {
                data.F_RealStartTime = (data.F_RealStartTime.Value.ToString("yyyy-MM-dd") + " 13:00").ToDateTime();
            }
            if (data.F_PeriodTime2 == 0)
            {
                data.F_RealEndTime = (data.F_RealEndTime.Value.ToString("yyyy-MM-dd") + " 12:30").ToDateTime();
            }
            else
            {
                data.F_RealEndTime = (data.F_RealEndTime.Value.ToString("yyyy-MM-dd") + " 18:00").ToDateTime();
            }
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data, op);

                Add(data);
                //var ask = Db.GetIQueryable<HR_AskLeave>().FirstOrDefault(x => x.F_Id == data.F_AskLeaveId);
                //if(ask != null)
                //{
                //    ask.F_IsTerminate = 1; //已销假
                //    Db.Update(ask);
                //}
            }
            else
            {
                UpdateEntity(data, op);
                Edit(data);
            }
            bool ret = false;

            string token = string.Empty;
            Base_User user = null;
            var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
            ProjectDept projectDept = new ProjectDept();
            if (formalEmployees != null)
            {
                user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == formalEmployees.BaseUserId);
                token = JWTHelper.GetBusinessToken(data.F_Id, user != null ? user.UserName.Replace("@cqlandmark.com", "") : "");
                projectDept = _hR_FormalEmployeesBusiness.GetProjectDept(formalEmployees.F_Id);
            }
            //var user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == data.F_CreateUserId);
            //string token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            var userName = user != null ? user.UserName.Replace("@cqlandmark.com", "") : "";
            paramters.Add("reqCode", token);    //请求token
            paramters.Add("reqNo", data.F_Id);  //业务id
            paramters.Add("applyPerson", userName);   //请求人AD账号
            var companyName = projectDept?.company;
            var companys = new List<string> { "重庆怡置商业管理有限公司", "重庆外商服务有限公司-商业" };
            paramters.Add("formId", companyName != null && companys.Contains(companyName) ? _configuration["OASYFormId:TermLeaveFlow"] : _configuration["OAFormId:TermLeaveFlow"]);
            //paramters.Add("formId", _configuration["OAFormId:TermLeaveFlow"]);  //请求人AD账号
            Dictionary<string, object> hrData = new Dictionary<string, object>();
            hrData.Add("vacate_hr_id", askModel.F_AskLeaveCode);    //
            hrData.Add("leaveOff_start_date", data.F_RealStartTime.Value.ToString("yyyy-MM-dd")); //销假开始日期：格式“yyyy-MM-dd”
            hrData.Add("leaveOff_start_time", data.F_PeriodTime); //销假开始时间0:上午 09:00 ，1：下午 13:00
            hrData.Add("leaveOff_end_date", data.F_RealEndTime.Value.ToString("yyyy-MM-dd")); //销假结束日期：格式“yyyy-MM-dd”
            hrData.Add("leaveOff_end_time", data.F_PeriodTime2);  //销假结束时间0:上午 12:30 ，1：下午 18:00
            hrData.Add("leaveOff_day_count", data.F_RealAskLeaveTime.Value);  //销假时长
            hrData.Add("unit", "1");    //申请单位（0:小时，1：天）
            hrData.Add("remark", data.F_Remark);    //销假说明
            hrData.Add("files", string.Empty);
            hrData.Add("applyDate", data.F_CreateDate.ToString("yyyy-MM-dd"));
            hrData.Add("code", data.F_Code);
            hrData.Add("org", askModel.F_OrganizeInfo);
            hrData.Add("oaCompany", OAHelper.GetNuLL(projectDept?.company));
            hrData.Add("project", OAHelper.GetNuLL(projectDept?.project));
            hrData.Add("dept", OAHelper.GetNuLL(projectDept?.dept));
            paramters.Add("hrData", hrData);

            string action = "create";
            string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);

            var retObj = str.ToObject<Dictionary<string, string>>();
            if (retObj != null)
            {
                if (retObj["errCode"] == "0000")
                {
                    data.F_WFId = retObj["oaReqId"];
                    data.F_WFState = (int)WFStates.审核中;
                    Update(data);
                    ret = true;
                }
                else
                {
                    throw new BusException(retObj["errDesc"]);
                }
            }
            else
            {
                throw new BusException("创建流程失败");
            }

            return ret;
        }

        /// <summary>
        /// 提交、退回流程
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <param name="act">submit 提交  subnoback 提交不需回复   subback 提交需要回复  reject退回</param>
        /// <returns>是否成功</returns>
        //[Transactional]
        public bool ActWorkflow(HR_TermLeave data, string url, IOperator op, string act = "submit")
        {
            ValidateTermDate(data);
            if (data.F_PeriodTime == 0)
            {
                data.F_RealStartTime = (data.F_RealStartTime.Value.ToString("yyyy-MM-dd") + " 09:00").ToDateTime();
            }
            else
            {
                data.F_RealStartTime = (data.F_RealStartTime.Value.ToString("yyyy-MM-dd") + " 13:00").ToDateTime();
            }
            if (data.F_PeriodTime2 == 0)
            {
                data.F_RealEndTime = (data.F_RealEndTime.Value.ToString("yyyy-MM-dd") + " 12:30").ToDateTime();
            }
            else
            {
                data.F_RealEndTime = (data.F_RealEndTime.Value.ToString("yyyy-MM-dd") + " 18:00").ToDateTime();
            }
            if (!data.F_Id.IsNullOrEmpty())
            {
                //var old = GetEntity(data.F_Id);
                UpdateEntity(data, op);
                Edit(data);
                //if(old != null && old.F_AskLeaveId != data.F_AskLeaveId)
                //{
                //    var askOld = Db.GetIQueryable<HR_AskLeave>().FirstOrDefault(x => x.F_Id == old.F_AskLeaveId);
                //    if (askOld != null)
                //    {
                //        askOld.F_IsTerminate = 0; //未销假
                //        Db.Update(askOld);
                //    }
                //    var ask = Db.GetIQueryable<HR_AskLeave>().FirstOrDefault(x => x.F_Id == data.F_AskLeaveId);
                //    if (ask != null)
                //    {
                //        ask.F_IsTerminate = 1; //已销假
                //        Db.Update(ask);
                //    }
                //}
            }
            bool ret = false;
            string token = string.Empty;
            Base_User user = null;
            var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
            ProjectDept projectDept = new ProjectDept();
            if (formalEmployees != null)
            {
                user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == formalEmployees.BaseUserId);
                token = JWTHelper.GetBusinessToken(data.F_Id, user != null ? user.UserName.Replace("@cqlandmark.com", "") : "");
                projectDept = _hR_FormalEmployeesBusiness.GetProjectDept(formalEmployees.F_Id);
            }
            //var user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == data.F_CreateUserId);
            var model = this.GetEntity(data.F_Id);
            var askModel = Db.GetIQueryable<HR_AskLeave>().FirstOrDefault(x => x.F_Id == model.F_AskLeaveId);
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            var userName = user != null ? user.UserName.Replace("@cqlandmark.com", "") : "";
            paramters.Add("reqCode", token);
            paramters.Add("reqNo", data.F_Id);
            paramters.Add("applyPerson", userName);
            paramters.Add("sign", "同意");
            paramters.Add("act", "submit");
            var companyName = projectDept?.company;
            var companys = new List<string> { "重庆怡置商业管理有限公司", "重庆外商服务有限公司-商业" };
            paramters.Add("formId", companyName != null && companys.Contains(companyName) ? _configuration["OASYFormId:TermLeaveFlow"] : _configuration["OAFormId:TermLeaveFlow"]);
            //paramters.Add("formId", _configuration["OAFormId:TermLeaveFlow"]);
            paramters.Add("oaId", model.F_WFId);
            Dictionary<string, object> hrData = new Dictionary<string, object>();
            hrData.Add("vacate_hr_id", askModel.F_AskLeaveCode);
            hrData.Add("leaveOff_start_date", data.F_RealStartTime.Value.ToString("yyyy-MM-dd")); //销假开始日期：格式“yyyy-MM-dd”
            hrData.Add("leaveOff_start_time", data.F_PeriodTime); //销假开始时间0:上午 09:00 ，1：下午 13:00
            hrData.Add("leaveOff_end_date", data.F_RealEndTime.Value.ToString("yyyy-MM-dd")); //销假结束日期：格式“yyyy-MM-dd”
            hrData.Add("leaveOff_end_time", data.F_PeriodTime2);  //销假结束时间0:上午 12:30 ，1：下午 18:00
            hrData.Add("leaveOff_day_count", data.F_RealAskLeaveTime.Value);  //销假时长
            hrData.Add("unit", "1");
            hrData.Add("remark", data.F_Remark);
            hrData.Add("files", string.Empty);
            hrData.Add("applyDate", data.F_CreateDate.ToString("yyyy-MM-dd"));
            hrData.Add("code", data.F_Code);
            hrData.Add("project", OAHelper.GetNuLL(projectDept?.project));
            hrData.Add("oaCompany", OAHelper.GetNuLL(projectDept?.company));
            hrData.Add("dept", OAHelper.GetNuLL(projectDept?.dept));
            hrData.Add("org", askModel.F_OrganizeInfo);
            paramters.Add("hrData", hrData);

            string action = "actWorkflow";
            string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);

            var retObj = str.ToObject<Dictionary<string, string>>();
            if (retObj != null)
            {
                if (retObj["errCode"] == "0000")
                {
                    if (act == "submit")
                    {
                        data.F_WFId = retObj["oaReqId"];
                        data.F_WFState = (int)WFStates.审核中;
                        Update(data);
                    }
                    ret = true;
                }
                else
                {
                    throw new BusException(retObj["errDesc"]);
                }
            }
            else
            {
                throw new BusException("创建流程失败");
            }

            return ret;
        }

        /// <summary>
        /// 流程强制归档
        /// </summary>
        /// <param name="data"></param>
        /// <param name="url">接口地址</param>
        /// <returns>是否成功</returns>
        public bool ArchiveWorkflow(HR_TermLeave data, string url)
        {
            bool ret = false;

            string token = string.Empty;
            Base_User user = null;
            var formalEmployees = Db.GetIQueryable<HR_FormalEmployees>().FirstOrDefault(x => x.F_Id == data.F_UserId);
            if (formalEmployees != null)
            {
                user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == formalEmployees.BaseUserId);
                token = JWTHelper.GetBusinessToken(data.F_Id, user != null ? user.UserName.Replace("@cqlandmark.com", "") : "");
            }
            //var user = Db.GetIQueryable<Base_User>().FirstOrDefault(x => x.Id == data.F_CreateUserId);
            //string token = JWTHelper.GetBusinessToken(data.F_Id, user?.UserName);
            var model = this.GetEntity(data.F_Id);
            Dictionary<string, object> paramters = new Dictionary<string, object>();
            var userName = user != null ? user.UserName.Replace("@cqlandmark.com", "") : "";
            paramters.Add("reqCode", token);
            paramters.Add("reqNo", data.F_Id);
            paramters.Add("applyPerson", userName);
            paramters.Add("oaId", model.F_WFId);
            string action = "archiveWorkflow";
            string str = HttpHelper.PostData(url + action, paramters, null, ContentType.Json);

            var retObj = str.ToObject<Dictionary<string, string>>();
            if (retObj != null)
            {
                if (retObj["errCode"] == "0000")
                {
                    data.F_WFId = retObj["oaReqId"];
                    data.F_WFState = (int)WFStates.取消流程;
                    Update(data);
                    var ask = Db.GetIQueryable<HR_AskLeave>().FirstOrDefault(x => x.F_Id == data.F_AskLeaveId);
                    if (ask != null)
                    {
                        ask.F_IsTerminate = 0; //已销假
                        Db.Update(ask);
                    }

                    ret = true;
                }
                else
                {
                    throw new BusException(retObj["errDesc"]);
                }
            }
            else
            {
                throw new BusException("创建流程失败");
            }

            return ret;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}