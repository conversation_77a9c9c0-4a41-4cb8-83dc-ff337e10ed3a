﻿using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;

namespace Coldairarrow.Api.Controllers.HR_Manage
{
    [Route("/HR_Manage/[controller]/[action]")]
    public class HR_FeeUseDetailController : BaseApiController
    {
        #region DI

        public HR_FeeUseDetailController(IHR_FeeUseDetailBusiness hR_FeeUseDetailBus)
        {
            _hR_FeeUseDetailBus = hR_FeeUseDetailBus;
        }

        IHR_FeeUseDetailBusiness _hR_FeeUseDetailBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_FeeUseDetail>> GetDataList(PageInput<ProductConditinDto> input)
        {
            return await _hR_FeeUseDetailBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_FeeUseDetail> GetTheData(IdInputDTO input)
        {
            return await _hR_FeeUseDetailBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 外部接口
        /// <summary>
        /// 流程回调
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public async Task<AjaxResult> FlowCallBack(PointFlowInputDTO input)
        {
            await _hR_FeeUseDetailBus.FlowCallBackAsync(input);

            return Success();
        }
        #endregion

        #region 生成
        /// <summary>
        /// 生成员工费用额度
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task CreateUserFeePoint(TypeInputDTO input)
        {
            await _hR_FeeUseDetailBus.CreateUserFeePoint(input.itemCode);
        }
        #endregion

        #region 提交

 

        [HttpPost]
        public async Task SaveData(HR_FeeUseDetail data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_FeeUseDetailBus.AddDataAsync(data);
            }
            else
            {
                await _hR_FeeUseDetailBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_FeeUseDetailBus.DeleteDataAsync(ids);
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_FeeUseDetailBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<AjaxResult> UploadFileByForm()
        {
            var quarterStr = Request.Form["quarterStr"];
            if (string.IsNullOrEmpty(quarterStr))
            {
                throw new BusException("请选择所属季度");
            }
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return Error("上传文件不能为空");

            if (Request.Form.Files.Count > 1)
                return Error("只能上传一个文件");

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            var res = await _hR_FeeUseDetailBus.ImportSaveData(physicPath, this.GetOperator(), quarterStr);
            return res;
        }

        /// <summary>
        /// 模板下载
        /// </summary>
        [HttpPost]
        public FileContentResult DownloadTemplate()
        {
            string filePath = Directory.GetCurrentDirectory() + "/BusinessTemplate/员工通讯费额度模板.xls";
            if (FileHelper.Exists(filePath))
            {
                var bys = System.IO.File.ReadAllBytes(filePath);//excel表转换成流
                return File(bys, "application/vnd.ms-excel");
            }
            else
            {
                throw new BusException("找不到模板");
            }

        }
        #endregion
    }
}