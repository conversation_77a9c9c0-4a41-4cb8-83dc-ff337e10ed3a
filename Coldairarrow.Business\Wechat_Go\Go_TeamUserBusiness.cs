﻿using Coldairarrow.Entity.Wechat_Go;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Util.DataAccess;

namespace Coldairarrow.Business.Wechat_Go
{
    public class Go_TeamUserBusiness : BaseBusiness<Go_TeamUser>, IGo_TeamUserBusiness, ITransientDependency
    {
        public Go_TeamUserBusiness(IGoDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<Go_TeamUser>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Go_TeamUser>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Go_TeamUser, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Go_TeamUser> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public List<string> GetListByTeamId(string teamId)
        {
            var list = (from a in Db.GetIQueryable<Go_TeamUser>()
                        where a.F_TeamId == teamId
                        select a.F_Id).ToList();
            return list;

        }
        public Go_TeamUser GetDataByOpenId(string id)
        {
            var data = GetIQueryable().Where(x => x.F_OpenId == id).FirstOrDefault();
            return data;
        }

        public int? GetUserType(string openId, string teamId)
        {
            var type = GetIQueryable().Where(x => x.F_OpenId == openId && x.F_TeamId == teamId).Select(x => x.F_UserType).FirstOrDefault();
            return type;
        }

        public List<string> GetDataByMini(string openId,string teamId)
        {
            var ids = GetIQueryable().Where(x => x.F_OpenId == openId && x.F_TeamId == teamId).Select(x =>x.F_Id).ToList();
            return ids;
        }

        public async Task AddDataAsync(Go_TeamUser data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Go_TeamUser data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Go_TeamUser>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Go_TeamUser, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<Go_TeamUser, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}