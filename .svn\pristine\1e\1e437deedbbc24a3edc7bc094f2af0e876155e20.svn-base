﻿using Coldairarrow.Entity.HR_ReportFormsManage;
using Coldairarrow.Util;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
namespace Coldairarrow.Entity.HR_EmployeeInfoManage.Extensions
{
    public static class HR_FormalEmployeesExtensions
    {
        /// <summary>
        /// 解密AES
        /// </summary>
        /// <param name="client"></param>
        /// <returns></returns>
        public static  void DecryptFormal(this HR_FormalEmployees client)
        {
            client.MobilePhone = AESHelper.DecryptString(client.MobilePhone, AESHelper.AesKey);
            client.IdCardAddress = AESHelper.DecryptString(client.IdCardAddress, AESHelper.AesKey);
            client.IdCardNumber = AESHelper.DecryptString(client.IdCardNumber, AESHelper.AesKey);
            client.RegisteredResidence = AESHelper.DecryptString(client.RegisteredResidence, AESHelper.AesKey);
            client.LicenseInfo = AESHelper.DecryptString(client.LicenseInfo, AESHelper.AesKey);
            client.CurrentBankCard = AESHelper.DecryptString(client.CurrentBankCard, AESHelper.AesKey);
            client.OldBankCard = AESHelper.DecryptString(client.OldBankCard, AESHelper.AesKey);
            client.HomeAddress = AESHelper.DecryptString(client.HomeAddress, AESHelper.AesKey);
            client.EmergencyContact = AESHelper.DecryptString(client.EmergencyContact, AESHelper.AesKey);
            client.EmergencyContactNumber = AESHelper.DecryptString(client.EmergencyContactNumber, AESHelper.AesKey);
        }

        /// <summary>
        /// 加密AES
        /// </summary>
        /// <param name="client"></param>
        /// <returns></returns>
        public static void EncryptForm(this HR_FormalEmployees client)
        {
            client.MobilePhone = AESHelper.EncryptString(client.MobilePhone, AESHelper.AesKey);
            client.IdCardAddress = AESHelper.EncryptString(client.IdCardAddress, AESHelper.AesKey);
            client.IdCardNumber = AESHelper.EncryptString(client.IdCardNumber, AESHelper.AesKey);
            client.RegisteredResidence = AESHelper.EncryptString(client.RegisteredResidence, AESHelper.AesKey);
            client.LicenseInfo = AESHelper.EncryptString(client.LicenseInfo, AESHelper.AesKey);
            client.CurrentBankCard = AESHelper.EncryptString(client.CurrentBankCard, AESHelper.AesKey);
            client.OldBankCard = AESHelper.EncryptString(client.OldBankCard, AESHelper.AesKey);
            client.HomeAddress = AESHelper.EncryptString(client.HomeAddress, AESHelper.AesKey);
            client.EmergencyContact = AESHelper.EncryptString(client.EmergencyContact, AESHelper.AesKey);
            client.EmergencyContactNumber = AESHelper.EncryptString(client.EmergencyContactNumber, AESHelper.AesKey);
        }
        
    }
}
