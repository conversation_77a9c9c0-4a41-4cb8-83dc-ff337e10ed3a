﻿using Coldairarrow.Business.Wechat_CostDept;
using Coldairarrow.Entity.Wechat_CostDept;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Wechat_CostDept
{
    [Route("/Wechat_CostDept/[controller]/[action]")]
    public class Wechat_ManagerController : BaseApiController
    {
        #region DI

        public Wechat_ManagerController(IWechat_ManagerBusiness wechat_ManagerBus)
        {
            _wechat_ManagerBus = wechat_ManagerBus;
        }

        IWechat_ManagerBusiness _wechat_ManagerBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Wechat_Manager>> GetDataList(PageInput<ManagerInputDTO> input)
        {
            return await _wechat_ManagerBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Wechat_Manager> GetTheData(IdInputDTO input)
        {
            return await _wechat_ManagerBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Wechat_Manager data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _wechat_ManagerBus.AddDataAsync(data);
            }
            else
            {
                await _wechat_ManagerBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _wechat_ManagerBus.DeleteDataAsync(ids);
        }

        #endregion

        #region 二次开发
        //测试
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult testDB(int type)
        {
            try
            {
                var list = _wechat_ManagerBus.GetListByType(type);
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error("失败:" + ex.Message);
            }
        }
        //根据项目type查询列表
        [NoCheckJWT]
        [HttpGet]
        public AjaxResult GetListByType(int type)
        {
            try
            {
                var list = _wechat_ManagerBus.GetListByType(type);
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error("失败:"+ex.Message);
            }
        }
        #endregion
    }
}