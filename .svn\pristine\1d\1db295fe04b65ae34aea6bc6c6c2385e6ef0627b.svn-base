﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Coldairarrow.Entity.HR_DataDictionaryManage
{
    /// <summary>
    /// 数据字典明细
    /// </summary>
    [Table("HR_DataDictionaryDetails")]
    public class HR_DataDictionaryDetails
    {

        /// <summary>
        /// F_Id
        /// </summary>
        [Key, Column(Order = 1)]
        public String F_Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime F_CreateDate { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public String F_CreateUserId { get; set; }

        /// <summary>
        /// 创建人名
        /// </summary>
        public String F_CreateUserName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? F_ModifyDate { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public String F_ModifyUserId { get; set; }

        /// <summary>
        /// 修改人名
        /// </summary>
        public String F_ModifyUserName { get; set; }

        /// <summary>
        /// 流程Guid
        /// </summary>
        public String F_WFId { get; set; }

        /// <summary>
        /// 业务状态
        /// </summary>
        public Int32? F_BusState { get; set; }

        /// <summary>
        /// 流程状态
        /// </summary>
        public Int32? F_WFState { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public String Remark { get; set; }

        /// <summary>
        /// 分类主键
        /// </summary>
        public String F_ItemId { get; set; }

        /// <summary>
        /// 父级主键
        /// </summary>
        public String F_ParentId { get; set; }

        /// <summary>
        /// 编码
        /// </summary>
        public String F_ItemCode { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public String F_ItemName { get; set; }

        /// <summary>
        /// 值
        /// </summary>
        public String F_ItemValue { get; set; }
        /// <summary>
        /// 关联OA的id
        /// </summary>
        public String F_OAid { get; set; }
        
        /// <summary>
        /// 关联OA的值
        /// </summary>
        public String F_OAValue { get; set; }
        /// <summary>
        /// 快速查询
        /// </summary>
        public String F_QuickQuery { get; set; }

        /// <summary>
        /// 简拼
        /// </summary>
        public String F_SimpleSpelling { get; set; }

        /// <summary>
        /// 是否默认
        /// </summary>
        public Int32? F_IsDefault { get; set; }

        /// <summary>
        /// 排序码
        /// </summary>
        public Int32? F_SortCode { get; set; }

        /// <summary>
        /// 删除标记
        /// </summary>
        public Int32? F_DeleteMark { get; set; }

        /// <summary>
        /// 有效标志
        /// </summary>
        public Int32? F_EnabledMark { get; set; }

    }
}