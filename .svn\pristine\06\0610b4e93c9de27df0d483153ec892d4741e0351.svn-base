﻿using Coldairarrow.Entity.Wechat_CostDept;
using Coldairarrow.Util;
using Coldairarrow.Util.DTO;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Coldairarrow.Business.Wechat_CostDept
{
    public class Wechat_GuideBusiness : BaseBusiness<Wechat_Guide>, IWechat_GuideBusiness, ITransientDependency
    {
        public Wechat_GuideBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        //联表查询后的
        public async Task<PageResult<Wechat_GuideDTO>> GetDataListAsync_(PageInput<GuideInputDTO> input)
        {
            Expression<Func<Wechat_Guide, Wechat_Point, Wechat_GuideDTO>> select = (a, b) => new Wechat_GuideDTO
            {
                pointName = b.W_Name
            };
            var search = input.Search;
            // var q = GetIQueryable();
            select = select.BuildExtendSelectExpre();
            var q = from a in Db.GetIQueryable<Wechat_Guide>().AsExpandable()
                    join b in Db.GetIQueryable<Wechat_Point>() on a.W_PointId equals b.Id into ab
                    from b in ab.DefaultIfEmpty()
                    select @select.Invoke(a, b);

            var where = LinqHelper.True<Wechat_GuideDTO>();


            //筛选
            if (!search.W_Name.IsNullOrEmpty())
                where = where.And(x => x.W_Name.Contains(search.W_Name));
            if (!search.W_PointId.IsNullOrEmpty())
                where = where.And(x => x.W_PointId == search.W_PointId);

            return await q.Where(where).GetPageResultAsync(input);
        }

        //获取
        public async Task<PageResult<Wechat_Guide>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Wechat_Guide>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Wechat_Guide, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Wechat_Guide> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(Wechat_Guide data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Wechat_Guide data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public List<Wechat_Guide> GetListByPointId(string index)
        {
            var q = GetIQueryable();
            var user = q.Where(o => o.W_PointId == index).ToList();
            return user;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}