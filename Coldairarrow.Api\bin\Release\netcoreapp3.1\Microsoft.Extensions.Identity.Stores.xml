<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Identity.Stores</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Identity.IdentityRole">
            <summary>
            The default implementation of <see cref="T:Microsoft.AspNetCore.Identity.IdentityRole`1"/> which uses a string as the primary key.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityRole.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Identity.IdentityRole"/>.
            </summary>
            <remarks>
            The Id property is initialized to form a new GUID string value.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityRole.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Identity.IdentityRole"/>.
            </summary>
            <param name="roleName">The role name.</param>
            <remarks>
            The Id property is initialized to form a new GUID string value.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IdentityRole`1">
            <summary>
            Represents a role in the identity system
            </summary>
            <typeparam name="TKey">The type used for the primary key for the role.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityRole`1.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Identity.IdentityRole`1"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityRole`1.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Identity.IdentityRole`1"/>.
            </summary>
            <param name="roleName">The role name.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityRole`1.Id">
            <summary>
            Gets or sets the primary key for this role.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityRole`1.Name">
            <summary>
            Gets or sets the name for this role.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityRole`1.NormalizedName">
            <summary>
            Gets or sets the normalized name for this role.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityRole`1.ConcurrencyStamp">
            <summary>
            A random value that should change whenever a role is persisted to the store
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityRole`1.ToString">
            <summary>
            Returns the name of the role.
            </summary>
            <returns>The name of the role.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IdentityRoleClaim`1">
            <summary>
            Represents a claim that is granted to all users within a role.
            </summary>
            <typeparam name="TKey">The type of the primary key of the role associated with this claim.</typeparam>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityRoleClaim`1.Id">
            <summary>
            Gets or sets the identifier for this role claim.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityRoleClaim`1.RoleId">
            <summary>
            Gets or sets the of the primary key of the role associated with this claim.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityRoleClaim`1.ClaimType">
            <summary>
            Gets or sets the claim type for this claim.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityRoleClaim`1.ClaimValue">
            <summary>
            Gets or sets the claim value for this claim.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityRoleClaim`1.ToClaim">
            <summary>
            Constructs a new claim with the type and value.
            </summary>
            <returns>The <see cref="T:System.Security.Claims.Claim"/> that was produced.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityRoleClaim`1.InitializeFromClaim(System.Security.Claims.Claim)">
            <summary>
            Initializes by copying ClaimType and ClaimValue from the other claim.
            </summary>
            <param name="other">The claim to initialize from.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IdentityUser">
            <summary>
            The default implementation of <see cref="T:Microsoft.AspNetCore.Identity.IdentityUser`1"/> which uses a string as a primary key.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityUser.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Identity.IdentityUser"/>.
            </summary>
            <remarks>
            The Id property is initialized to form a new GUID string value.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityUser.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Identity.IdentityUser"/>.
            </summary>
            <param name="userName">The user name.</param>
            <remarks>
            The Id property is initialized to form a new GUID string value.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IdentityUser`1">
            <summary>
            Represents a user in the identity system
            </summary>
            <typeparam name="TKey">The type used for the primary key for the user.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityUser`1.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Identity.IdentityUser`1"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityUser`1.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Identity.IdentityUser`1"/>.
            </summary>
            <param name="userName">The user name.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.Id">
            <summary>
            Gets or sets the primary key for this user.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.UserName">
            <summary>
            Gets or sets the user name for this user.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.NormalizedUserName">
            <summary>
            Gets or sets the normalized user name for this user.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.Email">
            <summary>
            Gets or sets the email address for this user.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.NormalizedEmail">
            <summary>
            Gets or sets the normalized email address for this user.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.EmailConfirmed">
            <summary>
            Gets or sets a flag indicating if a user has confirmed their email address.
            </summary>
            <value>True if the email address has been confirmed, otherwise false.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.PasswordHash">
            <summary>
            Gets or sets a salted and hashed representation of the password for this user.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.SecurityStamp">
            <summary>
            A random value that must change whenever a users credentials change (password changed, login removed)
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.ConcurrencyStamp">
            <summary>
            A random value that must change whenever a user is persisted to the store
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.PhoneNumber">
            <summary>
            Gets or sets a telephone number for the user.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.PhoneNumberConfirmed">
            <summary>
            Gets or sets a flag indicating if a user has confirmed their telephone address.
            </summary>
            <value>True if the telephone number has been confirmed, otherwise false.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.TwoFactorEnabled">
            <summary>
            Gets or sets a flag indicating if two factor authentication is enabled for this user.
            </summary>
            <value>True if 2fa is enabled, otherwise false.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.LockoutEnd">
            <summary>
            Gets or sets the date and time, in UTC, when any user lockout ends.
            </summary>
            <remarks>
            A value in the past means the user is not locked out.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.LockoutEnabled">
            <summary>
            Gets or sets a flag indicating if the user could be locked out.
            </summary>
            <value>True if the user could be locked out, otherwise false.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUser`1.AccessFailedCount">
            <summary>
            Gets or sets the number of failed login attempts for the current user.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityUser`1.ToString">
            <summary>
            Returns the username for this user.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IdentityUserClaim`1">
            <summary>
            Represents a claim that a user possesses. 
            </summary>
            <typeparam name="TKey">The type used for the primary key for this user that possesses this claim.</typeparam>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUserClaim`1.Id">
            <summary>
            Gets or sets the identifier for this user claim.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUserClaim`1.UserId">
            <summary>
            Gets or sets the primary key of the user associated with this claim.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUserClaim`1.ClaimType">
            <summary>
            Gets or sets the claim type for this claim.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUserClaim`1.ClaimValue">
            <summary>
            Gets or sets the claim value for this claim.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityUserClaim`1.ToClaim">
            <summary>
            Converts the entity into a Claim instance.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.IdentityUserClaim`1.InitializeFromClaim(System.Security.Claims.Claim)">
            <summary>
            Reads the type and value from the Claim.
            </summary>
            <param name="claim"></param>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IdentityUserLogin`1">
            <summary>
            Represents a login and its associated provider for a user.
            </summary>
            <typeparam name="TKey">The type of the primary key of the user associated with this login.</typeparam>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUserLogin`1.LoginProvider">
            <summary>
            Gets or sets the login provider for the login (e.g. facebook, google)
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUserLogin`1.ProviderKey">
            <summary>
            Gets or sets the unique provider identifier for this login.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUserLogin`1.ProviderDisplayName">
            <summary>
            Gets or sets the friendly name used in a UI for this login.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUserLogin`1.UserId">
            <summary>
            Gets or sets the primary key of the user associated with this login.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IdentityUserRole`1">
            <summary>
            Represents the link between a user and a role.
            </summary>
            <typeparam name="TKey">The type of the primary key used for users and roles.</typeparam>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUserRole`1.UserId">
            <summary>
            Gets or sets the primary key of the user that is linked to a role.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUserRole`1.RoleId">
            <summary>
            Gets or sets the primary key of the role that is linked to the user.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.IdentityUserToken`1">
            <summary>
            Represents an authentication token for a user.
            </summary>
            <typeparam name="TKey">The type of the primary key used for users.</typeparam>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUserToken`1.UserId">
            <summary>
            Gets or sets the primary key of the user that the token belongs to.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUserToken`1.LoginProvider">
            <summary>
            Gets or sets the LoginProvider this token is from.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUserToken`1.Name">
            <summary>
            Gets or sets the name of the token.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.IdentityUserToken`1.Value">
            <summary>
            Gets or sets the token value.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.RoleStoreBase`4">
            <summary>
            Creates a new instance of a persistence store for roles.
            </summary>
            <typeparam name="TRole">The type of the class representing a role.</typeparam>
            <typeparam name="TKey">The type of the primary key for a role.</typeparam>
            <typeparam name="TUserRole">The type of the class representing a user role.</typeparam>
            <typeparam name="TRoleClaim">The type of the class representing a role claim.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.#ctor(Microsoft.AspNetCore.Identity.IdentityErrorDescriber)">
            <summary>
            Constructs a new instance of <see cref="T:Microsoft.AspNetCore.Identity.RoleStoreBase`4"/>.
            </summary>
            <param name="describer">The <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.RoleStoreBase`4.ErrorDescriber">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> for any error that occurred with the current operation.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.CreateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Creates a new role in a store as an asynchronous operation.
            </summary>
            <param name="role">The role to create in the store.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the asynchronous query.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.UpdateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Updates a role in a store as an asynchronous operation.
            </summary>
            <param name="role">The role to update in the store.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the asynchronous query.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.DeleteAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Deletes a role from the store as an asynchronous operation.
            </summary>
            <param name="role">The role to delete from the store.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the asynchronous query.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.GetRoleIdAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the ID for a role from the store as an asynchronous operation.
            </summary>
            <param name="role">The role whose ID should be returned.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that contains the ID of the role.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.GetRoleNameAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the name of a role from the store as an asynchronous operation.
            </summary>
            <param name="role">The role whose name should be returned.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that contains the name of the role.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.SetRoleNameAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the name of a role in the store as an asynchronous operation.
            </summary>
            <param name="role">The role whose name should be set.</param>
            <param name="roleName">The name of the role.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.ConvertIdFromString(System.String)">
            <summary>
            Converts the provided <paramref name="id"/> to a strongly typed key object.
            </summary>
            <param name="id">The id to convert.</param>
            <returns>An instance of <typeparamref name="TKey"/> representing the provided <paramref name="id"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.ConvertIdToString(`1)">
            <summary>
            Converts the provided <paramref name="id"/> to its string representation.
            </summary>
            <param name="id">The id to convert.</param>
            <returns>An <see cref="T:System.String"/> representation of the provided <paramref name="id"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.FindByIdAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Finds the role who has the specified ID as an asynchronous operation.
            </summary>
            <param name="id">The role ID to look for.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that result of the look up.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.FindByNameAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Finds the role who has the specified normalized name as an asynchronous operation.
            </summary>
            <param name="normalizedName">The normalized role name to look for.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that result of the look up.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.GetNormalizedRoleNameAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Get a role's normalized name as an asynchronous operation.
            </summary>
            <param name="role">The role whose normalized name should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that contains the name of the role.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.SetNormalizedRoleNameAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Set a role's normalized name as an asynchronous operation.
            </summary>
            <param name="role">The role whose normalized name should be set.</param>
            <param name="normalizedName">The normalized name to set</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.ThrowIfDisposed">
            <summary>
            Throws if this class has been disposed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.Dispose">
            <summary>
            Dispose the stores
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.GetClaimsAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Get the claims associated with the specified <paramref name="role"/> as an asynchronous operation.
            </summary>
            <param name="role">The role whose claims should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that contains the claims granted to a role.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.AddClaimAsync(`0,System.Security.Claims.Claim,System.Threading.CancellationToken)">
            <summary>
            Adds the <paramref name="claim"/> given to the specified <paramref name="role"/>.
            </summary>
            <param name="role">The role to add the claim to.</param>
            <param name="claim">The claim to add to the role.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.RemoveClaimAsync(`0,System.Security.Claims.Claim,System.Threading.CancellationToken)">
            <summary>
            Removes the <paramref name="claim"/> given from the specified <paramref name="role"/>.
            </summary>
            <param name="role">The role to remove the claim from.</param>
            <param name="claim">The claim to remove from the role.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.RoleStoreBase`4.Roles">
            <summary>
            A navigation property for the roles the store contains.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.RoleStoreBase`4.CreateRoleClaim(`0,System.Security.Claims.Claim)">
            <summary>
            Creates a entity representing a role claim.
            </summary>
            <param name="role">The associated role.</param>
            <param name="claim">The associated claim.</param>
            <returns>The role claim entity.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.UserStoreBase`5">
            <summary>
            Represents a new instance of a persistence store for the specified user type.
            </summary>
            <typeparam name="TUser">The type representing a user.</typeparam>
            <typeparam name="TKey">The type of the primary key for a user.</typeparam>
            <typeparam name="TUserClaim">The type representing a claim.</typeparam>
            <typeparam name="TUserLogin">The type representing a user external login.</typeparam>
            <typeparam name="TUserToken">The type representing a user token.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.#ctor(Microsoft.AspNetCore.Identity.IdentityErrorDescriber)">
            <summary>
            Creates a new instance.
            </summary>
            <param name="describer">The <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> used to describe store errors.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserStoreBase`5.ErrorDescriber">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> for any error that occurred with the current operation.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.CreateUserClaim(`0,System.Security.Claims.Claim)">
            <summary>
            Called to create a new instance of a <see cref="T:Microsoft.AspNetCore.Identity.IdentityUserClaim`1"/>.
            </summary>
            <param name="user">The associated user.</param>
            <param name="claim">The associated claim.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.CreateUserLogin(`0,Microsoft.AspNetCore.Identity.UserLoginInfo)">
            <summary>
            Called to create a new instance of a <see cref="T:Microsoft.AspNetCore.Identity.IdentityUserLogin`1"/>.
            </summary>
            <param name="user">The associated user.</param>
            <param name="login">The sasociated login.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.CreateUserToken(`0,System.String,System.String,System.String)">
            <summary>
            Called to create a new instance of a <see cref="T:Microsoft.AspNetCore.Identity.IdentityUserToken`1"/>.
            </summary>
            <param name="user">The associated user.</param>
            <param name="loginProvider">The associated login provider.</param>
            <param name="name">The name of the user token.</param>
            <param name="value">The value of the user token.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetUserIdAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the user identifier for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose identifier should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the identifier for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetUserNameAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the user name for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose name should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the name for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.SetUserNameAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the given <paramref name="userName" /> for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose name should be set.</param>
            <param name="userName">The user name to set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetNormalizedUserNameAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the normalized user name for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose normalized name should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the normalized user name for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.SetNormalizedUserNameAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the given normalized name for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose name should be set.</param>
            <param name="normalizedName">The normalized name to set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.CreateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Creates the specified <paramref name="user"/> in the user store.
            </summary>
            <param name="user">The user to create.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the creation operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.UpdateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Updates the specified <paramref name="user"/> in the user store.
            </summary>
            <param name="user">The user to update.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the update operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.DeleteAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Deletes the specified <paramref name="user"/> from the user store.
            </summary>
            <param name="user">The user to delete.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the <see cref="T:Microsoft.AspNetCore.Identity.IdentityResult"/> of the update operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.FindByIdAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Finds and returns a user, if any, who has the specified <paramref name="userId"/>.
            </summary>
            <param name="userId">The user ID to search for.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the user matching the specified <paramref name="userId"/> if it exists.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.ConvertIdFromString(System.String)">
            <summary>
            Converts the provided <paramref name="id"/> to a strongly typed key object.
            </summary>
            <param name="id">The id to convert.</param>
            <returns>An instance of <typeparamref name="TKey"/> representing the provided <paramref name="id"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.ConvertIdToString(`1)">
            <summary>
            Converts the provided <paramref name="id"/> to its string representation.
            </summary>
            <param name="id">The id to convert.</param>
            <returns>An <see cref="T:System.String"/> representation of the provided <paramref name="id"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.FindByNameAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Finds and returns a user, if any, who has the specified normalized user name.
            </summary>
            <param name="normalizedUserName">The normalized user name to search for.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the user matching the specified <paramref name="normalizedUserName"/> if it exists.
            </returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Identity.UserStoreBase`5.Users">
            <summary>
            A navigation property for the users the store contains.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.SetPasswordHashAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the password hash for a user.
            </summary>
            <param name="user">The user to set the password hash for.</param>
            <param name="passwordHash">The password hash to set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetPasswordHashAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the password hash for a user.
            </summary>
            <param name="user">The user to retrieve the password hash for.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that contains the password hash for the user.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.HasPasswordAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Returns a flag indicating if the specified user has a password.
            </summary>
            <param name="user">The user to retrieve the password hash for.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> containing a flag indicating if the specified user has a password. If the 
            user has a password the returned value with be true, otherwise it will be false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.FindUserAsync(`1,System.Threading.CancellationToken)">
            <summary>
            Return a user with the matching userId if it exists.
            </summary>
            <param name="userId">The user's id.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The user if it exists.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.FindUserLoginAsync(`1,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Return a user login with the matching userId, provider, providerKey if it exists.
            </summary>
            <param name="userId">The user's id.</param>
            <param name="loginProvider">The login provider name.</param>
            <param name="providerKey">The key provided by the <paramref name="loginProvider"/> to identify a user.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The user login if it exists.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.FindUserLoginAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Return a user login with  provider, providerKey if it exists.
            </summary>
            <param name="loginProvider">The login provider name.</param>
            <param name="providerKey">The key provided by the <paramref name="loginProvider"/> to identify a user.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The user login if it exists.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.ThrowIfDisposed">
            <summary>
            Throws if this class has been disposed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.Dispose">
            <summary>
            Dispose the store
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetClaimsAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Get the claims associated with the specified <paramref name="user"/> as an asynchronous operation.
            </summary>
            <param name="user">The user whose claims should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that contains the claims granted to a user.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.AddClaimsAsync(`0,System.Collections.Generic.IEnumerable{System.Security.Claims.Claim},System.Threading.CancellationToken)">
            <summary>
            Adds the <paramref name="claims"/> given to the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user to add the claim to.</param>
            <param name="claims">The claim to add to the user.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.ReplaceClaimAsync(`0,System.Security.Claims.Claim,System.Security.Claims.Claim,System.Threading.CancellationToken)">
            <summary>
            Replaces the <paramref name="claim"/> on the specified <paramref name="user"/>, with the <paramref name="newClaim"/>.
            </summary>
            <param name="user">The user to replace the claim on.</param>
            <param name="claim">The claim replace.</param>
            <param name="newClaim">The new claim replacing the <paramref name="claim"/>.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.RemoveClaimsAsync(`0,System.Collections.Generic.IEnumerable{System.Security.Claims.Claim},System.Threading.CancellationToken)">
            <summary>
            Removes the <paramref name="claims"/> given from the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user to remove the claims from.</param>
            <param name="claims">The claim to remove.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.AddLoginAsync(`0,Microsoft.AspNetCore.Identity.UserLoginInfo,System.Threading.CancellationToken)">
            <summary>
            Adds the <paramref name="login"/> given to the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user to add the login to.</param>
            <param name="login">The login to add to the user.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.RemoveLoginAsync(`0,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Removes the <paramref name="loginProvider"/> given from the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user to remove the login from.</param>
            <param name="loginProvider">The login to remove from the user.</param>
            <param name="providerKey">The key provided by the <paramref name="loginProvider"/> to identify a user.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetLoginsAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Retrieves the associated logins for the specified <param ref="user"/>.
            </summary>
            <param name="user">The user whose associated logins to retrieve.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> for the asynchronous operation, containing a list of <see cref="T:Microsoft.AspNetCore.Identity.UserLoginInfo"/> for the specified <paramref name="user"/>, if any.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.FindByLoginAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Retrieves the user associated with the specified login provider and login provider key..
            </summary>
            <param name="loginProvider">The login provider who provided the <paramref name="providerKey"/>.</param>
            <param name="providerKey">The key provided by the <paramref name="loginProvider"/> to identify a user.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> for the asynchronous operation, containing the user, if any which matched the specified login provider and key.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetEmailConfirmedAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets a flag indicating whether the email address for the specified <paramref name="user"/> has been verified, true if the email address is verified otherwise
            false.
            </summary>
            <param name="user">The user whose email confirmation status should be returned.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The task object containing the results of the asynchronous operation, a flag indicating whether the email address for the specified <paramref name="user"/>
            has been confirmed or not.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.SetEmailConfirmedAsync(`0,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Sets the flag indicating whether the specified <paramref name="user"/>'s email address has been confirmed or not.
            </summary>
            <param name="user">The user whose email confirmation status should be set.</param>
            <param name="confirmed">A flag indicating if the email address has been confirmed, true if the address is confirmed otherwise false.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.SetEmailAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the <paramref name="email"/> address for a <paramref name="user"/>.
            </summary>
            <param name="user">The user whose email should be set.</param>
            <param name="email">The email to set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetEmailAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the email address for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose email should be returned.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The task object containing the results of the asynchronous operation, the email address for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetNormalizedEmailAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Returns the normalized email for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose email address to retrieve.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The task object containing the results of the asynchronous lookup operation, the normalized email address if any associated with the specified user.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.SetNormalizedEmailAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the normalized email for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose email address to set.</param>
            <param name="normalizedEmail">The normalized email to set for the specified <paramref name="user"/>.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.FindByEmailAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets the user, if any, associated with the specified, normalized email address.
            </summary>
            <param name="normalizedEmail">The normalized email address to return the user for.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The task object containing the results of the asynchronous lookup operation, the user if any associated with the specified normalized email address.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetLockoutEndDateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the last <see cref="T:System.DateTimeOffset"/> a user's last lockout expired, if any.
            Any time in the past should be indicates a user is not locked out.
            </summary>
            <param name="user">The user whose lockout date should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task`1"/> that represents the result of the asynchronous query, a <see cref="T:System.DateTimeOffset"/> containing the last time
            a user's lockout expired, if any.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.SetLockoutEndDateAsync(`0,System.Nullable{System.DateTimeOffset},System.Threading.CancellationToken)">
            <summary>
            Locks out a user until the specified end date has passed. Setting a end date in the past immediately unlocks a user.
            </summary>
            <param name="user">The user whose lockout date should be set.</param>
            <param name="lockoutEnd">The <see cref="T:System.DateTimeOffset"/> after which the <paramref name="user"/>'s lockout should end.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.IncrementAccessFailedCountAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Records that a failed access has occurred, incrementing the failed access count.
            </summary>
            <param name="user">The user whose cancellation count should be incremented.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the incremented failed access count.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.ResetAccessFailedCountAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Resets a user's failed access count.
            </summary>
            <param name="user">The user whose failed access count should be reset.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
            <remarks>This is typically called after the account is successfully accessed.</remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetAccessFailedCountAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Retrieves the current failed access count for the specified <paramref name="user"/>..
            </summary>
            <param name="user">The user whose failed access count should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the failed access count.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetLockoutEnabledAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Retrieves a flag indicating whether user lockout can enabled for the specified user.
            </summary>
            <param name="user">The user whose ability to be locked out should be returned.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, true if a user can be locked out, otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.SetLockoutEnabledAsync(`0,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Set the flag indicating if the specified <paramref name="user"/> can be locked out..
            </summary>
            <param name="user">The user whose ability to be locked out should be set.</param>
            <param name="enabled">A flag indicating if lock out can be enabled for the specified <paramref name="user"/>.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.SetPhoneNumberAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the telephone number for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose telephone number should be set.</param>
            <param name="phoneNumber">The telephone number to set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetPhoneNumberAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets the telephone number, if any, for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose telephone number should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the user's telephone number, if any.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetPhoneNumberConfirmedAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Gets a flag indicating whether the specified <paramref name="user"/>'s telephone number has been confirmed.
            </summary>
            <param name="user">The user to return a flag for, indicating whether their telephone number is confirmed.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, returning true if the specified <paramref name="user"/> has a confirmed
            telephone number otherwise false.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.SetPhoneNumberConfirmedAsync(`0,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Sets a flag indicating if the specified <paramref name="user"/>'s phone number has been confirmed..
            </summary>
            <param name="user">The user whose telephone number confirmation status should be set.</param>
            <param name="confirmed">A flag indicating whether the user's telephone number has been confirmed.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.SetSecurityStampAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the provided security <paramref name="stamp"/> for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose security stamp should be set.</param>
            <param name="stamp">The security stamp to set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetSecurityStampAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Get the security stamp for the specified <paramref name="user" />.
            </summary>
            <param name="user">The user whose security stamp should be set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the security stamp for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.SetTwoFactorEnabledAsync(`0,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Sets a flag indicating whether the specified <paramref name="user"/> has two factor authentication enabled or not,
            as an asynchronous operation.
            </summary>
            <param name="user">The user whose two factor authentication enabled status should be set.</param>
            <param name="enabled">A flag indicating whether the specified <paramref name="user"/> has two factor authentication enabled.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetTwoFactorEnabledAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Returns a flag indicating whether the specified <paramref name="user"/> has two factor authentication enabled or not,
            as an asynchronous operation.
            </summary>
            <param name="user">The user whose two factor authentication enabled status should be set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing a flag indicating whether the specified 
            <paramref name="user"/> has two factor authentication enabled or not.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetUsersForClaimAsync(System.Security.Claims.Claim,System.Threading.CancellationToken)">
            <summary>
            Retrieves all users with the specified claim.
            </summary>
            <param name="claim">The claim whose users should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> contains a list of users, if any, that contain the specified claim. 
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.FindTokenAsync(`0,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Find a user token if it exists.
            </summary>
            <param name="user">The token owner.</param>
            <param name="loginProvider">The login provider for the token.</param>
            <param name="name">The name of the token.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The user token if it exists.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.AddUserTokenAsync(`4)">
            <summary>
            Add a new user token.
            </summary>
            <param name="token">The token to be added.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.RemoveUserTokenAsync(`4)">
            <summary>
            Remove a new user token.
            </summary>
            <param name="token">The token to be removed.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.SetTokenAsync(`0,System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the token value for a particular user.
            </summary>
            <param name="user">The user.</param>
            <param name="loginProvider">The authentication provider for the token.</param>
            <param name="name">The name of the token.</param>
            <param name="value">The value of the token.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.RemoveTokenAsync(`0,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Deletes a token for a user.
            </summary>
            <param name="user">The user.</param>
            <param name="loginProvider">The authentication provider for the token.</param>
            <param name="name">The name of the token.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetTokenAsync(`0,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Returns the token value.
            </summary>
            <param name="user">The user.</param>
            <param name="loginProvider">The authentication provider for the token.</param>
            <param name="name">The name of the token.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.SetAuthenticatorKeyAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Sets the authenticator key for the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user whose authenticator key should be set.</param>
            <param name="key">The authenticator key to set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.GetAuthenticatorKeyAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Get the authenticator key for the specified <paramref name="user" />.
            </summary>
            <param name="user">The user whose security stamp should be set.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation, containing the security stamp for the specified <paramref name="user"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.CountCodesAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Returns how many recovery code are still valid for a user.
            </summary>
            <param name="user">The user who owns the recovery code.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The number of valid recovery codes for the user..</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.ReplaceCodesAsync(`0,System.Collections.Generic.IEnumerable{System.String},System.Threading.CancellationToken)">
            <summary>
            Updates the recovery codes for the user while invalidating any previous recovery codes.
            </summary>
            <param name="user">The user to store new recovery codes for.</param>
            <param name="recoveryCodes">The new recovery codes for the user.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The new recovery codes for the user.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`5.RedeemCodeAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Returns whether a recovery code is valid for a user. Note: recovery codes are only valid
            once, and will be invalid after use.
            </summary>
            <param name="user">The user who owns the recovery code.</param>
            <param name="code">The recovery code to use.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>True if the recovery code was found for the user.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Identity.UserStoreBase`8">
            <summary>
            Represents a new instance of a persistence store for the specified user and role types.
            </summary>
            <typeparam name="TUser">The type representing a user.</typeparam>
            <typeparam name="TRole">The type representing a role.</typeparam>
            <typeparam name="TKey">The type of the primary key for a role.</typeparam>
            <typeparam name="TUserClaim">The type representing a claim.</typeparam>
            <typeparam name="TUserRole">The type representing a user role.</typeparam>
            <typeparam name="TUserLogin">The type representing a user external login.</typeparam>
            <typeparam name="TUserToken">The type representing a user token.</typeparam>
            <typeparam name="TRoleClaim">The type representing a role claim.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`8.#ctor(Microsoft.AspNetCore.Identity.IdentityErrorDescriber)">
            <summary>
            Creates a new instance.
            </summary>
            <param name="describer">The <see cref="T:Microsoft.AspNetCore.Identity.IdentityErrorDescriber"/> used to describe store errors.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`8.CreateUserRole(`0,`1)">
            <summary>
            Called to create a new instance of a <see cref="T:Microsoft.AspNetCore.Identity.IdentityUserRole`1"/>.
            </summary>
            <param name="user">The associated user.</param>
            <param name="role">The associated role.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`8.GetUsersInRoleAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Retrieves all users in the specified role.
            </summary>
            <param name="normalizedRoleName">The role whose users should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.Task"/> contains a list of users, if any, that are in the specified role. 
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`8.AddToRoleAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Adds the given <paramref name="normalizedRoleName"/> to the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user to add the role to.</param>
            <param name="normalizedRoleName">The role to add.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`8.RemoveFromRoleAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Removes the given <paramref name="normalizedRoleName"/> from the specified <paramref name="user"/>.
            </summary>
            <param name="user">The user to remove the role from.</param>
            <param name="normalizedRoleName">The role to remove.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`8.GetRolesAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Retrieves the roles the specified <paramref name="user"/> is a member of.
            </summary>
            <param name="user">The user whose roles should be retrieved.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that contains the roles the user is a member of.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`8.IsInRoleAsync(`0,System.String,System.Threading.CancellationToken)">
            <summary>
            Returns a flag indicating if the specified user is a member of the give <paramref name="normalizedRoleName"/>.
            </summary>
            <param name="user">The user whose role membership should be checked.</param>
            <param name="normalizedRoleName">The role to check membership of</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> containing a flag indicating if the specified user is a member of the given group. If the 
            user is a member of the group the returned value with be true, otherwise it will be false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`8.FindRoleAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Return a role with the normalized name if it exists.
            </summary>
            <param name="normalizedRoleName">The normalized role name.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The role if it exists.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Identity.UserStoreBase`8.FindUserRoleAsync(`2,`2,System.Threading.CancellationToken)">
            <summary>
            Return a user role for the userId and roleId if it exists.
            </summary>
            <param name="userId">The user's id.</param>
            <param name="roleId">The role's id.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
            <returns>The user role if it exists.</returns>
        </member>
    </members>
</doc>
