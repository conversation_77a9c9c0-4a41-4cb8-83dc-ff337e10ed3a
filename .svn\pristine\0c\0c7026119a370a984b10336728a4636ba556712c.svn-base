﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using static Coldairarrow.Entity.Shop_Manage.Enum.ShopEnum;

namespace Coldairarrow.Entity.Shop_Manage
{
    /// <summary>
    /// 商品图片表
    /// </summary>
    [Table("Z_ProductsDetail")]
    public class Z_ProductsDetail
    {

        /// <summary>
        /// 主键
        /// </summary>
        [Key, Column(Order = 1)]
        public String F_Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime F_CreateDate { get; set; }

        /// <summary>
        /// 创建人id
        /// </summary>
        public String F_CreateUserId { get; set; }

        /// <summary>
        /// 创建人名称
        /// </summary>
        public String F_CreateUserName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? F_ModifyDate { get; set; }

        /// <summary>
        /// 修改人id
        /// </summary>
        public String F_ModifyUserId { get; set; }

        /// <summary>
        /// 修改人名称
        /// </summary>
        public String F_ModifyUserName { get; set; }

        /// <summary>
        /// 流程id
        /// </summary>
        public String F_WFId { get; set; }

        /// <summary>
        /// 业务状态
        /// </summary>
        public Int32? F_BusState { get; set; }

        /// <summary>
        /// 流程状态
        /// </summary>
        public Int32? F_WFState { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public String F_Remark { get; set; }

        /// <summary>
        /// 商品id
        /// </summary>
        public String F_ProductId { get; set; }

        /// <summary>
        /// 商品图片
        /// </summary>
        public String F_Str { get; set; }

        /// <summary>
        /// 图片类型
        /// </summary>
        public DetailType F_Type { get; set; } = DetailType.封面;

    }
}