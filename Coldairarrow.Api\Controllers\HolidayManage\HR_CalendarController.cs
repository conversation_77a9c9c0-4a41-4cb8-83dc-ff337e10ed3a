﻿using Coldairarrow.Business.HolidayManage;
using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Newtonsoft.Json;
using System.Text;
using System.Net;
using System.Dynamic;
using System.Globalization;
using Newtonsoft.Json.Linq;

namespace Coldairarrow.Api.Controllers.HolidayManage
{
    [Route("/HolidayManage/[controller]/[action]")]
    public class HR_CalendarController : BaseApiController
    {
        #region DI

        public HR_CalendarController(IHR_CalendarBusiness hR_CalendarBus)
        {
            _hR_CalendarBus = hR_CalendarBus;
        }

        IHR_CalendarBusiness _hR_CalendarBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_Calendar>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_CalendarBus.GetDataListAsync(input);
        }
        [HttpPost]
        public string GetHolidayInfo(ConditionDTO input)
        {
            return _hR_CalendarBus.GetHolidayInfoAsync(input);
        }

        [HttpPost]
        public async Task<HR_Calendar> GetTheData(IdInputDTO input)
        {
            return await _hR_CalendarBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_Calendar data)
        {
            data.F_Year = data.F_StartTime.Value.Year;
            data.F_Month = data.F_StartTime.Value.Month;
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_CalendarBus.AddDataAsync(data);
            }
            else
            {
                await _hR_CalendarBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_CalendarBus.DeleteDataAsync(ids);
        }


        //public async Task SystemIGetData(SystemYearInput input)
        //{
        //    Dictionary<string, string> tradHolidays = new Dictionary<string, string>();
        //    tradHolidays.Add("正月初一", "春节");
        //    tradHolidays.Add("bmp", "清明节");
        //    tradHolidays.Add("五月初五", "端午节");
        //    tradHolidays.Add("八月十五", "中秋节");
        //    tradHolidays.Where(i => i.Key == "").Select(s => s.Value);
        //    Dictionary<string, string> holidaysSeason = new Dictionary<string, string>();
        //    holidaysSeason.Add("2020-01-01", "元旦");
        //    holidaysSeason.Add("2020-05-01", "劳动节");
        //    holidaysSeason.Add("2020-10-01", "国庆节");
        //    if (string.IsNullOrWhiteSpace(input.Year))
        //    {
        //        input.Year = DateTime.Now.Year.ToString();
        //    }
        //    List<HR_Calendar> calendars = new List<HR_Calendar>();
        //    string startTime = input.Year + "-1";
        //    WebClient client = new WebClient();
        //    client.Encoding = Encoding.UTF8;
        //    var url = $"https://sp0.baidu.com/8aQDcjqpAAV3otqbppnN2DJv/api.php?query={startTime}&resource_id=6018";
        //    var jsondata = client.DownloadString(url);
        //    DateTime newDate;
        //    try
        //    {
        //        var model = new Util.Calendar();

        //        model = JsonConvert.DeserializeObject<Util.Calendar>(jsondata);
        //        if (model.data[0].holiday == null)
        //        {
        //            throw new BusException("暂无获取到节假日信息");
        //        }

        //        foreach (var item in model.data)
        //        {
        //            foreach (var holiday in item.holiday)
        //            {
        //                foreach (var day in holiday.list)
        //                {
        //                    newDate = Convert.ToDateTime(day.date);
        //                    HR_Calendar calendar = new HR_Calendar()
        //                    {
        //                        F_StartTime = newDate,
        //                        F_Month = newDate.Month,
        //                        F_Year = newDate.Year,
        //                        F_Default = 1,
        //                        F_BusState = day.status,
        //                    };
        //                    InitEntity(calendar);
        //                    calendar.F_Code = GetChineseDateTime(newDate);
        //                    if (day.status == 1)
        //                    {
        //                        string dateStr = newDate.ToString("yyyy-MM-dd");
        //                        var holidayName = holidaysSeason.FirstOrDefault(i => i.Key == dateStr).Value;
        //                        if (holidayName != null)
        //                        {
        //                            calendar.F_Name = holidayName;
        //                        }

        //                        var holidayName2 = tradHolidays.FirstOrDefault(i => i.Key == calendar.F_Code).Value;
        //                        if (holidayName2 != null)
        //                        {
        //                            if (!string.IsNullOrEmpty(holidayName))
        //                            {
        //                                calendar.F_Name = holidayName + "," + holidayName2;
        //                            }
        //                            else
        //                            {
        //                                calendar.F_Name = holidayName2;
        //                            }
        //                        }
        //                        if (newDate.Month == 4)
        //                        {
        //                            if (calendars.Count(i => i.F_Name == "清明节") == 0)
        //                            {


        //                                var thisDate = input.Year + "04";
        //                                var urlee = $"http://www.easybots.cn/api/holiday.php?m=" + thisDate;
        //                                var jsondatass = client.DownloadString(urlee);
        //                                string result = JObject.Parse(jsondatass)[thisDate].ToString();

        //                                switch (newDate.Day)
        //                                {
        //                                    case 4:
        //                                        string result1 = JObject.Parse(result)["04"]?.ToString();
        //                                        if (result1 == "2")
        //                                        {
        //                                            calendar.F_Name = "清明节";
        //                                        }
        //                                        break;
        //                                    case 5:
        //                                        string result2 = JObject.Parse(result)["05"]?.ToString();
        //                                        if (result2 == "2")
        //                                        {
        //                                            calendar.F_Name = "清明节";
        //                                        }
        //                                        break;
        //                                    case 6:
        //                                        string result3 = JObject.Parse(result)["06"]?.ToString();
        //                                        if (result3 == "2")
        //                                        {
        //                                            calendar.F_Name = "清明节";
        //                                        }
        //                                        break;
        //                                    default:
        //                                        break;
        //                                }
        //                            }
        //                        }

        //                    }
        //                    if (calendars.Count(i => i.F_StartTime == newDate) == 0)
        //                    {
        //                        calendars.Add(calendar);
        //                    }
        //                }
        //            }
        //        }
        //        _hR_CalendarBus.DeleteYear(input.Year);
        //        await _hR_CalendarBus.SystemIGetDataAsync(calendars);
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new BusException(ex.Message);
        //    }
        //}
        public async Task SystemIGetData(SystemYearInput input)
        {
            Dictionary<string, string> tradHolidays = new Dictionary<string, string>();
            tradHolidays.Add("正月初一", "春节");
            tradHolidays.Add("bmp", "清明节");
            tradHolidays.Add("五月初五", "端午节");
            tradHolidays.Add("八月十五", "中秋节");
            tradHolidays.Where(i => i.Key == "").Select(s => s.Value);
            Dictionary<string, string> holidaysSeason = new Dictionary<string, string>();
            holidaysSeason.Add("2020-01-01", "元旦");
            holidaysSeason.Add("2020-05-01", "劳动节");
            holidaysSeason.Add("2020-10-01", "国庆节");
            var key = "30924b8a4ebb223cb7e35799cf323199";
            if (string.IsNullOrWhiteSpace(input.Year))
            {
                input.Year = DateTime.Now.Year.ToString();
            }
            HashSet<HR_Calendar> calendars = new HashSet<HR_Calendar>();

            WebClient client = new WebClient();
            client.Encoding = Encoding.UTF8;
            for (var i = 1; i <=12; i++)
            {
                string startTime = input.Year + "-" + i.ToString();
                var url = $"http://v.juhe.cn/calendar/month?year-month={startTime}&key={key}";
                var jsondata = client.DownloadString(url);
                DateTime newDate;
                try
                {
                    var model = new Util.NewCalendar();

                    model = JsonConvert.DeserializeObject<Util.NewCalendar>(jsondata);
                    if (!model.reason.Equals("Success"))
                    {
                        throw new BusException("获取节假日信息失败");
                    }
                    var holiday_list = model.result.data.holiday_array[1];
                    foreach (var item in holiday_list.list)
                    {
                        HR_Calendar calendar = new HR_Calendar()
                        {
                            F_StartTime = item.date.ToDateTime(),
                            F_Month = item.date.ToDateTime().Month,
                            F_Year = item.date.ToDateTime().Year,
                            F_Default = 1,
                            F_BusState = item.status,
                            F_Name = holiday_list.name
                        };
                        var old = calendars.FirstOrDefault(i => i.F_Year == calendar.F_Year && i.F_StartTime == calendar.F_StartTime);
                        if (old == null)
                            calendars.Add(calendar);
                    }
                }
                catch (Exception ex)
                {
                    //throw new BusException(ex.Message);
                    break;
                }
            }
            if (calendars.Count > 0)
            {
                _hR_CalendarBus.DeleteYear(input.Year);
                var hR_Calendars = calendars.ToList();
                hR_Calendars.ForEach(calendar => InitEntity(calendar));
                await _hR_CalendarBus.SystemIGetDataAsync(hR_Calendars);
            }

        }
        #endregion
        [HttpPost]
        public List<HR_Calendar> GetNowDataList(SystemYearInput input)
        {
            return _hR_CalendarBus.GetNowDataListAsync(input);
        }

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_CalendarBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<HR_Calendar>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _hR_CalendarBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion

        #region other



        //C# 获取农历日期

        ///<summary>
        /// 实例化一个 ChineseLunisolarCalendar
        ///</summary>
        private static ChineseLunisolarCalendar ChineseCalendar = new ChineseLunisolarCalendar();

        ///<summary>
        /// 十天干
        ///</summary>
        private static string[] tg = { "甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸" };

        ///<summary>
        /// 十二地支
        ///</summary>
        private static string[] dz = { "子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥" };

        ///<summary>
        /// 十二生肖
        ///</summary>
        private static string[] sx = { "鼠", "牛", "虎", "免", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪" };

        ///<summary>
        /// 返回农历天干地支年
        ///</summary>
        ///<param name="year">农历年</param>
        ///<return s></return s>
        public static string GetLunisolarYear(int year)
        {
            if (year > 3)
            {
                int tgIndex = (year - 4) % 10;
                int dzIndex = (year - 4) % 12;

                return string.Concat(tg[tgIndex], dz[dzIndex], "[", sx[dzIndex], "]");
            }

            throw new ArgumentOutOfRangeException("无效的年份!");
        }

        ///<summary>
        /// 农历月
        ///</summary>

        ///<return s></return s>
        private static string[] months = { "正", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "十二(腊)" };

        ///<summary>
        /// 农历日
        ///</summary>
        private static string[] days1 = { "初", "十", "廿", "三" };
        ///<summary>
        /// 农历日
        ///</summary>
        private static string[] days = { "一", "二", "三", "四", "五", "六", "七", "八", "九", "十" };


        ///<summary>
        /// 返回农历月
        ///</summary>
        ///<param name="month">月份</param>
        ///<return s></return s>
        public static string GetLunisolarMonth(int month)
        {
            if (month < 13 && month > 0)
            {
                return months[month - 1];
            }

            throw new ArgumentOutOfRangeException("无效的月份!");
        }

        ///<summary>
        /// 返回农历日
        ///</summary>
        ///<param name="day">天</param>
        ///<return s></return s>
        public static string GetLunisolarDay(int day)
        {
            if (day > 0 && day < 32)
            {
                if (day != 20 && day != 30)
                {
                    return string.Concat(days1[(day - 1) / 10], days[(day - 1) % 10]);
                }
                else
                {
                    return string.Concat(days[(day - 1) / 10], days1[1]);
                }
            }

            throw new ArgumentOutOfRangeException("无效的日!");
        }



        ///<summary>
        /// 根据公历获取农历日期
        ///</summary>
        ///<param name="datetime">公历日期</param>
        ///<return s></return s>
        public static string GetChineseDateTime(DateTime datetime)
        {
            int year = ChineseCalendar.GetYear(datetime);
            int month = ChineseCalendar.GetMonth(datetime);
            int day = ChineseCalendar.GetDayOfMonth(datetime);
            //获取闰月， 0 则表示没有闰月
            int leapMonth = ChineseCalendar.GetLeapMonth(year);

            bool isleap = false;

            if (leapMonth > 0)
            {
                if (leapMonth == month)
                {
                    //闰月
                    isleap = true;
                    month--;
                }
                else if (month > leapMonth)
                {
                    month--;
                }
            }

            //return string.Concat(GetLunisolarYear(year), "年", isleap ? "闰" : string.Empty, GetLunisolarMonth(month), "月", GetLunisolarDay(day));
            return string.Concat(GetLunisolarMonth(month), "月", GetLunisolarDay(day));
        }
        #endregion
    }
}