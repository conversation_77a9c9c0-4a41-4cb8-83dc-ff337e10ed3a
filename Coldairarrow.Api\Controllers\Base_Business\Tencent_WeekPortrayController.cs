﻿using Coldairarrow.Business.Base_Business;
using Coldairarrow.Entity.Base_Business;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.Base_Business
{
    [Route("/Base_Business/[controller]/[action]")]
    public class Tencent_WeekPortrayController : BaseApiController
    {
        #region DI

        public Tencent_WeekPortrayController(ITencent_WeekPortrayBusiness tencent_WeekPortrayBus)
        {
            _tencent_WeekPortrayBus = tencent_WeekPortrayBus;
        }

        ITencent_WeekPortrayBusiness _tencent_WeekPortrayBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Tencent_WeekPortray>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _tencent_WeekPortrayBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Tencent_WeekPortray> GetTheData(IdInputDTO input)
        {
            return await _tencent_WeekPortrayBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Tencent_WeekPortray data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _tencent_WeekPortrayBus.AddDataAsync(data);
            }
            else
            {
                await _tencent_WeekPortrayBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _tencent_WeekPortrayBus.DeleteDataAsync(ids);
        }

        #endregion
    }
}