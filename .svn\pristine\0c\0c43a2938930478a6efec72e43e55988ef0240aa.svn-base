﻿using Coldairarrow.Util;
using Org.BouncyCastle.Asn1.Ocsp;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
namespace Coldairarrow.Entity.Base_Manage
{
    /// <summary>
    /// 系统日志表
    /// </summary>
    [Table("Base_Log")]
    public class Base_Log
    {

        /// <summary>
        /// 自然主键
        /// </summary>
        [Key, Column(Order = 1)]
        public String Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        public Int32 Level { get; set; }

        /// <summary>
        /// 日志内容
        /// </summary>
        public String LogContent { get; set; }

        /// <summary>
        /// logUrl
        /// </summary>
        public String logUrl { get; set; }
        /// <summary>
        /// 操作人Id
        /// </summary>
        public String CreatorId { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        public String CreatorRealName { get; set; }
        /// <summary>
        /// 请求Ip地址
        /// </summary>
        public string IPAddress { get; set; } = IpHelper.GetIpAddress();
        /// <summary>
        /// 请求Json
        /// </summary>
        public string JsonContent { get; set; }
    }
}