﻿<template>
  <a-modal
    :title="title"
    width="40%"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="()=>{this.visible=false}"
  >
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="entity" :rules="rules" v-bind="layout">
        <a-form-model-item label="F_Id" prop="F_Id">
          <a-input v-model="entity.F_Id" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="创建时间" prop="F_CreateDate">
          <a-input v-model="entity.F_CreateDate" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="创建人" prop="F_CreateUserId">
          <a-input v-model="entity.F_CreateUserId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="创建人名" prop="F_CreateUserName">
          <a-input v-model="entity.F_CreateUserName" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="修改时间" prop="F_ModifyDate">
          <a-input v-model="entity.F_ModifyDate" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="修改人" prop="F_ModifyUserId">
          <a-input v-model="entity.F_ModifyUserId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="修改人名" prop="F_ModifyUserName">
          <a-input v-model="entity.F_ModifyUserName" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="流程Guid" prop="F_WFId">
          <a-input v-model="entity.F_WFId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="业务状态" prop="F_BusState">
          <a-input v-model="entity.F_BusState" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="流程状态" prop="F_WFState">
          <a-input v-model="entity.F_WFState" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="备注" prop="Remark">
          <a-input v-model="entity.Remark" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="姓名" prop="NameUser">
          <a-input v-model="entity.NameUser" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="头像" prop="HeadPortrait">
          <a-input v-model="entity.HeadPortrait" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="员工编码" prop="EmployeesCode">
          <a-input v-model="entity.EmployeesCode" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="身份证号码" prop="IdCardNumber">
          <a-input v-model="entity.IdCardNumber" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="护照号码" prop="PassportNo">
          <a-input v-model="entity.PassportNo" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="出生日期" prop="DirthDate">
          <a-input v-model="entity.DirthDate" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="用工关系状态" prop="EmployRelStatus">
          <a-input v-model="entity.EmployRelStatus" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="性别" prop="Sex">
          <a-input v-model="entity.Sex" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="身份证地址" prop="IdCardAddress">
          <a-input v-model="entity.IdCardAddress" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="类别归属" prop="ND">
          <a-input v-model="entity.ND" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="类别归属2" prop="ND2">
          <a-input v-model="entity.ND2" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="类别归属3" prop="ND3">
          <a-input v-model="entity.ND3" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="类别归属4" prop="ND4">
          <a-input v-model="entity.ND4" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="专业类别" prop="ProfessionalCategory">
          <a-input v-model="entity.ProfessionalCategory" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="国籍" prop="Nationality">
          <a-input v-model="entity.Nationality" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="籍贯" prop="NativePlace">
          <a-input v-model="entity.NativePlace" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="民族" prop="NationalInfo">
          <a-input v-model="entity.NationalInfo" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="户口类型" prop="AccountType">
          <a-input v-model="entity.AccountType" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="户口所在地" prop="RegisteredResidence">
          <a-input v-model="entity.RegisteredResidence" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="婚姻状况" prop="MaritalStatus">
          <a-input v-model="entity.MaritalStatus" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="政治面貌" prop="PoliticalLandscape">
          <a-input v-model="entity.PoliticalLandscape" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="生育状况" prop="FertilityStatus">
          <a-input v-model="entity.FertilityStatus" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="是否购车" prop="IsWhetherCar">
          <a-input v-model="entity.IsWhetherCar" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="车牌信息" prop="LicenseInfo">
          <a-input v-model="entity.LicenseInfo" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="现银行卡号" prop="CurrentBankCard">
          <a-input v-model="entity.CurrentBankCard" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="旧银行卡号" prop="OldBankCard">
          <a-input v-model="entity.OldBankCard" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="生效日期" prop="EffectiveDate">
          <a-input v-model="entity.EffectiveDate" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="手机号码" prop="MobilePhone">
          <a-input v-model="entity.MobilePhone" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="电子邮件" prop="Email">
          <a-input v-model="entity.Email" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="办公电话" prop="OfficePhone">
          <a-input v-model="entity.OfficePhone" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="家庭电话" prop="HomePhone">
          <a-input v-model="entity.HomePhone" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="家庭地址" prop="HomeAddress">
          <a-input v-model="entity.HomeAddress" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="紧急联系人" prop="EmergencyContact">
          <a-input v-model="entity.EmergencyContact" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="紧急联系电话" prop="EmergencyContactNumber">
          <a-input v-model="entity.EmergencyContactNumber" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="系统用户ID" prop="BaseUserId">
          <a-input v-model="entity.BaseUserId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="职务ID" prop="F_PositionId">
          <a-input v-model="entity.F_PositionId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="部门ID" prop="F_DepartmentId">
          <a-input v-model="entity.F_DepartmentId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="职级" prop="F_Rank">
          <a-input v-model="entity.F_Rank" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="公司ID" prop="F_CompanyId">
          <a-input v-model="entity.F_CompanyId" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="学历" prop="F_RecordFormalschooling">
          <a-input v-model="entity.F_RecordFormalschooling" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="身高" prop="F_Stature">
          <a-input v-model="entity.F_Stature" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="血型" prop="F_BloodType">
          <a-input v-model="entity.F_BloodType" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="健康状况" prop="F_HealthCondition">
          <a-input v-model="entity.F_HealthCondition" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="外语水平" prop="F_ForeignLevel">
          <a-input v-model="entity.F_ForeignLevel" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="职业资格" prop="F_ProfessionalQualification">
          <a-input v-model="entity.F_ProfessionalQualification" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="紧急联系人2" prop="EmergencyContact2">
          <a-input v-model="entity.EmergencyContact2" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="紧急联系电话2" prop="EmergencyContactNumber2">
          <a-input v-model="entity.EmergencyContactNumber2" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="不适宜从事岗位及原因" prop="F_NotSuitable">
          <a-input v-model="entity.F_NotSuitable" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="对工作的期望" prop="F_ExpectationWork">
          <a-input v-model="entity.F_ExpectationWork" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item label="过往是否发生过与操守相关问题，包括但不限于：提供虚假个人信息、涉及经济问题、以权谋私等" prop="F_HaveEthical">
          <a-input v-model="entity.F_HaveEthical" autocomplete="off" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      layout: {
        labelCol: { span: 5 },
        wrapperCol: { span: 18 }
      },
      visible: false,
      loading: false,
      entity: {},
      rules: {},
      title: ''
    }
  },
  methods: {
    init() {
      this.visible = true
      this.entity = {}
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    openForm(id, title) {
      this.init()

      if (id) {
        this.loading = true
        this.$http.post('/HR_RegistrManage/HR_RegistrEntry/GetTheData', { id: id }).then(resJson => {
          this.loading = false

          this.entity = resJson.Data
        })
      }
    },
    handleSubmit() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.loading = true
        this.$http.post('/HR_RegistrManage/HR_RegistrEntry/SaveData', this.entity).then(resJson => {
          this.loading = false

          if (resJson.Success) {
            this.$message.success('操作成功!')
            this.visible = false

            this.parentObj.getDataList()
          } else {
            this.$message.error(resJson.Msg)
          }
        })
      })
    }
  }
}
</script>
