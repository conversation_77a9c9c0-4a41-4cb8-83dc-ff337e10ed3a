﻿using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.HR_EmployeeInfoManage
{
    [Route("/HR_EmployeeInfoManage/[controller]/[action]")]
    public class HR_PerformanceInfoController : BaseApiController
    {
        #region DI

        public HR_PerformanceInfoController(IHR_PerformanceInfoBusiness hR_PerformanceInfoBus, IDistributedCache cache)
        {
            _hR_PerformanceInfoBus = hR_PerformanceInfoBus;
            _cache = cache;
        }

        private readonly IDistributedCache _cache;
        IHR_PerformanceInfoBusiness _hR_PerformanceInfoBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_PerformanceInfoDTO>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_PerformanceInfoBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_PerformanceInfo> GetTheData(IdInputDTO input)
        {
            return await _hR_PerformanceInfoBus.GetTheDataAsync(input.id);
        }

        [HttpPost]
        public AjaxResult ChangYear(ChangYear year)
        {
            string changYear = ""; 
            changYear = year.YearDate.AddYears(year.Year).ToString("yyyy-MM-dd");
            return Success(changYear);
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_PerformanceInfo data)
        {
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_PerformanceInfoBus.AddDataAsync(data);
            }
            else
            {
                await _hR_PerformanceInfoBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_PerformanceInfoBus.DeleteDataAsync(ids);
        }

        #endregion

        #region 导入数据

        /// <summary>
        /// 导入绩效
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return Error("上传文件不能为空");

            if (Request.Form.Files.Count > 1)
                return Error("只能上传一个文件");

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            var res = _hR_PerformanceInfoBus.ImportSaveData(physicPath, this.GetOperator());
            if (res.Data != null)
            {
                string id = GuidHelper.GenerateKey();
                _cache.SetObject(id, res.Data);

                AjaxResult<string> ajaxResult = new AjaxResult<string>();
                ajaxResult.Success = false;
                ajaxResult.Msg = "保存失败";
                ajaxResult.ErrorCode = 3;
                ajaxResult.Data = id;
                return ajaxResult;
            }
            return res;

        }
        #endregion

        #region 导出Excel
        /// <summary>
        /// 模板下载
        /// </summary>
        [HttpPost]
        public FileContentResult DownloadTemplate()
        {
            string filePath = Directory.GetCurrentDirectory() + "/BusinessTemplate/绩效模板.xls";
            if (FileHelper.Exists(filePath))
            {
                var bys = System.IO.File.ReadAllBytes(filePath);//excel表转换成流
                return File(bys, "application/vnd.ms-excel");
            }
            else
            {
                throw new System.Exception("找不到模板");
            }

        }
        /// <summary>
        /// 下载错误Excel
        /// </summary>
        [HttpPost]
        public FileContentResult DownloadErrorExcel(IdInputDTO input)
        {
            DataTable dt = _cache.GetObject<DataTable>(input.id);
            if (dt != null)
            {
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "错误清单";
                excelconfig.Title = "错误清单";
                excelconfig.IsAllSizeColumn = true;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                int sort = 0;
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "员工编码", ExcelColumn = "员工编码", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "姓名", ExcelColumn = "姓名", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "绩效考核年份", ExcelColumn = "绩效考核年份", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "绩效等级", ExcelColumn = "绩效等级", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "备注", ExcelColumn = "备注", Alignment = "left", Sort = sort++ });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "导入错误", ExcelColumn = "导入错误", Alignment = "left", Sort = sort++ });

                var t = ExcelHelper.ExportMemoryStream(dt, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/vnd.ms-excel");
                _cache.Remove(input.id);

                return file;
            }
            else
            {
                throw new BusException("找不到文件");
            }

        }
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_PerformanceInfoBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "员工绩效表.xlsx";
                excelconfig.Title = "员工绩效表";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "empcode", ExcelColumn = "员工编码", Alignment = "left", Sort = 1 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "empname", ExcelColumn = "姓名", Alignment = "left", Sort = 2 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "performanceappyear", ExcelColumn = "绩效考核年份", Alignment = "left", Sort = 3 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "performancerating", ExcelColumn = "绩效等级", Alignment = "left", Sort = 4 });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "remark", ExcelColumn = "备注", Alignment = "left", Sort = 5 });
                //ExcelHelper.ExcelDownload(exportTable, excelconfig);
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, true, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

    }
}