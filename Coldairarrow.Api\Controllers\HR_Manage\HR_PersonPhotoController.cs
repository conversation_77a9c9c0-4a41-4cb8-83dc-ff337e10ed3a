﻿using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Coldairarrow.Util.Excel;
using Coldairarrow.Business.Base_Manage;
using Coldairarrow.Business.HR_EmployeeInfoManage;

namespace Coldairarrow.Api.Controllers.HR_Manage
{
    [Route("/HR_Manage/[controller]/[action]")]
    public class HR_PersonPhotoController : BaseController
    {
        #region DI

        public HR_PersonPhotoController(IHR_PersonPhotoBusiness hR_PersonPhotoBus,
            IBase_UserBusiness userBus, IHR_FormalEmployeesBusiness hR_FormalEmployeesBus)
        {
            _hR_PersonPhotoBus = hR_PersonPhotoBus;
            _userBus = userBus;
            _hR_FormalEmployeesBus = hR_FormalEmployeesBus;
        }

        IHR_PersonPhotoBusiness _hR_PersonPhotoBus { get; }
        IBase_UserBusiness _userBus { get; }
        IHR_FormalEmployeesBusiness _hR_FormalEmployeesBus { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_PersonPhoto>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_PersonPhotoBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_PersonPhoto> GetTheData(IdInputDTO input)
        {
            return await _hR_PersonPhotoBus.GetTheDataAsync(input.id);
        }

        #endregion

        #region 提交
        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_PersonPhotoBus.DeleteDataAsync(ids);
        }

        #endregion


        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _hR_PersonPhotoBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");

                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<HR_PersonPhoto>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _hR_PersonPhotoBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion


        /// <summary>
        /// 微信小程序上传图片
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult SingleFileUpload()
        {
            var formFile = Request.Form.Files["file"];//获取请求发送过来的文件
            try
            {
                if (formFile != null)
                {
                    //获取文件后缀
                    var fileExtension = Path.GetExtension(formFile.FileName);//获取文件格式，拓展名
                    if ( fileExtension != ".jpg" && fileExtension != ".png" && fileExtension != ".jpeg" && fileExtension != ".gif")
                    {
                        return Error("只支持图片文件");
                    }
                    HR_PersonPhoto _PersonPhoto = new HR_PersonPhoto();
                    _PersonPhoto.FID = Guid.NewGuid().ToString();
                    _PersonPhoto.FPersonID = "";//标识符
                    _PersonPhoto.F_UserId = "";//标识符
                    _PersonPhoto.FisInFile = 0;
                    _PersonPhoto.FsourceImageHeight = 0;
                    _PersonPhoto.FsourceImageWidth = 0;
                    //大头像
                    _PersonPhoto.FImageData = FileUpload.StreamToBytes(formFile.OpenReadStream());
                    //小头像
                    _PersonPhoto.FImageDataSource = _PersonPhoto.FImageData;
                    //文件类型
                    _PersonPhoto.FimageContentType = formFile.ContentType;

                    _hR_PersonPhotoBus.AddDataAsync(_PersonPhoto);

                    return Success("上传成功");
                }
                else
                {
                    return Error("上传失败，未检测上传的文件信息~");
                }

            }
            catch (Exception ex)
            {
                return Error("文件保存失败，异常信息为：" + ex.Message);
            }
        }

        /// <summary>
        /// 文件上传
        /// </summary> 
        [HttpPost]
        public IActionResult UploadFile()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());
            HR_PersonPhoto _PersonPhoto = new HR_PersonPhoto();
            _PersonPhoto.FID = Guid.NewGuid().ToString();
            _PersonPhoto.FPersonID = "";
            _PersonPhoto.FisInFile = 0;
            _PersonPhoto.FsourceImageHeight = 0;
            _PersonPhoto.FsourceImageWidth = 0;
            //大头像
            _PersonPhoto.FImageData = FileUpload.StreamToBytes(file.OpenReadStream());
            //小头像
            _PersonPhoto.FImageDataSource = _PersonPhoto.FImageData;
            //文件类型
            _PersonPhoto.FimageContentType = file.ContentType;
            _hR_PersonPhotoBus.AddDataAsync(_PersonPhoto);
            var res = new
            {
                name = _PersonPhoto.FID,
                status = "done",
            };

            return JsonContent(res.ToJson());

        }
        /// <summary>
        /// 员工头像上传
        /// </summary> 
        [HttpPost]
        public IActionResult FormalUploadFile()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());
            HR_PersonPhoto _PersonPhoto = new HR_PersonPhoto();
            _PersonPhoto.FID = Guid.NewGuid().ToString();
            _PersonPhoto.FPersonID = "";
            _PersonPhoto.FisInFile = 0;
            _PersonPhoto.FsourceImageHeight = 0;
            _PersonPhoto.FsourceImageWidth = 0;
            //大头像
            _PersonPhoto.FImageData = FileUpload.StreamToBytes(file.OpenReadStream());
            //小头像
            _PersonPhoto.FImageDataSource = _PersonPhoto.FImageData;
            //文件类型
            _PersonPhoto.FimageContentType = file.ContentType;
            _hR_PersonPhotoBus.AddDataAsync(_PersonPhoto);
            var res = new
            {
                name = _PersonPhoto.FID,
                status = "done",
                data = File(_PersonPhoto.FImageDataSource, FileUpload.GetResponseContentType(_PersonPhoto.FimageContentType), _PersonPhoto.FID + "." + _PersonPhoto.FimageContentType)
            };

            return JsonContent(res.ToJson());

        }

        /// <summary>
        /// 获取文件
        /// </summary> 
        /// <param name="userId">员工唯一编号</param>
        /// <param name="isbig">是否大头像</param>
        /// <returns></returns>
        [HttpGet, NoApiPermission]
        public FileContentResult GetFile(string userId, bool? isbig)
        {
            if (!string.IsNullOrEmpty(userId))
            {
                var _PersonPhoto = _hR_PersonPhotoBus.GetTheDataByPersonIdAsync(userId).Result;
                if (_PersonPhoto != null)
                {
                    if (isbig != null && isbig.Value)
                        return File(_PersonPhoto.FImageDataSource, FileUpload.GetResponseContentType(_PersonPhoto.FimageContentType), _PersonPhoto.FID + "." + _PersonPhoto.FimageContentType);
                    else
                        return File(_PersonPhoto.FImageData, FileUpload.GetResponseContentType(_PersonPhoto.FimageContentType), _PersonPhoto.FID + "." + _PersonPhoto.FimageContentType);
                }
            }

            //打开默认图片
            FileStream fs = System.IO.File.OpenRead(".\\BusinessTemplate\\head.png"); //OpenRead
            byte[] byteData = new byte[fs.Length];
            fs.Read(byteData, 0, byteData.Length);
            fs.Close();
            return File(byteData, "application/x-png", "head.png");
        }
        /// <summary>
        /// 获取文件(通过账号)
        /// </summary> 
        /// <param name="userName">用户账号</param>
        /// <param name="isbig">是否大头像</param>
        /// <returns></returns>
        [HttpGet, NoApiPermission]
        public FileContentResult GetFileByUserName(string userName, bool? isbig)
        {
            if (!string.IsNullOrEmpty(userName))
            {
                //通过用户账号查询用户数据
                var base_User = _userBus.GetBase_UserByUserName(userName, 1);
                if (base_User != null)
                {
                    //通过baseUser去查询正式员工id
                    var formalEmployees = _hR_FormalEmployeesBus.GetTheDataByUserId(base_User.Id);
                    if (formalEmployees != null)
                    {
                        var _PersonPhoto = _hR_PersonPhotoBus.GetTheDataByPersonIdAsync(formalEmployees.F_Id).Result;
                        if (_PersonPhoto != null)
                        {
                            if (isbig != null && isbig.Value)
                                return File(_PersonPhoto.FImageDataSource, FileUpload.GetResponseContentType(_PersonPhoto.FimageContentType), _PersonPhoto.FID + "." + _PersonPhoto.FimageContentType);
                            else
                                return File(_PersonPhoto.FImageData, FileUpload.GetResponseContentType(_PersonPhoto.FimageContentType), _PersonPhoto.FID + "." + _PersonPhoto.FimageContentType);
                        }
                    }
                }
            }

            //打开默认图片
            FileStream fs = System.IO.File.OpenRead(".\\BusinessTemplate\\head.png"); //OpenRead
            byte[] byteData = new byte[fs.Length];
            fs.Read(byteData, 0, byteData.Length);
            fs.Close();
            return File(byteData, "application/x-png", "head.png");
        }
        /// <summary>
        /// 获取文件
        /// </summary> 
        /// <param name="userId">员工唯一编号</param>
        /// <param name="isbig">是否大头像</param>
        /// <returns></returns>
        [HttpPost, NoCheckJWT]
        public AjaxResult GetUserFile(FilData filData)
        {
            var Ish = false;
            if (!string.IsNullOrEmpty(filData.userId))
            {
                var _PersonPhoto = _hR_PersonPhotoBus.GetTheDataByPersonIdAsync(filData.userId).Result;
                if (_PersonPhoto != null)
                {
                    if (filData.isbig != null && filData.isbig.Value)
                    {
                        var jsonData = new
                        {
                            put = true,
                            data = File(_PersonPhoto.FImageDataSource, FileUpload.GetResponseContentType(_PersonPhoto.FimageContentType), _PersonPhoto.FID + "." + _PersonPhoto.FimageContentType)
                        };
                        return Success(jsonData);
                    }

                    else
                    {
                        var jsonData = new
                        {
                            put = true,
                            data = File(_PersonPhoto.FImageData, FileUpload.GetResponseContentType(_PersonPhoto.FimageContentType), _PersonPhoto.FID + "." + _PersonPhoto.FimageContentType)
                        };
                        return Success(jsonData);
                    }
                }
            }

            //打开默认图片
            FileStream fs = System.IO.File.OpenRead(".\\BusinessTemplate\\head.png"); //OpenRead
            byte[] byteData = new byte[fs.Length];
            fs.Read(byteData, 0, byteData.Length);
            fs.Close();
            var Data = new
            {
                put = false,
                data = File(byteData, "application/x-png", "head.png")
            };
            return Success(Data);
        }
        public class FilData
        {
            /// <summary>
            /// 员工id
            /// </summary>
            public string userId { get; set; }
            /// <summary>
            /// 是否大头像
            /// </summary>
            public bool? isbig { get; set; }
        }
    }
}