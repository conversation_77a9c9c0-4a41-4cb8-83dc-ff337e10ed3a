﻿using Coldairarrow.Entity.HR_RegistrManage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.HR_RegistrManage
{
    public class HR_RegistrWorkExpeBusiness : BaseBusiness<HR_RegistrWorkExpe>, IHR_RegistrWorkExpeBusiness, ITransientDependency
    {
        public HR_RegistrWorkExpeBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<HR_RegistrWorkExpe>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RegistrWorkExpe>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RegistrWorkExpe, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<HR_RegistrWorkExpe> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_RegistrWorkExpe data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(HR_RegistrWorkExpe data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_RegistrWorkExpe>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_RegistrWorkExpe, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_RegistrWorkExpe, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        public int AddData(HR_RegistrWorkExpe data)
        {
            return Insert(data);
        }

        public void AddListData(List<HR_RegistrWorkExpe> data)
        {
            BulkInsert(data);
        }

        public int UpdatListeData(List<HR_RegistrWorkExpe> data)
        {
            return Update(data);
        }

        public int UpdateData(HR_RegistrWorkExpe data)
        {
            return Update(data);
        }

        public int DeleteData(HR_RegistrWorkExpe data)
        {
            return Delete(data);
        }

        public int DeleteDataListeData(List<HR_RegistrWorkExpe> data)
        {
            return Delete(data);
        }

        public async Task AddDataListAsync(List<HR_RegistrWorkExpe> data)
        {
            await Task.Run(() => BulkInsert(data));
        }

        public async Task UpdateDataListAsync(List<HR_RegistrWorkExpe> data)
        {
            await UpdateAsync(data);
        }
        public async Task DeleteDataAsync(string id)
        {
            await DeleteAsync(this.GetIQueryable().Where(i => i.F_UserId == id).ToList());
        }
        #endregion

        #region 私有成员

        #endregion
    }
}