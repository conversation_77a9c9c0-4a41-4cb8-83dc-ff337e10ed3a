<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Http.Connections.Common</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Http.Connections.HttpTransports">
            <summary>
            Constants related to HTTP transports.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Http.Connections.HttpTransports.All">
            <summary>
            A bitmask combining all available <see cref="T:Microsoft.AspNetCore.Http.Connections.HttpTransportType"/> values.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Connections.HttpTransportType">
            <summary>
            Specifies transports that the client can use to send HTTP requests.
            </summary>
            <remarks>
            This enumeration has a <see cref="T:System.FlagsAttribute"/> attribute that allows a bitwise combination of its member values.
            </remarks>
        </member>
        <member name="F:Microsoft.AspNetCore.Http.Connections.HttpTransportType.None">
            <summary>
            Specifies that no transport is used.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Http.Connections.HttpTransportType.WebSockets">
            <summary>
            Specifies that the web sockets transport is used.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Http.Connections.HttpTransportType.ServerSentEvents">
            <summary>
            Specifies that the server sent events transport is used.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Http.Connections.HttpTransportType.LongPolling">
            <summary>
            Specifies that the long polling transport is used.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Connections.NegotiateProtocol.ParseResponse(System.IO.Stream)">
            <summary>
            <para>
                This method is obsolete and will be removed in a future version.
                The recommended alternative is <see cref="M:Microsoft.AspNetCore.Http.Connections.NegotiateProtocol.ParseResponse(System.ReadOnlySpan{System.Byte})" />.
            </para>
            </summary>
        </member>
    </members>
</doc>
