﻿using Coldairarrow.Entity.Wechat_Shekou;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Util.DataAccess;

namespace Coldairarrow.Business.Wechat_Shekou
{
    public class Shekou_CompanyUserBusiness : BaseBusiness<Shekou_CompanyUser>, IShekou_CompanyUserBusiness, ITransientDependency
    {
        public Shekou_CompanyUserBusiness(IBusinessDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<Shekou_CompanyUser>> GetDataListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Shekou_CompanyUser>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Shekou_CompanyUser, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }

        public async Task<Shekou_CompanyUser> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(Shekou_CompanyUser data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(Shekou_CompanyUser data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<Shekou_CompanyUser>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<Shekou_CompanyUser, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<Shekou_CompanyUser, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }

        public Shekou_CompanyUser getUserByMobile(string mobile)
        {
            var q = GetIQueryable().Where(x => x.Mobile == mobile).FirstOrDefault();
            return q;
        }

        public List<Shekou_CompanyUser> getUserByCall()
        {
            var q = GetIQueryable().Where(x => x.IsCall == 1).ToList();
            return q;
        }
        #endregion

        #region 私有成员

        #endregion
    }
}