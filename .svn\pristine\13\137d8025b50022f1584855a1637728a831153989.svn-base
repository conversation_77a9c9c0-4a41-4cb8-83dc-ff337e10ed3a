﻿using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.Base_Manage
{
    public interface IBase_IpLock_BakBusiness
    {
        Task<PageResult<Base_IpLock_Bak>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<Base_IpLock_Bak> GetTheDataAsync(string id);
        Task AddDataAsync(Base_IpLock_Bak data);
        /// <summary>
        /// 导入excel并保存数据
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        Task SaveDataByImprotExcel(string type);
        Task UpdateDataAsync(Base_IpLock_Bak data);
        Task DeleteDataAsync(List<string> ids);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
    }
}