﻿using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HR_ReportFormsManage;
using Coldairarrow.Util;
using Coldairarrow.Util.DataAccess;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using Coldairarrow.Entity;
using Coldairarrow.Entity.Plan_Manage;

namespace Coldairarrow.Business.HR_ReportFormsManage
{
    public class ERPBusiness :BaseBusiness<TargetStatisticTimed>, IERPBusiness, ITransientDependency
    {
        public ERPBusiness(IERPDbAccessor db)
         : base(db)
        {
        }

        public void getTargetStatistic()
        {
            throw new System.NotImplementedException();
        }
    }
}