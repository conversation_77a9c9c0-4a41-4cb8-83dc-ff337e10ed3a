﻿using Coldairarrow.Business.Wechat_Go;
using Coldairarrow.Entity.Wechat_Go;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;
using System.Linq;
using System.IO;
using System.Collections;
using Newtonsoft.Json;
using Coldairarrow.Util.Helper;

namespace Coldairarrow.Api.Controllers.Wechat_Go
{
    [Route("/Wechat_Go/[controller]/[action]")]
    public class Go_TeamController : BaseApiController
    {
        #region DI

        public Go_TeamController(IGo_TeamBusiness go_TeamBus,IGo_TeamUserBusiness go_TeamUserBus)
        {
            _go_TeamBus = go_TeamBus;
            _go_TeamUserBus = go_TeamUserBus;
        }

        IGo_TeamBusiness _go_TeamBus { get; }
        IGo_TeamUserBusiness _go_TeamUserBus { get; }
        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<Go_Team>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _go_TeamBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<Go_Team> GetTheData(IdInputDTO input)
        {
            return await _go_TeamBus.GetTheDataAsync(input.id);
        }
        /// <summary>
        /// 小程序，获取团队详情
        /// </summary>
        /// <returns></returns>
        [NoCheckJWT]
        [HttpPost]
        public AjaxResult GetData()
        {
            try
            {
                string Id = HttpContext.Request.Form["Id"].ToString();
                var data = _go_TeamBus.GetTheDataAsync(Id);
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        /// <summary>
        /// 获取团队列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public  AjaxResult GetHotList()
        {
            try
            {
                int hot = int.Parse(HttpContext.Request.Form["hot"].ToString());
                var list = _go_TeamBus.GetHotDataAsync(hot);
                return Success(list);
            }
            catch(Exception ex)
            {
                return Error(ex.ToString());
            }
        }

        /// <summary>
        /// 获取团队成员
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult GetTeamUser()
        {
            try
            {
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var list = _go_TeamBus.GetTeamUser(teamId);
                return Success(list);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        /// <summary>
        /// 获取我的团队，用于圈子
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult GetMyTeam()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                var data = _go_TeamBus.GetMyTeam(openId);
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        /// <summary>
        /// 获取某人的团队
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult GetUserTeam()
        {
            try
            {
                string openId = HttpContext.Request.Form["openId"].ToString();
                var data = _go_TeamBus.GetTeamByOpenId(openId);
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        /// <summary>
        /// 创建团队
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult CreartTeam()
        {
            try
            {
                string JsonString = HttpContext.Request.Form["info"].ToString();
                string openId  = HttpContext.Request.Form["openId"].ToString();
                var data = JsonConvert.DeserializeObject<Go_Team>(JsonString);
                var checkDescir = WechatGoMiniHelper.checkMsgSec(data.F_Describe);
                if (!checkDescir)
                {
                    return Error("介绍不符合微信规定");
                }
                if (data.Id.IsNullOrEmpty())
                {
                    var result = _go_TeamUserBus.GetDataByOpenId(openId);
                    if (result.IsNullOrEmpty())
                    {
                        data.F_IsAble = 1;
                        data.Id = Guid.NewGuid().ToString("N");
                        data.CreateTime = DateTime.Now;
                        data.CreatorId = openId;
                        data.F_Leader = openId;
                        _go_TeamBus.AddDataAsync(data).Wait();
                        //新增管理员
                        var user = new Go_TeamUser();
                        user.F_CreateTime = DateTime.Now;
                        user.F_Id = Guid.NewGuid().ToString("N");
                        user.F_OpenId = openId;
                        user.F_TeamId = data.Id;
                        user.F_IsAble = 1;
                        user.F_UserType = 2;
                        _go_TeamUserBus.AddDataAsync(user).Wait();
                        return Success("新建圈子成功");
                    }
                    else
                    {
                        return Success("您已创建过圈子");
                    }

                }
                else
                {
                    data.F_IsAble = 1;
                    data.CreatorId = openId;
                    data.F_Leader = openId;
                    _go_TeamBus.UpdateDataAsync(data).Wait();
                    return Success("修改圈子成功");
                }
               
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }

        /// <summary>
        /// 获取排行
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult GetUserRank()
        {
            try
            {
                string type = HttpContext.Request.Form["type"].ToString();
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var data = new List<Go_MiniUserDTO>();
                switch (type)
                {
                    case "today": data = _go_TeamBus.getDayUserRank(teamId, type); break;
                    case "day": data = _go_TeamBus.getDayUserRank(teamId,type); break;
                    case "week": data = _go_TeamBus.getWeekUserRank(teamId, type); break;
                    case "month": data = _go_TeamBus.getWeekUserRank(teamId, type); break;
                    case "lastmonth": data = _go_TeamBus.getWeekUserRank(teamId, type); break;
                    default: data = _go_TeamBus.getWeekUserRank(teamId, type); break;
                }

                return Success(data);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        /// <summary>
        /// 获取排行
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult GetTeamRank()
        {
            try
            {
                string type = HttpContext.Request.Form["type"].ToString();
                var data = new List<Go_TeamDTO>();
                switch (type)
                {
                    case "today": data = _go_TeamBus.getDayTeamRank(type); break;
                    case "day": data = _go_TeamBus.getDayTeamRank(type); break;
                    case "week": data = _go_TeamBus.getWeekTeamRank(type); break;
                    case "month": data = _go_TeamBus.getWeekTeamRank(type); break;
                    case "lastmonth": data = _go_TeamBus.getlastMonthTeamRank(type); break;
                    default: data = _go_TeamBus.getWeekTeamRank(type); break;
                }
              
                return Success(data);
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }

        /// <summary>
        /// 解散队伍
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult deleteTeam()
        {
            try
            {
                string teamId = HttpContext.Request.Form["teamId"].ToString();
                var list_ = new List<string>();
                list_.Add(teamId);
                var list = _go_TeamUserBus.GetListByTeamId(teamId);
                _go_TeamUserBus.DeleteDataAsync(list).Wait();
                _go_TeamBus.DeleteDataAsync(list_).Wait();
                return Success("删除成功");
            }
            catch (Exception ex)
            {
                return Error(ex.ToString());
            }
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(Go_Team data)
        {
            if (data.Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _go_TeamBus.AddDataAsync(data);
            }
            else
            {
                await _go_TeamBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _go_TeamBus.DeleteDataAsync(ids);
        }

        #endregion
        

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            { //取出数据源
                DataTable exportTable = _go_TeamBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_createusername", ExcelColumn = "创建人", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                
                return file;
            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["UserId"] = "用户";

            var list = new ExcelHelper<Go_Team>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _go_TeamBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion

    }
}