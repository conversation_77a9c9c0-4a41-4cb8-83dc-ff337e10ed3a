﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Coldairarrow.Entity.HR_RegistrManage
{
    /// <summary>
    /// 员工登记表(登记信息)
    /// </summary>
    [Table("HR_RegistrEntry")]
    public class HR_RegistrEntry
    {

        /// <summary>
        /// F_Id
        /// </summary>
        [Key, Column(Order = 1)]
        public String F_Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime F_CreateDate { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public String F_CreateUserId { get; set; }

        /// <summary>
        /// 创建人名
        /// </summary>
        public String F_CreateUserName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? F_ModifyDate { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public String F_ModifyUserId { get; set; }

        /// <summary>
        /// 修改人名
        /// </summary>
        public String F_ModifyUserName { get; set; }

        /// <summary>
        /// 流程Guid
        /// </summary>
        public String F_WFId { get; set; }

        /// <summary>
        /// 业务状态
        /// </summary>
        public Int32? F_BusState { get; set; }

        /// <summary>
        /// 流程状态
        /// </summary>
        public Int32? F_WFState { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public String Remark { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public String NameUser { get; set; }

        /// <summary>
        /// 头像
        /// </summary>
        public String HeadPortrait { get; set; }

        /// <summary>
        /// 员工编码
        /// </summary>
        public String EmployeesCode { get; set; }

        /// <summary>
        /// 身份证号码
        /// </summary>
        public String IdCardNumber { get; set; }

        /// <summary>
        /// 护照号码
        /// </summary>
        public String PassportNo { get; set; }

        /// <summary>
        /// 出生日期
        /// </summary>
        public DateTime? DirthDate { get; set; }

        /// <summary>
        /// 用工关系状态
        /// </summary>
        public String EmployRelStatus { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Int32? Sex { get; set; }

        /// <summary>
        /// 身份证地址
        /// </summary>
        public String IdCardAddress { get; set; }

        /// <summary>
        /// 类别归属
        /// </summary>
        public String ND { get; set; }

        /// <summary>
        /// 类别归属2
        /// </summary>
        public String ND2 { get; set; }

        /// <summary>
        /// 类别归属3
        /// </summary>
        public String ND3 { get; set; }

        /// <summary>
        /// 类别归属4
        /// </summary>
        public String ND4 { get; set; }

        /// <summary>
        /// 专业类别
        /// </summary>
        public String ProfessionalCategory { get; set; }

        /// <summary>
        /// 国籍
        /// </summary>
        public String Nationality { get; set; }

        /// <summary>
        /// 籍贯
        /// </summary>
        public String NativePlace { get; set; }

        /// <summary>
        /// 民族
        /// </summary>
        public String NationalInfo { get; set; }

        /// <summary>
        /// 户口类型
        /// </summary>
        public String AccountType { get; set; }

        /// <summary>
        /// 户口所在地
        /// </summary>
        public String RegisteredResidence { get; set; }

        /// <summary>
        /// 婚姻状况
        /// </summary>
        public Int32? MaritalStatus { get; set; }

        /// <summary>
        /// 政治面貌
        /// </summary>
        public String PoliticalLandscape { get; set; }

        /// <summary>
        /// 生育状况
        /// </summary>
        public String FertilityStatus { get; set; }

        /// <summary>
        /// 是否购车
        /// </summary>
        public Int32? IsWhetherCar { get; set; }

        /// <summary>
        /// 车牌信息
        /// </summary>
        public String LicenseInfo { get; set; }

        /// <summary>
        /// 现银行卡号
        /// </summary>
        public String CurrentBankCard { get; set; }

        /// <summary>
        /// 旧银行卡号
        /// </summary>
        public String OldBankCard { get; set; }

        /// <summary>
        /// 生效日期
        /// </summary>
        public DateTime? EffectiveDate { get; set; }

        /// <summary>
        /// 手机号码
        /// </summary>
        public String MobilePhone { get; set; }

        /// <summary>
        /// 电子邮件
        /// </summary>
        public String Email { get; set; }

        /// <summary>
        /// 办公电话
        /// </summary>
        public String OfficePhone { get; set; }

        /// <summary>
        /// 家庭电话
        /// </summary>
        public String HomePhone { get; set; }

        /// <summary>
        /// 家庭地址
        /// </summary>
        public String HomeAddress { get; set; }

        /// <summary>
        /// 紧急联系人
        /// </summary>
        public String EmergencyContact { get; set; }

        /// <summary>
        /// 紧急联系电话
        /// </summary>
        public String EmergencyContactNumber { get; set; }

        /// <summary>
        /// 系统用户ID
        /// </summary>
        public String BaseUserId { get; set; }

        /// <summary>
        /// 职务ID
        /// </summary>
        public String F_PositionId { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public String F_DepartmentId { get; set; }

        /// <summary>
        /// 职级
        /// </summary>
        public String F_Rank { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public String F_CompanyId { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public String F_CompanyName { get; set; }
        /// <summary>
        /// 学历
        /// </summary>
        public String F_RecordFormalschooling { get; set; }

        /// <summary>
        /// 身高
        /// </summary>
        public Decimal? F_Stature { get; set; }

        /// <summary>
        /// 血型
        /// </summary>
        public String F_BloodType { get; set; }

        /// <summary>
        /// 健康状况
        /// </summary>
        public String F_HealthCondition { get; set; }

        /// <summary>
        /// 外语水平
        /// </summary>
        public String F_ForeignLevel { get; set; }

        /// <summary>
        /// 职业资格
        /// </summary>
        public String F_ProfessionalQualification { get; set; }

        /// <summary>
        /// 紧急联系人2
        /// </summary>
        public String EmergencyContact2 { get; set; }

        /// <summary>
        /// 紧急联系电话2
        /// </summary>
        public String EmergencyContactNumber2 { get; set; }

        /// <summary>
        /// 不适宜从事岗位及原因
        /// </summary>
        public String F_NotSuitable { get; set; }

        /// <summary>
        /// 对工作的期望
        /// </summary>
        public String F_ExpectationWork { get; set; }

        /// <summary>
        /// 过往是否发生过与操守相关问题，包括但不限于：提供虚假个人信息、涉及经济问题、以权谋私等
        /// </summary>
        public String F_HaveEthical { get; set; }
        /// <summary>
        /// 是否有朋友或亲戚在我公司任职
        /// </summary>
        public int? F_FriendsWorking { get; set; }
        /// <summary>
        /// 开始工作时间
        /// </summary>
        public DateTime? F_StartWorkTime { get; set; }

        /// <summary>
        /// 应聘者ID
        /// </summary>
        public String F_UserId { get; set; }
        
    }
}