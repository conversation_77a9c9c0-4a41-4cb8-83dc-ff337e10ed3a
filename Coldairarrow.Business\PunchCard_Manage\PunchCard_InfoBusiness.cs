﻿using Coldairarrow.Entity.PunchCard_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using System;
using System.Linq.Expressions;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Business.HR_EmployeeInfoManage;
using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.Entity;
using Coldairarrow.Entity.HR_AttendanceManage;
using Coldairarrow.Util.Helper;

namespace Coldairarrow.Business.PunchCard_Manage
{
    public class PunchCard_InfoBusiness : BaseBusiness<PunchCard_Info>, IPunchCard_InfoBusiness, ITransientDependency
    {
        public PunchCard_InfoBusiness(IDbAccessor db)
            : base(db)
        {
        }

        #region 外部接口

        public async Task<PageResult<PunchCard_InfoDTO>> GetDataListAsync(PageInput<PunchCardModel> input)
        {
            var sqlStr = @"
                select P.*,F.NameUser as UserName
                from PunchCard_Info P
                LEFT JOIN[dbo].[HR_FormalEmployees] F ON P.F_UserId = F.F_Id
                where 1=1 
               ";
            var search = input.Search;
            if (!string.IsNullOrEmpty(search.userName))
            {
                sqlStr += " and F.NameUser like '%" + search.userName + "%' ";
            }
            var q = this.Db.GetListBySql<PunchCard_InfoDTO>(sqlStr).AsQueryable();
            var where = LinqHelper.True<PunchCard_InfoDTO>();
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<PunchCard_InfoDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            if (input.Search.F_Time.HasValue)
            {
                q = q.Where(x => x.F_Time.Value.ToString("yyyy-MM-dd") == input.Search.F_Time.Value.ToString("yyyy-MM-dd"));
            }
            if (!search.UserId.IsNullOrEmpty())
            {
                q = q.Where(x => x.F_UserId == search.UserId);
            }
            if (search.PuchCardType.HasValue)
            {
                //出勤记录
                if (search.PuchCardType == 1)
                {
                    q = q.Where(x => x.F_TimeType == TimeType.正常 || x.F_TimeType == TimeType.外勤);
                }
                ///迟到记录
                else if (search.PuchCardType == 2)
                {
                    q = q.Where(x => x.F_TimeType != TimeType.正常 && x.F_TimeType != TimeType.外勤);
                }

            }
            //获取本月的数据
            if (search.isMonth.HasValue && search.isMonth == YesOrNo.Yes)
            {
                q = q.Where(x => x.F_Time.HasValue && x.F_Time.Value.ToString("yyyy-MM") == DateTime.Now.ToString("yyyy-MM") && x.F_WFState.HasValue);
            }
            var data = q.Where(where);
            var list =await data
              .OrderBy($@"{input.SortField} {input.SortType}")
              .Skip((input.PageIndex - 1) * input.PageRows)
              .Take(input.PageRows)
              .ToListAsync();
            return new PageResult<PunchCard_InfoDTO> { Data = list, Total = data.Count() };
        }
        /// <summary>
        /// 查询今日打卡情况
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<List<PunchCard_Info>> GetToDayIsComDAsync(string userId)
        {
            return !userId.IsNullOrEmpty() ? this.GetIQueryable().ToList().Where(x => x.F_Time.Value.ToString("yyyy-MM-dd") == DateTime.Now.ToString("yyyy-MM-dd") && x.F_UserId == userId).ToList() : new List<PunchCard_Info>();
        }
        /// <summary>
        /// 获取考勤报表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PageResult<PunchCard_ReportDto>> GetPuchCardReport(PageInput<PunchCardModel> input)
        {
            if (input == null)
            {
                throw new BusException("参数错误");
            }
            if (input.Search.Month.IsNullOrEmpty())
            {
                throw new BusException("月份不能为空");
            }
            List<PunchCard_ReportDto> punchCard_Infos = new List<PunchCard_ReportDto>();
            string[] empStates = { "被动离职", "离职", "派驻终止", "主动离职" };
            var search = input.Search;
            PageResult<PunchCard_ReportDto> returnList = new PageResult<PunchCard_ReportDto>();

            Expression<Func<HR_FormalEmployees, Base_Department, Base_Post, HR_FormalEmployeesDetailsDTO>> select = (e, d, p) => new HR_FormalEmployeesDetailsDTO
            {
                DepartmentName = d.Name,
                PostName = p.F_Name
            };
            select = select.BuildExtendSelectExpre();
            //获取假期额度员工ID
            //var userIds = Db.GetIQueryable<HR_HolidayLine>().Where(x => x.F_EffectTime.Value.Year == search.Year.Value).Select(x => x.F_UserId).Distinct();

            var q = from e in Db.GetIQueryable<HR_FormalEmployees>().AsExpandable()
                    join de in Db.GetIQueryable<HR_Departure>() on e.F_Id equals de.F_UserId into depar
                    from de in depar.DefaultIfEmpty()
                    join d in Db.GetIQueryable<Base_Department>() on e.F_DepartmentId equals d.Id into dept
                    from d in dept.DefaultIfEmpty()
                    join p in Db.GetIQueryable<Base_Post>() on e.F_PositionId equals p.F_Id into post
                    from p in post.DefaultIfEmpty()
                    where (e.F_PositionId == "4a07a06cde1f4a28b33c94e3b7354375") && (!empStates.Contains(e.EmployRelStatus) || (empStates.Contains(e.EmployRelStatus) && de.F_TrueDepartureDate.HasValue && de.F_TrueDepartureDate.Value.AddMonths(3) >= DateTime.Now))
                    select @select.Invoke(e, d, p);
            var where = LinqHelper.True<HR_FormalEmployeesDetailsDTO>();
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_FormalEmployeesDetailsDTO, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }
            where = where.AndIf(!search.EmployRelStatus.IsNullOrEmpty(), x => x.EmployRelStatus.Contains(search.EmployRelStatus));

            var result = q.Where(where).GetPageResult(input);

            returnList.ErrorCode = result.ErrorCode;
            returnList.Msg = result.Msg;
            returnList.Success = result.Success;
            returnList.Total = result.Total;
            returnList.Data = new List<PunchCard_ReportDto>();
            //获取选择月的每一天
            var dayTimes = TimeStampsHelper.GetDayByMonth(DateTime.Parse(search.Month + "-01"));
            if (result.Data != null && result.Data.Count > 0)
            {
                var empIds = result.Data.Select(x => x.F_Id);
                //获取请假数据
                var askList = Db.GetIQueryable<HR_AskLeave>().ToList().Where(x => empIds.Contains(x.F_UserId) && x.F_WFState == (int)WFStates.完成流程 && x.F_StartTime.Value.ToString("yyyy-MM") == search.Month).ToList();
                var askIds = askList.Select(x => x.F_Id);
                //获取销假数据
                var terList = Db.GetIQueryable<HR_TermLeave>().Where(x => askIds.Contains(x.F_AskLeaveId) && x.F_WFState == (int)WFStates.完成流程).ToList();
                //获取排班上班数据
                var scheduList = Db.GetIQueryable<HR_Scheduling>().Where(x => empIds.Contains(x.F_UserId) && !x.F_WorkState.Contains("休") && !x.F_WorkState.Contains("假") && x.F_Year == search.Year.Value).ToList();
                //获取加班数据
                var workOvertimeList = Db.GetIQueryable<HR_WorkOvertime>().Where(x => empIds.Contains(x.F_UserId) && x.F_WFState == (int)WFStates.完成流程 && x.F_WorkODate.Value.Year == search.Year.Value).ToList();
                //获取日历
                var calendarList = Db.GetIQueryable<HR_Calendar>().Where(x => x.F_Year == search.Year).ToList();
                //法定假天数
                int fdDay = calendarList.Where(x => x.F_StartTime.HasValue && x.F_StartTime.Value.DayOfWeek != DayOfWeek.Sunday && x.F_StartTime.Value.DayOfWeek != DayOfWeek.Saturday && x.F_BusState == 1).Count() -
                    calendarList.Where(x => x.F_StartTime.HasValue && (x.F_StartTime.Value.DayOfWeek == DayOfWeek.Sunday || x.F_StartTime.Value.DayOfWeek == DayOfWeek.Saturday)
                    && x.F_BusState == 2).Count();
                //获取小程序打卡
                var months = GetIQueryable().ToList().Select(x => x.F_Time.Value.ToString("yyyy-MM")).ToList();
                var wxPunchCards = GetIQueryable().ToList().Where(x => x.F_Time.Value.ToString("yyyy-MM") == search.Month).ToList();
                //应出勤天数=年历天数－周六、日天数－法定假天数
                DateTime dec31 = new DateTime(search.Year.Value, 12, 31);
                //年历天数
                int daysInYear = dec31.DayOfYear;
                //周六、日天数
                int wmDay = GetWMDay(search.Year.Value);

                foreach (var item in result.Data)
                {
                    if (item.NameUser == "袁康")
                    {
                        Console.WriteLine("111");
                    }
                    //应出勤天数
                    decimal DaysOnDuty = scheduList.Count();
                    if (DaysOnDuty == 0)
                    {
                        DaysOnDuty = daysInYear - wmDay - fdDay;
                    }
                    //加班天数
                    decimal workDay = Math.Round(workOvertimeList.Where(x => x.F_UserId == item.F_Id).Sum(x => x.F_ActualWorkOTime ?? 0) / 8m, 1);
                    //实际出勤天数 排班天数+加班天数-（请假天数-销假天数)
                    decimal ActualAttendanceDays = DaysOnDuty + workDay - (Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id).Sum(x => x.F_AskLeaveTime ?? 0)) - terList.Where(x => x.F_UserId == item.F_Id).Sum(x => x.F_RealAskLeaveTime ?? 0));
                    decimal AskLeaveDay = (Convert.ToDecimal(askList.Where(x => x.F_UserId == item.F_Id).Sum(x => x.F_AskLeaveTime ?? 0)) - terList.Where(x => x.F_UserId == item.F_Id).Sum(x => x.F_RealAskLeaveTime ?? 0));
                    //用户信息
                    PunchCard_ReportDto model = new PunchCard_ReportDto()
                    {
                        userName = item.NameUser,
                        depName = item.DepartmentName,
                        postName = item.PostName,
                        userEmployRelStatus = item.EmployRelStatus,
                        DaysOnDuty = dayTimes.Count,
                        ActualAttendanceDays = ActualAttendanceDays,
                        AskLeaveDay = AskLeaveDay,
                        timeReports = new List<TimeReport>()
                    };
                    List<TimeReport> timeReports = new List<TimeReport>();

                    var userWxPunchCard = wxPunchCards.Where(x => x.F_UserId == item.F_Id).ToList();
                    var userAskList = askList.ToList().Where(i => i.F_UserId == item.F_Id && (i.F_StartTime.Value.ToString("yyyy-MM") == search.Month || i.F_EndTime.Value.ToString("yyyy-MM") == search.Month));
                    //循环每天数据
                    dayTimes.ForEach(dt =>
                    {
                        TimeReport timeReport = new TimeReport()
                        {
                            day = dt,
                        };
                        //判断是否请假
                        var hR_AskLeaves = userAskList.Where(i => i.F_StartTime.Value >= dt && dt <= i.F_EndTime);
                        //查询当前的打卡数据
                        if (hR_AskLeaves.Count() == 0)
                        {
                            var todayPunch = userWxPunchCard.Where(x => x.F_Time.Value.ToString("yyyy-MM-dd") == dt.ToString("yyyy-MM-dd"));
                            if (todayPunch.Count() == 0)
                            {
                                timeReport.isDutyPunch = EnumHelper.GetEnumDescription(punchCardType.未打卡);
                                timeReport.isOffDutyPunch = EnumHelper.GetEnumDescription(punchCardType.未打卡);
                            }
                            else
                            {
                                var dutyTimeType = todayPunch.FirstOrDefault(x => x.F_Type == CardType.上班打卡)?.F_TimeType;
                                timeReport.isDutyPunch = dutyTimeType.HasValue ? EnumHelper.GetEnumDescription(dutyTimeType.Value) : "未打卡";
                                if (todayPunch.FirstOrDefault(x => x.F_Type == CardType.下班打卡) != null)
                                {
                                    var offcard = todayPunch.FirstOrDefault(x => x.F_Type == CardType.下班打卡);
                                    timeReport.isOffDutyPunch = offcard.F_TimeType.HasValue ? EnumHelper.GetEnumDescription(offcard.F_TimeType) : "未打卡";
                                }
                                else
                                {
                                    timeReport.isOffDutyPunch = EnumHelper.GetEnumDescription(punchCardType.未打卡);
                                }
                            }
                        }
                        else
                        {
                            timeReport.isDutyPunch = EnumHelper.GetEnumDescription(punchCardType.请假);
                            timeReport.isOffDutyPunch = EnumHelper.GetEnumDescription(punchCardType.请假);

                        }
                        timeReports.Add(timeReport);
                    });
                    model.ActualAttendanceDays = timeReports.Where(i => i.isDutyPunch != EnumHelper.GetEnumDescription(punchCardType.未打卡) && i.isOffDutyPunch != EnumHelper.GetEnumDescription(punchCardType.未打卡)).GroupBy(x => x.day).Count(i => i.Key != null);
                    model.timeReports = timeReports;
                    returnList.Data.Add(model);
                }
            }

            return returnList;
        }
        /// <summary>
        /// 获取本月的每天
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public List<DateTime> GetDayByMonth(DateTime date)
        {
            //当月第一天0时0分0秒：

            var startTime = DateTime.Now.AddDays(1 - DateTime.Now.Day).Date;
            List<DateTime> dateTimes = new List<DateTime>();
            //当月最后一天23时59分59秒：
            var endTime = DateTime.Now.AddDays(1 - DateTime.Now.Day).Date.AddMonths(1).AddSeconds(-1);
            for (var time = startTime; time <= endTime; time.AddDays(1))
            {
                dateTimes.Add(time);
            }
            return dateTimes;
        }
        //获取某年的周六周日
        public int GetWMDay(int year)
        {
            int retNum = 0;
            DateTime counYear = Convert.ToDateTime(year + "-01-01");
            DateTime nestYear = counYear.AddYears(1);
            for (DateTime i = counYear; i < nestYear; i = i.AddDays(1))
            {
                if (i.DayOfWeek == DayOfWeek.Saturday || i.DayOfWeek == DayOfWeek.Sunday)
                {
                    retNum++;
                }
            }
            return retNum;
        }
        public async Task<PunchCard_Info> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }
        public async Task<PunchCard_Info> GetTheDataByWfIdAsync(string wfId)
        {
            return await GetIQueryable().Where(x => x.F_WFId == wfId).FirstOrDefaultAsync();
        }
        public async Task AddDataAsync(PunchCard_Info data)
        {
            await InsertAsync(data);
        }

        public async Task UpdateDataAsync(PunchCard_Info data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }
        //public DataTable GetExcelListAsync(PageInput<PunchCardModel> input)
        //{
        //    input.PageRows = 50;
        //    var punchCard_Reports = GetPuchCardReport(input).Result.Data;
        //    //List<Dictionary<string, object>> punchCard_dic = new List<Dictionary<string, object>>();
        //    //foreach (var item in punchCard_Reports)
        //    //{
        //    //    Dictionary<string, object> dic = new Dictionary<string, object>();
        //    //    dic[""]
        //    //    foreach (var items in item.Personnels)
        //    //    {
        //    //        switch (items.Month)
        //    //        {
        //    //            case 1:
        //    //                changeDetailed.Incumbency1 = items.Incumbency;
        //    //                changeDetailed.NewHires1 = items.NewHires;
        //    //                changeDetailed.TurnoverNumber1 = items.TurnoverNumber;
        //    //                break;
        //    //            case 2:
        //    //                changeDetailed.Incumbency2 = items.Incumbency;
        //    //                changeDetailed.NewHires2 = items.NewHires;
        //    //                changeDetailed.TurnoverNumber2 = items.TurnoverNumber;
        //    //                break;
        //    //            case 3:
        //    //                changeDetailed.Incumbency3 = items.Incumbency;
        //    //                changeDetailed.NewHires3 = items.NewHires;
        //    //                changeDetailed.TurnoverNumber3 = items.TurnoverNumber;
        //    //                break;
        //    //            case 4:
        //    //                changeDetailed.Incumbency4 = items.Incumbency;
        //    //                changeDetailed.NewHires4 = items.NewHires;
        //    //                changeDetailed.TurnoverNumber4 = items.TurnoverNumber;
        //    //                break;
        //    //            case 5:
        //    //                changeDetailed.Incumbency5 = items.Incumbency;
        //    //                changeDetailed.NewHires5 = items.NewHires;
        //    //                changeDetailed.TurnoverNumber5 = items.TurnoverNumber;
        //    //                break;
        //    //            case 6:
        //    //                changeDetailed.Incumbency6 = items.Incumbency;
        //    //                changeDetailed.NewHires6 = items.NewHires;
        //    //                changeDetailed.TurnoverNumber6 = items.TurnoverNumber;
        //    //                break;
        //    //            case 7:
        //    //                changeDetailed.Incumbency7 = items.Incumbency;
        //    //                changeDetailed.NewHires7 = items.NewHires;
        //    //                changeDetailed.TurnoverNumber7 = items.TurnoverNumber;
        //    //                break;
        //    //            case 8:
        //    //                changeDetailed.Incumbency8 = items.Incumbency;
        //    //                changeDetailed.NewHires8 = items.NewHires;
        //    //                changeDetailed.TurnoverNumber8 = items.TurnoverNumber;
        //    //                break;
        //    //            case 9:
        //    //                changeDetailed.Incumbency9 = items.Incumbency;
        //    //                changeDetailed.NewHires9 = items.NewHires;
        //    //                changeDetailed.TurnoverNumber9 = items.TurnoverNumber;
        //    //                break;
        //    //            case 10:
        //    //                changeDetailed.Incumbency10 = items.Incumbency;
        //    //                changeDetailed.NewHires10 = items.NewHires;
        //    //                changeDetailed.TurnoverNumber10 = items.TurnoverNumber;
        //    //                break;
        //    //            case 11:
        //    //                changeDetailed.Incumbency11 = items.Incumbency;
        //    //                changeDetailed.NewHires11 = items.NewHires;
        //    //                changeDetailed.TurnoverNumber11 = items.TurnoverNumber;
        //    //                break;
        //    //            case 12:
        //    //                changeDetailed.Incumbency12 = items.Incumbency;
        //    //                changeDetailed.NewHires12 = items.NewHires;
        //    //                changeDetailed.TurnoverNumber12 = items.TurnoverNumber;
        //    //                break;
        //    //            default:
        //    //                break;
        //    //        }
        //    //    }
        //    //    changeDetaileds.Add(changeDetailed);
        //    //}
        //    //设置导出格式
        //    //ExcelConfig excelconfig = new ExcelConfig();
        //    //excelconfig.FileName = input.Search.Month+"打卡考勤报表.xls";
        //    //excelconfig.IsAllSizeColumn = true;
        //    ////每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
        //    //excelconfig.ColumnEntity = new List<ColumnModel>
        //    //    {
        //    //        //new ColumnModel() { Column = "index", ExcelColumn = "序号", Sort = 0 },
        //    //        new ColumnModel() { Column = "departmentname", ExcelColumn = "部门名称", Sort = 1 },
        //    //        new ColumnModel() { Column = "incumbency1", ExcelColumn = "在职人数", Sort = 2 },
        //    //        new ColumnModel() { Column = "newhires1", ExcelColumn = "新入职人数", Sort = 3 },
        //    //        new ColumnModel() { Column = "turnovernumber1", ExcelColumn = "离职人数", Sort = 4 },
        //    //        new ColumnModel() { Column = "incumbency2", ExcelColumn = "在职人数", Sort = 5 },
        //    //        new ColumnModel() { Column = "newhires2", ExcelColumn = "新入职人数", Sort = 6 },
        //    //        new ColumnModel() { Column = "turnovernumber2", ExcelColumn = "离职人数", Sort = 7 },
        //    //        new ColumnModel() { Column = "incumbency3", ExcelColumn = "在职人数", Sort = 8 },
        //    //        new ColumnModel() { Column = "newhires3", ExcelColumn = "新入职人数", Sort = 9 },
        //    //        new ColumnModel() { Column = "turnovernumber3", ExcelColumn = "离职人数", Sort = 10 },
        //    //        new ColumnModel() { Column = "incumbency4", ExcelColumn = "在职人数", Sort = 11 },
        //    //        new ColumnModel() { Column = "newhires4", ExcelColumn = "新入职人数", Sort = 12 },
        //    //        new ColumnModel() { Column = "turnovernumber4", ExcelColumn = "离职人数", Sort = 13 },
        //    //        new ColumnModel() { Column = "incumbency5", ExcelColumn = "在职人数", Sort = 14 },
        //    //        new ColumnModel() { Column = "newhires5", ExcelColumn = "新入职人数", Sort = 15 },
        //    //        new ColumnModel() { Column = "TurnoverNumber5".ToLower(), ExcelColumn = "离职人数", Sort = 16 },
        //    //        new ColumnModel() { Column = "Incumbency6".ToLower(), ExcelColumn = "在职人数", Sort = 17 },
        //    //        new ColumnModel() { Column = "NewHires6".ToLower(), ExcelColumn = "新入职人数", Sort = 18},
        //    //        new ColumnModel() { Column = "TurnoverNumber6".ToLower(), ExcelColumn = "离职人数", Sort = 19 },
        //    //        new ColumnModel() { Column = "Incumbency7".ToLower(), ExcelColumn = "在职人数", Sort = 20 },
        //    //        new ColumnModel() { Column = "NewHires7".ToLower(), ExcelColumn = "新入职人数", Sort = 21 },
        //    //        new ColumnModel() { Column = "TurnoverNumber7".ToLower(), ExcelColumn = "离职人数", Sort = 22 },
        //    //        new ColumnModel() { Column = "Incumbency8".ToLower(), ExcelColumn = "在职人数", Sort =23 },
        //    //        new ColumnModel() { Column = "NewHires8".ToLower(), ExcelColumn = "新入职人数", Sort = 24 },
        //    //        new ColumnModel() { Column = "TurnoverNumber8".ToLower(), ExcelColumn = "离职人数", Sort = 25 },
        //    //        new ColumnModel() { Column = "Incumbency9".ToLower(), ExcelColumn = "在职人数", Sort = 26 },
        //    //        new ColumnModel() { Column = "NewHires9".ToLower(), ExcelColumn = "新入职人数", Sort = 27 },
        //    //        new ColumnModel() { Column = "TurnoverNumber9".ToLower(), ExcelColumn = "离职人数", Sort = 28 },
        //    //        new ColumnModel() { Column = "Incumbency10".ToLower(), ExcelColumn = "在职人数", Sort = 29 },
        //    //        new ColumnModel() { Column = "NewHires10".ToLower(), ExcelColumn = "新入职人数", Sort = 30 },
        //    //        new ColumnModel() { Column = "TurnoverNumber10".ToLower(), ExcelColumn = "离职人数", Sort = 31 },
        //    //        new ColumnModel() { Column = "Incumbency11".ToLower(), ExcelColumn = "在职人数", Sort = 32 },
        //    //        new ColumnModel() { Column = "NewHires11".ToLower(), ExcelColumn = "新入职人数", Sort = 33 },
        //    //        new ColumnModel() { Column = "TurnoverNumber11".ToLower(), ExcelColumn = "离职人数", Sort = 34 },
        //    //        new ColumnModel() { Column = "Incumbency12".ToLower(), ExcelColumn = "在职人数", Sort = 35 },
        //    //        new ColumnModel() { Column = "NewHires12".ToLower(), ExcelColumn = "新入职人数", Sort = 36 },
        //    //        new ColumnModel() { Column = "TurnoverNumber12".ToLower(), ExcelColumn = "离职人数", Sort = 37 },
        //    //    };
        //    //int i = 0;
        //    //foreach (var item in excelconfig.ColumnEntity)
        //    //{
        //    //    item.Sort = i;
        //    //    i++;
        //    //}
        //    //List<HeaderMultiMergeModel> headerMultis = new List<HeaderMultiMergeModel>();
        //    //HeaderMultiMergeModel headerMulti = new HeaderMultiMergeModel
        //    //{
        //    //    Row = 0
        //    //};
        //    //List<HeaderMultiMergeDetails> mergeDetails = new List<HeaderMultiMergeDetails>();
        //    //for (int a = 0; a < 13; a++)
        //    //{
        //    //    switch (a)
        //    //    {
        //    //        case 0:
        //    //            mergeDetails.Add(AssignmentInfo("部门", 1, 2, 0, 0));
        //    //            break;
        //    //        case 1:
        //    //            mergeDetails.Add(AssignmentInfo("1月", 1, 1, 1, 3));
        //    //            break;
        //    //        case 2:
        //    //            mergeDetails.Add(AssignmentInfo("2月", 1, 1, 4, 6));
        //    //            break;
        //    //        case 3:
        //    //            mergeDetails.Add(AssignmentInfo("3月", 1, 1, 7, 9));
        //    //            break;
        //    //        case 4:
        //    //            mergeDetails.Add(AssignmentInfo("4月", 1, 1, 10, 12));
        //    //            break;
        //    //        case 5:
        //    //            mergeDetails.Add(AssignmentInfo("5月", 1, 1, 13, 15));
        //    //            break;
        //    //        case 6:
        //    //            mergeDetails.Add(AssignmentInfo("6月", 1, 1, 16, 18));
        //    //            break;
        //    //        case 7:
        //    //            mergeDetails.Add(AssignmentInfo("7月", 1, 1, 19, 21));
        //    //            break;
        //    //        case 8:
        //    //            mergeDetails.Add(AssignmentInfo("8月", 1, 1, 22, 24));
        //    //            break;
        //    //        case 9:
        //    //            mergeDetails.Add(AssignmentInfo("9月", 1, 1, 25, 27));
        //    //            break;
        //    //        case 10:
        //    //            mergeDetails.Add(AssignmentInfo("10月", 1, 1, 28, 30));
        //    //            break;
        //    //        case 11:
        //    //            mergeDetails.Add(AssignmentInfo("11月", 1, 1, 31, 33));
        //    //            break;
        //    //        case 12:
        //    //            mergeDetails.Add(AssignmentInfo("12月", 1, 1, 34, 36));
        //    //            break;
        //    //        default:
        //    //            break;
        //    //    }
        //    //}
        //    //headerMulti.List = mergeDetails;
        //    //headerMultis.Add(headerMulti);
        //    //var dataTabel = DataTableHelper.FillDataTable(changeDetaileds);
        //    //var t = ExcelHelper.ExportMemoryStream(dataTabel, excelconfig, false, headerMultis).GetBuffer();
        //    ////var t = ExcelHelper.ExportMemoryStream(dataTabel, excelconfig, true, null).GetBuffer();
        //    //return t;
        //}
        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<PunchCard_Info>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<PunchCard_Info, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<PunchCard_Info, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }
        #endregion

        #region 私有成员

        #endregion
    }
}