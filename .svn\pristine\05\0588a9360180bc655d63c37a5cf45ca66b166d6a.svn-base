<template>
  <a-modal :title="Title" :visible="visible" :confirm-loading="confirmLoading" @ok="handleUpload"
    @cancel="handleCancel">
    <template slot="footer">
      <a-button type="primary" icon="download" @click="downloadTemplate()">下载模板</a-button>
      <a-button type="primary" icon="upload" :disabled="fileList.length === 0" :loading="confirmLoading"
        @click="handleUpload">
        {{ confirmLoading ? '上传中' : '上传' }}
      </a-button>
    </template>
    <!--   :action="`${$rootUrl}HR_EmployeeInfoManage/HR_DisciplinaryRecords/UploadFileByForm?ID=` + UserId" -->
    <a-upload-dragger name="file" :multiple="false" accept=".xls" @change="handleChange" :before-upload="beforeUpload">
      <p class="ant-upload-drag-icon">
        <a-icon type="inbox" />
      </p>
      <p class="ant-upload-text">
        单击或拖动文件到此区域以上传
      </p>
      <p class="ant-upload-hint">
        *只能上传.xls与.xlsx文件
      </p>
    </a-upload-dragger>
  </a-modal>
</template>
<script>
import { downLoadFile } from '@/utils/plugin/axios-plugin.js'
import { operateFile } from '@/utils/tools.js'
export default {
  components: {},
  props: {
    parentObj: Object
  },
  data () {
    return {
      ModalText: 'Content of the modal',
      visible: false,
      confirmLoading: false,
      Title: '',
      UserId: '',
      fileList: []
    }
  },
  methods: {
    //下载模板
    downloadTemplate () {
      const title = this.title
      downLoadFile(
        "/HR_EmployeeInfoManage/HR_DisciplinaryRecords/DownloadTemplate",
        null,
        function (res) {
          console.log(res)
          if (res) {
            operateFile("", '奖惩记录模板')
          } else {
            console.log('失败')
          }
        },
        function (err) {
          console.log(err)
        }
      )
    },
    handleChange (info) {
      const status = info.file.status
      if (status !== 'uploading') {
        console.log(info.file, info.fileList)
      }
      if (status === 'done') {
        this.$message.success(`${info.file.name} file uploaded successfully.`)
      } else if (status === 'error') {
        this.$message.error(`${info.file.name} file upload failed.`)
      }
    },
    showModal () {
      this.visible = true
    },
    beforeUpload (file) {
      if (this.fileList.length >= 1) {
        this.$message.error("只能上传一个文件！")
        return false
      }
      this.fileList = [...this.fileList, file]
      return false
    },
    openForm (id, title) {
      this.Title = title
      this.visible = true
      this.UserId = id
    },
    handleUpload () {
      const { fileList } = this
      const formData = new FormData()
      fileList.forEach(file => {
        formData.append('id', this.UserId)
        formData.append('files[]', file)
      })
      // formData.append('ID', THI)
      this.uploading = true
      this.$http.post('/HR_EmployeeInfoManage/HR_DisciplinaryRecords/UploadFileByForm1', formData).then(resJson => {
        this.fileList = []
        this.uploading = false
        this.$message.success('upload successfully.')
      })
    },
    handleOk (e) {
      this.ModalText = 'The modal will be closed after two seconds'
      this.confirmLoading = true
      setTimeout(() => {
        this.visible = false
        this.confirmLoading = false
      }, 2000)
    },
    handleCancel (e) {
      console.log('Clicked cancel button')
      this.visible = false
    }
  }
}
</script>
