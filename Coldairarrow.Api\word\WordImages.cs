﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DocumentFormat.OpenXml;
using DW = DocumentFormat.OpenXml.Drawing.Wordprocessing;
using PIC = DocumentFormat.OpenXml.Drawing.Pictures;
using A = DocumentFormat.OpenXml.Drawing;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using System.IO;
using System.Drawing;

namespace Coldairarrow.Api.Word
{
    public class WordImages
    {
        /// <summary> 向word添加图片</summary>
        /// <param name="filePath"></param>
        /// <param name="picturePath"></param>
        public static void AddPictureIntoWord(WordprocessingDocument doc, string picturePath)
        {

            string picType = picturePath.Split('.').Last();
            ImagePartType imagePartType;
            ImagePart imagePart = null;
            // 通过后缀名判断图片类型, true 表示忽视大小写
            if (Enum.TryParse<ImagePartType>(picType, true, out imagePartType))
            {
                imagePart = doc.MainDocumentPart.AddImagePart(imagePartType);
            }
            System.IO.FileStream s = System.IO.File.Open(picturePath, FileMode.Open);
            System.Drawing.Image im = System.Drawing.Image.FromStream(s);
            s.Close();
            imagePart.FeedData(s = System.IO.File.Open(picturePath, FileMode.Open)); // 读取图片二进制流

            AddImageToBody(doc, doc.MainDocumentPart.GetIdOfPart(imagePart), im.Width, im.Height);
            s.Close();

        }
        /// <summary> 向word添加图片</summary>
        /// <param name="filePath"></param>
        /// <param name="picturePath"></param>
        public static void AddPictureIntoWord(WordprocessingDocument doc, Stream pictureStream, string pictureName)
        {
            // 从文件流中获取图片类型
            string picType = pictureName;
            ImagePartType imagePartType;

            // 通过后缀名判断图片类型，true 表示忽视大小写
            if (Enum.TryParse<ImagePartType>(picType, true, out imagePartType))
            {
                ImagePart imagePart = doc.MainDocumentPart.AddImagePart(imagePartType);

                // 将图片数据写入 ImagePart
                imagePart.FeedData(pictureStream);

                // 获取图片宽度和高度
                using (pictureStream)
                {
                    using (var bitmap = new Bitmap(pictureStream))
                    {
                        int width = bitmap.Width;
                        int height = bitmap.Height;

                        // 将图片添加到文档体中
                        AddImageToBody(doc, doc.MainDocumentPart.GetIdOfPart(imagePart), width, height);
                    }
                }
            }
            else
            {
                throw new ArgumentException("图片类型不支持");
            }
        }
        /// <summary>
        /// 传递二进制添加图片
        /// </summary>
        /// <param name="doc"></param>
        /// <param name="pictureBytes"></param>
        /// <param name="pictureName"></param>
        /// <exception cref="ArgumentException"></exception>
        public static void AddPictureIntoWord(WordprocessingDocument doc, byte[] pictureBytes, string bookmarkName)
        {
            using (MemoryStream memoryStream = new MemoryStream(pictureBytes))
            {
                ImagePart imagePart = doc.MainDocumentPart.AddImagePart(ImagePartType.Jpeg);
                imagePart.FeedData(memoryStream);

                using (memoryStream)
                {
                    memoryStream.Position = 0; // 重置内存流的位置
                    using (var bitmap = new Bitmap(memoryStream))
                    {
                        //int width = bitmap.Width;
                        //int height = bitmap.Height;
                        int width = 100;
                        int height = 120;

                        AddImageToBody(doc, doc.MainDocumentPart.GetIdOfPart(imagePart), width, height, bookmarkName);
                    }
                }
            }
        }

        private static void AddImageToBody(WordprocessingDocument wordDoc, string relationshipId, int width, int height, string bookmarkName)
        {

            var _w = width * 9525;
            var _h = height * 9525;
            if (_w > 5715000)
            {
                _w = 5715000;
            }
            if (_h > 5715000)
            {
                _h = 5715000;
            }

            Paragraph pictureParagraph = new Paragraph();
            Run pictureRun = new Run();

            var element = new Drawing(
                new DW.Inline(
                    new DW.Extent() { Cx = width * 9525L, Cy = height * 9525L }, // 调节图片大小
                    new DW.EffectExtent() { LeftEdge = 0L, TopEdge = 0L, RightEdge = 0L, BottomEdge = 0L },
                    new DW.DocProperties() { Id = (UInt32Value)1U, Name = "Picture 1" },
                    new DW.NonVisualGraphicFrameDrawingProperties(
                        new A.GraphicFrameLocks() { NoChangeAspect = true }),
                    new A.Graphic(
                        new A.GraphicData(
                            new PIC.Picture(
                                new PIC.NonVisualPictureProperties(
                                    new PIC.NonVisualDrawingProperties() { Id = (UInt32Value)0U, Name = "New Bitmap Image.jpg" },
                                    new PIC.NonVisualPictureDrawingProperties()),
                                new PIC.BlipFill(
                                    new A.Blip(
                                        new A.BlipExtensionList(
                                            new A.BlipExtension() { Uri = "{28A0092B-C50C-407E-A947-70E740481C1C}" }))
                                    {
                                        Embed = relationshipId,
                                        CompressionState = A.BlipCompressionValues.Print
                                    },
                                    new A.Stretch(new A.FillRectangle())),
                                     new PIC.ShapeProperties(
                                           new A.Transform2D(
                                             new A.Offset() { X = 0L, Y = 0L },
                                             new A.Extents() { Cx = _w * 1L, Cy = _h * 1L }), //与上面的对准
                                           new A.PresetGeometry(new A.AdjustValueList()) { Preset = A.ShapeTypeValues.Rectangle }))
                        )
                        { Uri = "http://schemas.openxmlformats.org/drawingml/2006/picture" })
                )
                {
                    DistanceFromTop = (UInt32Value)0U,
                    DistanceFromBottom = (UInt32Value)0U,
                    DistanceFromLeft = (UInt32Value)0U,
                    DistanceFromRight = (UInt32Value)0U,
                    // EditId = "50D07946"
                });

            //书签处写入---这是关键 tp为传递过来的 书签名称，替换成变量即可
            MainDocumentPart mainPart = wordDoc.MainDocumentPart;
            var bookmarks = from bm in mainPart.Document.Body.Descendants<BookmarkStart>()
                            where bm.Name == bookmarkName
                            select bm;
            var bookmark = bookmarks.SingleOrDefault();
            if (bookmark != null)
            {
                OpenXmlElement elem = bookmark.NextSibling();
                while (elem != null && !(elem is BookmarkEnd))
                {
                    OpenXmlElement nextElem = elem.NextSibling();
                    elem.Remove();
                    elem = nextElem;
                }
                var parent = bookmark.Parent;   // bookmark's parent element
                Run run = new Run(new RunProperties());
                //该处插入element，注意区分和字符插入的方法Append(dd)
                parent.InsertAfter<Run>(new Run(new Run(element)), bookmark);

            }
        }
        //帮助文档中的源码，直接拷贝出来修改，根据
        private static void AddImageToBody(WordprocessingDocument wordDoc, string relationshipId)
        {
            // Define the reference of the image.
            var element =
                 new Drawing(
                     new DW.Inline(
                         new DW.Extent() { Cx = 1990000L, Cy = 1792000L },
                         new DW.EffectExtent()
                         {
                             LeftEdge = 0L,
                             TopEdge = 0L,
                             RightEdge = 0L,
                             BottomEdge = 0L
                         },
                         new DW.DocProperties()
                         {
                             Id = (UInt32Value)1U,
                             Name = "Picture 1"
                         },
                         new DW.NonVisualGraphicFrameDrawingProperties(
                             new A.GraphicFrameLocks() { NoChangeAspect = true }),
                         new A.Graphic(
                             new A.GraphicData(
                                 new PIC.Picture(
                                     new PIC.NonVisualPictureProperties(
                                         new PIC.NonVisualDrawingProperties()
                                         {
                                             Id = (UInt32Value)0U,
                                             Name = "New Bitmap Image.jpg"
                                         },
                                         new PIC.NonVisualPictureDrawingProperties()),
                                     new PIC.BlipFill(
                                         new A.Blip(
                                             new A.BlipExtensionList(
                                                 new A.BlipExtension()
                                                 {
                                                     Uri =
                                                       "{28A0092B-C50C-407E-A947-70E740481C1C}"
                                                 })
                                         )
                                         {
                                             Embed = relationshipId,
                                             CompressionState =
                                             A.BlipCompressionValues.Print
                                         },
                                         new A.Stretch(
                                             new A.FillRectangle())),
                                     new PIC.ShapeProperties(
                                         new A.Transform2D(
                                             new A.Offset() { X = 0L, Y = 0L },
                                             new A.Extents() { Cx = 1990000L, Cy = 1792000L }),
                                         new A.PresetGeometry(
                                             new A.AdjustValueList()
                                         )
                                         { Preset = A.ShapeTypeValues.Rectangle }))
                             )
                             { Uri = "http://schemas.openxmlformats.org/drawingml/2006/picture" })
                     )
                     {
                         DistanceFromTop = (UInt32Value)0U,
                         DistanceFromBottom = (UInt32Value)0U,
                         DistanceFromLeft = (UInt32Value)0U,
                         DistanceFromRight = (UInt32Value)0U,
                         //  EditId = "50D07946"
                     });

            //书签处写入---这是关键 tp为传递过来的 书签名称，替换成变量即可
            MainDocumentPart mainPart = wordDoc.MainDocumentPart;
            var bookmarks = from bm in mainPart.Document.Body.Descendants<BookmarkStart>()
                            where bm.Name == "tp"
                            select bm;
            var bookmark = bookmarks.SingleOrDefault();
            if (bookmark != null)
            {
                OpenXmlElement elem = bookmark.NextSibling();
                while (elem != null && !(elem is BookmarkEnd))
                {
                    OpenXmlElement nextElem = elem.NextSibling();
                    elem.Remove();
                    elem = nextElem;
                }
                var parent = bookmark.Parent;   // bookmark's parent element
                Run run = new Run(new RunProperties());
                //该处插入element，注意区分和字符插入的方法Append(dd)
                parent.InsertAfter<Run>(new Run(new Run(element)), bookmark);

            }


        }


        // 摘抄自http://msdn.microsoft.com/EN-US/library/office/bb497430(v=office.15).aspx?cs-save-lang=1&cs-lang=csharp#code-snippet-5    
        /// <summary>向word添加图片 </summary>
        /// <param name="wordDoc"></param>
        /// <param name="width">图片宽度</param>
        /// <param name="height">图片高度</param>
        public static void AddImageToBody(WordprocessingDocument wordDoc, string relationshipId, int width, int height)
        {
            //wordDoc.
            var _w = width * 9525;
            var _h = height * 9525;
            if (_w > 5715000)
            {
                _w = 5715000;
            }
            if (_h > 5715000)
            {
                _h = 5715000;
            }
            // Define the reference of the image.
            var element =
               new Drawing(
                 new DW.Inline(
                   new DW.Extent() { Cx = width * 9525L, Cy = height * 9525L }, // 调节图片大小360000 EMUS 9525 px
                   new DW.EffectExtent() { LeftEdge = 0L, TopEdge = 0L, RightEdge = 0L, BottomEdge = 0L },
                   new DW.DocProperties() { Id = (UInt32Value)1U, Name = "Picture 1" },
                   new DW.NonVisualGraphicFrameDrawingProperties(
                     new A.GraphicFrameLocks() { NoChangeAspect = true }),
                   new A.Graphic(
                     new A.GraphicData(
                       new PIC.Picture(
                         new PIC.NonVisualPictureProperties(
                             new PIC.NonVisualDrawingProperties() { Id = (UInt32Value)0U, Name = "New Bitmap Image.jpg" },
                           new PIC.NonVisualPictureDrawingProperties()),
                         new PIC.BlipFill(
                           new A.Blip(
                             new A.BlipExtensionList(new A.BlipExtension() { Uri = "{28A0092B-C50C-407E-A947-70E740481C1C}" }))
                           {
                               Embed = relationshipId,
                               CompressionState = A.BlipCompressionValues.Print
                           },
                           new A.Stretch(new A.FillRectangle())),
                         new PIC.ShapeProperties(
                           new A.Transform2D(
                             new A.Offset() { X = 0L, Y = 0L },
                             new A.Extents() { Cx = _w * 1L, Cy = _h * 1L }), //与上面的对准
                           new A.PresetGeometry(new A.AdjustValueList()) { Preset = A.ShapeTypeValues.Rectangle }))
                     )
                     { Uri = "http://schemas.openxmlformats.org/drawingml/2006/picture" })
                 )
                 {
                     DistanceFromTop = (UInt32Value)0U,
                     DistanceFromBottom = (UInt32Value)0U,
                     DistanceFromLeft = (UInt32Value)0U,
                     DistanceFromRight = (UInt32Value)0U,
                     EditId = "50D07946"
                 });

            // Append the reference to body, the element should be in a Run.
            //添加参考到身体的元素，应该在运行。
            wordDoc.MainDocumentPart.Document.Body.AppendChild(new Paragraph(new Run(element)));
        }
    }
}
