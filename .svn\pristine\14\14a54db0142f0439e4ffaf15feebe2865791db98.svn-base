﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.HR_Manage
{
    public interface IHR_RecruitEduBackBusiness
    {
        Task<PageResult<HR_RecruitEduBack>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<HR_RecruitEduBack> GetTheDataAsync(string id);
        Task AddDataAsync(HR_RecruitEduBack data);
        Task UpdateDataAsync(HR_RecruitEduBack data);
        Task DeleteDataAsync(List<string> ids);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
        int AddData(HR_RecruitEduBack data);
        int UpdateData(HR_RecruitEduBack data);
        int DeleteData(HR_RecruitEduBack data);
        int UpdateListData(List<HR_RecruitEduBack> data);
        void AddListData(List<HR_RecruitEduBack> data);
    }
}