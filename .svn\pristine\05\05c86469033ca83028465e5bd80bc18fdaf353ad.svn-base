﻿using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using NPOI.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Threading.Tasks;

namespace Coldairarrow.Business.Base_Manage
{
    public interface IBase_PostBusiness
    {
        Task<List<Base_PostTreeDTO>> GetTreeDataListAsync(PostTreeInputDTO input);
        Task<PageResult<Base_PostDTO>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<List<Base_PostTreeDTO>> GetUserTreeDataAsync(PostTreeInputDTO input, IOperator op);

        Task<List<Base_PostTreeDTO>> GetUserTreeDataListAsync(PostTreeInputDTO input, IOperator op);

        List<Base_PostTreeDTO> GetPostProcessList(PostTreeInputDTO input, IOperator op);
        Task<List<Base_Post>> GetAllListAsync();
        /// <summary>
        /// 根据岗位id获取他的上级数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Base_Post GetParentPost(string id);
        /// <summary>
        /// 获取组织数据通过职位id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        string GetOrgName(string id);
        Task<Base_Post> GetTheDataAsync(string id);
        Base_PostListDTO GetFormDataAsync(string id);
        /// <summary>
        /// 获取岗位说明书
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<string> GetQualifications(string id);
        Task AddDataAsync(Base_Post data);
        Task UpdateDataAsync(Base_Post data);
        Task UpdateModelAsync(Base_Post data);
        Task UpdatePostInfoDataAsync(Base_Post data);
        /// <summary>
        /// 通过部门的主键ID获取数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<Base_PostDTO> GetThenIdAsync(string id);
        Task DeleteDataAsync(List<string> ids);
        /// <summary>
        /// 根据名称获取岗位信息
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        Base_Post GetByName(string name);
        /// <summary>
        /// 获取编号
        /// </summary>
        /// <returns></returns>
        string GetNumber();

        Task<PageResult<Base_PostDTO>> GetListPost(PageInput<ConditionDTO> input);

        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
    }
    public class PostTreeInputDTO
    {
        public string parentId { get; set; }
        public string departmentId { get; set; }
        public string CompanyId { get; set; }

        public string Name { get; set; }

    }

    public class Base_PostTreeDTO : TreeModel
    {
        public object children { get => Children; }
        public string title { get => Text; }
        public string value { get => Id; }
        public string key { get => Id; }

        public string F_Name { get; set; }
        public string F_EnCode { get; set; }

        public string F_DepartmentName { get; set; }

        public string F_Description { get; set; }
        /// <summary>
        /// 是否直接下属
        /// </summary>
        public int? F_IsDirec { get; set; }
    }
    public class Base_PostListDTO : Base_Post
    {
        [NotMapped]
        //办公座位
        public string F_SeatText => F_Seat == 1 ? "准备" : F_Seat == 0 ? "未准备" : "";
        [NotMapped]
        //办公用品
        public string F_SuppliesText => F_Supplies == 1 ? "准备" : F_Seat == 0 ? "未准备" : "";
        [NotMapped]
        //电脑采购
        public string F_ComputerPurchText
        {
            get
            {
                string str = "";
                switch (F_ComputerPurch)
                {
                    case 0:
                        str = "台式机";
                        break;
                    case 1:
                        str = "笔记本";
                        break;
                    case 2:
                        str = "不需要";
                        break;
                    default:
                        str = "";
                        break;
                }
                return str;
            }
        }
    }
    public class Base_PostDTO : Base_Post
    {
        public string CompanyName { get; set; }
        public string DepartmentName { get; set; }

        public string ParentName { get; set; }
        public string DepartmentName2 { get; set; }
        /// <summary>
        /// 组织
        /// </summary>
        public string OrgName { get; set; }
    }
    public class Base_PostOutDTO
    {
        /// <summary>
        /// 主键
        /// </summary>
        public String F_Id { get; set; }

        /// <summary>
        /// 上级Id
        /// </summary>
        public String F_ParentId { get; set; }

        /// <summary>
        /// 岗位名称
        /// </summary>
        public String F_Name { get; set; }

        /// <summary>
        /// 岗位编号
        /// </summary>
        public String F_EnCode { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public String F_CompanyId { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public String F_DepartmentId { get; set; }
    }
}