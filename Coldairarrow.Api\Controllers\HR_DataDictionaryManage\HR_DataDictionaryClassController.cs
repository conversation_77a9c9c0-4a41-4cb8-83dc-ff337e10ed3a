﻿using Coldairarrow.Business.HR_DataDictionaryManage;
using Coldairarrow.Entity.HR_DataDictionaryManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.HR_DataDictionaryManage
{
    [Route("/HR_DataDictionaryManage/[controller]/[action]")]
    public class HR_DataDictionaryClassController : BaseApiController
    {
        #region DI

        public HR_DataDictionaryClassController(IHR_DataDictionaryClassBusiness hR_DataDictionaryClassBus)
        {
            _hR_DataDictionaryClassBus = hR_DataDictionaryClassBus;
        }

        IHR_DataDictionaryClassBusiness _hR_DataDictionaryClassBus { get; }

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_DataDictionaryClass>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_DataDictionaryClassBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_DataDictionaryClass> GetTheData(IdInputDTO input)
        {
            return await _hR_DataDictionaryClassBus.GetTheDataAsync(input.id);
        }

        /// <summary>
        /// 获取字典分类列表
        /// </summary>
        /// <param name="keyword">关键词（名称/编码）</param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<List<HR_DataDictionaryClass>> GetClassifyList(string keyword)
        {
            var data = _hR_DataDictionaryClassBus.GetClassifyList(keyword);
            AjaxResult<List<HR_DataDictionaryClass>> res = new AjaxResult<List<HR_DataDictionaryClass>>();
            res.Data = data;
            return res;
        }
        /// <summary>
        /// 获取字典分类列表(树结构)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<List<DictionaryClassDTO>> GetClassifyTree()
        {
            var data = _hR_DataDictionaryClassBus.GetClassifyTree();
            AjaxResult<List<DictionaryClassDTO>> res = new AjaxResult<List<DictionaryClassDTO>>();
            res.Data = data;
            return res;
        }
        /// <summary>
        /// 分类编号不能重复
        /// </summary>
        /// <param name="ItemCode">编码</param>
        /// <param name="keyValue">主键</param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult ExistItemCode(string keyValue, string F_ItemCode)
        {
            bool res = _hR_DataDictionaryClassBus.ExistItemCode(keyValue, F_ItemCode);
            AjaxResult result = new AjaxResult();
            result.Msg = res ? "不重复" : "重复";
            return result;
        }
        /// <summary>
        /// 分类名称不能重复
        /// </summary>
        /// <param name="ItemName">名称</param>
        /// <param name="keyValue">主键</param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult ExistItemName(string keyValue, string F_ItemName)
        {
            bool res = _hR_DataDictionaryClassBus.ExistItemName(keyValue, F_ItemName);
            AjaxResult result = new AjaxResult();
            result.Msg = res ? "不重复" : "重复";
            return result;
        }

        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_DataDictionaryClass data)
        {
            #region 参数验证
            if(data == null)
            {
                throw new System.Exception("参数无效！");
            }
            if (data.F_ItemCode.IsNullOrEmpty())
            {
                throw new System.Exception("分类编码不能为空！");
            }
            if (data.F_ItemName.IsNullOrEmpty())
            {
                throw new System.Exception("分类名称不能为空！");
            }
            if (!_hR_DataDictionaryClassBus.ExistItemCode(data.F_Id, data.F_ItemCode))
            {
                throw new System.Exception("分类编号不能重复！");
            }
            if (!_hR_DataDictionaryClassBus.ExistItemName(data.F_Id, data.F_ItemName))
            {
                throw new System.Exception("分类名称不能重复！");
            }
            #endregion

            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_DataDictionaryClassBus.AddDataAsync(data);
            }
            else
            {
                await _hR_DataDictionaryClassBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_DataDictionaryClassBus.DeleteDataAsync(ids);
        }

        #endregion
    }
}