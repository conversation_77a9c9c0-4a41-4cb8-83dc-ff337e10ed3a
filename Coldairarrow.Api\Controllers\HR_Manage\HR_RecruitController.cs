﻿using Coldairarrow.Business.HR_Manage;
using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.HR_Manage
{
    [Route("/HR_Manage/[controller]/[action]")]
    public class HR_RecruitController : BaseApiController
    {
        #region DI

        public HR_RecruitController(IHR_RecruitBusiness hR_RecruitBus, IOperator @operator)
        {
            _hR_RecruitBus = hR_RecruitBus;
            _operator = @operator;
        }

        IHR_RecruitBusiness _hR_RecruitBus { get; }
        readonly IOperator _operator;

        #endregion

        #region 获取

        [HttpPost]
        public async Task<PageResult<HR_Recruit>> GetDataList(PageInput<ConditionDTO> input)
        {
            var t = _operator.UserId;
            return await _hR_RecruitBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_Recruit> GetTheData(IdInputDTO input)
        {
            return await _hR_RecruitBus.GetTheDataAsync(input.id);
        }
        [HttpPost]
        public HRRecruit GetListData()
        {
            return _hR_RecruitBus.GetListData();
        }
        [HttpPost]
        public bool EditRec(InterviewDTO input)
        {
            return _hR_RecruitBus.EditRec(input.KeyWord, input.type);
        }
        /// <summary>
        /// 微信小程序获取招聘列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpGet, NoCheckJWT]
        public async Task<List<HR_Recruit>> GetWechatList(bool hot)
        {
            return await _hR_RecruitBus.GetListAsync(hot);
        }
        /// <summary>
        /// 微信小程序获取招聘信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpGet, NoCheckJWT]
        public async Task<HR_Recruit> GetWechatDetail(string fid)
        {
            return await _hR_RecruitBus.GetTheDataAsync(fid);
        }
        [HttpPost]
        public List<RecPost> RecrPost()
        {
            return _hR_RecruitBus.RecrPost();
        }
        #endregion

        #region 提交

        [HttpPost]
        public async Task SaveData(HR_Recruit data)
        {
            //判读是否存在该岗位
            //if (!string.IsNullOrEmpty(data.F_Role))
            //{
            //    var hR_Recruit = _hR_RecruitBus.GetPostEntity(data.F_Role);
            //    if (hR_Recruit != null)
            //    {
            //        throw new Exception("该岗位已存在相关招聘名称");
            //    }
            //}
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_RecruitBus.AddDataAsync(data);
            }
            else
            {
                await _hR_RecruitBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_RecruitBus.DeleteDataAsync(ids);
        }
        /// <summary>
        /// 开始结束时间延期一个月
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task handleDelay(IdInputDTO input)
        {
            await _hR_RecruitBus.handleDelay(input.id);
        }



        #endregion

        #region 导出Excel
        /// <summary>
        /// Excel导出下载
        /// </summary>
        /// <param name="dtSource">DataTable数据源</param>
        /// <param name="excelConfig">导出设置包含文件名、标题、列设置</param>
        /// <param name="isSort">是否自定义排序，默认false，如果true需要ExcelConfig添加sort属性，sort从0开始排序</param>
        /// <param name="dataList">多行表头数据集</param>
        [HttpPost]
        public FileContentResult ExcelDownload(PageInput<ConditionDTO> input)
        {
            try
            {
                //取出数据源
                DataTable exportTable = _hR_RecruitBus.GetExcelListAsync(input);
                //设置导出格式
                ExcelConfig excelconfig = new ExcelConfig();
                excelconfig.HeadFont = "微软雅黑";
                excelconfig.HeadHeight = 15;
                excelconfig.HeadPoint = 11;
                excelconfig.FileName = "导出.xlsx";
                excelconfig.Title = "导出";
                excelconfig.IsAllSizeColumn = false;
                //每一列的设置,没有设置的列信息，系统将按datatable中的列名导出
                excelconfig.ColumnEntity = new List<ColumnModel>();
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "f_recruitname", ExcelColumn = "招聘岗位", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "F_Role", ExcelColumn = "招聘角色", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "F_Start", ExcelColumn = "开始时间", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "F_End", ExcelColumn = "结束时间", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "F_Url", ExcelColumn = "链接", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "F_Content", ExcelColumn = "招聘内容", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "F_Achieve", ExcelColumn = "招聘要求", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "F_City", ExcelColumn = "招聘城市", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "F_Experience", ExcelColumn = "经验", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "F_Education", ExcelColumn = "学历", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "F_Salary", ExcelColumn = "薪资", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "F_Detail", ExcelColumn = "岗位描述", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "F_Hot", ExcelColumn = "是否热门", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "F_Type", ExcelColumn = "类型", Alignment = "left" });
                excelconfig.ColumnEntity.Add(new ColumnModel() { Column = "F_Address", ExcelColumn = "地址", Alignment = "left" });
                var t = ExcelHelper.ExportMemoryStream(exportTable, excelconfig, false, null).GetBuffer();
                var file = File(t, "application/x-zip-compressed", "cccc.xls");
                //upStream.Dispose();
                return file;

                //return null;

            }
            catch (Exception ex)
            {

                throw ex;


            }
        }
        #endregion

        #region 导入数据

        /// <summary>
        /// 导入数据
        /// <summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadFileByForm()
        {
            var file = Request.Form.Files.FirstOrDefault();
            if (file == null)
                return JsonContent(new { status = "error" }.ToJson());

            string path = $"/Upload/{Guid.NewGuid().ToString("N")}/{file.FileName}";
            string physicPath = GetAbsolutePath($"~{path}");
            string dir = Path.GetDirectoryName(physicPath);
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            using (FileStream fs = new FileStream(physicPath, FileMode.Create))
            {
                file.CopyTo(fs);
            }

            Hashtable ht = new Hashtable();
            ht["f_recruitname"] = "招聘岗位";
            ht["F_Role"] = "招聘角色";
            ht["F_Start"] = "开始时间";
            ht["F_End"] = "结束时间";
            ht["F_Url"] = "链接";
            ht["F_Content"] = "招聘内容";
            ht["F_Achieve"] = "招聘要求";
            ht["F_City"] = "招聘城市";
            ht["F_Experience"] = "经验";
            ht["F_Education"] = "学历";
            ht["F_Salary"] = "薪资";
            ht["F_Detail"] = "岗位描述";
            ht["F_Hot"] = "是否热门";
            ht["F_Type"] = "类型";
            ht["F_Address"] = "地址";
            var list = new ExcelHelper<HR_Recruit>().ExcelImport(ht, physicPath);

            //如果出错则返回错误页面
            if (list == null) { return null; }
            else
            {
                foreach (var m in list)
                {
                    _hR_RecruitBus.AddDataAsync(m);
                }
            }

            //string url = $"{_configuration["WebRootUrl"]}{path}";
            var res = new
            {
                name = file.FileName,
                status = "done",
                //thumbUrl = url,
                //url = url
            };

            return JsonContent(res.ToJson());
        }

        #endregion
    }
}