﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data;

namespace Coldairarrow.Business.HR_Manage
{
    public interface IHR_RecruitWorkExpeBusiness
    {
        Task<PageResult<HR_RecruitWorkExpe>> GetDataListAsync(PageInput<ConditionDTO> input);
        Task<HR_RecruitWorkExpe> GetTheDataAsync(string id);
        Task AddDataAsync(HR_RecruitWorkExpe data);
        Task UpdateDataAsync(HR_RecruitWorkExpe data);
        Task DeleteDataAsync(List<string> ids);
        DataTable GetExcelListAsync(PageInput<ConditionDTO> input);
        int AddData(HR_RecruitWorkExpe data);
        int UpdateData(HR_RecruitWorkExpe data);
        int DeleteData(HR_RecruitWorkExpe data);
        int UpdateListData(List<HR_RecruitWorkExpe> data);
        void AddListData(List<HR_RecruitWorkExpe> data);
    }
}