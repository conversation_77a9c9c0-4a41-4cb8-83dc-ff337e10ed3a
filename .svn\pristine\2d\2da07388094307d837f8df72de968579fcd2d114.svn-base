﻿using Coldairarrow.Entity.HR_Manage;
using Coldairarrow.Util;
using EFCore.Sharding;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Data;
using Coldairarrow.Entity.HolidayManage;
using Coldairarrow.Entity.HR_EmployeeInfoManage;
using Coldairarrow.Entity.Serivces;
using org.apache.zookeeper;
using System;
using Coldairarrow.IBusiness;
using Coldairarrow.Entity.Base_Manage;
using Coldairarrow.Entity.Shop_Manage.Enum;
using NPOI.Util;
using System.Collections;
using Coldairarrow.Util.UEditor;
using Coldairarrow.Entity.HR_Manage.Enum;
using Coldairarrow.Entity;

namespace Coldairarrow.Business.HR_Manage
{
    public class HR_FeeUseDetailBusiness : BaseBusiness<HR_FeeUseDetail>, IHR_FeeUseDetailBusiness, ITransientDependency
    {
        public HR_FeeUseDetailBusiness(IDbAccessor db, IOperator @operator)
            : base(db)
        {
            _operator = @operator;
        }
        private readonly IOperator _operator;
        #region 外部接口

        public async Task<PageResult<HR_FeeUseDetail>> GetDataListAsync(PageInput<ProductConditinDto> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_FeeUseDetail>();
            var search = input.Search;
            if (!search.userId.IsNullOrWhiteSpace())
            {
                where = where.And(x => x.F_UserId == search.userId);
            }
            if (!search.userName.IsNullOrWhiteSpace())
            {
                where = where.And(x => x.F_UserName.Contains(search.userName));
            }
            if (search.orderType.HasValue)
            {
                where = where.And(x => x.F_DetailType == (HRPointEnum.FeeTypeEnum)search.orderType);
            }
            if (search.resType.HasValue)
            {
                where = where.And(x => x.F_ResType == (ShopEnum.PointBuyType)search.resType);
            }
            if (search.resType.HasValue)
            {
                where = where.And(x => x.F_ResType == (ShopEnum.PointBuyType)search.resType);
            }
            if (search.startTime.HasValue)
            {
                where = where.And(x => x.F_CreateDate >= search.startTime.Value);
            }
            if (search.endTime.HasValue)
            {
                where = where.And(x => x.F_CreateDate < search.endTime.Value.AddDays(1));
            }
            if (search.isMonth.HasValue)
            {
                //查询近3个月的数据
                where = where.And(x => x.F_CreateDate >= DateTime.Now.AddMonths(-3));
            }
            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_FeeUseDetail, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            return await q.Where(where).GetPageResultAsync(input);
        }


        /// <summary>
        /// 生成员工费用额度
        /// </summary>
        /// <param name="year"></param>
        /// <returns></returns>
        public async Task CreateUserFeePoint(string year)
        {
            //List<string> relStatus = new List<string>()
            //    {
            //        "派驻",
            //        "正式员工",
            //        "试用员工",
            //        "第三方用工",
            //        "第三方员工",
            //        "试用员工（延期转正）",
            //        "正式员工（校招）",
            //        "正式员工（销售）",
            //        "顾问"
            //    };

            //var model = Db.GetIQueryable<HR_FormalEmployees>().Where(i => relStatus.Contains(i.EmployRelStatus));
            //var List = new List<HR_FormalEmployees>();
            //if (!string.IsNullOrWhiteSpace(userId))
            //{
            //    var entity = Db.GetIQueryable<HR_HolidayLine>().Where(i => i.F_Year == year && i.F_UserId == userId && i.F_HolidayTypes != "病假").ToList();
            //    if (entity.Count > 0)
            //    {
            //        Db.Delete(entity);
            //    }
            //    List = model.Where(i => i.F_Id == userId).ToList();
            //}
            //else
            //{
            //    var ids = Db.GetIQueryable<HR_HolidayLine>().Where(i => i.F_Year == year).Select(s => s.F_UserId).Distinct().ToList();
            //    List = model.Where(i => !ids.Contains(i.F_Id)).ToList();
            //    var ueser = List.Where(i => i.F_Id == "446BF456-2D52-41FF-A0FB-EBDAE880295E").FirstOrDefault();
            //}
            //var dateNow = Convert.ToDateTime(year + "-12-31");
            //List<HR_HolidayLine> hR_Holidays = new List<HR_HolidayLine>();
            //var legal = "法定年假";
            //var welfare = "福利年假";
            //var sickLeave = "病假";

            //foreach (var item in List)
            //{
            //    if (item.NameUser == "张波")
            //    {
            //        Console.WriteLine("11");
            //    }
            //    if (!item.F_InTime.HasValue || !item.F_StartWorkTime.HasValue) { continue; }
            //    int ys = dateNow.Year - item.F_StartWorkTime.Value.Year;
            //    int ms = dateNow.Month - item.F_StartWorkTime.Value.Month;
            //    //span 开始工作至今有多少个月
            //    int span = ys * 12 + ms;
            //    int ysIn = dateNow.Year - item.F_InTime.Value.Year;
            //    int msIn = dateNow.Month - item.F_InTime.Value.Month;
            //    var workingYear = (int)Math.Floor(Convert.ToDecimal((ysIn * 12) + msIn) / 12);
            //    //if (span < 12)
            //    //{
            //    //    continue;
            //    //}
            //    decimal legalDay = 0;
            //    decimal welfareDay = 0;
            //    #region 初始化法定年假、福利年假、病假
            //    HR_HolidayLine LegalHoliday = new HR_HolidayLine()
            //    {
            //        F_ActualAmount = 0,
            //        F_BusState = 1,
            //        F_CreateDate = DateTime.Now.Date,
            //        F_CreateUserId = !op.UserId.IsNullOrEmpty() ? op.UserId : "Admin",
            //        F_CreateUserName = !op.RealName.IsNullOrEmpty() ? op.RealName : "超级管理员",
            //        F_EndTime = Convert.ToDateTime((Convert.ToInt32(year) + 1) + "-1-1").AddSeconds(-1),
            //        F_HolidayTypes = legal,
            //        F_Id = Guid.NewGuid().ToString(),
            //        F_LncDecLine = 0,
            //        F_StandardLine = 0,
            //        F_VacationUnit = "天",
            //        F_EffectTime = Convert.ToDateTime(year + "-1-1"),
            //        F_UsedLine = 0,
            //        F_UserId = item.F_Id,
            //        F_Year = year,
            //        F_StandardLineLast = 0,
            //        F_ActualAmountLast = 0,
            //    };
            //    HR_HolidayLine WelfareHoliday = new HR_HolidayLine();
            //    if (!item.EmployRelStatus.Contains("第三方"))
            //    {
            //        WelfareHoliday = new HR_HolidayLine()
            //        {
            //            F_ActualAmount = 5,
            //            F_BusState = 1,
            //            F_CreateDate = DateTime.Now.Date,
            //            F_CreateUserId = !op.UserId.IsNullOrEmpty() ? op.UserId : "Admin",
            //            F_CreateUserName = !op.RealName.IsNullOrEmpty() ? op.RealName : "超级管理员",
            //            F_EndTime = Convert.ToDateTime((Convert.ToInt32(year) + 1) + "-1-1").AddSeconds(-1),
            //            F_HolidayTypes = welfare,
            //            F_Id = Guid.NewGuid().ToString(),
            //            F_LncDecLine = 0,
            //            F_StandardLine = 0,
            //            F_VacationUnit = "天",
            //            F_EffectTime = Convert.ToDateTime(year + "-1-1"),
            //            F_UsedLine = 0,
            //            F_UserId = item.F_Id,
            //            F_Year = year,
            //            F_StandardLineLast = 0,
            //            F_ActualAmountLast = 0,
            //        };
            //    }

            //    HR_HolidayLine sickLeaveHoliday = new HR_HolidayLine()
            //    {
            //        F_ActualAmount = 6,
            //        F_BusState = 1,
            //        F_CreateDate = DateTime.Now.Date,
            //        F_CreateUserId = !op.UserId.IsNullOrEmpty() ? op.UserId : "Admin",
            //        F_CreateUserName = !op.RealName.IsNullOrEmpty() ? op.RealName : "超级管理员",
            //        F_EndTime = Convert.ToDateTime((Convert.ToInt32(year) + 1) + "-1-1").AddSeconds(-1),
            //        F_HolidayTypes = sickLeave,
            //        F_Id = Guid.NewGuid().ToString(),
            //        F_LncDecLine = 0,
            //        F_StandardLine = 6,
            //        F_VacationUnit = "天",
            //        F_EffectTime = Convert.ToDateTime(year + "-1-1"),
            //        F_UsedLine = 0,
            //        F_UserId = item.F_Id,
            //        F_Year = year,
            //        F_StandardLineLast = 0,
            //        F_ActualAmountLast = 0,
            //    };
            //    #endregion


            //    var Year = LessYear(span);
            //    if (dateNow.Year == item.F_InTime.Value.Year)//入职当年转正的员工
            //    {
            //        if (span <= 12)//工作年限不满一年的员工
            //        {
            //            TimeSpan tss = dateNow - item.F_InTime.Value;
            //            var days = tss.TotalDays;
            //            welfareDay = LessYear((decimal)days, 10);
            //            if (!item.EmployRelStatus.Contains("第三方"))
            //            {
            //                WelfareHoliday.F_ActualAmount = welfareDay;
            //                WelfareHoliday.F_LncDecLine = welfareDay - 10;
            //                WelfareHoliday.F_StandardLine = 10;
            //                WelfareHoliday.F_UsedLine = 0;
            //                WelfareHoliday.F_ActualAmountLast = 0;
            //                WelfareHoliday.F_ActualAmountLast = 0;
            //                WelfareHoliday.F_EffectTime = item.F_InTime.Value;
            //                hR_Holidays.Add(WelfareHoliday);
            //            }
            //        }
            //        else //工作年限一年以上的员工
            //        {
            //            var dataEnd = Convert.ToDateTime(year + "-12-31");
            //            TimeSpan ts = dateNow - item.F_InTime.Value;
            //            var day = ts.TotalDays;
            //            legalDay = LessYear((decimal)day, Year);
            //            LegalHoliday.F_ActualAmount = legalDay;
            //            LegalHoliday.F_LncDecLine = legalDay - Year;
            //            LegalHoliday.F_StandardLine = Year;
            //            LegalHoliday.F_UsedLine = 0;
            //            LegalHoliday.F_ActualAmountLast = 0;
            //            LegalHoliday.F_ActualAmountLast = 0;
            //            LegalHoliday.F_EffectTime = item.F_InTime.Value;
            //            hR_Holidays.Add(LegalHoliday);
            //            if (WelfareLegalYear(item.F_Rank) && !item.EmployRelStatus.Contains("第三方"))
            //            {
            //                if (item.F_Rank.Contains("5") || item.F_Rank.Contains("6") || item.F_Rank.Contains("7"))
            //                {
            //                    welfareDay = LessYear((decimal)day, 5);
            //                    WelfareHoliday.F_ActualAmount = welfareDay;
            //                    WelfareHoliday.F_LncDecLine = welfareDay - 5;
            //                    WelfareHoliday.F_StandardLine = 5;
            //                }
            //                else
            //                {
            //                    if (Year == 15)
            //                    {
            //                        continue;
            //                    }
            //                    else
            //                    {
            //                        welfareDay = LessYear((decimal)day, 5);
            //                        WelfareHoliday.F_ActualAmount = welfareDay;
            //                        WelfareHoliday.F_LncDecLine = welfareDay - 5;
            //                        WelfareHoliday.F_StandardLine = 5;
            //                    }
            //                }
            //                WelfareHoliday.F_UsedLine = 0;
            //                WelfareHoliday.F_ActualAmountLast = 0;
            //                WelfareHoliday.F_ActualAmountLast = 0;
            //                WelfareHoliday.F_EffectTime = item.F_InTime.Value;
            //                hR_Holidays.Add(WelfareHoliday);
            //            }
            //        }
            //    }
            //    else
            //    {
            //        var wokeY = 0;
            //        LegalHoliday.F_ActualAmount = Year;
            //        LegalHoliday.F_LncDecLine = 0;
            //        LegalHoliday.F_StandardLine = Year;
            //        LegalHoliday.F_UsedLine = 0;
            //        LegalHoliday.F_ActualAmountLast = 0;
            //        LegalHoliday.F_ActualAmountLast = 0;
            //        hR_Holidays.Add(LegalHoliday);
            //        if (WelfareLegalYear(item.F_Rank))
            //        {
            //            var enYear = span / 12;
            //            if (enYear <= 10)
            //            {
            //                if (item.F_Rank.Contains("5") || item.F_Rank.Contains("6"))
            //                {
            //                    WelfareHoliday.F_ActualAmount = 5 + workingYear;
            //                    WelfareHoliday.F_LncDecLine = 0;
            //                    WelfareHoliday.F_StandardLine = 5 + workingYear;
            //                }
            //                else
            //                {
            //                    wokeY = 5 + workingYear;
            //                    if (wokeY > 10)
            //                    {
            //                        wokeY = 10;
            //                    }
            //                    WelfareHoliday.F_ActualAmount = wokeY;
            //                    WelfareHoliday.F_LncDecLine = 0;
            //                    WelfareHoliday.F_StandardLine = wokeY;
            //                }
            //            }
            //            else if (10 < enYear && enYear < 20)
            //            {
            //                if (item.F_Rank.Contains("5") || item.F_Rank.Contains("6"))
            //                {
            //                    wokeY = 5 + workingYear;
            //                    if (wokeY > 10)
            //                    {
            //                        wokeY = 10;
            //                    }
            //                    WelfareHoliday.F_ActualAmount = wokeY;
            //                    WelfareHoliday.F_LncDecLine = 0;
            //                    WelfareHoliday.F_StandardLine = wokeY;
            //                }
            //                else
            //                {
            //                    WelfareHoliday.F_ActualAmount = 5;
            //                    WelfareHoliday.F_LncDecLine = 0;
            //                    WelfareHoliday.F_StandardLine = 5;
            //                }
            //            }
            //            else
            //            {
            //                if (item.F_Rank.Contains("5") || item.F_Rank.Contains("6"))
            //                {
            //                    WelfareHoliday.F_ActualAmount = 5;
            //                    WelfareHoliday.F_LncDecLine = 0;
            //                    WelfareHoliday.F_StandardLine = 5;
            //                }
            //                else
            //                {
            //                    hR_Holidays.Add(sickLeaveHoliday);
            //                    continue;
            //                }
            //            }

            //            if (item.F_Rank.Contains("5") || item.F_Rank.Contains("6"))
            //            {
            //                if (WelfareHoliday.F_ActualAmount + LegalHoliday.F_ActualAmount > 20)
            //                {
            //                    WelfareHoliday.F_ActualAmount = 15 - LegalHoliday.F_ActualAmount;
            //                    WelfareHoliday.F_StandardLine = 15 - LegalHoliday.F_StandardLine;
            //                }

            //            }
            //            else
            //            {
            //                if (WelfareHoliday.F_ActualAmount + LegalHoliday.F_ActualAmount > 15)
            //                {
            //                    WelfareHoliday.F_ActualAmount = 15 - LegalHoliday.F_ActualAmount;
            //                    WelfareHoliday.F_StandardLine = 15 - LegalHoliday.F_StandardLine;
            //                }
            //            }
            //            LegalHoliday.F_UsedLine = 0;
            //            LegalHoliday.F_ActualAmountLast = 0;
            //            LegalHoliday.F_ActualAmountLast = 0;
            //            if (!item.EmployRelStatus.Contains("第三方"))
            //            {
            //                hR_Holidays.Add(WelfareHoliday);
            //            }
            //        }
            //        hR_Holidays.Add(sickLeaveHoliday);
            //    }
            //}
            //if (hR_Holidays.Count() > 0)
            //{
            //    hR_Holidays.ForEach(i =>
            //    {
            //        i.F_CreateUserId = !op.UserId.IsNullOrEmpty() ? op.UserId : "Admin";
            //        i.F_CreateUserName = !op.RealName.IsNullOrEmpty() ? op.RealName : "超级管理员";
            //    });
            //}
            //Db.BulkInsert(hR_Holidays);
            //Db.SaveChanges();
        }

        public async Task<HR_FeeUseDetail> GetTheDataAsync(string id)
        {
            return await GetEntityAsync(id);
        }

        public async Task AddDataAsync(HR_FeeUseDetail data)
        {
            await InsertAsync(data);
        }
        public async Task UpdateDataAsync(HR_FeeUseDetail data)
        {
            await UpdateAsync(data);
        }

        public async Task DeleteDataAsync(List<string> ids)
        {
            await DeleteAsync(ids);
        }

        public DataTable GetExcelListAsync(PageInput<ConditionDTO> input)
        {
            var q = GetIQueryable();
            var where = LinqHelper.True<HR_FeeUseDetail>();
            var search = input.Search;

            //筛选
            if (!search.Condition.IsNullOrEmpty() && !search.Keyword.IsNullOrEmpty())
            {
                var newWhere = DynamicExpressionParser.ParseLambda<HR_FeeUseDetail, bool>(
                    ParsingConfig.Default, false, $@"{search.Condition}.Contains(@0)", search.Keyword);
                where = where.And(newWhere);
            }

            //var expr1 = DynamicExpressionParser.ParseLambda<HR_FeeUseDetail, bool>(ParsingConfig.Default, true, "F_WFState > 1 && F_RecruitName == \"小6\"");
            //where = where.And(where);


            return q.Where(where).ToDataTable();
        }


        /// <summary>
        /// 导入并保存数据
        /// </summary>
        /// <param name="physicPath">物理路径</param>
        /// <returns></returns>
        public async Task<AjaxResult<DataTable>> ImportSaveData(string physicPath, IOperator op, string quarterStr)
        {
            AjaxResult<DataTable> ajaxResult = new AjaxResult<DataTable>();
            Hashtable ht = new Hashtable();
            ht["F_UserName"] = "员工姓名";
            ht["F_UseNumber"] = "额度";

            var importList = new ExcelHelper<HR_FeeUseDetail>().ExcelImport(ht, physicPath);
            if (importList == null || importList.Count == 0)
            {
                ajaxResult.Success = false;
                ajaxResult.Msg = "上传数据错误或不能为空";
            }
            else
            {
                List<HR_FeeUseDetail> records = new List<HR_FeeUseDetail>();
                var userEntity = Db.GetIQueryable<HR_FormalEmployees>().ToList();
                var base_Users = Db.GetIQueryable<Base_User>().ToList();
                var date = DateTime.Now;

                //获取当前年的员工积分总额度
                var pointList = await Db.GetIQueryable<HR_FeePointEntity>().Where(i => i.F_Iyear == date.Year).ToListAsync();
                //获取当前月的员工积分总额度
                var pointUseDetails = await Db.GetIQueryable<HR_FeeUseDetail>().Where(i => i.F_Quarter == quarterStr).ToListAsync();
                var updatePointList = new List<HR_FeePointEntity>();
                var addPointList = new List<HR_FeePointEntity>();
                var noImpUsers = new List<string>();
                importList.ForEach(import =>
                {
                    if (!import.F_UserName.IsNullOrWhiteSpace() && (pointUseDetails.Count == 0 || (pointUseDetails.Count > 0 && !pointUseDetails.Any(x => x.F_UserName == import.F_UserName))))
                    {
                        HR_FeeUseDetail hR_FeeUseDetail = new HR_FeeUseDetail();
                        //查询员工得总额度
                        var pointEntity = pointList.Find(x => x.F_UserName == import.F_UserName.Trim());
                        //查找相关id
                        var base_User = base_Users.Where(x => x.RealName == import.F_UserName.Trim()).OrderBy(x => x.CreateTime).FirstOrDefault();
                        if (base_User != null)
                        {
                            hR_FeeUseDetail.F_UserName = import.F_UserName?.Trim();
                            hR_FeeUseDetail.F_UserId = base_User.Id?.Trim();
                            hR_FeeUseDetail.F_UserCode = !string.IsNullOrWhiteSpace(base_User.UserName) ? base_User.UserName.Replace("@cqlandmark.com", "") : "";
                            hR_FeeUseDetail.F_UseNumber = import.F_UseNumber;
                            hR_FeeUseDetail.F_Describe = quarterStr + "额度导入";
                            hR_FeeUseDetail.F_DetailType = HRPointEnum.FeeTypeEnum.自动充值;
                            hR_FeeUseDetail.F_ResType = ShopEnum.PointBuyType.增加;
                            hR_FeeUseDetail.F_Month = date.ToString("yyyy-MM");
                            hR_FeeUseDetail.F_Iyear = date.Year;
                            hR_FeeUseDetail.F_Quarter = quarterStr;
                            //F_ResNumer
                            if (pointEntity != null)
                            {
                                hR_FeeUseDetail.F_ResNumer = pointEntity.F_ResNumber;
                                hR_FeeUseDetail.F_BackNumer = pointEntity.F_ResNumber + import.F_UseNumber;
                                pointEntity.F_ResNumber = hR_FeeUseDetail.F_BackNumer;
                                pointEntity.F_StandardNumber += import.F_UseNumber;
                                this.UpdateEntity(pointEntity, _operator);
                                updatePointList.Add(pointEntity);
                            }
                            else
                            {
                                hR_FeeUseDetail.F_ResNumer = 0;
                                hR_FeeUseDetail.F_BackNumer = import.F_UseNumber;
                                //新增积分总额度表
                                HR_FeePointEntity hR_Point = new HR_FeePointEntity()
                                {
                                    F_UserName = import.F_UserName?.Trim(),
                                    F_UserId = base_User.Id?.Trim(),
                                    F_UserCode = hR_FeeUseDetail.F_UserCode,
                                    F_Iyear = date.Year,
                                    F_ResNumber = import.F_UseNumber,
                                    F_StandardNumber = import.F_UseNumber,
                                    F_LncDecNumber = 0,
                                    F_UsedNumber = 0
                                };
                                this.InitEntity(hR_Point, _operator);
                                addPointList.Add(hR_Point);
                            }
                            this.InitEntity(hR_FeeUseDetail, _operator);
                            records.Add(hR_FeeUseDetail);
                        }
                    }
                    else
                    {
                        if (!import.F_UserName.IsNullOrWhiteSpace())
                        {
                            noImpUsers.Add(import.F_UserName);
                        }
                    }
                });
                if (updatePointList.Count > 0)
                {
                    await this.Db.UpdateAsync(updatePointList);
                }

                if (addPointList.Count > 0)
                {
                    this.Db.BulkInsert(addPointList);
                }
                if (records.Count > 0)
                {
                    this.Db.BulkInsert(records);
                }
                if (noImpUsers.Count > 0)
                {
                    ajaxResult.Success = false;
                    ajaxResult.Msg = $"{records.Count}位员工导入成功," + string.Join(",", noImpUsers) + "导入失败,失败原因：该季度已导入";
                    ajaxResult.ErrorCode = 0;
                }
                else
                {
                    ajaxResult.Success = true;
                    ajaxResult.Msg = $"{records.Count}位员工导入成功";
                    ajaxResult.ErrorCode = 0;
                }
            }
            return ajaxResult;
        }
        #endregion

        #region 私有成员
        /// <summary>
        /// 流程回调
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task FlowCallBackAsync(PointFlowInputDTO input)
        {
            if (input == null)
            {
                throw new BusException("参数错误");
            }
            if (input.userPoints != null && input.userPoints.Count >= 0)
            {
                if (input.status == (int)WFStates.完成流程 || input.status == (int)WFStates.提交生效)
                {
                    //获取当前年的员工积分总额度
                    var pointList = await Db.GetIQueryable<HR_FeePointEntity>().Where(i => i.F_Iyear == input.year).ToListAsync();
                    //获取当前月的员工积分总额度
                    //var pointUseDetails = await Db.GetIQueryable<HR_FeeUseDetail>().Where(i => i.F_Month == Month).ToListAsync();
                    //扣除积分
                    var updateFeePoint = new List<HR_FeePointEntity>();
                    var addFeeUse = new List<HR_FeeUseDetail>();
                    var base_Users = Db.GetIQueryable<Base_User>().ToList();
                    input.userPoints.ForEach(item =>
                    {
                        var pointEntity = pointList.Find(x => (!string.IsNullOrWhiteSpace(x.F_UserCode) && x.F_UserCode == item.userAd) || x.F_UserName == item.userName);
                        if (pointEntity != null)
                        {
                            //查找相关id
                            var base_User = base_Users.Where(x => x.RealName == item.userName.Trim()).OrderBy(x => x.CreateTime).FirstOrDefault();
                            HR_FeeUseDetail hR_FeeUseDetail = new HR_FeeUseDetail()
                            {
                                F_Id = Guid.NewGuid().ToString("N"),
                                F_CreateDate = DateTime.Now,
                                F_CreateUserId = item.userAd,
                                F_CreateUserName = item.userName,
                                F_UserName = item.userName,
                                F_UserId = base_User.Id?.Trim(),
                                F_UserCode = item.userAd,
                                F_UseNumber = item.Num,
                                F_Describe = "季度积分扣除",
                                F_DetailType = HRPointEnum.FeeTypeEnum.积分扣除,
                                F_ResType = ShopEnum.PointBuyType.减少,
                                F_Month = DateTime.Now.ToString("yyyy-MM"),
                                F_Iyear = DateTime.Now.Year,
                                F_ResNumer = pointEntity.F_ResNumber,
                                F_BackNumer = pointEntity.F_ResNumber - item.Num,
                            };
                            addFeeUse.Add(hR_FeeUseDetail);
                            pointEntity.F_UsedNumber += item.Num;
                            pointEntity.F_ResNumber -= item.Num;
                            updateFeePoint.Add(pointEntity);

                        }
                    });
                    if (addFeeUse.Count > 0)
                    {
                        this.Db.BulkInsert(addFeeUse);
                    }
                    if (updateFeePoint.Count > 0)
                    {
                        await this.Db.UpdateAsync(updateFeePoint);
                    }
                }
            }
            else
            {
                throw new BusException("未发现员工额度");
            }
        }
        #endregion
    }
}