//第一次集成时使用的方法

<template>
    <div>
        <!--下面通过传递进来的id完成初始化-->
        <script :id="id"  type="text/plain"></script>
        </div>
     </template>

        <script>

        //需要修改  ueditor.config.js 的路径
        //var URL = window.UEDITOR_HOME_URL || ‘/static/ueditor_1/‘;

        //主体文件引入
        import '../../static/ueditor_1/ueditor.config.js'
        import '../../static/ueditor_1/ueditor.all.min.js'
        import '../../static/ueditor_1/lang/zh-cn/zh-cn.js'
        //主体文件引入


        export default {
            props: {
                //配置可以传递进来
                config:{}
            },
            data () {
                return {
                    //每个编辑器生成不同的id,以防止冲突
                    id: 'editor_' + (Math.random() * 100000000000000000),
                    //编辑器实例
                    instance: null,
            };
            },
            //此时--el挂载到实例上去了,可以初始化对应的编辑器了
            mounted () {
                this.initEditor()
            },

            beforeDestroy () {
                // 组件销毁的时候，要销毁 UEditor 实例
                if (this.instance !== null && this.instance.destroy) {
                    this.instance.destroy();
                }
            },
            methods: {
                initEditor () {
                    //dom元素已经挂载上去了
                    this.$nextTick(() => {
                        this.instance = UE.getEditor(this.id, this.config);
                        // 绑定事件，当 UEditor 初始化完成后，将编辑器实例通过自定义的 ready 事件交出去
                        this.instance.addListener('ready', () => {
                            this.$emit('ready', this.instance);
                        });
                        this.editor.addListener("contentChange", ()=> {
                            const wordCount = this.editor.getContentLength(true);
                            const content = this.editor.getContent();
                            const plainTxt = this.editor.getPlainTxt();
                            this.$emit('input', {wordCount: wordCount, content: content, plainTxt: plainTxt});
                        });
                    });
                }
            }
        };
        </script>