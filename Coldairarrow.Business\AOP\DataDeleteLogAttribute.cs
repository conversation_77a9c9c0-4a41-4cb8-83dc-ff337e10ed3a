﻿using Coldairarrow.IBusiness;
using Coldairarrow.Util;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

namespace Coldairarrow.Business
{
    public class DataDeleteLogAttribute : WriteDataLogAttribute
    {
        public DataDeleteLogAttribute(UserLogType logType, string nameField, string dataName)
            : base(logType, nameField, dataName)
        {
        }

        private string _names;
        private bool isId = false;
        List<string> logTypeList = new List<string>() {  "系统异常",
        "系统用户管理",
        "系统角色管理",
        "接口密钥管理",
        "部门管理"};
        public override async Task Befor(IAOPContext context)
        {
            List<string> ids = context.Arguments[0] as List<string>;
            var q = context.InvocationTarget.GetType().GetMethod("GetIQueryable").Invoke(context.InvocationTarget, new object[] { }) as IQueryable;
            List<object> deleteList = new List<object>();
            if (logTypeList.Count(i => i == _logType.ToString()) > 0)
            {
                isId = true;
            }
            if (isId)
            {
                deleteList = q.Where("@0.Contains(id)", ids).CastToList<object>();
            }
            else
            {
                deleteList = q.Where("@0.Contains(F_Id)", ids).CastToList<object>();
            } 
            _names = string.Join(",", deleteList.Select(x => x.GetPropertyValue(_nameField)?.ToString()));

            await Task.CompletedTask;
        }
        public override async Task After(IAOPContext context)
        {
            var op = context.ServiceProvider.GetService<IOperator>();

            op.WriteUserLog(_logType, $"删除{_dataName}:{_names}");

            await Task.CompletedTask;
        }
    }
}
