﻿<template>
  <a-card :bordered="false" :hoverable="true">
    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="hanldleAdd()">新建</a-button>
      <a-button type="primary" icon="minus" @click="handleDelete(selectedRowKeys)" :disabled="!hasSelected()"
        :loading="loading">删除</a-button>
      <a-button type="primary" icon="arrow-down" @click="exportExcel()">导出Excel</a-button>
      <a-button type="primary" icon="redo" @click="getDataList()">刷新</a-button>
    </div>

    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="10">
          <a-col :md="4" :sm="24">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="queryParam.condition">
                <a-select-option key="W_OpenId">微信id</a-select-option>
                <a-select-option key="W_Phone">联系方式</a-select-option>
                <a-select-option key="W_Email">邮箱</a-select-option>
                <a-select-option key="W_Truename">真实姓名</a-select-option>
                <a-select-option key="W_NickName">姓名</a-select-option>
                <a-select-option key="W_City">城市</a-select-option>
                <a-select-option key="W_Province">省份</a-select-option>
                <a-select-option key="W_Country">国家</a-select-option>
                <a-select-option key="W_Icon">头像</a-select-option>
                <a-select-option key="W_Address">用户地址</a-select-option>
                <a-select-option key="F_CreateUserId">创建人Id</a-select-option>
                <a-select-option key="F_CreateUserName">创建人</a-select-option>
                <a-select-option key="F_ModifyUserId">修改人Id</a-select-option>
                <a-select-option key="F_ModifyUserName">修改人</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item>
              <a-input v-model="queryParam.keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-button type="primary" icon="search" @click="() => {this.pagination.current = 1; this.getDataList()}">查询
            </a-button>
            <a-button style="margin-left: 8px" icon="sync" @click="() => (queryParam = {})">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table" :columns="columns" :rowKey="row => row.W_OpenId" :dataSource="data" :pagination="pagination"
      :loading="loading" @change="handleTableChange"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true">
      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record.W_OpenId)">编辑</a>
          <a-divider type="vertical" />
          <a @click="handleDelete([record.W_OpenId])">删除</a>
        </template>
      </span>
    </a-table>

    <edit-form ref="editForm" :parentObj="this"></edit-form>
  </a-card>
</template>

<script>
import EditForm from './EditForm'

const columns = [
  { title: '微信id', dataIndex: 'W_OpenId', width: '10%' },
  { title: '联系方式', dataIndex: 'W_Phone', width: '10%' },
  { title: '邮箱', dataIndex: 'W_Email', width: '10%' },
  { title: '用户类型', dataIndex: 'W_UserType', width: '10%' },
  { title: '真实姓名', dataIndex: 'W_Truename', width: '10%' },
  { title: '性别，0男，1女', dataIndex: 'W_Gender', width: '10%' },
  { title: '昵称', dataIndex: 'W_NickName', width: '10%' },
  { title: '城市', dataIndex: 'W_City', width: '10%' },
  { title: '省份', dataIndex: 'W_Province', width: '10%' },
  { title: '国家', dataIndex: 'W_Country', width: '10%' },
  { title: '头像', dataIndex: 'W_Icon', width: '10%' },
  { title: '用户地址', dataIndex: 'W_Address', width: '10%' },
  { title: '最后登录时间', dataIndex: 'W_LastLogin', width: '10%' },
  { title: '注册时间', dataIndex: 'W_FirstLogin', width: '10%' },
]

export default {
  components: {
    EditForm
  },
  mounted () {
    this.getDataList()
  },
  data () {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 10,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      filters: {},
      sorter: { field: 'W_OpenId', order: 'asc' },
      loading: false,
      columns,
      queryParam: {},
      selectedRowKeys: []
    }
  },
  methods: {
    handleTableChange (pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.filters = { ...filters }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    exportExcel () {
    },
    getDataList () {
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/HR_Manage/HR_WechatUser/GetDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'W_OpenId',
          SortType: this.sorter.order,
          Search: this.queryParam,
          ...this.filters
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          let pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange (selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    hasSelected () {
      return this.selectedRowKeys.length > 0
    },
    hanldleAdd () {
      this.$refs.editForm.openForm()
    },
    handleEdit (id) {
      this.$refs.editForm.openForm(id)
    },
    handleDelete (ids) {
      var thisObj = this
      this.$confirm({
        title: '确认删除吗?',
        onOk () {
          return new Promise((resolve, reject) => {
            thisObj.$http.post('/HR_Manage/HR_WechatUser/DeleteData', ids).then(resJson => {
              resolve()

              if (resJson.Success) {
                thisObj.$message.success('操作成功!')

                thisObj.getDataList()
              } else {
                thisObj.$message.error(resJson.Msg)
              }
            })
          })
        }
      })
    }
  }
}
</script>