﻿using Coldairarrow.Business.HR_DataDictionaryManage;
using Coldairarrow.Entity.HR_DataDictionaryManage;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coldairarrow.Api.Controllers.HR_DataDictionaryManage
{
    [Route("/HR_DataDictionaryManage/[controller]/[action]")]
    public class HR_DataDictionaryDetailsController : BaseApiController
    {
        #region DI

        public HR_DataDictionaryDetailsController(IHR_DataDictionaryClassBusiness hR_DataDictionaryClassBus,
            IHR_DataDictionaryDetailsBusiness hR_DataDictionaryDetailsBus, IConfiguration configuration)
        {
            _hR_DataDictionaryClassBus = hR_DataDictionaryClassBus;
            _hR_DataDictionaryDetailsBus = hR_DataDictionaryDetailsBus;
            _configuration = configuration;
        }

        IHR_DataDictionaryDetailsBusiness _hR_DataDictionaryDetailsBus { get; }
        IHR_DataDictionaryClassBusiness _hR_DataDictionaryClassBus { get; }
        readonly IConfiguration _configuration;

        #endregion

        #region 获取
        [HttpPost]
        public async Task<List<DataDictionaryDetailsOut>> GetDataClassList()
        {
            return await _hR_DataDictionaryDetailsBus.GetDataClassListAsync();
        }

        [HttpPost]
        public async Task<PageResult<HR_DataDictionaryDetails>> GetDataList(PageInput<ConditionDTO> input)
        {
            return await _hR_DataDictionaryDetailsBus.GetDataListAsync(input);
        }

        [HttpPost]
        public async Task<HR_DataDictionaryDetails> GetTheData(IdInputDTO input)
        {
            return await _hR_DataDictionaryDetailsBus.GetTheDataAsync(input.id);
        }
        /// <summary>
        /// 根据字典类型ID查询明细列表
        /// </summary>
        /// <param name="dicId"></param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<List<HR_DataDictionaryDetails>> GetListByDicId(string dicId)
        {
            AjaxResult<List<HR_DataDictionaryDetails>> res = new AjaxResult<List<HR_DataDictionaryDetails>>();
            res.Data = _hR_DataDictionaryDetailsBus.GetListByDicId(dicId);
            return res;
        }
        /// <summary>
        /// 获取字典分类列表
        /// </summary>
        /// <param name="keyword">关键词（名称/编码）</param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<List<HR_DataDictionaryDetails>> GetDetailList(string itemCode, string keyword)
        {
            var data = _hR_DataDictionaryDetailsBus.GetDetailList(itemCode, keyword);
            AjaxResult<List<HR_DataDictionaryDetails>> res = new AjaxResult<List<HR_DataDictionaryDetails>>();
            res.Data = data;
            return res;
        }
        /// <summary>
        /// 获取字典分类列表
        /// </summary>
        /// <param name="keyword">关键词（名称/编码）</param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public AjaxResult<List<HR_DataDictionaryDetails>> PostDetailList(TypeInputDTO input)
        {
            var data = _hR_DataDictionaryDetailsBus.PostDetailList(input.itemCode);
            AjaxResult<List<HR_DataDictionaryDetails>> res = new AjaxResult<List<HR_DataDictionaryDetails>>();
            res.Data = data;
            return res;
        }
        /// <summary>
        /// 项目值不能重复
        /// </summary>
        /// <param name="keyValue">主键</param>
        /// <param name="F_ItemValue">项目值</param>
        /// <param name="itemCode">分类编码</param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult ExistDetailItemValue(string keyValue, string F_ItemValue, string itemCode)
        {
            bool res = _hR_DataDictionaryDetailsBus.ExistDetailItemValue(keyValue, F_ItemValue, itemCode);
            AjaxResult result = new AjaxResult();
            result.Msg = res ? "不重复" : "重复";
            return result;
        }

        /// <summary>
        /// 项目名不能重复
        /// </summary>
        /// <param name="keyValue">主键</param>
        /// <param name="F_ItemName">项目名</param>
        /// <param name="itemCode">分类编码</param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult ExistDetailItemName(string keyValue, string F_ItemName, string itemCode)
        {
            bool res = _hR_DataDictionaryDetailsBus.ExistDetailItemName(keyValue, F_ItemName, itemCode);
            AjaxResult result = new AjaxResult();
            result.Msg = res ? "不重复" : "重复";
            return result;
        }
        [HttpPost]
        public AjaxResult GetOANDData()
        {
            //string str = HttpHelper.PostData(_configuration["OAUrl"] + "rest/getProject/", null, null, ContentType.Json);
            //var retObj = str.ToObject<Dictionary<string, string>>();
            //OAData descJsonStu = new OAData();
            //if (retObj != null)
            //{
            //    if (retObj["errCode"] == "0000")
            //    {
            //        descJsonStu = JsonConvert.DeserializeObject<OAData>(retObj["data"]);
            //    }
            //}
            return Success(_hR_DataDictionaryDetailsBus.GetOANDData(_configuration["OAUrl"] + "rest/archives/getProject"));
        }
        #endregion

        #region 提交

        /// <summary>
        /// 保存明细数据实体
        /// </summary>
        /// <param name="itemCode">分类编码</param>
        /// <param name="data">实体</param>
        [HttpPost]
        public async Task SaveData(PostEntity postEntity)
        {
            var data = postEntity.data;
            var itemCode = postEntity.itemCode;
            #region 参数验证
            if (data == null)
            {
                throw new System.Exception("参数无效！");
            }
            if (itemCode.IsNullOrEmpty())
            {
                throw new System.Exception("分类编码不能为空！");
            }
            if (data.F_ItemValue.IsNullOrEmpty())
            {
                throw new System.Exception("项目值不能为空！");
            }
            if (data.F_ItemName.IsNullOrEmpty())
            {
                throw new System.Exception("项目名不能为空！");
            }
            if (!_hR_DataDictionaryDetailsBus.ExistDetailItemValue(data.F_Id, data.F_ItemValue, itemCode))
            {
                throw new System.Exception("项目值不能重复！");
            }
            if (!_hR_DataDictionaryDetailsBus.ExistDetailItemName(data.F_Id, data.F_ItemName, itemCode))
            {
                throw new System.Exception("项目名不能重复！");
            }
            #endregion

            var model = _hR_DataDictionaryClassBus.GetClassifyEntityByCode(itemCode);
            if (model == null)
            {
                throw new System.Exception("字典分类不存在！");
            }
            data.F_ItemId = model.F_Id;
            if (data.F_Id.IsNullOrEmpty())
            {
                InitEntity(data);

                await _hR_DataDictionaryDetailsBus.AddDataAsync(data);
            }
            else
            {
                await _hR_DataDictionaryDetailsBus.UpdateDataAsync(data);
            }
        }

        [HttpPost]
        public async Task DeleteData(List<string> ids)
        {
            await _hR_DataDictionaryDetailsBus.DeleteDataAsync(ids);
        }

        #endregion
        public class PostEntity
        {
            public string itemCode = "";
            public HR_DataDictionaryDetails data = new HR_DataDictionaryDetails();
        }
        public class TreeCode
        {
            public string itemCode = "";
            public string keyword = "";
        }
    }
}